<!DOCTYPE html>
<html>
  <head>
    <title>NoSQL Query Interface</title>
    <link
      href="https://cdn.jsdelivr.net/npm/jsoneditor@9.10.4/dist/jsoneditor.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/jsoneditor@9.10.4/dist/jsoneditor.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <style>
      * {
        box-sizing: border-box;
      }
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        background: #f5f7fa;
      }
      .container {
        max-width: 100%;
      }
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 100%;
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
      }
      .container {
        max-width: 100%;
      }
      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 100%;
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
      }
      .query-section {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
      }
      .form-group {
        margin-bottom: 20px;
      }
      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
      }
      select,
      input {
        width: 100%;
        padding: 12px;
        border: 2px solid #e1e5e9;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s;
      }
      select:focus,
      input:focus {
        outline: none;
        border-color: #667eea;
      }
      .query-editor {
        border: 2px solid #e1e5e9;
        border-radius: 6px;
        min-height: 150px;
      }
      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s;
        margin: 5px;
        display: inline-block;
      }
      .btn-primary {
        background: #667eea;
        color: white;
      }
      .btn-primary:hover {
        background: #5a6fd8;
        transform: translateY(-2px);
      }
      .btn-success {
        background: #28a745;
        color: white;
      }
      .btn-success:hover {
        background: #218838;
      }
      .btn-secondary {
        background: #6c757d;
        color: white;
      }
      .btn-secondary:hover {
        background: #5a6268;
      }
      .btn-warning {
        background: #ffc107;
        color: #212529;
      }
      .btn-warning:hover {
        background: #e0a800;
      }
      .btn-info {
        background: #17a2b8;
        color: white;
      }
      .btn-info:hover {
        background: #138496;
      }
      .result {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
      }
      #jsoneditor {
        height: 500px;
        border: 1px solid #e1e5e9;
        border-radius: 6px;
      }
      .controls {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
        justify-content: space-between;
      }
      .query-examples {
        margin-top: 15px;
      }
      .example-btn {
        background: #e9ecef;
        color: #495057;
        border: 1px solid #ced4da;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        margin: 2px;
        font-size: 12px;
        transition: background 0.3s;
      }
      .example-btn:hover {
        background: #dee2e6;
      }
      .stats {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }
      .stat-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid #dee2e6;
        min-width: 120px;
      }
      .stat-item strong {
        font-size: 1.2em;
        color: #495057;
      }
      .stat-item div {
        font-size: 0.9em;
        color: #6c757d;
        margin-top: 5px;
      }
      .loading {
        display: none;
        color: #667eea;
        margin-left: 10px;
      }
      .form-row {
        display: flex;
        gap: 20px;
      }
      .form-row .form-group {
        flex: 1;
      }
      .error-message {
        color: #dc3545;
        background: #f8d7da;
        padding: 15px;
        border-radius: 6px;
        margin: 10px 0;
      }
      .success-message {
        color: #155724;
        background: #d4edda;
        padding: 15px;
        border-radius: 6px;
        margin: 10px 0;
      }
      .CodeMirror {
        height: 150px;
      }
      .result-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
      }
      .result-header h3 {
        margin: 0;
        color: #495057;
      }
      .button-group {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🍃 NoSQL Query Interface</h1>
        <p>Professional database query tool for organization</p>
      </div>

      <div class="query-section">
        <form id="queryForm">
          <div class="form-group">
            <label>📊 Collection:</label>
            <select id="collection" required>
              <!-- Collection options will be populated dynamically -->
            </select>
          </div>

          <div class="form-group">
            <label>🔍 NoSQL Query (JSON):</label>
            <div id="queryEditor" class="query-editor"></div>
            <div class="query-examples">
              <small>Quick Examples:</small>
              <button type="button" class="example-btn" onclick="setQuery('{}')">
                All Records
              </button>
              <button type="button" class="example-btn" onclick="setQuery('{}')">
                Active Status
              </button>
              <button type="button" class="example-btn" onclick="setQuery('{}')">
                Recent Records
              </button>
              <button type="button" class="example-btn" onclick="setQuery('{}')">Has ID</button>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>📄 Limit:</label>
              <input type="number" id="limit" value="10" min="1" max="1000" />
            </div>
            <div class="form-group">
              <label>⏭️ Skip:</label>
              <input type="number" id="skip" value="0" min="0" />
            </div>
            <div class="form-group">
              <label>🔄 Sort Field:</label>
              <input type="text" id="sortField" placeholder="createdAt" />
            </div>
            <div class="form-group">
              <label>📈 Sort Order:</label>
              <select id="sortOrder">
                <option value="-1">Descending</option>
                <option value="1">Ascending</option>
              </select>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px">
            <button type="submit" class="btn btn-primary">🚀 Execute Query</button>
            <button type="button" class="btn btn-warning" onclick="clearQuery()">🧹 Clear</button>
            <button type="button" class="btn btn-info" onclick="formatQuery()">
              ✨ Format JSON
            </button>
            <span class="loading">⏳ Executing...</span>
          </div>
        </form>
      </div>

      <div id="result" class="result" style="display: none">
        <div class="stats" id="queryStats" style="display: none">
          <div class="stat-item">
            <strong id="recordCount">0</strong>
            <div>Records</div>
          </div>
          <div class="stat-item">
            <strong id="queryTime">0ms</strong>
            <div>Query Time</div>
          </div>
          <div class="stat-item">
            <strong id="collectionName">-</strong>
            <div>Collection</div>
          </div>
        </div>

        <div class="result-header">
          <h3>📊 Query Results</h3>
          <div class="button-group">
            <button class="btn btn-success" onclick="copyToClipboard()">📋 Copy JSON</button>
            <button id="toggleBtn" class="btn btn-secondary" onclick="toggleExpandCollapse()">
              ⬆ Collapse All
            </button>
            <button class="btn btn-info" onclick="downloadJSON()">💾 Download</button>
          </div>
        </div>
        <div id="jsoneditor"></div>
      </div>
    </div>

    <script>
      // Global variables
      var editor = null
      var jsonData = null
      var queryEditor = null
      var queryStartTime = 0
      var baseURL = 'BASE_URL_PLACEHOLDER'
      var collectionsData = COLLECTIONS_DATA_PLACEHOLDER
      var isExpanded = true // Track expand/collapse state

      // Initialize CodeMirror for query editor
      document.addEventListener('DOMContentLoaded', function () {
        // Populate collections dropdown
        populateCollections()

        queryEditor = CodeMirror(document.getElementById('queryEditor'), {
          value: '{}',
          mode: 'javascript',
          theme: 'monokai',
          lineNumbers: true,
          autoCloseBrackets: true,
          matchBrackets: true,
          indentUnit: 2,
          tabSize: 2
        })

        document.getElementById('queryForm').onsubmit = function (e) {
          e.preventDefault()
          executeQuery()
        }
      })

      function populateCollections() {
        const collectionSelect = document.getElementById('collection')

        if (collectionsData && Array.isArray(collectionsData)) {
          const collectionOptions = collectionsData
            .map((col) => `<option value="${col.name}">${titleCase(col.name)}</option>`)
            .join('')

          collectionSelect.innerHTML = collectionOptions
        }
      }

      function titleCase(str) {
        const ignoreWord = 'MODEL'
        return str
          .split('_') // ['CAMPAIGN', 'MODEL', 'V2']
          .filter((part) => part !== ignoreWord) // remove 'MODEL'
          .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
          .join('') // 'CampaignV2'
      }

      // console.log(titleCase('CAMPAIGN_MODEL_V2')); // CampaignV2

      function executeQuery() {
        var collection = document.getElementById('collection').value
        var query = queryEditor.getValue()
        var limit = document.getElementById('limit').value
        var skip = document.getElementById('skip').value
        var sortField = document.getElementById('sortField').value
        var sortOrder = document.getElementById('sortOrder').value
        // Get organization ID from localStorage
        let orgId = ''
        try {
          const orgData = JSON.parse(localStorage.getItem('orgData'))
          orgId = orgData?.id || '152' // Default to 152 if not found
        } catch (e) {
          console.error('Error parsing orgData:', e)
          orgId = '152' // Default fallback
        }

        document.querySelector('.loading').style.display = 'inline'
        queryStartTime = Date.now()

        var headers = { 'Content-Type': 'application/json' }

        // Forward authorization header if present
        var token = JSON.parse(localStorage.getItem('token'))
        if (token) {
          headers['Authorization'] = token.access_token
        }

        fetch(baseURL + '/workforce/orgSetup/execute-query', {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            collection: `${titleCase(collection)}_${orgId}`,
            query: query,
            limit: limit,
            skip: skip,
            sortField: sortField,
            sortOrder: sortOrder
          })
        })
          .then(function (response) {
            return response.json()
          })
          .then(function (result) {
            var queryTime = Date.now() - queryStartTime

            // Handle the API response structure: {"success": true, "data": {"result": [...], "count": 10}}
            var displayData = result
            if (result.success && result.data && result.data.result) {
              displayData = result.data.result
            }

            initializeEditor(displayData)
            updateStats(displayData, collection, queryTime, result)
            document.getElementById('result').style.display = 'block'
          })
          .catch(function (error) {
            document.getElementById('jsoneditor').innerHTML =
              '<div style="color: red; padding: 20px;">❌ Error: ' + error.message + '</div>'
            document.getElementById('result').style.display = 'block'
          })
          .finally(function () {
            document.querySelector('.loading').style.display = 'none'
          })
      }

      function initializeEditor(data) {
        jsonData = data
        var container = document.getElementById('jsoneditor')
        var options = {
          mode: 'view',
          modes: ['view', 'tree'],
          search: true,
          navigationBar: false
        }

        if (editor) {
          editor.destroy()
        }

        editor = new JSONEditor(container, options)
        editor.set(data)
        editor.expandAll()
      }

      function expandAll() {
        if (editor) {
          editor.expandAll()
        }
      }

      function collapseAll() {
        if (editor) {
          editor.collapseAll()
        }
      }

      function toggleExpandCollapse() {
        if (editor) {
          var toggleBtn = document.getElementById('toggleBtn')

          if (isExpanded) {
            editor.collapseAll()
            toggleBtn.textContent = '⬇ Expand All'
            isExpanded = false
          } else {
            editor.expandAll()
            toggleBtn.textContent = '⬆ Collapse All'
            isExpanded = true
          }
        }
      }

      function setQuery(queryStr) {
        queryEditor.setValue(queryStr)
      }

      function clearQuery() {
        queryEditor.setValue('{}')
        document.getElementById('limit').value = '10'
        document.getElementById('skip').value = '0'
        document.getElementById('sortField').value = ''
      }

      function formatQuery() {
        try {
          var query = JSON.parse(queryEditor.getValue())
          queryEditor.setValue(JSON.stringify(query, null, 2))
        } catch (e) {
          alert('Invalid JSON format')
        }
      }

      function copyToClipboard() {
        if (jsonData) {
          var jsonStr = JSON.stringify(jsonData, null, 2)

          // Try the modern clipboard API first
          if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard
              .writeText(jsonStr)
              .then(function () {
                var btn = document.querySelector('.btn-success')
                var original = btn.textContent
                btn.textContent = '✅ Copied!'
                setTimeout(function () {
                  btn.textContent = original
                }, 2000)
              })
              .catch(function () {
                // Fall back to the older method if clipboard API fails
                fallbackCopyToClipboard(jsonStr)
              })
          } else {
            // Use fallback for browsers without clipboard API
            fallbackCopyToClipboard(jsonStr)
          }
        }
      }

      function fallbackCopyToClipboard(text) {
        var textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()

        try {
          document.execCommand('copy')
          var btn = document.querySelector('.btn-success')
          var original = btn.textContent
          btn.textContent = '✅ Copied!'
          setTimeout(function () {
            btn.textContent = original
          }, 2000)
        } catch (err) {
          console.error('Failed to copy: ', err)
        }

        document.body.removeChild(textArea)
      }

      function downloadJSON() {
        if (jsonData) {
          var jsonStr = JSON.stringify(jsonData, null, 2)
          var blob = new Blob([jsonStr], { type: 'application/json' })
          var url = URL.createObjectURL(blob)

          var a = document.createElement('a')
          a.href = url
          a.download = 'query-result-' + new Date().toISOString().split('T')[0] + '.json'
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
        }
      }

      function updateStats(data, collection, queryTime, originalResult) {
        // Use the count from the API response if available, otherwise count the array
        var recordCount = 0
        if (originalResult && originalResult.data && originalResult.data.count !== undefined) {
          recordCount = originalResult.data.count
        } else if (Array.isArray(data)) {
          recordCount = data.length
        } else {
          recordCount = 1
        }

        document.getElementById('recordCount').textContent = recordCount
        document.getElementById('queryTime').textContent = queryTime + 'ms'
        document.getElementById('collectionName').textContent = collection
        document.getElementById('queryStats').style.display = 'flex'
      }
    </script>
  </body>
</html>
