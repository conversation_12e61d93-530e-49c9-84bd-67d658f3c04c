import Vue from "vue";
import Router from "vue-router";
import routes from "./routes";

Vue.use(Router);

const router = new Router({
  mode: "history",
  routes,
});

router.beforeEach((to, from, next) => {
  const token = Vue.prototype.$storage.getUniversal("token");

  if (to.meta.private && !token) {
    next({ name: "Login" });
  } else if (to.meta.public && token) {
    next({ name: "Dashboard" });
  } else {
    next(); 
  }
});

export default router;
