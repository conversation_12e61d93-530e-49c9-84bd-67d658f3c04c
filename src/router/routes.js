import Dashboard from '@/pages/Workforce/DashboardPage.vue'
import Login from '@/pages/Workforce/LoginPage.vue'
import Employees from '@/pages/Workforce/EmployeesPage.vue'
import TeamReview from '@/pages/Workforce/TeamReview.vue'
import AmcManagement from '@/pages/Workforce/AmcPage.vue'
import Profile from '@/pages/Workforce/ProfilePage.vue'
import Organization from '@/pages/Workforce/OrganizationPage.vue'
import NotFound from '@/pages/NotFound.vue'
import NotificationsPage from '@/pages/Workforce/NotificationsPage.vue'
import InventorySettings from '@/pages/Workforce/Inventory-settings.vue'
import StockManagement from '@/pages/Workforce/Stock-Management.vue'
import CollectionCustomer from '@/pages/Workforce/CollectionCustomer.vue'
import Lenders from '@/components/Workforce/InventorySettings/Partners.vue'
import ProductCustomer from '@/pages/Workforce/ProductCustomer.vue'
import CorporateExpenses from '@/pages/Workforce/Corporate-Expenses.vue'
import Settings from '@/pages/Workforce/Settings.vue'
import CollectionManagement from '@/pages/Workforce/CollectionPage.vue'
import LeadManagement from '@/pages/Workforce/LeadPage.vue'
// import Campaigns from '@/pages/Workforce/Campaigns.vue'
// import ReportPage from '@/pages/Workforce/ReportPage.vue'
import EmailCompaign from '@/pages/Workforce/EmailCampaign.vue'
import EmailTemplates from '@/pages/Workforce/EmailTemplates.vue'
import ReportsDashboard from '@/pages/Workforce/ReportsDashboard.vue'
import DynamicResponses from '@/pages/Workforce/Dynamic-Responses.vue'
import WorkflowSteps from '@/pages/Workforce/WorkflowSteps.vue'
import OrganizationHierarchy from '@/pages/Workforce/OrganizationHierarchy.vue'

const publicRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { layout: 'NoLayout', public: true }
  },
  {
    path: '*',
    name: 'NotFound',
    component: NotFound,
    meta: { layout: 'NoLayout', public: true }
  }
]

const privateRoutes = [
  {
    path: '/workforce/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { layout: 'DefaultLayout', private: true }
  },
  // {
  //   path: '/workforce/employees',
  //   name: 'Employees',
  //   component: Employees,
  //   meta: { layout: 'DefaultLayout', private: true }
  // },
  {
    path: '/workforce/organization',
    name: 'OrganizationH',
    component: OrganizationHierarchy,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/team-review',
    name: 'TeamReview',
    component: TeamReview,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/collection-management/:subCase',
    name: 'CollectionManagement',
    component: CollectionManagement,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/leadspage/:usecase',
    name: 'LeadManagement',
    component: LeadManagement,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/amc',
    name: 'AmcManagement',
    component: AmcManagement,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/notifications',
    name: 'NotificationPage',
    component: NotificationsPage,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/inventory-settings',
    name: 'inventory-settings',
    component: InventorySettings,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/stock-management',
    name: 'stock-management',
    component: StockManagement,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/customers',
    name: 'CollectionCustomer',
    component: CollectionCustomer,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/partners',
    name: 'Lenders',
    component: Lenders,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/workflow-steps',
    name: 'WorkflowSteps',
    component: WorkflowSteps,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/product-customers',
    name: 'ProductCustomer',
    component: ProductCustomer,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/profile',
    name: 'Profile',
    component: Profile,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/org',
    name: 'Organization',
    component: Organization,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/corporate-Expenses',
    name: 'CorporateExpense',
    component: CorporateExpenses,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/reports',
    name: 'ReportsDashboard',
    component: ReportsDashboard,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/settings',
    name: 'Settings',
    component: Settings,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/campaigns',
    name: 'EmailCompaign',
    component: EmailCompaign,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/email-templates',
    name: 'EmailTemplates',
    component: EmailTemplates,
    meta: { layout: 'DefaultLayout', private: true }
  },
  {
    path: '/workforce/dynamic-responses',
    name: 'DynamicResponses',
    component: DynamicResponses,
    meta: { layout: 'DefaultLayout', private: true }
  },
  // Dynamic route for use cases -
  {
    path: '/workforce/:useCase/:subCase',
    name: 'DynamicUseCase',
    component: CollectionManagement,
    meta: { layout: 'DefaultLayout', private: true }
  }
  // Add more private routes here
]

const routes = [...privateRoutes, ...publicRoutes]

export default routes
