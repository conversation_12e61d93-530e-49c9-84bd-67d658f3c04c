{"default": [], "settings": ["SETTINGS:read", "SETTINGS:edit", "SETTINGS:write", "SETTINGS:delete"], "users": ["EMPLOYEES:read", "EMPLOYEES:write", "EMPLOYEES:edit", "EMPLOYEES:delete"], "tasks": ["TASKS:read", "TASKS:write", "TASKS:edit", "TASKS:delete"], "leads": ["LEAD_MANAGEMENT:read", "LEAD_MANAGEMENT:write", "LEAD_MANAGEMENT:edit", "LEAD_MANAGEMENT:delete"], "attendence": ["ATTENDANCE:read", "ATTENDANCE:write", "ATTENDANCE:edit", "ATTENDANCE:delete"], "company": ["COMPANY_SETTINGS:read", "COMPANY_SETTINGS:write", "COMPANY_SETTINGS:edit", "COMPANY_SETTINGS:delete"], "notification": ["NOTIFICATIONS:read", "NOTIFICATIONS:write", "NOTIFICATIONS:edit", "NOTIFICATIONS:delete"], "inventoryManagement": ["STOCK_MANAGEMENT:read", "STOCK_MANAGEMENT:write", "STOCK_MANAGEMENT:edit", "STOCK_MANAGEMENT:delete"], "inventoryRequest": ["ALLOCATE_PRODUCTS:read", "ALLOCATE_PRODUCTS:write", "ALLOCATE_PRODUCTS:edit", "ALLOCATE_PRODUCTS:delete"], "customerManagement": ["CUSTOMERS:read", "CUSTOMERS:write", "CUSTOMERS:edit", "CUSTOMERS:delete"], "collectionManagement": ["COLLECTION_MANAGEMENT:read", "COLLECTION_MANAGEMENT:write", "COLLECTION_MANAGEMENT:edit", "COLLECTION_MANAGEMENT:delete"], "settingRead": ["SETTINGS:read", "ADMIN:read"], "settingEdit": ["SETTINGS:edit", "ADMIN:edit"], "settingWrite": ["SETTINGS:write", "ADMIN:write"], "settingDelete": ["SETTINGS:delete", "ADMIN:delete"], "userRead": ["EMPLOYEES:read", "USER:read"], "userEdit": ["EMPLOYEES:edit", "USER:edit"], "userWrite": ["EMPLOYEES:write", "USER:write"], "userDelete": ["EMPLOYEES:delete", "USER:delete"], "taskRead": ["TASKS:read", "TASK:read"], "taskEdit": ["TASKS:edit", "TASK:edit"], "taskWrite": ["TASKS:write", "TASK:write"], "taskDelete": ["TASKS:delete", "TASK:delete"], "leadRead": ["LEAD_MANAGEMENT:read", "LEADS:read", "SELLS:read", "PROJECT_MANAGEMENT:read"], "leadEdit": ["LEAD_MANAGEMENT:edit", "LEADS:edit", "SELLS:edit", "PROJECT_MANAGEMENT:edit"], "leadWrite": ["LEAD_MANAGEMENT:write", "LEADS:write", "SELLS:write", "PROJECT_MANAGEMENT:write"], "leadDelete": ["LEAD_MANAGEMENT:delete", "LEADS:delete", "SELLS:delete", "PROJECT_MANAGEMENT:dleete"], "formRead": ["FORMS:read"], "formEdit": ["FORMS:edit"], "formWrite": ["FORMS:write"], "formDelete": ["FORMS:delete"], "attendenceRead": ["ATTENDANCE:read", "ATTENDENCE_POLICY:read"], "attendenceEdit": ["ATTENDANCE:edit"], "attendenceWrite": ["ATTENDANCE:write"], "attendenceDelete": ["ATTENDANCE:delete"], "companyRead": ["COMPANY_SETTINGS:read", "COMPANY_SETTINGS:read"], "companyEdit": ["COMPANY_SETTINGS:edit"], "companyWrite": ["COMPANY_SETTINGS:write"], "companyDelete": ["COMPANY_SETTINGS:delete"], "notificationRead": ["NOTIFICATIONS:read"], "notificationWrite": ["NOTIFICATIONS:write"], "notificationEdit": ["NOTIFICATIONS:edit"], "notificationDelete": ["NOTIFICATIONS:delete"], "inventoryRead": ["STOCK_MANAGEMENT:read"], "inventoryWrite": ["STOCK_MANAGEMENT:write"], "inventoryEdit": ["STOCK_MANAGEMENT:edit"], "inventoryDelete": ["STOCK_MANAGEMENT:delete"], "inventoryRequestRead": ["ALLOCATE_PRODUCTS:read"], "inventoryRequestWrite": ["ALLOCATE_PRODUCTS:write"], "inventoryRequestEdit": ["ALLOCATE_PRODUCTS:edit"], "inventoryRequestDelete": ["ALLOCATE_PRODUCTS:delete"], "customerRead": ["CUSTOMERS:read", "CUSTOMER_MANGEMENT:read"], "customerWrite": ["CUSTOMERS:write"], "customerEdit": ["CUSTOMERS:edit"], "customerDelete": ["CUSTOMERS:delete"], "targetRead": ["ANALYTICS:read"], "targetWrite": ["ANALYTICS:write"], "targetEdit": ["ANALYTICS:edit"], "targetDelete": ["ANALYTICS:delete"], "amcRead": ["AMC:read"], "amcWrite": ["AMC:write"], "amcEdit": ["AMC:edit"], "amcDelete": ["AMC:delete"], "collectionRead": ["COLLECTION_MANAGEMENT:read"], "collectionWrite": ["COLLECTION_MANAGEMENT:write"], "collectionEdit": ["COLLECTION_MANAGEMENT:edit"], "collectionDelete": ["COLLECTION_MANAGEMENT:delete"], "projectRead": ["PROJECT_MANAGEMENT:read"], "projectWrite": ["PROJECT_MANAGEMENT:write"], "projectEdit": ["PROJECT_MANAGEMENT:edit"], "projectDelete": ["PROJECT_MANAGEMENT:delete"]}