import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
// import { featureConfig } from "./features";

dayjs.extend(duration)

export function convertToTitleCase(inputString) {
  return inputString
    .toLowerCase()
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

export function splitAndTitleCase(str) {
  const ignoreWord = 'MODEL'
  return str
    .split('_')
    .filter((part) => part !== ignoreWord)
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
    .join('')
}

export function debounce(func, wait, immediate) {
  let timeout

  return function () {
    const context = this
    const args = arguments

    clearTimeout(timeout)

    timeout = setTimeout(() => {
      timeout = null
      if (!immediate) func.apply(context, args)
    }, wait)

    if (immediate && !timeout) func.apply(context, args)
  }
}

export function hasPermission(userPermissions, requiredPermissions) {
  let hasPermission = false
  const permissionObj = userPermissions.reduce((prev, curr) => {
    prev[curr] = true
    return prev
  }, {})

  requiredPermissions.forEach((element) => {
    if (permissionObj[element]) {
      hasPermission = true
    }
  })

  return hasPermission
}

export function isModuleVisible(moduleName) {
  if (typeof window === 'undefined') return true
  try {
    const orgData = JSON.parse(localStorage.getItem('orgData'))
    if (!orgData || !orgData.plan_details) {
      return true
    }
    const planDetails = orgData.plan_details
    return planDetails.MODULES[moduleName]?.enable === true
  } catch (error) {
    console.error('Error parsing orgData from localStorage:', error)
    return false
  }
}

export function isModuleFeatureAllowed(moduleName, featureName) {
  if (typeof window === 'undefined') return true
  try {
    const orgData = JSON.parse(localStorage.getItem('orgData'))
    if (!orgData || !orgData.plan_details) {
      return true
    }
    const planDetails = orgData.plan_details
    return (
      planDetails.MODULES[moduleName]?.features?.[featureName] === 'allow' ||
      planDetails.FEATURE_LIST_AND_SETTINGS[moduleName]?.features?.[featureName] === 'allow'
    )
  } catch (error) {
    console.log(error)
    return false
  }
}

export function isAnyFeatureAllowed(moduleName) {
  if (typeof window === 'undefined') return false

  try {
    const orgData = JSON.parse(localStorage.getItem('orgData'))
    if (!orgData || !orgData.plan_details) {
      return true
    }

    const planDetails = orgData.plan_details
    const module = planDetails.FEATURE_LIST_AND_SETTINGS[moduleName]

    if (module && module.features) {
      return Object.values(module.features).some((feature) => feature === 'allow')
    }
    return false
  } catch (error) {
    console.error('Error checking module features:', error)
    return false
  }
}

const XLSX = require('xlsx')

export function downloadExcel(data, sheetName, fileName) {
  const excelSheet = XLSX.utils.json_to_sheet(data)
  const newBook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(newBook, excelSheet, sheetName)
  XLSX.writeFile(newBook, fileName)
}

const specialOrg = ['DEVORG', 'MITTAL', 'GTNODE', 'FRONTM', 'ICNPTR']

export function checkOrgCode(orgCode) {
  return specialOrg.includes(orgCode)
}

export function accessByString(o, s) {
  s = s.replace(/\[(\w+)\]/g, '.$1') // convert indexes to properties
  s = s.replace(/^\./, '') // strip a leading dot
  var a = s.split('.')
  for (var i = 0, n = a.length; i < n; ++i) {
    var k = a[i]
    if (k in o) {
      o = o[k]
    } else {
      return
    }
  }
  return o
}

export function sortDropdownOptions(options, fieldName = 'text') {
  const sorted = options.sort((a, b) => {
    if (!accessByString(a, fieldName)) return 0

    const aTxt = accessByString(a, fieldName).toLowerCase()
    const bTxt = accessByString(b, fieldName).toLowerCase()
    if (aTxt < bTxt) {
      return -1
    }
    if (aTxt > bTxt) {
      return 1
    }
    return 0
  })

  return sorted
}

export function extractData(data) {
  if (data) {
    let newData

    Object.keys(data).forEach((year) => {
      Object.keys(data[year]).forEach((month) => {
        Object.keys(data[year][month]).forEach((day) => {
          const dataPerDay = data[year][month][day]
          newData = {
            year,
            month,
            day,
            locations: dataPerDay
          }
        })
      })
    })

    return newData
  } else {
    return null
  }
}

export function extractMonthData(data) {
  if (data) {
    let newData

    Object.keys(data).forEach((year) => {
      Object.keys(data[year]).forEach((month) => {
        const dataPerDay = data[year][month]
        newData = {
          year,
          month,

          locations: dataPerDay
        }
      })
    })

    return newData
  } else {
    return null
  }
}

export function getFullAddress(address) {
  if (address) {
    const { addressLine1, addressLine2, city, country, pinCode, state } = address

    if (addressLine1 == 'N/A') {
      return 'N/A'
    }
    const addressParts = [addressLine1, addressLine2, city, country, pinCode, state].filter(
      (part) => part && part !== 'N/A'
    )

    return addressParts.length > 0 ? addressParts.join(', ') : ''
  }
  return '—'
}

export function sortPairDistances(pairDistance) {
  if (pairDistance) {
    const distanceBetweenPoints = pairDistance.sort((a, b) => {
      const keyA = Object.keys(a)[0]
      const keyB = Object.keys(b)[0]

      const pairNumberA = parseInt(keyA.split('_')[1])
      const pairNumberB = parseInt(keyB.split('_')[1])

      if (pairNumberA !== pairNumberB) {
        return pairNumberA - pairNumberB
      } else {
        const valueA = parseFloat(Object.values(a)[0])
        const valueB = parseFloat(Object.values(b)[0])
        return valueA - valueB
      }
    })
    return distanceBetweenPoints
  }
}

export function fullName(employee) {
  if (employee) {
    const firstName = employee.firstName || ''
    const lastName = employee.lastName || ''
    return firstName + ' ' + lastName
  }
  return '-'
}

export function dayHourFormat(startDate, endDate) {
  const start = dayjs(startDate)
  const end = dayjs(endDate)

  const diff = dayjs.duration(end.diff(start))

  const days = diff.days()
  const hours = diff.hours()
  const minutes = diff.minutes()

  if (!days) {
    return `${hours}h ${minutes}m`
  }
  return `${days}d ${hours}h ${minutes}m`
}

export function isValidHttpUrl(string) {
  let url

  try {
    url = new URL(string)
  } catch (_) {
    return false
  }

  return url.protocol === 'http:' || url.protocol === 'https:'
}

export const convertNumberToAmount = (num) => {
  if (!num) return '00.00'
  let decimal = '00'

  if (num % 1 !== 0) {
    const parts = String(num).split('.')
    num = parts[0]
    decimal = parts[1]
  }

  return `${num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}.${decimal}`
}

export const mergeOptions = (arr) => {
  return arr.reduce((merged, item) => {
    if (item.options && Array.isArray(item.options)) {
      merged.push(...item.options)
    }
    return merged
  }, [])
}

export const mergeOptionsDetails = (arr) => {
  return arr.reduce((merged, item) => {
    if (item.optionsList && Array.isArray(item.optionsList)) {
      merged.push(...item.optionsList)
    }
    return merged
  }, [])
}

export const extractExcelHeaders = (selectedFile) => {
  return new Promise((resolve, reject) => {
    if (!selectedFile || selectedFile.length === 0) {
      console.error('No file selected.')
      return reject(new Error('No file selected'))
    }

    const file = selectedFile[0]
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonSheet = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (jsonSheet.length > 0) {
          const headers = jsonSheet[0]
          resolve(headers)
        } else {
          reject(new Error('No data found in the sheet.'))
        }
      } catch (error) {
        console.error('Error processing Excel file:', error)
        reject(error)
      }
    }

    reader.onerror = (error) => {
      console.error('Error reading the file:', error)
      reject(error)
    }

    reader.readAsArrayBuffer(file)
  })
}

export const maskPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) {
    return ''
  }
  phoneNumber = String(phoneNumber)
  if (phoneNumber.length < 8) {
    return phoneNumber
  }
  return phoneNumber?.slice(0, 2) + '******' + phoneNumber?.slice(-2)
}

export const calculateOverdueDays = (
  dueDate,
  emiStatus,
  paymentDate = null,
  emiAmount = 0,
  lateFeeType = null,
  lateFeeValue = 0
) => {
  if (!dueDate) {
    return {
      days: 0,
      isOverdue: false,
      overdueText: '',
      isLatePaid: false,
      latePaidText: '',
      lateFeeAmount: 0,
      lateFeeText: ''
    }
  }

  const due = dayjs(dueDate)

  // Calculate late fee amount based on type and value
  const calculateLateFee = (days, emiAmount, lateFeeType, lateFeeValue, yearDays) => {
    if (!lateFeeType || !lateFeeValue || days <= 0) return 0

    if (lateFeeType === 'Fixed') {
      return lateFeeValue * days
    } else if (lateFeeType === 'Percentage') {
      return (emiAmount * lateFeeValue * (days / yearDays)) / 100
    }
    return 0
  }

  // If EMI is paid or minimum amount paid, check if it was paid late
  if (emiStatus === 'Paid' || emiStatus === 'Minimum Amount Paid') {
    const paid = paymentDate ? dayjs(paymentDate).startOf('day') : dayjs().startOf('day')
    const lateDays = paid.diff(due.startOf('day'), 'day')
    const yearDays =
      paid.year() % 4 === 0 && (paid.year() % 100 !== 0 || paid.year() % 400 === 0) ? 366 : 365

    if (lateDays > 0) {
      const lateFeeAmount = calculateLateFee(
        lateDays,
        emiAmount,
        lateFeeType,
        lateFeeValue,
        yearDays
      )
      const latePaidText = `${lateDays} day${lateDays > 1 ? 's' : ''} late paid`
      const lateFeeText = lateFeeAmount > 0 ? `Late Fee: ₹${lateFeeAmount.toFixed(2)}` : ''

      return {
        days: lateDays,
        isOverdue: false,
        overdueText: '',
        isLatePaid: true,
        latePaidText,
        lateFeeAmount,
        lateFeeText
      }
    }

    // Paid on time or early
    return {
      days: 0,
      isOverdue: false,
      overdueText: '',
      isLatePaid: false,
      latePaidText: '',
      lateFeeAmount: 0,
      lateFeeText: ''
    }
  }

  // For unpaid EMIs, calculate overdue days
  const current = paymentDate ? dayjs(paymentDate).startOf('day') : dayjs().startOf('day')
  const diffDays = current.diff(due.startOf('day'), 'day')
  const yearDays =
    current.year() % 4 === 0 && (current.year() % 100 !== 0 || current.year() % 400 === 0)
      ? 366
      : 365

  const isOverdue = diffDays > 0
  const days = isOverdue ? diffDays : 0
  const lateFeeAmount = calculateLateFee(days, emiAmount, lateFeeType, lateFeeValue, yearDays)

  let overdueText = ''
  let lateFeeText = ''

  if (isOverdue) {
    overdueText = `${days} day${days > 1 ? 's' : ''} overdue`
    if (lateFeeAmount > 0) {
      lateFeeText = `Late Fee: ₹${lateFeeAmount.toFixed(2)}`
    }
  }

  return {
    days,
    isOverdue,
    overdueText,
    isLatePaid: false,
    latePaidText: '',
    lateFeeAmount,
    lateFeeText
  }
}

export const getEmiStatusColor = (status) => {
  const statusColors = {
    Paid: {
      bgColor: 'green lighten-5',
      textColor: 'green darken-2',
      chipColor: 'green'
    },
    Pending: {
      bgColor: 'orange lighten-5',
      textColor: 'orange darken-2',
      chipColor: 'orange'
    },
    Overdue: {
      bgColor: 'red lighten-5',
      textColor: 'red darken-2',
      chipColor: 'red'
    },
    Partial: {
      bgColor: 'blue lighten-5',
      textColor: 'blue darken-2',
      chipColor: 'blue'
    },
    'Minimum Amount Paid': {
      bgColor: 'amber lighten-5',
      textColor: 'amber darken-3',
      chipColor: 'amber darken-1'
    },
    'Waived Off': {
      bgColor: 'purple lighten-5',
      textColor: 'purple darken-2',
      chipColor: 'purple'
    },
    Cancelled: {
      bgColor: 'grey lighten-3',
      textColor: 'grey darken-2',
      chipColor: 'grey'
    }
  }

  return (
    statusColors[status] || {
      bgColor: 'grey lighten-3',
      textColor: 'grey darken-2',
      chipColor: 'grey'
    }
  )
}
// Foreclosure calculation utilities
export const calculateForeclosureAmount = (loanDetails, emiSchedule, charges = {}) => {
  const {
    lateFee = 0,
    processingFee = 0, // This will be prepayment charges in foreclosure context
    legalCharges = 0,
    penaltyDiscount = 0,
    interestWaiver = 0,
    goodwillDiscount = 0
  } = charges

  // Calculate principal outstanding based on EMI schedule
  const principalPaid = emiSchedule
    .filter((emi) => emi.emiStatus === 'Paid' || emi.emiStatus === 'Minimum Amount Paid')
    .reduce((sum, emi) => sum + (emi.principalAmount || 0), 0)

  // Get the current outstanding amount from the latest unpaid EMI or calculate from loan amount
  let principalOutstanding = 0
  const unpaidEmis = emiSchedule.filter(
    (emi) => emi.emiStatus !== 'Paid' && emi.emiStatus !== 'Minimum Amount Paid'
  )

  if (unpaidEmis.length > 0) {
    // Use the outstanding amount from the first unpaid EMI
    principalOutstanding = unpaidEmis[0].outstandingAmount || 0
  } else {
    // Fallback calculation if no unpaid EMIs
    principalOutstanding = (loanDetails.loanAmountSanctioned || 0) - principalPaid
  }

  // Calculate interest
  const interestPaid = emiSchedule
    .filter((emi) => emi.emiStatus === 'Paid' || emi.emiStatus === 'Minimum Amount Paid')
    .reduce((sum, emi) => sum + (emi.interestAmount || 0), 0)

  // Calculate daily accrued interest (this should be calculated separately in the component)
  // For now, we'll use a basic calculation here, but the component should override this
  const accruedInterest = emiSchedule
    .filter((emi) => emi.emiStatus !== 'Paid' && emi.emiStatus !== 'Minimum Amount Paid')
    .reduce((sum, emi) => sum + (emi.interestAmount || 0), 0)

  // Calculate penalties
  const grossPenalty = lateFee + processingFee + legalCharges
  const totalDiscounts = penaltyDiscount + interestWaiver + goodwillDiscount

  // Calculate final amounts
  const netPenalty = Math.max(0, grossPenalty - penaltyDiscount)
  const netAccruedInterest = Math.max(0, accruedInterest - interestWaiver)
  const subtotal = principalOutstanding + netAccruedInterest + grossPenalty
  const totalForeclosureAmount = Math.max(0, subtotal - totalDiscounts)

  return {
    principalOutstanding,
    principalPaid,
    accruedInterest: netAccruedInterest,
    interestPaid,
    penaltyAmount: netPenalty,
    totalForeclosureAmount,
    subtotal,
    totalDiscounts
  }
}

export const getForeclosureStatusColor = (status) => {
  const statusColors = {
    Draft: {
      bgColor: 'grey lighten-3',
      textColor: 'grey darken-2',
      chipColor: 'grey'
    },
    'Pending Approval': {
      bgColor: 'orange lighten-5',
      textColor: 'orange darken-2',
      chipColor: 'orange'
    },
    Approved: {
      bgColor: 'green lighten-5',
      textColor: 'green darken-2',
      chipColor: 'green'
    },
    Rejected: {
      bgColor: 'red lighten-5',
      textColor: 'red darken-2',
      chipColor: 'red'
    },
    Completed: {
      bgColor: 'blue lighten-5',
      textColor: 'blue darken-2',
      chipColor: 'blue'
    }
  }

  return statusColors[status] || statusColors['Draft']
}

export const validateForeclosureData = (foreclosureData) => {
  const errors = []

  // if (!foreclosureData.closureReason) {
  //   errors.push('Closure reason is required')
  // }

  if (foreclosureData.totalForeclosureAmount <= 0) {
    errors.push('Foreclosure amount must be greater than zero')
  }

  if (foreclosureData.penaltyDiscount > foreclosureData.penaltyAmount) {
    errors.push('Penalty discount cannot exceed penalty amount')
  }

  if (foreclosureData.interestWaiver > foreclosureData.accruedInterest) {
    errors.push('Interest waiver cannot exceed accrued interest')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// EMI and Tenure calculation utilities
export const calculateTenure = (principal, emi, annualRate) => {
  if (!principal || !emi || !annualRate || emi <= 0 || principal <= 0) {
    return 0
  }

  const monthlyRate = annualRate / 12 / 100

  if (monthlyRate === 0) {
    return Math.ceil(principal / emi)
  }

  const monthlyInterest = principal * monthlyRate
  if (emi <= monthlyInterest) {
    // EMI is too low to cover even the interest
    return Infinity
  }

  const numerator = Math.log(1 + (principal * monthlyRate) / emi)
  const denominator = Math.log(1 + monthlyRate)

  return Math.ceil(numerator / denominator)
}

export const calculateEMI = (principal, tenureMonths, annualRate) => {
  if (!principal || !tenureMonths || !annualRate || principal <= 0 || tenureMonths <= 0) {
    return 0
  }

  const monthlyRate = annualRate / 12 / 100

  if (monthlyRate === 0) {
    return Math.round(principal / tenureMonths)
  }

  const numerator = principal * monthlyRate * Math.pow(1 + monthlyRate, tenureMonths)
  const denominator = Math.pow(1 + monthlyRate, tenureMonths) - 1

  return Math.round(numerator / denominator)
}
