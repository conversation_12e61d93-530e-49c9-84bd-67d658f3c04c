import { notificationTypes, INFO_NOTIFICATION } from './constants'

export const reformatNotification = (data) => {
  if (data && Array.isArray(data)) {
    return data.map((item) => ({
      type: notificationTypes[item.notificationType] || INFO_NOTIFICATION,
      message: item.notificationMessage,
      notificationGenric: item.notificationGenric
    }))
  }
}
export const getAdditionalCharges = (percentage, referenceAmount) => {
  const totalPercentage = parseFloat(percentage || 0)
  const originalAmount = (totalPercentage / 100) * parseFloat(referenceAmount)
  if (totalPercentage < 0 || totalPercentage > 100) {
    throw new Error('Percentage must be between 0 and 100')
  }
  if (originalAmount < 0) {
    throw new Error('Reference amount must be greater than 0')
  }
  if (originalAmount > referenceAmount) {
    throw new Error('Additional charges cannot exceed the reference amount')
  }
}

export const setDataOnEnter = (items) => {
  return items.length > 0 ? items[0] : null
}

export const callStatusMap = new Map([
  ['A party Initiated||', { message: 'Agent call initiated', color: 'text-orange-500' }],
  [
    'A party connected/Notconnected|Connected|',
    {
      message: 'Agent connected, Customer call initiated',
      color: 'text-orange-500'
    }
  ],
  [
    'B party initiated|Connected|',
    {
      message: 'Agent connected, Customer disconnected',
      color: 'text-orange-500'
    }
  ],
  [
    'B party Connected/Notconnected|Connected|Connected',
    { message: 'Connected', color: 'text-green-500' }
  ],
  ['Call End|Connected|Connected', { message: 'Connected', color: 'text-green-500' }],
  [
    'A party connected/Notconnected|User Not Responding|',
    { message: 'Agent not reachable', color: 'text-red-500', retry: true }
  ],
  [
    'B party Connected/Notconnected|Connected|User Not Responding',
    { message: 'Customer not reachable', color: 'text-red-500', retry: true }
  ],
  [
    'A party connected/Notconnected|Disconnected|',
    { message: 'Agent disconencted the call', color: 'text-red-500', retry: true }
  ],
  [
    'B party Connected/Notconnected|Connected|Disconnected',
    { message: 'Customer disconnected the call', color: 'text-red-500', retry: true }
  ],
  [
    'Call End|User Not Responding|',
    { message: 'Agent not reachable', color: 'text-red-500', retry: true }
  ],
  [
    'Call End|Connected|User Not Responding',
    { message: 'Customer not reachable', color: 'text-red-500', retry: true }
  ],
  [
    'Call End|Disconnected|',
    {
      message: 'Agent disconnected the call',
      color: 'text-red-500',
      retry: true
    }
  ],
  [
    'Call End|Connected|Disconnected',
    {
      message: 'Customer disconnected the call',
      color: 'text-red-500',
      retry: true
    }
  ],
  [
    'Call End|Timeout|',
    {
      message: 'Agent did not pick up',
      color: 'text-red-500',
      retry: true
    }
  ],
  [
    'A party connected/Notconnected|Timeout|',
    {
      message: 'Customer did not pick up',
      color: 'text-red-500',
      retry: true
    }
  ],
  [
    'Call End|Timeout|',
    {
      message: 'Agent did not pick up',
      color: 'text-red-500',
      retry: true
    }
  ],
  [
    'B party Connected/Notconnected|Connected|Timeout',
    {
      message: 'Customer did not pick up',
      color: 'text-red-500',
      retry: true
    }
  ]
])
