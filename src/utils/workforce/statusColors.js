// task status dropdown color coding (component - TaskComponent.vue)

export const taskStatusColors = {
  PENDING: {
    textColor: '#FFCC00',
    bgColor: 'rgba(255, 204, 0, 0.12)'
  },
  MISSED: {
    textColor: '#FF0000',
    bgColor: 'rgb(255, 0, 0, 0.12)'
  },
  IN_PROGRESS: {
    textColor: '#3399FF',
    bgColor: 'rgb(51, 153, 255, 0.12)'
  },
  ENDED: {
    textColor: '#66CC66',
    bgColor: 'rgb(102, 204, 102, 0.12)'
  },
  DEFERRED: {
    textColor: '#FF9900',
    bgColor: 'rgb(255, 153, 0, 0.12)'
  },
  COMPLETED: {
    textColor: '#00CC00',
    bgColor: 'rgb(0, 204, 0, 0.12)'
  },
  CANCELLED: {
    textColor: '#999999',
    bgColor: 'rgb(153, 153, 153, 0.12)'
  },
  REJECTED: {
    textColor: '#FF3366',
    bgColor: 'rgb(255, 51, 102, 0.12)'
  },
  APPROVED: {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  PENDINGLEAVE: {
    textColor: '#FF6600',
    bgColor: 'rgb(255, 102, 0, 0.12)'
  }
}

// chip status color coding (component - ChipStatus.vue)
export const chipStatusColors = {
  pending: {
    textColor: '#F59E0B'
  },
  inProgress: { textColor: '#3B82F6' },
  partialPay: { textColor: '#EAB308' },
  partialDisbursed: { textColor: '#0EA5E9' },
  paid: {
    textColor: '#22C55E'
  },
  overdue: {
    textColor: '#DC2626'
  },
  late_fee: {
    textColor: '#B91C1C'
  },
  late_paid: { textColor: '#F97316' },
  minimum_amount_paid: {
    textColor: '#D97706'
  },
  waived_off: {
    textColor: '#999999'
  },
  hold: {
    textColor: '#FF6633'
  },
  cancelled: {
    textColor: '#999999'
  },
  rejected: {
    textColor: '#FF3366'
  },
  approved: {
    textColor: '#33CC33'
  },
  in_progress: {
    textColor: '#3399FF'
  },
  active: {
    textColor: '#33CC33'
  },
  inactive: {
    textColor: '#999999'
  },
  completed: {
    textColor: '#16A34A'
  },
  not_completed: {
    textColor: '#6B7280'
  },
  required: {
    textColor: '#33CC3333'
  },
  optional: {
    textColor: '#999999'
  },
  recovered: {
    textColor: '#15803D'
  },
  expected: { textColor: '#64748B' },
  fullyDisbursed: {
    textColor: '#059669'
  },
  outstanding: { textColor: '#7C2D12' },
  requested: { textColor: '#8B5CF6' },
  sanctioned: { textColor: '#06B6D4' }
}

// lead status dropdown color coding (component - Leads.vue)

export const leadStatusColors = {
  INTRODUCE_PRODUCT: {
    textColor: '#FF6633',
    bgColor: 'rgba(255, 125, 82, 0.12)'
  },
  QUALIFYING_LEAD: {
    textColor: '#9966CC',
    bgColor: 'rgb(153, 102, 204, 0.12)'
  },
  NURTURING: { textColor: '#33CCCC', bgColor: 'rgb(51, 204, 204, 0.12)' },
  DELIVERING_SALES_PRESENTATION: {
    textColor: '#FF3300',
    bgColor: 'rgb(255, 51, 0, 0.12)'
  },
  OFFER_A_FREE_TRIAL: {
    textColor: '#99CC00',
    bgColor: 'rgb(153, 204, 0, 0.12)'
  },
  SEND_PROPOSAL: {
    textColor: '#FF6600',
    bgColor: 'rgb(255, 102, 0, 0.12)'
  },
  NEGOTIATE_TERMS: {
    textColor: '#FF9900',
    bgColor: 'rgb(255, 153, 0, 0.12)'
  },
  CLOSING_DEAL: {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  ONBOARDING: {
    textColor: '#6699FF',
    bgColor: 'rgb(102, 153, 255, 0.12)'
  },
  LOST: { textColor: '#CC0000', bgColor: 'rgb(204, 0, 0, 0.12)' },
  ON_HOLD: { textColor: '#999999', bgColor: 'rgb(153, 153, 153, 0.12)' }
}

export const attendanceColor = {
  Present: {
    textColor: '#33CCCC',
    bgColor: 'rgb(51, 204, 204, 0.12)'
  },
  Absent: {
    textColor: '#9966CC',
    bgColor: 'rgb(153, 102, 204, 0.12)'
  },
  'Shift In-Progress': {
    textColor: '#FF3300',
    bgColor: 'rgb(255, 51, 0, 0.12)'
  },
  'Decision Pending': {
    textColor: '#3399FF',
    bgColor: 'rgb(51, 153, 255, 0.12)'
  },
  'Full Day': {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  'Half Day': {
    textColor: '#FFCC00',
    bgColor: 'rgba(255, 204, 0, 0.12)'
  },
  'Over Time': {
    textColor: '#6699FF',
    bgColor: 'rgb(102, 153, 255, 0.12)'
  }
}

export const orderStatusColors = {
  PENDING: {
    textColor: '#FFCC00',
    bgColor: 'rgba(255, 204, 0, 0.12)'
  },
  PLACED: {
    textColor: '#FFCC00',
    bgColor: 'rgba(255, 204, 0, 0.12)'
  },
  DISPATCHED: {
    textColor: '#FF9900',
    bgColor: 'rgb(255, 153, 0, 0.12)'
  },
  INSTALLED: {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  DELIVERED: {
    textColor: '#66CC66',
    bgColor: 'rgb(102, 204, 102, 0.12)'
  },
  CANCELLED: {
    textColor: '#999999',
    bgColor: 'rgb(153, 153, 153, 0.12)'
  },
  RETURNED: {
    textColor: '#FF3366',
    bgColor: 'rgb(255, 51, 102, 0.12)'
  },
  COMPLETED: {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  IN_PROGRESS: {
    textColor: '#3399FF',
    bgColor: 'rgb(51, 153, 255, 0.12)'
  },
  RETURN: {
    textColor: '#FF3366',
    bgColor: 'rgb(255, 51, 102, 0.12)'
  },
  OPEN: {
    textColor: '#FFCC00',
    bgColor: 'rgba(255, 204, 0, 0.12)'
  },
  CLOSED: {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  APPROVED: {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  REJECTED: {
    textColor: '#FF3366',
    bgColor: 'rgb(255, 51, 102, 0.12)'
  },
  WITHDRAW: {
    textColor: '#999999',
    bgColor: 'rgb(153, 153, 153, 0.12)'
  }
}

export const sparePartRequestStatusColors = {
  HOLD: {
    textColor: '#FFCC00',
    bgColor: 'rgba(255, 204, 0, 0.12)'
  },
  APPROVED: {
    textColor: '#33CC33',
    bgColor: 'rgb(51, 204, 51, 0.12)'
  },
  REJECTED: {
    textColor: '#FF3366',
    bgColor: 'rgb(255, 51, 102, 0.12)'
  },
  RAISED: {
    textColor: '#3399FF',
    bgColor: 'rgb(51, 153, 255, 0.12)'
  },
  WITHDRAW: {
    textColor: '#999999',
    bgColor: 'rgb(153, 153, 153, 0.12)'
  }
}

export const notificationColor = {
  APPROVED: {
    textColor: '#FFFFFF',
    bgColor: '#33CC33'
  },
  REJECTED: {
    textColor: '#FFFFFF',
    bgColor: '#CC0000'
  },
  WITHDRAW: {
    textColor: '#FFFFFF',
    bgColor: '#999999'
  },
  PENDING: {
    textColor: '#FFFFFF',
    bgColor: '#c99c05'
  },
  RAISED: {
    textColor: '#FFFFFF',
    bgColor: '#3399FF'
  },
  HOLD: {
    textColor: '#FFFFFF',
    bgColor: '#FF6633'
  },
  PARTIAL_APPROVED: {
    textColor: '#FFFFFF',
    bgColor: '#99CC00'
  },
  INFO: {
    textColor: '#FFFFFF',
    bgColor: '#6699FF'
  }
}

export const roasterColor = {
  color1: {
    bgColor: '#EAD6FD',
    borderColor: '#bc94e3'
  },
  color2: {
    bgColor: '#C7EEEA',
    borderColor: '#6bf2e4'
  },
  color3: {
    bgColor: '#FEDDC7',
    borderColor: '#f5b387'
  },
  color4: {
    bgColor: '#FAD3E7',
    borderColor: '#f092c2'
  },
  color5: {
    bgColor: '#E5F0FF',
    borderColor: '#94bef7'
  },
  color6: {
    bgColor: '#FCEDF2',
    borderColor: '#f7a8c3'
  },
  color7: {
    bgColor: '#DDD2FD',
    borderColor: '#ac92f7'
  },
  color8: {
    bgColor: '#CFD3D3',
    borderColor: '#787d7d'
  },
  color9: {
    bgColor: '#EAF9F8',
    borderColor: '#a7d4d1'
  },
  color10: {
    bgColor: '#8E96F7',
    borderColor: '#4851bd'
  }
}
