export const INFO_NOTIFICATION = 'info'
export const WARNING_NOTIFICATION = 'warning'
export const ERROR_NOTIFICATION = 'error'
export const SUCCESS_NOTIFICATION = 'success'

export const notificationTypes = {
  COMPLAINT_APPROVAL_REQUEST: WARNING_NOTIFICATION,
  INVENTORY_REQUEST: SUCCESS_NOTIFICATION
}

export const NOTIFICATION_CHANNEL = 'notification'
export const USER_GUIDE_LINK =
  'https://drive.google.com/file/d/1SYc9FRRiI2OhhnYUAaZEgEYINrv1Hzj_/view?usp=drive_link'
export const PUSHER_CLIENT_LIB = 'https://js.pusher.com/8.2.0/pusher.min.js'

export const SelectionTypes = {
  EMPLOYEES: 'EMPLOYEES',
  TEAMS: 'TEAMS',
  CUSTOMERS: 'CUSTOMERS'
}

export const TAB_ITEMS = {
  COLLECTION: [
    {
      key: 'Cases',
      nameKey: 'caseTable',
      name: 'Cases',
      component: 'Cases',
      icon: 'mdi-briefcase'
    },
    {
      key: 'Visits',
      nameKey: 'visitTable',
      name: 'Visits',
      component: 'Visits',
      icon: 'mdi-map-marker'
    },
    {
      key: 'CaseAnalytics',
      nameKey: 'caseAnalyticsTable',
      name: 'Case Analytics',
      component: 'CaseAnalytics',
      icon: 'mdi-chart-line'
    },
    {
      key: 'CallHistory',
      nameKey: 'callHistoryTable',
      name: 'Call History',
      component: 'CallHistory',
      icon: 'mdi-phone-clock'
    },
    {
      key: 'CaseCustomer',
      nameKey: 'customerTable',
      name: 'Customers',
      component: 'Customer',
      icon: 'mdi-account-group'
    }
  ],
  TEAM_REVIEW: [
    {
      key: 'employees',
      name: 'Employees',
      component: 'EmployeePageCard',
      visibility: true,
      icon: 'mdi-account-tie'
    },
    {
      key: 'attendance',
      name: 'Attendance Muster',
      component: 'AttendanceTable',
      visibility: true,
      icon: 'mdi-clock-time-four'
    },
    {
      key: 'roaster',
      name: 'Roaster',
      component: 'RoasterTable',
      visibility: true,
      icon: 'mdi-calendar-month'
    },
    {
      key: 'timeline',
      name: 'Timeline',
      component: 'TimeLineNew',
      visibility: true,
      icon: 'mdi-timeline-clock'
    }
  ],
  NOTIFICATION: [
    {
      key: 'notifications',
      name: 'Notifications',
      component: 'Notifications',
      visibility: true,
      icon: 'mdi-bell'
    },
    {
      key: 'alerts',
      name: 'Approvals',
      component: 'Alerts',
      visibility: true,
      icon: 'mdi-alert-circle'
    },
    {
      key: 'templates',
      name: 'Templates',
      component: 'NotificationCard',
      visibility: true,
      icon: 'mdi-file-document-outline'
    }
  ],
  ORGANIZATION: [
    {
      key: 'employees',
      name: 'Employees',
      component: 'EmployeesPage',
      visibility: true,
      icon: 'mdi-account-tie'
    },
    {
      key: 'role',
      name: 'Role',
      component: 'Roles',
      visibility: true,
      icon: 'mdi-shield-account'
    },
    {
      key: 'branches',
      name: 'Branches & Teams',
      component: 'BranchTeam',
      visibility: true,
      icon: 'mdi-office-building'
    },

    {
      key: 'organizationhierarchy',
      name: 'Organization Hierarchy',
      component: 'OrganizationHierarchy',
      visibility: true,
      icon: 'mdi-sitemap'
    }
  ]
}

export const routeConfig = {
  employees: {
    path: '/workforce/organization',
    selectValues: [SelectionTypes.EMPLOYEES],
    queryKeys: {
      [SelectionTypes.EMPLOYEES]: { default: 'search' }
    },
    visibility: true
  },
  'team-review': {
    path: '/workforce/team-review',
    selectValues: [SelectionTypes.EMPLOYEES],
    queryKeys: {
      [SelectionTypes.EMPLOYEES]: { default: 'search' }
    },
    visibility: true
  },
  'collection-management': {
    path: '/workforce/collection-management',
    selectValues: [SelectionTypes.EMPLOYEES, SelectionTypes.CUSTOMERS],
    queryKeys: {
      [SelectionTypes.EMPLOYEES]: { default: 'empId', CallHistory: 'empNo' },
      [SelectionTypes.TEAMS]: { default: 'search' },
      [SelectionTypes.CUSTOMERS]: { default: 'customer' }
    },
    visibility: true,
    activeTabFilter: {
      CaseCustomer: [SelectionTypes.CUSTOMERS],
      CaseAnalytics: [SelectionTypes.EMPLOYEES],
      EMI: [SelectionTypes.CUSTOMERS],
      Default: [SelectionTypes.EMPLOYEES, SelectionTypes.CUSTOMERS]
    }
  },
  loan_management: {
    path: '/workforce/loan_management',
    selectValues: [SelectionTypes.EMPLOYEES, SelectionTypes.CUSTOMERS],
    queryKeys: {
      [SelectionTypes.EMPLOYEES]: { default: 'empId', CallHistory: 'empNo' },
      [SelectionTypes.TEAMS]: { default: 'search' },
      [SelectionTypes.CUSTOMERS]: { default: 'customer' }
    },
    visibility: true,
    activeTabFilter: {
      CaseCustomer: [SelectionTypes.CUSTOMERS],
      CaseAnalytics: [SelectionTypes.EMPLOYEES],
      EMI: [SelectionTypes.CUSTOMERS],
      Default: [SelectionTypes.EMPLOYEES, SelectionTypes.CUSTOMERS]
    }
  },
  customers: {
    path: '/workforce/customers',
    selectValues: [SelectionTypes.CUSTOMERS],
    queryKeys: {
      [SelectionTypes.CUSTOMERS]: { default: 'customer' }
    },
    visibility: true
  }
}

export const workFlowStepsOptions = {
  loan: {
    'Lead Generation Steps': {
      'Lead Generation': ['Initial customer inquiry'],
      'Eligibility Check': ['Basic qualification screening'],
      'Product Selection': ['Loan type', 'Amount type']
    },
    'Application & Documentation': {
      'Application Submission': ['Complete loan application form'],
      'Document Collection': ['Gather required documents'],
      'Document Verification': ['Validate submitted documents'],
      'KYC Verification': ['Identity verification', 'Address verification'],
      'Income Verification': ['Salary slips', 'ITR', 'Bank statements']
    },
    'Credit Assessment': {
      'CIBIL/Credit Score Check': ['Credit bureau report'],
      'Credit Analysis': ['Evaluate creditworthiness'],
      'Bank Statement Analysis': ['Cash flow assessment'],
      'Employment Verification': ['Job confirmation'],
      'Reference Check': ['Personal references', 'Professional references']
    },
    'Property/Collateral (for secured loans)': {
      'Property Valuation': ['Asset appraisal'],
      'Legal Verification': ['Title clearance', 'Legal clearance'],
      'Technical Evaluation': ['Property inspection'],
      'Insurance Verification': ['Asset insurance check']
    },
    'Approval Process': {
      'Credit Committee Review': ['Internal approval process'],
      'Risk Assessment': ['Final risk evaluation'],
      'Loan Approval': ['Management approval'],
      'Terms Finalization': ['Interest rate', 'Tenure confirmation'],
      'Sanction Letter': ['Formal approval document']
    },
    'Documentation & Legal': {
      'Loan Agreement Preparation': ['Legal documentation'],
      'Stamp Duty & Registration': ['Legal formalities'],
      'Insurance Processing': ['Loan protection insurance'],
      'Guarantor Documentation': ['Co-signer formalities']
    },
    Disbursement: {
      'Pre-Disbursement Check': ['Final verification'],
      'Account Opening': ['Loan account creation'],
      'Disbursement Authorization': ['Final approval for release'],
      'Fund Transfer': ['Money disbursement'],
      'Disbursement Confirmation': ['Payment confirmation']
    },
    'Post-Disbursement': {
      'Welcome Kit': ['Loan documents and guidelines'],
      'EMI Setup': ['Auto-debit instructions'],
      'Account Activation': ['Loan account goes live']
    }
  },
  disbursement: {
    'Pre-Disbursement Steps': {
      'Disbursement Request': ['Customer request', 'System initiated request'],
      'Eligibility Verification': ['Check disbursement conditions'],
      'Document Compliance Check': ['Verify required documents'],
      'Outstanding Verification': ['Check pending dues', 'Check pending charges'],
      'Limit Availability Check': ['Confirm available disbursement amount'],
      'Beneficiary Verification': ['Validate recipient details'],
      'Purpose Verification': ['Confirm fund usage for specific loans']
    },
    'Disbursement Authorization': {
      'Disbursement Approval': ['Internal authorization'],
      'Signatory Verification': ['Authorized person confirmation'],
      'Compliance Check': ['Regulatory compliance verification'],
      'Risk Assessment': ['Final disbursement risk check'],
      'Manager Approval': ['Senior management sign-off']
    },
    'Fund Transfer Process': {
      'Payment Instruction Creation': ['Generate payment order'],
      'Bank Account Verification': ['Validate recipient account'],
      'Fund Reservation': ['Block amount for transfer'],
      'Payment Processing': ['Execute fund transfer'],
      'Transaction Confirmation': ['Payment success verification'],
      'Receipt Generation': ['Create disbursement receipt']
    },
    'Multiple Disbursement Specific Steps': {
      'Tranche Planning': ['Define disbursement schedule'],
      'Milestone Verification': ['Check completion criteria'],
      'Progress Assessment': ['Evaluate project/construction progress'],
      'Utilization Certificate': ['Previous disbursement usage proof'],
      'Inspection Report': ['Physical verification (for construction)'],
      'Vendor Invoice Verification': ['Direct payment validation'],
      'Retention Amount Calculation': ['Hold-back amount determination']
    },
    'Post-Disbursement Activities': {
      'Disbursement Confirmation': ['Send confirmation to customer'],
      'Account Update': ['Update loan account balance'],
      'Interest Calculation Start': ['Begin interest accrual'],
      'EMI Schedule Generation': ['Create repayment schedule'],
      'Welcome Communication': ['Send loan kit/guidelines'],
      'Insurance Activation': ['Activate loan protection insurance']
    },
    'Documentation & Compliance': {
      'Disbursement Certificate': ['Issue formal certificate'],
      'Regulatory Reporting': ['Submit required reports'],
      'Audit Trail Creation': ['Document transaction history'],
      'File Closure': ['Complete disbursement documentation'],
      'Digital Archive': ['Store electronic records']
    },
    'Account Management Setup': {
      'EMI Auto-Debit Setup': ['Configure automatic payments'],
      'Statement Generation': ['First loan statement'],
      'Online Access Activation': ['Enable digital banking'],
      'Customer Onboarding': ['Portal/app access setup'],
      'Communication Preferences': ['Set notification preferences']
    },
    'Monitoring & Tracking': {
      'Utilization Monitoring': ['Track fund usage (business loans)'],
      'Repayment Tracking Setup': ['Monitor payment behavior'],
      'Account Health Check': ['Initial account assessment'],
      'Cross-sell Opportunity': ['Identify additional products'],
      'Relationship Manager Assignment': ['Assign account manager']
    }
  },
  emi: {
    'Pre-EMI Steps': {
      'EMI Due Notification': ['Send payment reminder (7/3/1 days before)'],
      'Outstanding Balance Check': ['Verify current dues'],
      'Account Status Verification': ['Check account standing'],
      'Payment Method Validation': ['Confirm payment mode availability'],
      'Penalty Calculation': ['Calculate late fees if applicable']
    },
    'Auto-Debit Workflow': {
      'Auto-Debit Instruction': ['System initiated payment'],
      'Bank Balance Verification': ['Check sufficient funds'],
      'Debit Authorization': ['Bank processes payment'],
      'Payment Confirmation': ['Receive payment status'],
      'Bounce Handling': ['Process failed payments']
    },
    'Document Requirements (Conditional)': {
      'Regular EMI (No Documents)': ['Standard Payment - No additional documents required'],
      'Special Circumstances': [
        'Moratorium Request',
        'EMI Restructuring',
        'Prepayment Request',
        'Part Payment',
        'EMI Holiday Request'
      ],
      'Business Loan EMI': [
        'Quarterly Financials',
        'GST Returns',
        'Bank Statements',
        'Stock Statements',
        'Utilization Certificate'
      ],
      'Construction/Home Loan EMI': [
        'Construction Progress Report',
        'Contractor Bills',
        'Insurance Renewal',
        'Property Tax Receipt'
      ]
    },
    'Manual Payment Workflow': {
      'Payment Initiation': ['Customer initiated payment'],
      'Payment Mode Selection': ['Choose payment method'],
      'Amount Verification': ['Confirm payment amount'],
      'Payment Processing': ['Execute transaction'],
      'Receipt Generation': ['Create payment receipt']
    },
    'Successful Payment': {
      'Payment Acknowledgment': ['Send receipt/confirmation'],
      'Account Statement Update': ['Reflect new balance'],
      'Credit Score Update': ['Report to credit bureaus'],
      'Next EMI Reminder Setup': ['Schedule next notification'],
      'Loyalty Points Credit': ['Award customer benefits']
    },
    'Failed/Missed Payment': {
      'Bounce Charges': ['Apply penalty fees'],
      'Overdue Notice': ['Send payment demand'],
      'Collection Workflow': ['Initiate recovery process'],
      'Credit Score Impact': ['Report delinquency'],
      'Account Status Update': ['Mark as overdue']
    }
  }
}
export const documentTypeOptions = {
  emi: {
    'Regular EMI (No Documents)': ['Standard Payment - No additional documents required'],
    'Special Circumstances': [
      'Moratorium Request - Income loss certificate, medical bills',
      'EMI Restructuring - Financial hardship documents',
      'Prepayment Request - NOC request form, ID proof',
      'Part Payment - Payment instruction letter',
      'EMI Holiday Request - Salary certificate, company letter'
    ],
    'Business Loan EMI': [
      'Quarterly Financials - P&L, Balance Sheet (quarterly EMIs)',
      'GST Returns - Tax compliance proof',
      'Bank Statements - Cash flow verification',
      'Stock Statements - Inventory reports (for inventory funding)',
      'Utilization Certificate - Fund usage proof'
    ],
    'Construction/Home Loan EMI': [
      'Construction Progress Report - Work completion certificate',
      'Contractor Bills - Expense verification',
      'Insurance Renewal - Property insurance documents',
      'Property Tax Receipt - Tax payment proof'
    ],
    'EMI Processing Steps': [
      'Payment Allocation - Principal vs Interest breakdown',
      'Account Update - Update outstanding balance',
      'Interest Calculation - Recalculate remaining interest',
      'Schedule Update - Adjust future EMI schedule',
      'Confirmation SMS/Email - Send payment confirmation'
    ]
  }
}

export const APP_URL = {
  uat: 'https://uatapi.myfinfi.com/workforce',
  prod: 'https://api.myfinfi.com/workforce'
}
