// toastMessages.js

const generateLeadSuccessMessage = (title, actionType, status) => {
  return `${title} ${
    actionType.charAt(0).toUpperCase() + actionType.slice(1)
  } ${status} !!`;
};

const generateTaskSuccessMessage = (title, actionType, status) => {
  return `${title} ${
    actionType.charAt(0).toUpperCase() + actionType.slice(1)
  } ${status}!!`;
};

export const assignEmployeeMessage = (employee, reportees) =>
  reportees
    ? `${employee.firstName} ${employee.lastName} is assigned to ${reportees.firstName} ${reportees.lastName}.`
    : `${employee.firstName} ${employee.lastName} is unassigned`;
export const assignEmployeeError = "Error assigning employee to manager !!";
export const roleUpdateSuccess = "Role Updated Successfully !!";
export const roleUpdateError = "Role Updation Unsuccessful !!";
export const employeeImportSuccess = "Employees Imported Successfully !!";
export const taskCreationSuccess = (title) =>
  generateTaskSuccessMessage(title, "Created", "Successfully");

export const taskCreationError = (title) =>
  generateTaskSuccessMessage(title, "Creation", "Failed");

export const taskDeleteSuccess = (title) =>
  generateTaskSuccessMessage(title, "Deleted", "Successfully");

export const taskDeleteError = (title) =>
  generateTaskSuccessMessage(title, "Deletion", "Failed");

export const taskUpdateSuccess = (title) =>
  generateTaskSuccessMessage(title, "Updated", "Successfully");

export const taskUpdateError = (title) =>
  generateTaskSuccessMessage(title, "Updation", "Failed");

export const taskStatusSuccess = (title) =>
  generateTaskSuccessMessage(title, "Status Updated", "Successfully");
export const taskStatusError = (title) =>
  generateTaskSuccessMessage(title, "Status Updation", "Failed");
export const taskAssignSuccess = (title) =>
  generateTaskSuccessMessage(title, "Assigned", "Successfully");

export const leadStatusSuccess = (title) =>
  generateLeadSuccessMessage(title, "Status Updated", "Successfully");

export const leadStatusError = (title) =>
  generateLeadSuccessMessage(title, "Status Updation", "Failed");

export const leadDeleteSuccess = (title) =>
  generateLeadSuccessMessage(title, "Deleted", "Successfully");
export const leadDeleteError = (title) =>
  generateLeadSuccessMessage(title, "Deletion", "Failed");

export const leadUpdateSuccess = (title) =>
  generateLeadSuccessMessage(title, "Updated", "Successfully");
export const leadUpdateError = (title) =>
  generateLeadSuccessMessage(title, "Updation", "Failed");

export const leadCreationSuccess = (title) =>
  generateLeadSuccessMessage(title, "Created", "Successfully");
export const leadCreationError = (title) =>
  generateLeadSuccessMessage(title, "Creation", "Failed");

export const companyBranchCreationSuccess =
  "Company Branch Created Successfully !!";
export const companyBranchCreationError =
  "Company Branch Creation Unsuccessful!!";
export const companyCreationSuccess = "Company Created Successfully!!";
export const companyCreationError = "Company Creation Unsuccessful!!";
export const companyUpdateSuccess = "Company Updated Successfully!!";
export const selectAtLeastOne = "Please select at least one permission.";
export const roleCreationSuccess = "Role Created Successfully!!";
export const roleCreationError = "Role Creation Unsuccessful!!";
export const roleDeleteSuccess = "Role Deleted Successfully!!";
export const roleDeleteError = "Role Deletion Unsuccessful!!";
export const attendanceUpdateSuccess = "Attendance Updated Successfully !!";
export const attendanceUpdateError = "Error in Attendance Update !!";
export const descriptionLimit = "Description exceeds 300 characters.";
export const requiredMessage = "Please fill in all required fields";

export const configLimitError =
  "Select at least 2 options in Custom configuration.";
export const configStepsSuccess = "Completion Steps Updated.";
export const configStepsError = "Completion Steps Not Updated.";
export const orgConfigSuccess = "Organization Configuration Updated !!";
export const orgConfigError = "Error in Organization Configuration Updated !!";
