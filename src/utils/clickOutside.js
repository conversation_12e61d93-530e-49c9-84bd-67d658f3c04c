export default {
  bind(el, binding, vnode) {
    // Define a handler to detect clicks outside the element
    el.clickOutsideEvent = function(event) {
      // Check if the clicked element is outside the bound element
      if (!(el === event.target || el.contains(event.target))) {
        // If it is outside, call the provided method (passed as binding value)
        vnode.context[binding.expression](event);
      }
    };

    // Attach the click event listener to the document
    document.addEventListener('click', el.clickOutsideEvent);
  },
  unbind(el) {
    // Remove the event listener when the element is unbound
    document.removeEventListener('click', el.clickOutsideEvent);
  }
};
