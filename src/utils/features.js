export const featureConfig = {
  features: {
    MODULES: {
      DASHBOARD: {
        enable: true,
        features: {
          SHOW_ATTENDANCE_STATS: "allow",
          SHOW_CASE_ANALYTICS: "allow",
          SHOW_COMPLAINTS: "allow",
          SHOW_PRODUCTS_OUT_OF_STOCK: "allow",
        },
      },
      EMPLOYEES: {
        enable: true,
        features: {
          MANAGE_EMPLOYEES: "allow",
        },
      },
      ATTENDANCE: {
        enable: true,
        features: {
          MANAGE_ATTENDANCE: "allow",
          DOWNLOAD_REPORT: "allow",
          MANAGE_ROSTER: "allow",
          CREATE_SHIFTS: "allow",
          ASSIGN_SHIFTS_TO_EMPLOYEE: "allow",
          DOW<PERSON>OAD_ROSTER: "allow",
        },
      },
      AMC: {
        enable: true,
        features: {
          MANAGE_AMC_PLANS: "allow",
          SHOW_AMC_STATS: "allow",
          MANAGE_AMC_SELLS: "allow",
          LINK_AMC_WITH_PRODUCT_SELLS: "allow",
          LIN<PERSON>_AMC_WITH_CUSTOMER: "allow",
        },
      },
      CUSTOMERS: {
        enable: true,
        features: {
          MANAGE_CUSTOMERS: "allow",
        },
      },
      SELLS: {
        enable: true,
        features: {
          MANAGE_PRODUCT_SELLS: "allow",
          MANAGE_COMPLAINTS_AGAINST_PRODUCTS: "allow",
          MANAGE_CUSTOM_COMPALINT: "allow",
          CUSTOMISE_PRODUCT_SELL_RECORD: "allow",
          DOWNLOAD_REPORT: "allow",
        },
      },
      STOCK_MANAGEMENT: {
        enable: true,
        features: {
          MANAGE_STORES: "allow",
          MANAGE_PARTNERS: "allow",
          MANAGE_PARTNER_PRODUCT_RELATION: "allow",
          REFILL_INVENTORY: "allow",
          CHECK_CURRENT_STOCK: "allow",
          CHECK_PRODUCT_HISTORY: "allow",
        },
      },
      ALLOCATE_PRODUCTS: {
        enable: true,
        features: {
          ALLOCATE_PRODUCT: "allow",
          TRACK_PRODUCT_ALLOCATION: "allow",
        },
      },
      COLLECTION_MANAGEMENT: {
        enable: true,
      },
      TASKS: {
        enable: true,
      },
      REPORTS: {
        enable: true,
      },
      ANALYTICS: {
        enable: true,
      },
      CLAIMS: {
        enable: true,
        features: {
          MANAGE_CLAIMS: "allow",
        },
      },
      SETTINGS: {
        enable: true,
      },
      NOTIFICATIONS: {
        enable: true,
      },
      LEAD_MANAGEMENT: {
        enable: true,
        customise: {
          moduleDisplayName: "",
          lang: {},
        },
      },
    },
    FEATURE_LIST_AND_SETTINGS: {
      EMPLOYEE_MANAGEMENT: {
        features: {
          CREATE_EMPLOYEES: "allow",
          CREATE_BULK_EMPLOYEES: "allow",
          BULK_ASSIGN_TEAMS: "allow",
          BULK_ASSIGN_BRANCHES: "allow",
          BULK_ASSIGN_ROLES: "allow",
          BULK_ASSIGN_SALARY_TEMPLATE: "allow",
          EMPLOYEE_LIST: "allow",
          ASSIGN_ROLE: "allow",
          ASSIGN_TEAM: "allow",
          ASSIGN_BRANCH: "deny",
          ASSIGN_SALARY_TEMPLATE: "deny",
          EMPLOYEE_LIVE_TRACKING: "deny",
        },
      },
      ATTENDANCE_MANAGEMENT: {
        features: {
          SET_SHIFT_TIME: "allow",
          SET_WORKING_DAYS: "allow",
          SET_DAILY_WORKING_HOUR_EXPECTED: "allow",
          SET_FULL_DAY_THRESHOLD: "allow",
          SET_HALF_DAY_THRESHOLD: "allow",
          SET_OVER_TIME_THRESHOLD: "allow",
          ENABLE_EMPLOYEE_OFF_WORKIN_HOURS: "allow",
          SYSTEM_CALCULATION: "allow",
          CLOCKIN: "allow",
          CLOCKOUT: "allow",
          CLOCKIN_WITH_SELFIE: "allow",
          CLOCKOUT_WITH_SELFIE: "allow",
          CLOCKIN_WITH_QR_CODE: "allow",
          CLOCKOUT_WITH_QR_CODE: "allow",
          AUTO_ATTENDANCE_MARK: "allow",
          AUTO_CHECKOUT: "allow",
          STRICT_CLOCKOUT_FOR_DAILY_TASKS: "allow",
          ENABLE_GLOBAL_GEO_FENCING_MODE: "allow",
          ENABLE_GEO_FENCING_FOR_WORK_FROM_HOME_EMPLOYEES: "allow",
          ENABLE_BRANCH_WISE_GEO_FENCING_MODE: "",
          ALLOW_CLOCKIN_CLOCKOUT_FROM_ALL_GEO_FENCING: "allow",
          LINK_ROSTER: "allow",
        },
      },
      LEAVE_MANAGEMENT: {
        features: {
          PLAN_YEARLY_HOLIDAYS: "allow",
          PLAN_ORG_LEAVES: "allow",
          SET_GLOBAL_LEAVE_POLICY: "allow",
          SET_LEAVE_POLICY_BASED_ON_TYPE: "allow",
          BULK_ASSIGN_LEAVE_QUOTA: "allow",
          MANAGE_CUSTOM_LEAVE_QUOTA_FOR_EACH_EMPLOYEE: "allow",
          CREATE_CUSTOM_HOLIDAY: "allow",
          CREATE_CUSTOM_LEAVE_TYPE: "allow",
          ENABLE_LEAVE_APPROVAL_PROCESS: "allow",
        },
      },
      TEAM_MANAGEMENT: {
        features: {
          CREATE_DIRECT_TEAMS: "allow",
          CREATE_BRANCH_TEAMS: "allow",
          LINK_TEAM_WITH_BRANCH: "allow",
          BULK_ASSIGN_EMPLOYEE_TEAMS: "allow",
          BULK_ASSIGN_ENTITY_TEAMS: "allow",
          ASSIGN_TEAM_TO_EMPLOYEE: "allow",
          ASSIGN_TEAM_TO_ENTITY: "allow",
        },
      },
      BRANCH_MANAGEMENT: {
        features: {
          CREATE_DIRECT_BRANCHES: "allow",
          LINK_BRANCH_WITH_TEAM: "allow",
          BULK_ASSIGN_EMPLOYEE_BRANCHES: "allow",
          BULK_ASSIGN_ENTITY_BRANCHES: "allow",
          ASSIGN_BRANCH_TO_EMPLOYEE: "allow",
          ASSIGN_BRANCH_TO_ENTITY: "allow",
        },
      },
      SALARY_MANAGEMENT: {
        features: {
          CREATE_SALARY_TEMPLATES: "allow",
          GENERATE_SALARY_SLIPS: "allow",
          // BULK_ASSIGN_SALARY_TEMPLATES: "allow",
          // ASSIGN_SALARY_TEMPLATE_TO_EMPLOYEE: "allow",
          CONTROL_SALARY_TEMPLATE_STATUS: "allow",
        },
      },
      CLAIM_MANAGEMENT: {
        features: {
          SET_EXPENSE_MODES_AND_PRICING: "allow",
          SET_ALLOWANCE_TYPES: "allow",
          // CREATE_CUSTOM_ALLOWANCE: "allow",
          // CREATE_CUSTOM_EXPENSE_MODE: "allow",
          DOWNLOAD_CLAIM_REPORTS: "allow",
          PRINT_CLAIM_REPORTS: "allow",
          // CREATE_CUSTOM_CLAIM_STATUS: "allow",
          ENABLE_CLAIM_APPROVAL_PROCESS: "allow",
        },
      },
      NOTIFICATION_MANAGEMENT: {
        features: {
          SET_SMS_NOTIFICATION: "allow",
          SET_EMAIL_NOTIFICATION: "allow",
          SET_IN_APP_NOTIFICATION: "allow",
          // SET_PUSH_NOTIFICATION: "allow",
          CEATE_NOTIFICATION_TRIGGER: "allow",
          ENABLE_APPROVAL_VERIFICATION: "allow",
        },
      },
      TASK_MANAGEMENT: {
        features: {
          CREATE_RECURSIVE_TASKS: "allow",
          MANAGE_TASK_COMPLETION_SEQUENCE: "allow",
          VALIDATE_SERVICE_BASED_ON_EMP_OR_CUSTOMER_OTP: "allow",
          VERIFY_EMPLOYEE_ON_PRODUCT_COLLECTION: "allow",
          SET_CUSTOM_DOCUMENT_PROOF_COLLECTION: "allow",
          SET_CUSTOM_MEETING_OUT_COMES: "allow",
          AUTO_CARRY_FORWARD_TASK: "allow",
          SET_TASK_COMPLETION_ESTIMATION: "allow",
          SET_TASK_PRE_DEFINED_TITLES: "allow",
          SET_TASK_DIRECT_COMPLETION: "allow",
          ENABLE_AUTO_ASSIGNMENT: "allow",
          // ENABLE_BULK_TASKS_CREATION: "allow",
          // ENABLE_TASK_CLONING: "allow",
          // ENABLE_QUESTIONNAIRE: "allow",
          // ENABLE_TASK_PICTURE: "allow",
          // ENABLE_MEETING_AUDIO_RECORDING: "allow",
          // ENABLE_MEETING_VIDEO_RECORDING: "allow",
          // ENABLE_OTP_VERIFICATION: "allow",
          // ENABLE_INVENTORY_TYPE: "allow",
          // ENABLE_TASK_APPROVAL: "allow",
        },
      },
      //feature key is missing
      // ADDONS: {
      //   WHATS_APP_BOT: "allow",
      //   CUSTOMISE_BOT_FLOW: "allow",
      // },
    },
    LIMITATIONS: {
      NUMBER_OF_USERS: "INT_VALUE",
      NUMBER_OF_BRANCHES: "INT_VALUE",
      NUMBER_OF_TEAMS: "INT_VALUE",
      NUMBER_OF_SUB_CASES: "INT_VALUE",
      NUMBER_OF_STORES: "INT_VALUE",
      NUMBER_OF_APPROVERS: "INT_VALUE",
      NUMBER_OF_CUSTOMERS: "INT_VALUE",
      NUMBER_OF_PARTNERS: "INT_VALUE",
      NUMBER_OF_BOT_FLOWS: "INT_VALUE",
      SMS_MONTHLY_LIMIT: "INT_VALUE",
      EMAIL_MONTHLY_LIMIT: "INT_VALUE",
      PUSH_NOTIFICATION_MONTHLY_LIMIT: "INT_VALUE",
      IN_APP_MONTHLY_LIMIT: "INT_VALUE",
      TOTAL_CASE_OR_LEAD_LIMIT: "INT_VALUE",
      TOTAL_TASK_LIMIT: "INT_VALUE",
      TOTAL_COMPLAINT_LOG_LIMIT: "INT_VALUE",
      TOTAL_PRODUCT_SELLS_LIMIT: "INT_VALUE",
      TOTAL_INVENTORY_LIMIT: "INT_VALUE",
      STORAGE_CAPACITY: "INT_VALUE",
      LOCATION_TRACK_API_HITS: "INT_VALUE",
    },
  },
};
