// src/plugins/axios.js
import Vue from "vue";
import axios from "axios";


const axiosInstance = axios.create({
  baseURL: process.env.VUE_APP_BASE_URL,
  timeout: 600000,
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token = Vue.prototype.$storage.getUniversal("token");
    if (token) {
      config.headers.Authorization = token.access_token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

Vue.prototype.$axios = axiosInstance;
