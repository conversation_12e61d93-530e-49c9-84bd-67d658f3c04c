// storagePlugin.js
const storage = {
  install(Vue) {
    Vue.prototype.$storage = {
      getUniversal(key) {
        const data = localStorage.getItem(key);
        try {
          return JSON.parse(data);
        } catch (e) {
          return data;
        }
      },

      setUniversal(key, value) {
        if (typeof value === "object") {
          localStorage.setItem(key, JSON.stringify(value));
        } else {
          localStorage.setItem(key, value);
        }
      },

      removeUniversal(key) {
        localStorage.removeItem(key);
      },
    };
  },
};


export default storage;
