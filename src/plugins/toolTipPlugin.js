import { createPopper } from '@popperjs/core'
import Vue from 'vue'

const TooltipPlugin = {
  install(Vue) {
    Vue.directive('tooltip', {
      bind(el, binding) {
        const tooltipText = binding.value.text || ''
        const options = {
          placement: binding.value.placement || 'top',
          backgroundColor: binding.value.backgroundColor || 'black',
          color: binding.value.color || 'white',
          width: binding.value.width || 'auto'
        }

        const tooltipEl = document.createElement('div')
        tooltipEl.style.backgroundColor = options.backgroundColor
        tooltipEl.style.color = options.color
        tooltipEl.style.padding = '5px 10px'
        tooltipEl.style.borderRadius = '4px'
        tooltipEl.style.maxWidth = '200px'
        tooltipEl.style.maxWidth = options.width
        tooltipEl.style.fontSize = '14px'
        tooltipEl.style.pointerEvents = 'none'
        tooltipEl.style.whiteSpace = 'nowrap'
        tooltipEl.style.zIndex = '1000'
        tooltipEl.style.display = 'none'
        tooltipEl.style.whiteSpace = 'normal'
        tooltipEl.style.textAlign = 'center'
        tooltipEl.innerHTML = tooltipText

        const arrow = document.createElement('div')
        arrow.className = 'arrow'
        arrow.style.width = '10px'
        arrow.style.height = '10px'
        arrow.style.position = 'absolute'
        arrow.style.backgroundColor = 'transparent'
        tooltipEl.appendChild(arrow)

        document.body.appendChild(tooltipEl)

        const popperInstance = createPopper(el, tooltipEl, {
          placement: options.placement,
          modifiers: [
            { name: 'arrow', options: { element: arrow } },
            { name: 'offset', options: { offset: [0, 8] } } // Adjusts distance
          ]
        })

        const showTooltip = () => {
          tooltipEl.style.display = 'block'
          popperInstance.update()
        }
        const hideTooltip = () => {
          tooltipEl.style.display = 'none'
        }

        el.addEventListener('mouseenter', showTooltip)
        el.addEventListener('mouseleave', hideTooltip)

        el._tooltipEl = tooltipEl
        el._popperInstance = popperInstance
      },
      unbind(el) {
        if (el._tooltipEl) {
          el._tooltipEl.remove()
        }
        if (el._popperInstance) {
          el._popperInstance.destroy()
        }
      }
    })
  }
}

Vue.use(TooltipPlugin)
export default TooltipPlugin
