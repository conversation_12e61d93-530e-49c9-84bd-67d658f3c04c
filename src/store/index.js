// src/store/index.js
import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';

Vue.use(Vuex);

export const store = new Vuex.Store({
  state: {
    wfmPermissions: [],
    orgLanguage: [],
    useCase: [],
    subCases: [],
    selectValue: "",
    inputValue: "",
  },
  getters: {
    getSelectValue: (state) => state.selectValue,
    getInputValue: (state) => state.inputValue,
  },
  mutations: {
    set_wfmPermissions(state, permissions) {
      state.wfmPermissions = permissions;
    },
    set_orgLanguage(state, permissions) {
      state.orgLanguage = permissions;
    },
    set_useCase(state, permissions) {
      state.useCase = permissions;
    },
    set_subCase(state, permissions) {
      state.subCases = permissions;
    },
    reset_wfmPermissions(state) {
      state.wfmPermissions = [];
    },
    reset_orgLanguage(state) {
      state.orgLanguage = [];
    },
    reset_useCase(state) {
      state.useCase = [];
    },
    reset_subCase(state) {
      state.subCases = [];
    },
    setSelectValue(state, value) {
      state.selectValue = value;
    },
    setInputValue(state, value) {
      state.inputValue = value;
    },
  },
  actions: {
    setWfmPermissions({ commit }, permissions) {
      commit('set_wfmPermissions', permissions);
    },
    setOrgLanguage({ commit }, permissions) {
      commit('set_orgLanguage', permissions);
    },
    setUseCase({ commit }, permissions) {
      commit('set_useCase', permissions);
    },
    setSubCases({ commit }, permissions) {
      commit('set_subCase', permissions);
    },
    resetWfmPermissions({ commit }) {
      commit('reset_wfmPermissions');
    },
    resetOrgLanguage({ commit }) {
      commit('reset_orgLanguage');
    },
    resetUseCase({ commit }) {
      commit('reset_useCase');
    },
    resetSubCase({ commit }) {
      commit('reset_subCase');
    },
    updateSelectValue({ commit }, value) {
      commit("setSelectValue", value);
    },
    updateInputValue({ commit }, value) {
      commit("setInputValue", value);
    },
  },
  // Add persisted state here
  plugins: [
    createPersistedState({
      key: 'finfi._vuex', // Key for local storage
      paths: ['wfmPermissions', 'orgLanguage', 'useCase', 'subCases', 'selectValue', 'inputValue'], // Specify the state properties to persist
    }),
  ],
});
