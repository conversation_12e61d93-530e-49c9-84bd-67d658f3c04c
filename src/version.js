import packageJson from '../package.json'

export const version = packageJson.version

export const checkVersion = async () => {
  try {
    const storedVersion = localStorage.getItem('buildTime')
    const response = await fetch('/version.json?t=' + new Date().getTime())
    const versionData = await response.json()

    if (!storedVersion || storedVersion !== versionData.version) {
      //   console.log(`New version detected: ${versionData.version}`)
      localStorage.setItem('buildTime', versionData.buildTime)
      if (caches) {
        caches.keys().then((names) => {
          names.forEach((name) => {
            caches.delete(name)
          })
        })
      }
      //   window.location.reload(true)
    }
  } catch (error) {
    console.error('Version check failed:', error)
  }
}
