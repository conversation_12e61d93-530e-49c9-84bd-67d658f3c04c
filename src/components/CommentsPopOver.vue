<template>
  <div v-if="menuState" style="position: absolute; z-index: 10;" ref="popover">
    <v-card class="mb-3 w-64">
      <v-card-text v-for="(comment, index) in comments" :key="index">
        <div class="flex items-start gap-4">
          <div>
            <div class="font-semibold">{{ comment.commentBy?.firstName }} {{ comment.commentBy?.lastName }}</div>
            <div class="text-sm text-gray-500 dark-text-color">{{ $dayjs(comment.commentOn).format("DD-MM-YYYY hh:mm A") }}</div>
          </div>
        </div>
        <div class="mt-2 text-black dark-text-color">
          {{ comment.comment }}
        </div>
        <v-divider v-if="index < comments.length - 1" class="my-3"></v-divider>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
export default {
  props: {
    comments: {
      type: Array,
      required: true,
    },
    menuState: {
      type: Boolean,
      required: true,
    },
  },
  watch: {
    menuState(newVal) {
      if (newVal) {
        this.addOutsideClickListener();
      } else {
        this.removeOutsideClickListener();
      }
    },
  },
  methods: {
    handleOutsideClick(event) {
      if (this.$refs.popover && !this.$refs.popover.contains(event.target)) {
        this.$emit('close');
      }
    },
    addOutsideClickListener() {
      document.addEventListener('click', this.handleOutsideClick);
    },
    removeOutsideClickListener() {
      document.removeEventListener('click', this.handleOutsideClick);
    },
  },
  mounted() {
    if (this.menuState) {
      this.addOutsideClickListener();
    }
  },
  beforeDestroy() {
    this.removeOutsideClickListener();
  },
};
</script>
