<template>
  <div class="bg-gray-50">
    <div>
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-8">
        <v-progress-circular indeterminate color="primary" size="50"></v-progress-circular>
        <span class="ml-3 text-gray-600">Loading attendance data...</span>
      </div>

      <v-row v-else>
        <v-col v-for="(userData, index) in employeeData" :key="index" cols="12" md="6" lg="4">
          <v-card class="bg-white rounded-lg !border !border-gray-300 pa-4" elevation="0">
            <div class="flex items-center justify-between mb-3">
              <div
                class="flex items-center space-x-3"
                :class="`border-l-4 pl-3 py-2 mb-3 ${getUserTypeColorClass(userData?.status)}`"
              >
                <div class="relative">
                  <v-avatar :color="getAvatarColor(userData?.fullName)" size="40">
                    <span class="white--text font-semibold title-case">
                      {{ getUserInitials(userData?.fullName) }}
                    </span>
                  </v-avatar>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900 title-case">
                    {{ userData?.fullName }}
                    <span class="text-sm text-gray-500">({{ userData?.employeeId }})</span>
                  </h3>
                  <div class="flex items-center text-sm text-gray-600">
                    <v-icon small class="mr-1">mdi-phone</v-icon>
                    {{ userData.mobile }}
                  </div>
                </div>
              </div>

              <div
                class="flex justify-end mb-8 ml-auto space-y-1"
                v-if="String(userData.status).toLowerCase() === 'checked_in'"
              >
                <BatteryIcons :batteryLevel="userData.batteryHealth" :batteryColor="'green'" />
                <span class="ml-1 text-sm text-green-600"> {{ userData?.batteryHealth }}% </span>
              </div>
            </div>

            <!-- Punch Status -->
            <div
              v-if="userData"
              :class="`pl-3 py-2 pb-2 border border-gray-200 rounded-t-lg ${getPunchTypeColorClass(
                userData?.status
              )}`"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <v-icon small>mdi-clock-outline</v-icon>
                  <span class="text-sm font-medium title-case">
                    {{
                      userData?.checkedOutSystem
                        ? 'checked out by system'
                        : toTitleCase(userData?.status)
                    }}
                    :
                    {{
                      userData?.statusTime ? $dayjs(userData?.statusTime).format('HH:MM A') : 'N/A'
                    }}
                  </span>
                </div>
                <v-chip
                  v-if="userData.showTimeline !== false"
                  small
                  :color="userData.hasCheckInData ? 'primary' : 'grey'"
                  :disabled="!userData.hasCheckInData"
                  label
                  class="text-xs mr-2 title-case"
                  :class="{ 'cursor-not-allowed opacity-50': !userData.hasCheckInData }"
                >
                  Timeline
                  <v-icon :color="userData.hasCheckInData ? 'white' : 'grey darken-2'" size="20"
                    >mdi-link-variant</v-icon
                  >
                </v-chip>
              </div>
            </div>
            <div
              class="flex items-start space-x-2 pt-2 text-gray-600 min-h-[24px] border border-gray-200"
            >
              <v-icon small class="ml-2 text-gray-200 border-r border-gray-300"
                >mdi-map-marker</v-icon
              >

              <p
                class="text-xs pt-0.5 flex-1 transition-all duration-300 ease-in-out overflow-hidden"
                :class="
                  userData.showMore
                    ? 'max-h-32 whitespace-normal'
                    : 'max-h-[20px] truncate whitespace-nowrap'
                "
              >
                {{ toTitleCase(userData?.location) }}
              </p>

              <v-chip text label small class="-mt-1 text-xs mr-2" @click="toggleShowMore(index)">
                {{ userData.showMore ? 'Hide' : 'View' }}
              </v-chip>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
import BatteryIcons from './Workforce/AttendanceManagement/BatteryIcons.vue'
export default {
  name: 'LocationDashboard',
  components: {
    BatteryIcons
  },

  props: {
    employees: {
      type: Array,
      default: () => []
    }
    // teamData: { type: Array }
  },
  data() {
    return {
      showMore: false,
      employeeData: [],
      isLoading: false
    }
  },
  watch: {
    employees: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.fetchCurrentDayAttendance()
        }
      },
      immediate: true
    }
  },
  methods: {
    toggleShowMore(index) {
      this.employeeData[index].showMore = !this.employeeData[index].showMore
    },
    getUserInitials(user) {
      if (!user) return ''
      try {
        return user
          .split(' ')
          .filter((word) => word)
          .map((n) => n[0])
          .join('')
          .toUpperCase()
      } catch (error) {
        console.error('Error in getUserInitials:', error)
        return ''
      }
    },

    toTitleCase(str) {
      if (!str) return ''
      return String(str)
        .toLowerCase()
        .replace(/\b\w/g, (l) => l.toUpperCase())
    },
    getAvatarColor(user) {
      const colors = ['#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#F44336', '#607D8B']
      if (!user) return colors[0]
      const index = user.length % colors.length
      return colors[index]
    },
    getStatusColorClass(color) {
      switch (String(color).toLowerCase()) {
        case 'green':
          return 'bg-green-500'
        case 'orange':
          return 'bg-orange-500'
        case 'red':
          return 'bg-red-500'
        case 'gray':
          return 'bg-gray-400'
        default:
          return 'bg-gray-400'
      }
    },
    getPunchTypeColorClass(status) {
      if (!status) return 'text-gray-600 bg-gray-50 '
      switch (String(status).toLowerCase()) {
        case 'checked_in':
          return 'text-green-600 bg-green-50 '
        case 'checked_out':
          return 'text-red-600 bg-red-50'
        default:
          return 'text-gray-600 bg-gray-50 '
      }
    },
    getUserTypeColorClass(status) {
      if (!status) return 'text-gray-600 border-l-gray-500'
      switch (String(status).toLowerCase()) {
        case 'checked_in':
          return 'text-green-600 border-l-green-500'
        case 'checked_out':
          return 'text-red-600 border-l-red-500'
        default:
          return 'text-gray-600 border-l-gray-500'
      }
    },
    async fetchCurrentDayAttendance() {
      if (!this.employees || this.employees.length === 0) {
        this.employeeData = []
        return
      }

      try {
        this.isLoading = true

        // Get current date for start and end
        const currentDate = this.$dayjs().format('YYYY-MM-DD')

        // Extract user IDs from employees
        const userIds = this.employees.map((employee) => employee._id).join(',')

        const response = await this.$axios.get('/workforce/user/v2/attendance', {
          params: {
            start: currentDate,
            end: currentDate,
            userIds: userIds
          }
        })

        const attendanceData = response.data?.employeeAttendance || []
        const attendanceMap = {}

        attendanceData.forEach((attendance) => {
          const employeeId = attendance.userId._id
          if (
            !attendanceMap[employeeId] ||
            new Date(attendance.checkInOutData[attendance.checkInOutData.length - 1].time) >
              new Date(
                attendanceMap[employeeId].checkInOutData[
                  attendanceMap[employeeId].checkInOutData.length - 1
                ].time
              )
          ) {
            attendanceMap[employeeId] = attendance
          }
        })

        const transformedemployeeData = []

        this.employees.forEach((employee) => {
          const employeeId = employee._id
          const attendance = attendanceMap[employeeId]

          if (attendance && attendance.checkInOutData && attendance.checkInOutData.length > 0) {
            const lastCheckInData = attendance.checkInOutData[attendance.checkInOutData.length - 1]
            const status = (lastCheckInData?.status || '').toLowerCase()

            transformedemployeeData.push({
              ...employee,
              status: status === 'checked_in' ? 'CHECKED_IN' : 'CHECKED_OUT',
              location:
                lastCheckInData?.location?.formatted_address ||
                lastCheckInData?.location?.address ||
                'Address Not Available',
              batteryHealth: lastCheckInData?.deviceData?.batteryHealth || 0,
              statusTime: lastCheckInData?.time || '',
              checkedOutSystem: lastCheckInData?.checkedOutSystem || false,
              hasCheckInData: true,
              showMore: false
            })
          } else {
            transformedemployeeData.push({
              ...employee,
              status: 'CHECKED_OUT',
              location: 'Address Not Available',
              batteryHealth: 0,
              statusTime: '',
              checkedOutSystem: false,
              hasCheckInData: false,
              showMore: false
            })
          }
        })

        this.employeeData = transformedemployeeData
      } catch (error) {
        // Fallback: show employees without attendance data
        this.employeeData = this.employees.map((employee) => ({
          ...employee,
          status: 'CHECKED_OUT',
          location: 'Unable to fetch attendance data',
          batteryHealth: 0,
          statusTime: '',
          checkedOutSystem: false,
          hasCheckInData: false,
          showMore: false
        }))
      } finally {
        this.isLoading = false
      }
    },

    // Method to manually refresh attendance data
    async refreshAttendance() {
      await this.fetchCurrentDayAttendance()
    }
  }
}
</script>

<style scoped></style>
