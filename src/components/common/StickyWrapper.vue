<template>
  <div class="sticky-wrapper" ref="stickyWrapper">
    <div 
      ref="stickyContent" 
      class="sticky-content"
      :class="{ 'is-sticky': isSticky }"
      :style="stickyStyles"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StickyWrapper',
  props: {
    offsetTop: {
      type: Number,
      default: 0
    },
    zIndex: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      isSticky: false,
      originalWidth: 0,
      originalTop: 0,
      scrollListener: null
    }
  },
  computed: {
    stickyStyles() {
      if (!this.isSticky) return {}
      
      return {
        position: 'fixed',
        top: `${this.offsetTop}px`,
        width: `${this.originalWidth}px`,
        zIndex: this.zIndex
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initialize()
      this.scrollListener = this.handleScroll.bind(this)
      window.addEventListener('scroll', this.scrollListener)
      window.addEventListener('resize', this.initialize)
    })
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.scrollListener)
    window.removeEventListener('resize', this.initialize)
  },
  methods: {
    initialize() {
      const wrapper = this.$refs.stickyWrapper
      const content = this.$refs.stickyContent
      
      if (wrapper && content) {
        this.originalWidth = wrapper.offsetWidth
        this.originalTop = wrapper.getBoundingClientRect().top + window.pageYOffset
        
        // Set the wrapper height to prevent layout jumps
        wrapper.style.height = `${content.offsetHeight}px`
      }
    },
    handleScroll() {
      const scrollPosition = window.pageYOffset
      
      if (scrollPosition > this.originalTop - this.offsetTop) {
        if (!this.isSticky) {
          this.isSticky = true
        }
      } else {
        if (this.isSticky) {
          this.isSticky = false
        }
      }
    }
  }
}
</script>

<style scoped>
.sticky-wrapper {
  position: relative;
}

.sticky-content {
  transition: all 0.2s ease;
}

.is-sticky {
  position: fixed;
}
</style>