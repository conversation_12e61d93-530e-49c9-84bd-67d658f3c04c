<template>
  <div class="draggable-wrapper">
    <draggable v-model="internalItems" v-bind="dragOptions" @end="onDragEnd" :disabled="disabled">
      <transition-group type="transition" name="flip-list">
        <div
          v-for="(item, index) in internalItems"
          :key="getItemKey(item, index)"
          class="draggable-item"
        >
          <div class="drag-handle">
            <v-icon color="primary">mdi-drag</v-icon>
          </div>
          <slot :item="item" :index="index"></slot>
        </div>
      </transition-group>
    </draggable>
  </div>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'DraggableWrapper',
  components: {
    draggable
  },
  props: {
    items: {
      type: Array,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    keyField: {
      type: String,
      default: '_id'
    },
    dragOptions: {
      type: Object,
      default: () => ({
        animation: 200,
        handle: '.drag-handle'
      })
    }
  },
  data() {
    return {
      internalItems: []
    }
  },
  watch: {
    items: {
      handler(newVal) {
        this.internalItems = [...newVal]
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getItemKey(item, index) {
      return item[this.keyField] || `item-${index}`
    },
    onDragEnd() {
      this.$emit('update:items', this.internalItems)
      this.$emit('reordered', this.internalItems)
    }
  }
}
</script>

<style scoped>
.draggable-wrapper {
  width: 100%;
}

.draggable-item {
  /* display: flex; */
  align-items: center;
  margin-bottom: 8px;
  position: relative;
  width: 100%;
}

.drag-handle {
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s;
  margin-right: 8px;
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.draggable-item:hover .drag-handle {
  opacity: 1;
}

.flip-list-move {
  transition: transform 0.5s;
}
</style>
