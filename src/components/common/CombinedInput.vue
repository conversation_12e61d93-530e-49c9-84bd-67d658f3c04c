<template>
  <div class="combined-input-wrapper">
    <v-row no-gutters>
      <v-col :cols="leftCols">
        <v-select
          :value="leftValue"
          :label="leftLabel"
          :items="leftItems"
          :outlined="outlined"
          :dense="dense"
          :hide-details="hideDetails"
          :prepend-inner-icon="leftIcon"
          :placeholder="leftPlaceholder"
          :rules="leftRules"
          :disabled="leftDisabled"
          :class="leftFieldClass"
          @change="onLeftChange"
        />
      </v-col>
      <v-col :cols="rightCols">
        <v-text-field
          :value="rightValue"
          :label="rightLabel"
          :type="rightType"
          :outlined="outlined"
          :dense="dense"
          :hide-details="hideDetails"
          :prepend-inner-icon="rightIcon"
          :placeholder="rightPlaceholder"
          :suffix="rightSuffix"
          :rules="rightRules"
          :disabled="rightDisabled"
          :class="rightFieldClass"
          @input="onRightInput"
        />
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: 'CombinedInput',
  props: {
    leftValue: {
      type: [String, Number],
      default: null
    },
    leftLabel: {
      type: String,
      default: 'Select'
    },
    leftItems: {
      type: Array,
      default: () => []
    },
    leftIcon: {
      type: String,
      default: null
    },
    leftPlaceholder: {
      type: String,
      default: ''
    },
    leftRules: {
      type: Array,
      default: () => []
    },
    leftDisabled: {
      type: Boolean,
      default: false
    },
    leftCols: {
      type: [String, Number],
      default: 6
    },

    // Right field (text) props
    rightValue: {
      type: [String, Number],
      default: ''
    },
    rightLabel: {
      type: String,
      default: 'Value'
    },
    rightType: {
      type: String,
      default: 'text'
    },
    rightIcon: {
      type: String,
      default: null
    },
    rightPlaceholder: {
      type: String,
      default: ''
    },
    rightSuffix: {
      type: String,
      default: ''
    },
    rightRules: {
      type: Array,
      default: () => []
    },
    rightDisabled: {
      type: Boolean,
      default: false
    },
    rightCols: {
      type: [String, Number],
      default: 6
    },

    // Common props
    outlined: {
      type: Boolean,
      default: true
    },
    dense: {
      type: Boolean,
      default: true
    },
    hideDetails: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    leftFieldClass() {
      return `combined-input-left pr-0`
    },
    rightFieldClass() {
      return `combined-input-right pl-0`
    }
  },
  methods: {
    onLeftChange(value) {
      this.$emit('left-change', value)
      this.$emit('update:leftValue', value)
    },
    onRightInput(value) {
      this.$emit('right-input', value)
      this.$emit('update:rightValue', value)
    }
  }
}
</script>

<style scoped>
.combined-input-wrapper {
  width: 100%;
}

.combined-input-left :deep(.v-input__slot) {
  border-bottom-right-radius: 0px !important;
  border-top-right-radius: 0px !important;
}

.combined-input-right :deep(.v-input__slot) {
  border-bottom-left-radius: 0px !important;
  border-top-left-radius: 0px !important;
  border-left: none !important;
}
</style>
