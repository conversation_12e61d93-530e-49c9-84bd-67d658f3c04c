<template>
  <div class="custom-input-group">
    <v-select
      :items="getSelectItems"
      v-model="selectValue"
      solo
      flat
      dense
      hide-details
      class="select-input"
      label="Select"
      :style="{ width: '150px' }"
      :append-icon="getSelectItems.length === 1 ? '' : undefined"
      :disabled="getSelectItems.length === 1"
    />
    <v-text-field
      v-if="textFieldSelection"
      v-model="formattedInputValue"
      solo
      flat
      dense
      hide-details
      label="Type here for search..."
      placeholder="Type here for search..."
      class="text-input"
      :style="{ width: '700px' }"
      clearable
      @input="handleInput"
    />
    <v-autocomplete
      v-else
      v-model="inputValue"
      :items="[...(recentSearches[selectValue] || []), ...filteredSearchSuggestions]"
      :search-input.sync="searchValue"
      solo
      flat
      dense
      hide-details
      class="text-input"
      :style="{ width: '700px' }"
      append-icon=""
      label="Type here for search..."
      placeholder="Type here for search..."
      @update:search-input="debouncedFetchSuggestions"
      @change="handleSelection"
      @click:clear="clearSearchQuery"
      :return-object="true"
      clearable
    >
      <template v-slot:item="data">
        <div class="flex justify-between text-sm">
          <span>{{ data.item.text }}</span>
          <v-icon
            small
            class="ml-2"
            @click.stop="removeSearchTerm(data.item)"
            v-if="recentSearches[selectValue]?.includes(data.item)"
          >
            mdi-close-circle
          </v-icon>
        </div>
      </template>
    </v-autocomplete>
  </div>
</template>

<script>
import debounce from 'lodash.debounce'
import { SelectionTypes, routeConfig } from '@/utils/workforce/constants'
export default {
  props: {
    enableFetch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      subCases: this.$store.state.subCases,
      searchValue: '',
      employeeName: '',
      teamId: '',
      customerId: '',
      selectItems: [
        { text: 'Employees', value: SelectionTypes.EMPLOYEES },
        { text: 'Teams', value: SelectionTypes.TEAMS },
        { text: 'Customers', value: SelectionTypes.CUSTOMERS }
      ],
      searchSuggestions: [],
      recentSearches: this.getRecentSearches(),
      options: { sortBy: [], sortDesc: [], itemsPerPage: 9, page: 1 }
    }
  },
  computed: {
    selectValue: {
      get() {
        return (
          this.$store.getters.getSelectValue ||
          this.getSelectItems[0]?.value ||
          SelectionTypes.EMPLOYEES
        )
      },
      set(value) {
        this.$store.dispatch('updateSelectValue', value)
      }
    },
    inputValue: {
      get() {
        return this.$store.getters.getInputValue
      },
      set(value) {
        this.$store.dispatch('updateInputValue', value)
      }
    },
    activeTab() {
      return this.$route.query.activeTab || ''
    },
    textFieldSelection() {
      if (
        this.enableFetch &&
        (this.selectValue === SelectionTypes.EMPLOYEES ||
          this.selectValue === SelectionTypes.CUSTOMERS)
      ) {
        return false
      }
      return true
    },
    formattedInputValue: {
      get() {
        if (typeof this.inputValue === 'object' && this.inputValue !== null) {
          return this.inputValue.name || this.inputValue.customerId || this.inputValue.empId || ''
        }
        return this.inputValue
      },
      set(value) {
        this.inputValue = value
      }
    },
    getSelectItems() {
      if (!this.currentRouteConfig) return []

      let selectValues = this.currentRouteConfig?.selectValues || [
        { text: 'Employees', value: SelectionTypes.EMPLOYEES },
        { text: 'Teams', value: SelectionTypes.TEAMS },
        { text: 'Customers', value: SelectionTypes.CUSTOMERS }
      ]

      if (this.currentRouteConfig.activeTabFilter) {
        const filterMap = this.currentRouteConfig.activeTabFilter
        selectValues = filterMap[this.activeTab] || filterMap.Default || selectValues
      }

      return selectValues.map((value) => ({
        text: value.charAt(0).toUpperCase() + value.slice(1).toLowerCase(),
        value
      }))
    },
    currentRouteConfig() {
      return (
        Object.values(routeConfig).find((route) => this.$route.path.startsWith(route.path)) || {}
      )
    },
    filteredSearchSuggestions() {
      if (!this.selectValue || !this.searchSuggestions.length) return []

      const recentSearchesSet = new Set(
        (this.recentSearches[this.selectValue] || []).map((item) => item.text.toLowerCase())
      )

      return this.searchSuggestions.filter((suggestion) => {
        const suggestionText = suggestion.text.toLowerCase()

        // Check if exact match exists
        if (recentSearchesSet.has(suggestionText)) {
          return false
        }

        // Check for similarity (case-insensitive, substring match, etc.)
        return !Array.from(recentSearchesSet).some((recent) => {
          return this.isSimilar(recent, suggestionText)
        })
      })
    }
  },
  watch: {
    $route(to, from) {
      if (to.path && to.path !== from.path) {
        this.resetFields()
      }
    },
    '$route.query.activeTab': {
      handler(newActiveTab, oldActiveTab) {
        if (!oldActiveTab && newActiveTab) {
          return
        }
        if (newActiveTab !== oldActiveTab) {
          this.resetFields()
          this.clearSearchQuery()
        }
      },
      immediate: true
    },
    '$store.state.subCases': {
      handler(newSubCases) {
        this.subCases = newSubCases
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    isSimilar(a, b) {
      return a.includes(b) || b.includes(a)
    },
    isDifferent(queryParams) {
      const currentQuery = this.$route.query
      return (
        Object.keys(queryParams).length !== Object.keys(currentQuery).length ||
        Object.keys(queryParams).some((key) => queryParams[key] !== currentQuery[key])
      )
    },
    clearSearchQuery() {
      this.$store.dispatch('updateInputValue', '')
      const currentRouteKey = Object.keys(routeConfig).find((key) =>
        this.$route.path.startsWith(routeConfig[key].path)
      )

      if (!currentRouteKey) return

      const queryParams = { ...this.$route.query }
      const queryKeys = routeConfig[currentRouteKey].queryKeys

      // Remove all query parameters defined for the current route
      Object.values(queryKeys).forEach((keys) => {
        if (typeof keys === 'object') {
          Object.values(keys).forEach((key) => delete queryParams[key])
        } else {
          delete queryParams[keys]
        }
      })

      if (this.isDifferent(queryParams)) {
        this.$router
          .replace({ path: this.$route.path, query: queryParams })
          .catch(this.handleNavigationError())
      }
    },
    resetFields() {
      this.searchValue = ''
      this.searchSuggestions = []
      this.$store.dispatch('updateSelectValue', null)
      this.$store.dispatch('updateInputValue', null)
    },
    fetchSuggestions() {
      if (!this.enableFetch || !this.searchValue) return
      if (this.inputValue && typeof this.inputValue === 'object') return
      if (this.selectValue === SelectionTypes.EMPLOYEES) {
        this.fetchEmployeeSuggestions()
      } else if (this.selectValue === SelectionTypes.TEAMS) {
        this.fetchTeamSuggestions()
      } else if (this.selectValue === SelectionTypes.CUSTOMERS) {
        this.fetchCustomerSuggestions()
      }
    },

    getUserFullName(user) {
      return user.fullName ? `${user.fullName}` : `${user.firstName} ${user.lastName}`
    },

    getCustomerFullName(customer) {
      return customer.customerFullName
        ? `${customer.customerFullName}`
        : `${customer.firstName} ${customer.lastName}`
    },

    async fetchEmployeeSuggestions() {
      try {
        const params = { search: this.searchValue }
        const response = await this.$axios.get('workforce/v2/users', {
          params
        })

        this.searchSuggestions =
          response.data.users?.map((user) => ({
            text: `${this.getUserFullName(user)} - Reporting To: ${
              user.reportesTo?.fullName || 'No-One'
            }`,
            value: {
              name: user.fullName,
              empId: user._id,
              empNumber: user.mobile
            }
          })) || []
      } catch (error) {
        console.error('Error fetching employee suggestions:', error)
      }
    },
    async fetchTeamSuggestions() {
      try {
        const params = { search: this.searchValue }
        const response = await this.$axios.get('workforce/collection/cases', {
          params: params
        })
        this.searchSuggestions =
          response.data.cases?.map((el) => ({
            text: el.customer
              ? `${el.caseNo} - ${this.getCustomerFullName(el.customer)} - ${
                  el.customer?.crn ?? 'N/A'
                }`
              : el.caseNo,
            value: {
              cNo: el.caseNo,
              sCName: el.caseType ?? 'Unknown'
            }
          })) ?? []
      } catch (error) {
        console.error('Error fetching team suggestions:', error)
      }
    },
    async fetchCustomerSuggestions() {
      try {
        this.loadTable = true
        const { page, itemsPerPage } = {
          page: 1,
          itemsPerPage: 5
        }
        const params = {
          page,
          limit: itemsPerPage
        }
        if (this.searchValue) {
          params.search = this.searchValue
        }

        const response = await this.$axios.get('/workforce/customers', {
          params
        })
        this.searchSuggestions =
          response.data?.customers?.map((customer) => ({
            text: `${this.getCustomerFullName(customer) ?? 'Unknown'} - ${customer?.crn ?? 'N/A'}`,
            value: {
              name: customer?.customerFullName ?? 'Unknown',
              customerId: customer?._id ?? 'N/A',
              crn: customer?.crn ?? 'N/A'
            }
          })) ?? []
      } catch (error) {
        console.log(error)
      }
    },

    getQueryParams(value, queryKey) {
      const queryParams = { ...this.$route.query }

      if (queryKey) {
        value ? (queryParams[queryKey] = value) : delete queryParams[queryKey]
      }

      return queryParams
    },

    handleNavigationError(err) {
      if (err?.name !== 'NavigationDuplicated') console.error('Navigation error', err)
    },

    handleInput(inputValue) {
      const routeKey = Object.keys(routeConfig).find((key) =>
        this.$route.path.startsWith(routeConfig[key].path)
      )
      if (!routeKey) return

      const path = this.$route.path
      const queryKeys = routeConfig[routeKey].queryKeys[this.selectValue] || {}
      const queryParamKey = queryKeys[this.activeTab] ?? queryKeys.default

      if (!queryParamKey) return

      const queryParams = this.getQueryParams(inputValue, queryParamKey)

      this.$router
        .push({
          path: path,
          query: queryParams
        })
        .catch(this.handleNavigationError)
    },

    handleSelection(selectedItem) {
      const { value } = selectedItem ? selectedItem : {}

      const currentRouteKey = Object.keys(routeConfig).find((key) =>
        this.$route.path.startsWith(routeConfig[key].path)
      )

      if (!currentRouteKey) return

      const queryKeys = routeConfig[currentRouteKey].queryKeys[this.selectValue] || {}
      const queryKey = queryKeys[this.activeTab] ?? queryKeys.default

      let queryParams = { ...this.$route.query }

      if (value) {
        if (this.selectValue === SelectionTypes.CUSTOMERS) {
          queryParams[queryKey] = value.customerId
        } else if (this.activeTab === 'CallHistory') {
          queryParams[queryKey] = value.empNumber
        } else {
          queryParams[queryKey] = value.empId
        }
      } else {
        delete queryParams[queryKey]
      }

      this.$router
        .push({
          path: this.$route.path,
          query: queryParams
        })
        .catch(this.handleNavigationError)

      if (selectedItem) this.saveSearchTerm(selectedItem)
    },
    saveSearchTerm(term) {
      if (typeof window !== 'undefined') {
        let searches = this.getRecentSearches()

        if (!searches[this.selectValue]) {
          searches[this.selectValue] = []
        }
        if (this.selectValue === SelectionTypes.TEAMS) {
          const existingIndex = searches[this.selectValue].findIndex(
            (item) => item.value.cNo === term.value.cNo
          )

          if (existingIndex !== -1) {
            searches[this.selectValue].splice(existingIndex, 1)
          }
        } else if (this.selectValue === SelectionTypes.CUSTOMERS) {
          const existingIndex = searches[this.selectValue].findIndex(
            (item) => item.value?.customerId === term.value?.customerId
          )

          if (existingIndex !== -1) {
            searches[this.selectValue].splice(existingIndex, 1)
          }
        } else {
          const existingIndex = searches[this.selectValue].findIndex(
            (item) => item.value?.empId === term.value?.empId
          )

          if (existingIndex !== -1) {
            searches[this.selectValue].splice(existingIndex, 1)
          }
        }
        searches[this.selectValue].unshift(term)
        if (searches[this.selectValue].length > 5) {
          searches[this.selectValue].pop()
        }
        localStorage.setItem('recentSearches', JSON.stringify(searches))
        this.recentSearches = searches
      }
    },
    getRecentSearches() {
      if (typeof window !== 'undefined') {
        return JSON.parse(localStorage.getItem('recentSearches')) || {}
      }
      return {}
    },
    removeSearchTerm(item) {
      let searches = this.getRecentSearches()

      if (searches[this.selectValue]) {
        if (this.selectValue === SelectionTypes.TEAMS) {
          searches[this.selectValue] = searches[this.selectValue].filter(
            (search) => search.value.cNo !== item.value.cNo
          )
        } else if (this.selectValue === SelectionTypes.CUSTOMERS) {
          searches[this.selectValue] = searches[this.selectValue].filter(
            (search) => search.value?.customerId !== item.value?.customerId
          )
        } else {
          searches[this.selectValue] = searches[this.selectValue].filter(
            (search) => search.value?.empId !== item.value?.empId
          )
        }
        localStorage.setItem('recentSearches', JSON.stringify(searches))
        this.recentSearches = searches
      }
    }
  },
  created() {
    this.debouncedHandleInput = debounce(this.handleInput, 500)
    this.handleInput = this.debouncedHandleInput
    this.debouncedFetchSuggestions = debounce(this.fetchSuggestions, 500)
  },
  beforeDestroy() {
    this.$store.dispatch('updateSelectValue', null)
    this.$store.dispatch('updateInputValue', null)
  }
}
</script>

<style scoped>
.custom-input-group {
  display: flex;
  align-items: center;
  padding: 2px;
  background-color: white;
}

.custom-input-group .v-select .v-input__control,
.custom-input-group .v-autocomplete .v-input__control {
  background-color: white !important;
}

.select-input {
  flex-grow: 1;
  width: 15%;
  font-size: smaller;
  border-right: 1px solid #ccc;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.text-input {
  flex-grow: 2;
  margin-left: 2px;
  width: 90%;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
</style>
