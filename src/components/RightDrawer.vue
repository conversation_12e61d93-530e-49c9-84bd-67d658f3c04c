<template>
  <div>
    <v-icon class="settings-icon" @click="toggleSettings" size="24">mdi-chevron-triple-left</v-icon>

    <div class="settings-container" :class="{ open: isSettingsOpen }" @click.stop>
      <div class="flex items-center gap-2">
        <p class="mb-0 text-[#21427D] font-semibold" @click="callCustomer">
          <v-icon> mdi-phone </v-icon>
          Call a customer
        </p>
      </div>
      <v-divider class="my-2"></v-divider>
      <p @click="downloadApp" class="mb-0 text-[#21427D] font-semibold">
        <v-icon>mdi-android</v-icon>
        Download App
      </p>
      <v-divider class="my-2"></v-divider>
      <p @click="openHelp" class="mb-0 text-[#21427D] font-semibold">
        <v-icon> mdi-comment-processing-outline </v-icon>
        Help Desk
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RightDrawer',
  data() {
    return {
      isSettingsOpen: false
    }
  },
  methods: {
    toggleSettings() {
      this.isSettingsOpen = !this.isSettingsOpen
    },
    handleOutsideClick(event) {
      const settingsContainer = this.$el.querySelector('.settings-container')
      const settingsIcon = this.$el.querySelector('.settings-icon')

      if (
        settingsContainer &&
        !settingsContainer.contains(event.target) &&
        event.target !== settingsIcon
      ) {
        this.isSettingsOpen = false
      }
    },
    callCustomer() {
      this.$emit('openCallCutomer')
      this.isSettingsOpen = false
    },
    openHelp() {
      this.$emit('openHelpDesk')
      this.isSettingsOpen = false
    },
    downloadApp() {
      this.$emit('downloadEmpApp')
      this.isSettingsOpen = false
    }
  },
  mounted() {
    document.addEventListener('click', this.handleOutsideClick)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleOutsideClick)
  }
}
</script>

<style scoped>
.settings-icon {
  position: fixed;
  top: 50px;
  right: 0px;
  color: rgb(166, 164, 164);
  border: none;
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
}
.settings-icon:hover {
  color: rgb(64, 62, 62);
}

.settings-container {
  position: fixed;
  top: 50px;
  right: -340px; /* Fully hidden off-screen */
  width: 200px;
  height: 160px;
  background: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  transition: right 0.3s ease-in-out;
  padding: 20px;
  z-index: 900;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.settings-container p {
  cursor: pointer;
}

.settings-container.open {
  right: 0;
}
</style>
