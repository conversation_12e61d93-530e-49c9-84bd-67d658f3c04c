<template>
  <div class="flex items-center">
    <div class="mr-1.5 employee-status-container">
      <img
        :src="getEmployeeImage(item)"
        @error="handleImageError"
        alt="Employee Image"
        class="rounded-full h-8 w-8 inline -mt-1 mr-1 employee-image"
        v-if="item.profileImage"
      />
      <v-icon v-else style="font-size: 32px"> mdi-account-circle </v-icon>
    </div>
    <div class="w-full">
      <span
        v-if="item"
        class="text-sm truncate-name flex items-start"
        v-tooltip="{
          text: `${getEmployeeFullName(item)}`
        }"
      >
        {{ getEmployeeFullName(item) }}
      </span>
      <div
        class="text-xs flex items-center justify-start gap-2 text-gray-500"
        v-if="view === 'attendenceTable'"
      >
        <span
          class="w-2 h-2 inline-block rounded-full ml-1 transition-all duration-300"
          :class="getCurrentDateClockIN(item) ? 'bg-green-500 glowing' : 'bg-red-500 dimmed'"
        ></span>
        {{ avalibility }}
      </div>
    </div>
  </div>
</template>

<script>
import { fullName } from '@/utils/common'

export default {
  name: 'EmployeeProfile',
  props: {
    item: Object,
    view: String
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_S3_BASE_URL,
      fallbackImage: require('~/assets/img/genericImage.png'),
      avalibility: ''
    }
  },
  methods: {
    getEmployeeImage(item) {
      if (item.profileImage) {
        return `${this.baseUrl}${item.profileImage}`
      } else {
        if (item.gender === 'M') {
          return require('~/assets/img/profileMale.png')
        } else if (item.gender === 'F') {
          return require('~/assets/img/profileFemale.png')
        } else {
          return require('~/assets/img/genericImage.png')
        }
      }
    },
    handleImageError(event) {
      event.target.src = this.fallbackImage
    },
    getEmployeeFullName(item) {
      if (item.name) {
        return item.name
      }
      return fullName(item)
    },
    getCurrentDateClockIN(item) {
      if (item.attendance) {
        const today = this.$dayjs().format('DD MMM')
        const clockIn =
          item?.attendance[today] &&
          item?.attendance[today]?.CurrentStatus !== 'CHECKED_OUT' &&
          item?.attendance[today]?.CurrentStatus
        if (clockIn) {
          this.avalibility = 'Clocked-In'
        } else {
          this.avalibility = 'Clocked-Out'
        }
        return clockIn
      }
      return
    }
  }
}
</script>

<style scoped>
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.8);
  }
  50% {
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.8);
  }
}

@keyframes dim {
  0% {
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.6);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.6);
  }
}

.glowing {
  animation: glow 2s infinite;
}

.dimmed {
  animation: dim 2s infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.truncate-name {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.employee-status-container {
  position: relative;
  display: inline-block;
}

.employee-image,
.employee-icon {
  display: inline;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
}
</style>
