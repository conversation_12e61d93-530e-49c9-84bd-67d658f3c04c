<template>
  <v-autocomplete
    ref="autocomplete"
    v-model="selectedAddress"
    @change="onChange"
    @update:search-input="getLocationSuggestions"
    label="Address *"
    :items="branches"
    :filter="customFilter"
    item-text="label"
    item-value="value"
    hide-no-data
    hide-details
    outlined
    dense
    required
    clearable
  >
    <template v-slot:item="{ item }">
      <v-list-item-content class="w-[600px]">
        <v-list-item-title>{{ item.label }}</v-list-item-title>
      </v-list-item-content>
    </template>
  </v-autocomplete>
</template>

<script>
/* global google */
import { debounce } from '@/utils/common'

export default {
  name: 'CommonAutocomplete',
  props: {
    addressLine1: String,
    branch: Array
  },
  data() {
    return {
      selectedAddress: null,
      branches: [],
      placesServiceData: null,
      googleMapsLoaded: false
    }
  },
  watch: {
    addressLine1: {
      handler(newVal) {
        console.log(newVal)
        if (newVal) this.selectedAddress = newVal
      },
      immediate: true
    },
    branch: {
      handler(newVal) {
        this.branches = newVal
      },
      immediate: true
    }
  },
  methods: {
    onChange(val) {
      if (!this.googleMapsLoaded) {
        console.error('Google Maps not loaded yet.')
        return
      }

      const selectedBranch = this.branches.find((branch) => branch.value === val)
      if (selectedBranch) {
        this.selectedAddress = selectedBranch
      }
      const request = {
        placeId: val,
        fields: ['address_component', 'formatted_address', 'name', 'geometry']
      }

      const service = new google.maps.places.PlacesService(document.createElement('div'))
      service.getDetails(request, (place, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK) {
          const addressComponents = place.address_components
          const branchData = {}
          for (const component of addressComponents) {
            for (const type of component.types) {
              if (type === 'locality') {
                branchData.city = component.long_name
              } else if (type === 'administrative_area_level_1') {
                branchData.state = component.long_name
              } else if (type === 'route') {
                branchData.address = component.long_name
              } else if (type === 'postal_code') {
                branchData.pinCode = component.long_name
              } else if (type === 'country') {
                branchData.country = component.long_name
              } else if (type === 'street_number') {
                branchData.streetNumber = component.long_name
              } else if (type === 'sublocality') {
                branchData.sublocality = component.long_name
              }
            }
          }
          this.selectedAddress = {
            label: place.formatted_address,
            value: selectedBranch.value
          }
          this.$emit('selected-place', {
            formatted_address: place.formatted_address,
            city: branchData.city || '',
            state: branchData.state || '',
            country: branchData.country || '',
            pincode: branchData.pinCode || '',
            location: place.geometry.location
          })
        }
      })
    },
    getLocationSuggestions: debounce(function (searchTxt) {
      if (!searchTxt || this.selectedAddress) {
        return
      }
      console.log(searchTxt, this.selectedAddress)
      if (this.placesServiceData) {
        this.placesServiceData.textSearch(
          {
            query: searchTxt + ' in India',
            componentRestrictions: { country: 'IN' }
          },
          (results, status) => {
            if (status === google.maps.places.PlacesServiceStatus.OK) {
              this.branches = results.map((result) => ({
                label: result.formatted_address,
                value: result.place_id
              }))
            }
          }
        )
      }
    }, 1000),
    customFilter() {
      return true
    },
    async loadGoogleMapsScript() {
      if (typeof google !== 'undefined' && google.maps && google.maps.places) {
        this.initPlacesService()
        this.googleMapsLoaded = true
        return
      }
      const script = document.createElement('script')
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.VUE_APP_MAP_KEY}&libraries=places&callback=Function.prototype`
      script.async = true
      script.defer = true
      document.head.appendChild(script)
      window.initGoogleMaps = () => {
        this.initPlacesService()
        this.googleMapsLoaded = true
      }
    },
    initPlacesService() {
      this.placesServiceData = new google.maps.places.PlacesService(document.createElement('div'))
    }
  },
  mounted() {
    this.selectedAddress = this.$props.addressLine1
    this.branches = this.$props.branch
    this.loadGoogleMapsScript()
  }
}
</script>

<style scoped></style>
