<template>
  <div>
    <!-- Travel Modes -->
    <div
      class="mb-3"
      v-show="isFeatureVisible('SET_EXPENSE_MODES_AND_PRICING')"
    >
      <div v-if="travelArray.length">
        <div
          :class="
            Expense !== 'Expense'
              ? 'sticky flex top-0 justify-between items-center z-50 py-4'
              : 'flex justify-between items-center pb-4'
          "
        >
          <p class="text-base mb-0 font-semibold">Travel modes</p>
          <v-btn plain @click="onCancel" v-if="Expense !== 'Expense'">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
        <v-row
          v-for="(travel, index) in travelArray"
          :key="index"
          class="flex items-center"
        >
          <v-col>
            <v-select
              v-model="travel.mode"
              label="Mode of travel"
              :items="filteredTravelModes(index)"
              item-text="text"
              item-value="value"
              hide-details
              outlined
              dense
            ></v-select>
          </v-col>
          <v-col>
            <v-text-field
              label="Price (per Km)"
              v-model.number="travel.pricePerKm"
              hide-details
              outlined
              dense
            ></v-text-field>
          </v-col>
          <v-col>
            <v-text-field
              label="Min Amount"
              v-model.number="travel.min"
              hide-details
              outlined
              dense
            ></v-text-field>
          </v-col>
          <v-col>
            <v-text-field
              label="Max Amount"
              v-model.number="travel.max"
              hide-details
              outlined
              dense
            ></v-text-field>
          </v-col>
          <v-icon
            color="red"
            size="20"
            v-if="travelArray.length !== 1"
            @click="removeTravelMode(index)"
            >mdi-delete</v-icon
          >
        </v-row>
        <div class="flex justify-end">
          <v-btn
            v-if="showAddTravelModeButton"
            @click="addTravelMode"
            :color="$vuetify.theme.currentTheme.primary"
            class="mt-4 mr-5 white--text"
            >Add Mode</v-btn
          >
        </div>
      </div>
      <no-data
        v-else
        title="Nothing to Display"
        :subTitle="`No travel modes are enabled for the organization`"
        :btnText="`Set Travel Modes`"
        :btnAction="addTravelMode"
      />
    </div>

    <!-- Allowance Types -->
    <div v-show="isFeatureVisible('SET_ALLOWANCE_TYPES')">
      <v-divider></v-divider>
      <div class="mt-5" v-if="allowanceArray.length">
        <p class="text-base font-semibold">Allowance types</p>
        <v-row
          v-for="(allow, index) in allowanceArray"
          :key="index"
          class="flex items-center"
        >
          <v-col>
            <v-select
              v-model="allow.type"
              label="Allowance Type"
              :items="filteredAllowanceType(index)"
              item-text="text"
              item-value="value"
              hide-details
              outlined
              dense
            ></v-select>
          </v-col>
          <v-col>
            <v-text-field
              label="Amount"
              v-model.number="allow.amount"
              hide-details
              outlined
              dense
            ></v-text-field>
          </v-col>
          <v-icon
            color="red"
            size="20"
            class="mr-3"
            v-if="allowanceArray.length !== 1"
            @click="removeAllowanceType(index)"
            >mdi-delete</v-icon
          >
        </v-row>
        <div class="flex justify-end">
          <v-btn
            @click="addAllowanceType"
            v-if="showAddAllowanceTypeButton"
            :color="$vuetify.theme.currentTheme.primary"
            class="mt-4 mr-5 white--text"
            >Add Type</v-btn
          >
        </div>
      </div>
      <no-data
        v-else
        title="Nothing to Display"
        :subTitle="`No allowance types are enabled for the organisation`"
        :btnText="`Set Allowance Types`"
        :btnAction="addAllowanceType"
      />
    </div>
  </div>
</template>

<script>
import { isModuleFeatureAllowed } from "@/utils/common";
export default {
  name: "TravelAndAllowance",
  props: {
    travelArray: Array,
    allowanceArray: Array,
    travelModes: Array,
    allowanceTypes: Array,
    selectUserExpense: Object,
    Expense: String,
  },
  computed: {
    showAddTravelModeButton() {
      return this.travelArray?.length < this.travelModes.length;
    },
    showAddAllowanceTypeButton() {
      return this.allowanceArray?.length < this.allowanceTypes.length;
    },
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed("CLAIM_MANAGEMENT", feature);
    },
    addTravelMode() {
      this.travelArray.push({
        mode: "",
        pricePerKm: null,
        min: null,
        max: null,
      });
      this.$emit("updateTravelArray", this.travelArray);
    },
    removeTravelMode(index) {
      this.travelArray.splice(index, 1);
      this.$emit("updateTravelArray", this.travelArray);
    },
    addAllowanceType() {
      this.allowanceArray.push({
        type: "",
        amount: null,
      });
      this.$emit("updateAllowanceArray", this.allowanceArray);
    },
    removeAllowanceType(index) {
      this.allowanceArray.splice(index, 1);
      this.$emit("updateAllowanceArray", this.allowanceArray);
    },
    filteredTravelModes(index) {
      const selectedModes = this.travelArray.map((item) => item.mode);
      return this.travelModes.filter(
        (mode) =>
          !selectedModes.includes(mode.value) ||
          mode.value === this.travelArray[index].mode
      );
    },
    filteredAllowanceType(index) {
      const selectedTypes = this.allowanceArray.map((item) => item.type);
      return this.allowanceTypes.filter(
        (type) =>
          !selectedTypes.includes(type.value) ||
          type.value === this.allowanceArray[index].type
      );
    },
    onCancel() {
      this.$emit("onCancel");
    },
  },
};
</script>
