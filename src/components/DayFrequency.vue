<template>
  <div>
    <div class="flex items-center w-5/6 space-x-5">
      <!-- Initial select input -->
      <label
        for="selectedOption"
        class="block font-medium w-32 text-[#464A53] dark-text-color -mt-2"
        >Frequency:</label
      >
      <div class="select-wrapper">
        <v-select
          v-model="selectedOption"
          id="selectedOption"
          :items="options"
          @change="handleOptionChange"
          :menu-props="{ offsetY: true }"
          :disabled="selectedDaysDisabled"
          class="pb-0 custom-select"
          solo
          flat
          dense
          hide-details
        ></v-select>
      </div>
    </div>

    <!-- Show buttons for days when "Repeat" is selected -->
    <div v-if="selectedOption === 'Repeat'" class="pl-[9.3rem] mt-3 mb-2">
      <v-btn
        v-for="day in days"
        :key="day"
        @click="toggleDay(day)"
        class="white--text mr-3"
        :color="selectedDays.includes(day) ? $vuetify.theme.currentTheme.primary : '#babfbb'"
        :disabled="selectedDaysDisabled"
        fab
        small
        depressed
        :ripple="false"
      >
        {{ day[0] }}
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      options: ["One Time", "Repeat"],
      selectedOption: "One Time", // Initially selected option
      days: [
        "SUNDAY",
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
      ],
      selectedDays: [],
    };
  },
  props: {
    selectedDaysDisabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    toggleDay(day) {
      if (this.selectedDays.includes(day)) {
        this.selectedDays = this.selectedDays.filter(
          (selectedDay) => selectedDay !== day
        );
      } else {
        this.selectedDays.push(day);
      }

      // Send the flag and selectedDays array to the parent component
      this.$emit("selection-updated", {
        isRepeat: this.selectedOption === "Repeat",
        selectedDays: this.selectedDays,
      });
    },
    handleOptionChange() {
      // Handle option change and reset selectedDays when "One Time" is selected
      if (this.selectedOption === "One Time") {
        this.selectedDays = [];
      }

      // Send the flag and selectedDays array to the parent component
      this.$emit("selection-updated", {
        isRepeat: this.selectedOption === "Repeat",
        selectedDays: this.selectedDays,
      });
    },
    reinitialise() {
      this.selectedOption = "One Time";
      this.selectedDays = [];
    },
  },
};
</script>
<style>
.custom-select {
  height: 35px;
}
.select-wrapper {
  border: 1px solid #e9e8e8;
  padding: 4px;
  border-radius: 10px;
}
</style>
