<template>
  <v-menu offset-y :close-on-content-click="closeOnContentClick" right class="filterMenu bg-white dark-bg-custom">
    <template v-slot:activator="{ on }">
      <div class="m-2 cursor-pointer" @click="toggleFilters" v-on="on">

        <div class="flex items-center">
          <v-badge :content="selectedCount" :value="selectedCount" :color="$vuetify.theme.currentTheme.primary" overlap>
            <v-icon size="28px" color="grey">mdi-filter</v-icon>
          </v-badge>

        </div>
      </div>
    </template>
    <slot></slot>
  </v-menu>
</template>

<script>
export default {
  props: {
    selectedCount: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      showFilters: false,
      closeOnContentClick: false,
    };
  },
  methods: {
    toggleFilters() {
      this.showFilters = !this.showFilters;
    },
  }
};
</script>