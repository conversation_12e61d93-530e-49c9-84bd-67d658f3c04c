<template>
  <div>
    <div>
      <v-card
        class="draggable-card"
        :style="{ top: `${position.y}px`, left: `${position.x}px` }"
        @mousedown="startDrag"
        style="width: 500px"
      >
        <v-form ref="formRef" v-model="isFormValid" v-if="!isCallStart">
          <v-list>
            <h3 class="text-lg font-medium mx-4 mb-2">Call a Customer</h3>
            <v-col cols="12" class="pt-0">
              <v-autocomplete
                v-model="singleModule"
                :items="modules"
                outlined
                dense
                hide-details
                label="Select Module *"
                :rules="[rules.required]"
              ></v-autocomplete>
            </v-col>

            <v-col cols="12" class="pt-0" v-if="fieldsToShow.showTypes">
              <v-autocomplete
                v-model="type"
                :items="types"
                item-text="name"
                item-value="_id"
                outlined
                dense
                hide-details
                :label="`Select Type *`"
                :rules="[rules.required]"
              ></v-autocomplete>
            </v-col>

            <v-col cols="12" class="pt-0" v-if="fieldsToShow.showCaseId">
              <v-autocomplete
                class="wfm-search-fields"
                v-model="filterCase"
                :items="cases"
                :label="`Search Case *`"
                item-text="caseNo"
                item-value="_id"
                hide-details
                solo
                flat
                clearable
                outlined
                dense
                prepend-inner-icon="mdi-magnify"
                @update:search-input="handleInput"
                :disabled="!type"
                :rules="[rules.required]"
              ></v-autocomplete>
            </v-col>

            <v-col cols="12" class="pt-0" v-if="fieldsToShow.showTaskId">
              <v-autocomplete
                v-model="taskId"
                :items="getTasks"
                item-text="taskTitle"
                item-value="_id"
                outlined
                dense
                hide-details
                label="Select Task *"
                :rules="[rules.required]"
              ></v-autocomplete>
            </v-col>

            <v-divider></v-divider>
            <v-col cols="12" class="pt-0 mt-4" v-if="fieldsToShow.showIterator">
              <v-autocomplete
                v-model="iterator"
                :items="iterators"
                item-text="phoneNumber"
                item-value="_id"
                outlined
                dense
                hide-details
                label="Select Iterator *"
                :rules="[rules.required]"
              ></v-autocomplete>
            </v-col>

            <v-col cols="12" class="pt-0" v-if="fieldsToShow.showAgentNo">
              <v-text-field
                v-model="loginMobile"
                outlined
                dense
                hide-details
                label="Enter agent mobile no. *"
                type="number"
                :rules="[rules.required, rules.mobile]"
              ></v-text-field>
            </v-col>

            <v-col cols="12" class="pt-0 pb-0" v-if="fieldsToShow.showCustomerNo">
              <v-autocomplete
                v-if="customerNos && !!customerNos.length"
                v-model="number"
                :items="customerNos"
                label="Select customer mobile no."
                hide-details
                solo
                flat
                clearable
                outlined
                type="number"
                :rules="[rules.required, rules.mobile]"
                :disabled="!filterCase"
              ></v-autocomplete>
              <v-text-field
                v-else
                v-model="number"
                label="Enter customer mobile no."
                hide-details
                solo
                flat
                outlined
                dense
                type="number"
                :rules="[rules.required, rules.mobile]"
                :disabled="!filterCase"
              ></v-text-field>
            </v-col>

            <v-card-actions v-if="!isCallStart">
              <v-spacer></v-spacer>
              <v-btn outlined @click="handleClose">Cancel</v-btn>
              <v-btn
                :color="$vuetify.theme.currentTheme.primary"
                @click="handleInitiate(false)"
                :loading="loading"
                class="white--text ml-4"
              >
                Initiate Call
              </v-btn>
            </v-card-actions>
          </v-list>
        </v-form>

        <div class="flex flex-col justify-between mb-4 mt-4" v-else-if="isCallStart && !isComment">
          <div class="mx-4 flex justify-between">
            <v-icon @click="handleBack">mdi-keyboard-backspace</v-icon>
            <v-btn icon @click="handleClose">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>

          <div class="flex w-full justify-around items-center p-4 bg-white dark-bg-custom">
            <div class="flex flex-col items-center">
              <img src="@/assets/img/UserIcon.svg" class="animated-icon" />
              <p class="mt-2 text-sm font-semibold" :class="statusColor">Agent</p>
              <p class="mt-2 text-sm" :class="statusColor">{{ loginMobile }}</p>
            </div>

            <div class="flex flex-col items-center justify-center">
              <p class="mx-4 text-sm font-semibold mb-0" :class="statusColor">
                {{ getStatus }}
              </p>

              <div class="arrow-icon-container" v-if="status == 'connecting'">
                <v-icon class="arrow-icon" :class="statusColor">mdi-chevron-right</v-icon>
                <v-icon class="arrow-icon" :class="statusColor">mdi-chevron-right</v-icon>
                <v-icon class="arrow-icon" :class="statusColor">mdi-chevron-right</v-icon>
                <v-icon class="arrow-icon" :class="statusColor">mdi-chevron-right</v-icon>
                <v-icon class="arrow-icon" :class="statusColor">mdi-chevron-right</v-icon>
                <v-icon class="arrow-icon" :class="statusColor">mdi-chevron-right</v-icon>
              </div>
            </div>

            <div class="flex flex-col items-center">
              <img src="@/assets/img/UserIcon.svg" class="animated-icon" />
              <p class="mt-2 text-sm font-semibold" :class="statusColor">Customer</p>
              <p class="mt-2 text-sm" :class="statusColor">{{ number }}</p>
            </div>
          </div>

          <div class="flex flex-col justify-end mx-4" v-if="status === 'connected'">
            <v-text class="text-gray-700">* Post conversation comment</v-text>
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="mt-2 text-right white--text"
              @click.stop="isComment = true"
            >
              Add Comment
            </v-btn>
          </div>
          <div class="flex justify-center mx-4" v-if="errorMssg">
            <p class="mb-0 text-sm" :class="statusColor">{{ errorMssg }}</p>
          </div>
        </div>

        <div class="p-4" v-else-if="isComment">
          <div class="flex justify-between">
            <div class="flex items-center">
              <v-icon @click.stop="handleBackStatus">mdi-keyboard-backspace</v-icon>
              <v-text class="text-lg font-semibold ml-2">Add Your Comment</v-text>
            </div>
            <v-btn icon @click="handleClose" :disabled="!outcome">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>
          <v-textarea
            auto-grow
            outlined
            dense
            hide-details
            v-model="commentTextArea"
            label="Add Comment (optional)"
          ></v-textarea>

          <div class="w-full flex justify-end">
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="mt-2 text-right white--text"
              @click="handleUpdate"
              :disabled="!outcome"
              :loading="isCommentSubmit"
            >
              Update
            </v-btn>
          </div>
        </div>
      </v-card>
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils/common'
export default {
  name: 'DialerPopOver',
  data() {
    return {
      position: { x: 0, y: 0 },
      isDragging: false,
      dragOffset: { x: 0, y: 0 },
      customerNos: [],
      number: '',
      iterators: [],
      iterator: '',
      items: [],
      status: '',
      singleModule: '',
      modules: [],
      cases: [],
      caseId: '',
      tasks: [],
      taskId: '',
      isCallStart: false,
      rules: {
        required: (v) => !!v || 'This field is required',
        email: (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
        number: (v) => (!isNaN(parseFloat(v)) && isFinite(v)) || 'Must be a number',
        mobile: (v) =>
          (/^[0-9]{10}$/.test(v) && v.length === 10) || 'Please enter a valid Mobile number'
      },
      types: [],
      type: '',
      filterCase: '',
      loginMobile: '',
      errorMssg: '',
      isFormValid: false,
      commentTextArea: '',
      isComment: false,
      isCommentSubmit: false,
      callId: '',
      customers: [],
      agentName: '',
      outcome: '',
      userDefinedCaseStatus: [],
      additionalInfo: {},
      callIdentifier: ''
    }
  },
  props: {
    callItem: Object
  },
  methods: {
    handleClose() {
      this.$emit('handleCloseDialer', false)
    },
    handleBackStatus() {
      this.isCallStart = true
      this.isComment = false
    },
    handleBack() {
      this.isCallStart = false
    },
    startDrag(event) {
      this.isDragging = true
      this.dragOffset.x = event.clientX - this.position.x
      this.dragOffset.y = event.clientY - this.position.y
      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)
    },
    onDrag(event) {
      if (this.isDragging) {
        this.position.x = event.clientX - this.dragOffset.x
        this.position.y = event.clientY - this.dragOffset.y
      }
    },
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },
    async getVoiceNumbers() {
      try {
        const response = await this.$axios.get('/workforce/tele-caller/credentials')
        if (response.data.success) {
          this.iterators = response?.data?.data.map((ite) => {
            if (ite.phoneNumber) {
              return {
                phoneNumber: ite.phoneNumber,
                _id: ite._id
              }
            }
          })

          this.iterator = this.iterators[0]._id
        }
      } catch (error) {
        console.log(error)
      }
    },

    async handleInitiate(retry) {
      if (!this.$refs.formRef.validate() && !retry) {
        return
      }
      this.isCallStart = true
      if (!retry) {
        if (
          !this.singleModule ||
          !this.filterCase ||
          !this.iterator ||
          !this.loginMobile ||
          !this.number
        ) {
          this.$toast.error('Please select fields.')
          this.isCallStart = false
          return
        }
        if (String(this.loginMobile).length !== 10 || String(this.number).length !== 10) {
          this.$toast.error('Number must be 10 digits.')
          this.isCallStart = false
          return
        }
        this.status = 'connecting'
        const singleCase = this.cases.find((caseDetail) => caseDetail?._id === this.filterCase)
        if (singleCase) {
          this.additionalInfo = {
            caseInfo: {
              customerFullName: singleCase?.customer?.customerFullName,
              customerId: singleCase?.customer?._id,
              caseId: singleCase?._id,
              caseNo: singleCase?.caseNo,
              caseType: singleCase?.caseType
            },
            agentName: this.agentName,
            phoneNoId: this.iterator
          }
        }
        this.callIdentifier = `${this.singleModule}_${this.type}_${this.filterCase}`
      }

      try {
        const payload = {
          customerPhone: String(this.number),
          agentPhone: String(this.loginMobile),
          identifier: this.callIdentifier,
          phoneNoId: this.iterator,
          additionalInfo: this.additionalInfo
        }
        const response = await this.$axios.post(`/workforce/tele-caller/voice-call`, payload)
        if (response?.data?.success) {
          this.status = 'connected'
          this.callId = response?.data?.callInitiator?._id
        } else {
          this.status = 'failed'
        }
      } catch (error) {
        console.error('An error occurred:', error)
        this.status = 'failed'
        this.errorMssg = error?.response?.data?.message
      }
    },

    async handleUpdate() {
      this.isCommentSubmit = true
      await this.handleComment()
      await this.editCase()
      this.isCommentSubmit = false
    },

    async handleComment() {
      if (!this.commentTextArea) {
        this.$toast.error('Please Enter Comment.')
        return
      }
      if (!this.callId) {
        this.$toast.error('Call not placed.')
        return
      }

      this.status = 'connecting'
      try {
        const payload = {
          comment: this.commentTextArea.trim()
        }
        const response = await this.$axios.put(
          `/workforce/tele-caller/voice-call/${this.callId}`,
          payload
        )
        if (response?.data?.success) {
          this.$toast.success(response?.data?.message)
          this.$toast.success(response?.data?.message)

          this.handleClose()
        } else {
          this.isCommentSubmit = false
        }
      } catch (error) {
        console.error('An error occurred:', error)
        this.isCommentSubmit = false
        const errorMessage = error?.response?.data?.message || 'An error occurred'
        this.$toast.error(errorMessage)
      }
    },
    async fetchCases(value) {
      try {
        const caseType = this.types.find((nametype) => nametype._id === this.type)
        const params = {
          page: '*',
          limit: '*'
        }
        params.search = value
        params.caseType = caseType.name

        const response = await this.$axios.get('/workforce/collection/cases', {
          params
        })
        if (response?.data?.success) {
          this.cases = response.data?.cases
        }
      } catch (error) {
        console.log(error)
        this.$toast.error(error?.response?.data?.message)
      }
    },
    async fetchCustomers(value) {
      try {
        console.log('value', value)
        const params = {}
        if (value) {
          params.search = value
        }
        const response = await this.$axios.get('/workforce/customers', {
          params
        })

        if (response?.data?.success) {
          this.customers = response.data.customers
        }
      } catch (error) {
        console.log(error)
        const errorMessage = error?.response?.data?.message || error
        this.$toast.error(errorMessage)
      }
    },
    handleInput: debounce(async function (value) {
      if (value) {
        await this.fetchCases(value)
      }
    }, 800),
    handleSearchCustomer: debounce(async function (value) {
      if (value) {
        await this.fetchCustomers(value)
      }
    }, 800),
    async editCase() {
      try {
        const payload = {
          userDefinedCaseStatus: this.outcome
        }
        const response = await this.$axios.put(`/workforce/collection/case/${this.filterCase}`, {
          payload
        })
        if (!response.data?.success) {
          this.isCommentSubmit = false
        }
      } catch (error) {
        this.isCommentSubmit = false
        const errorMessage = error?.response?.data?.message || error
        this.$toast.error(errorMessage)
        console.error(error)
      }
    }
  },

  watch: {
    filterCase: {
      handler(newVal) {
        if (newVal) {
          this.customerNos = this.cases.filter(
            (item) => item._id === newVal
          ).customer?.customerMobile
        } else {
          this.customerNos = []
        }
      },
      immediate: true
    },
    type: {
      handler(newVal) {
        if (newVal && !this.$props.callItem) {
          this.filterCase = ''
          this.cases = []
          this.customerNos = []
          this.number = ''
        }
      },
      immediate: true
    }
  },
  computed: {
    fieldsToShow() {
      const fields = {
        showIterator: false,
        showCaseId: false,
        showTaskId: false,
        showAgentNo: false,
        showCustomerNo: false,
        showTypes: false
      }

      switch (this.singleModule) {
        case 'CASES':
          fields.showIterator = true
          fields.showCaseId = true
          fields.showAgentNo = true
          fields.showCustomerNo = true
          fields.showTypes = true
          break
        case 'INVENTORY':
        case 'Sales':
          break
        default:
          break
      }

      return fields
    },
    getTasks() {
      if (this.caseId) {
        const matchedCase = this.cases.find((caseItem) => caseItem._id === this.caseId)
        return matchedCase ? matchedCase.tasks : []
      }
      return []
    },
    loading() {
      if (this.status === 'connecting') {
        return true
      } else {
        return false
      }
    },
    getStatus() {
      if (this.status === 'connecting') {
        return 'Connecting...'
      } else if (this.status === 'connected') {
        return 'connected'
      } else {
        return 'failed'
      }
    },
    statusColor() {
      if (this.status === 'connecting') {
        return 'text-[#21427D]'
      } else if (this.status === 'connected') {
        return 'text-green-700'
      } else {
        return 'text-red-700'
      }
    },
    iconColor() {
      if (this.status === 'connecting') {
        return '#21427D'
      } else if (this.status === 'connected') {
        return 'green'
      } else {
        return 'red'
      }
    },
    getUserDefinedCaseStatus() {
      if (this.types) {
        const foundType = this.types.find((branchType) => branchType.name === this.type)
        return foundType ? foundType.userDefinedCaseStatus : null
      }
      return null
    }
  },
  async mounted() {
    const orgData = JSON.parse(localStorage.getItem('orgData'))
    const orgModules = orgData?.plan_details?.MODULES
    if (orgModules?.COLLECTION_MANAGEMENT?.enable) {
      this.modules.push('CASES')
    }
    if (orgModules?.SELLS?.enable) {
      this.modules.push('INVENTORY')
    }

    if (this.modules.length > 0) {
      this.singleModule = this.modules[0]
    }

    const userRole = JSON.parse(localStorage.getItem('user'))
    let role
    if (userRole) {
      this.loginMobile = String(userRole?.user?.mobile)
      this.agentName = `${userRole?.user?.first_name} ${userRole?.user?.last_name}`
      role = userRole?.role.find((roleId) => roleId === 'WFM_ADMIN')
    }
    const subcases = this.$store.state.subCases
    if (role === 'WFM_ADMIN') {
      this.types = subcases[0].subCase
    } else {
      this.types = subcases[0].subCase.map((sub) => {
        if (sub.branch === userRole.branch) {
          return sub
        } else {
          return
        }
      })
    }
    if (this.types) {
      this.userDefinedCaseStatus = this.types.find((branchType) => branchType.name === this.type)
    }

    if (this.$props.callItem) {
      const filteredType = this.types.find(
        (typeFilter) => typeFilter.name === this.$props.callItem.additionalInfo.caseInfo.caseType
      )
      if (filteredType) {
        this.type = filteredType
      }
      this.filterCase = this.$props.callItem?.additionalInfo?.caseInfo?.caseId

      this.loginMobile = this.$props?.callItem.agentNumber
      this.number = this.$props.callItem?.customerNumber
      this.additionalInfo = {
        caseInfo: this.$props.callItem?.additionalInfo?.caseInfo,
        agentName: this.$props.callItem?.additionalInfo?.agentName,
        phoneNoId: this.$props.callItem?.additionalInfo?.phoneNoId
      }
      this.callIdentifier = this.$props?.callItem?.identifier
      let retry = true
      this.handleInitiate(retry)
    }
    await this.getVoiceNumbers()
    this.position = {
      x: 0,
      y: 0
    }
  }
}
</script>

<style scoped>
.fixed {
  position: fixed;
}

.bottom-right {
  bottom: 20px;
  right: 20px;
}

.draggable-card {
  position: relative; /* Change from fixed to relative */
  cursor: move;
  width: 500px;
  z-index: 9999;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin: 0 auto;
}

.animated-icon {
  font-size: 50px;
  transition: transform 0.3s ease-in-out;
}

.animated-icon:hover {
  transform: scale(1.1);
}

.arrow-icon-container {
  display: flex;
  gap: 2px;
}

.arrow-icon {
  width: 5px;
  animation: blink-arrow 1.5s infinite;
}

.arrow-icon:nth-child(2) {
  animation-delay: 0.3s;
}

.arrow-icon:nth-child(3) {
  animation-delay: 0.6s;
}
.arrow-icon:nth-child(4) {
  animation-delay: 0.9s;
}
.arrow-icon:nth-child(5) {
  animation-delay: 0.12s;
}
.arrow-icon:nth-child(6) {
  animation-delay: 0.15s;
}

@keyframes blink-arrow {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.status-text {
  font-size: 14px;
  color: #9ae08c;
}

.call-time {
  font-size: 12px;
  margin-top: 4px;
}
</style>
