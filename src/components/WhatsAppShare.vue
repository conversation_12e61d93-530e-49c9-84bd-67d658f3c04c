<template>
  <div>
    <v-icon
      size="20"
      color="green"
      class="mr-2"
      @click="openShareDialog = true"
      v-tooltip="{
        text: 'Share on WhatsApp',
      }"
      >mdi-whatsapp</v-icon
    >

    <v-dialog v-model="openShareDialog" width="600">
      <v-card>
        <v-card-title class="headline">Edit Message</v-card-title>
        <v-card-text>
          <v-textarea
            v-model="customMessage"
            label="Message"
            rows="10"
            outlined
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="blue darken-1" text @click="cancelShare">Cancel</v-btn>
          <v-btn color="green darken-1" text @click="sendWhatsAppMessage"
            >Send</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  props: {
    defaultMessage: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      openShareDialog: false,
      customMessage: this.defaultMessage,
    };
  },
  methods: {
    sendWhatsAppMessage() {
      const encodedMessage = encodeURIComponent(this.customMessage);
      const whatsappUrl = `https://api.whatsapp.com/send?text=${encodedMessage}`;
      window.open(whatsappUrl, "_blank");
      this.openShareDialog = false;
    },
    cancelShare() {
      this.openShareDialog = false;
    },
  },
};
</script>

<style scoped>
/* Add your custom styles here */
</style>
