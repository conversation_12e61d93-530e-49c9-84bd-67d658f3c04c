<template>
  <div class="flex">
    <v-speed-dial v-model="fab" :direction="direction" open-on-hover>
      <template v-slot:activator>
        <v-btn
          v-model="fab"
          :color="$vuetify.theme.currentTheme.primary"
          dark
          fab
          x-small
          elevation="0"
        >
          <v-icon :color="iconColor" v-if="fab">mdi-close</v-icon>
          <v-icon :color="iconColor" v-else>{{ icon }}</v-icon>
        </v-btn>
      </template>
      <v-btn
        fab
        dark
        x-small
        color="green"
        v-if="showEditButton"
        :disabled="isButtonDisabled"
      >
        <v-icon
          v-tooltip="{
            text: 'Edit',
          }"
          @click="() => handleEdit(item)"
          v-show="canEdit"
        >
          mdi-pencil-outline
        </v-icon>
      </v-btn>

      <v-btn v-if="showViewButton" fab dark x-small color="blue">
        <v-icon
          v-tooltip="{
            text: 'View Contacts',
          }"
          @click="() => handleView(item)"
        >
          mdi-cellphone
        </v-icon>
      </v-btn>
      <v-btn v-if="showLeaveButton" fab dark x-small color="#FFCC00">
        <v-icon
          v-tooltip="{
            text: `${leaveIconText}`,
          }"
          @click="() => handleLeaveAction(item)"
        >
          {{ leaveIcon }}
        </v-icon>
      </v-btn>
      <v-btn v-if="showExpenseButton" fab dark x-small color="#2A83FF">
        <v-icon
          v-tooltip="{
            text: `${expenseIconText}`,
          }"
          @click="() => handleExpenseAction(item)"
        >
          {{ expenseIcon }}
        </v-icon>
      </v-btn>
      <v-btn
        v-if="showDeleteButton"
        fab
        dark
        x-small
        color="red"
        :disabled="isButtonDisabled"
      >
        <v-icon
          v-tooltip="{
            text: 'Delete',
          }"
          @click="() => handleDelete(item)"
          v-show="canDelete"
        >
          mdi-delete-outline
        </v-icon>
      </v-btn>
      <v-btn
        v-if="showAddTaskButton"
        fab
        dark
        x-small
        color="indigo"
        :disabled="isButtonDisabled"
      >
        <v-icon
          v-tooltip="{
            text: `${plusIconText}`,
          }"
          v-show="canWrite"
          @click="() => addTaskAction(item)"
        >
          {{ showAddIcon }}
        </v-icon>
      </v-btn>
    </v-speed-dial>
  </div>
</template>

<script>
export default {
  props: {
    item: Object,
    canEdit: Boolean,
    canDelete: Boolean,
    canWrite: Boolean,
    addTaskAction: Function,
    handleEdit: Function,
    handleDelete: Function,
    handleView: Function,
    handleLeaveAction: Function,
    handleExpenseAction: Function,
    plusIconText: { type: String, default: "Task" },
    showAddIcon: { type: String, default: "mdi-plus" },
    leaveIconText: { type: String, default: "Leave" },
    leaveIcon: { type: String, default: "mdi-calendar-multiple-check" },
    expenseIconText: { type: String, default: "Set Expense Limit" },
    expenseIcon: { type: String, default: "mdi-cash-multiple" },
    iconColor: {
      type: String,
      default: "white",
    },
    icon: {
      type: String,
      default: "mdi-menu",
    },
    bg: {
      type: String,
      default: "blue darken-1",
    },
    showViewButton: {
      type: Boolean,
      default: false,
    },
    showAddTaskButton: {
      type: Boolean,
      default: false,
    },
    showDeleteButton: {
      type: Boolean,
      default: true,
    },
    showEditButton: {
      type: Boolean,
      default: true,
    },
    isButtonDisabled: {
      type: Boolean,
      default: false,
    },
    showLeaveButton: {
      type: Boolean,
      default: false,
    },
    showExpenseButton: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fab: false,
      direction: "left",
    };
  },
};
</script>

<style scoped>
/* Add your component-specific styles here */
</style>
