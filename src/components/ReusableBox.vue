<template>
  <div :class="boxClass" class="shadow-lg h-full rounded-xl flex flex-col justify-center items-center"
    v-if="number || number == 0">
    <p class="text-gray-100 mb-0 font-semibold text-lg">{{ content }}</p>
    <p class="text-2xl font-semibold text-gray-100 mb-0">{{ number }}</p>
  </div>
  <!-- <v-skeleton-loader v-else class="mx-auto stats-loader" max-height="120" type="image"></v-skeleton-loader> -->
</template>

<script>
export default {
  name: "ReusableBox",
  props: {
    number: {
      type: Number,
    },
    color: {
      type: String,
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
  },
  computed: {
    boxClass() {
      return this.color;
    },
  },
};
</script>

<style>
.v-application .stats-loader {
  border-radius: 30px;
  padding: 0 !important;
}

.stats-loader .v-skeleton-loader__image {
  height: 100px;
}
</style>
