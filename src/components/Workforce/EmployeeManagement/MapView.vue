<template>
  <div class="map-container bg-white dark-bg-custom">
    <div v-if="isLoading" class="loading-spinner">
      <v-progress-circular indeterminate :size="70" :width="4" color="black">
      </v-progress-circular>
    </div>
    <div v-else id="map" style="width: 100%; height: 700px"></div>
  </div>
</template>

<script>
/* global google */

export default {
  name: "MapComponent",
  data() {
    return {
      map: null,
      isLoading: true,
    };
  },
  async mounted() {
    this.isLoading = true;
  },
  props: {
    userLiveLocation: Array,
  },
  watch: {
    userLiveLocation: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.isLoading = false;
          this.initMap();
        }
      },
      deep: true,
    },
  },

  methods: {
    initMap() {
      const script = document.createElement("script");
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.VUE_APP_MAP_KEY}&callback=initMap`;
      script.async = true;
      script.defer = true;
      script.onload = () => {
        this.map = new google.maps.Map(document.getElementById("map"), {
          center: { lat: 28.6448, lng: 77.216721 },
          zoom: 5,
        });
        this.infoWindows = [];

        // Loop through each user's live location
        this.userLiveLocation.forEach((user) => {
          const { latitude, longitude } = user.coords;
          const position = { lat: latitude, lng: longitude };
          const marker = new google.maps.Marker({
            position: position,
            map: this.map,
          });

          // Create an info window that always opens
          const batteryLevel = this.calculateBatteryLevel(user) || 50;
          const infowindowContent = `<div class="flex">
             <div>
              <img src="https://th.bing.com/th/id/OIP.BlUKujG01kQ2x4rXzbHoUAHaHa?rs=1&pid=ImgDetMain" alt="Avatar" style="width: 50px; height: 50px; border-radius: 50%;">
              </div>
              <div class="ml-1">
                <strong>${user.userDetails?.firstName || ""} ${user.userDetails?.lastName || ""
            }</strong>
              <div><strong>Phone</strong>: ${user.userDetails?.mobile || ""
            }</div>
              <div><strong>Email</strong>: ${user.userDetails?.email || ""
            }</div>
              <div><strong>Employee ID</strong>: ${user.userDetails?.employeeId
            }</div>
              <div class="w-48"><strong>At Address: </strong><span id="address-span-${user.roamUserId
            }">Fetching address... </span> <strong>(since ${this.$dayjs(
              user.lastTracked + "z"
            ).format("DD-MM-YYYY hh:mm A")})</strong></div>
              
                <div class="flex">
                  <strong>Battery Level</strong>: <img src="https://static.vecteezy.com/system/resources/previews/016/774/440/original/battery-charging-icon-on-transparent-background-free-png.png" alt="battery" style="width: 20px; height: 15px;"> ${batteryLevel}%
                </div>
              </div>
              </div>
              `;

          const infowindow = new google.maps.InfoWindow({
            content: infowindowContent,
          });
          infowindow.open(this.map, marker);

          marker.addListener("click", () => {
            this.infoWindows.forEach((window) => {
              window.close();
            });
            infowindow.open(this.map, marker);
          });
          const geocoder = new google.maps.Geocoder();
          geocoder.geocode({ location: position }, (results, status) => {
            if (status === "OK") {
              if (results[0]) {
                const address = results[0].formatted_address;
                const addressElement = document.getElementById(
                  `address-span-${user.roamUserId}`
                );
                if (addressElement) {
                  addressElement.textContent = `${address}`;
                }
              } else {
                window.alert("No results found");
              }
            } else {
              window.alert("Geocoder failed due to: " + status);
            }
          });
        });
      };
      document.head.appendChild(script);
    },
    calculateBatteryLevel(user) {
      const batteryPercentage = user.batteryPercentage || 50;
      return batteryPercentage;
    },
  },
};
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 700px;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
