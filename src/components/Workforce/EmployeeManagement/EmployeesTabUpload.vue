<template>
  <div class="pa-4 px-10 py-5">
    <h3 class="mt-5">Select File to Upload employees</h3>
    <v-file-input
      class="mt-5"
      label="Select File"
      outlined
      dense
      v-model="file"
      accept=".xlsx"
    ></v-file-input>
    <div class="flex mt-3 gap-2 flex-wrap">
      <v-btn
        :color="$vuetify.theme.currentTheme.primary"
        @click="handleDownload"
        class="white--text"
        >Download Sample(XLSX)</v-btn
      >
      <v-btn
        :color="$vuetify.theme.currentTheme.primary"
        class="white--text"
        @click="upload()"
        :loading="uploading"
        :disabled="uploading"
      >
        Upload
      </v-btn>
      <v-btn @click="cancel()" outlined> Cancel </v-btn>
    </div>
    <v-alert dense text type="info" id="errorList" v-if="msg"
      >{{ msg }}

      <p class="mb-0">Total : {{ success + failed }}</p>
      <p class="mb-0">Success : {{ success }}</p>
      <p class="mb-0">Failed : {{ failed }}</p>
    </v-alert>
    <v-btn v-if="invalidUsers.length" @click="downloadFile">
      Download Failed Status<v-icon color="red">mdi-download</v-icon>
    </v-btn>

    <div v-if="invalidUsers && invalidUsers.length > 0" class="mt-4">
      <v-alert type="error" text outlined class="mt-2" :icon="false">
        <strong>Failed Upload Details:</strong>
        <div style="max-height: 300px; overflow-y: auto">
          <v-list dense>
            <v-list-item v-for="(user, index) in invalidUsers" :key="index">
              <v-list-item-content>
                <v-list-item-title>
                  {{ index + 1 }} - Employee ID: {{ user.employee_id }} - Reason:
                  <span class="multiline-text">{{ user.failedReason }}</span>
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </div>
      </v-alert>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmployeesTabUploadComponent',
  props: ['companyCode', 'financialPartnerType'],
  data() {
    return {
      orgUser: this.$storage.getUniversal('user'),
      file: null,
      msg: null,
      success: 0,
      failed: 0,
      uploading: false,
      fileName: 'Failed_Employee_Entry',
      invalidUsers: []
    }
  },

  methods: {
    async upload() {
      if (!this.file) {
        this.$toast.error('Please Upload a file')
        return
      }

      const orgId = this.companyCode || this.orgUser?.organization_id
      this.msg = 'uploading'
      this.uploading = true
      var formData = new FormData()
      formData.append('attachments', this.file)
      formData.append('organization_id', orgId)

      try {
        const response = await this.$axios.post('/workforce/user/bulk', formData)

        this.success = response.data.data.usersCreated
        this.failed = response.data?.data?.usersFailed

        this.invalidUsers = response.data.data?.invalidUsers
        if (this.success === 0) {
          this.msg = 'Uploading Failed'
          this.$toast.error('Employees creation failed')
        } else if (this.success > 0 && this.failed > 0) {
          this.msg = `Uploading and creation of ${this.failed} employees failed, download failed status.`
          this.$toast.error(`Uploading and creation of ${this.failed} employees failed.`)
        } else {
          this.msg = 'Employees registered successfully'
          const successMessage = response?.data?.message || 'Employees registered successfully'
          this.$toast.success(successMessage)
          this.uploading = false
        }

        if (response.data?.data?.usersFailed === 0 && response.data?.success) {
          this.$emit('refreshEmployees')
        }
      } catch (error) {
        console.log('Failed', error)
        const errorMessage = error?.response?.data?.message || error
        this.$toast.error(errorMessage)
        this.uploading = false
      } finally {
        this.uploading = false
      }
    },
    cancel() {
      this.$emit('cancelDrawer')
      this.msg = null
      this.file = null
      this.success = 0
      this.failed = 0
      this.invalidUsers = []
    },
    handleDownload() {
      this.$emit('callParentFunction')
    },
    downloadFile() {
      const XLSX = require('xlsx')
      const data = this.invalidUsers.length ? this.invalidUsers : []
      const ws = XLSX.utils.json_to_sheet(data)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'Data')
      const filename = `${this.fileName}.xlsx`
      XLSX.writeFile(wb, filename)
    }
  }
}
</script>

<style scoped>
#errorList {
  margin-top: 20px;
}
</style>
