<template>
  <div class="w-full h-full min-h-screen flex flex-col">
    <div class="hierarchy-tree w-full flex-1 relative overflow-hidden">
      <div class="absolute top-4 right-[160px] z-10">
        <div class="relative inline-block">
          <v-select
            v-model="currentDirection"
            :items="directions"
            item-text="label"
            item-value="value"
            @change="changeDirection"
            outlined
            dense
            class="text-xs font-medium text-gray-700"
          >
          </v-select>
        </div>
      </div>

      <div id="svg-tree" class="w-full h-full absolute" v-if="userData.length"></div>
      <NoData
        v-else
        title="Nothing to display"
        subTitle="Please create the hierarchy-tree for your organization"
        class="w-full h-full flex items-center justify-center absolute inset-0"
      />
    </div>
  </div>
</template>

<script>
import NoData from '@/components/NoData.vue'
import ApexTree from 'apextree'

export default {
  name: 'OrgHierarchyTree',
  components: {
    NoData
  },
  data() {
    return {
      userData: [],
      baseUrl: process.env.VUE_APP_S3_BASE_URL,
      currentDirection: 'top',
      isDropdownOpen: false,
      tree: null,
      directions: [
        { label: 'Top to Bottom', value: 'top' },
        { label: 'Bottom to Top', value: 'bottom' },
        { label: 'Left to Right', value: 'left' },
        { label: 'Right to Left', value: 'right' }
      ]
    }
  },
  mounted() {
    this.fetchEmployees()
    // Close dropdown when clicking outside
    document.addEventListener('click', this.handleClickOutside)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    toggleDropdown() {
      this.isDropdownOpen = !this.isDropdownOpen
    },
    handleClickOutside(event) {
      const dropdown = this.$el.querySelector('.dropdown-container')
      if (dropdown && !dropdown.contains(event.target)) {
        this.isDropdownOpen = false
      }
    },

    changeDirection(direction) {
      this.currentDirection = direction
      this.isDropdownOpen = false
      this.renderApexTree()
    },
    async fetchEmployees() {
      try {
        const allUser = {
          limit: '*',
          sortBy: 'firstName',
          sortOrder: 'asc'
        }
        const response = await this.$axios.get('workforce/v2/users', {
          params: allUser
        })
        if (response.data.success) {
          this.userData = this.structureData(response.data.users)
          this.$nextTick(() => {
            this.renderApexTree()
          })
        }
      } catch (error) {
        console.error('Error fetching employees:', error)
      }
    },
    structureData(data) {
      const map = {}

      data.forEach((item) => {
        const id = item._id
        if (!map[id]) {
          map[id] = { ...item, children: [] }
        }
      })

      const result = []

      data.forEach((item) => {
        const id = item._id
        const reportsToName = item.reportesTo?._id

        if (reportsToName) {
          if (!map[reportsToName]) {
            map[reportsToName] = { id: reportsToName, children: [] }
          }
          map[reportsToName].children.push(map[id])
        } else {
          if (map[id].children.length > 0 || item.reportesTo == null) {
            result.push(map[id])
          }
        }
      })

      return result
    },
    renderApexTree(autoFit = false) {
      if (!this.userData.length) return

      this.$nextTick(() => {
        const popupContainer = document.querySelector('.hierarchy-tree')

        // Get exact container dimensions
        let containerWidth = popupContainer ? popupContainer.offsetWidth : window.innerWidth
        let containerHeight = popupContainer ? popupContainer.offsetHeight : window.innerHeight

        // Ensure minimum dimensions
        containerWidth = Math.max(containerWidth, 800)
        containerHeight = Math.max(containerHeight, 600)

        // For auto-fit, calculate optimal dimensions based on tree size
        if (autoFit) {
          const nodeCount = this.countNodes(this.userData[0])
          const estimatedWidth = Math.max(containerWidth, nodeCount * 200)
          const estimatedHeight = Math.max(containerHeight, 600)
          containerWidth = estimatedWidth
          containerHeight = estimatedHeight
        }

        const apexData = this.convertToApexFormat(this.userData[0])

        const options = {
          contentKey: 'data',
          width: containerWidth,
          height: containerHeight,
          nodeWidth: 180,
          nodeHeight: 80,
          childrenSpacing: 70,
          siblingSpacing: 40,
          direction: this.currentDirection,
          fontSize: '20px',
          fontFamily: 'sans-serif',
          fontWeight: '600',
          fontColor: '#a06dcc',
          borderWidth: 2,
          borderColor: '#a06dcc',
          canvasStyle: ' border: 2px solid #ccc; background: #f6f6f6; width: 100%; height: 100%;',
          nodeTemplate: (content) => {
            let imageUrl = content.imageURL
            let roleName = content.role || ''

            return `<div style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; height: 100%; box-shadow: 1px 2px 4px #ccc; padding: 0 10px; border-radius: 6px;">   
                <img style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;" src="${imageUrl}" alt="">
                <div style="display: flex; flex-direction: column; align-items: flex-start; margin-left: 10px;">
                  <div style="font-weight: bold; font-family: Arial; font-size: 14px">${content.name}</div>
                  <div style="font-family: Arial; font-size: 12px; color: #21427D;">${roleName}</div>
                </div>
              </div>`
          },
          nodeStyle: 'box-shadow: 0px 4px 8px -2px rgba(0,0,0,0.2); border-radius: 6px;',
          enableToolbar: true
        }

        // Clear existing tree
        const treeContainer = document.getElementById('svg-tree')
        if (treeContainer) {
          treeContainer.innerHTML = ''
        }

        // Initialize ApexTree
        this.tree = new ApexTree(document.getElementById('svg-tree'), options)
        this.tree.render(apexData)
      })
    },
    countNodes(node) {
      let count = 1
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          count += this.countNodes(child)
        })
      }
      return count
    },
    convertToApexFormat(node) {
      const convertNode = (userNode) => {
        const role = userNode.wfmRole ? userNode.wfmRole.role : ''
        const bgColor = this.getRoleColor(role)
        const result = {
          id: userNode._id,
          data: {
            name: userNode.fullName || '',
            imageURL: this.getEmployeeImage(userNode),
            role: role
          },
          options: {
            nodeBGColor: bgColor
          }
        }

        // Convert children if they exist
        if (userNode.children && userNode.children.length > 0) {
          result.children = userNode.children.map((child) => convertNode(child))
        }

        return result
      }

      return convertNode(node)
    },
    getEmployeeImage(item) {
      if (item.profileImage) {
        return `${this.baseUrl}${item.profileImage}`
      } else {
        if (item.gender === 'M') {
          return require('~/assets/img/profileMale.png')
        } else if (item.gender === 'F') {
          return require('~/assets/img/profileFemale.png')
        } else {
          return require('~/assets/img/profileOther.png')
        }
      }
    },
    getRoleColor(role) {
      const roleColors = {
        CEO: '#94ddff',
        Director: '#ffc7c2',
        Manager: '#e3c2ff',
        'Team Lead': '#d2edc5',
        Developer: '#e9f08f'
      }

      return roleColors[role] || '#ffffff'
    }
  }
}
</script>

<style scoped>
/* Additional styles for better dropdown visibility */
</style>
