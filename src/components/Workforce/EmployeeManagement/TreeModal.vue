<template>
  <div v-if="isOpen" class="modal-overlay" @click.self="closeModal">
    <div class="modal-content bg-white dark-bg-custom">
      <div class="flex w-full justify-between items-center">
        <h2 class="font-bold text-xl">Organization Structure</h2>
        <v-btn icon @click="closeModal" color="black">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </div>
      <ul class="pl-0">
        <tree-node />
      </ul>
    </div>
  </div>
</template>

<script>
import TreeNode from './TreeNode.vue'

export default {
  components: {
    TreeNode
  },
  props: {
    isOpen: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {}
  },
  methods: {
    closeModal() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  padding: 20px;
  border-radius: 8px;
  max-width: 80vw;
  width: 100%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.close-button {
  background-color: transparent;
  border: none;
  font-size: 20px;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
}
</style>
