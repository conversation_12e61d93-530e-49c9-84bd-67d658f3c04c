<template>
  <div>
    <v-container>
      <v-form class="px-10" ref="form" v-model="valid" lazy-validation>
        <!-- <h2> Add Employee </h2> -->
        <!-- <v-card class="pa-2 mt-2" outlined dense  tile> -->
        <!-- <v-alert type="error">
          In the manual creation of employee you might face some issue, so use
          bulk creation process.
        </v-alert> -->
        <div class="mb-8">
          <v-row class="row pb-6 mt-0">
            <v-col cols="12">
              <strong>Profile</strong>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <v-col cols="12" md="4">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.first_name"
                :rules="rules.first_name"
                label="First Name *"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.middle_name"
                label="Middle Name"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.last_name"
                label="Last Name"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.mobile"
                label="Mobile *"
                type="number"
                :rules="rules.mobile"
                :disabled="this.disabled"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field outlined dense v-model="form.email" label="Email"></v-text-field>
            </v-col>
          </v-row>
          <v-row class="row pb-6 mt-0">
            <v-col cols="12">
              <strong>Employee Detail</strong>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.employee_id"
                label="Employee ID *"
                :disabled="this.disabled"
                :rules="rules.employee_id"
                :clearable="true"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-select
                :items="employeeTypes"
                v-model="form.employee_type"
                label="Employee Type *"
                item-text="name"
                item-value="name"
                dense
                outlined
                class="pr-3"
                :rules="rules.employee_type"
                :clearable="true"
              ></v-select>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <v-col cols="12" md="4">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.department"
                :rules="rules.department"
                label="Department"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4">
              <v-text-field
                outlined
                dense
                v-model="form.designation"
                :rules="rules.designation"
                label="Designation"
                class="pr-3"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.salary"
                label="Salary per month"
                type="number"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row class="row mt-0">
            <v-col cols="12">
              <strong>Permanent Address (Optional)</strong>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <!-- <CommonAutocomplete
                class="pr-3"
                :addressLine1="permanetAddressLine1"
                v-model="permanetSelectedAddress"
                @selected-place="handleSelectedPlace"
              /> -->
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.permanent.addressLine1"
                label="Address Line 1"
                hide-details
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.permanent.city"
                label="City"
                hide-details
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.permanent.state"
                label="State"
                hide-details
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.permanent.country"
                label="Country"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.permanent.pinCode"
                label="Pin Code"
                type="number"
              ></v-text-field>
            </v-col>
          </v-row>
          <!-- <v-row class="row mt-0">
            <v-col cols="12">
              <strong>Current Address (Optional)</strong>
            </v-col>
            <v-col cols="12">
              <v-checkbox
                v-model="form.address.sameAsPermanent"
                type="checkbox"
                label="Same as permanent address"
              />
            </v-col>
          </v-row> -->
          <v-row v-if="!form.address.sameAsPermanent">
            <v-col cols="12">
              <CommonAutocomplete
                class="pr-3"
                :addressLine1="currentAddressLine1"
                v-model="currentSelectedAddress"
                @selected-place="handleSelectedPlaceCurrent"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.current.city"
                label="City"
                hide-details
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.current.state"
                label="State"
                hide-details
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.current.country"
                label="Country"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.address.current.pinCode"
                label="Pin Code"
                type="number"
              ></v-text-field>
            </v-col>
          </v-row>

          <!-- <v-row class="row pb-6 mt-0">
            <v-col cols="12">
              <strong>Bank Details (Optional)</strong>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <v-col cols="12" md="6">
              <v-text-field
                class="pr-3"
                outlined
                dense
                v-model="form.ifsc_code"
                label="IFSC Code"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <div class="flex">
                <v-text-field
                  class="pr-3"
                  outlined
                  dense
                  v-model="form.account_number"
                  label="Account Number"
                  :prepend-inner-icon="
                    !!employee?.is_account_number_verified
                      ? 'mdi-check-circle'
                      : 'mdi-close-circle'
                  "
                ></v-text-field>
                <v-btn
                  v-if="!employee?.is_account_number_verified"
                  :loading="fetchingVerificationName"
                  @click="fetchVerificationName(employee.user_id)"
                  color="primary"
                >
                  Verify
                </v-btn>
              </div>
            </v-col>
          </v-row> -->
          <!-- </v-card> -->

          <!--  Corporate expenses -->
          <v-container
            v-if="orgConfig?.organization_config?.is_corporate_expense_enabled"
            class="px-0"
            fluid
          >
            <v-row class="row pb-6 mt-0">
              <v-col cols="12">
                <strong>Coporate Expense</strong>
              </v-col>
            </v-row>

            <v-checkbox
              :color="$vuetify.theme.currentTheme.primary"
              v-model="form.is_corporate_expense_enabled"
              :label="`Enable Corporate Expenses.`"
            ></v-checkbox>
            <v-text-field
              v-if="form.is_corporate_expense_enabled"
              class="pr-3"
              outlined
              dense
              v-model="form.max_corporate_expense_limit"
              label="Corporate expenses limit."
              type="number"
            >
            </v-text-field>
          </v-container>

          <v-divider class="my-4"></v-divider>
          <p class="font-semibold">Assign Manager</p>
          <v-select
            v-model="selectedManager"
            :items="fullNameManagers"
            item-text="fullName"
            item-value="_id"
            label="Select Reportee"
            return-object
            outlined
            dense
          ></v-select>
          <p class="font-semibold">Assign Role</p>
          <v-select
            v-model="selectedRole"
            :items="roleList"
            item-text="role"
            item-value="_id"
            label="Select Role"
            clearable
            return-object
            outlined
            dense
          ></v-select>
          <!-- End -->
        </div>

        <div class="fixed bottom-0 w-full z-50 bg-white dark-bg-custom py-2">
          <v-btn @click="cancel()" outlined class="mr-4"> Cancel </v-btn>
          <v-btn
            @click="saveEmployee()"
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
          >
            Save
          </v-btn>
        </div>
      </v-form>
    </v-container>
    <v-dialog v-model="isAccountVerificationDialogVisible" width="500">
      <v-card>
        <v-card-title class="text-h5 grey lighten-2"> Verify bank account </v-card-title>

        <v-card-text class="mt-3">
          <code>
            {{ accountVerificationResponse }}
          </code>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            text
            @click="verifyBankAccount(employee.user_id)"
          >
            Verify
          </v-btn>

          <v-btn color="danger" text @click="isAccountVerificationDialogVisible = false">
            Cancel
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import CommonAutocomplete from '~/components/CommonAutoComplete.vue'

export default {
  name: 'EmployeesTabAddComponent',
  props: [
    'companyCode',
    'disabled',
    'empData',
    'orgConfig',
    'updateEmployee',
    'managersList',
    'roleList'
  ],
  components: {
    CommonAutocomplete
  },
  data() {
    return {
      permanetAddressLine1: '',
      permanetSelectedAddress: '',
      currentAddressLine1: '',
      currentSelectedAddress: '',
      addressLineCurrent1: '',
      selectedAddressCurrent: '',
      employee: this.empData,
      userData: this.$storage.getUniversal('user'),
      selectedManager: null,
      selectedRole: null,
      employeeTypes: [],
      valid: true,
      is_corporate_expense_enabled: false,
      fetchingVerificationName: false,
      isAccountVerificationDialogVisible: false,
      accountVerificationResponse: '',
      form: {
        user_id: this.empData ? this.empData.user_id : '',
        first_name: this.empData ? this.empData.first_name : '',
        middle_name: this.empData ? this.empData.middle_name : '',
        last_name: this.empData ? this.empData.last_name : '',
        mobile: this.empData ? this.empData.mobile : '',
        email: this.empData ? this.empData.email : undefined,
        employee_id: this.empData ? this.empData.employee_id : '',
        employee_type: this.empData ? this.empData.employee_type : '',
        department: this.empData ? this.empData.department : '',
        manager_id: this.empData ? this.empData.manager_id : '',
        designation: '',
        salary: this.empData ? this.empData.salary : undefined,
        ifsc_code: this.empData ? this.empData.ifsc_code : '',
        account_number: this.empData ? this.empData.bank_account_number : '',
        is_corporate_expense_enabled: this.empData
          ? this.empData.is_corporate_expense_enabled
          : false,
        max_corporate_expense_limit: this.empData ? this.empData.max_corporate_expense_limit : 0,
        address: {
          sameAsPermanent: this.empData?.is_both_address_same || true,
          permanent: {
            addressLine1: this.empData?.permanent_address_line_1 || '',
            addressLine2: this.empData?.permanent_address_line_2 || '',
            city: this.empData?.permanent_address_city || '',
            state: this.empData?.permanent_address_state || '',
            country: this.empData?.permanent_address_country || '',
            pinCode: this.empData?.permanent_address_pincode || ''
          },
          current: {
            addressLine1: this.empData?.current_address_line_1 || '',
            addressLine2: this.empData?.current_address_line_2 || '',
            city: this.empData?.current_address_city || '',
            state: this.empData?.current_address_state || '',
            country: this.empData?.current_address_country || '',
            pinCode: this.empData?.current_address_pincode || ''
          }
        }
      },
      validCharRegex: /^[\w\s!@#$%^&*()-_+=?/<>;:'",.]+$/,
      alphanumRegex: /^\w+$/,

      rules: {
        first_name: [(v) => !!v || 'First Name is required'],
        mobile: [
          (v) => !!v || 'Mobile is required',
          (v) => /^[0-9]{10}$/.test(v) || 'Please enter a valid Mobile number'
        ],
        employee_id: [(v) => this.alphanumRegex.test(v) || 'Employee ID is required'],
        employee_type: [(v) => !!v || 'Employee Type is required'],
        department: [(v) => this.validCharRegex.test(v) || 'Please enter a valid department'],
        designation: [(v) => this.validCharRegex.test(v) || 'Please enter a valid designation']
      }
    }
  },
  mounted() {
    this.getEmployeeTypes()
  },
  watch: {
    managersList: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initializeSelectedManager()
        }
      }
    }
  },
  computed: {
    fullNameManagers() {
      return this.managersList.map((manager) => ({
        ...manager,
        fullName:
          manager._id === this.userData._id
            ? 'Self'
            : `${manager.firstName} ${manager.lastName} - ${manager.wfmRole.role}`
      }))
    }
  },
  methods: {
    getLoggedInUserId() {
      return this.userData
    },
    initializeSelectedManager() {
      this.selectedManager = this.getLoggedInUserId()
    },
    handleSelectedPlaceCurrent(place) {
      this.currentSelectedAddress = place.formatted_address
      this.form.address.current.addressLine1 = this.currentSelectedAddress
      this.form.address.current.city = place.city
      this.form.address.current.state = place.state
      this.form.address.current.country = place.country
      this.form.address.current.pinCode = place.pincode
    },
    handleSelectedPlace(place) {
      console.log('place', place)
      this.permanetSelectedAddress = place.formatted_address
      this.form.address.permanent.addressLine1 = this.permanetSelectedAddress
      this.form.address.permanent.city = place.city
      this.form.address.permanent.state = place.state
      this.form.address.permanent.country = place.country
      this.form.address.permanent.pinCode = place.pincode
    },
    async fetchVerificationName(userId) {
      this.fetchingVerificationName = true
      const { data } = await this.$axios.get(`/bank-transfers/verify-account/${userId}`)
      this.accountVerificationResponse = data.result
      this.isAccountVerificationDialogVisible = true
      this.fetchingVerificationName = false
    },
    async verifyBankAccount(userId) {
      await this.$axios.put(`/bank-transfers/verify-account/${userId}`)

      this.$emit('refreshEmployees')
      this.isAccountVerificationDialogVisible = false
    },
    getEmployeeTypes() {
      this.employeeTypes = []
      this.$axios.get('/ext/employee-types').then((res) => {
        for (let item of res.data) {
          this.employeeTypes.push({ name: item })
        }
      })
    },
    toasterError(msg) {
      this.$toast.error(msg)
    },
    saveEmployee() {
      this.$refs.form.validate()
      const alphanumRegex = /^\w+$/
      const validCharRegex = /^[\w\s!@#$%^&*()-_+=?/<>;:'",.]+$/

      this.form.first_name = this.form.first_name.trim()
      this.form.last_name = this.form.last_name.trim()
      if (!alphanumRegex.test(this.form.first_name) || this.form.first_name == undefined) {
        this.toasterError('Please enter valid First Name')
        return false
      }
      if (!/^[0-9]{10}$/.test(this.form.mobile) || this.form.mobile == undefined) {
        this.toasterError('Please enter valid Mobile')
        return false
      }
      if (!alphanumRegex.test(this.form.employee_id) || this.form.employee_id == undefined) {
        this.toasterError('Please enter valid Employee ID')
        return false
      }
      if (this.form.employee_type == '' || this.form.employee_type == undefined) {
        this.toasterError('Please enter the Employee type')
        return false
      }
      if (this.form.department && !validCharRegex.test(this.form.department)) {
        this.toasterError('Please enter valid department')
        return false
      }
      if (this.form.designation && !validCharRegex.test(this.form.designation)) {
        this.toasterError('Please enter valid designaiton')
        return false
      }
      if (this.form.ifsc_code && !alphanumRegex.test(this.form.ifsc_code)) {
        this.toasterError('Please enter valid IFSC code')
        return false
      }
      // Check if the corporate expense is enabled and limit is greater then zero or not
      if (this.form.is_corporate_expense_enabled == true) {
        if (this.form.max_corporate_expense_limit <= 0) {
          this.toasterError('Please enter an amount greater than zero')
          return false
        }
      } else {
        this.form.max_corporate_expense_limit = 0
      }
      if (this.form.account_number && !alphanumRegex.test(this.form.account_number)) {
        this.toasterError('Please enter valid acocunt number')
        return false
      }

      const currentAddrs = this.form.address.sameAsPermanent
        ? this.form.address.permanent
        : this.form.address.current
      const userAddress = {
        address_type: 'BILLING',
        addressLine1: currentAddrs.addressLine1,
        addressLine2: currentAddrs.addressLine2 || '',
        city: currentAddrs.city,
        state: currentAddrs.state,
        country: currentAddrs.country,
        pinCode: currentAddrs.pinCode
      }
      const payload = {
        ...this.form,
        userAddress,
        organization_id: this.companyCode || this.userData.organization_id
      }
      delete payload.address
      this.$axios
        .post('/workforce/cualitywork/employee', payload)
        .then((res) => {
          if (res.data?.success || res.status == 201) {
            if (res.data.message == 'Update Success') {
              this.updateEmployee(this.companyCode, this.form.employee_id)
              this.$toast.success('Employee updated successfully')
            } else {
              this.$toast.success('Employee registered successfully')
            }
            const manager = this.selectedManager

            const newUser = {
              ...res.data?.data,
              manager,
              selectedRole: this.selectedRole
            }

            this.$emit('refreshEmployees', newUser)
            this.form = {
              first_name: '',
              middle_name: '',
              last_name: '',
              mobile: '',
              employee_id: '',
              employee_type: '',
              department: '',
              manager_id: '',
              designation: '',
              ifsc_code: '',
              account_number: '',
              is_corporate_expense_enabled: 0,
              address: {
                sameAsPermanent: true,
                permanent: {
                  addressLine1: '',
                  addressLine2: '',
                  city: '',
                  state: '',
                  country: '',
                  pinCode: ''
                },
                current: {
                  addressLine1: '',
                  addressLine2: '',
                  city: '',
                  state: '',
                  country: '',
                  pinCode: ''
                }
              }
            }
          }
        })
        .catch((err) => {
          console.log('err ', err.response)
          if (err.response.data.message) {
            this.$toast.error(err.response.data.message)
          }
        })
    },
    cancel() {
      this.$refs.form.resetValidation()
      this.form = {
        first_name: '',
        middle_name: '',
        last_name: '',
        mobile: '',
        employee_id: '',
        employee_type: '',
        department: '',
        manager_id: '',
        designation: '',
        ifsc_code: '',
        account_number: '',
        is_corporate_expense_enabled: 0,
        address: {
          sameAsPermanent: true,
          permanent: {
            addressLine1: '',
            addressLine2: '',
            city: '',
            state: '',
            country: '',
            pinCode: ''
          },
          current: {
            addressLine1: '',
            addressLine2: '',
            city: '',
            state: '',
            country: '',
            pinCode: ''
          }
        }
      }
      this.$emit('cancelDrawer')
    }
  }
}
</script>

<style scoped>
.flex {
  display: flex;
}
</style>
