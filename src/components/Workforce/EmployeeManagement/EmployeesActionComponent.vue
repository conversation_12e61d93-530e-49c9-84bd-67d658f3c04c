<template>
  <div class="dark-bg-custom h-screen">
    <div class="fixed top-0 w-full z-50">
      <v-tabs v-model="tab" background-color="primary" :color="$vuetify.theme.currentTheme.primary">
        <v-tabs-slider color="white"></v-tabs-slider>

        <v-tab
          v-for="(item, index) in filteredTab"
          :key="item"
          :style="getUnselectedTabColor(tab, index)"
          :color="$vuetify.theme.currentTheme.primary"
        >
          {{ item }}
        </v-tab>
      </v-tabs>
    </div>
    <v-card color="basil" flat class="h-screen pt-12">
      <EmployeesTabAddComponent
        v-bind:companyCode="companyCode"
        v-if="tab === 0"
        v-on:refreshEmployees="(val) => refreshEmployee(val)"
        v-on:cancelDrawer="cancelDrawer()"
        :managersList="managersList"
        :roleList="roleList"
      />
      <div
        v-if="
          financialPartnerType === 'FINFI' || user?.role[0] === 'SUPER_ADMIN' || isWfmAccessEnabled
        "
      >
        <EmployeesTabUploadComponent
          v-bind:companyCode="companyCode"
          v-bind:financialPartnerType="financialPartnerType"
          v-if="tab === 1"
          v-on:refreshEmployees="refreshEmployee()"
          v-on:cancelDrawer="cancelDrawer()"
          @callParentFunction="emitToParentFileDownload"
        />
      </div>
    </v-card>
  </div>
</template>

<script>
import EmployeesTabAddComponent from './EmployeesTabAdd.vue'
import EmployeesTabUploadComponent from './EmployeesTabUpload.vue'
import { demoEmployeesData } from '@/utils/demoFormat'
import { isModuleFeatureAllowed } from '@/utils/common'
export default {
  name: 'EmployeesActionTabs',
  props: ['companyCode', 'financialPartnerType', 'managersList', 'roleList'],
  components: {
    EmployeesTabAddComponent,
    EmployeesTabUploadComponent
  },
  data() {
    return {
      tab: null,
      items: ['Add Employee', 'Upload Employee'],
      user: this.$storage.getUniversal('user')
    }
  },
  mounted() {
    if (this.financialPartnerType === 'NBFC' && this.user?.role[0] !== 'SUPER_ADMIN') {
      this.items.splice(1, 1)
    }
  },
  computed: {
    getCompanyCode() {
      return this.companyCode
    },
    isWfmAccessEnabled() {
      return this.user?.role.includes('WFM_ADMIN')
    },
    filteredTab() {
      if (this.isFeatureVisible('CREATE_BULK_EMPLOYEES') && this.isWfmAccessEnabled) {
        return this.items
      }
      return ['Add Employee']
    }
  },
  methods: {
    refreshEmployee(val) {
      this.$emit('refreshEmployeeAction', val)
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('EMPLOYEE_MANAGEMENT', feature)
    },
    cancelDrawer() {
      this.$emit('cancelDrawerAction')
    },
    emitToParentFileDownload() {
      this.downloadFile('xlsx', demoEmployeesData, 'Sample_User_Format')
      // this.$emit("callParentFunction");
    },
    downloadFile(format, data, fileName) {
      if (format === 'csv') {
        this.downloadCSV(data, `${fileName}.csv`)
      } else if (format === 'xlsx') {
        this.downloadXLSX(data, `${fileName}.xlsx`)
      }
    },
    downloadXLSX(data, fileName) {
      const XLSX = require('xlsx')
      const excelSheet = XLSX.utils.json_to_sheet(data)
      const newBook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(newBook, excelSheet, 'Sheet1')
      XLSX.writeFile(newBook, `${fileName}`)
    },
    getUnselectedTabColor(tab, index) {
      if (tab !== index) {
        return {
          color: '#99ff99'
        }
      }
    }
  }
}
</script>
