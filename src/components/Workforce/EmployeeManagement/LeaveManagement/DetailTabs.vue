<template>
  <div>
    <!-- Tabs Navigation -->
    <div class="flex justify-between bg-white items-center">
      <v-tabs
        v-model="activeTab"
        class="bg-white dark-bg-default shadow-md flex justify-between pl-2"
      >
        <v-tab>Employee Details</v-tab>
        <v-tab>Onboarding</v-tab>
        <v-tab>Offboarding</v-tab>
      </v-tabs>

      <v-icon @click="handleModalClose" class="mx-4">mdi-close</v-icon>
    </div>

    <!-- Tabs Content -->
    <v-tabs-items v-model="activeTab">
      <!-- Employee Details Tab -->
      <v-tab-item>
        <employee-details
          :selected-user-profile="selectedUserProfile"
          :branch="branch"
        />
      </v-tab-item>

      <!-- Onboarding Tab -->
      <v-tab-item>
        <div class="bg-gray-100 p-4">
          <div class="flex justify-between">
            <h3 class="text-xl font-bold mb-4">On-Boarding Questions</h3>
            <v-btn color="primary" small @click="saveOnboard">Save</v-btn>
          </div>

          <!-- Questions -->
          <v-row>
            <!-- Selected Questions -->
            <v-col
              v-for="(question, index) in selectedOnboardingQuestions"
              :key="index"
              cols="12"
              md="6"
              class="mb-4"
            >
              <div class="p-4 pt-0 bg-gray-100 rounded-lg shadow-md relative">
                <div class="flex justify-between my-2">
                  <p class="font-semibold mb-0 mt-2 text-sm">
                    ( {{ index + 1 }} )
                    {{ question.question }}
                  </p>

                  <v-icon
                    @click="removeOnboardingQuestion(index)"
                    color="red"
                    small
                    v-tooltip="{
                      text: 'Remove',
                    }"
                    class="absolute top-2 right-2 cursor-pointer"
                  >
                    mdi-delete
                  </v-icon>
                </div>

                <!-- Display Different Response Types -->
                <div v-if="question.userResponse" class="ml-6 text-sm">
                  <div v-if="question.questionType === 'TEXT'">
                    {{ question.userResponse }}
                  </div>
                  <div v-else-if="question.questionType === 'TEXTAREA'">
                    {{ question.userResponse }}
                  </div>
                  <div v-else-if="question.questionType === 'CHECKBOX'">
                    <ul>
                      <li v-for="(option, i) in question.userResponse" :key="i">
                        - {{ option }}
                      </li>
                    </ul>
                  </div>
                  <div v-else-if="question.questionType === 'RADIO'">
                    {{ question.userResponse }}
                  </div>
                  <div v-else-if="question.questionType === 'image'">
                    <img
                      :src="question.userResponse"
                      alt="Uploaded"
                      class="w-24 h-24 rounded-md"
                    />
                  </div>
                  <div v-else-if="question.questionType === 'LOCAL_ATTACHMENT'">
                    <a
                      :href="question.userResponse"
                      target="_blank"
                      class="text-blue-500 hover:underline"
                    >
                      View Attachment
                    </a>
                  </div>
                </div>
                <div v-else class="ml-6 text-sm">
                  <p>No user response</p>
                </div>
              </div>
            </v-col>

            <!-- Add New Question Card -->
            <v-col cols="12" md="6" class="mb-4" v-if="newQuestionVisible">
              <div class="p-4 bg-gray-100 rounded-lg shadow-md">
                <v-select
                  v-model="newQuestion.selected"
                  :items="availableQuestions"
                  item-text="question"
                  item-value="question"
                  label="Select a Question"
                  class="my-4"
                  @change="addNewQuestion"
                ></v-select>
              </div>
            </v-col>
          </v-row>
        </div>
      </v-tab-item>

      <!-- Offboarding Tab -->
      <v-tab-item>
        <div class="bg-gray-100 p-4">
          <div class="flex justify-between">
            <h3 class="text-xl font-bold mb-4">Off-Boarding Questions</h3>
            <v-btn color="primary" small @click="saveOffboard">Save</v-btn>
          </div>
          <v-row>
            <v-col
              v-for="(question, index) in selectedOffboardingQuestions"
              :key="index"
              cols="12"
              md="6"
              class="mb-4"
            >
              <div class="p-4 pt-0 bg-gray-100 rounded-lg shadow-md relative">
                <div class="flex justify-between my-2">
                  <p class="font-semibold mb-0 mt-2 text-sm">
                    ( {{ index + 1 }} )
                  </p>

                  <v-icon
                    @click="removeOffboardingQuestion(index)"
                    color="red"
                    small
                    v-tooltip="{
                      text: 'Remove',
                    }"
                    class="absolute top-2 right-2 cursor-pointer"
                  >
                    mdi-delete
                  </v-icon>
                </div>
                <p class="font-semibold">{{ question.question }}</p>
                <p class="text-sm text-gray-500 mb-2">
                  Type: {{ question.type }}
                </p>
                <div v-if="question.type === 'text'">
                  {{ question.response }}
                </div>
                <div v-else-if="question.type === 'textarea'">
                  {{ question.response }}
                </div>
                <div v-else-if="question.type === 'checkbox'">
                  <ul>
                    <li
                      v-for="(option, i) in question.response"
                      :key="i"
                      class="text-gray-700"
                    >
                      - {{ option }}
                    </li>
                  </ul>
                </div>
                <div v-else-if="question.type === 'radio'">
                  {{ question.response }}
                </div>
                <div v-else-if="question.type === 'image'">
                  <img
                    :src="question.response"
                    alt="Uploaded"
                    class="w-24 h-24 rounded-md"
                  />
                </div>
                <div v-else-if="question.type === 'attachment'">
                  <a
                    :href="question.response"
                    target="_blank"
                    class="text-blue-500 hover:underline"
                  >
                    View Attachment
                  </a>
                </div>
              </div>
            </v-col>

            <!-- Add New Question Card -->
            <v-col
              cols="12"
              md="6"
              class="mb-4"
              v-if="newOffboardingQuestionVisible"
            >
              <div class="p-4 bg-gray-100 rounded-lg shadow-md">
                <v-select
                  v-model="newOffboardingQuestion.selected"
                  :items="availableOffboardingQuestions"
                  item-text="question"
                  item-value="question"
                  label="Select a Question"
                  class="my-4"
                  @change="addNewOffboardingQuestion"
                ></v-select>
              </div>
            </v-col>
          </v-row>
        </div>
      </v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script>
import EmployeeDetails from "./EmployeeDetails.vue";

export default {
  components: {
    EmployeeDetails,
  },
  props: {
    selectedUserProfile: Object,
    branch: Object,
  },
  data() {
    return {
      activeTab: 0,
      selectedOnboardingQuestions: [],
      selectedOffboardingQuestions: [],
      newQuestion: {
        selected: null,
      },
      newQuestionVisible: false,
      onboardingQuestions: [],
      offboardingQuestions: [],
      newOffboardingQuestion: {
        selected: null,
      },
      newOffboardingQuestionVisible: false,
    };
  },
  watch: {
    selectedUserProfile: {
      async handler(newVal) {
        this.selectedUserProfile = newVal;
        // await this.fetchRoleWiseQuestions();
        await this.fetchEmpQuestions();
      },
    },
  },

  computed: {
    availableQuestions() {
      return this.onboardingQuestions.filter(
        (q) =>
          !this.selectedOnboardingQuestions.some(
            (sq) => sq.question === q.question
          )
      );
    },
    availableOffboardingQuestions() {
      return this.offboardingQuestions.filter(
        (q) =>
          !this.selectedOffboardingQuestions.some(
            (sq) => sq.question === q.question
          )
      );
    },
  },
  methods: {
    handleModalClose() {
      this.selectedOffboardingQuestions = [];
      this.selectedOnboardingQuestions = [];
      this.$emit("handleClose");
    },
    addNewQuestion() {
      const selected = this.onboardingQuestions.find(
        (q) => q.question === this.newQuestion.selected
      );

      if (selected) {
        this.selectedOnboardingQuestions.push({ ...selected });
        this.newQuestion.selected = null;
        this.newQuestionVisible = this.availableQuestions.length > 0;
      }
    },
    removeOnboardingQuestion(index) {
      this.selectedOnboardingQuestions.splice(index, 1);
      this.newQuestionVisible = true;
    },
    addNewOffboardingQuestion() {
      const selected = this.offboardingQuestions.find(
        (q) => q.question === this.newOffboardingQuestion.selected
      );
      if (selected) {
        this.selectedOffboardingQuestions.push({ ...selected });
        this.newOffboardingQuestion.selected = null;
        this.newOffboardingQuestionVisible =
          this.availableOffboardingQuestions.length > 0;
      }
    },
    removeOffboardingQuestion(index) {
      this.selectedOffboardingQuestions.splice(index, 1);
      this.newOffboardingQuestionVisible = true;
    },
    async fetchRoleWiseQuestions() {
      try {
        const role = this.selectedUserProfile?.wfmRole?._id;
        if (!role) {
          return;
        }
        const { data } = await this.$axios.get(
          `workforce/onboardexit/config/${role}`
        );
        this.onboardingQuestions = data.data?.onboard_data?.questions;
        this.offboardingQuestions = data.data?.exit_data?.questions;
        this.selectedOnboardingQuestions = [...this.onboardingQuestions];
        this.selectedOffboardingQuestions = [...this.offboardingQuestions];
      } catch (error) {
        console.log(error);
        this.$toast.error(error.response?.data?.message);
      }
    },
    async fetchEmpQuestions() {
      try {
        const { data } = await this.$axios.get(
          `workforce/onboardexit/empData/${this.selectedUserProfile._id}`
        );

        this.onboardingQuestions = data.data?.onboard_data?.questions;
        this.offboardingQuestions = data.data?.exit_data?.questions;
        this.selectedOnboardingQuestions = [...this.onboardingQuestions];
        this.selectedOffboardingQuestions = [...this.offboardingQuestions];
      } catch (error) {
        console.log(error);
      }
    },
    async saveOnboard() {
      try {
        if (!this.selectedUserProfile._id) {
          this.$toast.error("User id not found");
          return;
        }
        const payload = {
          userId: this.selectedUserProfile._id,
          onboard_data: {
            questions: this.selectedOnboardingQuestions,
            onboarding_status: "IN_PROGRESS",
          },
        };

        await this.$axios.post("workforce/onboardexit/onboard", payload);
        this.$toast.success(
          `Onboarding Questions updated successfully ${this.selectedUserProfile.firstName}`
        );
      } catch (error) {
        console.log(error);
      }
    },
    async saveOffboard() {
      try {
        if (!this.selectedUserProfile._id) {
          this.$toast.error("User id not found");
          return;
        }
        const payload = {
          userId: this.selectedUserProfile._id,
          exit_data: {
            questions: this.selectedOffboardingQuestions,
            exitStatus: "IN_PROGRESS",
          },
        };

        await this.$axios.post("workforce/onboardexit/exit", payload);
        this.$toast.success(
          `Onboarding Questions updated successfully ${this.selectedUserProfile.firstName}`
        );
      } catch (error) {
        console.log(error);
      }
    },
  },
  async mounted() {
    // await this.fetchRoleWiseQuestions();
    await this.fetchEmpQuestions();
  },
};
</script>

<style scoped></style>
