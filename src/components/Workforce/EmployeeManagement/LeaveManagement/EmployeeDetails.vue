<template>
  <div class="p-8 bg-gray-100 dark-bg-default">
    <!-- Header -->
    <div class="mb-4 flex justify-between">
      <h4 class="text-xl font-bold">Profile</h4>
      <!-- <v-icon @click="handleModalClose">mdi-close</v-icon> -->
    </div>

    <!-- Profile Section -->
    <div class="flex justify-between">
      <!-- Left Section -->
      <div class="w-1/3 bg-white dark-bg-custom p-6 rounded-lg shadow-lg">
        <div class="flex flex-col items-center">
          <div class="bg-gray-200 dark-bg-custom p-2 rounded-full">
            <img
              :src="profileImageUrl"
              alt="User Image"
              class="w-16 h-16 rounded-full"
            />
          </div>
          <p class="mt-4 font-bold text-gray-700">
            {{ firstName }} {{ lastName }}
          </p>
        </div>
        <ul class="mt-8 space-y-4 text-gray-600 dark-text-color">
          <li v-if="empId"><strong>Employee Id:</strong> {{ empId }}</li>
          <li v-if="domain"><strong>Org Role:</strong> {{ domain }}</li>
          <li v-if="reporting">
            <strong>Reporting To:</strong> {{ reporting }}
          </li>
          <li v-if="selectedUserProfile?.department">
            <strong>Department:</strong> {{ selectedUserProfile?.department }}
          </li>
          <li v-if="selectedUserProfile?.designation">
            <strong>Designation:</strong> {{ selectedUserProfile?.designation }}
          </li>
          <li v-if="branch?.branchName">
            <strong>Branch:</strong> {{ branch?.branchName }}
          </li>
          <li>
            <strong>Status:</strong>
            <span class="text-green-600">{{
              selectedUserProfile.userStatus
            }}</span>
          </li>
        </ul>
      </div>

      <!-- Right Section -->
      <div class="w-2/3 ml-6">
        <!-- User Info -->
        <div class="bg-white dark-bg-custom p-6 rounded-lg shadow-lg mb-6">
          <h3 class="text-lg font-semibold text-gray-700 mb-4">User Info</h3>
          <div class="space-y-2 text-gray-600">
            <p><strong>User Id:</strong> {{ selectedUserProfile.userId }}</p>
            <p><strong>First Name:</strong> {{ firstName }}</p>
            <p><strong>Last Name:</strong> {{ lastName }}</p>
            <p>
              <strong>Mobile Number:</strong>
              {{ selectedUserProfile.maskedMobileNumber }}
            </p>
            <p><strong>Email:</strong> {{ email }}</p>
            <p><strong>CTC:</strong> {{ selectedUserProfile?.ctc }}</p>
            <p><strong>Address:</strong> {{ address }}</p>
          </div>
        </div>

        <!-- User Documents -->
        <div class="bg-white dark-bg-custom p-6 rounded-lg shadow-lg">
          <h3 class="text-lg font-semibold text-gray-700 mb-4">
            User Documents
          </h3>
          <div class="grid grid-cols-3 gap-4">
            <a
              v-for="document in selectedUserProfile.documents"
              :key="document.id"
              :href="getDocumentUrl(document)"
              target="_blank"
              class="flex flex-col items-center"
            >
              <img
                :src="getImageSrc(document)"
                alt="Document"
                class="w-24 h-24 object-cover rounded-md"
              />
              <p class="mt-2 text-sm text-gray-700">
                {{ document.typeOfDocument || "Unknown Type" }}
              </p>
            </a>
          </div>

          <!-- Upload Section -->
          <div class="mt-6">
            <h4 class="text-md font-semibold mb-4">Upload New Document</h4>
            <v-text-field
              v-model="typeOfDocument"
              placeholder="Type of document"
              label="Type of document"
              outlined
              dense
            ></v-text-field>
            <div class="flex items-center space-x-4">
              <input
                type="file"
                @change="handleFileUpload"
                class="block w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200"
              />
              <button
                class="bg-blue-500 text-white px-4 py-2 rounded-md shadow-md hover:bg-blue-600"
                :disabled="isUploading"
                @click="uploadDocument"
              >
                <span v-if="!isUploading">Upload</span>
                <span v-else>Uploading...</span>
              </button>
            </div>
            <p v-if="uploadError" class="text-red-500 text-sm mt-2">
              {{ uploadError }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { fullName, getFullAddress } from "@/utils/common";

export default {
  props: {
    selectedUserProfile: Object,
    branch: Object,
  },
  data() {
    return {
      selectedFile: null,
      uploadError: "",
      typeOfDocument: "",
      isUploading: false,
    };
  },

  computed: {
    profileImageUrl() {
      return this.getEmployeeImage(this.selectedUserProfile);
    },
    empId() {
      return this.selectedUserProfile.employeeId;
    },
    domain() {
      return this.selectedUserProfile?.wfmRole?.role || "";
    },
    reporting() {
      return fullName(this.selectedUserProfile?.reportesTo) || "";
    },
    userStatus() {
      return this.selectedUserProfile.userStatus === "ACTIVE"
        ? "Enabled"
        : "Disabled";
    },
    email() {
      return this.selectedUserProfile.maskedEmail;
    },
    address() {
      return getFullAddress(this.selectedUserProfile);
    },
    firstName() {
      return this.selectedUserProfile?.firstName;
    },
    lastName() {
      return this.selectedUserProfile?.lastName;
    },
  },
  methods: {
    getImageSrc(image) {
      if (this.isImage(image.url)) {
        return `${process.env.VUE_APP_S3_BASE_URL}${image.url}`;
      } else if (this.isPDF(image.url)) {
        return require("@/assets/img/pdf.png");
      } else if (this.isWord(image.url)) {
        return require("@/assets/img/word.jpg");
      } else {
        return "";
      }
    },
    isImage(src) {
      return /\.(jpg|jpeg|png|gif)$/i.test(src);
    },
    isPDF(src) {
      return /\.pdf$/i.test(src);
    },
    isWord(src) {
      return /\.(doc|docx)$/i.test(src);
    },
    getDocumentUrl(document) {
      return `${process.env.VUE_APP_S3_BASE_URL}${document.url}`;
    },
    getEmployeeImage(item) {
      if (item.profileImage) {
        return `${process.env.VUE_APP_S3_BASE_URL}${item?.profileImage}`;
      } else {
        if (item.gender === "M") {
          return require("~/assets/img/profileMale.png");
        } else if (item.gender === "F") {
          return require("~/assets/img/profileFemale.png");
        } else {
          return require("~/assets/img/profileOther.png");
        }
      }
    },
    handleFileUpload(event) {
      this.selectedFile = event.target.files[0];
    },
    async uploadDocument() {
      if (!this.selectedFile) {
        this.uploadError = "Please select a file to upload.";
        return;
      }
      if (this.isUploading) return;

      this.isUploading = true;
      try {
        const formData = new FormData();
        formData.append("attachments", this.selectedFile);
        formData.append("typeOfDocument", this.typeOfDocument);

        const response = await this.$axios.post(
          "/workforce/add/user/documents",
          formData
        );

        const newDocument = {
          typeOfDocument: this.typeOfDocument,
          url: response.data.doc.doc_physical_path,
          id: response.data.doc.doc_id,
          _id: response.data.doc.doc_id,
        };

        this.selectedUserProfile.documents.push(newDocument);

        this.selectedFile = null;
        this.uploadError = "";
        this.typeOfDocument = "";
      } catch (error) {
        this.uploadError = "Failed to upload document. Please try again.";
        console.error(error);
      } finally {
        this.isUploading = false;
      }
    },
  },
};
</script>
