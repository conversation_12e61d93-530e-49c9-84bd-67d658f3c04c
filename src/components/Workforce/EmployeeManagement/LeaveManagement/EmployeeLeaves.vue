<template>
  <v-container fluid class="bg-white dark-bg-custom px-6 py-6">
    <v-row class="flex justify-between" no-gutters>
      <h4 class="mb-0 text-xl font-bold">Leaves</h4>
      <v-icon @click="handleClose">mdi-close</v-icon>
    </v-row>
    <v-row>
      <v-col cols="12" class="d-flex align-center">
        <v-avatar size="80" class="mr-4">
          <img :src="employeeImage" alt="Employee Photo" />
        </v-avatar>
        <div>
          <div class="text-h6 font-semibold">
            {{ employeeFullName(employeeData) }}
          </div>
          <div class="text-body-1">{{ employeeData.wfmRole?.role }}</div>
          <div class="text-body-2">{{ employeeData.wfmRole?.description }}</div>
        </div>
        <v-spacer></v-spacer>
        <v-row>
          <v-col class="text-body-2 text-gray-600 dark-text-color">
            <strong>Emp ID:</strong> <br />{{ employeeData.employeeId }}
          </v-col>
          <v-col class="text-body-2 text-gray-600 dark-text-color">
            <strong>Status:</strong><br />
            <v-chip outlined color="green" small>{{
              employeeData.userStatus
            }}</v-chip>
          </v-col>
          <v-col class="text-body-2 text-gray-600 dark-text-color">
            <strong>Work Phone:</strong> <br />{{ employeeData.mobile }}
          </v-col>
          <v-col class="text-body-2 text-gray-600 dark-text-color">
            <strong>Work Email:</strong> <br />
            {{ employeeData.email || "-" }}
          </v-col>
        </v-row>
      </v-col>
    </v-row>

    <v-divider class="mt-4"> </v-divider>
    <!-- Apply for Leave Button -->
    <v-row>
      <v-col cols="12" class="mb-4">
        <div v-if="selectedQuota?.length" class="flex justify-end">
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="mt-4 mr-4 white--text"
            v-if="selectedQuota?.length"
            @click="applyLeave({}, false)"
            >Apply Leave</v-btn
          >
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="mt-4 mr-4 white--text"
            @click="openQuotaDialog"
            >Edit Quota</v-btn
          >
        </div>
        <no-data
          v-else
          title="Nothing to Display"
          :subTitle="`Start by adding employee quota`"
          :btnText="'Add Quota'"
          :btnAction="openQuotaDialog"
          :callTab="callTab"
        />
      </v-col>
    </v-row>

    <!-- Leaves Cards -->
    <v-row>
      <v-col
        cols="12"
        sm="6"
        md="4"
        v-for="quota in quotas"
        :key="quota.typeOfLeave"
      >
        <v-card class="pa-4">
          <div class="text-h6 font-semibold flex justify-between">
            {{ convertCase(quota.typeOfLeave) }}
            <span>
              <div class="flext justify-end">
                <v-chip
                  small
                  color="primary"
                  class="mr-4 cursor-pointer"
                  @click="editLeave(quota)"
                  :disabled="!isEditDisabled(quota)"
                  >Edit</v-chip
                >
                <v-chip
                  small
                  color="primary"
                  class="cursor-pointer"
                  @click="applyLeave(quota, true)"
                  >Add</v-chip
                >
              </div>
            </span>
          </div>
          <v-row>
            <v-col>
              <p
                class="text-sm text-green-600"
                hide-details
                :style="{ color: quota.color }"
              >
                Available
              </p>
              <div
                class="text-h4 text-green-600"
                :style="{ color: quota.color }"
              >
                {{ quota.availableBalance }}
                <span class="text-base">days</span>
              </div>
            </v-col>
            <v-col>
              <v-row class="text-gray-500 text-xs text-center">
                <v-col>
                  Planned
                  <br />
                  <span class="font-semibold"
                    >{{ getPlannedDays(quota.typeOfLeave) }} days</span
                  >
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-divider></v-divider>
          <div class="text-gray-500 text-xs mt-2">
            {{ quota?.usedBalance }} days used
          </div>
          <v-progress-linear
            :value="progressCalculate(quota)"
            height="8"
            class="my-2"
          ></v-progress-linear>
          <div class="flex justify-between">
            <v-chip
              class="mt-2"
              label
              :color="statusColor('PENDINGLEAVE').bgColor"
              :text-color="statusColor('PENDINGLEAVE').textColor"
              small
              v-if="isPrevLeavePending(quota)"
            >
              Pending Request
            </v-chip>
            <v-btn
              class="white--text"
              :color="$vuetify.theme.currentTheme.primary"
              :disabled="!hasLeaveDetails(quota)"
              @click="viewLeaveDetails(quota)"
              >View Details</v-btn
            >
          </div>
        </v-card>
      </v-col>
    </v-row>

    <!-- Add Leave Dialog -->
    <v-dialog v-model="showAddLeaveDialog" max-width="700px" persistent>
      <v-card :color="$vuetify.theme.currentTheme.cardColor">
        <v-card-title>
          <v-row class="flex justify-between" no-gutters>
            <h4 class="text-xl font-bold">
              {{ editMode ? "Edit Leave" : "Apply Leave" }}
            </h4>

            <v-icon @click="closeLeaveDialog">mdi-close</v-icon>
          </v-row>
        </v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-select
              v-model="selectedLeaveType"
              :items="formattedLeaves"
              item-text="itemText"
              item-value="typeOfLeave"
              label="Select Leave Type"
              outlined
              :disabled="disableLeaveTypeSelect"
              dense
            ></v-select>
            <v-menu
              v-model="menu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="dates"
                  multiple
                  chips
                  small-chips
                  label="Select leave dates"
                  readonly
                  clearable
                  outlined
                  dense
                  v-on="on"
                  v-bind="attrs"
                  @click="openMenu"
                  :rules="durationValidation"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="dates"
                no-title
                multiple
                scrollable
                :events="Object.keys(leaveEvents)"
                :event-color="getEventColor"
                :allowed-dates="allowDates"
                :min="minDate"
                :max="maxDate"
              />
            </v-menu>
            <v-text-field
              v-model="durationWithSuffix"
              label="Duration"
              outlined
              disabled
              dense
            ></v-text-field>
            <v-select
              v-model="selectedApproval"
              label="Select Approver"
              :items="employees"
              :item-text="getFullName"
              item-value="_id"
              placeholder="Start Approver"
              hide-no-data
              multiple
              clearable
              outlined
              :rules="approverValidation"
              dense
            >
            </v-select>
            <v-textarea
              v-model="comments"
              outlined
              dense
              label="Comments"
            ></v-textarea>
            <v-file-input
              label="Supporting Documents"
              outlined
              dense
            ></v-file-input>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn outlined class="mr-4" @click="closeLeaveDialog">Cancel</v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="addLeave"
            >Save</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Add Quota -->
    <v-dialog v-model="showAddQuotaDialog" max-width="600px">
      <v-card>
        <v-card-title
          class="flex justify-between items-center sticky top-0 dark-bg-custom z-10"
        >
          <h4 class="text-xl font-bold">
            {{ selectedQuota?.length ? "Edit" : "Add" }} Employee Leave Quota
          </h4>
          <v-icon @click="cancelQuota">mdi-close</v-icon>
        </v-card-title>
        <v-alert type="info" color="blue" text dense
          >Edit quota will change the employee assigned quota.</v-alert
        >
        <v-card-text class="mt-2">
          <v-form ref="form">
            <v-row v-for="(quota, i) in quotaPolicy" :key="i">
              <v-checkbox
                v-model="quota.checked"
                :color="$vuetify.theme.currentTheme.primary"
              ></v-checkbox>
              <v-col>
                <v-select
                  v-model="quota.typeOfLeave"
                  :items="orgQuota"
                  label="Leave Quota Type"
                  outlined
                  hide-details
                  dense
                  disabled
                ></v-select>
              </v-col>
              <v-col>
                <v-text-field
                  v-model="quota.numberOfDays"
                  label="Number of Days"
                  outlined
                  dense
                  type="number"
                  hide-details
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn outlined class="mr-4" @click="cancelQuota">Cancel</v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="addQuota"
            >Save</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- leave Details -->
    <v-dialog v-model="showLeaveDetailsDialog" max-width="700px">
      <v-card>
        <v-card-title class="flex justify-between">
          <span>Leave Details</span>
          <v-icon @click="showLeaveDetailsDialog = false">mdi-close</v-icon>
        </v-card-title>
        <v-card-text>
          <v-list>
            <v-list-item v-for="leave in selectedLeaveDetails" :key="leave._id">
              <v-list-item-content
                class="border border-blue-200 pa-4 rounded-md mb-2"
              >
                <v-list-item-title>
                  Approver Names:
                  {{ formatNames(leave.requestApprovalsfrom).join(", ") }}
                </v-list-item-title>
                <v-list-item-subtitle
                  >Leave Dates:
                  <v-chip-group column>
                    <v-chip
                      v-for="date in formatLeave(leave.leaveDates)"
                      :key="date"
                      class="ma-1"
                      small
                    >
                      {{ date }}
                    </v-chip>
                  </v-chip-group>
                </v-list-item-subtitle>
                <v-list-item-subtitle
                  >Duration: {{ leave.durations }}</v-list-item-subtitle
                >
                <v-list-item-subtitle
                  >Status: {{ leave.leaveStatus }}</v-list-item-subtitle
                >
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn outlined @click="showLeaveDetailsDialog = false">Close</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { fullName, convertToTitleCase, debounce } from "@/utils/common";
import { taskStatusColors } from "@/utils/workforce/statusColors";
import NoData from "@/components/NoData.vue";
export default {
  data() {
    return {
      userData: this.$storage.getUniversal("user"),
      reporteeChain: this.$storage.getUniversal("userReporteeChain"),
      quotas: [],
      showAddLeaveDialog: false,
      showAddQuotaDialog: false,
      searchUser: "",
      selectedApproval: [],
      employees: [],
      orgQuota: [],
      dates: [],
      leaves: [],
      checkedQuotas: [],
      quotaPolicy: [],
      showLeaveDetailsDialog: false,
      selectedLeaveDetails: [],
      supportingDocuments: null,
      selectedQuota: [],
      selectedLeaveType: null,
      comments: "",
      selectedLeaveDays: null,
      selectedDuration: 0,
      autoApproved: false,
      menu: false,
      editMode: false,
      disableLeaveTypeSelect: true,
      datepickerOpened: false,
    };
  },
  components: {
    NoData,
  },
  props: {
    employeeData: Object,
    employeeImage: String,
    orgConfigSettings: Object,
    showLeadManagement: Boolean,
  },
  watch: {
    employeeData: {
      async handler(newData) {
        if (newData) {
          await this.getQuota();
          await this.getLeaves();
          await this.fetchApproval();
        }
      },
    },
    dates(newDates) {
      if (newDates) {
        this.updateDuration();
      }
    },
  },
  computed: {
    checkPendingReq() {
      const isPendingLeave = this.leaves.some((leave) => {
        return (
          leave.typeOfLeaveApplied === this.selectedLeaveType.typeOfLeave &&
          leave.leaveStatus === "PENDING"
        );
      });

      return isPendingLeave;
    },
    isEditDisabled() {
      return (quota) => {
        return this.leaves.some(
          (leave) =>
            leave.typeOfLeaveApplied === quota.typeOfLeave &&
            leave.leaveStatus === "PENDING"
        );
      };
    },
    formattedLeaves() {
      return this.quotas?.map((quota) => ({
        ...quota,
        itemText: `${quota.typeOfLeave} - ${quota.availableBalance} days`,
      }));
    },
    durationWithSuffix() {
      return `${this.selectedDuration}d`;
    },
    durationValidation() {
      if (!this.selectedDuration) {
        return ["Please select the dates."];
      }
      if (this.selectedLeaveType && this.selectedDuration) {
        const quota = this.quotas.find(
          (q) => q.typeOfLeave === this.selectedLeaveType.typeOfLeave
        );
        if (quota && parseInt(this.selectedDuration) > quota.availableBalance) {
          return [
            `Selected duration exceeds available balance (${quota.availableBalance} days) for ${this.selectedLeaveType.typeOfLeave}.`,
          ];
        }
      }
      return [];
    },
    approverValidation() {
      if (!this.selectedApproval) {
        return ["Please select atleast apporver"];
      }
      return [];
    },
    minDate() {
      const now = this.$dayjs();
      const currentYear = now.year();
      return now.isBefore(`${currentYear}-04-01`)
        ? `${currentYear - 1}-04-01`
        : `${currentYear}-04-01`;
    },
    maxDate() {
      const now = this.$dayjs();
      const currentYear = now.year();
      return now.isBefore(`${currentYear}-04-01`)
        ? `${currentYear}-03-31`
        : `${currentYear + 1}-03-31`;
    },
    allowDates() {
      const dayjs = require("dayjs");
      const isBetween = require("dayjs/plugin/isBetween");
      dayjs.extend(isBetween);

      const workingDays = this.orgConfigSettings?.workingDays || [];
      const holidays = this.orgConfigSettings?.listOfHolidays || [];

      const isWorkingDay = (date) => {
        const dayOfWeekString = dayjs(date).format("dddd").toUpperCase();
        return workingDays.includes(dayOfWeekString);
      };

      const isHoliday = (date) => {
        const dateToCheck = dayjs(date).startOf("day");
        return holidays.some((holiday) => {
          const startDate = dayjs(holiday.holidayStartDate).startOf("day");
          const endDate = dayjs(holiday.holidayEndDate).endOf("day");
          return (
            dateToCheck.isSame(startDate) ||
            dateToCheck.isBetween(startDate, endDate, null, "[]")
          );
        });
      };

      const isAllowedDate = (date) => {
        return isWorkingDay(date) && !isHoliday(date);
      };

      return isAllowedDate;
    },
    leaveEvents() {
      return this.leaves.reduce((acc, leave) => {
        leave.leaveDates.forEach((date) => {
          const formattedDate = new Date(date).toISOString().substr(0, 10);
          acc[formattedDate] = leave.leaveStatus;
        });
        return acc;
      }, {});
    },
  },
  methods: {
    handleClose() {
      this.$emit("handleLeaveClose");
    },
    employeeFullName(item) {
      if (item) {
        return fullName(this.employeeData);
      }
    },
    convertCase(str) {
      return convertToTitleCase(str);
    },
    populateLeaves() {
      this.orgQuota = this.orgConfigSettings?.commonLeaves.map(
        (el) => el.typeOfLeave
      );
    },
    progressCalculate(item) {
      if (item.usedBalance) {
        const cal =
          (item.usedBalance / (item.usedBalance + item.availableBalance)) * 100;
        return cal;
      }
      return;
    },
    getFullName(employee) {
      return (
        fullName(employee) +
        (employee.wfmRole ? ` (${employee.wfmRole.role})` : "")
      );
    },
    editLeave(quota) {
      const lastPendingLeave = this.leaves.find(
        (leave) =>
          leave.typeOfLeaveApplied === quota.typeOfLeave &&
          leave.leaveStatus === "PENDING"
      );

      //change above Pending to PENDING when leave edit is possible

      if (lastPendingLeave) {
        this.selectedApproval = lastPendingLeave.requestApprovalsfrom.map(
          (el) => el.approvalFrom
        );
        this.selectedLeaveType = {
          typeOfLeave: lastPendingLeave.typeOfLeaveApplied,
        };

        this.dates = this.formatLeave(lastPendingLeave.leaveDates);
        this.selectedDuration = parseInt(lastPendingLeave.durations);
        this.supportingDocuments = lastPendingLeave.supportingDocuments || null;
        this.comments = lastPendingLeave.comments
          ? lastPendingLeave.comments[0].comment
          : "";
        this.editMode = true;
        this.selectedLeave = lastPendingLeave;
        this.showAddLeaveDialog = true;
      } else {
        this.$toast.error(
          "No pending leave request found for this leave type."
        );
      }
    },

    async addLeave() {
      try {
        const payload = {
          userId: this.employeeData._id,
        };
        if (this.selectedApproval) {
          payload.requestApprovalsfrom = this.selectedApproval.map((el) => {
            return { approvalFrom: el._id };
          });
          payload.supportingDocuments = "";
          payload.typeOfLeaveApplied =
            this.selectedLeaveType?.typeOfLeave || this.selectedLeaveType;
          payload.duration = `${this.selectedDuration}d`;
          payload.leaveDates = this.dates;
        }
        if (this.comments) {
          payload.comments = [
            {
              commentType: "NEW_REQUEST",
              comment: this.comments,
              commentOn: this.$dayjs().format("YYYY-MM-DD"),
              commentBy: this.userData?._id,
            },
          ];
        }

        const isValid = this.$refs.form.validate();
        if (!isValid) return;

        const url = this.editMode
          ? `/workforce/emp/leave/request/${this.selectedLeave._id}`
          : "/workforce/emp/leave/request";

        const method = this.editMode ? "put" : "post";

        const response = await this.$axios({
          method,
          url,
          data: payload,
        });
        const successMessage =
          response?.data?.message || "Leave request raised successfully";
        this.$toast.success(successMessage);
        this.closeLeaveDialog();
        await this.getQuota();
        await this.getLeaves();
      } catch (error) {
        console.log(error);
      }
    },
    closeLeaveDialog() {
      this.$refs.form.reset();
      this.$refs.form.resetValidation();
      this.showAddLeaveDialog = false;
      this.editMode = false;
    },
    async getLeaves() {
      try {
        const empId = this.employeeData._id;
        const response = await this.$axios.get(
          `/workforce/emp/leave/requests?userId=${empId}`
        );
        this.leaves = response.data?.allEmployeeLeaveRequests;
      } catch (error) {
        console.log(error);
      }
    },
    getPlannedDays(leaveType) {
      const leaves = this.leaves.filter(
        (leave) => leave.typeOfLeaveApplied === leaveType
      );
      return leaves.reduce(
        (total, leave) => total + parseFloat(leave.duration),
        0
      );
    },
    applyLeave(leave, fromDropdown = false) {
      this.selectedLeaveType = leave;

      const isAnyPendingLeave = this.leaves.some((leave) => {
        return leave.leaveStatus === "PENDING";
      });

      if (this.checkPendingReq || isAnyPendingLeave) {
        this.$toast.error(
          "A previous leave request is pending for the leave types."
        );
        return;
      }
      this.disableLeaveTypeSelect = fromDropdown;
      this.selectedApproval = this.employeeData?.reportesTo
        ? [this.employeeData?.reportesTo]
        : "";
      this.searchUser = this.employeeData?.reportesTo?.firstName || "";
      this.showAddLeaveDialog = true;
    },
    // handleSearchEmployee: debounce(function () {
    //   // if (this.searchUser) {
    //   this.fetchApproval();
    //   // }
    // }, 1000),
    async fetchApproval() {
      try {
        const params = {};
        // if (this.searchUser) {
        //   params.search = this.searchUser;
        // }
        if (
          this.userData?.branch &&
          !this.userData?.role?.includes("WFM_ADMIN")
        ) {
          params.branch = this.userData?.branch;
        }
        const response = await this.$axios.get("workforce/v2/users", {
          params: params,
        });
        const emp = response.data.users;
        this.employees = this.reporteeChain[this.employeeData._id]?.managerChain
          ? this.splitAndJoinUsers(
              this.reporteeChain[this.employeeData._id]?.managerChain
            )
          : [this.employeeData];
      } catch (error) {
        console.log(error);
      }
    },
    splitAndJoinUsers(str) {
      return str ? str.split("->").map((item) => item.trim()) : [];
    },
    async addQuota() {
      try {
        const existingQuota = this.selectedQuota?.length > 0;

        const url = existingQuota
          ? `/workforce/emp/leave/quota/${this.selectedQuota[0]._id}`
          : "/workforce/emp/leave/quota";

        const method = existingQuota ? "put" : "post";

        const filteredLeavePolicies = this.quotaPolicy.filter(
          (empLeave) => empLeave.checked
        );

        if (!filteredLeavePolicies.length) {
          this.$toast.error("Please select the suitable leave quota");
        }
        const leaveType = this.selectedLeaveType?.typeOfLeave;
        const usedBalance = this.getPlannedDays(leaveType);

        const empLeave = filteredLeavePolicies.map((empLeave) => {
          return {
            typeOfLeave: empLeave.typeOfLeave,
            availableBalance: existingQuota
              ? empLeave.numberOfDays - usedBalance
              : empLeave.numberOfDays,
            usedBalance: empLeave.usedBalance || usedBalance,
          };
        });

        const payload = {
          userId: this.$props.employeeData._id,
          allowedLeaveTypes: filteredLeavePolicies.map((el) => el.typeOfLeave),
          leaveBalance: empLeave,
        };

        await this.$axios({
          method,
          url,
          data: payload,
        });

        this.$toast.success(
          "Employee quota " +
            (existingQuota ? "updated" : "added") +
            " successfully"
        );
        await this.getQuota();
        this.showAddQuotaDialog = false;
      } catch (error) {
        console.log(error);
      }
    },
    cancelQuota() {
      this.populateLeaves();
      this.showAddQuotaDialog = false;
    },
    async getQuota() {
      try {
        const empId = this.employeeData._id;
        const response = await this.$axios.get(
          `/workforce/emp/leave/quota/${empId}`
        );
        this.selectedQuota = response.data?.employeeLeaveQuota;
        this.quotas = this.selectedQuota[0]?.leaveBalance.map((quota) => ({
          ...quota,
          status: "Available",
          planned: 0,
          entitlement: quota.availableBalance,
          used: quota.usedBalance,
          used_percentage: 0,
          unit: "days",
          color: "green",
        }));
      } catch (error) {
        console.log(error);
      }
    },
    viewLeaveDetails(quota) {
      const leaves = this.leaves.filter(
        (leave) => leave.typeOfLeaveApplied === quota.typeOfLeave
      );
      this.selectedLeaveDetails = leaves;
      this.showLeaveDetailsDialog = true;
    },
    hasLeaveDetails(quota) {
      return this.leaves.some(
        (leave) => leave.typeOfLeaveApplied === quota.typeOfLeave
      );
    },
    isPrevLeavePending(leaveType) {
      return this.leaves.some(
        (leave) =>
          leave.typeOfLeaveApplied === leaveType.typeOfLeave &&
          leave.leaveStatus === "PENDING"
      );
    },
    openQuotaDialog() {
      this.quotaPolicy = JSON.parse(
        JSON.stringify(this.orgConfigSettings.commonLeaves)
      );

      this.quotaPolicy.forEach((quota) => {
        const existingQuota = this.quotas?.find(
          (q) => q.typeOfLeave === quota.typeOfLeave
        );

        quota.checked = !!existingQuota;
        quota.numberOfDays = existingQuota
          ? existingQuota.availableBalance
          : quota.numberOfDays;
        quota.usedBalance = existingQuota ? existingQuota.usedBalance : 0;
      });
      this.showAddQuotaDialog = true;
    },
    updateDuration() {
      this.selectedDuration = this.dates.length;
    },
    formatLeave(dates) {
      return dates.map((date) => this.$dayjs(date).format("YYYY-MM-DD"));
    },
    formatNames(names) {
      return names.map((name) => fullName(name.approvalFrom));
    },
    statusColor(status) {
      const colorInfo = taskStatusColors[status];
      return colorInfo;
    },
    openMenu() {
      if (!this.datepickerOpened) {
        this.menu = true; // Open the v-menu (which contains v-date-picker)
        this.datepickerOpened = true; // Set flag to true to indicate date picker has been opened
      }
    },
    getEventColor(date) {
      const status = this.leaveEvents[date];
      switch (status) {
        case "PENDING":
          return "orange";
        case "APPROVED":
          return "green";
        case "REJECTED":
          return "red";
        case "WITHDRAW":
          return "grey";
        default:
          return "";
      }
    },
  },
  async mounted() {
    await this.populateLeaves();
    await this.getQuota();
    await this.getLeaves();
    await this.fetchApproval();
  },
};
</script>

<style scoped>
.font-semibold {
  font-weight: 600;
}

.v-menu__content {
  min-width: auto !important;
}
</style>
