<template>
  <div v-if="task" class="bg-gray-100 dark-bg-custom">
    <div class="mx-4 py-2 flex justify-between">
      <div>
        <p class="text-md font-semibold my-0 text-green-500">
          {{ task?.isOtpVerified ? 'Otp successfully verified! ✅' : '' }}
        </p>
        <p class="text-md font-semibold my-0" v-if="isLoanManagement">
          <span>Appointment - </span>
          <span class="text-sm">
            {{ $dayjs(task.taskStartDateTime).format('DD/MM/YYYY hh:mm A') }}
          </span>
        </p>
        <p class="text-md font-semibold my-0" v-if="isLoanManagement">
          <span>Status - </span>
          <v-chip
            small
            label
            :text-color="statusColor(task.taskStatus).textColor"
            :color="statusColor(task.taskStatus).bgColor"
            >{{ task.taskStatus }}</v-chip
          >
        </p>
      </div>
      <div v-if="!selectedCase && !callTab" class="flex justify-end align-center">
        <!-- <WhatsAppShare :defaultMessage="prepareShareData()" class="mr-2" /> -->
        <v-icon
          size="20"
          color="primary"
          @click="openEditTask"
          v-if="isVisitNotCompleted"
          v-tooltip="{
            text: 'Edit'
          }"
          v-show="canEdit"
          class="mr-2"
          >mdi-pencil</v-icon
        >
        <v-icon
          size="20"
          color="red"
          @click="deleteTask"
          v-if="isVisitNotCompleted"
          v-tooltip="{
            text: 'Delete'
          }"
          v-show="canDelete"
          >mdi-delete</v-icon
        >
      </div>
    </div>
    <v-divider></v-divider>

    <div class="text-sm my-3 px-4 py-4 dark-bg-custom" v-if="selectedCase">
      <v-row>
        <v-col class="px-4 py-1" cols="2"
          ><strong>{{ configLang.caseNo?.displayName || 'Case No' }}</strong></v-col
        >
        <v-col class="px-4 py-1">{{ selectedCase.caseNo }}</v-col>
      </v-row>
      <v-row v-if="selectedCase.customer?.customerFullName">
        <v-col class="px-4 py-1" cols="2"
          ><strong>{{ tableMapping.customerTable?.displayName || 'Customer' }} Name</strong></v-col
        >
        <v-col class="px-4 py-1">
          {{ selectedCase.customer?.customerFullName }}
        </v-col>
      </v-row>
      <v-row v-if="selectedCase.customer?.customerAddress?.length">
        <v-col class="px-4 py-1" cols="2"><strong>Customer Address</strong></v-col>
        <v-col class="px-4 py-1">
          {{ customerAddress(selectedCase.customer?.customerAddress[0]) || '-' }}
        </v-col>
      </v-row>
      <v-row v-for="(column, i) in additionalColums" :key="i">
        <v-col class="px-4 py-1" cols="2" v-show="column.value"
          ><strong>{{ convertToTitle(column?.key) || '' }}</strong></v-col
        >
        <v-col class="px-4 py-1" v-show="column.value">{{ column.value }}</v-col>
      </v-row>
      <v-row v-if="selectedCase?.productInfo">
        <v-col class="px-4 py-2" cols="2"
          ><strong>{{ configLang.productInfo?.displayName }}</strong></v-col
        >
        <v-col class="px-4 py-2">
          {{ selectedCase?.productInfo }}
        </v-col>
      </v-row>
    </div>
    <div class="flex justify-between bg-gray-100 dark-bg-custom p-4">
      <div class="w-full p-4 bg-white dark-bg-custom rounded-md">
        <v-expansion-panels v-model="panel" flat multiple>
          <v-expansion-panel>
            <v-expansion-panel-header class="px-2">
              <h3 class="text-md font-semibold">
                {{ tableMapping.visitTable?.displayName || 'Visit' }} Details
              </h3>
            </v-expansion-panel-header>

            <v-expansion-panel-content class="text-[#464A53] dark-text-color">
              <div class="overflow-x-auto">
                <table class="w-full border-collapse rounded-lg">
                  <tr>
                    <td class="px-4 py-2">
                      <strong>{{ configLang.taskTitle?.displayName }}</strong>
                    </td>
                    <td class="px-4 py-2">{{ task?.taskTitle }}</td>
                  </tr>
                  <tr v-if="task?.taskDescription">
                    <td class="px-4 py-2">
                      <strong>{{ configLang.taskDescription?.displayName }}</strong>
                    </td>
                    <td class="px-4 py-2">
                      {{ task?.taskDescription }}
                    </td>
                  </tr>
                  <tr v-if="task.additionalInfo?.userUploadedImage">
                    <td class="px-4 py-2">
                      <strong>Customer Uploaded Image</strong>
                    </td>
                    <td class="px-4 py-2">
                      <img
                        :src="task.additionalInfo?.userUploadedImage"
                        alt="Customer Uploaded Image"
                        class="w-52 h-56 object-cover rounded-md"
                      />
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-2">
                      <strong>{{ tableMapping.visitTable?.displayName || 'Visit' }} Status</strong>
                    </td>
                    <td class="px-4 py-2">{{ task.taskStatus }}</td>
                  </tr>
                  <tr v-if="task.actualStartDateTime">
                    <td class="px-4 py-2"><strong>Start Date/Time</strong></td>
                    <td class="px-4 py-2">
                      {{
                        task.actualStartDateTime
                          ? $dayjs(task.actualStartDateTime).format('DD/MM/YYYY hh:mm A')
                          : '-'
                      }}
                    </td>
                  </tr>
                  <tr v-if="task.additionalInfo?.taskStartData?.address">
                    <td class="px-4 py-2"><strong>Start Location</strong></td>
                    <td class="px-4 py-2">
                      {{ task.additionalInfo?.taskStartData?.address || '-' }}
                    </td>
                  </tr>
                  <tr v-if="task.additionalInfo?.taskStartData?.coords?.latitude">
                    <td class="px-4 py-2">
                      <strong>Start Latitude/ Longitude</strong>
                    </td>
                    <td class="px-4 py-2">
                      {{ task.additionalInfo?.taskStartData?.coords?.latitude || '-' }}
                      /
                      {{ task.additionalInfo?.taskStartData?.coords?.longitude || '-' }}
                    </td>
                  </tr>
                  <tr v-if="task?.assignedTo?.firstName">
                    <td class="px-4 py-2">
                      <strong>{{ configLang.assignedTo?.displayName }}</strong>
                    </td>
                    <td class="px-4 py-2">
                      {{ employeeFullName(task.assignedTo) }}
                    </td>
                  </tr>
                  <tr v-if="task.meetingDetails?.meetingNotes">
                    <td class="px-4 py-2"><strong>Meeting Notes</strong></td>
                    <td class="px-4 py-2">
                      {{ task.meetingDetails?.meetingNotes }}
                    </td>
                  </tr>
                  <tr v-if="task.meetingDetails?.meetingOutCome">
                    <td class="px-4 py-2"><strong>Meeting Outcome</strong></td>
                    <td class="px-4 py-2">
                      {{ task.meetingDetails?.meetingOutCome }}
                    </td>
                  </tr>
                  <tr v-if="task.meetingDetails?.fundCollected">
                    <td class="px-4 py-2">
                      <strong>{{ task.meetingDetails?.meetingOutCome || '' }} Amount</strong>
                    </td>
                    <td class="px-4 py-2">
                      {{ task.meetingDetails?.fundCollected }}
                    </td>
                  </tr>
                  <tr v-if="task.meetingDetails?.nextTaskScheduledOn">
                    <td class="px-4 py-2">
                      <strong
                        >Next {{ tableMapping.visitTable?.displayName || 'Visit' }} Date</strong
                      >
                    </td>
                    <td class="px-4 py-2">
                      {{
                        task.meetingDetails?.nextTaskScheduledOn
                          ? $dayjs(task.meetingDetails?.nextTaskScheduledOn).format(
                              'DD/MM/YYYY hh:mm A'
                            )
                          : ''
                      }}
                    </td>
                  </tr>
                  <tr v-for="(question, index) in filteredQuestions" :key="index">
                    <td class="px-4 py-2">
                      <strong>{{ question.question }}</strong>
                    </td>
                    <td class="px-4 py-2">
                      <template v-if="isAttachmentHaveLink(question.questionType)">
                        <div class="flex flex-wrap gap-2">
                          <div
                            v-for="(response, i) in question.userResponse"
                            :key="i"
                            class="response-media"
                          >
                            <template v-if="question.questionType === 'AUDIO_CAPTURE'">
                              <audio controls class="mr-2">
                                <source :src="mergeLinks(response)" type="audio/mpeg" />
                                Your browser does not support the audio element.
                              </audio>
                            </template>
                            <template v-else-if="question.questionType === 'VIDEO_CAPTURE'">
                              <video controls class="mr-2" max-width="200" max-height="200">
                                <source :src="mergeLinks(response)" type="video/webm" />
                                Your browser does not support the video element.
                              </video>
                            </template>
                            <template v-else>
                              <v-img
                                :src="mergeLinks(response)"
                                alt="Attachment Image"
                                max-width="50"
                                min-height="50"
                                class="cursor-pointer border border-blue-300"
                                @click="openLink(response)"
                              ></v-img>
                            </template>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        {{
                          Array.isArray(question.userResponse)
                            ? question.userResponse.join(', ')
                            : question.userResponse
                        }}
                      </template>
                    </td>
                  </tr>
                </table>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>

          <v-expansion-panel v-if="isCustomerFeedbackAvailable">
            <v-expansion-panel-header class="px-2">
              <h3 class="text-md font-semibold">Customer Feedback</h3>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="text-[#464A53] dark-text-color">
              <div class="overflow-x-auto">
                <table class="w-full border-collapse rounded-lg">
                  <tr
                    v-for="(question, index) in task.meetingDetails?.customerQuestionnaire"
                    :key="index"
                  >
                    <td class="px-4 py-2">
                      <strong>{{ question.question }}</strong>
                    </td>
                    <td class="px-4 py-2">
                      <template v-if="isAttachmentHaveLink(question.questionType)">
                        <div class="flex flex-wrap gap-2">
                          <div
                            v-for="(response, i) in question.userResponse"
                            :key="i"
                            class="response-media"
                          >
                            <template v-if="question.questionType === 'AUDIO_CAPTURE'">
                              <audio controls class="mr-2">
                                <source :src="mergeLinks(response)" type="audio/mpeg" />
                                Your browser does not support the audio element.
                              </audio>
                            </template>
                            <template v-else-if="question.questionType === 'VIDEO_CAPTURE'">
                              <video controls class="mr-2" max-width="200" max-height="200">
                                <source :src="mergeLinks(response)" type="video/webm" />
                                Your browser does not support the video element.
                              </video>
                            </template>
                            <template v-else>
                              <v-img
                                :src="mergeLinks(response)"
                                alt="Attachment Image"
                                max-width="50"
                                min-height="50"
                                class="cursor-pointer border border-blue-300"
                                @click="openLink(response)"
                              ></v-img>
                            </template>
                          </div>
                        </div>
                      </template>
                      <template v-else>
                        {{
                          Array.isArray(question.userResponse)
                            ? question.userResponse.join(', ')
                            : question.userResponse
                        }}
                      </template>
                    </td>
                  </tr>
                </table>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>

      <div class="w-1/3 pl-4 bg-gray-100 dark-bg-custom" v-if="isImageAudioAvailable">
        <div class="mb-5 bg-gray-100" v-if="getImageUrl">
          <h3 class="text-md flex justify-center bg-gray-100 dark-bg-custom font-semibold">
            Selfie/Live Image
          </h3>
        </div>
        <div class="flex justify-center" v-if="getImageUrl">
          <img
            :src="getImageUrl"
            alt="Task Image"
            class="w-52 h-56 object-cover rounded-md print-image"
          />
        </div>
        <div class="mt-10 hide-on-print" v-if="getAudioUrl">
          <h3 class="text-sm mt-5 font-semibold flex justify-center">Meeting Recording</h3>
          <div v-if="getAudioUrl">
            <audio controls class="mt-5 mx-auto">
              <source :src="getAudioUrl" type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
          <div v-else class="mt-2">
            <p
              class="text-center font-normal rounded-lg bg-gray-200 text-gray-600 dark-text-color p-1"
            >
              Audio recording not available.
            </p>
          </div>
        </div>
        <div v-if="imagesData?.length">
          <h3 class="text-md mt-5 font-semibold flex justify-center">Attached Documents</h3>
          <div class="mt-4">
            <LightBox :imagesData="imagesData" />
          </div>
        </div>
      </div>
    </div>
    <v-dialog v-model="openEditTaskDialog" max-width="600">
      <AddEditCaseTask
        :caseConfig="caseConfig"
        :editTask="task"
        @caseTaskAdded="handleTaskAdded"
        @updateTask="handleUpdateTask"
      />
    </v-dialog>
    <AlertPopUp
      :showConfirmationDialog="showConfirmationDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="`Are you sure you want to delete the ${
        tableMapping.visitTable?.displayName || 'Visit'
      }?`"
      :confirmationActionText="'DELETE'"
      :confirmationActionColor="'red'"
      :performAction="performDelete"
      :cancel="cancel"
    />
  </div>
</template>
<script>
import LightBox from '@/components/LightBox.vue'
import { convertToTitleCase, fullName, getFullAddress, hasPermission } from '@/utils/common'
import { taskStatusColors } from '@/utils/workforce/statusColors'
import AddEditCaseTask from './AddEditCaseTask'
// import WhatsAppShare from '@/components/WhatsAppShare'
import AlertPopUp from '@/components/AlertPopUp'
import permissionData from '@/utils/permissions'

export default {
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      panel: [0, 1],
      noImage: require('@/assets/img/notfound.jpg'),
      openEditTaskDialog: false,
      showConfirmationDialog: false,
      whatsappMessage: ''
    }
  },
  components: {
    LightBox,
    AddEditCaseTask,
    AlertPopUp
    // WhatsAppShare
  },
  props: {
    task: Object,
    caseConfig: Object,
    selectedCase: Object,
    type: String,
    callTab: Boolean
  },
  watch: {
    task: {
      handler(newval) {
        this.task = newval
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.collectionEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.collectionDelete)
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.collectionWrite)
    },
    isLoanManagement() {
      return this.$route?.params?.teams === 'collection-management'
    },
    additionalColums() {
      if (this.selectedCase?.additionalColumns) {
        const newColumns = Object.entries(this.selectedCase?.additionalColumns)?.map(
          ([key, obj]) => {
            return {
              key,
              value: obj
            }
          }
        )
        return newColumns
      }
      return []
    },
    isVisitNotCompleted() {
      return this.task?.taskStatus !== 'COMPLETED'
    },
    getImageUrl() {
      const baseUrl = process.env.VUE_APP_S3_BASE_URL
      const selfie = this.task.additionalInfo?.selfie

      if (selfie && selfie.doc_physical_path) {
        const docPhysicalPath = selfie.doc_physical_path
        return baseUrl + docPhysicalPath
      }
      return null
    },
    getAudioUrl() {
      const baseUrl = process.env.VUE_APP_S3_BASE_URL
      const audio = this.task.additionalInfo?.audio

      if (audio && audio.doc_physical_path) {
        const docPhysicalPath = audio.doc_physical_path
        return baseUrl + docPhysicalPath
      } else {
        return ''
      }
    },
    imagesData() {
      const baseUrl = process.env.VUE_APP_S3_BASE_URL
      const attachments = this.task.meetingDetails?.attachments?.map((el) => ({
        src: baseUrl + el.filePath,
        crossorigin: 'anonymous'
      }))
      return attachments
    },
    configLang() {
      return this.$props.caseConfig?.fieldMappings
    },
    isImageAudioAvailable() {
      return this.getImageUrl || this.getAudioUrl || this.imagesData?.length
    },
    isCustomerFeedbackAvailable() {
      return (
        this.task.meetingDetails &&
        this.task.meetingDetails?.customerQuestionnaire &&
        this.task.meetingDetails?.customerQuestionnaire?.length
      )
    },
    filteredQuestions() {
      return this.task.meetingDetails?.questionnaire?.filter(
        (question) => question.userResponse && question.userResponse?.length
      )
    },
    tableMapping() {
      return this.$props.caseConfig?.tableTabMapping || {}
    }
  },
  methods: {
    openGoogleMaps(address) {
      const encodedAddress = encodeURIComponent(address)
      const googleMapsURL = encodedAddress
        ? `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`
        : ''
      return googleMapsURL
    },
    prepareShareData() {
      const userName = this.employeeFullName(this.task?.assignedTo)
      let message = `${userName}'s visit update. CUSTOMER: ${this.task?.case.customerName} , CASE: ${this.task?.case.caseNo},`
      if (this.task.additionalInfo?.taskStartData?.address) {
        message += `LOCATION: ${this.openGoogleMaps(
          this.task.additionalInfo?.taskStartData?.address
        )} AT ${this.$dayjs(this.task.actualStartDateTime).format('DD/MM/YYYY hh:mm A')}.`
      }
      if (this.task.meetingDetails?.meetingNotes) {
        message += `MEETING NOTES: ${this.task.meetingDetails?.meetingNotes}.`
      }

      if (this.task?.case?.caseStatus) {
        message += ` and outcome of visit is ${this.task?.case?.caseStatus}.`
      }
      if (this.getImageUrl) {
        message += ` Captured image url: ${this.getImageUrl}.`
      }
      return message
    },
    convertToTitle(str) {
      return convertToTitleCase(str)
    },
    handleTaskAdded() {
      this.openEditTaskDialog = false
    },
    handleUpdateTask(item) {
      this.task = item
    },
    employeeFullName(item) {
      return fullName(item)
    },
    customerAddress(item) {
      return getFullAddress(item)
    },
    statusColor(status) {
      const colorInfo = taskStatusColors[status]
      return colorInfo
    },
    openEditTask() {
      this.openEditTaskDialog = true
    },
    deleteTask() {
      this.showConfirmationDialog = true
    },
    performDelete() {
      try {
        this.$emit('deleteTask', this.task)
        this.cancel()
      } catch (error) {
        console.log(error)
      }
    },
    cancel() {
      this.showConfirmationDialog = false
    },
    isAttachmentHaveLink(questionType) {
      const attachmentTypes = [
        'LOCAL_ATTACHMENTS',
        'CLICK_PICTURES',
        'AUDIO_CAPTURE',
        'VIDEO_CAPTURE'
      ]
      return attachmentTypes.includes(questionType)
    },
    mergeLinks(link) {
      const url = process.env.VUE_APP_S3_BASE_URL + link
      return url
    },
    openLink(link) {
      const url = process.env.VUE_APP_S3_BASE_URL + link
      window.open(url, '_blank')
    }
  }
}
</script>
<style scoped>
table tr td {
  border: 1px solid rgb(190, 186, 186);
  font-size: 14px;
  width: 50%;
}
</style>
