<template>
  <div>
    <!-- Loading indicator -->
    <div v-if="loadingCustomers" class="mx-6 mb-4">
      <v-alert type="info" dense text class="mb-0">
        <v-progress-circular indeterminate size="16" class="mr-2"></v-progress-circular>
        Loading customer details...
      </v-alert>
    </div>

    <!-- Primary Case Details -->
    <div class="text-sm my-3 mx-6 p-6 bg-white">
      <v-row v-for="(row, rowIndex) in organizedData" :key="rowIndex">
        <v-col
          v-for="(item, colIndex) in row"
          :key="colIndex"
          class="px-4 py-1 column-container"
          cols="4"
        >
          <template v-if="item">
            <div class="field-container">
              <strong class="text-base">{{ item.label }} :</strong>
              <div
                class="text-base ml-2"
                :class="{ 'cursor-pointer hover:text-blue-700 dark-hover-text': item?.clickEvent }"
                @click="item?.clickEvent ? routeToCustomer(item) : null"
              >
                {{ item.value }}
              </div>
            </div>
          </template>
        </v-col>
      </v-row>
    </div>

    <!-- Co-Applicant Details Sections -->
    <div v-if="isLoanCase && borrowerTabs.length > 1" class="mx-6">
      <div v-for="(borrower, index) in borrowerTabs" :key="borrower.id" class="mb-4">
        <div class="bg-blue-50 p-3 rounded-t-md border-l-4 border-blue-500">
          <h5 class="text-base font-semibold text-blue-800 flex items-center">
            <v-icon class="mr-2" color="blue">
              {{ index === 0 ? 'mdi-account-star' : 'mdi-account-plus' }}
            </v-icon>
            {{ borrower.name }}
          </h5>
        </div>

        <div class="text-sm p-6 bg-white rounded-b-md border border-t-0">
          <v-row v-for="(row, rowIndex) in getBorrowerOrganizedData(borrower)" :key="rowIndex">
            <v-col
              v-for="(item, colIndex) in row"
              :key="colIndex"
              class="px-4 py-1 column-container"
              cols="4"
            >
              <template v-if="item">
                <div class="field-container">
                  <strong class="text-base">{{ item.label }} :</strong>
                  <div
                    class="text-base ml-2 cursor-pointer hover:text-blue-700 dark-hover-text"
                    @click="routeToCustomer(item)"
                  >
                    {{ item.value }}
                  </div>
                </div>
              </template>
            </v-col>
          </v-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getFullAddress } from '@/utils/common'
export default {
  name: 'OptimizedCaseDetails',
  props: {
    selectedCase: {
      type: Object,
      required: true
    },
    configLang: {
      type: Object,
      default: () => ({})
    },
    caseConfig: {
      type: Object,
      default: () => ({})
    },
    additionalColums: {
      type: Array,
      default: () => []
    },
    isLoanCase: {
      type: Boolean,
      default: false
    },
    borrowerInfo: {
      type: Object,
      default: null
    },
    borrowerTabs: {
      type: Array,
      default: () => []
    },
    customerDetails: {
      type: Object,
      default: () => ({})
    },
    loadingCustomers: {
      type: Boolean,
      default: false
    }
  },

  mounted() {
    this.refreshBorrowerData()
  },

  watch: {
    selectedCase: {
      handler() {
        this.refreshBorrowerData()
      },
      deep: true
    },
    isLoanCase() {
      if (this.isLoanCase) {
        this.refreshBorrowerData()
      }
    }
  },

  computed: {
    organizedData() {
      const allFields = this.getAllFields
      return this.organizeIntoRows(allFields)
    },
    getAllFields() {
      const fields = []

      if (this.selectedCase?.caseNo) {
        fields.push({
          label: this.configLang.caseNo?.displayName || 'Case No',
          value: this.selectedCase.caseNo
        })
      }

      if (this.selectedCase?.userDefinedCaseStatus) {
        fields.push({
          label: this.configLang.userDefinedCaseStatus?.displayName || 'Outcome',
          value: this.selectedCase.userDefinedCaseStatus
        })
      }

      // Primary Customer information (only show in main details, not in borrower sections)
      if (this.selectedCase.customer?.customerFullName) {
        const customerLabel =
          this.caseConfig.tableTabMapping?.customerTable?.displayName || 'Primary Customer'
        fields.push({
          label: `${customerLabel} Name`,
          value: this.selectedCase.customer.customerFullName,
          clickEvent: true
        })
      }

      if (this.selectedCase.customer?.customerAddress?.length) {
        const customerLabel =
          this.caseConfig.tableTabMapping?.customerTable?.displayName || 'Primary Customer'
        fields.push({
          label: `${customerLabel} Address`,
          value: this.customerAddress(this.selectedCase.customer.customerAddress[0]) || '-'
        })
      }

      if (this.isLoanCase) {
        const loanFields = this.getLoanFields()
        fields.push(...loanFields)

        // Add co-applicant count summary
        if (this.selectedCase?.loanDetails?.coApplicants?.length) {
          fields.push({
            label: 'Total Co-Applicants',
            value: this.selectedCase.loanDetails.coApplicants.length
          })
        }

        // Add guarantor information in organized data
        this.addGuarantorFields(fields)
      }

      // Case dates
      if (this.selectedCase.caseStartDate) {
        const caseLabel = this.caseConfig.tableTabMapping?.caseTable?.displayName || 'Case'
        fields.push({
          label: `${caseLabel} Start Date`,
          value: this.$dayjs(this.selectedCase.caseStartDate).format('DD/MM/YYYY hh:mm A')
        })
      }

      if (this.selectedCase.caseEndDate) {
        const caseLabel = this.caseConfig.tableTabMapping?.caseTable?.displayName || 'Case'
        fields.push({
          label: `${caseLabel} End Date`,
          value: this.$dayjs(this.selectedCase.caseEndDate).format('DD/MM/YYYY hh:mm A')
        })
      }

      const additionalFields = this.getAdditionalFields()
      fields.push(...additionalFields)

      if (this.selectedCase?.productInfo) {
        fields.push({
          label: this.configLang.productInfo?.displayName || 'Product Info',
          value: this.selectedCase.productInfo
        })
      }
      return fields
    }
  },

  methods: {
    getLoanFields() {
      const loanFields = []
      const loanDetails = this.selectedCase.loanDetails

      if (!loanDetails) return loanFields

      const loanFieldsConfig = [
        { key: 'typeOfLoan', label: this.configLang.typeOfLoan?.displayName || 'Type of Loan' },
        {
          key: 'loanAmountRequested',
          label: this.configLang.loanAmountRequested?.displayName || 'Loan Amount',
          format: 'currency'
        },
        {
          key: 'loanAmountSanctioned',
          label: this.configLang.loanAmountSanctioned?.displayName || 'Sanctioned Amount',
          format: 'currency'
        },
        {
          key: 'tenureMonths',
          label: this.configLang.tenure?.displayName || 'Tenure',
          suffix: ' months'
        },
        {
          key: 'annualProcessingRate',
          label: this.configLang.annualProcessingRate?.displayName || 'Annual Processing Fee',
          format: 'interest'
        },
        {
          key: 'interestRate',
          label: this.configLang.interestRate?.displayName || 'Interest Rate',
          format: 'interest'
        },
        {
          key: 'processingFee',
          label: this.configLang.processingFee?.displayName || 'Processing Fee',
          format: 'processingFee'
        },
        {
          key: 'currentStatus',
          label: this.configLang.currentStatus?.displayName || 'Current Status'
        },
        { key: 'kycStatus', label: this.configLang.kycStatus?.displayName || 'KYC Status' }
      ]

      loanFieldsConfig.forEach((config) => {
        if (loanDetails[config.key]) {
          let value = loanDetails[config.key]

          if (config.format === 'currency') {
            value = `₹ ${this.formatCurrency(value)}`
          } else if (config.format === 'interest') {
            value =
              config.key === 'annualProcessingRate'
                ? `${value.toFixed(2)}%`
                : `${value.toFixed(2)}% (${loanDetails.typeOfInterest || ''})`
          } else if (config.format === 'processingFee') {
            const totalShare = Array.isArray(value)
              ? value.reduce((sum, item) => sum + (item.share || 0), 0)
              : 0
            value = `${totalShare}%`
          } else if (config.suffix) {
            value = `${value}${config.suffix}`
          }

          loanFields.push({
            label: config.label,
            value: value
          })
        }
      })

      return loanFields
    },
    getAdditionalFields() {
      const additionalFields = []

      // Add dynamic fields from additionalColumns
      if (this.additionalColums && this.additionalColums.length) {
        this.additionalColums.forEach((column) => {
          if (column.value) {
            additionalFields.push({
              label: this.configLang[column.key]?.displayName || this.convertToTitle(column.key),
              value: column.value
            })
          }
        })
      }

      return additionalFields
    },

    organizeIntoRows(fields) {
      const rows = []
      const columnsPerRow = 3
      const rowsNeeded = Math.ceil(fields.length / columnsPerRow)

      for (let i = 0; i < rowsNeeded; i++) {
        rows.push(Array(columnsPerRow).fill(null))
      }

      for (let col = 0; col < columnsPerRow; col++) {
        for (let row = 0; row < rowsNeeded; row++) {
          const fieldIndex = col * rowsNeeded + row
          if (fieldIndex < fields.length) {
            rows[row][col] = fields[fieldIndex]
          }
        }
      }

      return rows
    },

    customerAddress(address) {
      return getFullAddress(address)
    },

    formatCurrency(amount) {
      return `${amount?.toLocaleString('en-IN')}`
    },
    convertToTitle(text) {
      return text
    },

    getBorrowerOrganizedData(borrower) {
      const fields = this.getBorrowerFields(borrower)
      return this.organizeIntoRows(fields)
    },
    getBorrowerFields(borrower) {
      const fields = []
      const customerData = borrower.customerData

      if (customerData?.customerFullName) {
        fields.push({
          label: 'Full Name',
          value: customerData.customerFullName
        })
      }

      if (borrower.share !== undefined && borrower.share > 0) {
        fields.push({
          label: 'Share Percentage',
          value: `${borrower.share}%`
        })
      }

      if (borrower.emiAmount > 0) {
        fields.push({
          label: 'EMI Amount',
          value: `₹${this.formatCurrency(borrower.emiAmount)}`
        })
      }

      return fields
    },
    addGuarantorFields(fields) {
      if (!this.selectedCase?.loanDetails?.guarantors?.length) return

      this.selectedCase.loanDetails.guarantors.forEach((guarantor, index) => {
        console.log(guarantor)
        const guarantorData = this.customerDetails[guarantor.customer] || {}
        const name = guarantorData.customerFullName || 'Unknown Guarantor'

        fields.push({
          label: `Guarantor ${index + 1}`,
          value: name
        })
      })
    },
    refreshBorrowerData() {
      // Emit event to parent component to refresh borrower data
      this.$emit('refresh-borrowers', this.selectedCase)
    },

    routeToCustomer(item) {
      const customerName = item.value || ''
      if (customerName) {
        const url = this.$router.resolve(`/workforce/customers?search=${customerName}`).href
        window.open(url, '_blank')
      }
    }
  }
}
</script>

<style scoped>
.field-container {
  margin-bottom: 8px;
  display: flex;
}

.column-container {
  position: relative;
}

.column-container:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 1px;
  background-color: #bab7b7;
}
</style>
