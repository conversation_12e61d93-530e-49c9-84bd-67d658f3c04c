<template>
  <v-dialog v-model="dialog" max-width="900" persistent>
    <v-card class="flex flex-col max-h-screen overflow-hidden">
      <!-- Fixed Header -->
      <div class="sticky top-0 z-50 bg-gray-200 border-gray-300">
        <div class="flex justify-between items-start p-4">
          <div class="flex justify-between align-center">
            <h4 class="text-xl mb-0 font-weight-bold flex items-center">
              <v-icon left color="primary">mdi-pencil-circle</v-icon>
              Edit EMI Details
            </h4>
            <div v-if="overdueInfo.isOverdue || overdueInfo.isLatePaid">
              <v-chip outlined :color="overdueInfo.isLatePaid ? 'warning' : 'error'">
                <v-icon left small>
                  {{ overdueInfo.isLatePaid ? 'mdi-clock-alert' : 'mdi-alert-circle' }}
                </v-icon>
                {{ overdueInfo.isLatePaid ? overdueInfo.latePaidText : overdueInfo.overdueText }}
              </v-chip>
            </div>
          </div>
          <v-icon @click="closeDialog" class="cursor-pointer hover:bg-gray-300 rounded p-1">
            mdi-close
          </v-icon>
        </div>
      </div>

      <!-- Scrollable Content -->
      <v-card-text class="flex-1 overflow-y-auto px-4 py-4">
        <v-form ref="emiForm" v-model="isFormValid">
          <v-row>
            <!-- EMI Header Information -->

            <!-- Late Fee Information -->
            <v-col
              cols="12"
              v-if="
                editedEmi.lateFeeType &&
                editedEmi.lateFeeValue &&
                editedEmi.emiStatus !== 'Minimum Amount Paid'
              "
            >
              <v-alert dense outlined type="warning" class="mb-0">
                <div class="text-sm">
                  <strong
                    >Late Fee of {{ formatAmount(editedEmi.lateFeeValue) }}% will be charged for
                    late payments</strong
                  >
                  <!-- {{
                    editedEmi.lateFeeType === 'Fixed'
                      ? `Fixed amount of ₹${formatAmount(
                          editedEmi.lateFeeValue
                        )} will be charged for late payments.`
                      : `${editedEmi.lateFeeValue}% of EMI amount (₹${formatAmount(
                          editedEmi.emiAmount
                        )}) will be charged for late payments.`
                  }} -->

                  <span v-if="overdueInfo.days > 0" class="text-red-600">
                    - ₹{{ formatAmount(overdueInfo.lateFeeAmount) }} paid for ({{
                      overdueInfo.days
                    }}
                    day{{ overdueInfo.days > 1 ? 's' : '' }}
                    {{ overdueInfo.isOverdue ? 'overdue' : 'late paid' }})
                  </span>
                </div>
              </v-alert>
            </v-col>

            <v-col cols="12" md="4">
              <v-menu
                v-model="dueDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="formattedDueDate"
                    label="Due Date"
                    prepend-inner-icon="mdi-calendar"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    disabled
                    hide-details
                    :rules="[(v) => !!v || 'Due date is required']"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="editedEmi.dueDate"
                  @input="formatDueDate"
                  no-title
                  scrollable
                ></v-date-picker>
              </v-menu>
            </v-col>

            <v-col cols="12" md="4">
              <v-select
                v-model="editedEmi.emiStatus"
                :items="emiStatusOptions"
                label="EMI Status"
                item-text="status"
                required
                :rules="[(v) => !!v || 'EMI status is required']"
                dense
                outlined
                hide-details
                :prepend-inner-icon="getStatusIcon(editedEmi.emiStatus)"
                @change="onStatusChange"
              ></v-select>
            </v-col>
            <v-col
              cols="12"
              md="4"
              v-if="editedEmi.emiStatus === 'Paid' || editedEmi.emiStatus === 'Minimum Amount Paid'"
            >
              <v-menu
                v-model="paymentDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="formattedPaymentDate"
                    label="Payment Date"
                    prepend-inner-icon="mdi-calendar-check"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    hide-details
                    :rules="[(v) => !!v || 'Payment date is required']"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="editedEmi.paymentDate"
                  @input="formatPaymentDate"
                  no-title
                  scrollable
                  :min="editedEmi.dueDate"
                  :max="currentDate"
                ></v-date-picker>
              </v-menu>
            </v-col>
            <!-- Late Fee Configuration -->
            <v-col cols="12" md="4">
              <v-select
                v-model="editedEmi.lateFeeType"
                :items="lateFeeTypeOptions"
                label="Late Fee Type"
                item-text="text"
                item-value="value"
                dense
                outlined
                hide-details
                prepend-inner-icon="mdi-percent"
                clearable
              ></v-select>
            </v-col>

            <v-col cols="12" md="4">
              <v-text-field
                v-model="editedEmi.lateFeeValue"
                label="Late Fee"
                type="number"
                dense
                outlined
                hide-details
                :placeholder="editedEmi.lateFeeType === 'Fixed' ? 'ex: 500.00' : 'ex: 2.5'"
                prepend-inner-icon="mdi-calculator"
                :suffix="editedEmi.lateFeeType === 'Fixed' ? 'INR' : '%'"
                :disabled="!editedEmi.lateFeeType"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="4">
              <v-text-field
                v-model="editedEmi.emiAmount"
                label="EMI Amount"
                type="number"
                required
                :rules="[(v) => !!v || 'EMI amount is required']"
                dense
                outlined
                hide-details
                placeholder="ex : 50000.00"
                prepend-inner-icon="mdi-currency-inr"
                suffix="INR"
                :disabled="editedEmi.emiStatus === 'Minimum Amount Paid'"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="4">
              <v-text-field
                v-model="editedEmi.principalAmount"
                label="Principal Amount"
                type="number"
                required
                :rules="[(v) => !!v || 'Principal amount is required']"
                dense
                outlined
                hide-details
                placeholder="ex : 50000.00"
                prepend-inner-icon="mdi-currency-inr"
                suffix="INR"
                :disabled="editedEmi.emiStatus === 'Minimum Amount Paid'"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="4">
              <v-text-field
                v-model="editedEmi.interestAmount"
                label="Interest Amount"
                type="number"
                required
                :rules="[(v) => !!v || 'Interest amount is required']"
                dense
                outlined
                hide-details
                placeholder="ex : 10000.00"
                prepend-inner-icon="mdi-currency-inr"
                suffix="INR"
                :disabled="editedEmi.emiStatus === 'Minimum Amount Paid'"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="4" v-if="overdueInfo.lateFeeAmount > 0">
              <v-text-field
                :value="formatAmount(overdueInfo.lateFeeAmount)"
                label="Calculated Late Fee"
                dense
                outlined
                hide-details
                readonly
                prepend-inner-icon="mdi-currency-inr"
                suffix="INR"
                class="calculated-late-fee"
              ></v-text-field>
            </v-col>
            <!-- Minimum Amount Paid Field - Only show when status is Minimum Amount Paid -->
            <v-col cols="12" md="4" v-if="editedEmi.emiStatus === 'Minimum Amount Paid'">
              <v-text-field
                :value="formattedMinimumAmount"
                label="Minimum Amount (Interest + Late Fee)"
                type="text"
                dense
                outlined
                hide-details
                readonly
                prepend-inner-icon="mdi-currency-inr"
                suffix="INR"
                class="minimum-amount-field"
              ></v-text-field>
            </v-col>

            <!-- Minimum Amount Paid Information -->
            <v-col cols="12" v-if="editedEmi.emiStatus === 'Minimum Amount Paid'">
              <v-alert dense outlined type="warning" class="mb-0">
                <div class="text-sm">
                  <strong>Minimum Amount :</strong>
                  Customer paid minimum amount of ₹{{ formattedMinimumAmount }} (Interest: ₹{{
                    formatAmount(editedEmi.interestAmount)
                  }}
                  <span v-if="overdueInfo.lateFeeAmount > 0">
                    + Late Fee: ₹{{ formatAmount(overdueInfo.lateFeeAmount) }} </span
                  >).
                  <br />
                  <span class="text-orange-600">
                    Remining principal amount of ₹{{ formatAmount(editedEmi.principalAmount) }}
                    carry forword to the next EMI.
                  </span>
                </div>
              </v-alert>
            </v-col>

            <!-- Additional Charges Section -->
            <v-col cols="12">
              <AdditionalChargesSection :emiAmount="editedEmi.emiAmount" />
            </v-col>
            <!-- Activities Section -->
            <v-col cols="12" v-if="editedEmi.activities && editedEmi.activities.length">
              <v-expansion-panels flat>
                <v-expansion-panel elevation="0" @click="isExpansionOpen = !isExpansionOpen">
                  <v-expansion-panel-header class="px-0">
                    <h5 class="text-lg font-semibold">Required Documents</h5>
                    <template v-slot:actions>
                      <v-icon>{{ isExpansionOpen ? 'mdi-minus' : 'mdi-plus' }}</v-icon>
                    </template>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content class="expension-content">
                    <v-col cols="12" v-for="(activity, index) in editedEmi.activities" :key="index">
                      <v-card outlined elevation="0" class="mb-4 pa-4">
                        <v-row>
                          <v-col cols="12" md="6">
                            <v-text-field
                              v-model="activity.activityType"
                              label="Document Type"
                              required
                              :rules="[(v) => !!v || 'Document type is required']"
                              dense
                              outlined
                              hide-details
                              placeholder="ex : Bank Statement"
                              prepend-inner-icon="mdi-format-list-bulleted-type"
                            ></v-text-field>
                          </v-col>

                          <v-col cols="12" md="6">
                            <v-select
                              v-model="activity.status"
                              :items="activityStatusOptions"
                              item-text="status"
                              item-value="status"
                              label="Status"
                              required
                              :rules="[(v) => !!v || 'Status is required']"
                              dense
                              outlined
                              hide-details
                              :prepend-inner-icon="getStatusActivity(activity.status)"
                            ></v-select>
                          </v-col>

                          <v-col cols="12">
                            <div class="document-upload mb-2">
                              <v-file-input
                                v-model="activityFiles[index]"
                                label="Attach Document"
                                prepend-inner-icon="mdi-paperclip"
                                prepend-icon=""
                                outlined
                                dense
                                placeholder="ex : pdf"
                                accept="image/*, application/pdf"
                                :show-size="1000"
                                hide-details
                              ></v-file-input>

                              <div v-if="activity.activityDoc" class="mt-2 flex items-center">
                                <v-icon size="20" color="primary" class="mr-2"
                                  >mdi-file-document-outline</v-icon
                                >
                                <a
                                  :href="activity.activityDoc"
                                  target="_blank"
                                  class="text-blue-600 hover:underline mr-2"
                                >
                                  Current Document
                                </a>
                                <v-btn
                                  x-small
                                  icon
                                  color="error"
                                  @click="removeActivityDocument(index)"
                                >
                                  <v-icon x-small>mdi-close</v-icon>
                                </v-btn>
                              </div>
                            </div>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <!-- Fixed Footer -->
      <div class="sticky bottom-0 z-40 bg-white border-t border-gray-300 shadow-lg">
        <div class="flex items-center justify-end gap-3 px-4 py-3">
          <v-btn outlined class="min-w-24" @click="closeDialog">
            <v-icon left size="18">mdi-close</v-icon>
            Cancel
          </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text min-w-32"
            :loading="isSubmitting"
            :disabled="!isFormValid || isSubmitting"
            @click="saveEmiDetails"
          >
            <v-icon left size="18">mdi-content-save</v-icon>
            Save All Changes
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import { calculateOverdueDays } from '@/utils/common'
import AdditionalChargesSection from '../CaseModules/AdditionalChargesSection.vue'
export default {
  name: 'AddEditEmi',
  components: {
    AdditionalChargesSection
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    emiData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isFormValid: false,
      isSubmitting: false,
      isExpansionOpen: false,
      editedEmi: {
        _id: null,
        dueDate: '',
        emiAmount: 0,
        principalAmount: 0,
        interestAmount: 0,
        emiStatus: '',
        paymentDate: '',
        lateFeeType: '',
        lateFeeValue: 0,
        activities: []
      },
      activityFiles: [],
      dueDateMenu: false,
      paymentDateMenu: false,
      formattedDueDate: '',
      formattedPaymentDate: '',
      // Options
      activityStatusOptions: [
        { status: 'Completed', icon: 'mdi-check-circle' },
        { status: 'Not Completed', icon: 'mdi-close-circle' },
        { status: 'In Progress', icon: 'mdi-progress-clock' }
      ],
      emiStatusOptions: [
        { status: 'Pending', icon: 'mdi-clock-outline' },
        { status: 'Paid', icon: 'mdi-check-circle' },
        { status: 'Overdue', icon: 'mdi-alert-circle' },
        { status: 'Waived Off', icon: 'mdi-cancel' },
        { status: 'Cancelled', icon: 'mdi-close-circle' },
        { status: 'Minimum Amount Paid', icon: 'mdi-cash-minus' }
      ],
      lateFeeTypeOptions: [
        { text: 'Fixed Amount', value: 'Fixed' },
        { text: 'Percentage', value: 'Percentage' }
      ]
    }
  },
  computed: {
    dialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    },
    overdueDays() {
      return this.overdueInfo.days
    },
    overdueInfo() {
      // In edit form, always use the payment date if available to show real-time calculation
      // This allows users to see how changing the payment date affects overdue calculation
      return calculateOverdueDays(
        this.editedEmi.dueDate,
        this.editedEmi.emiStatus,
        this.editedEmi.paymentDate || null,
        this.editedEmi.emiAmount,
        this.editedEmi.lateFeeType,
        this.editedEmi.lateFeeValue
      )
    },
    minimumAmountComputed() {
      const interestAmount = parseFloat(this.editedEmi.interestAmount) || 0
      const lateFeeAmount = this.overdueInfo.lateFeeAmount || 0
      return interestAmount + lateFeeAmount
    },
    formattedMinimumAmount() {
      return this.formatAmount(this.minimumAmountComputed)
    },
    currentDate() {
      return this.$dayjs().format('YYYY-MM-DD')
    }
  },
  watch: {
    value(newVal) {
      if (newVal && this.emiData && this.emiData._id) {
        this.initializeForm()
      }
    },
    emiData: {
      handler(newData) {
        if (newData && newData._id && this.value) {
          this.initializeForm()
        }
      },
      deep: true
    }
  },
  methods: {
    initializeForm() {
      // Deep clone the EMI to avoid direct mutation
      this.editedEmi = {
        _id: this.emiData._id,
        dueDate: this.emiData.dueDate,
        emiAmount: this.emiData.emiAmount,
        principalAmount: this.emiData.principalAmount,
        interestAmount: this.emiData.interestAmount,
        emiStatus: this.emiData.emiStatus,
        paymentDate: this.emiData.paymentDate || '',
        lateFeeType: this.emiData.lateFeeType || '',
        lateFeeValue: this.emiData.lateFeeValue || 0,
        activities: JSON.parse(JSON.stringify(this.emiData.activities || []))
      }

      // If EMI is not paid/minimum amount paid and no paid date from backend, set current date
      if (
        this.editedEmi.emiStatus !== 'Paid' &&
        this.editedEmi.emiStatus !== 'Minimum Amount Paid' &&
        !this.editedEmi.paymentDate
      ) {
        this.editedEmi.paymentDate = this.$dayjs().format('YYYY-MM-DD')
      }

      // Initialize activity files array
      this.activityFiles = new Array(this.editedEmi.activities.length).fill(null)

      // Format the due date
      this.formattedDueDate = this.$dayjs(this.emiData.dueDate).format('DD/MM/YYYY')

      // Format the paid date (will always exist now)
      this.formattedPaymentDate = this.$dayjs(this.editedEmi.paymentDate).format('DD/MM/YYYY')
    },
    getStatusIcon(s) {
      const status = this.emiStatusOptions.find((el) => el.status === s)
      return status?.icon
    },
    getStatusActivity(activityStatus) {
      const status = this.activityStatusOptions.find((el) => el.status === activityStatus)
      return status?.icon
    },

    closeDialog() {
      this.dialog = false
      this.resetForm()
    },

    resetForm() {
      this.editedEmi = {
        _id: null,
        dueDate: '',
        emiAmount: 0,
        principalAmount: 0,
        interestAmount: 0,
        emiStatus: '',
        paymentDate: '',
        lateFeeType: '',
        lateFeeValue: 0,
        activities: []
      }
      this.activityFiles = []
      this.formattedDueDate = ''
      this.formattedPaymentDate = ''
      this.isFormValid = false
    },

    formatDueDate() {
      this.formattedDueDate = this.$dayjs(this.editedEmi.dueDate).format('DD/MM/YYYY')
      this.dueDateMenu = false
    },

    formatPaymentDate() {
      this.formattedPaymentDate = this.$dayjs(this.editedEmi.paymentDate).format('DD/MM/YYYY')
      this.paymentDateMenu = false
    },

    onStatusChange() {
      if (
        (this.editedEmi.emiStatus === 'Paid' ||
          this.editedEmi.emiStatus === 'Minimum Amount Paid') &&
        !this.editedEmi.paymentDate
      ) {
        // Set current date as default paid date
        this.editedEmi.paymentDate = this.$dayjs().format('YYYY-MM-DD')
        this.formatPaymentDate()
      } else if (
        this.editedEmi.emiStatus !== 'Paid' &&
        this.editedEmi.emiStatus !== 'Minimum Amount Paid'
      ) {
        // Clear paid date if status is not paid or minimum amount paid
        this.editedEmi.paymentDate = ''
        this.formattedPaymentDate = ''
      }
    },

    removeActivityDocument(index) {
      this.editedEmi.activities[index].activityDoc = ''
    },

    formatAmount(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toLocaleString('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    async saveEmiDetails() {
      if (!this.$refs.emiForm.validate()) return

      this.isSubmitting = true

      try {
        // Process each activity's document if needed
        for (let i = 0; i < this.editedEmi.activities.length; i++) {
          if (this.activityFiles[i]) {
            // Here you would implement your document upload logic
            const documentUrl = await this.uploadDocument(this.activityFiles[i])
            this.editedEmi.activities[i].activityDoc = documentUrl
          }

          // Update the timestamp
          this.editedEmi.activities[i].updatedAt = new Date().toISOString()
        }

        // Prepare the updated EMI
        const updatedEmi = {
          ...this.editedEmi,
          emiAmount: parseFloat(this.editedEmi.emiAmount),
          principalAmount: parseFloat(this.editedEmi.principalAmount),
          interestAmount: parseFloat(this.editedEmi.interestAmount),
          lateFeeValue: parseFloat(this.editedEmi.lateFeeValue) || 0,
          isPaid: this.editedEmi.emiStatus === 'Paid' ? true : false,
          paymentDate: this.editedEmi.paymentDate,
          // Include minimum amount and late fee amount for Minimum Amount Paid status
          ...(this.editedEmi.emiStatus === 'Minimum Amount Paid' && {
            minimumPaidAmount: this.minimumAmountComputed
          })
        }

        // Emit event to parent component to handle the update
        this.$emit('save-emi', {
          emiId: this.editedEmi._id,
          updatedEmi
        })

        // Close dialog
        this.closeDialog()

        // Show success message
        this.$emit('show-notification', {
          type: 'success',
          message: 'EMI details updated successfully'
        })
      } catch (error) {
        console.error('Error updating EMI details:', error)
        this.$emit('show-notification', {
          type: 'error',
          message: 'Failed to update EMI details'
        })
      } finally {
        this.isSubmitting = false
      }
    },

    // Document upload method (placeholder)
    async uploadDocument(file) {
      // This would be your actual document upload implementation
      // For example:
      // const formData = new FormData()
      // formData.append('file', file)
      // const response = await this.$axios.post('/api/upload', formData)
      // return response.data.fileUrl

      // For now, return a placeholder URL
      return 'https://example.com/document.pdf'
    }
  }
}
</script>

<style scoped>
.minimum-amount-field >>> .v-text-field__slot input {
  color: #f57c00 !important;
  font-weight: 600 !important;
}

/* Dark mode support for sticky elements */
@media (prefers-color-scheme: dark) {
  .dark\:bg-gray-800 {
    background-color: #1f2937 !important;
  }

  .dark\:border-gray-600 {
    border-color: #4b5563 !important;
  }
}

/* Ensure proper scrolling behavior */
.v-dialog .v-card {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
}

/* Custom scrollbar styling */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
.expension-content {
  padding: 0px !important;
}
</style>
