<template>
  <div class="overflow-x-auto overflow-y-auto position-relative">
    <v-data-table
      :headers="headers"
      :items="emiData"
      class=""
      :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30, 50] }"
      :loading="loadTable"
      :options.sync="options"
      :server-items-length="totalItems"
      fixed-header
      show-select
      item-key="_id"
      v-model="selected"
    >
      <template #item.applicationNumber="{ item }">
        <td class="px-4 py-6 font-semibold">{{ item.loan?.applicationNumber || '-' }}</td>
      </template>
      <template #item.customerName="{ item }">
        <td
          class="px-4 py-6 font-semibold cursor-pointer hover:text-blue-700 dark-hover-text"
          @click="routeToCustomer(item)"
        >
          {{ item.customer?.customerFullName || '-' }}
        </td>
      </template>
      <template #item.typeOfLoan="{ item }">
        <td class="px-4 py-6 font-semibold">{{ item.loan?.typeOfLoan || '-' }}</td>
      </template>
      <template #item.emiAmount="{ item }">
        <td class="px-4 py-6 font-semibold">
          ₹{{ formatAmount(item.emiAmount) }}
          <div v-if="getOverdueInfo(item).lateFeeText" class="mt-1">
            <v-chip small outlined :color="statusColor('late_fee').textColor">
              {{ getOverdueInfo(item).lateFeeText }}
            </v-chip>
          </div>
        </td>
      </template>
      <template #item.dueDate="{ item }">
        <td class="px-4 py-6 font-semibold">
          <div>{{ item.dueDate ? $dayjs(item.dueDate).format('DD/MM/YYYY') : '-' }}</div>
          <div
            v-if="getOverdueInfo(item).isOverdue || getOverdueInfo(item).isLatePaid"
            class="mt-1"
          >
            <v-chip
              small
              outlined
              :color="
                getOverdueInfo(item).isOverdue
                  ? statusColor('overdue').textColor
                  : statusColor('late_fee').textColor
              "
              class="mb-1"
            >
              {{
                getOverdueInfo(item).isLatePaid
                  ? getOverdueInfo(item).latePaidText
                  : getOverdueInfo(item).overdueText
              }}
            </v-chip>
          </div>
        </td>
      </template>
      <template #item.emiStatus="{ item }">
        <td class="px-4 py-6">
          <v-chip
            :color="statusColor(item.emiStatus).textColor"
            outlined
            small
            class="font-bold"
            v-if="item.emiStatus !== 'Minimum Amount Paid'"
          >
            {{ item.emiStatus }}
          </v-chip>
          <div class="mt-1" v-else>
            <v-chip
              small
              :color="statusColor('minimum_amount_paid')?.textColor"
              class="font-bold"
              outlined
            >
              {{ statusFormat(item.emiStatus) }} - ₹{{
                formatAmount(calculateMinimumAmountForEmi(item))
              }}
            </v-chip>
          </div>
        </td>
      </template>
      <template #item.outstandingAmount="{ item }">
        <td class="px-4 py-6 font-semibold">₹{{ formatAmount(item.outstandingAmount) }}</td>
      </template>
      <template #item.penaltyAmount="{ item }">
        <td class="px-4 py-6 font-semibold">
          <span :class="item.penaltyAmount > 0 ? 'text-red-600' : ''">
            ₹{{ formatAmount(item.penaltyAmount) }}
          </span>
        </td>
      </template>
      <template #item.lateFee="{ item }">
        <td class="px-4 py-6 font-semibold">
          <!-- <span :class="getOverdueInfo(item).lateFeeAmount > 0 ? 'text-red-600' : ''">
            ₹{{ formatAmount(getOverdueInfo(item).lateFeeAmount) }}
          </span> -->
          <div v-if="item.lateFeeType && item.lateFeeValue" class="text-xs text-gray-500 mt-1 pl-2">
            {{ item.lateFeeType === 'Fixed' ? 'Fixed' : `${item.lateFeeValue}%` }}
          </div>
        </td>
      </template>
      <template #item.paymentMethod="{ item }">
        <td class="px-4 py-6 font-semibold">{{ item.paymentMethod || '-' }}</td>
      </template>
      <template #item.activitiesCount="{ item }">
        <td class="px-4 py-6 font-semibold">
          <v-chip
            small
            outlined
            v-tooltip="{
              text: `${getActivitiesTooltip(item)}`,
              width: '450px'
            }"
          >
            {{ item.activities?.length || 0 }} Sanctions
          </v-chip>
          <v-chip small outlined :color="getPDDStatus(item.activities).color" class="mt-2">
            {{ getPDDStatus(item.activities).status }}
          </v-chip>
        </td>
      </template>
      <template #item.action="{ item }">
        <td class="px-4 py-6" @click.stop>
          <action-button
            :item="item"
            :canEdit="canEdit"
            :handleEdit="() => editEmi(item)"
            :showEditButton="canEdit"
            :showDeleteButton="false"
          />
        </td>
      </template>
    </v-data-table>
    <!-- Edit EMI Dialog -->
    <AddEditEmi
      v-model="editEmiDialog"
      :emi-data="selectedEmi"
      @save-emi="handleEmiUpdate"
      @show-notification="$emit('show-notification', $event)"
    />

    <!-- Bulk Upload Dialog -->
    <v-dialog v-model="bulkUploadDialog" max-width="1000px" persistent>
      <GenericBulkUpload
        title="Bulk Update EMI Status"
        subtitle="Update multiple EMI statuses using Excel file"
        entity-name="EMI"
        :method="'put'"
        :required-columns="['EMI Status', 'Emi Id']"
        :upload-endpoint="bulkUploadEndpoint"
        :additional-payload="getBulkUploadPayload()"
        @close="closeBulkUploadDialog"
        @uploadComplete="handleBulkUploadComplete"
      />
    </v-dialog>

    <!-- Floating Selection Actions Bar -->
    <FloatingActionBar
      v-if="selected.length > 0"
      :selected="selected"
      :bulk-updating="bulkUpdating"
      :status-options="emiStatusOptions"
      :show-delete-selected="false"
      :show-delete-all="false"
      :show-update-status="true"
      :show-clear="true"
      @bulk-status-update="handleBulkStatusUpdate"
      @clear-selection="selected = []"
    />
  </div>
</template>

<script>
import ActionButton from '@/components/ActionButton'
import { fullName, calculateOverdueDays, getEmiStatusColor } from '@/utils/common'
import AddEditEmi from './AddEditEmi.vue'
import GenericBulkUpload from '../GenericBulkUpload.vue'
import { chipStatusColors } from '@/utils/workforce/statusColors'
import FloatingActionBar from '../CaseModules/FloatingActionBar.vue'

export default {
  name: 'EmiTable',
  components: {
    ActionButton,
    AddEditEmi,
    GenericBulkUpload,
    FloatingActionBar
  },
  props: {
    caseConfig: Object,
    canEdit: Boolean,
    canDelete: Boolean,
    filterParams: Object // Pass filter params from parent
  },
  data() {
    return {
      emiData: [],
      headers: [],
      loadTable: false,
      totalItems: 0,
      selected: [],
      options: {
        page: 1,
        itemsPerPage: 10
      },
      emiStatusOptions: [
        { text: 'Pending', value: 'Pending' },
        { text: 'Paid', value: 'Paid' },
        { text: 'Overdue', value: 'Overdue' },
        { text: 'Partial', value: 'Partial' },
        { text: 'Minimum Amount Paid', value: 'Minimum Amount Paid' }
      ],
      pddStatusOptions: [
        { text: 'Completed', value: 'Completed' },
        { text: 'Not Completed', value: 'Not Completed' },
        { text: 'No PDD', value: 'No PDD' },
        { text: 'In Progress', value: 'In Progress' },
        { text: 'Pending Review', value: 'Pending Review' }
      ],
      editEmiDialog: false,
      selectedEmi: {},
      bulkUploadDialog: false,
      bulkUpdating: false
    }
  },
  methods: {
    statusFormat(status) {
      if (status === 'Minimum Amount Paid') {
        return 'Min Amt Paid'
      }
    },
    routeToCustomer(item) {
      const customerName = item.customer?.customerFullName || ''
      if (customerName) {
        const url = this.$router.resolve(
          `/workforce/${this.caseConfig.useCase}/${this.caseConfig?._id}?activeTab=CaseCustomer&search=${customerName}`
        ).href
        window.open(url, '_blank')
      }
    },
    editEmi(item) {
      this.selectedEmi = item
      this.editEmiDialog = true
    },

    deleteEmi(item) {
      this.$emit('deleteEmi', item)
    },

    async handleEmiUpdate(payload) {
      await this.$axios.put(
        `/workforce/collection/emi-details/${payload.emiId}`,
        payload.updatedEmi
      )
      const index = this.emiData.findIndex((emi) => emi._id === payload.emiId)
      if (index !== -1) {
        this.$set(this.emiData, index, { ...this.emiData[index], ...payload.updatedEmi })
      }
      this.$toast.success('EMI details updated successfully')
    },

    employeeFullName(item) {
      return fullName(item)
    },

    formatAmount(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toLocaleString('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    getStatusColor(status) {
      return getEmiStatusColor(status).chipColor
    },
    statusColor(colors) {
      const colorInfo = chipStatusColors[colors.toLowerCase()]
      return colorInfo || {}
    },
    getOverdueInfo(item) {
      return calculateOverdueDays(
        item.dueDate,
        item.emiStatus,
        item.paymentDate,
        item.emiAmount,
        item.lateFeeType,
        item.lateFeeValue
      )
    },
    getPDDStatus(activities) {
      if (activities?.length) {
        const isAllStepsCompleted = activities.every((activity) => activity?.status === 'Completed')
        return isAllStepsCompleted
          ? { status: 'Completed', color: this.statusColor('completed')?.textColor }
          : { status: 'Not Completed', color: this.statusColor('not_completed')?.textColor }
      }
      return { status: 'No PDD', color: 'grey' }
    },

    getActivitiesTooltip(item) {
      if (!item.activities?.length) return 'No activities'

      return item.activities
        .map(
          (activity) =>
            `<div style="margin-bottom: 8px; padding: 4px 0; border-bottom: 1px solid rgba(255,255,255,0.2);">
          <strong>${activity.activityType}</strong><br>
          <span>Status:</span> <span style="color: ${
            activity.status === 'Completed' ? '#4CAF50' : '#FF9800'
          };">
             ${activity.status}
          </span>
        </div>`
        )
        .join('')
    },

    async fetchEmiDetails() {
      try {
        const { page, itemsPerPage } = this.options
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems,
          ...this.filterParams
        }
        if (this.caseConfig.useCase == 'loan_management') {
          params.subCase = this.caseConfig?._id
        }

        // Add customer filter from route query
        if (this.$route.query.customer) {
          params.customer = this.$route.query.customer
        }

        this.loadTable = true
        const response = await this.$axios.get('/workforce/collection/emi-details', { params })

        this.emiData = response.data.emiDetails || []
        this.totalItems = response.data?.pagination?.totalCount || this.emiData.length || 0
        this.loadTable = false
      } catch (error) {
        console.log(error)
        this.loadTable = false
      }
    },

    async exportEmiData() {
      try {
        const chunkSize = 50
        const totalPages = Math.ceil(this.totalItems / chunkSize)

        const promises = []

        for (let index = 0; index < totalPages; index++) {
          const params = {
            page: index + 1,
            limit: chunkSize,
            ...this.filterParams
          }

          // Add customer filter from route query
          if (this.$route.query.customer) {
            params.customer = this.$route.query.customer
          }

          const promise = this.$axios.get('/workforce/collection/emi-details', { params })
          promises.push(promise)
        }

        const responses = await Promise.all(promises)

        const data = responses.reduce((acc, response) => {
          return acc.concat(response.data.emiDetails || [])
        }, [])

        await this.generateExcelReport(data)
      } catch (error) {
        console.error('Error exporting EMI data:', error)
        this.$toast.error('Failed to export EMI data')
      }
    },

    async generateExcelReport(data) {
      const ExcelJS = require('exceljs')
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('EMI Details')

      // Define columns
      worksheet.columns = [
        { header: 'S.No', key: 'serialNumber', width: 8 },
        { header: 'Application Number', key: 'applicationNumber', width: 20 },
        { header: 'Customer Name', key: 'customerName', width: 25 },
        { header: 'Type of Loan', key: 'typeOfLoan', width: 15 },
        { header: 'Loan Amount Sanctioned', key: 'loanAmountSanctioned', width: 20 },
        { header: 'Interest Rate', key: 'interestRate', width: 12 },
        { header: 'Loan Tenure', key: 'loanTenure', width: 12 },
        { header: 'Loan Current Status', key: 'loanCurrentStatus', width: 18 },
        { header: 'Due Date', key: 'dueDate', width: 12 },
        { header: 'EMI Amount', key: 'emiAmount', width: 12 },
        { header: 'Principal Amount', key: 'principalAmount', width: 15 },
        { header: 'Interest Amount', key: 'interestAmount', width: 15 },
        { header: 'Outstanding Amount', key: 'outstandingAmount', width: 18 },
        { header: 'Penalty Amount', key: 'penaltyAmount', width: 15 },
        { header: 'Late Fee Amount', key: 'lateFeeAmount', width: 15 },
        { header: 'Late Fee Type', key: 'lateFeeType', width: 12 },
        { header: 'Late Fee Value', key: 'lateFeeValue', width: 12 },
        { header: 'Is Paid', key: 'isPaid', width: 10 },
        { header: 'EMI Status', key: 'emiStatus', width: 12 },
        { header: 'Payment Method', key: 'paymentMethod', width: 15 },
        { header: 'Total Activities', key: 'totalActivities', width: 15 },
        { header: 'Completed Activities', key: 'completedActivities', width: 18 },
        { header: 'Pending Activities', key: 'pendingActivities', width: 16 },
        { header: 'PDD Status', key: 'pddStatus', width: 12 },
        { header: 'Sanction Type', key: 'sanctionType', width: 15 },
        { header: 'Sanction Document', key: 'sanctionDocument', width: 20 },
        { header: 'Sanction Status', key: 'sanctionStatus', width: 15 },
        { header: 'Emi Id', key: 'emiId', width: 25 }
      ]

      worksheet.getRow(1).font = { bold: true, color: { argb: 'FFFFFF' } }
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '366092' }
      }
      worksheet.getRow(1).alignment = { horizontal: 'center', vertical: 'middle' }

      const restructuredData = this.prepareEmiExportData(data)
      restructuredData.forEach((row, index) => {
        const excelRow = worksheet.addRow(row)

        if (index % 2 === 0) {
          excelRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' }
          }
        }

        const emiStatusCell = excelRow.getCell(16)
        emiStatusCell.dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Pending,Paid,Overdue,Partial,Minimum Amount Paid"']
        }

        const pddStatusCell = excelRow.getCell(21)
        pddStatusCell.dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"Completed,Not Completed,No PDD,In Progress,Pending Review"']
        }
      })

      worksheet.columns.forEach((column) => {
        if (column.width < 10) column.width = 10
        if (column.width > 30) column.width = 30
      })

      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      const file_date = new Date().toISOString().split('T')[0]
      link.download = `EMI_Details_${file_date}.xlsx`

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      this.$toast.success('EMI report exported successfully!')
    },

    prepareEmiExportData(data) {
      const exportData = []
      let serialNumber = 1

      data.forEach((item) => {
        const loan = item.loan || {}
        const caseData = item.case || {}
        const customer = caseData.customer || {}
        const activities = item.activities || []

        // Base EMI data that will be common for all rows
        const overdueInfo = this.getOverdueInfo(item)

        const baseEmiData = {
          serialNumber: serialNumber,
          applicationNumber: loan.applicationNumber || '',
          customerName: customer.customerFullName || '',
          typeOfLoan: loan.typeOfLoan || '',
          loanAmountSanctioned: loan.loanAmountSanctioned || 0,
          interestRate: loan.interestRate || 0,
          loanTenure: loan.tenure || 0,
          loanCurrentStatus: loan.currentStatus || '',
          dueDate: item.dueDate ? this.$dayjs(item.dueDate).format('DD/MM/YYYY') : '',
          emiAmount: item.emiAmount || 0,
          principalAmount: item.principalAmount || 0,
          interestAmount: item.interestAmount || 0,
          outstandingAmount: item.outstandingAmount || 0,
          penaltyAmount: item.penaltyAmount || 0,
          lateFeeAmount: overdueInfo.lateFeeAmount || 0,
          lateFeeType: item.lateFeeType || '',
          lateFeeValue: item.lateFeeValue || 0,
          isPaid: item.isPaid ? 'Yes' : 'No',
          emiStatus: item.emiStatus || '',
          paymentMethod: item.paymentMethod || '',
          totalActivities: activities.length || 0,
          completedActivities: activities.filter((act) => act.status === 'Completed').length || 0,
          pendingActivities: activities.filter((act) => act.status !== 'Completed').length || 0,
          pddStatus: this.getPDDStatus(activities).status,
          emiId: item?._id
        }

        if (activities.length > 0) {
          activities.forEach((activity) => {
            exportData.push({
              ...baseEmiData,
              serialNumber: serialNumber++,
              sanctionType: activity.activityType || '',
              sanctionDocument: activity.activityDoc || '',
              sanctionStatus: activity.status || ''
            })
          })
        } else {
          exportData.push({
            ...baseEmiData,
            serialNumber: serialNumber++,
            sanctionType: '',
            sanctionDocument: '',
            sanctionStatus: ''
          })
        }
      })

      return exportData
    },

    openBulkUploadDialog() {
      this.bulkUploadDialog = true
    },

    closeBulkUploadDialog() {
      this.bulkUploadDialog = false
    },

    handleBulkUploadComplete() {
      this.closeBulkUploadDialog()
      this.fetchEmiDetails() // Refresh the table
      this.$emit('show-notification', {
        type: 'success',
        message: 'EMI bulk update completed successfully!'
      })
    },

    getBulkUploadPayload() {
      const payload = {}

      if (this.caseConfig.useCase === 'loan_management') {
        payload.subCase = this.caseConfig?._id
      }

      return payload
    },

    calculateMinimumAmountForEmi(item) {
      const interestAmount = parseFloat(item.interestAmount) || 0
      const overdueInfo = this.getOverdueInfo(item)
      const lateFeeAmount = overdueInfo.lateFeeAmount || 0
      return interestAmount + lateFeeAmount
    },

    async handleBulkStatusUpdate(status) {
      if (this.selected.length === 0) {
        this.$toast.warning('Please select EMI records to update')
        return
      }

      this.bulkUpdating = true

      try {
        const updatePromises = this.selected.map((emi) => {
          const updatedEmi = {
            emiStatus: status,
            updatedAt: new Date().toISOString()
          }

          return this.$axios
            .put(`/workforce/collection/emi-details/${emi._id}`, updatedEmi)
            .then((response) => ({
              success: true,
              emiId: emi._id,
              data: response.data,
              updatedEmi
            }))
            .catch((error) => ({
              success: false,
              emiId: emi._id,
              error: error.response?.data?.message || error.message
            }))
        })

        const results = await Promise.all(updatePromises)

        const successfulUpdates = results.filter((result) => result.success)
        const failedUpdates = results.filter((result) => !result.success)

        // Update local data for successful updates
        successfulUpdates.forEach((result) => {
          const index = this.emiData.findIndex((emi) => emi._id === result.emiId)
          if (index !== -1) {
            this.$set(this.emiData, index, { ...this.emiData[index], ...result.updatedEmi })
          }
        })

        // Clear selection
        this.selected = []

        // Show notifications
        if (successfulUpdates.length > 0) {
          this.$emit('show-notification', {
            type: 'success',
            message: `${successfulUpdates.length} EMI status(es) updated to ${status}`
          })
        }

        if (failedUpdates.length > 0) {
          this.$emit('show-notification', {
            type: 'warning',
            message: `${successfulUpdates.length} EMI(s) updated successfully, ${failedUpdates.length} failed`
          })
        }
      } catch (error) {
        console.error('Bulk update error:', error)
        this.$emit('show-notification', {
          type: 'error',
          message: 'Failed to update EMI statuses'
        })
      } finally {
        this.bulkUpdating = false
      }
    }
  },

  computed: {
    bulkUploadEndpoint() {
      return '/workforce/emi-details/bulk'
    }
  },

  watch: {
    caseConfig: {
      handler(config) {
        if (config) {
          this.headers = [
            {
              text: 'Application No',
              value: 'applicationNumber',
              sortable: false,
              width: '140px'
            },
            { text: 'Customer', value: 'customerName', sortable: false, width: '120px' },
            { text: 'Loan Type', value: 'typeOfLoan', sortable: false, width: '120px' },
            { text: 'EMI Amount', value: 'emiAmount', sortable: false, width: '120px' },
            { text: 'Due Date', value: 'dueDate', sortable: false, width: '120px' },
            { text: 'Outstanding', value: 'outstandingAmount', sortable: false, width: '130px' },
            { text: 'Penalty', value: 'penaltyAmount', sortable: false, width: '100px' },
            { text: 'Late Fee', value: 'lateFee', sortable: false, width: '100px' },
            { text: 'Payment Method', value: 'paymentMethod', sortable: false, width: '130px' },
            { text: 'Status', value: 'emiStatus', sortable: false, width: '100px' },
            // { text: 'PDD Status', value: 'pddStatus', sortable: false, width: '100px' },
            { text: 'PDD(s)', value: 'activitiesCount', sortable: false, width: '100px' },
            { text: 'Action', value: 'action', sortable: false, width: '100px' }
          ]
        }
      },
      immediate: true
    },

    options: {
      handler() {
        this.fetchEmiDetails()
      },
      deep: true
    },

    filterParams: {
      handler() {
        this.options.page = 1
        this.fetchEmiDetails()
      },
      deep: true
    },

    '$route.query.customer': {
      handler() {
        this.options.page = 1
        this.fetchEmiDetails()
      },
      immediate: true
    }
  },

  mounted() {
    this.fetchEmiDetails()
  }
}
</script>
<style scoped>
.v-data-table >>> .v-data-table__wrapper > table > tbody > tr > td > .v-data-table__checkbox {
  text-align: center;
}

.position-relative {
  position: relative;
}
</style>
