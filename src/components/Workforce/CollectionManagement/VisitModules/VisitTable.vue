<template>
  <div class="py-2">
    <!-- Filters and actions moved to parent component slot -->

    <!-- Conditional table rendering -->
    <!-- Visit Table -->
    <div v-if="!isLoanPage" class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="caseTasks"
        class="text-sm"
        @click:row="selectTask"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30, 50] }"
        :loading="loadTable"
        :options.sync="options"
        :server-items-length="totalItems"
        fixed-header
        show-select
        item-key="_id"
        v-model="selected"
      >
        <template v-slot:item.taskTitle="{ item }">
          <td
            class="px-4 py-6 cursor-pointer hover:text-blue-600 hover:dark-hover-text font-semibold"
          >
            <span
              class="block max-w-[180px] truncate"
              v-tooltip="{
                text: `${item.taskTitle}`
              }"
            >
              {{ item.taskTitle }}
            </span>
          </td>
        </template>
        <template v-slot:item.caseNo="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.case.caseNo }}</td>
        </template>
        <template v-slot:item.taskDescription="{ item }">
          <td class="px-4 py-6 font-semibold">
            <span
              class="block max-w-[200px] truncate text-center"
              v-tooltip="{
                text: `${item.taskDescription}`
              }"
            >
              {{ item.taskDescription }}
            </span>
          </td>
        </template>

        <template v-slot:item.bkt="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.case.bkt }}</td>
        </template>
        <template v-slot:item.pos="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.case.pos }}</td>
        </template>
        <template v-slot:item.emiAmount="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.case.emiAmount }}</td>
        </template>
        <template v-slot:item.taskStartDateTime="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{
              item.actualStartDateTime ? $dayjs(item.actualStartDateTime).format('DD/MM/YYYY') : ''
            }}
            <br />
            {{
              item.actualStartDateTime ? $dayjs(item.actualStartDateTime).format('hh:mm A') : '-'
            }}
          </td>
        </template>
        <template v-slot:item.nextVisitDateTime="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{
              item.meetingDetails?.nextTaskScheduledOn
                ? $dayjs(item.meetingDetails?.nextTaskScheduledOn).format('DD/MM/YYYY')
                : ''
            }}
            <br />
            {{
              item.meetingDetails?.nextTaskScheduledOn
                ? $dayjs(item.meetingDetails?.nextTaskScheduledOn).format('hh:mm A')
                : '-'
            }}
          </td>
        </template>
        <template v-slot:item.meetingOutcome="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.meetingDetails?.meetingOutCome }}
          </td>
        </template>
        <template v-slot:item.assignedTo="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ employeeFullName(item.assignedTo) }}
          </td>
        </template>
        <template v-slot:item.taskStatus="{ item }">
          <OrderStatuses
            :orderStatus="item.taskStatus"
            :statuses="taskStatusOptions"
            @statusChange="(status) => updateCaseTask(status, item._id)"
          />
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4 py-6" @click.stop>
            <action-button
              :item="item"
              :canEdit="canEdit"
              :handleEdit="() => casesEdit(item)"
              :handleDelete="() => caseTaskDelete(item)"
              :showDeleteButton="canDelete"
              :showEditButton="canEdit"
              :canDelete="canDelete"
            />
          </td>
        </template>
      </v-data-table>
    </div>

    <!-- EMI Table -->
    <EmiTable
      v-else
      ref="emiTable"
      :caseConfig="caseConfig"
      :canEdit="canEdit"
      :canDelete="canDelete"
      :filterParams="filterParams()"
      @emiSelected="handleEmiSelected"
      @editEmi="handleEditEmi"
      @deleteEmi="handleDeleteEmi"
    />

    <v-dialog v-model="showTaskDataDialog" width="70rem">
      <div class="sticky flex top-0 justify-end z-50 bg-[#f3f4f6] dark-bg-custom">
        <v-btn plain @click="closeDialog">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </div>
      <CaseTaskData
        :task="selectedTask"
        :selectedCase="selectedCase"
        :key="selectedTask?._id"
        :caseConfig="caseConfig"
      />
    </v-dialog>
    <v-dialog v-model="openAddCaseTask" max-width="600">
      <AddEditCaseTask
        :editTask="editTask"
        @caseTaskAdded="handleTaskAdded"
        :caseConfig="caseConfig"
      />
    </v-dialog>
    <v-dialog v-model="showExportDialog" max-width="83rem" persistent>
      <v-card class="p-3">
        <div class="flex justify-between items-center btnClass border-b">
          <h6 class="text-xl mb-0">Manage Export Columns</h6>
          <v-btn icon @click="cancelExportDialog" color="black">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
        <v-data-table
          :headers="exportTableHeader"
          hide-default-footer
          :items="[]"
          class="no-data-table sortHandle"
          fixed-header
          :key="anIncreasingNumber"
          v-sortable-table="{ onEnd: sortTheHeadersAndUpdateTheKey }"
        >
          <template v-slot:no-data>
            <div class="text-center pa-4 flex items-center">
              <v-icon size="28">mdi-information</v-icon>
              <p class="mb-0 ml-2">Drag to rearrange headers.</p>
            </div>
          </template>
        </v-data-table>
        <v-divider></v-divider>
        <v-card-text class="mt-4">
          <div class="flex justify-end mb-2">
            <v-btn small color="primary" @click="selectAllFields" :disabled="!exportKeys?.length">
              Select All
            </v-btn>
          </div>
          <v-select
            v-model="selectedKeys"
            :items="exportKeys"
            item-text="text"
            item-value="value"
            label="Fields to Export"
            multiple
            small-chips
            outlined
            clearable
          ></v-select>
        </v-card-text>
        <v-card-actions class="flex justify-center mb-2">
          <v-spacer></v-spacer>
          <v-btn outlined color="grey" class="rounded-md mr-4 px-6" @click="cancelExportDialog">
            <span class="text-black dark-text-color">Cancel</span>
          </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text rounded-md px-6"
            @click="exportVisits"
            :loading="exportLoading"
            :disabled="exportLoading || !selectedKeys.length"
          >
            Export
            <v-icon right dark>mdi-download</v-icon>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import Sortable from 'sortablejs'
import ActionButton from '@/components/ActionButton'
import CaseTaskData from '../CaseTaskData'
import AddEditCaseTask from '../AddEditCaseTask'
import { fullName, debounce, convertToTitleCase, hasPermission } from '@/utils/common'
import OrderStatuses from '../../InventoryManagement/OrderStatuses.vue'
import FilterPopOver from '@/components/FilterPopOver.vue'
import permissionData from '@/utils/permissions'
import EmiTable from './EmiTable.vue'

function watchClass(targetNode, classToWatch) {
  let lastClassState = targetNode.classList.contains(classToWatch)
  const observer = new MutationObserver((mutationsList) => {
    for (let mutation of mutationsList) {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const currentClassState = mutation.target.classList.contains(classToWatch)
        if (lastClassState !== currentClassState) {
          lastClassState = currentClassState
          if (!currentClassState) {
            mutation.target.classList.add('sortHandle')
          }
        }
      }
    }
  })
  observer.observe(targetNode, { attributes: true })
}

export default {
  name: 'VisitTable',
  components: {
    ActionButton,
    CaseTaskData,
    AddEditCaseTask,
    OrderStatuses,
    // FilterPopOver,
    EmiTable
  },
  props: {
    caseConfig: Object,
    employees: Array,
    orgConfig: Object
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      visitStatus: '',
      openAddCaseTask: false,
      valid: true,
      caseTasks: [],
      dates: [],
      selectedCase: {},
      startDate: null,
      endDate: null,
      showTaskDataDialog: false,
      menu: false,
      visitMenu: false,
      exportEmi: false,
      exportLoading: false,
      loadTable: true,
      filter: '',
      selectedTask: null,
      anIncreasingNumber: null,
      selectedVisitDate: null,
      filterAssignedTo: null,
      editTask: null,
      options: { itemsPerPage: 10, page: 1 },
      totalItems: 0,
      rules: {
        dateRule: [
          () => {
            if (this.startDate && !this.endDate) {
              return `Please select both start and end date.`
            }
            return true
          }
        ]
      },

      selectedKeys: [],
      exportKeys: [],
      showExportDialog: false,
      taskStatusOptions: [
        { label: 'Pending', value: 'PENDING' },
        { label: 'In-Progress', value: 'IN_PROGRESS' },
        { label: 'Completed', value: 'COMPLETED' }
      ],
      awsBaseUrl: process.env.VUE_APP_S3_BASE_URL,
      selectedCount: 0,
      selected: [],
      emiDetails: [],
      isStepsCompleted: null
    }
  },
  methods: {
    filterParams() {
      const params = {}
      if (this.startDate && this.endDate) {
        params.start = this.startDate
        params.end = this.endDate
      }
      if (this.visitStatus) {
        params.taskStatus = this.visitStatus
      }
      if (this.caseConfig.name && !this.isLoanPage) {
        params.type = this.caseConfig?.name
      }
      if (this.filter && this.isLoanPage) {
        params.applicationNumber = this.filter
      }
      if (this.filter && !this.isLoanPage) {
        params.search = this.filter
      }
      if (this.selectedVisitDate) {
        params.plannedFor = this.selectedVisitDate
      }
      if (this.filterAssignedTo) {
        params.assignedTo = this.filterAssignedTo
      }
      if (this.isStepsCompleted !== null) {
        params.isStepsCompleted = this.isStepsCompleted
      }
      return params
    },
    async fetchCaseTask() {
      try {
        // Only fetch visit data, EMI table handles its own data
        if (this.isLoanPage) return

        const { page, itemsPerPage } = this.options
        const filterParams = this.filterParams()
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems,
          ...filterParams
        }

        this.loadTable = true
        const response = await this.$axios.get('/workforce/collection/case/tasks', { params })

        this.caseTasks = response.data.tasks
        this.loadTable = false
        this.totalItems = response.data?.pagination?.totalCount || response.data?.total || 0
      } catch (error) {
        console.log(error)
        this.loadTable = false
      }
    },
    casesEdit(item) {
      this.editTask = item
      this.openAddCaseTask = true
    },
    async caseTaskDelete(item) {
      try {
        const params = {
          caseType: this.caseConfig?.name
        }
        const response = await this.$axios.delete(
          `/workforce/collection/case/task/${item._id}`,
          params
        )
        const index = this.caseTasks.findIndex((task) => task._id === item._id)
        if (index !== -1) {
          this.caseTasks.splice(index, 1)
        }
        this.$toast.success(response?.data?.message)
        await this.fetchCaseTask()
      } catch (error) {
        const errorMessage =
          error?.response?.data?.message || 'An error occurred while deleting the case task.'
        this.$toast.error(errorMessage)
        console.error(error)
      }
    },
    async updateCaseTask(taskStatus, id) {
      try {
        const payload = {
          taskStatus: taskStatus
        }
        await this.$axios.put(`/workforce/collection/case/task/${id}`, payload)
        this.fetchCaseTask()
      } catch (error) {
        this.$toast.error(error.response?.data?.message)
        console.log(error)
      }
    },
    async exportVisits() {
      try {
        this.exportLoading = true

        const responses = await this.fetchAllData()

        const exportData = this.prepareExportData(responses)

        const filteredData = exportData.map((row) => {
          const filteredRow = {}
          this.selectedKeys?.forEach((key) => {
            filteredRow[key] = row[key]
          })
          return filteredRow
        })

        const XLSX = require('xlsx')
        const ws = XLSX.utils.json_to_sheet(filteredData)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Task Data')
        const file_date = this.$dayjs().format('DD/MM/YYYY')
        const filename = `${this.caseConfig.name}_Visit_Data_${file_date}.xlsx`
        XLSX.writeFile(wb, filename)
      } catch (error) {
        console.error('Error exporting data:', error)
      } finally {
        this.exportLoading = false
      }
    },
    async fetchAllData() {
      const chunkSize = 50
      const totalCount = this.totalItems
      const totalPages = Math.ceil(totalCount / chunkSize)
      const filterParams = this.filterParams()

      const promises = []

      for (let index = 0; index < totalPages; index++) {
        const params = {
          limit: chunkSize,
          page: index + 1,
          ...filterParams
        }
        const promise = this.$axios.get('/workforce/collection/case/tasks', {
          params
        })
        promises.push(promise)
      }

      const responses = await Promise.all(promises)

      return responses.reduce((acc, response) => acc.concat(response.data.tasks), [])
    },
    async openExportDialog() {
      if (this.isLoanPage) {
        this.exportEmi = true
        await this.$refs.emiTable?.exportEmiData()
        this.exportEmi = false
        return
      }
      if (this.$refs.form) {
        const isValid = this.$refs.form.validate()
        if (!isValid) return
      }
      const exportOptions = this.prepareExportData(this.caseTasks)
      if (exportOptions.length > 0) {
        let maxKeysObject = exportOptions.reduce((prev, current) => {
          return Object.keys(current).length > Object.keys(prev).length ? current : prev
        })
        this.exportKeys = Object.keys(maxKeysObject).map((key) => ({
          text: key,
          value: key
        }))

        const orgQuestion = this.$props.orgConfig.questionnaire
        orgQuestion?.forEach((q) => {
          this.exportKeys.push({
            text: q.question,
            value: q.question
          })
        })
      } else {
        this.exportKeys = []
      }
      this.showExportDialog = true
    },
    cancelExportDialog() {
      this.showExportDialog = false
      const questionnaireQuestions = this.$props.orgConfig?.questionnaire.map((q) => q.question)
      this.selectedKeys = [
        `${this.configLang?.caseNo.displayName}`,
        `${this.configLang?.customerFullName.displayName}`,
        'Customer Address',
        'Case Status',
        'Visit Status',
        'Visit Started',
        'Visit Completed',
        'Visit Start Address',
        'Visit Start Lat/Lon',
        'Visit End Address',
        'Visit End Lat/Lon',
        'Follow Up(Visit)',
        'Case Outcome',
        'Next Follow Up Date/Time',
        'Follow Up Comment',
        ...questionnaireQuestions,
        `${this.configLang?.taskTitle.displayName}`,
        `${this.configLang?.crn.displayName}`,
        'Image Link',
        `${this.configLang?.assignedTo.displayName}`,
        'Employee ID',
        'Role'
      ]
    },
    prepareExportData(data) {
      const visitTitle = this.caseConfig.tableTabMapping?.visitTable?.displayName || 'Visit'
      const custTitle = this.caseConfig.tableTabMapping?.customerTable?.displayName || 'Customer'
      const rows = data.map((el, i) => {
        const rowData = {
          'Sr No': i + 1,
          [`${this.configLang?.assignedTo.displayName}`]: this.employeeFullName(el.assignedTo),
          [`${this.orgLanguage.employees || 'Employee'} Id`]: el.assignedTo?.employeeId || '',
          Role: el.assignedTo?.wfmRole?.role || '',
          [`${visitTitle} Started`]: this.startDateTime(el),
          [`${visitTitle} Completed`]: this.endDateTime(el),
          [`${visitTitle} Status`]: el.taskStatus,
          [`${this.configLang?.customerFullName.displayName}`]:
            el.case?.customer?.customerFullName || '',
          [`${custTitle} Address`]: el.taskDescription,
          [`${visitTitle} Start Address`]: el.additionalInfo?.taskStartData?.address,
          [`${visitTitle} Start Lat/Lon`]: el.additionalInfo?.taskStartData
            ? Object.values(el.additionalInfo?.taskStartData?.coords).join(', ')
            : '',
          [`${visitTitle} End Lat/Lon`]: el.additionalInfo?.taskEndData?.address,
          [`${visitTitle} End Lat/Lon`]: el.additionalInfo?.taskEndData
            ? Object.values(el.additionalInfo?.taskEndData?.coords).join(', ')
            : '',
          [`${this.configLang?.caseNo.displayName}`]: el.case.caseNo,
          [`${this.configLang?.bkt.displayName}`]: el.case.bkt,
          [`${this.configLang?.pos.displayName}`]: el.case.pos,
          [`${this.configLang?.emiAmount?.displayName}`]: el.case?.emiAmount,
          [`${this.configLang?.productInfo?.displayName}`]: el.case?.productInfo,
          [`${this.configLang?.typeOfLoan?.displayName}`]: el.case?.typeOfLoan,
          [`${this.configLang?.tenure?.displayName}`]: el.case?.tenure,
          [`${this.configLang?.taskTitle.displayName}`]: el.taskTitle,
          [`${this.configLang?.crn.displayName}`]: el.case.customer?.crn || '',
          Status: el.case.status,
          'Follow Up(Visit)': el.meetingDetails?.meetingOutCome || '-',
          Outcome: el.case?.userDefinedCaseStatus,
          'Next Follow Up Date/Time': el.meetingDetails?.nextTaskScheduledOn
            ? this.$dayjs(el.meetingDetails?.nextTaskScheduledOn).format('DD/MM/YYYY hh:mm A')
            : '',
          'Follow Up Comment': el.meetingDetails?.meetingNotes,
          'Image Link': el.additionalInfo?.selfie
            ? `${this.awsBaseUrl}${el.additionalInfo?.selfie?.doc_physical_path}`
            : '',
          'Audio Link': el.additionalInfo?.audio
            ? `${this.awsBaseUrl}${el.additionalInfo?.audio?.doc_physical_path}`
            : ''
        }

        if (el.case?.additionalColumns) {
          Object.entries(el.case?.additionalColumns)?.forEach(([key, value]) => {
            rowData[convertToTitleCase(key)] = value
          })
        }

        el.meetingDetails?.questionnaire?.forEach((question) => {
          if (
            ['VIDEO_CAPTURE', 'AUDIO_CAPTURE', 'LOCAL_ATTACHMENTS', 'CLICK_PICTURES'].includes(
              question.questionType
            )
          ) {
            if (Array.isArray(question.userResponse)) {
              rowData[question.question] = question.userResponse
                .map((response) => `${this.awsBaseUrl}${response}`)
                .join(', ')
            } else if (question.userResponse) {
              rowData[question.question] = `${this.awsBaseUrl}${question.userResponse}`
            } else {
              rowData[question.question] = ''
            }
          } else {
            rowData[question.question] = Array.isArray(question.userResponse)
              ? question.userResponse.join(', ')
              : question.userResponse
          }
        })

        el.meetingDetails?.attachments?.forEach((attachment, index) => {
          rowData[`Attachment ${index + 1}`] = `${this.awsBaseUrl}${attachment.filePath}`
        })

        return rowData
      })
      return rows
    },
    onDateSelected() {
      if (this.dates.length === 2) {
        this.startDate = this.formatDate(this.dates[0])
        this.endDate = this.formatDate(this.dates[1])
        this.options.page = 1
        this.fetchCaseTask()
      } else {
        this.startDate = this.formatDate(this.dates[0])
        this.endDate = null
      }
    },
    onDateCleared() {
      this.dates = []
      this.startDate = null
      this.endDate = null
      this.fetchCaseTask()
    },
    onVisitSelected() {
      this.fetchCaseTask()
      this.visitMenu = false
    },
    onVisitCleared() {
      this.selectedVisitDate = null
      this.fetchCaseTask()
    },
    formatDate(dateString) {
      return this.$dayjs(dateString).format('YYYY-MM-DD')
    },
    startDateTime(item) {
      if (item.additionalInfo?.taskStartData) {
        return this.$dayjs(item.additionalInfo?.taskStartData?.datetime).format(
          'DD/MM/YYYY hh:mm A'
        )
      } else if (item.actualStartDateTime) {
        return this.$dayjs(item.actualStartDateTime).format('DD/MM/YYYY hh:mm A')
      } else {
        return ''
      }
    },
    sortTheHeadersAndUpdateTheKey(evt) {
      let headersTmp = [...this.exportTableHeader]
      const oldIndex = evt.oldIndex
      const newIndex = evt.newIndex
      if (newIndex >= headersTmp.length) {
        let k = newIndex - headersTmp.length + 1
        while (k--) {
          headersTmp.push(undefined)
        }
      }

      headersTmp.splice(newIndex, 0, headersTmp.splice(oldIndex, 1)[0])
      headersTmp = headersTmp.filter((el) => el !== undefined)

      const newKeys = headersTmp.map((key) => key?.text)
      this.selectedKeys.splice(0, this.selectedKeys.length, ...newKeys)
      this.anIncreasingNumber += 1
    },

    endDateTime(item) {
      if (item.additionalInfo?.taskEndData) {
        return this.$dayjs(item.additionalInfo?.taskEndData?.datetime).format('DD/MM/YYYY hh:mm A')
      } else if (item.actualStartDateTime) {
        return this.$dayjs(item.actualEndDateTime).format('DD/MM/YYYY hh:mm A')
      } else {
        return ''
      }
    },
    selectAllFields() {
      this.selectedKeys = this.exportKeys.map((key) => key.value)
      this.anIncreasingNumber += 1
    },
    handleTaskAdded() {
      this.openAddCaseTask = false
      this.fetchCaseTask()
    },
    selectTask(task) {
      this.selectedTask = task
      this.selectedCase = task.case
      this.showTaskDataDialog = true
    },
    closeDialog() {
      this.showTaskDataDialog = false
    },
    employeeFullName(item) {
      return fullName(item)
    },
    handleInput: debounce(function () {
      this.options.page = 1
      this.fetchCaseTask()
    }, 500),
    visibleDate(date) {
      return this.$dayjs(date).format('DD-MM-YYYY')
    },

    // Func for count total filtered count
    updateSelectedCount() {
      this.selectedCount = [
        this.visitDate,
        this.formattedDate,
        this.visitStatus,
        this.filterAssignedTo
      ].filter((item) => item !== null && item !== '' && item.length > 0).length
    },
    handleChange() {
      this.handleInput()
      this.updateSelectedCount()
    },
    handleUnassignedCases() {
      this.filterAssignedTo = 'null'
      this.handleChange()
    },
    handleEmiSelected(emi) {
      // Handle EMI selection - you can show EMI details dialog
      this.selectedTask = emi
      this.selectedCase = {
        ...emi.loan,
        emiDetails: emi,
        activities: emi.activities
      }
      this.showTaskDataDialog = true
    },
    handleEditEmi(item) {
      // Handle EMI edit
      this.editTask = item
      this.openAddCaseTask = true
    },
    handleDeleteEmi(item) {
      // Handle EMI delete
      console.log('Delete EMI:', item)
      // You can implement delete logic here
    },

    openBulkUploadDialog() {
      if (this.isLoanPage && this.$refs.emiTable) {
        this.$refs.emiTable.openBulkUploadDialog()
      }
    },

    openBulkUpdateDialog() {
      if (this.isLoanPage && this.$refs.emiTable) {
        this.$refs.emiTable.openBulkUpdateDialog()
      }
    },

    setDefaultDateRange() {
      // Set default date range to current month start and end only for loan management
      if (this.isLoanPage) {
        const now = this.$dayjs()
        const startOfMonth = now.startOf('month').format('YYYY-MM-DD')
        const endOfMonth = now.endOf('month').format('YYYY-MM-DD')

        this.dates = [startOfMonth, endOfMonth]
        this.startDate = startOfMonth
        this.endDate = endOfMonth
      }
    }
  },
  directives: {
    'sortable-table': {
      inserted: (el, binding) => {
        el.querySelectorAll('th')?.forEach((draggableEl) => {
          watchClass(draggableEl, 'sortHandle')
          draggableEl.classList.add('sortHandle')
        })
        Sortable.create(
          el.querySelector('tr'),
          binding.value ? { ...binding.value, handle: '.sortHandle' } : {}
        )
      }
    }
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.collectionEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.collectionDelete)
    },
    formattedDate() {
      if (this.dates.length === 2) {
        const startDate = this.visibleDate(this.dates[0])
        const endDate = this.visibleDate(this.dates[1])
        return `${startDate} to ${endDate}`
      } else if (this.dates.length === 1) {
        return this.visibleDate(this.dates[0])
      }
      return ''
    },
    visitDate() {
      if (this.selectedVisitDate) {
        return this.$dayjs(this.selectedVisitDate).format('DD-MM-YYYY')
      }
      return ''
    },
    exportTableHeader() {
      const headers = this.selectedKeys.map((el) => {
        return { text: el, sortable: false, width: '120px' }
      })
      return headers
    },
    filteredTaskStatus() {
      if (this.dates.length) {
        return this.taskStatusOptions.filter((s) => s.value !== 'PENDING')
      }
      return this.taskStatusOptions
    },
    isExportDisabled() {
      return this.dates.length === 1
    },
    users() {
      return [
        ...this.employees.map((emp) => ({
          ...emp,
          fullName: fullName(emp)
        })),
        {
          _id: 'null',
          fullName: 'Unassigned'
        }
      ]
    },
    configLang() {
      return this.caseConfig.fieldMappings
    },
    orgLanguage() {
      return this.orgLang.data.leftNav
    },
    isLoanPage() {
      return this.$route?.params?.useCase === 'loan_management'
    }
  },
  beforeCreate() {
    this.debouncedFetchCaseTask = () => {}
  },
  created() {
    this.debouncedFetchCaseTask = debounce(this.fetchCaseTask, 500)
    this.setDefaultDateRange()
  },
  mounted() {
    this.$emit('component-mounted')
  },
  watch: {
    '$route.query.empId': {
      async handler(newQuery) {
        try {
          this.filterAssignedTo = newQuery || ''
          this.options.page = 1
          await this.debouncedFetchCaseTask()
        } catch (error) {
          console.error('Error updating employee search:', error)
        }
      },
      immediate: true
    },
    caseConfig: {
      handler(config) {
        if (config) {
          const fm = config.fieldMappings

          const columns = [
            { key: 'taskTitle', text: fm.taskTitle?.displayName, width: null },
            { key: 'caseNo', text: fm.caseNo?.displayName, width: '150px' },
            {
              key: 'taskDescription',
              text: fm.taskDescription?.displayName,
              width: '180px'
            },
            { key: 'bkt', text: fm.bkt?.displayName, width: '80px' },
            { key: 'pos', text: fm.pos?.displayName, width: null },
            { key: 'emiAmount', text: fm.emiAmount?.displayName, width: null },
            {
              key: 'taskStartDateTime',
              text: 'Start Date/Time',
              width: '120px'
            },
            {
              key: 'nextVisitDateTime',
              text: 'Next Visit Date/Time',
              width: '120px'
            },
            {
              key: 'meetingOutcome',
              text: 'Meeting Status',
              width: '100px'
            },
            {
              key: 'assignedTo',
              text: fm.assignedTo?.displayName,
              width: '150px'
            },
            { key: 'taskStatus', text: 'Status', width: null },
            { key: 'action', text: 'Action', width: null }
          ]

          this.headers = columns
            .filter((col) => !col.key || fm[col.key]?.visibility !== false) // Filter out columns with false visibility
            .map((col) => ({
              text: col.text,
              value: col.key || col.text.toLowerCase().replace(/ /g, ''),
              sortable: false,
              width: col.width || undefined
            }))

          const visitTitle = this.caseConfig.tableTabMapping?.visitTable?.displayName || 'Visit'

          // Processing selected keys
          const questionnaireQuestions =
            this.$props.orgConfig?.questionnaire?.map((q) => q?.question) || []
          this.selectedKeys = [
            fm.caseNo?.displayName,
            fm.customerFullName?.displayName,
            `${fm.customerFullName?.displayName || 'Customer'} Address`,
            'Status',
            `${visitTitle} Status`,
            `${visitTitle} Started`,
            `${visitTitle} Completed`,
            `${visitTitle} Start Address`,
            `${visitTitle} Start Lat/Lon`,
            `${visitTitle} End Address`,
            `${visitTitle} End Lat/Lon`,
            `Follow Up(${visitTitle})`,
            'Outcome',
            'Next Follow Up Date/Time',
            'Follow Up Comment',
            ...questionnaireQuestions,
            fm.taskTitle?.displayName,
            fm.crn?.displayName,
            'Image Link',
            fm.assignedTo?.displayName,
            `${this.orgLanguage.employees || 'Employee'} Id`
          ]
        }
      },
      immediate: true
    },

    options: {
      handler() {
        this.fetchCaseTask()
      },
      deep: true
    },
    selectedKeys: {
      handler(newHeaders, oldHeaders) {
        if (newHeaders !== oldHeaders) {
          this.sortTheHeadersAndUpdateTheKey(newHeaders)
        }
      },
      deep: true
    },

    // updated filtered counts
    visitDate() {
      this.updateSelectedCount()
    },
    formattedDate() {
      this.updateSelectedCount()
    }
  }
}
</script>
<style scoped>
.sortHandle {
  cursor: move;
}

.v-data-table >>> .v-data-table__wrapper > table > tbody > tr > td > .v-data-table__checkbox {
  text-align: center;
}
.wfm-search-fields.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 40px !important;
}
.unassignedOptions {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}
.v-list {
  padding-top: 0px !important;
}
</style>
