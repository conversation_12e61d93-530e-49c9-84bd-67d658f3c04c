<template>
  <div>
    <!-- Template Selection -->
    <div class="mb-4">
      <v-select
        v-model="selectedTemplateId"
        :items="availableTemplates"
        item-text="name"
        item-value="_id"
        label="Workflow Template"
        dense
        outlined
        hide-details
        :disabled="!!workflowInstance"
        prepend-inner-icon="mdi-file-tree-outline"
        @change="handleTemplateChange"
        :loading="loadingTemplates"
      >
      </v-select>
    </div>

    <!-- Workflow Steps -->
    <div v-if="workflowSteps.length > 0" class="workflow-steps">
      <v-expansion-panels v-model="expandedPanel" class="workflow-panels" flat>
        <v-expansion-panel
          v-for="(step, index) in workflowSteps"
          :key="`step-${index}`"
          class="workflow-step-panel"
          :class="{ 'step-completed': step.status === 'COMPLETED' }"
        >
          <v-expansion-panel-header class="workflow-panel-header">
            <div class="workflow-header-content">
              <div class="step-info">
                <v-icon
                  :color="step.status === 'COMPLETED' ? 'success' : 'grey'"
                  class="step-icon mr-3"
                >
                  {{ step.status === 'COMPLETED' ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                </v-icon>
                <div class="step-details">
                  <span v-if="step.isRequired" class="text-red-500 mr-2">*</span>
                  <span class="step-name">{{ step.name }}</span>
                </div>
              </div>
              <div class="step-actions">
                <!-- Remarks Icon -->
                <v-btn
                  icon
                  small
                  @click.stop="toggleRemarks(index)"
                  :color="step.remarks ? 'primary' : 'grey'"
                  class="mr-1"
                  v-tooltip="{ text: 'Add Remarks' }"
                >
                  <v-icon x-small>mdi-comment-text-outline</v-icon>
                </v-btn>
              </div>
            </div>
          </v-expansion-panel-header>

          <v-expansion-panel-content v-slot="{ open }">
            <!-- Step Remarks (shown when toggled) -->
            <div v-if="showRemarks[index]" class="remarks-section mt-4 mb-2">
              <v-textarea
                v-if="open"
                v-model="step.remarks"
                label="Step Remarks"
                outlined
                dense
                rows="2"
                auto-grow
                hide-details
                :disabled="readonly"
                placeholder="Add any notes or comments for this step"
              ></v-textarea>
            </div>

            <!-- Requirements Section -->
            <div
              v-if="step.requirements && step.requirements.length > 0"
              class="requirements-section"
            >
              <div
                v-for="(req, reqIndex) in step.requirements"
                :key="`req-${index}-${reqIndex}`"
                class="requirement-item"
              >
                <div class="requirement-content">
                  <div class="requirement-header">
                    <div class="requirement-info">
                      <span v-if="step.isRequired" class="text-red-500 mr-2">*</span>
                      <p class="mb-0">{{ req.name }}</p>
                      <div class="requirement-badges">
                        <!-- <v-chip
                          v-if="req.isRequired"
                          x-small
                          color="orange"
                          text-color="white"
                          class="mr-1"
                        >
                          Required
                        </v-chip> -->
                        <!-- <v-chip v-if="req.isDocument" x-small color="blue" text-color="white">
                          Document
                        </v-chip> -->
                      </div>
                    </div>
                    <v-checkbox
                      v-if="open"
                      v-model="req.completed"
                      label="Done"
                      color="success"
                      hide-details
                      :disabled="readonly"
                      @change="handleRequirementChange(index, reqIndex)"
                      class="requirement-checkbox"
                    ></v-checkbox>
                  </div>
                  <div v-if="req.isDocument && !readonly" class="">
                    <v-file-input
                      v-model="req.file"
                      label="Attach Document"
                      outlined
                      dense
                      hide-details
                      prepend-icon=""
                      placeholder="Upload document"
                      prepend-inner-icon="mdi-paperclip"
                      @change="handleFileUpload(index, reqIndex, $event)"
                      class="document-upload"
                    ></v-file-input>

                    <!-- Document Preview if already uploaded -->
                    <div v-if="req.documentUrl" class="mt-2">
                      <v-btn small text color="primary" @click="previewDocument(req.documentUrl)">
                        <v-icon small left>mdi-file-document-outline</v-icon>
                        View Document
                      </v-btn>
                    </div>
                  </div>

                  <!-- Document Upload (only if isDocument is true) -->
                </div>
              </div>
            </div>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </div>

    <!-- No Steps Message -->
    <div v-else class="no-steps-message">
      <v-icon large color="grey lighten-1">mdi-timeline-plus-outline</v-icon>
      <p>No workflow steps available. Please select a workflow template.</p>
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils/common'

export default {
  name: 'WorkflowStepRenderer',
  props: {
    entityType: {
      type: String,
      required: true,
      validator: (value) => ['LOAN', 'DISBURSEMENT', 'EMI'].includes(value)
    },
    loanType: {
      type: String,
      default: null
    },
    readonly: {
      type: Boolean,
      default: false
    },
    existingWorkflowData: {
      type: [Array, Object],
      default: null
    }
  },
  data() {
    return {
      // Template selection
      selectedTemplateId: null,
      availableTemplates: [],
      loadingTemplates: false,

      // Workflow instance data
      workflowInstance: null,
      workflowSteps: [],

      // UI state
      expandedPanel: 0,
      showRemarks: {},

      // Flag to prevent infinite loops during data population
      isPopulating: false
    }
  },
  created() {
    this.debouncedPopulateFromExistingData = debounce(this.populateFromExistingData, 300)
  },
  async mounted() {
    await this.fetchTemplates()

    if (this.workflowInstanceId) {
      await this.fetchWorkflowInstance()
    } else if (this.existingWorkflowData) {
      // Populate from existing workflow data
      await this.populateFromExistingData()
    }
  },
  watch: {
    workflowSteps: {
      handler(newSteps) {
        if (this.selectedTemplateId && newSteps.length > 0 && !this.isPopulating) {
          this.emitWorkflowData()
        }
      },
      deep: true
    },
    selectedTemplateId(newId) {
      if (newId && !this.isPopulating) {
        this.emitWorkflowData()
      }
    },
    existingWorkflowData: {
      handler(newData, oldData) {
        // Only populate if data actually changed and we're not already populating
        if (newData && !this.isPopulating && JSON.stringify(newData) !== JSON.stringify(oldData)) {
          this.debouncedPopulateFromExistingData()
        }
      },
      deep: true,
      immediate: false
    }
  },
  methods: {
    // Fetch available workflow templates
    async fetchTemplates() {
      try {
        this.loadingTemplates = true
        // Map entity type to template type (server side filter)

        // const templateTypeMap = {
        //   LOAN: 'LOAN_LEVEL',
        //   DISBURSEMENT: 'DISBURSEMENT_LEVEL',
        //   EMI: 'EMI_LEVEL'
        // }

        // const params = {
        //   type: templateTypeMap[this.entityType]
        // }

        // if (this.loanType) {
        //   params.loanType = this.loanType
        // }

        const response = await this.$axios.get('/workforce/collection/workflow-templates')
        const allTemplates = response.data.templates || []

        const templateTypeMap = {
          LOAN: 'LOAN_LEVEL',
          DISBURSEMENT: 'DISBURSEMENT_LEVEL',
          EMI: 'EMI_LEVEL'
        }

        const targetType = templateTypeMap[this.entityType]

        // Filter templates client-side(remove when server side fix)
        this.availableTemplates = allTemplates.filter((template) => {
          if (template.type !== targetType) {
            return false
          }
          if (this.loanType && template.loanType && template.loanType !== this.loanType) {
            return false
          }

          return true
        })

        this.loadingTemplates = false
      } catch (error) {
        console.error('Error fetching workflow templates:', error)
        this.$toast.error('Failed to load workflow templates')
        this.loadingTemplates = false
      }
    },

    async fetchWorkflowInstance() {
      try {
        const response = await this.$axios.get(
          `/workforce/collection/workflow-instances/${this.workflowInstanceId}`
        )
        this.workflowInstance = response.data.workflowInstance

        if (this.workflowInstance) {
          this.selectedTemplateId = this.workflowInstance.templateId
          this.workflowSteps = this.workflowInstance.steps.map((step) => ({
            ...step,
            dueDate: step.dueDate ? step.dueDate.substr(0, 10) : null
          }))

          // Initialize UI state
          this.workflowSteps.forEach((_, index) => {
            this.initializeStepUI(index)
          })

          // Expand first panel
          if (this.workflowSteps.length > 0) {
            // this.expandedPanel = [0]
          }
        }
      } catch (error) {
        console.error('Error fetching workflow instance:', error)
        this.$toast.error('Failed to load workflow data')
      }
    },

    // Handle template selection change
    async handleTemplateChange() {
      if (!this.selectedTemplateId) {
        this.workflowSteps = []
        return
      }

      try {
        const response = await this.$axios.get(
          `/workforce/collection/workflow-templates/${this.selectedTemplateId}`
        )
        const template = response.data.data
        if (!template || !template.steps) {
          this.$toast.error('Invalid template structure')
          return
        }

        // Transform template steps to workflow instance steps
        this.workflowSteps = template.steps.map((templateStep) => {
          return {
            stepId: templateStep.stepId,
            name: templateStep.name,
            status: 'PENDING',
            isRequired: templateStep.isRequired || false,
            requirements: (templateStep.requirements || []).map((req) => ({
              name: req.name,
              isRequired: req.isRequired || false,
              isDocument: req.isDocument || false,
              completed: false,
              documentUrl: null,
              file: null // For file upload UI
            })),
            remarks: ''
          }
        })

        // Initialize UI state for each step
        this.workflowSteps.forEach((_, index) => {
          this.$set(this.showRemarks, index, false)
        })

        // Expand first panel
        if (this.workflowSteps.length > 0) {
          // this.expandedPanel = [0]
        }
      } catch (error) {
        console.error('Error applying workflow template:', error)
        this.$toast.error('Failed to apply workflow template')
      }
    },

    // Toggle remarks visibility
    toggleRemarks(index) {
      this.$set(this.showRemarks, index, !this.showRemarks[index])
    },

    // Handle file upload for document requirements
    async handleFileUpload(stepIndex, reqIndex, file) {
      if (!file) return

      try {
        // Create form data for file upload
        const formData = new FormData()
        formData.append('file', file)

        // Upload file to server
        const response = await this.$axios.post('/workforce/collection/upload-document', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        // Update requirement with document URL
        if (response.data && response.data.url) {
          this.$set(
            this.workflowSteps[stepIndex].requirements[reqIndex],
            'documentUrl',
            response.data.url
          )
          this.$set(this.workflowSteps[stepIndex].requirements[reqIndex], 'completed', true)

          this.$toast.success('Document uploaded successfully')

          // Check if step should be completed
          this.checkStepCompletion(stepIndex)
        }
      } catch (error) {
        console.error('Error uploading document:', error)
        this.$toast.error('Failed to upload document')
      }
    },

    // Preview document
    previewDocument(url) {
      if (!url) return
      window.open(url, '_blank')
    },

    // Handle requirement completion change
    handleRequirementChange(stepIndex, reqIndex) {
      this.checkStepCompletion(stepIndex)
    },

    // Check if all requirements in a step are completed
    checkStepCompletion(stepIndex) {
      const step = this.workflowSteps[stepIndex]
      if (!step.requirements || step.requirements.length === 0) {
        // If no requirements, step can be marked as completed
        this.$set(step, 'status', 'COMPLETED')
        return
      }

      // Check if all required requirements are completed
      const requiredRequirements = step.requirements.filter((req) => req.isRequired)
      const allRequiredCompleted = requiredRequirements.every((req) => req.completed)

      // Check if all requirements (required + optional) are completed
      const allCompleted = step.requirements.every((req) => req.completed)

      if (allCompleted) {
        this.$set(step, 'status', 'COMPLETED')
      } else if (allRequiredCompleted && step.status === 'PENDING') {
        this.$set(step, 'status', 'IN_PROGRESS')
      } else if (!allRequiredCompleted && step.status !== 'PENDING') {
        this.$set(step, 'status', 'PENDING')
      }
    },

    // Get color for step status
    getStatusColor(status) {
      switch (status) {
        case 'PENDING':
          return 'grey'
        case 'IN_PROGRESS':
          return 'blue'
        case 'COMPLETED':
          return 'success'
        case 'REJECTED':
          return 'error'
        case 'SKIPPED':
          return 'orange'
        default:
          return 'grey'
      }
    },

    // Format status for display
    formatStatus(status) {
      if (!status) return ''

      return status
        .split('_')
        .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
        .join(' ')
    },
    emitWorkflowData() {
      // Don't emit if we're still loading or don't have a template selected
      if (this.loadingTemplates || !this.selectedTemplateId) {
        return
      }

      // Find the selected template to get additional properties
      const selectedTemplate = this.availableTemplates.find(
        (template) => template._id === this.selectedTemplateId
      )

      // Prepare workflow data
      const workflowData = {
        templateId: this.selectedTemplateId,
        entityType: this.entityType,
        steps: this.workflowSteps.map((step) => {
          // Remove UI-specific properties
          const stepData = {
            stepId: step.stepId,
            name: step.name,
            status: step.status,
            isRequired: step.isRequired,
            remarks: step.remarks || ''
          }

          // Process requirements
          if (step.requirements && step.requirements.length > 0) {
            stepData.requirements = step.requirements.map((req) => ({
              name: req.name,
              isRequired: req.isRequired,
              isDocument: req.isDocument,
              completed: req.completed,
              documentUrl: req.documentUrl || null
            }))
          }

          return stepData
        })
      }

      // Add EMI frequency if this is an EMI workflow and template has it
      if (this.entityType === 'EMI' && selectedTemplate?.emiFrequency) {
        workflowData.emiFrequency = selectedTemplate.emiFrequency
      }
      if (
        this.entityType === 'EMI' &&
        selectedTemplate?.lateFeeValue &&
        selectedTemplate.lateFeeType
      ) {
        workflowData.lateFeeType = selectedTemplate.lateFeeType
        workflowData.lateFeeValue = selectedTemplate.lateFeeValue
      }

      // Emit the workflow data to parent
      this.$emit('workflow-saved', {
        workflowData
      })
    },
    // Add this new method to populate from existing data
    async populateFromExistingData() {
      if (!this.existingWorkflowData || this.isPopulating) return

      try {
        this.isPopulating = true

        // Set the template ID
        this.selectedTemplateId = this.existingWorkflowData.templateId

        // Wait for templates to load before continuing
        if (this.loadingTemplates) {
          await new Promise((resolve) => {
            const checkTemplates = setInterval(() => {
              if (!this.loadingTemplates) {
                clearInterval(checkTemplates)
                resolve()
              }
            }, 100)
          })
        }

        // If we have steps data, populate them
        if (this.existingWorkflowData.steps && this.existingWorkflowData.steps.length > 0) {
          this.workflowSteps = this.existingWorkflowData.steps.map((step) => ({
            ...step
          }))

          // Initialize UI state for each step
          this.workflowSteps.forEach((_, index) => {
            this.$set(this.showRemarks, index, false)
          })

          if (this.workflowSteps.length > 0) {
            // this.expandedPanel = [0]
          }
        } else if (this.selectedTemplateId) {
          await this.handleTemplateChange()
        }
      } finally {
        this.isPopulating = false
      }
    }
  }
}
</script>

<style scoped>
.workflow-panels {
  margin-top: 16px;
}

.workflow-step-panel {
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.workflow-step-panel:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-completed {
  border-color: #4caf50;
  background-color: #f8fff8;
}

.workflow-panel-header {
  background-color: #f5f5f5;
  padding: 12px 16px;
}

.step-completed .workflow-panel-header {
  background-color: #e8f5e8;
}

.workflow-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.step-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.step-icon {
  font-size: 20px;
}

.step-details {
  display: flex;
  /* flex-direction: column; */
  align-items: flex-start;
}

.step-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

/* .required-badge {
  font-size: 10px;
  color: #ff5722;
  font-weight: 500;
  margin-top: 2px;
} */

.step-actions {
  display: flex;
  align-items: center;
}

.remarks-section {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.requirements-section {
  padding: 8px 0;
}

.requirement-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fafafa;
}

.requirement-item:last-child {
  margin-bottom: 0;
}

.requirement-content {
  width: 100%;
}

.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.requirement-info {
  display: flex;
  flex: 1;
}

.requirement-checkbox {
  margin: 0;
  padding: 0;
}

.requirement-checkbox :deep(.v-input__slot) {
  margin-bottom: 0;
}

.requirement-checkbox :deep(.v-messages) {
  display: none;
}

.requirement-badges {
  display: flex;
  gap: 4px;
  margin-top: 4px;
  margin-left: 32px;
}

.document-upload {
  max-width: 300px;
}

.no-steps-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #757575;
  text-align: center;
  /* border: 1px dashed #e0e0e0; */
}

/* Dark theme support */
:deep(.theme--dark) .workflow-step-panel {
  border-color: #444;
  background-color: #2a2a2a;
}

:deep(.theme--dark) .step-completed {
  border-color: #4caf50;
  background-color: #1e2e1e;
}

:deep(.theme--dark) .workflow-panel-header {
  background-color: #2d2d2d;
}

:deep(.theme--dark) .step-completed .workflow-panel-header {
  background-color: #2a3a2a;
}

:deep(.theme--dark) .step-name {
  color: #e0e0e0;
}

:deep(.theme--dark) .requirement-item {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .remarks-section {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .no-steps-message {
  background-color: #2d2d2d;
  border-color: #444;
  color: #aaa;
}
</style>
