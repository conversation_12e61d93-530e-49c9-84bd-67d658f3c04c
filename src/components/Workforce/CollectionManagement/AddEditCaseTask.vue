<template>
  <v-card class="w-[43rem]" flat>
    <div class="flex justify-between p-4">
      <h4 class="mb-0 text-xl">
        {{
          editTask
            ? `Edit ${tableMapping.visitTable?.displayName || 'Visit'}`
            : `Add ${tableMapping.visitTable?.displayName || 'Visit'}`
        }}
      </h4>
      <v-icon @click="cancel">mdi-close</v-icon>
    </div>
    <div class="p-4">
      <v-form ref="form" v-model="valid" lazy-validation>
        <v-text-field
          v-model="caseId"
          :label="configLang.caseNo.displayName || 'Case No'"
          disabled
          dense
          outlined
          hide-details
          class="mb-4"
        ></v-text-field>
        <v-text-field
          v-model="taskTitle"
          :label="configLang.taskTitle?.displayName || 'Task Title'"
          :rules="[(v) => !!v || `${configLang.taskTitle?.displayName} is required`]"
          dense
          outlined
          hide-details
          class="mb-4"
        ></v-text-field>
        <v-text-field
          v-model="taskDescription"
          :label="configLang.taskDescription.displayName || 'Task Description'"
          v-show="configLang.taskDescription?.visible"
          dense
          outlined
          hide-details
          class="mb-4"
        ></v-text-field>
        <v-autocomplete
          v-model="assignedTo"
          :label="configLang.assignedTo.displayName || 'Assigned To'"
          :items="employees"
          :item-text="getFullName"
          :search-input.sync="searchUser"
          item-value="_id"
          @update:search-input="debounceUser"
          hide-no-data
          v-show="configLang.assignedTo?.visibility"
          dense
          outlined
          hide-details
          class="mb-4"
        >
        </v-autocomplete>
        <v-select
          :items="statusOptions"
          v-model="taskStatus"
          :label="`${tableMapping.visitTable?.displayName || 'Visit'} Status`"
          item-text="text"
          item-value="value"
          dense
          outlined
          hide-details
          class="mb-4"
        ></v-select>
        <v-text-field
          v-model="taskStartDateTime"
          type="datetime-local"
          label="Due Date Time"
          dense
          outlined
          hide-details
          class="mb-4"
        ></v-text-field>
        <!-- <v-text-field
          v-model="taskEndDateTime"
          type="datetime-local"
          label="End Date Time"
          dense
          outlined
          hide-details
          class="mb-4"
        ></v-text-field> -->
        <div class="flex items-center justify-end gap-4">
          <v-btn outlined @click="cancel"> Cancel </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            @click="handleClick"
            class="white--text"
          >
            Save
          </v-btn>
        </div>
      </v-form>
    </div>
  </v-card>
</template>
<script>
import { debounce } from '@/utils/common'
export default {
  name: 'AddEditCaseTask',
  data() {
    return {
      userData: this.$storage.getUniversal('user'),
      valid: true,
      taskTitle: '',
      taskDescription: '',
      caseId: null,
      employees: [],
      searchUser: '',
      assignedTo: null,
      taskStatus: 'PENDING',
      taskStartDateTime: null,
      taskEndDateTime: null,
      statusOptions: [
        { text: 'Pending', value: 'PENDING' },
        { text: 'In-Progress', value: 'IN_PROGRESS' },
        { text: 'Completed', value: 'COMPLETED' }
      ]
    }
  },
  props: {
    editCase: Object,
    editTask: Object,
    caseConfig: Object
  },
  watch: {
    editCase: {
      handler(newVal) {
        this.editCase = newVal
        this.taskStartDateTime = this.$dayjs().format('YYYY-MM-DDTHH:mm')
        this.taskEndDateTime = this.$dayjs().endOf('day').format('YYYY-MM-DDTHH:mm')
        this.populate()
      },
      deep: true
    }
  },
  computed: {
    configLang() {
      return this.$props.caseConfig.fieldMappings
    },
    tableMapping() {
      return this.$props.caseConfig?.tableTabMapping || {}
    }
  },
  methods: {
    async addCaseTask() {
      try {
        const payload = {
          caseType: this.caseConfig?.name,
          taskTitle: this.taskTitle,
          taskDescription: this.taskDescription || undefined,
          case: this.editCase._id,
          assignedTo: this.assignedTo || undefined,
          taskStatus: this.taskStatus || undefined,
          taskStartDateTime: this.taskStartDateTime || undefined,
          taskEndDateTime: this.taskEndDateTime || undefined,
          meetingDetails: {}
        }
        const response = await this.$axios.post('/workforce/collection/case/task', payload)
        this.$emit('newTaskAdded', response?.data?.task)
        this.cancel()
      } catch (error) {
        this.$toast.error(error.response?.data?.message)
        console.log(error)
      }
    },
    async updateCaseTask() {
      try {
        const payload = {
          caseType: this.caseConfig?.name,
          taskTitle: this.taskTitle,
          taskDescription: this.taskDescription,
          taskStatus: this.taskStatus,
          assignedTo: this.assignedTo
        }
        const response = await this.$axios.put(
          `/workforce/collection/case/task/${this.editTask._id}`,
          payload
        )
        this.$emit('updateTask', response.data?.task)
        this.cancel()
      } catch (error) {
        this.$toast.error(error.response?.data?.message)
        console.log(error)
      }
    },
    handleClick() {
      const isValid = this.$refs.form.validate()
      if (!isValid) {
        this.$toast.error('Please fill the required fields')
        return
      }
      if (this.editTask) {
        this.updateCaseTask()
      } else {
        this.addCaseTask()
      }
    },
    getFullName(employee) {
      return (
        `${employee.firstName || ''} ${employee.lastName || ''}` +
        (employee.wfmRole ? ` (${employee.wfmRole.role})` : '')
      )
    },
    debounceUser: debounce(async function (text) {
      if (!text || this.assignedTo) return true
      this.fetchUsers()
    }, 1000),
    async fetchUsers() {
      try {
        const params = {}
        if (this.searchUser) {
          params.search = this.searchUser
        }
        if (this.userData?.branch && !this.userData?.role?.includes('WFM_ADMIN')) {
          params.branch = this.userData?.branch
        }
        const response = await this.$axios.get('/workforce/v2/users', {
          params
        })
        this.employees = response.data?.users
      } catch (error) {
        console.log(error)
      }
    },
    cancel() {
      this.$emit('caseTaskAdded')
      if (!this.editTask) {
        this.$refs.form.reset()
      }
    },
    populate() {
      if (this.editCase) {
        this.caseId = this.editCase?.caseNo
      }
      if (this.editTask) {
        this.taskTitle = this.editTask?.taskTitle
        this.taskDescription = this.editTask?.taskDescription
        this.caseId = this.editTask?.case?.caseNo
        this.assignedTo = this.editTask?.assignedTo?._id
        this.taskStatus = this.editTask?.taskStatus
        this.taskStartDateTime = this.$dayjs(this.editTask?.taskStartDateTime).format(
          'YYYY-MM-DDTHH:mm'
        )
        this.taskEndDateTime = this.editTask?.taskStatus
        this.taskEndDateTime = this.$dayjs(this.editTask?.taskEndDateTime).format(
          'YYYY-MM-DDTHH:mm'
        )
      }
      if (this.editTask?.assignedTo) {
        this.searchUser = this.editTask?.assignedTo?.firstName
        this.fetchUsers()
      }
    }
  },
  async mounted() {
    this.populate()
    this.taskStartDateTime = this.$dayjs().format('YYYY-MM-DDTHH:mm')
    this.taskEndDateTime = this.$dayjs().endOf('day').format('YYYY-MM-DDTHH:mm')
  }
}
</script>
<style></style>
