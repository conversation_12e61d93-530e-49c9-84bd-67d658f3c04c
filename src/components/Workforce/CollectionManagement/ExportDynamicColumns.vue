<template>
  <div>
    <v-dialog v-model="showExportDialog" max-width="83rem" persistent>
      <v-card class="p-3">
        <div class="flex justify-between items-center btnClass border-b">
          <h6 class="text-xl mb-0">Manage Export Columns</h6>
          <v-btn icon @click="cancelExportDialog" color="black">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
        <v-data-table
          :headers="exportTableHeader"
          hide-default-footer
          :items="[]"
          class="no-data-table sortHandle"
          fixed-header
          :key="anIncreasingNumber"
          v-sortable-table="{ onEnd: sortTheHeadersAndUpdateTheKey }"
        >
          <template v-slot:no-data>
            <div class="text-center pa-4 flex items-center">
              <v-icon size="28">mdi-information</v-icon>
              <p class="mb-0 ml-2">Drag to rearrange headers.</p>
            </div>
          </template>
        </v-data-table>
        <v-divider></v-divider>
        <v-card-text class="mt-4">
          <div class="flex justify-end mb-2">
            <v-btn small color="primary" @click="selectAllFields" :disabled="!exportKeys?.length">
              Select All
            </v-btn>
          </div>
          <v-select
            v-model="selectedKeys"
            :items="exportKeys"
            item-text="text"
            item-value="value"
            label="Fields to Export"
            multiple
            small-chips
            outlined
            clearable
          ></v-select>
        </v-card-text>
        <v-card-actions class="flex justify-center mb-2">
          <v-spacer></v-spacer>
          <v-btn outlined color="grey" class="rounded-md mr-4 px-6" @click="cancelExportDialog">
            <span class="text-black dark-text-color">Cancel</span>
          </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text rounded-md px-6"
            @click="exportData"
            :loading="exportLoading"
            :disabled="exportLoading || !selectedKeys?.length"
          >
            Export
            <v-icon right dark>mdi-download</v-icon>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import Sortable from 'sortablejs'

function watchClass(targetNode, classToWatch) {
  let lastClassState = targetNode.classList.contains(classToWatch)
  const observer = new MutationObserver((mutationsList) => {
    for (let mutation of mutationsList) {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const currentClassState = mutation.target.classList.contains(classToWatch)
        if (lastClassState !== currentClassState) {
          lastClassState = currentClassState
          if (!currentClassState) {
            mutation.target.classList.add('sortHandle')
          }
        }
      }
    }
  })
  observer.observe(targetNode, { attributes: true })
}
export default {
  data() {
    return {
      userOrg: this.$storage.getUniversal('user'),
      anIncreasingNumber: null,
      selectedKeys: []
    }
  },
  props: {
    showExportDialog: Boolean,
    exportKeys: Array,
    exportLoading: Boolean,
    caseType: String
  },
  directives: {
    'sortable-table': {
      inserted: (el, binding) => {
        el.querySelectorAll('th').forEach((draggableEl) => {
          watchClass(draggableEl, 'sortHandle')
          draggableEl.classList.add('sortHandle')
        })
        Sortable.create(
          el.querySelector('tr'),
          binding.value ? { ...binding.value, handle: '.sortHandle' } : {}
        )
      }
    }
  },
  watch: {
    selectedKeys: {
      handler(newHeaders, oldHeaders) {
        if (newHeaders !== oldHeaders) {
          this.sortTheHeadersAndUpdateTheKey(newHeaders)
        }
      },
      deep: true
    },
    caseType: {
      handler(newVal) {
        if (newVal) {
          this.updateKeys(newVal)
        }
      }
    }
  },
  computed: {
    exportTableHeader() {
      const headers = this.selectedKeys.map((el) => {
        return { text: el, sortable: false, width: '120px' }
      })
      return headers
    }
  },
  methods: {
    sortTheHeadersAndUpdateTheKey(evt) {
      let headersTmp = [...this.exportTableHeader]
      const oldIndex = evt.oldIndex
      const newIndex = evt.newIndex
      if (newIndex >= headersTmp.length) {
        let k = newIndex - headersTmp.length + 1
        while (k--) {
          headersTmp.push(undefined)
        }
      }

      headersTmp.splice(newIndex, 0, headersTmp.splice(oldIndex, 1)[0])
      headersTmp = headersTmp.filter((el) => el !== undefined)

      const newKeys = headersTmp.map((key) => key?.text)
      this.selectedKeys.splice(0, this.selectedKeys.length, ...newKeys)
      this.anIncreasingNumber += 1
    },
    cancelExportDialog() {
      this.$emit('closeExportDialog')
    },
    async exportData() {
      this.$emit('exportData', this.selectedKeys)
    },
    updateKeys(caseType) {
      const storageKeyName = 'exportColumnSelectedKey'
      const loggedInUserId = this.userOrg?._id
      const storageData = JSON.parse(localStorage.getItem(storageKeyName)) || {}

      if (storageData[loggedInUserId]) {
        const caseEntry = storageData[loggedInUserId].find((entry) => entry.caseType === caseType)

        if (caseEntry) {
          this.selectedKeys = [...caseEntry.selectedKeys]
          return
        }
      }

      this.selectedKeys = []
    },
    selectAllFields() {
      this.selectedKeys = this.exportKeys.map((key) => key.value)
      this.anIncreasingNumber += 1
    }
  },
  mounted() {
    this.updateKeys(this.caseType)
  }
}
</script>
<style></style>
