<template>
  <div class="bg-gray-100 dark-bg-default pb-8">
    <!-- Header with ActiveTabComponent and Close Button -->
    <div class="sticky top-0 z-50 bg-[#f3f4f6] dark-bg-custom">
      <div class="flex justify-between items-center px-5 py-2">
        <div class="flex-1">
          <ActiveTabComponent :tabs="mainTabs" :defaultTab="activeTab" @tab-changed="setActiveTab">
            <!-- Tab buttons slot with status chip -->
            <template #tab-buttons>
              <div class="flex items-center space-x-4">
                <h4 class="text-md font-semibold" v-if="!isLoanCase">
                  Status -
                  <v-chip
                    label
                    :text-color="statusColor(selectedCase?.status)?.textColor"
                    :color="statusColor(selectedCase?.status)?.bgColor"
                  >
                    {{ selectedCase?.status }}
                  </v-chip>
                </h4>
                <v-btn plain @click="closeDialog">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </template>

            <!-- Main Case Details Tab Content -->
            <template #main>
              <div class="">
                <hr />

                <!-- Case Details Section -->
                <div>
                  <div class="flex items-center justify-between ml-5 py-2">
                    <h4 class="text-lg font-semibold">
                      {{ caseConfig.tableTabMapping.caseTable?.displayName || 'Case' }} Details
                    </h4>

                    <!-- Loan Status Indicators -->
                    <div v-if="isLoanCase" class="mr-5">
                      <v-chip
                        v-if="selectedCase.loanDetails?.currentStatus === 'Foreclosed'"
                        color="red"
                        text-color="white"
                        small
                        class="mr-2"
                      >
                        <v-icon left small>mdi-bank-off</v-icon>
                        Foreclosed
                      </v-chip>
                      <v-chip
                        v-else-if="selectedCase.loanDetails?.lastPrepaymentDate"
                        color="blue"
                        text-color="white"
                        small
                        class="mr-2"
                      >
                        <v-icon left small>mdi-cash-multiple</v-icon>
                        Recent Prepayment
                      </v-chip>
                      <v-chip v-else color="green" text-color="white" small class="mr-2">
                        <v-icon left small>mdi-bank-check</v-icon>
                        Active
                      </v-chip>
                    </div>
                  </div>

                  <!-- Loading indicator when refreshing case data -->
                  <div v-if="isRefreshingCase" class="mx-5 mb-4">
                    <v-alert type="info" dense text class="mb-0">
                      <v-progress-circular
                        indeterminate
                        size="16"
                        class="mr-2"
                      ></v-progress-circular>
                      Refreshing case data...
                    </v-alert>
                  </div>

                  <!-- Case Details - All borrowers in single view -->
                  <CaseDetailsData
                    :selectedCase="selectedCase"
                    :caseConfig="caseConfig"
                    :additionalColums="additionalColums"
                    :configLang="configLang"
                    :isLoanCase="isLoanCase"
                    :borrowerTabs="borrowerTabs"
                    :customerDetails="customerDetails"
                    :loadingCustomers="loadingCustomers"
                    @refresh-borrowers="handleRefreshBorrowers"
                  />
                </div>
                <!-- Loan Timeline Section (only for loan cases) -->
                <div v-if="isLoanCase" class="px-4 py-4 bg-white rounded-md">
                  <div class="flex justify-between items-center mb-2">
                    <div class="flex justify-start">
                      <h4 class="text-lg font-semibold mx-2 cursor-pointer">Timeline</h4>
                    </div>
                    <!-- <v-btn icon @click="toggleTimelineCollapse">
                          <v-icon>{{ isTimelineCollapsed ? 'mdi-arrow-down-box ' : 'mdi-arrow-up-box' }}</v-icon>
                        </v-btn> -->
                  </div>

                  <!-- Loan Timeline -->
                  <LoanTimeline :steps="loanWorkflowSteps" :isCollapsed="isTimelineCollapsed" />
                </div>

                <div
                  class="mx-10"
                  v-if="!isLoanCase && selectedCase.tasks && selectedCase.tasks.length"
                >
                  <!-- Visit Details Section -->
                  <h4 class="text-lg font-semibold ml-5 mt-4 py-2">
                    {{ caseConfig.tableTabMapping.visitTable?.displayName || 'Visit' }} Details
                  </h4>
                  <!-- Pending Visits -->
                  <div v-if="pendingVisits && pendingVisits.length">
                    <div class="flex gap-5 items-center">
                      <v-divider></v-divider>
                      <h5 class="text-md font-semibold ml-2 mb-2">
                        [ Upcoming
                        {{ caseConfig.tableTabMapping.visitTable?.displayName || 'Visit' }} ]
                      </h5>
                      <v-divider></v-divider>
                    </div>

                    <v-expansion-panels v-model="pendingPanel" flat>
                      <v-row class="text-center px-8 dark-bg-custom">
                        <v-col class="mt-4 font-semibold px-0">{{
                          caseConfig.tableTabMapping.visitTable?.displayName || 'Visit/Calls'
                        }}</v-col>
                        <v-col class="mt-4 font-semibold px-0"
                          >{{
                            caseConfig.tableTabMapping.visitTable?.displayName || 'Visit'
                          }}
                          Details</v-col
                        >
                        <v-col
                          class="mt-4 font-semibold px-0"
                          v-if="isCallEnabled('CALL_A_CUSTOMER')"
                          >Call Frequency</v-col
                        >
                        <v-col class="mt-4 font-semibold px-0">Outcome</v-col>
                        <v-col class="mt-4 font-semibold px-0">Next Scheduled</v-col>
                        <v-col class="mt-4 font-semibold px-0">Status</v-col>
                      </v-row>
                      <v-expansion-panel
                        v-for="(task, index) in pendingVisits"
                        :key="'pending-' + index"
                        class="mt-3"
                      >
                        <v-expansion-panel-header class="pt-0 pb-0 penel_header px-2">
                          <div class="flex justify-between align-center w-full">
                            <v-row class="text-center">
                              <v-col>
                                <p class="text-base font-semibold mt-5">
                                  {{ pendingVisits.length - index }}
                                </p>
                              </v-col>
                              <v-col>
                                <p class="mt-5 font-semibold text-blue-600">
                                  {{ selectedCase.caseNo }}
                                </p>
                              </v-col>
                              <v-col v-if="isCallEnabled('CALL_A_CUSTOMER')">
                                <p class="mt-5 font-semibold">
                                  {{ Array.isArray(task.callInfo) ? task.callInfo.length : 0 }}
                                </p>
                              </v-col>
                              <v-col>
                                <p class="mt-5 font-semibold">
                                  {{ task.meetingDetails?.meetingOutCome }}
                                </p>
                              </v-col>
                              <v-col>
                                <p class="mt-5 font-semibold">
                                  {{
                                    task.meetingDetails?.nextTaskScheduledOn
                                      ? $dayjs(task.meetingDetails?.nextTaskScheduledOn).format(
                                          'DD/MM/YYYY hh:mm A'
                                        )
                                      : $dayjs(task.taskStartDateTime).format('DD/MM/YYYY hh:mm A')
                                  }}
                                </p>
                              </v-col>
                              <v-col>
                                <v-chip
                                  small
                                  label
                                  class="mt-5 font-semibold"
                                  :text-color="statusColor(task.taskStatus).textColor"
                                  :color="statusColor(task.taskStatus).bgColor"
                                  >{{ task.taskStatus }}</v-chip
                                >
                              </v-col>
                            </v-row>
                          </div>
                        </v-expansion-panel-header>
                        <v-expansion-panel-content class="text-sm">
                          <CaseTaskData
                            :task="taskData(task)"
                            :key="task._id"
                            :type="type"
                            :caseConfig="caseConfig"
                            @deleteTask="handleDeleteTask"
                            :callTab="callTab"
                          />
                        </v-expansion-panel-content>
                      </v-expansion-panel>
                    </v-expansion-panels>
                  </div>

                  <no-data
                    v-else
                    title="No Upcoming Visits"
                    :subTitle="`Start by adding ${
                      caseConfig.tableTabMapping.visitTable?.displayName || 'Visit/Calls'
                    }`"
                    :btnText="`Add ${
                      caseConfig.tableTabMapping.visitTable?.displayName || 'Visit'
                    }`"
                    :btnAction="addVisit"
                    :callTab="callTab"
                  />

                  <!-- Other Visits -->
                  <div v-if="otherVisits && otherVisits.length">
                    <div class="flex gap-5 items-center my-6">
                      <v-divider></v-divider>
                      <h5 class="text-md font-semibold ml-2 mb-2">
                        [ Older
                        {{ caseConfig.tableTabMapping.visitTable?.displayName || 'Visits' }} ]
                      </h5>
                      <v-divider></v-divider>
                    </div>

                    <v-expansion-panels v-model="otherPanel" flat>
                      <v-row class="text-center px-8 dark-bg-custom">
                        <v-col class="mt-4 font-semibold px-0">{{
                          caseConfig.tableTabMapping.visitTable?.displayName || 'Visit/Calls'
                        }}</v-col>
                        <v-col class="mt-4 font-semibold px-0"
                          >{{
                            caseConfig.tableTabMapping.visitTable?.displayName || 'Visit'
                          }}
                          Details</v-col
                        >
                        <v-col
                          class="mt-4 font-semibold px-0"
                          v-if="isCallEnabled('CALL_A_CUSTOMER')"
                          >Call Frequency</v-col
                        >
                        <v-col class="mt-4 font-semibold px-0">Outcome</v-col>
                        <v-col class="mt-4 font-semibold px-0">Last Scheduled</v-col>
                        <v-col class="mt-4 font-semibold px-0">Status</v-col>
                      </v-row>
                      <v-expansion-panel
                        v-for="(task, index) in otherVisits"
                        :key="'other-' + index"
                        class="mt-3"
                      >
                        <v-expansion-panel-header class="pt-0 pb-0 penel_header px-2">
                          <div class="flex justify-between align-center w-full">
                            <v-row class="text-center">
                              <v-col>
                                <p class="text-base font-semibold mt-5">
                                  {{ otherVisits.length - index }}
                                </p>
                              </v-col>
                              <v-col>
                                <p class="mt-5 font-semibold text-blue-600">
                                  {{ selectedCase.caseNo }}
                                </p>
                              </v-col>
                              <v-col v-if="isCallEnabled('CALL_A_CUSTOMER')">
                                <p class="mt-5 font-semibold">
                                  {{ Array.isArray(task.callInfo) ? task.callInfo.length : 0 }}
                                </p>
                              </v-col>
                              <v-col>
                                <p class="mt-5 font-semibold">
                                  {{ task.meetingDetails?.meetingOutCome }}
                                </p>
                              </v-col>
                              <v-col>
                                <p class="mt-5 font-semibold">
                                  {{
                                    task.meetingDetails?.nextTaskScheduledOn
                                      ? $dayjs(task.meetingDetails?.nextTaskScheduledOn).format(
                                          'DD/MM/YYYY hh:mm A'
                                        )
                                      : ''
                                  }}
                                </p>
                              </v-col>
                              <v-col>
                                <v-chip
                                  small
                                  label
                                  class="mt-5 font-semibold"
                                  :text-color="statusColor(task.taskStatus).textColor"
                                  :color="statusColor(task.taskStatus).bgColor"
                                  >{{ task.taskStatus }}</v-chip
                                >
                              </v-col>
                            </v-row>
                          </div>
                        </v-expansion-panel-header>
                        <v-expansion-panel-content class="text-sm">
                          <CaseTaskData
                            :task="taskData(task)"
                            :key="task._id"
                            :type="type"
                            :caseConfig="caseConfig"
                            @deleteTask="handleDeleteTask"
                            :callTab="callTab"
                          />
                        </v-expansion-panel-content>
                      </v-expansion-panel>
                    </v-expansion-panels>
                  </div>

                  <no-data
                    v-else
                    title="Nothing to Display"
                    :subTitle="`Start by adding visits`"
                    :btnText="'Add Visit'"
                    :btnAction="addVisit"
                    :callTab="callTab"
                  />
                </div>
              </div>
            </template>

            <!-- Disbursement Timeline -->
            <template #disbursements>
              <div>
                <div v-if="disbursements.length > 0" class="space-y-6">
                  <!-- Loop through all disbursements -->
                  <div
                    v-for="(disbursement, index) in disbursements"
                    :key="disbursement._id || index"
                    class="bg-white p-6 border border-gray-200"
                  >
                    <!-- Disbursement Header -->
                    <div class="flex justify-between items-center mb-4">
                      <h3 class="text-lg font-semibold text-gray-800">
                        Disbursement #{{ index + 1 }}
                      </h3>
                    </div>

                    <!-- Disbursement Details Card -->
                    <div class="mb-6 p-4 bg-gray-50">
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <div class="text-sm text-gray-600 mb-1">Amount</div>
                          <div class="font-semibold text-lg text-green-600">
                            ₹{{ formatCurrency(disbursement.amount) }}
                          </div>
                        </div>
                        <div>
                          <div class="text-sm text-gray-600 mb-1">Date</div>
                          <div class="font-semibold">
                            {{ $dayjs(disbursement.date).format('DD/MM/YYYY') }}
                          </div>
                        </div>
                        <div>
                          <div class="text-sm text-gray-600 mb-1">Payment Mode</div>
                          <div class="font-semibold">
                            {{ disbursement.paymentMode || 'Not specified' }}
                          </div>
                        </div>
                        <div>
                          <div class="text-sm text-gray-600 mb-1">Reference No</div>
                          <div class="font-semibold">
                            {{ disbursement.referenceNo || '-' }}
                          </div>
                        </div>
                        <div>
                          <div class="text-sm text-gray-600 mb-1">Bank Account</div>
                          <div class="font-semibold">
                            {{ disbursement.bankAccount || '-' }}
                          </div>
                        </div>
                        <div>
                          <div class="text-sm text-gray-600 mb-1">Processed By</div>
                          <div class="font-semibold">
                            {{ disbursement.processedBy || '-' }}
                          </div>
                        </div>
                      </div>

                      <!-- Remarks Section -->
                      <div v-if="disbursement.remarks" class="mt-4 pt-4 border-t border-gray-200">
                        <div class="text-sm text-gray-600 mb-1">Remarks</div>
                        <div class="font-medium text-gray-800">
                          {{ disbursement.remarks }}
                        </div>
                      </div>
                    </div>

                    <!-- Disbursement Timeline -->
                    <div class="mt-4">
                      <h4 class="text-md font-semibold mb-3 text-gray-700">
                        Disbursement Workflow
                      </h4>
                      <LoanTimeline
                        :steps="getDisbursementWorkflowSteps(disbursement)"
                        :isCollapsed="false"
                      />
                    </div>
                  </div>
                </div>

                <!-- No Disbursements State -->
                <div v-else class="text-center py-12">
                  <v-icon size="64" color="grey lighten-1">mdi-cash-multiple</v-icon>
                  <div class="mt-4 text-lg text-gray-600">No disbursements available</div>
                  <div class="text-sm text-gray-500 mt-2">
                    Disbursements will appear here once they are processed
                  </div>
                </div>
              </div>
            </template>

            <!-- Pre-Payment Tab Content -->
            <template #foreclosure>
              <div v-if="isLoanCase && isPrepaymentAllowed">
                <LoanForeclosureView
                  :loanDetails="selectedCase.loanDetails"
                  :caseId="selectedCase._id"
                  :emiSchedule="selectedCase.loanDetails?.emiSchedule || []"
                  :borrowerTabs="borrowerTabs"
                  :selectedCase="selectedCase"
                  @foreclosure-processed="handleForeclosureProcessed"
                />
              </div>
            </template>

            <template #schedules>
              <div v-if="isLoanCase">
                <!-- Prepayment Not Allowed Alert -->
                <v-alert v-if="!isPrepaymentAllowed" type="info" dense text class="mb-4">
                  <v-icon small class="mr-2">mdi-information</v-icon>
                  Prepayment/Foreclosure is not enabled for this loan. Contact administrator to
                  enable prepayment option.
                </v-alert>

                <!-- Tab Navigation for Loan Details -->
                <v-tabs v-model="loanTab" v-if="borrowerTabs.length > 1">
                  <v-tab v-for="borrower in borrowerTabs" :key="borrower.id">
                    <div class="text-center text-sm">
                      {{ borrower.name }}
                      <div class="text-xs text-gray-600">
                        ({{ borrower.customerData.customerFullName }})
                      </div>
                    </div>
                  </v-tab>
                </v-tabs>

                <v-tabs-items v-model="loanTab">
                  <!-- EMI Schedule Tab for each borrower -->
                  <v-tab-item v-for="borrower in borrowerTabs" :key="borrower.id">
                    <LoanEmiScheduleView
                      :emiSchedule="getFilteredEmiSchedule(borrower.id)"
                      :borrowerId="borrower.id"
                      :borrowerName="borrower.name"
                      @update-emi="handleEmiUpdate"
                      @show-notification="showNotification"
                    />
                  </v-tab-item>
                </v-tabs-items>
              </div>
            </template>
          </ActiveTabComponent>
        </div>
      </div>
    </div>

    <!-- Add Visit Dialog -->
    <v-dialog v-model="openAddVisit" max-width="600">
      <AddEditCaseTask
        :caseConfig="caseConfig"
        :editCase="selectedCase"
        @newTaskAdded="handleAdded"
        @caseTaskAdded="cancelVisit"
      />
    </v-dialog>
  </div>
</template>
<script>
import NoData from '@/components/NoData.vue'
import CaseTaskData from './CaseTaskData'
import AddEditCaseTask from './AddEditCaseTask'
import CaseDetailsData from './CaseDetailsData'
import LoanTimeline from './LoanTimeline'
import LoanEmiScheduleView from './LoanEmiScheduleView.vue'
import LoanForeclosureView from './LoanForeclosureView.vue'
import ActiveTabComponent from '@/components/ActitiveTabcomponent.vue'
import {
  convertToTitleCase,
  getFullAddress,
  fullName,
  dayHourFormat,
  isModuleFeatureAllowed
} from '@/utils/common'
import { orderStatusColors } from '@/utils/workforce/statusColors'
export default {
  name: 'CaseDetails',
  data() {
    return {
      panel: 0,
      type: 'visitDetails',
      openAddVisit: false,
      pendingPanel: 0,
      otherPanel: 0,
      isTimelineCollapsed: false,
      activeTab: 'main',
      selectedDisbursementIndex: 0,
      loanTab: 0, // 0 for EMI Schedule, 1 for Foreclosure
      isRefreshingCase: false,
      customerDetails: {}, // Store fetched customer details by ID
      loadingCustomers: false
    }
  },
  components: {
    NoData,
    CaseTaskData,
    AddEditCaseTask,
    CaseDetailsData,
    LoanTimeline,
    LoanEmiScheduleView,
    LoanForeclosureView,
    ActiveTabComponent
  },
  props: {
    selectedCase: Object,
    caseConfig: Object,
    callTab: Boolean
  },
  watch: {
    selectedCase: {
      handler(newData, oldData) {
        if (newData) {
          this.activeTab = 'main'
          // Clear customer details when case changes and fetch new data
          if (!oldData || newData._id !== oldData._id) {
            this.customerDetails = {}
            if (this.isLoanCase) {
              this.fetchCustomerDetails()
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    mainTabs() {
      const tabs = [
        {
          key: 'main',
          label:
            `${this.caseConfig.tableTabMapping.caseTable?.displayName} Details` || 'Case Details',
          icon: 'mdi-file-document-outline',
          component: 'CaseDetailsContent'
        }
      ]
      if (this.isLoanCase) {
        tabs.push({
          key: 'disbursements',
          label: 'Disbursements',
          icon: 'mdi-bank-transfer',
          component: 'Disbursements'
        })
      }

      if (this.isPrepaymentAllowed) {
        tabs.push({
          key: 'foreclosure',
          label: 'Pre-Payment',
          icon: 'mdi-cash-fast',
          component: 'LoanForeclosureView'
        })
      }
      if (this.isLoanCase) {
        tabs.push({
          key: 'schedules',
          label: 'EMI Schedule',
          icon: 'mdi-calendar-clock',
          component: 'EmiSchedule'
        })
      }

      return tabs
    },
    isLoanCase() {
      return !!this.selectedCase?.loanDetails?._id
    },
    isPrepaymentAllowed() {
      return this.selectedCase?.loanDetails?.prepaymentAllowed === true
    },
    loanWorkflowSteps() {
      return this.selectedCase?.loanDetails?.loanWorkflow?.steps || []
    },
    disbursements() {
      return this.selectedCase?.loanDetails?.loanDisbursements || []
    },
    disbursementItems() {
      return this.disbursements.map((item, index) => ({
        text: `Disbursement #${index + 1} - ₹${item.amount} (${this.$dayjs(item.date).format(
          'DD/MM/YYYY'
        )})`,
        value: index
      }))
    },
    selectedDisbursement() {
      return this.disbursements[this.selectedDisbursementIndex] || null
    },
    disbursementWorkflowSteps() {
      if (this.selectedDisbursement?.workflow?.steps) {
        return this.selectedDisbursement.workflow.steps
      }
      return this.selectedDisbursement?.disbursementWorkflowSteps || []
    },
    borrowerTabs() {
      const tabs = []

      if (!this.selectedCase) return tabs

      // Add primary borrower
      if (this.selectedCase?.customer) {
        const primaryId = this.selectedCase.customer._id || this.selectedCase.customer.customerId
        tabs.push({
          id: primaryId,
          name: 'Primary Borrower',
          customerData: this.selectedCase.customer,
          share: this.getPrimaryBorrowerShare(),
          emiAmount: this.getEmiAmountForBorrower(primaryId)
        })
      }

      // Add co-applicants/co-borrowers with fetched customer data
      if (this.selectedCase?.loanDetails?.coApplicants?.length) {
        this.selectedCase.loanDetails.coApplicants.forEach((coApplicant, index) => {
          const customerId = coApplicant.customer
          const customerData = this.customerDetails[customerId] || {
            customerFullName: 'Loading...',
            _id: customerId
          }

          tabs.push({
            id: customerId,
            name: `Co-Borrower ${index + 1}`,
            customerData: customerData,
            share: coApplicant.share || 0,
            emiAmount: this.getEmiAmountForBorrower(customerId)
          })
        })
      }

      return tabs
    },
    isPreviewableDocument() {
      const previewableTypes = ['application/pdf', 'text/plain']
      return previewableTypes.includes(this.previewDocument?.type)
    },
    isImageDocument() {
      return this.previewDocument?.type?.startsWith('image/')
    },
    additionalColums() {
      if (this.selectedCase?.additionalColumns) {
        const addCol = this.$props.caseConfig?.additionalColumns
        const newColumns = Object.entries(this.selectedCase?.additionalColumns)?.map(
          ([key, obj]) => {
            const fieldType = addCol?.[key].fieldType
            return {
              key,
              value: obj,
              fieldType
            }
          }
        )
        return newColumns
      }
      return []
    },
    configLang() {
      return this.$props.caseConfig?.fieldMappings
    },
    pendingVisits() {
      return this.selectedCase.tasks?.filter((task) => {
        const taskDate = task?.taskStartDateTime ? this.$dayjs(task.taskStartDateTime) : null
        return (
          taskDate &&
          (taskDate.isSame(this.$dayjs(), 'day') || taskDate.isAfter(this.$dayjs())) &&
          task.taskStatus !== 'COMPLETED'
        )
      })
    },
    otherVisits() {
      return this.selectedCase.tasks?.filter((task) => {
        const taskDate = task?.taskStartDateTime ? this.$dayjs(task.taskStartDateTime) : null
        return (
          (taskDate && taskDate.isBefore(this.$dayjs(), 'day')) || task.taskStatus == 'COMPLETED'
        )
      })
    }
  },
  methods: {
    async handleEmiUpdate(val) {
      try {
        const response = await this.$axios.put(
          `/workforce/collection/emi-details/${val.emiId}`,
          val.updatedEmi
        )

        // Update the EMI in the UI with the response data
        if (response.data.data && this.selectedCase?.loanDetails?.emiSchedule) {
          // Find the index of the updated EMI in the schedule
          const emiIndex = this.selectedCase.loanDetails.emiSchedule.findIndex(
            (emi) => emi._id === val.emiId
          )

          // If found, update it with the response data
          if (emiIndex !== -1) {
            this.$set(this.selectedCase.loanDetails.emiSchedule, emiIndex, response.data.data)

            this.$toast.success('EMI details updated successfully')
          }
        }

        return response.data.data
      } catch (error) {
        const errorMessage = error?.response?.data?.message || 'Failed to update EMI details'
        this.$toast.error(errorMessage)
        console.error('Error updating EMI:', error)
      }
    },
    showNotification() {},
    formatCurrency(value) {
      return new Intl.NumberFormat('en-IN').format(value)
    },
    getStepStatusClass(status) {
      switch (status) {
        case 'Completed':
          return 'step-completed'
        case 'Rejected':
          return 'step-rejected'
        case 'Pending':
        default:
          return 'step-pending'
      }
    },
    getConnectorClass(currentStatus, nextStatus) {
      if (currentStatus === 'Completed' && nextStatus === 'Completed') {
        return 'connector-completed'
      } else if (currentStatus === 'Rejected' || nextStatus === 'Rejected') {
        return 'connector-rejected'
      } else {
        return ''
      }
    },
    hasDocuments(step) {
      return step.documents && step.documents.length > 0
    },
    viewDocuments(step) {
      this.selectedStep = step
      this.documentDialog = true
    },
    getDocumentUrl(doc) {
      if (!doc) return ''

      // For dummy documents, return a placeholder image based on type
      if (doc.path && doc.path.startsWith('/dummy/')) {
        if (doc.type === 'application/pdf') {
          return 'https://via.placeholder.com/150x200/f5f5f5/666666?text=PDF'
        } else if (doc.type.startsWith('image/')) {
          return 'https://via.placeholder.com/150x200/e0f7fa/0288d1?text=Image'
        } else {
          return 'https://via.placeholder.com/150x200/f5f5f5/666666?text=File'
        }
      }

      // For real documents, use the actual path
      const baseUrl = process.env.VUE_APP_S3_BASE_URL || ''
      return baseUrl + (doc.path || doc.url || '')
    },
    openDocumentPreview(doc) {
      this.previewDocument = doc
      this.previewDialog = true
    },
    getDocumentIcon(doc) {
      if (!doc || !doc.type) return 'mdi-file-outline'

      if (doc.type.startsWith('image/')) {
        return 'mdi-file-image-outline'
      } else if (doc.type === 'application/pdf') {
        return 'mdi-file-pdf-outline'
      } else if (doc.type.includes('word')) {
        return 'mdi-file-word-outline'
      } else if (doc.type.includes('excel') || doc.type.includes('spreadsheet')) {
        return 'mdi-file-excel-outline'
      } else {
        return 'mdi-file-document-outline'
      }
    },
    getDocumentIconColor(doc) {
      if (!doc || !doc.type) return 'grey'

      if (doc.type.startsWith('image/')) {
        return 'blue'
      } else if (doc.type === 'application/pdf') {
        return 'red'
      } else if (doc.type.includes('word')) {
        return 'blue darken-3'
      } else if (doc.type.includes('excel') || doc.type.includes('spreadsheet')) {
        return 'green'
      } else {
        return 'grey darken-1'
      }
    },
    downloadDocument(doc) {
      if (!doc) return

      const url = this.getDocumentUrl(doc)
      const link = document.createElement('a')
      link.href = url
      link.download = doc.name || 'document'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    isCallEnabled(feature) {
      return isModuleFeatureAllowed('ADDONS', feature)
    },
    taskData(task) {
      const newTask = {
        ...task,
        case: {
          caseNo: this.selectedCase.caseNo,
          customerName: this.selectedCase.customer?.customerFullName,
          caseStatus: this.selectedCase?.userDefinedCaseStatus
        }
      }
      return newTask
    },
    calculateTAT(item) {
      const caseStart = item.caseStartDate
      const caseEnd = item.caseEndDate
      if (caseEnd) {
        return dayHourFormat(caseStart, caseEnd)
      }
      return '-'
    },
    async handleDeleteTask(item) {
      try {
        const response = await this.$axios.delete(`/workforce/collection/case/task/${item._id}`)
        const index = this.selectedCase?.tasks.findIndex((task) => task._id === item._id)
        if (index !== -1) {
          this.selectedCase?.tasks.splice(index, 1)
        }
        const successMessage = response?.data?.message || 'Case task deleted successfully.'
        this.$toast.success(successMessage)
      } catch (error) {
        const errorMessage =
          error?.response?.data?.message || 'An error occurred while deleting the case task.'
        this.$toast.error(errorMessage)
        console.error(error)
      }
    },
    convertToTitle(str) {
      return convertToTitleCase(str)
    },
    statusColor(status) {
      const colorInfo = orderStatusColors[status]
      return colorInfo
    },
    customerAddress(item) {
      return getFullAddress(item)
    },
    customerName(item) {
      if (item) {
        return fullName(item)
      }
    },
    addVisit() {
      this.openAddVisit = true
    },
    cancelVisit() {
      this.openAddVisit = false
    },
    handleAdded(task) {
      this.openAddVisit = false
      this.selectedCase.tasks.push(task)
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    setActiveTab(tab) {
      this.activeTab = tab
    },
    toggleTimelineCollapse() {
      this.isTimelineCollapsed = !this.isTimelineCollapsed
    },
    getFilteredEmiSchedule(borrowerId) {
      if (!this.selectedCase?.loanDetails?.emiSchedule) return []

      return this.selectedCase.loanDetails.emiSchedule.filter((emi) => {
        // Check if EMI belongs to this borrower
        return emi.customer === borrowerId || emi.customerId === borrowerId
      })
    },
    getSelectedCaseForBorrower(borrower) {
      // Create a modified case object with the borrower as the primary customer
      return {
        ...this.selectedCase,
        customer: borrower.customerData
      }
    },
    async handleForeclosureProcessed(data) {
      // Handle foreclosure completion
      const isFullForeclosure = data.loanClosureType === 'foreclosure'
      const successMessage = isFullForeclosure
        ? 'Loan foreclosure completed successfully'
        : 'Partial payment processed successfully'

      this.$toast.success(successMessage)

      // Update case status if needed
      if (data.caseStatus) {
        this.selectedCase.status = data.caseStatus
        this.selectedCase.userDefinedCaseStatus = data.caseStatus
      }

      // Update loan details with new information
      if (this.selectedCase.loanDetails) {
        // Update loan status based on closure type
        if (isFullForeclosure) {
          this.selectedCase.loanDetails.currentStatus = 'Foreclosed'
          this.selectedCase.loanDetails.foreclosureDate = data.foreclosureDate
          this.selectedCase.loanDetails.foreclosureAmount = data.foreclosureAmount
        } else {
          // For partial payment, update relevant fields
          if (data.tenureReduction > 0) {
            this.selectedCase.loanDetails.tenureReduced = data.tenureReduction
          }
          if (data.amountReduction > 0) {
            this.selectedCase.loanDetails.lastPrepaymentAmount = data.amountReduction
            this.selectedCase.loanDetails.lastPrepaymentDate = data.foreclosureDate
          }
        }
      }

      // Refresh case data from server to get updated information
      this.isRefreshingCase = true
      try {
        const response = await this.$axios.get(
          `/workforce/collection/case/${this.selectedCase._id}`
        )
        if (response.data && response.data.data) {
          // Update the selected case with fresh data from server
          Object.assign(this.selectedCase, response.data.data)
        }
      } catch (error) {
        console.error('Error refreshing case data:', error)
        this.$toast.warning('Could not refresh case data. Please refresh the page.')
      } finally {
        this.isRefreshingCase = false
      }

      // Switch to main tab (Case Details) to show updated information after a short delay
      setTimeout(() => {
        this.activeTab = 'main'
        this.loanTab = 0
        this.$toast.info('Switched to Case Details to show updated information')
      }, 1500)

      // Emit event to parent component to refresh case data
      this.$emit('case-updated', this.selectedCase)
    },
    async fetchCustomerDetails() {
      if (!this.isLoanCase || !this.selectedCase) return

      this.loadingCustomers = true
      try {
        const customerIds = []

        // Collect all customer IDs that need to be fetched
        if (this.selectedCase?.loanDetails?.coApplicants?.length) {
          this.selectedCase.loanDetails.coApplicants.forEach((coApp) => {
            if (coApp.customer && !this.customerDetails[coApp.customer]) {
              customerIds.push(coApp.customer)
            }
          })
        }

        if (this.selectedCase?.loanDetails?.guarantors?.length) {
          this.selectedCase.loanDetails.guarantors.forEach((guarantor) => {
            if (guarantor.customer && !this.customerDetails[guarantor.customer]) {
              customerIds.push(guarantor.customer)
            }
          })
        }

        // Remove duplicates
        const uniqueCustomerIds = [...new Set(customerIds)]

        if (uniqueCustomerIds.length === 0) {
          this.loadingCustomers = false
          return
        }

        // Fetch all customers in parallel using the single API endpoint
        const customerPromises = uniqueCustomerIds.map(async (customerId) => {
          try {
            const response = await this.$axios.get(`/workforce/customer/${customerId}`)
            return { customerId, data: response.data.customer }
          } catch (error) {
            console.error(`Error fetching customer ${customerId}:`, error)
            return { customerId, data: null }
          }
        })

        const customerResults = await Promise.all(customerPromises)

        // Store the fetched customer data
        const newCustomerDetails = { ...this.customerDetails }
        customerResults.forEach(({ customerId, data }) => {
          if (data) {
            newCustomerDetails[customerId] = data
          }
        })

        this.customerDetails = newCustomerDetails
      } catch (error) {
        console.error('Error fetching customer details:', error)
      } finally {
        this.loadingCustomers = false
      }
    },
    getPrimaryBorrowerShare() {
      if (!this.selectedCase?.loanDetails?.coApplicants?.length) return 100

      const totalCoApplicantShare = this.selectedCase.loanDetails.coApplicants.reduce(
        (sum, coApp) => sum + (coApp.share || 0),
        0
      )
      return Math.max(0, 100 - totalCoApplicantShare)
    },
    getEmiAmountForBorrower(borrowerId) {
      if (!this.selectedCase?.loanDetails?.emiSchedule?.length) return 0

      const borrowerEmis = this.selectedCase.loanDetails.emiSchedule.filter(
        (emi) => emi.customer === borrowerId || emi.customerId === borrowerId
      )

      return borrowerEmis[0]?.emiAmount
    },
    async handleRefreshBorrowers(caseData) {
      // Handle the refresh borrowers event from CaseDetailsData
      if (this.isLoanCase && caseData) {
        await this.fetchCustomerDetails()
      }
    },

    getDisbursementWorkflowSteps(disbursement) {
      // Return workflow steps for individual disbursement
      if (disbursement?.workflow?.steps) {
        return disbursement.workflow.steps
      }
      if (disbursement?.disbursementWorkflowSteps) {
        return disbursement.disbursementWorkflowSteps
      }

      // Default workflow steps if none exist
      return [
        {
          name: 'Disbursement Initiated',
          status: 'Completed',
          date: disbursement.date,
          description: 'Disbursement request has been initiated'
        },
        {
          name: 'Amount Verification',
          status: disbursement.amount ? 'Completed' : 'Pending',
          description: 'Disbursement amount verified'
        },
        {
          name: 'Bank Processing',
          status: disbursement.referenceNo ? 'Completed' : 'Pending',
          description: 'Processing through banking system'
        },
        {
          name: 'Disbursement Complete',
          status: disbursement.status === 'Completed' ? 'Completed' : 'Pending',
          date: disbursement.completedDate,
          description: 'Funds successfully disbursed to borrower'
        }
      ]
    }
  },
  async mounted() {
    // Fetch customer details when component mounts
    if (this.isLoanCase && this.selectedCase) {
      await this.fetchCustomerDetails()
    }
  }
}
</script>
<style scoped>
.penel_header {
  border-bottom: 1px solid rgb(192, 189, 189);
}
</style>
