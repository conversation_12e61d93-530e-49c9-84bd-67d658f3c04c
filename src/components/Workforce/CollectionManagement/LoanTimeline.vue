<template>
  <div>
    <div class="workflow-timeline">
      <div class="timeline-container" :class="{ 'multi-step': steps?.length > 5 }">
        <div
          v-for="(step, index) in steps"
          :key="index"
          class="timeline-step"
          :class="[getStepStatusClass(step.status), { collapsed: isCollapsed }]"
        >
          <div class="timeline-step-circle">
            <v-icon v-if="step.status === 'COMPLETED'" size="16" color="white">mdi-check</v-icon>
            <v-icon v-else-if="step.status === 'REJECTED'" size="16" color="white"
              >mdi-close</v-icon
            >
            <span v-else>{{ index + 1 }}</span>
          </div>
          <div class="timeline-step-content" v-show="!isCollapsed">
            <div class="step-title">{{ step.name }}</div>
            <div class="step-date">{{ $dayjs(step.updatedAt).format('DD/MM/YYYY') }}</div>
            <v-chip small outlined label :color="getStepStatusColor(step.status)">
              {{ step.status }}
            </v-chip>
            <div v-if="step.remarks" class="step-remarks mt-1">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <span v-bind="attrs" v-on="on" class="remarks-truncate">{{ step.remarks }}</span>
                </template>
                <span>{{ step.remarks }}</span>
              </v-tooltip>
            </div>
            <!-- <div v-if="hasDocuments(step)" class="mt-2">
              <v-btn x-small icon @click="viewDocuments(step)">
                <v-icon small color="primary">mdi-file-document-outline</v-icon>
              </v-btn>
            </div> -->
          </div>
          <div v-if="isCollapsed" class="step-title-collapsed">{{ step.name }}</div>
          <div
            v-if="index < steps?.length - 1"
            class="timeline-connector"
            :class="[
              getConnectorClass(step.status, steps[index + 1]?.status),
              { 'connector-collapsed': isCollapsed }
            ]"
          ></div>
        </div>
      </div>
    </div>

    <!-- Document viewer dialog -->
    <v-dialog v-model="documentDialog" max-width="800">
      <v-card>
        <v-card-title class="text-md">
          Documents for {{ selectedStep?.name }}
          <v-spacer></v-spacer>
          <v-btn icon @click="documentDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <div v-if="selectedStep?.documents?.length" class="document-grid">
            <div v-for="(doc, i) in selectedStep.documents" :key="i" class="document-item">
              <div class="document-preview-card" @click="openDocumentPreview(doc)">
                <div class="document-icon">
                  <v-icon size="36" :color="getDocumentIconColor(doc)">{{
                    getDocumentIcon(doc)
                  }}</v-icon>
                </div>
                <div class="document-name">{{ doc.name || `Document ${i + 1}` }}</div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-4">No documents available for this step.</div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Document preview dialog -->
    <v-dialog v-model="previewDialog" fullscreen>
      <v-card>
        <v-toolbar dark color="primary">
          <v-btn icon dark @click="previewDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
          <v-toolbar-title>{{ previewDocument?.name || 'Document Preview' }}</v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn icon @click="downloadDocument(previewDocument)">
            <v-icon>mdi-download</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="pa-0 document-preview-container">
          <iframe
            v-if="isPreviewableDocument"
            :src="getDocumentUrl(previewDocument)"
            class="document-preview"
          ></iframe>
          <v-img
            v-else-if="isImageDocument"
            :src="getDocumentUrl(previewDocument)"
            contain
            max-height="90vh"
            class="document-preview"
          ></v-img>
          <div v-else class="d-flex justify-center align-center flex-column pa-8">
            <v-icon size="64" color="grey lighten-1">mdi-file-document-outline</v-icon>
            <div class="mt-4 text-center">
              This document type cannot be previewed.<br />
              <v-btn color="primary" class="mt-4" @click="downloadDocument(previewDocument)">
                Download Document
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'LoanTimeline',
  props: {
    steps: {
      type: Array,
      required: true
    },
    isCollapsed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      documentDialog: false,
      previewDialog: false,
      selectedStep: null,
      previewDocument: null
    }
  },
  computed: {
    isPreviewableDocument() {
      const previewableTypes = ['application/pdf', 'text/plain']
      return previewableTypes.includes(this.previewDocument?.type)
    },
    isImageDocument() {
      return this.previewDocument?.type?.startsWith('image/')
    }
  },
  methods: {
    getStepStatusClass(status) {
      switch (status) {
        case 'COMPLETED':
          return 'completed'
        case 'REJECTED':
          return 'rejected'
        case 'IN_PROGRESS':
          return 'in-progress'
        case 'SKIPPED':
          return 'skipped'
        case 'PENDING':
        default:
          return 'pending'
      }
    },
    getStepStatusColor(status) {
      switch (status) {
        case 'COMPLETED':
          return 'success'
        case 'REJECTED':
          return 'error'
        case 'IN_PROGRESS':
          return 'info'
        case 'SKIPPED':
          return 'warning'
        case 'PENDING':
        default:
          return 'grey'
      }
    },
    getConnectorClass(currentStatus, nextStatus) {
      if (currentStatus === 'COMPLETED') {
        if (nextStatus === 'COMPLETED') return 'connector-completed'
        if (nextStatus === 'IN_PROGRESS') return 'connector-in-progress'
      }
      return 'connector-pending'
    },
    hasDocuments(step) {
      return step.documents && step.documents.length > 0
    },
    viewDocuments(step) {
      this.selectedStep = step
      this.documentDialog = true
    },
    openDocumentPreview(doc) {
      this.previewDocument = doc
      this.previewDialog = true
    },
    downloadDocument(doc) {
      if (!doc) return

      const url = this.getDocumentUrl(doc)
      const link = document.createElement('a')
      link.href = url
      link.download = doc.name || 'document'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    getDocumentUrl(doc) {
      if (!doc) return ''

      // For dummy documents, return a placeholder image based on type
      if (doc.path && doc.path.startsWith('/dummy/')) {
        if (doc.type === 'application/pdf') {
          return 'https://via.placeholder.com/150x200/f5f5f5/666666?text=PDF'
        } else if (doc.type.startsWith('image/')) {
          return 'https://via.placeholder.com/150x200/e0f7fa/0288d1?text=Image'
        } else {
          return 'https://via.placeholder.com/150x200/f5f5f5/666666?text=File'
        }
      }

      // For real documents, use the actual path
      const baseUrl = process.env.VUE_APP_S3_BASE_URL || ''
      return baseUrl + (doc.path || doc.url || '')
    },
    getDocumentIcon(doc) {
      if (!doc || !doc.type) return 'mdi-file-outline'

      if (doc.type.startsWith('image/')) return 'mdi-file-image-outline'
      if (doc.type === 'application/pdf') return 'mdi-file-pdf-outline'
      if (doc.type.includes('word')) return 'mdi-file-word-outline'
      if (doc.type.includes('excel') || doc.type.includes('sheet')) return 'mdi-file-excel-outline'

      return 'mdi-file-document-outline'
    },
    getDocumentIconColor(doc) {
      if (!doc || !doc.type) return 'grey'

      if (doc.type.startsWith('image/')) return 'teal'
      if (doc.type === 'application/pdf') return 'red'
      if (doc.type.includes('word')) return 'blue'
      if (doc.type.includes('excel') || doc.type.includes('sheet')) return 'green'

      return 'grey'
    },
    formatStatusText(status) {
      if (!status) return ''

      return status
        .split('_')
        .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
        .join(' ')
    }
  }
}
</script>

<style scoped>
/* Timeline styles */
.workflow-timeline {
  overflow-x: auto;
  scrollbar-width: thin;
}

.workflow-timeline::-webkit-scrollbar {
  height: 6px;
}

.workflow-timeline::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.timeline-container {
  display: flex;
  min-width: 100%;
  padding: 20px 10px;
  transition: all 0.3s ease;
}

.timeline-container.multi-step {
  min-width: 150%;
}

.timeline-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 150px;
}

/* Collapsed timeline step styles */
.timeline-step.collapsed {
  flex-direction: row;
  align-items: center;
  height: auto;
  padding: 8px 0;
  position: relative;
}

.timeline-step-circle {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #e0e0e0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.timeline-step.completed .timeline-step-circle {
  background-color: #4caf50;
}

.timeline-step.rejected .timeline-step-circle {
  background-color: #f44336;
}

.timeline-step.in-progress .timeline-step-circle {
  background-color: #2196f3;
}

.timeline-step.skipped .timeline-step-circle {
  background-color: #ff9800;
}

.timeline-step-content {
  margin-top: 10px;
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  /* background-color: rgb(242, 241, 241);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12); */
  width: 90%;
  /* min-height: 160px; */
}

.step-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.step-title-collapsed {
  margin-left: 10px;
  font-weight: 500;
  font-size: 0.9rem;
  background: white;
  z-index: 10;
}

.step-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.step-remarks {
  font-size: 12px;
  font-style: italic;
  color: #666;
}

.remarks-truncate {
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
}

.timeline-connector {
  position: absolute;
  top: 14px;
  right: calc(-50% + 14px);
  width: 100%;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

/* Add arrow to connector */
.timeline-connector::after {
  content: '';
  position: absolute;
  right: 0;
  top: -4px;
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 8px solid #e0e0e0;
}

.connector-completed {
  background-color: #4caf50;
}

.connector-completed::after {
  border-left-color: #4caf50;
}

.connector-in-progress {
  background: linear-gradient(to right, #4caf50 50%, #e0e0e0 50%);
}

.connector-in-progress::after {
  border-left-color: #e0e0e0;
}

.timeline-step.collapsed .timeline-step-circle {
  width: 20px;
  height: 20px;
  font-size: 12px;
  flex-shrink: 0;
}

/* Adjust connector for collapsed view */
.timeline-step.collapsed .timeline-connector {
  top: 18px;
  height: 2px;
  width: calc(100% - 40px);
  left: 40px;
  transform: translateY(0);
}

.timeline-step.collapsed .timeline-connector::after {
  top: -4px;
  right: 0;
}

/* Document viewer styles */
.document-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
  padding: 10px;
}

.document-item {
  display: flex;
  flex-direction: column;
}

.document-preview-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.document-preview-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.document-icon {
  margin-bottom: 8px;
}

.document-name {
  text-align: center;
  font-size: 14px;
  word-break: break-word;
}

.document-preview-container {
  height: calc(100vh - 64px);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.document-preview {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
