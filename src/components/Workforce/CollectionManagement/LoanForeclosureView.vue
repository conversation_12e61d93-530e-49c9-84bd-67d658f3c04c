<template>
  <div class="loan-foreclosure-view">
    <!-- Borrower Selection Tabs at Top -->
    <v-card v-if="borrowerTabs.length > 1" flat solo>
      <v-tabs v-model="activeBorrowerTab" class="mb-2" color="primary">
        <v-tab v-for="borrower in borrowerTabs" :key="borrower.id" class="font-medium">
          <div class="text-center text-sm">
            {{ borrower.name }}
            <div class="text-xs text-gray-600">({{ borrower.customerData.customerFullName }})</div>
          </div>
        </v-tab>
      </v-tabs>
    </v-card>

    <div class="flex gap-4">
      <!-- Foreclosure Summary -->
      <v-card class="mb-6 w-1/2" outlined>
        <v-card-title class="text-base pb-2">
          <v-icon left color="success">mdi-cash-multiple</v-icon>
          Account Summary
        </v-card-title>
        <v-card-text>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="summary-item">
              <p class="text-sm text-gray-600 mb-2">Loan Amount</p>
              <p class="text-md font-semibold text-blue-600 mb-2">
                ₹{{ formatCurrency(getBorrowerLoanAmount()) }}
              </p>
            </div>
            <div class="summary-item">
              <p class="text-sm text-gray-600 mb-2">Principal Paid</p>
              <p class="text-md font-semibold text-green-600 mb-2">
                ₹{{ formatCurrency(calculatePrincipalPaid()) }}
              </p>
            </div>
            <div class="summary-item">
              <p class="text-sm text-gray-600 mb-2">Principal Balance</p>
              <p class="text-md font-semibold text-orange-600 mb-2">
                ₹{{ formatCurrency(foreclosureData.principalOutstanding) }}
              </p>
            </div>
            <div class="summary-item">
              <p class="text-sm text-gray-600 mb-2">Interest Paid</p>
              <p class="text-md font-semibold text-purple-600 mb-2">
                ₹{{ formatCurrency(calculateInterestPaid()) }}
              </p>
            </div>
          </div>
        </v-card-text>
      </v-card>

      <!-- EMI Status Overview -->
      <v-card class="mb-6 w-1/2" outlined>
        <v-card-title class="pb-2 text-base">
          <v-icon left color="info">mdi-chart-pie</v-icon>
          EMI(s) Overview
        </v-card-title>
        <v-card-text>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div class="status-item">
              <p class="text-sm text-gray-600 mb-2">Total</p>
              <p class="text-md font-semibold mb-2">{{ emiStatusData.totalEmis }}</p>
            </div>
            <div class="status-item">
              <p class="text-sm text-gray-600 mb-2">Paid</p>
              <p class="text-md font-semibold text-green-600 mb-2">{{ emiStatusData.paidEmis }}</p>
            </div>
            <div class="status-item">
              <p class="text-sm text-gray-600 mb-2">Pending</p>
              <p class="text-md font-semibold text-red-600 mb-2">{{ emiStatusData.pendingEmis }}</p>
            </div>
            <div class="status-item">
              <p class="text-sm text-gray-600 mb-2">Overdue</p>
              <p class="text-md font-semibold text-orange-600 mb-2">
                {{ emiStatusData.overdueEmis }}
              </p>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </div>
    <!-- Detailed Breakdown -->

    <!-- <v-card class="mb-6" outlined>
      <v-card-title class="pb-2">
        <v-icon left color="warning">mdi-file-document-edit</v-icon>
        EMI Details
      </v-card-title>
      <v-card-text>
        <v-expansion-panels v-model="expandedPanel" multiple class="border" flat>
          <v-expansion-panel>
            <v-expansion-panel-header>
              <div class="flex items-center">
                <v-icon class="mr-2" color="purple">mdi-format-list-numbered</v-icon>
                EMI(s)
                <span v-if="currentBorrower" class="ml-2 text-sm text-gray-600">
                  ({{ currentBorrowerEmiSchedule.length }})
                </span>
              </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <div class="overflow-x-auto">
                <table class="w-full text-sm">
                  <thead>
                    <tr class="border-b">
                      <th class="text-left p-2">EMI #</th>
                      <th class="text-left p-2">Due Date</th>
                      <th class="text-left p-2">EMI Amount</th>
                      <th class="text-left p-2">Principal</th>
                      <th class="text-left p-2">Interest</th>
                      <th class="text-left p-2">Status</th>
                      <th class="text-left p-2">Outstanding</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(emi, index) in currentBorrowerEmiSchedule"
                      :key="emi._id"
                      class="border-b hover:bg-gray-50"
                    >
                      <td class="p-2 font-medium">{{ index + 1 }}</td>
                      <td class="p-2">{{ formatDate(emi.dueDate) }}</td>
                      <td class="p-2">₹{{ formatCurrency(emi.emiAmount) }}</td>
                      <td class="p-2">₹{{ formatCurrency(emi.principalAmount) }}</td>
                      <td class="p-2">₹{{ formatCurrency(emi.interestAmount) }}</td>
                      <td class="p-2">
                        <v-chip
                          x-small
                          :color="getStatusColor(emi.emiStatus).bgColor"
                          :text-color="getStatusColor(emi.emiStatus).textColor"
                        >
                          {{ emi.emiStatus }}
                        </v-chip>
                      </td>
                      <td class="p-2">₹{{ formatCurrency(emi.outstandingAmount) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card-text>
    </v-card> -->
    <!-- Payment Configuration -->
    <v-card class="mb-6" outlined>
      <v-card-title class="pb-2 text-base">
        <v-icon left color="primary">mdi-calculator</v-icon>
        Payment
      </v-card-title>
      <v-card-text>
        <div v-if="loanClosureType === 'prepayment'" class="mt-4">
          <!-- Full Payment Checkbox -->
          <div class="mb-4 p-3 bg-blue-50 rounded border border-blue-200">
            <v-checkbox v-model="isFullPayment" @change="handleFullPaymentToggle" class="mb-2">
              <template v-slot:label>
                <div>
                  <span class="font-medium">Pay full amount</span>
                  <div class="text-sm text-gray-600">
                    Total Due: ₹{{ formatCurrency(getTotalAmountDue()) }}
                    <span class="text-xs">(Principal + Accrued Interest)</span>
                  </div>
                </div>
              </template>
            </v-checkbox>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <v-text-field
              v-model.number="partialAmount"
              label="Payment Amount"
              prefix="₹"
              type="number"
              outlined
              dense
              :min="0"
              :disabled="isFullPayment"
              :rules="paymentAmountRules"
              :error-messages="getPaymentAmountErrors()"
              @input="validateAndCalculate"
              :hint="`Maximum: ₹${formatCurrency(maxPaymentAmount)}`"
              persistent-hint
            ></v-text-field>
            <v-text-field
              v-model.number="foreclosureData.accruedInterest"
              label="Accrued Interest"
              prefix="₹"
              type="number"
              outlined
              dense
              :min="0"
              :rules="accruedInterestRules"
              :error-messages="getAccruedInterestErrors()"
              @input="onAccruedInterestChange"
              persistent-hint
            ></v-text-field>
          </div>

          <!-- Prepayment Application Options -->
          <div class="mt-4">
            <h4 class="font-semibold mb-3">How should the prepayment be applied?</h4>
            <v-radio-group
              v-model="prepaymentApplication"
              @change="calculateForeclosure"
              :disabled="isFullPayment"
            >
              <v-radio
                label="Reduce Loan Tenure (Keep emi amount same)"
                value="reduce_tenure"
              ></v-radio>
              <v-radio label="Reduce Emi Amount (Keep tenure same)" value="reduce_emi"></v-radio>
              <v-radio label="Hybrid (Balanced emi and tenure)" value="custom_tenure"></v-radio>
            </v-radio-group>

            <!-- Custom Tenure Slider -->
            <div
              v-if="prepaymentApplication === 'custom_tenure'"
              class="mt-4 p-4 bg-blue-50 rounded border"
            >
              <h5 class="font-medium mb-3">Hybrid Payment Configuration</h5>
              <p class="text-sm text-gray-600 mb-3">
                Make a prepayment of ₹{{ formatCurrency(partialAmount || 0) }} and select how many
                EMIs you want to complete in total. The slider shows EMI positions from 1 to
                {{ emiStatusData.totalEmis }} (all EMIs). Moving right reduces remaining EMIs and
                increases monthly EMI amount.
              </p>
              <div class="mb-4">
                <v-slider
                  v-model="customTenure"
                  :min="minTenure"
                  :max="maxTenure"
                  :step="1"
                  thumb-label="always"
                  track-color="grey lighten-2"
                  color="primary"
                  @input="onTenureSliderChange"
                  :disabled="isFullPayment"
                >
                  <template v-slot:thumb-label="{ value }"> {{ value }}m </template>
                </v-slider>
                <div class="flex justify-between text-sm text-gray-600 mt-2">
                  <span>Min: {{ minTenure }} month</span>
                  <span>Selected: {{ customTenure }} months</span>
                  <span>Max: {{ maxTenure }} months (All EMIs)</span>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white p-3 rounded border">
                  <p class="text-sm text-gray-600">New EMI Amount</p>
                  <p class="text-lg font-semibold text-blue-600">
                    ₹{{ formatCurrency(customEmiAmount) }}
                  </p>
                  <p class="text-xs text-gray-500">
                    {{ customEmiAmount > currentEmiAmount ? 'Increased' : 'Decreased' }} by ₹{{
                      formatCurrency(Math.abs(customEmiAmount - currentEmiAmount))
                    }}
                  </p>
                </div>
                <div class="bg-white p-3 rounded border">
                  <p class="text-sm text-gray-600">Tenure Change</p>
                  <p class="text-lg font-semibold" :class="tenureChangeColor">
                    {{ tenureChangeText }}
                  </p>
                  <p class="text-xs text-gray-500">
                    EMI Position: {{ customTenure }} of {{ emiStatusData.totalEmis }}
                    <br />
                    Remaining EMIs: {{ Math.max(0, emiStatusData.totalEmis - customTenure) }}
                  </p>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <ChargesDiscountsSection
                  type="penalties"
                  :items="penaltyCharges"
                  :format-currency="formatCurrency"
                  :amount-rules="amountRules"
                  @charges-changed="onPenaltyChargesChanged"
                />
              </div>
              <div>
                <ChargesDiscountsSection
                  type="discounts"
                  :items="discountWaivers"
                  :format-currency="formatCurrency"
                  :amount-rules="amountRules"
                  :max-discount="getTotalAmountBeforeDiscounts()"
                  @discounts-changed="onDiscountWaiversChanged"
                />
              </div>
            </div>

            <!-- Payment Summary & Impact -->
            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Payment Details -->
              <div>
                <h4 class="font-semibold mb-3">Payment Details</h4>
                <!-- <v-select
                  v-model="closureDetails.closureReason"
                  :items="closureReasons"
                  label="Payment Reason"
                  outlined
                  dense
                  class="mb-3"
                ></v-select> -->
                <v-textarea
                  v-model="closureDetails.remarks"
                  label="Payment Remarks"
                  outlined
                  dense
                  rows="2"
                ></v-textarea>
                <v-menu
                  v-model="dateMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="closureDetails.closureDate"
                      label="Payment Date"
                      prepend-inner-icon="mdi-calendar"
                      readonly
                      outlined
                      dense
                      hide-details
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="closureDetails.closureDate"
                    @input="dateMenu = false"
                    :max="todayDate"
                  ></v-date-picker>
                </v-menu>
                <div class="space-y-3 p-4 payment-summary rounded my-6" v-if="prepaymentImpact">
                  <div class="space-y-2 text-sm" v-if="prepaymentImpact">
                    <h5 class="font-semibold mb-2 text-blue-800 pt-1">Prepayment Impact:</h5>
                    <div
                      v-if="prepaymentApplication === 'reduce_tenure'"
                      class="flex justify-between"
                    >
                      <span>Tenure Reduction:</span>
                      <span class="font-medium text-blue-600">
                        {{ prepaymentImpact.tenureReduction }} months
                      </span>
                    </div>
                    <div v-if="prepaymentApplication === 'reduce_emi'" class="flex justify-between">
                      <span>New EMI Amount:</span>
                      <span class="font-medium text-blue-600">
                        ₹{{ formatCurrency(prepaymentImpact.newEmiAmount) }}
                      </span>
                    </div>
                    <div
                      v-if="prepaymentApplication === 'custom_tenure'"
                      class="flex justify-between"
                    >
                      <span>Custom Tenure:</span>
                      <span class="font-medium text-blue-600">
                        {{ prepaymentImpact.customTenure }} months
                      </span>
                    </div>
                    <div
                      v-if="prepaymentApplication === 'custom_tenure'"
                      class="flex justify-between"
                    >
                      <span>New EMI Amount:</span>
                      <span class="font-medium text-blue-600">
                        ₹{{ formatCurrency(prepaymentImpact.newEmiAmount) }}
                      </span>
                    </div>
                    <div class="flex justify-between">
                      <span>Interest Savings:</span>
                      <span class="font-medium text-green-600">
                        ₹{{ formatCurrency(prepaymentImpact.interestSavings) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Combined Payment Summary & Impact -->
              <div>
                <h4 class="font-semibold mb-3">Payment Summary</h4>
                <div class="space-y-3 p-4 payment-summary rounded mb-4">
                  <div class="flex justify-between">
                    <span>Principal Outstanding:</span>
                    <span class="font-medium">
                      ₹{{ formatCurrency(foreclosureData.principalOutstanding) }}</span
                    >
                  </div>

                  <div class="flex justify-between">
                    <span>Accrued Interest:</span>
                    <span class="font-medium"
                      >+ ₹{{ formatCurrency(foreclosureData.accruedInterest) }}</span
                    >
                  </div>
                  <div class="flex justify-between border-t pt-2">
                    <span></span>
                    <span class="font-semibold">₹{{ formatCurrency(getTotalAmountDue()) }}</span>
                  </div>
                  <div class="flex justify-between" v-if="!isFullPayment">
                    <span>Prepayment Principal:</span>
                    <span class="font-medium text-green-600">
                      - ₹{{ formatCurrency(prepaymentImpact?.prepaymentAmount || 0) }}
                    </span>
                  </div>
                  <div class="flex justify-between border-t pt-2" v-if="!isFullPayment">
                    <span></span>
                    <span class="font-semibold"
                      >₹{{
                        formatCurrency(
                          getTotalAmountDue() - (prepaymentImpact?.prepaymentAmount || 0)
                        )
                      }}</span
                    >
                  </div>
                  <div class="flex justify-between">
                    <span>Total Penalties:</span>
                    <span class="font-medium text-red-600"
                      >+ ₹{{ formatCurrency(getTotalPenaltyCharges()) }}</span
                    >
                  </div>
                  <div class="flex justify-between border-t pt-2">
                    <span></span>
                    <span class="font-semibold"
                      >₹{{
                        formatCurrency(
                          getTotalAmountDue() -
                            prepaymentImpact?.prepaymentAmount +
                            getTotalPenaltyCharges()
                        )
                      }}</span
                    >
                  </div>
                  <div class="flex justify-between">
                    <span>Total Discounts:</span>
                    <span class="font-medium text-green-600"
                      >- ₹{{ formatCurrency(foreclosureData.totalDiscounts) }}</span
                    >
                  </div>
                  <div class="flex justify-between border-t pt-2" v-if="isFullPayment">
                    <span class="text-lg font-bold">Full Amount Payable:</span>
                    <span class="font-medium">
                      ₹{{
                        formatCurrency(
                          foreclosureData.totalForeclosureAmount +
                            getTotalPenaltyCharges() -
                            foreclosureData.totalDiscounts
                        )
                      }}
                    </span>
                  </div>
                  <div class="flex justify-between border-t pt-2" v-else>
                    <span class="text-lg font-bold">Net Outstanding Principal:</span>
                    <span class="font-medium">
                      ₹{{
                        formatCurrency(
                          getTotalAmountDue() -
                            (prepaymentImpact?.prepaymentAmount || 0) +
                            getTotalPenaltyCharges() -
                            foreclosureData.totalDiscounts
                        )
                      }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-4">
      <v-btn outlined @click="resetCalculation">
        <v-icon left>mdi-refresh</v-icon>
        Reset
      </v-btn>
      <v-btn color="primary" @click="processForeclosure" :loading="processing">
        <v-icon left>mdi-check</v-icon>
        Continue
      </v-btn>
    </div>

    <!-- Confirmation Dialog -->
    <v-dialog v-model="confirmDialog" max-width="500" persistent>
      <v-card>
        <v-card-title class="headline"
          >Confirm
          {{ loanClosureType === 'foreclosure' ? 'Foreclosure' : 'Partial Payment' }}
        </v-card-title>
        <v-card-text>
          <p v-if="!processing">
            Are you sure you want to process this
            {{ loanClosureType === 'foreclosure' ? 'foreclosure' : 'partial payment' }}
            <span v-if="currentBorrower"
              >for <strong>{{ currentBorrower.name }}</strong></span
            >?
          </p>

          <!-- Processing Status -->
          <div v-if="processing" class="text-center py-4">
            <v-progress-circular
              indeterminate
              color="primary"
              size="40"
              class="mb-3"
            ></v-progress-circular>
            <p class="mb-2">
              Processing
              {{ loanClosureType === 'foreclosure' ? 'foreclosure' : 'partial payment' }}...
            </p>
            <small class="text-gray-600">Please wait, this may take a moment.</small>
          </div>

          <div v-if="!processing" class="mt-4 p-4 bg-gray-100 rounded">
            <div class="flex justify-between mb-2">
              <span class="font-semibold">Final Amount:</span>
              <span class="font-semibold text-green-600"
                >₹{{ formatCurrency(foreclosureData.totalForeclosureAmount) }}</span
              >
            </div>
            <div
              v-if="loanClosureType === 'foreclosure'"
              class="flex justify-between text-sm text-gray-600"
            >
              <span>EMIs to be marked as paid:</span>
              <span>{{ unpaidEmiCount }} EMIs</span>
            </div>
            <div
              v-else-if="prepaymentImpact && prepaymentImpact.tenureReduction > 0"
              class="flex justify-between text-sm text-gray-600"
            >
              <span>Tenure reduction:</span>
              <span>{{ prepaymentImpact.tenureReduction }} months</span>
            </div>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="confirmDialog = false" :disabled="processing">Cancel</v-btn>
          <v-btn
            color="primary"
            @click="confirmForeclosure"
            :loading="processing"
            :disabled="processing"
          >
            {{ processing ? 'Processing...' : 'Confirm' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import {
  getEmiStatusColor,
  calculateForeclosureAmount,
  validateForeclosureData,
  calculateTenure,
  calculateEMI
} from '@/utils/common'
import ChargesDiscountsSection from './ChargesDiscountsSection.vue'

export default {
  name: 'LoanForeclosureView',
  components: {
    ChargesDiscountsSection
  },
  props: {
    loanDetails: {
      type: Object,
      required: true
    },
    emiSchedule: {
      type: Array,
      required: true
    },
    caseId: String,
    borrowerTabs: {
      type: Array,
      default: () => []
    },
    selectedCase: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    caseId: {
      handler(newId) {
        if (newId) {
          this.calculateForeclosure()
        }
      },
      immediate: true
    },
    activeBorrowerTab: {
      handler() {
        this.manualAccruedInterest = false // Reset manual flag when switching borrowers
        this.initializeTenureSlider()
        this.calculateForeclosure()
      }
    },
    partialAmount: {
      handler() {
        if (this.prepaymentApplication === 'custom_tenure') {
          this.onTenureSliderChange()
        }
      }
    }
  },
  data() {
    return {
      loanClosureType: 'prepayment',
      partialAmount: 0,
      isFullPayment: false,
      prepaymentApplication: 'reduce_tenure', // Default option
      prepaymentImpact: null,
      expandedPanel: [],
      manualAccruedInterest: false, // Track if user manually changed accrued interest

      // Custom tenure slider properties
      customTenure: 0,
      customEmiAmount: 0,
      currentEmiAmount: 0,
      originalRemainingTenure: 0,
      minTenure: 1,
      maxTenure: 1, // Will be set to pending EMIs count
      sliderTotalRange: 60, // Total range for visual display
      dateMenu: false,
      confirmDialog: false,
      processing: false,
      activeBorrowerTab: 0, // For borrower tabs

      penaltyCharges: [{ chargeType: 'Prepayment Charges', amount: 0 }],

      discountWaivers: [{ discountType: 'Interest Waiver', amount: 0 }],

      closureDetails: {
        closureReason: '',
        remarks: '',
        closureDate: new Date().toISOString().substr(0, 10)
      },

      closureReasons: [
        'Full Payment',
        'Partial Settlement',
        'Legal Settlement',
        'Mutual Agreement',
        'Other'
      ],

      foreclosureData: {
        principalOutstanding: 0,
        principalPaid: 0,
        accruedInterest: 0,
        interestPaid: 0,
        penaltyAmount: 0,
        totalForeclosureAmount: 0,
        subtotal: 0,
        totalDiscounts: 0
      }
    }
  },

  computed: {
    todayDate() {
      return new Date().toISOString().substr(0, 10)
    },
    currentBorrowerEmiSchedule() {
      if (!this.borrowerTabs.length) return this.emiSchedule

      const currentBorrower = this.borrowerTabs[this.activeBorrowerTab]
      if (!currentBorrower) return []

      return this.emiSchedule.filter(
        (emi) => emi.customer === currentBorrower.id || emi.customerId === currentBorrower.id
      )
    },

    emiStatusData() {
      const schedule = this.currentBorrowerEmiSchedule
      const totalEmis = schedule.length
      const paidEmis = schedule.filter(
        (emi) => emi.emiStatus === 'Paid' || emi.emiStatus === 'Minimum Amount Paid'
      ).length
      const pendingEmis = schedule.filter(
        (emi) => emi.emiStatus === 'Pending' || emi.emiStatus === 'Due'
      ).length
      const overdueEmis = schedule.filter(
        (emi) => emi.emiStatus === 'Overdue' || emi.emiStatus === 'Late'
      ).length

      return {
        totalEmis,
        paidEmis,
        pendingEmis,
        overdueEmis
      }
    },

    unpaidEmiCount() {
      return this.currentBorrowerEmiSchedule.filter(
        (emi) => emi.emiStatus !== 'Paid' || emi.emiStatus !== 'Minimum Amount Paid'
      ).length
    },

    currentBorrower() {
      if (this.borrowerTabs.length === 0) return null
      return this.borrowerTabs[this.activeBorrowerTab] || this.borrowerTabs[0] || null
    },

    tenureChangeText() {
      const totalEmis = this.emiStatusData.totalEmis
      const currentRemainingEmis = this.originalRemainingTenure
      const selectedTenure = this.customTenure

      // Calculate how many EMIs will be reduced compared to current remaining
      const emisReduction = currentRemainingEmis - (totalEmis - selectedTenure)

      if (emisReduction === 0) return 'Same as Current'
      if (emisReduction > 0) return `${emisReduction} EMIs Reduced`
      return `${Math.abs(emisReduction)} EMIs Extended`
    },

    tenureChangeColor() {
      const totalEmis = this.emiStatusData.totalEmis
      const currentRemainingEmis = this.originalRemainingTenure
      const selectedTenure = this.customTenure

      const emisReduction = currentRemainingEmis - (totalEmis - selectedTenure)

      if (emisReduction === 0) return 'text-gray-600'
      if (emisReduction > 0) return 'text-green-600' // EMIs reduced
      return 'text-red-600' // EMIs extended
    },

    // Validation computed properties
    maxPaymentAmount() {
      return this.foreclosureData.principalOutstanding + this.foreclosureData.accruedInterest
    },

    paymentAmountRules() {
      return [
        (v) => v >= 0 || 'Payment amount cannot be negative',
        (v) =>
          v <= this.maxPaymentAmount ||
          `Payment amount cannot exceed ₹${this.formatCurrency(
            this.maxPaymentAmount
          )} (Principal + Accrued Interest)`,
        (v) => !isNaN(v) || 'Please enter a valid number'
      ]
    },

    accruedInterestRules() {
      return [
        (v) => v >= 0 || 'Accrued interest cannot be negative',
        (v) => !isNaN(v) || 'Please enter a valid number'
      ]
    },

    amountRules() {
      return [
        (v) => v >= 0 || 'Amount cannot be negative',
        (v) => !isNaN(v) || 'Please enter a valid number'
      ]
    }
  },

  mounted() {
    // Initialize with first borrower if multiple borrowers exist
    if (this.borrowerTabs.length > 0) {
      this.activeBorrowerTab = 0
    }
    this.initializeTenureSlider()
    this.calculateForeclosure()
    this.setDefaultPartialAmount()
  },

  methods: {
    onPenaltyChargesChanged(charges) {
      // Validate penalty charges before applying
      const validatedCharges = charges.map((charge) => ({
        ...charge,
        amount: Math.max(0, parseFloat(charge.amount) || 0)
      }))
      this.penaltyCharges = validatedCharges
      this.validateAndCalculate()
    },

    onDiscountWaiversChanged(discounts) {
      // Validate discount waivers before applying
      const maxDiscount = this.getTotalAmountBeforeDiscounts()

      let totalDiscounts = 0

      const validatedDiscounts = discounts.map((discount) => {
        const amount = Math.max(0, parseFloat(discount.amount) || 0)
        totalDiscounts += amount
        return {
          ...discount,
          amount: amount
        }
      })
      console.log(validatedDiscounts)

      // Ensure total discounts don't exceed available amount
      // if (totalDiscounts > maxDiscount) {
      //   this.$toast.warning(
      //     `Total discounts (₹${this.formatCurrency(
      //       totalDiscounts
      //     )}) cannot exceed available amount (₹${this.formatCurrency(maxDiscount)})`
      //   )
      //   return
      // }

      this.discountWaivers = validatedDiscounts
      this.validateAndCalculate()
    },

    getTotalPenaltyCharges() {
      return this.penaltyCharges.reduce((sum, charge) => sum + (parseFloat(charge.amount) || 0), 0)
    },

    getTotalDiscountWaivers() {
      return this.discountWaivers.reduce(
        (sum, discount) => sum + (parseFloat(discount.amount) || 0),
        0
      )
    },

    calculateLateFees() {
      let totalLateFees = 0
      this.currentBorrowerEmiSchedule.forEach((emi) => {
        if (emi.emiStatus === 'Overdue' || emi.emiStatus === 'Late') {
          // Calculate late fees based on overdue days
          const overdueDays = this.calculateOverdueDays(emi.dueDate)
          const lateFeeRate = this.loanDetails.lateFeeRate || 2 // 2% default
          totalLateFees += ((emi.emiAmount * lateFeeRate) / 100) * Math.ceil(overdueDays / 30)
        }
      })
      return totalLateFees
    },

    calculatePrepaymentCharges() {
      // Calculate prepayment charges based on borrower's outstanding principal
      const prepaymentRate = this.loanDetails.prepaymentCharges || 0
      const principalOutstanding = this.foreclosureData.principalOutstanding || 0

      if (prepaymentRate > 0 && principalOutstanding > 0) {
        return (prepaymentRate / 100) * principalOutstanding
      }
      return 0
    },

    calculateOverdueDays(dueDate) {
      const today = new Date()
      const due = new Date(dueDate)
      const diffTime = today - due
      return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))
    },

    // Handle manual accrued interest changes
    onAccruedInterestChange() {
      this.manualAccruedInterest = true
      this.validateAndCalculate()
    },

    // Validation methods
    validateAndCalculate() {
      if (this.validateInputs()) {
        this.calculateForeclosure()
      }
    },

    validateInputs() {
      let isValid = true
      const errors = []

      // Validate payment amount
      if (this.partialAmount < 0) {
        errors.push('Payment amount cannot be negative')
        isValid = false
      }
      if (this.partialAmount > this.maxPaymentAmount) {
        errors.push(`Payment amount cannot exceed ₹${this.formatCurrency(this.maxPaymentAmount)}`)
        isValid = false
      }

      // Validate accrued interest
      if (this.foreclosureData.accruedInterest < 0) {
        errors.push('Accrued interest cannot be negative')
        isValid = false
      }

      // Show validation errors
      if (errors.length > 0) {
        // errors.forEach((error) => this.$toast.error(error))
      }

      return isValid
    },

    getPaymentAmountErrors() {
      const errors = []
      if (this.partialAmount < 0) {
        errors.push('Payment amount cannot be negative')
      }
      if (this.partialAmount > this.maxPaymentAmount) {
        errors.push(`Cannot exceed ₹${this.formatCurrency(this.maxPaymentAmount)}`)
      }
      return errors
    },

    getAccruedInterestErrors() {
      const errors = []
      if (this.foreclosureData.accruedInterest < 0) {
        errors.push('Accrued interest cannot be negative')
      }
      return errors
    },

    getTotalAmountBeforeDiscounts() {
      return (
        this.foreclosureData.principalOutstanding +
        this.foreclosureData.accruedInterest +
        this.getTotalPenaltyCharges()
      )
    },

    calculateForeclosure() {
      // Calculate principal outstanding from current borrower's EMI schedule
      const schedule = this.currentBorrowerEmiSchedule
      const unPaidEmis = schedule.filter((emi) => emi.emiStatus === 'Pending')
      const principalOutstanding = unPaidEmis?.[0]?.outstandingAmount

      this.foreclosureData.principalOutstanding = principalOutstanding

      // Only auto-calculate accrued interest if it hasn't been manually set
      if (!this.manualAccruedInterest) {
        // Calculate accrued interest with proper daily calculation and validation
        const properAccruedInterest = this.calculateAccruedInterest()
        const interestWaiverAmount = this.discountWaivers
          .filter((d) => d.discountType.toLowerCase().includes('interest'))
          .reduce((sum, d) => sum + (parseFloat(d.amount) || 0), 0)

        // Apply validation constraints
        const calculatedAccruedInterest = Math.max(0, properAccruedInterest - interestWaiverAmount)

        this.foreclosureData.accruedInterest = Math.round(calculatedAccruedInterest * 100) / 100
      }

      // Calculate total penalty charges and discounts
      const totalPenaltyCharges = this.getTotalPenaltyCharges()
      const totalDiscountWaivers = this.getTotalDiscountWaivers()

      // Store total discounts for reference
      this.foreclosureData.totalDiscounts = Math.round(totalDiscountWaivers * 100) / 100

      // Calculate penalty amount (before discounts)
      this.foreclosureData.penaltyAmount = Math.round(totalPenaltyCharges * 100) / 100

      // Calculate subtotal (principal + accrued interest)
      this.foreclosureData.subtotal =
        this.foreclosureData.principalOutstanding + this.foreclosureData.accruedInterest

      // Calculate total foreclosure amount with penalties and discounts applied
      const grossAmount = this.foreclosureData.subtotal + totalPenaltyCharges
      this.foreclosureData.totalForeclosureAmount = Math.max(
        0,
        Math.round((grossAmount - totalDiscountWaivers) * 100) / 100
      )

      // Handle partial foreclosure with validation
      if (this.loanClosureType === 'prepayment' && this.partialAmount > 0 && !this.isFullPayment) {
        // Ensure partial amount doesn't exceed maximum allowed
        const validatedPartialAmount = Math.min(this.partialAmount, this.maxPaymentAmount)

        // For partial payments, the foreclosure amount is the partial amount
        this.foreclosureData.totalForeclosureAmount = Math.round(validatedPartialAmount * 100) / 100

        // Update partial amount if it was capped
        if (validatedPartialAmount !== this.partialAmount) {
          this.partialAmount = validatedPartialAmount
        }

        // Calculate prepayment impact
        this.calculatePrepaymentImpact()
      } else if (this.isFullPayment) {
        // For full payment, include all charges and apply all discounts
        this.partialAmount = Math.round(this.foreclosureData.totalForeclosureAmount * 100) / 100
        this.prepaymentImpact = null
      } else {
        this.prepaymentImpact = null
      }
    },

    calculatePrepaymentImpact() {
      if (!this.partialAmount || this.partialAmount <= 0) {
        this.prepaymentImpact = null
        return
      }

      const currentPrincipal = this.foreclosureData.principalOutstanding
      const unpaidEmis = this.currentBorrowerEmiSchedule.filter(
        (emi) => emi.emiStatus !== 'Paid' && emi.emiStatus !== 'Minimum Amount Paid'
      )
      const currentEmi =
        unpaidEmis.length > 0
          ? unpaidEmis[0].emiAmount
          : (this.currentBorrower ? this.currentBorrower.emiAmount : this.loanDetails.emiAmount) ||
            0
      const annualInterestRate = this.loanDetails.interestRate || 0
      const remainingTenure = this.emiStatusData.pendingEmis + this.emiStatusData.overdueEmis

      const netPrepayment = this.partialAmount
      const newPrincipal = Math.max(0, currentPrincipal - netPrepayment)

      let impact = {
        newOutstandingPrincipal: newPrincipal,
        interestSavings: 0,
        tenureReduction: 0,
        newEmiAmount: currentEmi
      }

      if (newPrincipal > 0 && annualInterestRate > 0 && remainingTenure > 0 && currentEmi > 0) {
        if (this.prepaymentApplication === 'reduce_tenure') {
          // Use utility function to calculate new tenure
          const newTenure = calculateTenure(newPrincipal, currentEmi, annualInterestRate)

          if (newTenure < remainingTenure && newTenure !== Infinity) {
            impact.tenureReduction = Math.max(0, remainingTenure - newTenure)
            // impact.interestSavings = currentEmi * impact.tenureReduction
          }
        } else if (this.prepaymentApplication === 'reduce_emi') {
          // Use utility function to calculate new EMI
          const newEmi = calculateEMI(newPrincipal, remainingTenure, annualInterestRate)

          if (newEmi > 0 && newEmi < currentEmi) {
            impact.newEmiAmount = newEmi
            // impact.interestSavings = (currentEmi - newEmi) * remainingTenure
          }
        } else if (this.prepaymentApplication === 'custom_tenure') {
          // For custom tenure, use the calculated values from slider
          this.calculateCustomTenureImpact()
          return // Return early as impact is set in calculateCustomTenureImpact
        }
      } else if (newPrincipal <= 0) {
        // If prepayment covers entire principal, tenure becomes 0
        impact.tenureReduction = remainingTenure
        // impact.interestSavings = currentEmi * remainingTenure
        impact.newOutstandingPrincipal = 0
      }

      this.prepaymentImpact = impact
    },

    calculateAccruedInterest() {
      // Calculate daily accrued interest from last paid EMI date to foreclosure date
      const foreclosureDate = new Date(this.closureDetails.closureDate)
      const annualInterestRate = this.loanDetails.interestRate || 0
      const dailyInterestRate = annualInterestRate / 100 / 365 // Convert annual % to daily decimal

      // Find the last paid EMI to determine the start date for accrued interest calculation
      const paidEmis = this.emiSchedule
        .filter((emi) => emi.emiStatus === 'Paid' || emi.emiStatus === 'Minimum Amount Paid')
        .sort((a, b) => new Date(b.dueDate) - new Date(a.dueDate))

      let startDate
      if (paidEmis.length > 0) {
        // Start from the last paid EMI due date
        startDate = new Date(paidEmis[0].dueDate)
      } else {
        // If no EMIs are paid, start from loan disbursement date or first EMI due date
        const firstEmi = this.emiSchedule[0]
        startDate = firstEmi ? new Date(firstEmi.dueDate) : new Date()
      }

      // Calculate days between start date and foreclosure date
      const timeDiff = foreclosureDate.getTime() - startDate.getTime()
      const daysDiff = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)))

      // Calculate accrued interest: Principal Outstanding × Daily Interest Rate × Days
      const principalOutstanding = this.foreclosureData.principalOutstanding || 0
      const accruedInterest = principalOutstanding * dailyInterestRate * daysDiff
      return Math.max(0, accruedInterest)
    },

    resetCalculation() {
      this.loanClosureType = 'prepayment'
      this.isFullPayment = false
      this.partialAmount = 0
      this.prepaymentApplication = 'reduce_tenure'
      this.prepaymentImpact = null
      this.manualAccruedInterest = false // Reset manual accrued interest flag

      // Reset tenure slider
      this.initializeTenureSlider()

      this.calculateForeclosure()
      this.setDefaultPartialAmount()
      this.closureDetails = {
        closureReason: '',
        remarks: '',
        closureDate: new Date().toISOString().substr(0, 10)
      }
      this.$toast.info('Calculation reset to default values')
    },
    processForeclosure() {
      // if (!this.closureDetails.closureReason) {
      //   this.$toast.error('Please select a closure reason')
      //   return
      // }

      this.confirmDialog = true
    },

    async confirmForeclosure() {
      this.processing = true

      try {
        // Perform final validation before processing
        if (!this.validateInputs()) {
          this.processing = false
          return
        }

        // Additional business logic validation
        const businessValidation = this.performBusinessValidation()
        if (!businessValidation.isValid) {
          businessValidation.errors.forEach((error) => this.$toast.error(error))
          this.processing = false
          return
        }

        const validation = validateForeclosureData({
          ...this.foreclosureData,
          closureReason: this.closureDetails.closureReason,
          penaltyAmount: this.getTotalPenaltyCharges()
        })

        if (!validation.isValid) {
          validation.errors.forEach((error) => this.$toast.error(error))
          this.processing = false
          return
        }

        // Calculate tenure reduction for partial payments
        let tenureReduction = 0
        if (this.loanClosureType === 'prepayment' && this.prepaymentImpact) {
          tenureReduction = this.prepaymentImpact.tenureReduction || 0
        }

        // Calculate net outstanding principal after discounts
        const netOutstandingPrincipal = this.isFullPayment
          ? 0 // For full payment, outstanding becomes 0
          : Math.max(0, this.foreclosureData.principalOutstanding - (this.partialAmount || 0))

        const payload = {
          loanClosureType: this.isFullPayment ? 'foreclosure' : this.loanClosureType,
          totalAmtPaid: this.foreclosureData?.totalForeclosureAmount,
          accruedIntrest: this.foreclosureData.accruedInterest || 0,
          discount: this.getTotalDiscountWaivers(),
          penalty: this.getTotalPenaltyCharges(),
          actualPrincipalAmt: netOutstandingPrincipal,
          remarks: this.closureDetails.remarks || '',
          tenureReduction: tenureReduction,
          amountReduction:
            this.prepaymentApplication === 'reduce_tenure' ? 0 : this.partialAmount || 0,
          penaltyCharges: this.penaltyCharges,
          discountWaivers: this.discountWaivers,
          customerId: this.currentBorrower?.id || null,
          borrowerName: this.currentBorrower?.name || null,
          borrowerShare: this.currentBorrower?.share || 100
        }
        // console.log(payload)
        // return

        const response = await this.$axios.put(
          `/workforce/collection/loan/${this.caseId}/closure`,
          payload
        )

        const borrowerText = this.currentBorrower ? ` for ${this.currentBorrower.name}` : ''

        if (this.loanClosureType === 'foreclosure') {
          this.$toast.success(`Full foreclosure processed successfully${borrowerText}!`)
        } else {
          this.$toast.success(
            `Partial payment processed successfully${borrowerText}! ${
              tenureReduction > 0
                ? `Tenure reduced by ${tenureReduction} months.`
                : 'EMI amount updated.'
            }`
          )
        }

        this.$emit('foreclosure-processed', {
          ...response.data,
          caseStatus: this.loanClosureType === 'foreclosure' ? 'Foreclosed' : 'Active',
          foreclosureAmount: this.foreclosureData.totalForeclosureAmount,
          foreclosureDate: this.closureDetails.closureDate,
          loanClosureType: this.loanClosureType,
          tenureReduction: tenureReduction,
          amountReduction: payload.amountReduction,
          closureReason: this.closureDetails.closureReason,
          remarks: this.closureDetails.remarks,
          principalOutstanding: this.foreclosureData.principalOutstanding,
          accruedInterest: this.foreclosureData.accruedInterest,
          penaltyAmount: this.getTotalPenaltyCharges(),
          totalDiscounts: this.getTotalDiscountWaivers(),
          penaltyCharges: this.penaltyCharges,
          discountWaivers: this.discountWaivers
        })

        this.confirmDialog = false
      } catch (error) {
        const errorMessage = error?.response?.data?.message || 'Failed to process foreclosure'
        this.$toast.error(errorMessage)
        console.error('Foreclosure processing error:', error)
      } finally {
        this.processing = false
      }
    },

    formatCurrency(value) {
      return new Intl.NumberFormat('en-IN').format(value || 0)
    },

    formatDate(dateString) {
      return this.$dayjs(dateString).format('DD MMM YYYY')
    },

    getStatusColor(status) {
      return getEmiStatusColor(status)
    },

    calculatePrincipalPaid() {
      // Calculate total principal paid from current borrower's EMIs with 'Paid' or 'Minimum Amount Paid' status
      return this.currentBorrowerEmiSchedule
        .filter((emi) => emi.emiStatus === 'Paid' || emi.emiStatus === 'Minimum Amount Paid')
        .reduce((total, emi) => total + (emi.principalAmount || 0), 0)
    },

    calculateInterestPaid() {
      // Calculate total interest paid from current borrower's EMIs with 'Paid' or 'Minimum Amount Paid' status
      return this.currentBorrowerEmiSchedule
        .filter((emi) => emi.emiStatus === 'Paid' || emi.emiStatus === 'Minimum Amount Paid')
        .reduce((total, emi) => total + (emi.interestAmount || 0), 0)
    },
    getTotalAmountDue() {
      const total = this.foreclosureData.principalOutstanding + this.foreclosureData.accruedInterest
      return Math.round(total * 100) / 100
    },

    performBusinessValidation() {
      const errors = []
      let isValid = true

      // Check if payment amount is reasonable
      if (this.partialAmount > 0 && this.partialAmount < 100) {
        errors.push('Payment amount seems too low. Please verify the amount.')
        isValid = false
      }

      // Check if total discounts exceed reasonable limits
      const totalDiscounts = this.getTotalDiscountWaivers()
      const totalAmount = this.getTotalAmountBeforeDiscounts()
      if (totalDiscounts > totalAmount * 0.9) {
        // 90% discount limit
        errors.push('Total discounts cannot exceed 90% of the total amount')
        isValid = false
      }

      // Check if penalties are reasonable
      const totalPenalties = this.getTotalPenaltyCharges()
      if (totalPenalties > this.foreclosureData.principalOutstanding) {
        errors.push('Total penalties cannot exceed the principal outstanding amount')
        isValid = false
      }

      // Ensure final amount is positive
      const finalAmount = totalAmount - totalDiscounts
      if (finalAmount <= 0) {
        errors.push('Final payment amount must be greater than zero')
        isValid = false
      }

      return { isValid, errors }
    },

    setDefaultPartialAmount() {
      if (this.isFullPayment) {
        // For full payment, calculate the total amount including penalties and discounts
        this.calculateForeclosure() // Calculate first to get updated foreclosure amount
        this.partialAmount = Math.round(this.foreclosureData.totalForeclosureAmount * 100) / 100
      } else {
        this.partialAmount = ''
        this.calculateForeclosure()
      }
    },

    handleFullPaymentToggle() {
      if (this.isFullPayment) {
        this.prepaymentApplication = ''
      } else {
        // Reset to default when unchecking full payment
        this.prepaymentApplication = 'reduce_tenure'
        this.initializeTenureSlider()
      }
      this.setDefaultPartialAmount()
    },

    getBorrowerLoanAmount() {
      // Return the original loan amount for this borrower based on their EMI schedule
      // This is used for display purposes in the summary
      const schedule = this.currentBorrowerEmiSchedule
      if (schedule.length === 0) return this.loanDetails.loanAmountSanctioned || 0

      // Calculate total principal from all EMIs (paid + unpaid)
      const totalPrincipal = schedule.reduce((sum, emi) => sum + (emi.principalAmount || 0), 0)
      return totalPrincipal || this.loanDetails.loanAmountSanctioned || 0
    },

    initializeTenureSlider() {
      // Calculate current EMI amount
      const unpaidEmis = this.currentBorrowerEmiSchedule.filter(
        (emi) => emi.emiStatus !== 'Paid' && emi.emiStatus !== 'Minimum Amount Paid'
      )

      this.currentEmiAmount =
        unpaidEmis.length > 0
          ? unpaidEmis[0].emiAmount
          : (this.currentBorrower ? this.currentBorrower.emiAmount : this.loanDetails.emiAmount) ||
            0

      // Calculate EMI counts for slider logic
      const totalEmis = this.emiStatusData.totalEmis
      const pendingEmis = this.emiStatusData.pendingEmis + this.emiStatusData.overdueEmis

      // Calculate remaining tenure (pending + overdue)
      this.originalRemainingTenure = pendingEmis

      // Set slider bounds: from 1 to all EMIs
      this.minTenure = 1
      this.maxTenure = totalEmis

      // Set initial custom tenure to (all EMIs - pending EMIs) which represents the remaining tenure after prepayment
      this.customTenure = Math.max(1, totalEmis - pendingEmis)
      this.customEmiAmount = this.currentEmiAmount

      // Ensure custom tenure is within allowed bounds
      this.customTenure = Math.min(Math.max(this.customTenure, this.minTenure), this.maxTenure)
    },

    onTenureSliderChange() {
      if (this.customTenure <= 0 || !this.foreclosureData.principalOutstanding) {
        this.customEmiAmount = 0
        return
      }

      const annualInterestRate = this.loanDetails.interestRate || 0
      const prepaymentAmount = this.partialAmount || 0
      const principalOutstanding = this.foreclosureData.principalOutstanding

      // Calculate new principal after prepayment
      const newPrincipal = Math.max(0, principalOutstanding - prepaymentAmount)

      if (newPrincipal <= 0) {
        this.customEmiAmount = 0
        this.calculateCustomTenureImpact()
        return
      }

      // Calculate remaining tenure based on slider position
      const totalEmis = this.emiStatusData.totalEmis
      const newRemainingTenure = Math.max(0, totalEmis - this.customTenure)

      if (newRemainingTenure === 0) {
        this.customEmiAmount = 0
        this.calculateCustomTenureImpact()
        return
      }

      // Calculate new EMI based on reduced principal and remaining tenure
      this.customEmiAmount =
        Math.round(calculateEMI(newPrincipal, newRemainingTenure, annualInterestRate) * 100) / 100

      // Update prepayment impact for custom tenure
      this.calculateCustomTenureImpact()
    },

    calculateCustomTenureImpact() {
      if (this.prepaymentApplication !== 'custom_tenure') return

      const currentPrincipal = this.foreclosureData.principalOutstanding || 0
      const prepaymentAmount = this.partialAmount || 0

      // Calculate new principal after prepayment
      const newPrincipal = Math.max(0, currentPrincipal - prepaymentAmount)

      if (newPrincipal <= 0) {
        // If prepayment covers entire principal
        this.prepaymentImpact = {
          newOutstandingPrincipal: 0,
          interestSavings: this.currentEmiAmount * this.originalRemainingTenure,
          tenureReduction: this.originalRemainingTenure,
          newEmiAmount: 0,
          customTenure: 0
        }
        return
      }

      // Calculate remaining tenure based on slider selection
      // If slider is at totalEmis, remaining tenure = 0 (full payment)
      // If slider is at 1, remaining tenure = totalEmis - 1
      const totalEmis = this.emiStatusData.totalEmis
      const newRemainingTenure = Math.max(0, totalEmis - this.customTenure)

      if (newRemainingTenure === 0) {
        // Full payment scenario
        this.customEmiAmount = 0
        this.prepaymentImpact = {
          newOutstandingPrincipal: 0,
          interestSavings: this.currentEmiAmount * this.originalRemainingTenure,
          tenureReduction: this.originalRemainingTenure,
          newEmiAmount: 0,
          customTenure: this.customTenure,
          prepaymentAmount: prepaymentAmount
        }
        return
      }

      // Calculate what the EMI would be with the new principal and remaining tenure
      const annualInterestRate = this.loanDetails.interestRate || 0
      const newEmiForCustomTenure = calculateEMI(
        newPrincipal,
        newRemainingTenure,
        annualInterestRate
      )

      // Update the custom EMI amount display
      this.customEmiAmount = Math.round(newEmiForCustomTenure * 100) / 100

      // Calculate total payment amounts for comparison
      const originalTotalPayment = this.currentEmiAmount * this.originalRemainingTenure
      const newTotalPayment = this.customEmiAmount * newRemainingTenure + prepaymentAmount

      // Calculate interest savings (including the benefit of prepayment)
      const interestSavings = Math.max(0, originalTotalPayment - newTotalPayment)

      // Calculate actual tenure reduction
      const tenureReduction = this.originalRemainingTenure - newRemainingTenure

      this.prepaymentImpact = {
        newOutstandingPrincipal: Math.round(newPrincipal * 100) / 100,
        interestSavings: Math.round(interestSavings * 100) / 100,
        tenureReduction: tenureReduction,
        newEmiAmount: Math.round(this.customEmiAmount * 100) / 100,
        customTenure: this.customTenure,
        prepaymentAmount: prepaymentAmount
      }
    }
  }
}
</script>

<style scoped>
.loan-foreclosure-view {
  /* margin: 0 auto; */
}

.summary-item,
.status-item {
  padding: 6px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  transition: all 0.2s ease;
}

.summary-item:hover,
.status-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.summary-item:nth-child(2) {
  border-left-color: #f59e0b;
}

.summary-item:nth-child(3) {
  border-left-color: #ef4444;
}

.summary-item:nth-child(4) {
  border-left-color: #10b981;
}

.status-item:nth-child(2) {
  border-left-color: #10b981;
}

.status-item:nth-child(3) {
  border-left-color: #ef4444;
}

.status-item:nth-child(4) {
  border-left-color: #f59e0b;
}

/* Dark mode support */
.dark-mode .loan-foreclosure-view {
  background-color: #1e1e1e;
  color: #e0e0e0;
}

.dark-mode .summary-item,
.dark-mode .status-item {
  background: #2d2d2d;
  color: #e0e0e0;
}

/* Responsive design */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .loan-foreclosure-view {
    padding: 10px;
  }
}

/* Custom table styling */
table {
  border-collapse: collapse;
}

table th,
table td {
  border: 1px solid #e5e7eb;
}

table th {
  background-color: #f9fafb;
  font-weight: 600;
}

table tr:hover {
  background-color: #f3f4f6;
}

/* Payment summary styling */
.payment-summary {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
}

/* Form section styling */
.form-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

/* Improved spacing */
.space-y-3 > * + * {
  margin-top: 0.75rem;
}
</style>
