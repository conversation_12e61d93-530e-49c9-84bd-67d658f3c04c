<template>
  <v-card class="loan-form-card" flat>
    <!-- Header -->
    <div class="form-header">
      <!-- <h3 class="form-title">{{ editCase ? 'Edit Loan Application' : 'New Loan Application' }}</h3> -->
      <div class="flex justify-start">
        <h3
          class="form-title"
          :class="{ underline: activeType === 'create' }"
          @click="setActiveType('create')"
        >
          {{ editCase ? 'Edit Loan Application' : 'New Loan Application' }}
        </h3>

        <span class="px-4 pt-1"> | </span>
        <h3
          class="form-title"
          :class="{ underline: activeType === 'bulk' }"
          @click="setActiveType('bulk')"
        >
          Bulk Loan Create
        </h3>
      </div>
      <v-btn icon @click="cancel">
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </div>

    <v-divider></v-divider>

    <!-- Form Content -->
    <div>
      <div class="form-content" v-if="activeType === 'create'">
        <v-form ref="form" v-model="valid" lazy-validation>
          <!-- Basic Information Section -->
          <div class="form-section">
            <!-- Assignment Section -->
            <div class="form-section">
              <h4 class="section-title">
                <v-icon color="primary" class="mr-2">mdi-account-check</v-icon>
                Responsible Agent
              </h4>
              <div class="section-content">
                <v-autocomplete
                  v-model="assignedTo"
                  :label="loanLang?.assignedTo?.displayName || 'Assigned To'"
                  :items="employees"
                  :item-text="getEmployeeName"
                  item-value="_id"
                  :loading="loadingEmployees"
                  :search-input.sync="searchEmployee"
                  @update:search-input="debounceEmployee"
                  outlined
                  dense
                  hide-details
                  hide-no-data
                  clearable
                  prepend-inner-icon="mdi-account-arrow-right"
                  :placeholder="`Select ${loanLang?.assignedTo?.displayName || 'Assignee'}`"
                >
                  <template v-slot:selection="{ item }">
                    <div class="v-select__selection">
                      {{ getEmployeeName(item) }}
                    </div>
                  </template>
                </v-autocomplete>
              </div>
            </div>
            <h4 class="section-title">
              <v-icon color="primary" class="mr-2">mdi-account-outline</v-icon>
              Participant Information
            </h4>

            <div class="section-content">
              <v-row>
                <v-col cols="12" md="6" class="border-r-2 border-gray-300">
                  <!-- Primary Merchant Field with Plus Icon -->
                  <div class="merchant-field-container">
                    <!-- Show read-only text field in edit mode -->
                    <v-text-field
                      v-if="editCase"
                      :value="getSelectedCustomerName(editCase)"
                      label="Primary Merchant"
                      outlined
                      dense
                      hide-details
                      prepend-inner-icon="mdi-account"
                      readonly
                      class="readonly-field primary-merchant-field"
                    >
                      <template v-slot:append>
                        <v-btn
                          icon
                          small
                          v-tooltip="{ text: 'Add Borrower' }"
                          color="primary"
                          @click="addBorrowerField"
                          class="add-borrower-btn"
                        >
                          <v-icon small>mdi-plus</v-icon>
                        </v-btn>
                      </template>
                    </v-text-field>

                    <!-- Show autocomplete in create mode -->
                    <v-autocomplete
                      v-else
                      v-model="customerId"
                      :label="`Primary Merchant`"
                      :items="customers"
                      :item-text="getFullName"
                      :search-input.sync="searchCustomer"
                      item-value="_id"
                      @update:search-input="debounceCustomer"
                      hide-no-data
                      outlined
                      dense
                      hide-details
                      prepend-inner-icon="mdi-account-search"
                      placeholder="ex : John Doe"
                      clearable
                      :rules="[(v) => !!v || 'Primary Merchant is required']"
                      class="primary-merchant-field"
                    >
                      <template v-slot:selection="{ item }">
                        <div class="v-select__selection">
                          <v-icon small class="mr-1">mdi-account</v-icon>
                          {{ getFullName(item) }}
                        </div>
                      </template>
                      <template v-slot:append>
                        <v-btn
                          icon
                          small
                          v-tooltip="{ text: 'Add Borrower' }"
                          color="primary"
                          @click.stop="addBorrowerField"
                          :disabled="!customerId"
                          class="add-borrower-btn"
                        >
                          <v-icon small>mdi-plus</v-icon>
                        </v-btn>
                      </template>
                    </v-autocomplete>
                  </div>

                  <!-- Dynamic Borrower Fields -->
                  <div v-if="dynamicBorrowers.length > 0" class="mt-3">
                    <div
                      v-for="(borrower, index) in dynamicBorrowers"
                      :key="`borrower-${index}`"
                      class="dynamic-borrower-field mb-2"
                    >
                      <!-- Show autocomplete when searching -->
                      <v-autocomplete
                        v-if="!borrower.customerId || borrower.isSearching"
                        v-model="borrower.customerId"
                        :label="`Co-Borrower ${index + 1}`"
                        :items="coApplicantCustomers"
                        :item-text="getFullName"
                        :search-input.sync="borrower.searchTerm"
                        item-value="_id"
                        @update:search-input="(text) => debounceDynamicBorrower(text, index)"
                        @change="(value) => onDynamicBorrowerChange(value, index)"
                        hide-no-data
                        outlined
                        dense
                        hide-details
                        prepend-inner-icon="mdi-account-multiple"
                        :placeholder="`Search for co-borrower ${index + 1}`"
                        clearable
                        :loading="loadingCoApplicantCustomers"
                        class="borrower-autocomplete"
                      >
                        <template v-slot:selection="{ item }">
                          <div class="v-select__selection">
                            <v-icon small class="mr-1">mdi-account-multiple</v-icon>
                            {{ getFullName(item) }}
                          </div>
                        </template>
                        <template v-slot:append>
                          <v-btn
                            icon
                            small
                            v-tooltip="{ text: 'Remove Borrower' }"
                            color="error"
                            @click.stop="removeBorrowerField(index)"
                            class="remove-borrower-btn"
                          >
                            <v-icon small>mdi-minus</v-icon>
                          </v-btn>
                        </template>
                      </v-autocomplete>

                      <!-- Show text field when selected -->
                      <v-text-field
                        v-else
                        :value="getSelectedBorrowerName(borrower.customerId)"
                        :label="`Co-Borrower ${index + 1}`"
                        outlined
                        dense
                        hide-details
                        prepend-inner-icon="mdi-account-multiple"
                        readonly
                        class="borrower-text-field"
                      >
                        <template v-slot:append>
                          <v-btn
                            icon
                            small
                            v-tooltip="{ text: 'Search Borrower' }"
                            color="primary"
                            @click.stop="enableBorrowerSearch(index)"
                            class="search-borrower-btn"
                            title="Search for different co-borrower"
                          >
                            <v-icon small>mdi-magnify</v-icon>
                          </v-btn>
                          <v-btn
                            icon
                            small
                            v-tooltip="{ text: 'Remove Borrower' }"
                            color="error"
                            @click.stop="removeBorrowerField(index)"
                            class="remove-borrower-btn ml-1"
                          >
                            <v-icon small>mdi-minus</v-icon>
                          </v-btn>
                        </template>
                      </v-text-field>
                    </div>
                  </div>
                </v-col>

                <v-col cols="12" md="6">
                  <v-autocomplete
                    v-model="partnerId"
                    :label="`Partner/Vendor`"
                    :items="partners"
                    :item-text="getPartnerName"
                    :search-input.sync="searchPartner"
                    item-value="_id"
                    @update:search-input="debouncePartner"
                    hide-no-data
                    outlined
                    dense
                    hide-details
                    prepend-inner-icon="mdi-handshake"
                    placeholder="ex : Bajaj Finserv"
                    :loading="loadingPartners"
                    clearable
                    @change="addFeeParty"
                  >
                  </v-autocomplete>
                </v-col>
              </v-row>
            </div>
          </div>

          <!-- Loan Details Section -->
          <div class="form-section form-content-border">
            <h4 class="section-title">
              <v-icon color="primary" class="mr-2">mdi-cash-multiple</v-icon>
              Loan Details
            </h4>

            <div class="section-content">
              <v-row>
                <v-col cols="12" md="6">
                  <v-select
                    v-model="typeOfLoan"
                    :label="loanLang?.typeOfLoan?.displayName || 'Loan Type'"
                    :items="typeOfLoans"
                    :rules="[(v) => !!v || 'Loan Type is required']"
                    outlined
                    dense
                    hide-details
                    prepend-inner-icon="mdi-tag-outline"
                    placeholder="ex : Personal"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="caseNo"
                    :label="loanLang?.caseNo?.displayName || 'Loan ID'"
                    :rules="[(v) => !!v || 'Loan ID is required']"
                    outlined
                    dense
                    hide-details
                    prepend-inner-icon="mdi-identifier"
                    placeholder="ex : LN-1234567890"
                  >
                  </v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="loanAmountRequested"
                    :label="loanLang?.loanAmountRequested?.displayName || 'Loan Amount'"
                    type="number"
                    :rules="[(v) => !!v || 'Loan Amount is required']"
                    outlined
                    dense
                    hide-details
                    prepend-inner-icon="mdi-currency-inr"
                    placeholder="ex : 254567.00"
                    suffix="INR"
                  >
                  </v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="tenureMonths"
                    :label="loanLang?.tenure?.displayName || 'Tenure'"
                    type="number"
                    :rules="[(v) => !!v || 'Tenure is required']"
                    outlined
                    dense
                    hide-details
                    prepend-inner-icon="mdi-calendar-range"
                    placeholder="ex : 12 Months"
                    suffix="Months"
                  >
                  </v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <combined-input
                    :left-value.sync="typeOfInterest"
                    :left-label="loanLang?.typeOfInterest?.displayName || 'Interest Type'"
                    :left-items="['Fixed', 'Floating']"
                    left-icon="mdi-percent"
                    left-placeholder="ex : Fixed"
                    :left-cols="4"
                    :right-value.sync="interestRate"
                    :right-label="loanLang?.interestRate?.displayName || 'Interest Rate (%)'"
                    right-type="number"
                    right-icon="mdi-chart-line"
                    :right-disabled="editCase && typeOfInterest === 'Fixed'"
                    right-placeholder="ex : 10.5%"
                    :right-suffix="formattedInterestAmount(interestRate)"
                    :right-rules="[
                      (v) => !!v || 'Interest Rate is required',
                      (v) => (v && v > 0 && v <= 100) || 'Interest Rate must be between 0 and 100'
                    ]"
                    :right-cols="8"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <combined-input
                    :left-value.sync="lateFeeType"
                    :left-label="loanLang?.lateFeeType?.displayName || 'Late fee'"
                    :left-items="['Fixed', 'Percentage']"
                    left-icon="mdi-cash-minus"
                    left-placeholder="ex : Fixed"
                    :left-cols="4"
                    :right-value.sync="latePaymentFee"
                    :right-label="loanLang?.latePaymentFee?.displayName || 'Late fee amount (%)'"
                    right-type="number"
                    :right-disabled="editCase && typeOfInterest === 'Fixed'"
                    right-placeholder="ex : 10.5%"
                    :right-cols="8"
                  />
                </v-col>
              </v-row>
              <!-- Collateral Section -->
              <div class="collateral-section mt-4">
                <div class="fee-header-compact">
                  <h6 class="fee-title">
                    <v-icon small class="mr-1">mdi-shield-check</v-icon>
                    Collateral(s)
                  </h6>
                </div>

                <div class="collateral-content mt-2">
                  <v-row>
                    <v-col cols="12" md="6">
                      <v-select
                        v-model="collateralType"
                        label="Collateral Type"
                        :items="collateralTypes"
                        outlined
                        dense
                        hide-details
                        prepend-inner-icon="mdi-shield-outline"
                        placeholder="Select collateral type"
                      ></v-select>
                    </v-col>
                    <v-col cols="12" md="6">
                      <v-file-input
                        v-model="collateralDocuments"
                        label="Upload Documents"
                        multiple
                        outlined
                        dense
                        hide-details
                        prepend-inner-icon="mdi-file-document-outline"
                        placeholder="Select collateral documents"
                        accept="image/*,.pdf,.doc,.docx"
                        @change="handleCollateralDocuments"
                        :disabled="collateralType && collateralType == 'None'"
                      ></v-file-input>
                    </v-col>
                  </v-row>

                  <v-row>
                    <v-col cols="12">
                      <v-textarea
                        v-model="collateralRemarks"
                        label="Collateral Remarks"
                        outlined
                        dense
                        rows="3"
                        auto-grow
                        hide-details
                        prepend-inner-icon="mdi-comment-text-outline"
                        placeholder="Add details about the collateral (value, condition, location, etc.)"
                        :disabled="collateralType && collateralType == 'None'"
                      ></v-textarea>
                    </v-col>
                  </v-row>

                  <!-- Uploaded Documents Display -->
                  <v-row v-if="uploadedCollateralDocuments.length > 0">
                    <v-col cols="12">
                      <div class="uploaded-documents">
                        <h6 class="documents-title">
                          <v-icon small class="mr-1">mdi-file-check</v-icon>
                          Uploaded Documents
                        </h6>
                        <div class="documents-list">
                          <v-chip
                            v-for="(doc, index) in uploadedCollateralDocuments"
                            :key="index"
                            small
                            color="primary"
                            text-color="white"
                            class="mr-2 mb-2"
                            close
                            @click:close="removeCollateralDocument(index)"
                          >
                            <v-icon small class="mr-1">mdi-file</v-icon>
                            {{ doc }}
                          </v-chip>
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                </div>
              </div>

              <!-- Co-Applicants Section -->
              <div class="co-applicants-section mt-4">
                <div class="co-applicants-header">
                  <h5 class="co-applicants-title">
                    <v-icon small class="mr-2">mdi-account-multiple</v-icon>
                    Borrowers & Share Distribution
                  </h5>
                </div>

                <!-- Show message if no borrowers added yet -->
                <div v-if="allBorrowers.length === 0" class="no-borrowers-message mb-3">
                  <v-alert type="info" dense text>
                    <v-icon small class="mr-2">mdi-information</v-icon>
                    Add borrowers using the + icon next to the Primary Merchant field above.
                  </v-alert>
                </div>

                <!-- Show all borrowers (primary + dynamic) -->
                <div v-if="allBorrowers.length > 0">
                  <div>
                    <!-- Primary Borrower Summary - Compact -->
                    <div class="primary-borrower-summary mb-2">
                      <div class="borrower-info">
                        <v-avatar size="24" color="success" class="mr-2">
                          <v-icon x-small color="white">mdi-account-star</v-icon>
                        </v-avatar>
                        <div class="borrower-details">
                          <span class="borrower-name"
                            >Primary Borrower: {{ getSelectedCustomerName(editCase) }}</span
                          >
                          <div class="borrower-share">
                            <span class="share-percentage"
                              >{{ borrowerSharePercentage.toFixed(1) }}%</span
                            >
                            <span class="share-amount"
                              >₹{{ formatShareAmount(borrowerSharePercentage) }}</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Selected Co-Applicants List - Compact Grid Layout -->
                <div v-if="coApplicants.length > 0" class="co-applicants-grid">
                  <!-- Co-Applicants Cards in Grid -->
                  <div class="co-applicants-cards-grid">
                    <div
                      v-for="(coApplicant, index) in coApplicants"
                      :key="coApplicant._id"
                      class="co-applicant-card-compact"
                    >
                      <div class="co-applicant-header">
                        <div class="co-applicant-info">
                          <v-avatar size="24" color="primary" class="mr-2">
                            <span class="white--text text-xs">
                              {{ getInitials(coApplicant.customerFullName) }}
                            </span>
                          </v-avatar>
                          <div class="co-applicant-details">
                            <div class="co-applicant-name">
                              {{ coApplicant.customerFullName }} (Co-Borrower - {{ index + 1 }})
                            </div>
                            <div class="co-applicant-contact">
                              {{ coApplicant.email || coApplicant.phone || 'No contact' }}
                            </div>
                          </div>
                        </div>
                        <v-btn icon x-small color="error" @click="removeCoApplicant(index)">
                          <v-icon x-small>mdi-delete</v-icon>
                        </v-btn>
                      </div>

                      <div class="co-applicant-share">
                        <div class="share-input-group">
                          <v-text-field
                            v-model.number="coApplicant.sharePercentage"
                            label="Share %"
                            type="number"
                            suffix="%"
                            outlined
                            dense
                            hide-details
                            placeholder="0"
                            min="0"
                            max="100"
                            @input="calculateBorrowerShare"
                            class="share-percentage-input"
                          ></v-text-field>
                          <div class="share-amount-compact">
                            <span class="amount-label">Amount:</span>
                            <span class="amount-value"
                              >₹{{ formatShareAmount(coApplicant.sharePercentage) }}</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Share Summary Alert - Compact -->
                  <v-alert
                    :type="totalSharePercentage === 100 ? 'success' : 'warning'"
                    dense
                    text
                    class="share-summary-alert"
                  >
                    <div class="share-summary-content">
                      <span class="summary-text">
                        Total: {{ totalSharePercentage.toFixed(1) }}%
                        <span v-if="totalSharePercentage !== 100" class="status-text">
                          ({{ totalSharePercentage > 100 ? 'Over-allocated' : 'Under-allocated' }})
                        </span>
                      </span>
                    </div>
                  </v-alert>
                </div>

                <!-- Empty State -->
                <div v-else class="co-applicants-empty-state">
                  <v-icon color="grey lighten-1" size="32">mdi-account-multiple-outline</v-icon>
                  <p class="text--secondary mt-2 mb-0 text-sm">
                    No co-borrowers added yet. Search and select "Co-Borrowers" from the list.
                  </p>
                </div>
              </div>

              <!-- Guarantors Section -->
              <div class="co-applicants-section mt-4">
                <div class="co-applicants-header">
                  <h5 class="co-applicants-title">
                    <v-icon small class="mr-2">mdi-shield-account</v-icon>
                    Add Guarantors
                  </h5>
                </div>

                <!-- Guarantor Search (shown when adding) -->
                <v-expand-transition>
                  <div>
                    <v-card outlined class="mb-3">
                      <v-autocomplete
                        v-model="selectedGuarantor"
                        :label="'Search Guarantor'"
                        :items="availableGuarantorCustomers"
                        :item-text="getFullName"
                        :search-input.sync="searchGuarantor"
                        item-value="_id"
                        @update:search-input="debounceGuarantorSearch"
                        hide-no-data
                        outlined
                        dense
                        hide-details
                        prepend-inner-icon="mdi-account-search"
                        placeholder="Search for guarantor..."
                        clearable
                        @change="addGuarantor"
                      >
                        <template v-slot:selection="{ item }">
                          <div class="v-select__selection">
                            <v-icon small class="mr-1">mdi-shield-account</v-icon>
                            {{ getFullName(item) }}
                          </div>
                        </template>
                      </v-autocomplete>
                    </v-card>
                  </div>
                </v-expand-transition>

                <!-- Selected Guarantors List - Compact Grid Layout -->
                <div v-if="guarantors.length > 0" class="co-applicants-grid">
                  <!-- Guarantors Cards in Grid -->
                  <div class="co-applicants-cards-grid">
                    <div
                      v-for="(guarantor, index) in guarantors"
                      :key="guarantor._id"
                      class="co-applicant-card-compact"
                    >
                      <div class="co-applicant-header">
                        <div class="co-applicant-info">
                          <v-avatar size="24" color="orange" class="mr-2">
                            <span class="white--text text-xs">
                              {{ getInitials(guarantor.customerFullName) }}
                            </span>
                          </v-avatar>
                          <div class="co-applicant-details">
                            <div class="co-applicant-name">
                              {{ guarantor.customerFullName }} (Guarantor - {{ index + 1 }})
                            </div>
                            <div class="co-applicant-contact">
                              {{ guarantor.email || guarantor.phone || 'No contact' }}
                            </div>
                          </div>
                        </div>
                        <v-btn icon x-small color="error" @click="removeGuarantor(index)">
                          <v-icon x-small>mdi-delete</v-icon>
                        </v-btn>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Empty State -->
                <div v-else class="co-applicants-empty-state">
                  <v-icon color="grey lighten-1" size="32">mdi-shield-account-outline</v-icon>
                  <p class="text--secondary mt-2 mb-0 text-sm">
                    No guarantors added yet. Search and select "Guarantors" from the list.
                  </p>
                </div>
              </div>

              <!-- Loan Workflow Section -->
              <div class="form-section mt-4 form-content-border">
                <h4 class="section-title">
                  <v-icon color="primary" class="mr-2">mdi-timeline-outline</v-icon>
                  Loan Workflow
                </h4>
                <div class="section-content">
                  <workflow-step-renderer
                    entity-type="LOAN"
                    :loan-type="typeOfLoan"
                    :existing-workflow-data="loanWorkflowData"
                    :key="`loan-workflow-${editCase?._id || 'new'}`"
                    @workflow-saved="(val) => handleLoanWorkflowSaved(val)"
                  />
                </div>
              </div>

              <v-row>
                <v-col cols="12">
                  <div class="processing-fee-section">
                    <div class="fee-header-compact">
                      <h6 class="fee-title">
                        <v-icon small class="mr-1">mdi-check-circle</v-icon>
                        Sanction Details
                      </h6>
                    </div>

                    <div class="flex gap-4">
                      <v-text-field
                        v-model="loanAmountSanctioned"
                        :label="loanLang?.loanAmountSanctioned?.displayName || 'Sanctioned Amount'"
                        :rules="[(v) => !!v || 'Sanctioned Amount is required']"
                        type="number"
                        outlined
                        dense
                        hide-details
                        prepend-inner-icon="mdi-currency-inr"
                        placeholder="ex: 10000.00"
                        suffix="INR"
                      >
                      </v-text-field>
                      <v-menu
                        v-model="sanctionDateMenu"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                        min-width="auto"
                      >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field
                            v-model="formattedSanctionDate"
                            :label="loanLang?.sanctionDate?.displayName || 'Sanction Date'"
                            prepend-inner-icon="mdi-calendar"
                            readonly
                            v-bind="attrs"
                            v-on="on"
                            outlined
                            dense
                            hide-details
                            placeholder="ex : 01/01/2023"
                          ></v-text-field>
                        </template>
                        <v-date-picker
                          v-model="sanctionDate"
                          @input="formatSanctionDate"
                          no-title
                          scrollable
                        ></v-date-picker>
                      </v-menu>
                    </div>
                  </div>
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12">
                  <div class="processing-fee-section">
                    <div class="fee-header-compact">
                      <h6 class="fee-title">
                        <v-icon small class="mr-1">mdi-account-multiple</v-icon>
                        Processing Fee
                      </h6>
                    </div>

                    <div class="fee-parties-compact mt-2">
                      <div
                        v-for="(party, index) in processingFeeParties"
                        :key="`fee-party-${index}`"
                        class="fee-party-compact mb-1"
                      >
                        <div class="fee-party-content">
                          <div class="party-name-section">
                            <v-autocomplete
                              v-if="!party.self"
                              v-model="party.partner"
                              :label="'Partner/Lender Name'"
                              :items="partners"
                              :item-text="getPartnerName"
                              :search-input.sync="searchPartner"
                              item-value="_id"
                              @update:search-input="debouncePartner"
                              hide-no-data
                              outlined
                              dense
                              hide-details
                              prepend-inner-icon="mdi-handshake"
                              placeholder="Select Partner/Lender"
                              :loading="loadingPartners"
                              readonly
                              class="party-name-field"
                            >
                            </v-autocomplete>
                            <v-text-field
                              v-else
                              :value="orgName"
                              label="Organization Name"
                              outlined
                              dense
                              hide-details
                              readonly
                              class="party-name-field readonly-field"
                              prepend-inner-icon="mdi-star"
                            >
                              <template v-slot:prepend-inner>
                                <v-icon small color="primary">mdi-star</v-icon>
                              </template>
                            </v-text-field>
                          </div>
                          <div class="party-percentage-section">
                            <v-text-field
                              v-model="party.share"
                              label="Share %"
                              type="number"
                              outlined
                              dense
                              hide-details
                              suffix="%"
                              placeholder="1.5"
                              @input="calculateTotalProcessingFee"
                              class="party-percentage-field"
                            ></v-text-field>
                          </div>
                          <div class="party-amount-section">
                            <span class="amount-display">
                              {{ formatCurrency(calculatePartyAmount(party.share)) || '₹0' }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Compact Total Summary -->
                    <div class="fee-summary-compact mt-2">
                      <div class="flex justify-end">
                        <span class="total-label text-sm">Total Fee:</span>
                        <p class="total-chip">{{ totalProcessingFeePercentage.toFixed(1) }}%</p>
                        <span class="total-amount"
                          >({{ formattedPfAmount(totalProcessingFeePercentage) }})</span
                        >
                      </div>
                    </div>
                  </div>

                  <!-- Additional Charges Section -->
                  <!-- <div class="additional-charges-section mt-4">
                  <div class="fee-header-compact">
                    <h6 class="fee-title">
                      <v-icon small class="mr-1">mdi-cash-plus</v-icon>
                      Additional Charges
                    </h6>
                    <v-btn
                      x-small
                      color="primary"
                      outlined
                      @click="addAdditionalCharge"
                      class="add-party-btn-compact"
                    >
                      <v-icon x-small class="mr-1">mdi-plus</v-icon>
                      Add
                    </v-btn>
                  </div>

                  <div class="fee-parties-compact mt-2">
                    <div
                      v-for="(charge, index) in additionalCharges"
                      :key="`additional-charge-${index}`"
                      class="fee-party-compact mb-1"
                    >
                      <div class="additional-charge-content">
                        <div class="charge-type-section">
                          <v-text-field
                            v-model="charge.feeType"
                            label="Fee Type"
                            outlined
                            dense
                            hide-details
                            prepend-inner-icon="mdi-tag-outline"
                            placeholder="e.g., Documentation Fee"
                            class="charge-type-field"
                          ></v-text-field>
                        </div>
                        <div class="charge-percentage-section">
                          <v-text-field
                            v-model="charge.percentage"
                            label="Percentage"
                            type="number"
                            outlined
                            dense
                            hide-details
                            suffix="%"
                            placeholder="0.5"
                            @input="calculateTotalAdditionalCharges"
                            class="charge-percentage-field"
                          ></v-text-field>
                        </div>
                        <div class="charge-emi-section">
                          <v-checkbox
                            v-model="charge.linkWithEmi"
                            label="Link with EMI"
                            color="primary"
                            hide-details
                            dense
                            class="charge-emi-checkbox"
                          ></v-checkbox>
                        </div>
                        <div class="charge-actions-section" v-if="additionalCharges.length > 1">
                          <v-btn icon x-small color="error" @click="removeAdditionalCharge(index)">
                            <v-icon x-small>mdi-delete</v-icon>
                          </v-btn>
                        </div>
                        <div class="charge-amount-section">
                          <span class="amount-display">
                            {{ formatCurrency(calculateChargeAmount(charge.percentage)) || '₹0' }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="fee-summary-compact mt-2" v-if="additionalCharges.length > 0">
                    <div class="flex justify-end">
                      <span class="total-label text-sm">Total Charges:</span>
                      <p class="total-chip">{{ totalAdditionalChargesPercentage.toFixed(1) }}%</p>
                      <span class="total-amount"
                        >({{
                          formattedAdditionalChargesAmount(totalAdditionalChargesPercentage)
                        }})</span
                      >
                    </div>
                  </div>
                </div> -->
                  <div class="mt-4">
                    <AdditionalChargesSection
                      :show-link-with-emi="true"
                      :emi-amount="loanAmountSanctioned"
                      :format-currency="formatCurrency"
                    />
                  </div>
                </v-col>
              </v-row>

              <!-- Prepayment Settings -->
              <div class="prepayment-section mt-4">
                <div class="prepayment-header">
                  <h6 class="prepayment-title">
                    <v-icon small class="mr-1">mdi-cash-fast</v-icon>
                    Prepayment
                  </h6>
                </div>

                <div class="prepayment-content mt-2">
                  <v-row>
                    <v-col cols="12">
                      <v-checkbox
                        v-model="prepaymentAllowed"
                        :label="
                          loanLang?.prepaymentAllowed?.displayName ||
                          'Eligible for Pre-payment/Early Closure'
                        "
                        color="primary"
                        hide-details
                        class="mt-0"
                      >
                        <template v-slot:label>
                          <span class="prepayment-label">
                            Eligible for Pre-payment/Early Closure
                            <br />
                            <small class="text--secondary">
                              Enable this option to allow customers to prepay or early close the
                              loan
                            </small>
                          </span>
                        </template>
                      </v-checkbox>
                    </v-col>
                  </v-row>

                  <v-expand-transition>
                    <v-row v-if="prepaymentAllowed">
                      <v-col cols="12">
                        <v-text-field
                          v-model="preEmiBuffer"
                          label="PRE EMI Buffer (Months)"
                          type="number"
                          outlined
                          dense
                          hide-details
                          prepend-inner-icon="mdi-calendar-clock"
                          placeholder="ex: 3"
                          suffix="Months"
                          hint="Minimum EMIs to be paid before prepayment is allowed"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-expand-transition>
                </div>
              </div>
            </div>
          </div>

          <!-- Disbursement Section -->
          <div class="form-section form-content-border">
            <h4 class="section-title">
              <v-icon color="primary" class="mr-2">mdi-cash-multiple</v-icon>
              Disbursement Details
            </h4>

            <div class="section-content">
              <!-- Show fully disbursed message at the top if applicable -->
              <div v-if="isFullyDisbursed" class="fully-disbursed-message mb-4">
                <v-icon large color="success">mdi-check-circle</v-icon>
                <p>
                  Full loan amount has been disbursed. Total disbursed:
                  {{ formatCurrency(totalDisbursementAmount) }}
                </p>
              </div>

              <v-expansion-panels
                v-model="expandedDisbursementPanel"
                multiple
                class="workflow-panels"
              >
                <!-- :class="{
                  'disbursement-disabled': isFullyDisbursed
                }" -->
                <v-expansion-panel
                  v-for="(disbursement, index) in loanDisbursements"
                  :key="`disbursement-${index}`"
                  class="workflow-step-panel"
                >
                  <v-expansion-panel-header class="workflow-panel-header">
                    <div class="workflow-header-content">
                      <div class="step-info">
                        <span class="step-number">{{ index + 1 }}</span>
                        <span class="step-name">
                          {{ formatCurrency(disbursement.amount) || '₹0' }}
                        </span>
                        <v-chip
                          x-small
                          v-if="disbursement.status"
                          class="status-chip"
                          :color="getStatusColor(disbursement.status)"
                          text-color="white"
                        >
                          {{ disbursement.status }}
                        </v-chip>
                      </div>
                      <v-btn
                        icon
                        x-small
                        @click.stop="removeDisbursement(index)"
                        class="remove-step-btn"
                      >
                        <v-icon small>mdi-close</v-icon>
                      </v-btn>
                    </div>
                  </v-expansion-panel-header>

                  <v-expansion-panel-content class="workflow-panel-content">
                    <v-row>
                      <v-col cols="12" md="6">
                        <v-text-field
                          v-model="disbursement.amount"
                          label="Disbursement Amount"
                          type="number"
                          :rules="[(v) => !!v || 'Amount is required']"
                          outlined
                          dense
                          hide-details
                          prepend-inner-icon="mdi-currency-inr"
                          placeholder="ex : 154645.00"
                          suffix="INR"
                        ></v-text-field>
                      </v-col>

                      <v-col cols="12" md="6">
                        <v-menu
                          v-model="disbursementDateMenus[index]"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="auto"
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field
                              v-model="formattedDisbursementDates[index]"
                              label="Disbursement Date"
                              prepend-inner-icon="mdi-calendar"
                              readonly
                              v-bind="attrs"
                              v-on="on"
                              outlined
                              dense
                              hide-details
                              placeholder="ex : 01/01/2023"
                            ></v-text-field>
                          </template>
                          <v-date-picker
                            v-model="disbursement.date"
                            @input="formatDisbursementDate(index)"
                            no-title
                            scrollable
                          ></v-date-picker>
                        </v-menu>
                      </v-col>

                      <v-col cols="12" md="6">
                        <v-select
                          v-model="disbursement.status"
                          label="Status"
                          :items="['Pending', 'Completed', 'Cancelled']"
                          outlined
                          dense
                          hide-details
                          prepend-inner-icon="mdi-flag-outline"
                        ></v-select>
                      </v-col>

                      <v-col cols="12" md="6">
                        <v-select
                          v-model="disbursement.paymentMode"
                          label="Payment Mode"
                          :items="['Bank Transfer', 'Cheque/DD']"
                          outlined
                          dense
                          hide-details
                          prepend-inner-icon="mdi-bank-outline"
                          @change="handlePaymentModeChange(index)"
                        ></v-select>
                      </v-col>

                      <v-col cols="12" md="6">
                        <v-text-field
                          v-model="disbursement.referenceNo"
                          label="Reference/UTR Number"
                          outlined
                          dense
                          hide-details
                          prepend-inner-icon="mdi-pound"
                          placeholder="ex : UTR123456789"
                        ></v-text-field>
                      </v-col>

                      <!-- Bank Transfer Details -->
                      <template
                        v-if="
                          disbursement.paymentMode === 'Bank Transfer' ||
                          disbursement.paymentMode === 'Online'
                        "
                      >
                        <v-col cols="12">
                          <div class="payment-details-section">
                            <h5 class="payment-details-title">Bank Details</h5>
                            <v-row>
                              <v-col cols="12" md="4">
                                <v-text-field
                                  v-model="disbursement.bankDetails.bankName"
                                  label="Bank Name"
                                  outlined
                                  dense
                                  hide-details
                                  placeholder="ex : HDFC Bank"
                                  prepend-inner-icon="mdi-bank"
                                ></v-text-field>
                              </v-col>
                              <v-col cols="12" md="4">
                                <v-text-field
                                  v-model="disbursement.bankDetails.accountNumber"
                                  label="Account Number"
                                  outlined
                                  dense
                                  hide-details
                                  placeholder="ex : ***********"
                                  prepend-inner-icon="mdi-numeric"
                                ></v-text-field>
                              </v-col>
                              <v-col cols="12" md="4">
                                <v-text-field
                                  v-model="disbursement.bankDetails.ifscCode"
                                  label="IFSC Code"
                                  outlined
                                  dense
                                  hide-details
                                  placeholder="ex : HDFC0001234"
                                  prepend-inner-icon="mdi-alphabetical"
                                ></v-text-field>
                              </v-col>
                            </v-row>
                          </div>
                        </v-col>
                      </template>

                      <!-- Cheque Details -->
                      <template v-if="disbursement.paymentMode === 'Cheque'">
                        <v-col cols="12">
                          <div class="payment-details-section">
                            <h5 class="payment-details-title">Cheque/DD Details</h5>
                            <v-row>
                              <v-col cols="12" md="4">
                                <v-text-field
                                  v-model="disbursement.chequeDetails.chequeNumber"
                                  label="Cheque/DD Number"
                                  outlined
                                  dense
                                  hide-details
                                  placeholder="ex : **********"
                                  prepend-inner-icon="mdi-numeric"
                                ></v-text-field>
                              </v-col>
                              <v-col cols="12" md="4">
                                <v-text-field
                                  v-model="disbursement.chequeDetails.bankName"
                                  label="Bank Name"
                                  outlined
                                  dense
                                  hide-details
                                  placeholder="ex : SBI Bank"
                                  prepend-inner-icon="mdi-bank"
                                ></v-text-field>
                              </v-col>
                              <v-col cols="12" md="4">
                                <v-text-field
                                  v-model="disbursement.chequeDetails.branch"
                                  label="Branch"
                                  outlined
                                  dense
                                  hide-details
                                  placeholder="ex : Main Branch"
                                  prepend-inner-icon="mdi-map-marker"
                                ></v-text-field>
                              </v-col>
                            </v-row>
                          </div>
                        </v-col>
                      </template>

                      <v-col cols="12">
                        <v-textarea
                          v-model="disbursement.remarks"
                          label="Remarks"
                          outlined
                          dense
                          rows="2"
                          auto-grow
                          hide-details
                          prepend-inner-icon="mdi-comment-text-outline"
                          placeholder="Add any notes or comments"
                        ></v-textarea>
                      </v-col>

                      <!-- Disbursement Workflow -->
                      <v-col cols="12">
                        <div class="mt-4">
                          <h5 class="text-[#21427d] font-medium pb-2">
                            <v-icon class="mr-1">mdi-timeline-outline</v-icon>
                            Disbursement Workflow
                          </h5>
                          <workflow-step-renderer
                            entity-type="DISBURSEMENT"
                            :loan-type="typeOfLoan"
                            :existing-workflow-data="disbursementWorkflowData[index]"
                            :key="`disbursement-workflow-${index}-${editCase?._id || 'new'}`"
                            @workflow-saved="
                              (instance) => handleDisbursementWorkflowSaved(index, instance)
                            "
                          />
                        </div>
                      </v-col>
                    </v-row>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </div>
          </div>

          <!-- EMI Workflow Section -->
          <div class="form-section form-content-border">
            <h4 class="section-title">
              <v-icon color="primary" class="mr-2">mdi-calendar-month</v-icon>
              EMI(s)
            </h4>
            <div class="section-content">
              <v-row>
                <v-col cols="12">
                  <div class="processing-fee-section mb-4">
                    <!-- <div class="fee-header-compact">
                    <h6 class="fee-title">
                      <v-icon small class="mr-1">mdi-check-circle</v-icon>
                      EMI Details
                    </h6>
                  </div> -->
                    <div>
                      <v-menu
                        v-model="emiStartDateMenu"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                        min-width="auto"
                      >
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field
                            v-model="formattedEmiStartDate"
                            :label="loanLang?.emiStartDate?.displayName || 'EMI Start Date'"
                            prepend-inner-icon="mdi-calendar"
                            readonly
                            v-bind="attrs"
                            v-on="on"
                            outlined
                            dense
                            hide-details
                            placeholder="ex : 01/01/2023"
                          ></v-text-field>
                        </template>
                        <v-date-picker
                          v-model="emiStartDate"
                          @input="formatEmiStartDate"
                          no-title
                          scrollable
                        ></v-date-picker>
                      </v-menu>
                    </div>
                  </div>
                </v-col>
              </v-row>
              <div class="px-4">
                <h4 class="section-title">
                  <v-icon color="primary" class="mr-2">mdi-timeline-outline</v-icon>
                  EMI Workflow
                </h4>
                <workflow-step-renderer
                  entity-type="EMI"
                  :loan-type="typeOfLoan"
                  :existing-workflow-data="emiWorkflowData"
                  :key="`emi-workflow-${editCase?._id || 'new'}`"
                  @workflow-saved="(val) => handleEmiWorkflowSaved(val)"
                />
              </div>
            </div>
          </div>
        </v-form>
        <v-divider></v-divider>

        <!-- <div class="form-actions">
          <v-btn text @click="cancel" class="cancel-btn"> Cancel </v-btn>
          <v-btn
            color="primary"
            @click="saveLoanDetails"
            :loading="saving"
            :disabled="saving"
            class="save-btn"
          >
            <v-icon left>mdi-content-save</v-icon>
            {{ editCase ? 'Update' : 'Save' }}
          </v-btn>
        </div> -->
      </div>

      <!-- Bulk Loan Create Form -->

      <LoanBulkUpload
        v-if="activeType === 'bulk'"
        ref="bulkUploader"
        :loan-type="typeOfLoan"
        :case-config="caseConfig"
        :loan-amount-sanctioned="loanAmountSanctioned"
        :emi-start-date="emiStartDate"
        :formatted-emi-start-date="formattedEmiStartDate"
        :disbursement-workflow-data="disbursementWorkflowData"
        :saving="saving"
        :edit-case="editCase"
      />
      <div class="border-t px-5 py-3 bg-white">
        <div class="flex justify-end gap-3">
          <v-btn text @click="cancel" class="cancel-btn"> Cancel </v-btn>
          <v-btn
            color="primary"
            @click="saveLoanDetails"
            :loading="saving"
            :disabled="saving"
            class="save-btn"
          >
            <v-icon left>mdi-content-save</v-icon>
            {{ editCase ? 'Update' : 'Save' }}
          </v-btn>
        </div>
      </div>
    </div>
  </v-card>
</template>
<script>
import WorkflowStepRenderer from '@/components/Workforce/CollectionManagement/WorkflowStepRenderer.vue'
import CombinedInput from '@/components/common/CombinedInput.vue'
import { debounce } from '@/utils/common'
import AdditionalChargesSection from './AdditionalChargesSection.vue'
import LoanBulkUpload from './LoanBulkUpload.vue'

export default {
  name: 'AddEditLoanManagement',
  components: {
    WorkflowStepRenderer,
    CombinedInput,
    AdditionalChargesSection,
    LoanBulkUpload
  },
  props: {
    editCase: {
      type: Object,
      default: null
    },
    caseConfig: {
      type: Object,
      default: null
    }
  },
  watch: {
    editCase: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.populateForm()
        }
      },
      deep: true,
      immediate: true
    },
    loanAmountSanctioned: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.$nextTick(() => {
            this.checkAndAddDisbursementForm()
          })
        }
      }
    }
  },
  data() {
    return {
      // orgName: this.$storage.getUniversal('organization_code'),
      valid: true,
      saving: false,
      caseNo: '',
      typeOfLoan: '',
      typeOfLoans: ['Personal', 'Home', 'Auto', 'Gold', 'Business', 'Education', 'Other'],
      customerId: null,
      searchCustomer: '',
      customers: [],
      partnerId: null,
      searchPartner: '',
      partners: [],
      loadingPartners: false,

      // Co-Applicants
      coApplicants: [],
      selectedCoApplicant: null,
      searchCoApplicant: '',
      coApplicantCustomers: [],
      loadingCoApplicantCustomers: false,
      borrowerSharePercentage: 100, // Default to 100% for primary borrower

      // Dynamic Borrowers (new approach)
      dynamicBorrowers: [], // Each item: { customerId, searchTerm, sharePercentage, isSearching }

      // Guarantors
      guarantors: [],
      showGuarantorSearch: false,
      selectedGuarantor: null,
      searchGuarantor: '',
      loanAmountRequested: '',
      tenureMonths: '',
      typeOfInterest: 'Fixed',
      interestRate: '',
      loanAmountSanctioned: '',
      emiStartDate: new Date().toISOString().substr(0, 10),
      emiStartDateMenu: false,
      formattedEmiStartDate: '',
      sanctionDate: new Date().toISOString().substr(0, 10),
      sanctionDateMenu: false,
      formattedSanctionDate: '',
      processingFee: '',

      // Prepayment settings
      prepaymentAllowed: false,
      preEmiBuffer: '',

      // Late payment fee settings
      lateFeeType: 'Fixed',
      latePaymentFee: 0,

      // Processing Fee Split - New Structure
      processingFeeParties: [{ partner: null, self: true, share: 0 }],

      // Additional Charges
      additionalCharges: [{ feeType: '', percentage: 0, linkWithEmi: false }],

      // Collateral
      collateralType: 'None',
      collateralTypes: ['Property', 'Gold', 'Vehicle', 'FD', 'None'],
      collateralDocuments: [],
      collateralRemarks: '',
      uploadedCollateralDocuments: [],

      // Employee selection
      assignedTo: null,
      employees: [],
      searchEmployee: '',
      loadingEmployees: false,

      // Add separate tracking for step reviewers
      stepSearchEmployees: {},
      stepEmployees: {},
      stepLoadingEmployees: {},

      // Loan disbursements
      loanDisbursements: [],
      expandedDisbursementPanel: [],
      disbursementDateMenus: {},
      formattedDisbursementDates: {},
      initialDisbursementCount: 0, // Track original disbursement count for edit mode

      // Disbursement workflow steps
      disbursementStepPanels: {},
      disbursementStepDateMenus: {},
      formattedDisbursementStepDates: {},
      disbursementSteps: [
        'Initiated',
        'Approved',
        'Processed',
        'Transferred',
        'Confirmed',
        'Rejected'
      ],
      selectedLoanWorkflowTemplate: null,
      selectedDisbursementWorkflowTemplate: null,

      // For storing complete workflow data
      loanWorkflowData: null,
      disbursementWorkflowData: {},
      emiWorkflowData: null,
      activeType: 'create'
    }
  },
  methods: {
    setActiveType(type) {
      // If switching from bulk to create, reset form data
      if (this.activeType === 'bulk' && type === 'create') {
        this.resetFormData()
      }
      this.activeType = type
    },

    resetFormData() {
      // Reset form data
      this.$refs.form?.reset()
      this.coApplicants = []
      this.guarantors = []
      this.customerId = null
      this.dynamicBorrowers = []
      this.coApplicantCustomers = []
      this.processingFeeParties = [{ partner: null, self: true, share: 0 }]
      this.additionalCharges = [{ feeType: '', percentage: 0, linkWithEmi: false }]
      this.collateralType = 'None'
      this.collateralRemarks = ''
      this.uploadedCollateralDocuments = []
      this.loanWorkflowData = {}
      this.emiWorkflowData = {}
      this.disbursementWorkflowData = []
    },
    cancel() {
      // Reset active type to create
      this.activeType = 'create'

      // Reset form data
      this.resetFormData()
      this.$emit('caseAdded')
    },
    onCustomerChange() {
      if (!this.customerId) {
        this.searchCustomer = ''
      }
    },
    onPartnerChange() {
      if (!this.partnerId) {
        this.searchPartner = ''
      }
    },
    handleLoanWorkflowSaved(workflowInfo) {
      this.loanWorkflowData = workflowInfo.workflowData
    },
    handleDisbursementWorkflowSaved(index, workflowInfo) {
      this.$set(this.disbursementWorkflowData, index, workflowInfo.workflowData)
    },
    handleEmiWorkflowSaved(workflowInfo) {
      this.emiWorkflowData = workflowInfo.workflowData
    },
    // handleBulkUpload(data) {
    //   this.$emit('caseAdded', data)
    //   this.$toast.success('Bulk loan upload completed successfully')
    // },
    // closeBulkUpload() {
    //   this.activeType = 'create'
    //   // Reset form data when closing bulk upload
    //   this.resetFormData()
    // },
    formattedPfAmount(value) {
      if (value && this.loanAmountSanctioned) {
        const amount = (value / 100) * this.loanAmountSanctioned
        return this.formatCurrency(amount)
      }
      return '₹0'
    },
    formattedAdditionalChargesAmount(value) {
      if (value && this.loanAmountSanctioned) {
        const amount = (value / 100) * this.loanAmountSanctioned
        return this.formatCurrency(amount)
      }
      return '₹0'
    },
    formattedInterestAmount(value) {
      if (value && this.loanAmountSanctioned && this.tenureMonths) {
        const principal = parseFloat(this.loanAmountSanctioned)
        const rate = parseFloat(value)
        const tenureYears = parseFloat(this.tenureMonths) / 12
        const amount = principal * (rate / 100) * tenureYears
        return this.formatCurrency(amount)
      }
      return '₹0'
    },
    debounceCustomer: debounce(async function (text) {
      if (!text) {
        return
      }
      if (this.customerId || this.selectedGuarantor) {
        return
      }
      await this.fetchCustomers()
    }, 800),
    async fetchCustomers() {
      try {
        const params = {
          search: this.searchCustomer,
          limit: 10
        }
        const response = await this.$axios.get('/workforce/customers', { params })
        this.customers = response.data.customers || []
      } catch (error) {
        console.error('Error fetching customers:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to fetch customers'
        this.$toast.error(errorMessage)
        this.customers = []
      }
    },

    async fetchCustomerById(customerId) {
      if (!customerId) return null

      // Check if customer is already in the array
      const existingCustomer = this.customers.find((c) => c._id === customerId)
      if (existingCustomer) {
        return existingCustomer
      }

      try {
        const response = await this.$axios.get(`/workforce/customer/${customerId}`)
        const customer = response.data.customer || response.data
        if (customer) {
          // Add the customer to the customers array
          this.customers.push(customer)
        }
        return customer
      } catch (error) {
        console.error('Error fetching customer by ID:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to fetch customer details'
        this.$toast.error(errorMessage)
        return null
      }
    },
    debouncePartner: debounce(async function (text) {
      if (!text) {
        // this.partners = []
        return
      }
      if (this.partnerId) return
      await this.fetchPartners()
    }, 800),
    async fetchPartners() {
      try {
        this.loadingPartners = true
        const params = {
          search: this.searchPartner,
          limit: 10
        }
        const response = await this.$axios.get('/workforce/ecom/partners', { params })
        this.partners = response.data.partners || []
      } catch (error) {
        console.error('Error fetching partners:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to fetch partners'
        this.$toast.error(errorMessage)
        this.partners = []
      } finally {
        this.loadingPartners = false
      }
    },
    getPartnerName(item) {
      return item.partnerName || 'Unknown Partner'
    },
    getFullName(item) {
      return item.customerFullName
    },
    getInitials(name) {
      if (!name) return '??'
      const names = name.split(' ')
      return names.length >= 2
        ? `${names[0].charAt(0)}${names[1].charAt(0)}`.toUpperCase()
        : name.charAt(0).toUpperCase()
    },

    // Dynamic Borrowers methods
    addBorrowerField() {
      if (!this.customerId && !this.editCase) {
        this.$toast.warning('Please select a primary merchant first')
        return
      }

      // Clear previous search results to prevent auto-opening dropdown
      this.coApplicantCustomers = []
      this.searchCoApplicant = ''

      this.dynamicBorrowers.push({
        customerId: null,
        searchTerm: '',
        sharePercentage: 0,
        isSearching: true
      })
    },

    enableBorrowerSearch(index) {
      // Clear previous search results to start fresh
      this.coApplicantCustomers = []
      this.searchCoApplicant = ''

      // Enable search mode for this borrower
      this.$set(this.dynamicBorrowers[index], 'isSearching', true)
      this.$set(this.dynamicBorrowers[index], 'searchTerm', '')
      this.$set(this.dynamicBorrowers[index], 'customerId', null)
    },

    getSelectedBorrowerName(customerId) {
      if (!customerId) return ''

      // First check in main customers array
      const customer = this.customers.find((c) => c._id === customerId)
      if (customer) {
        return customer.customerFullName
      }

      // Then check in co-applicant customers array
      const coApplicantCustomer = this.coApplicantCustomers.find((c) => c._id === customerId)
      if (coApplicantCustomer) {
        return coApplicantCustomer.customerFullName
      }

      return 'Unknown Customer'
    },

    removeBorrowerField(index) {
      // Get the borrower being removed
      const borrowerToRemove = this.dynamicBorrowers[index]

      // Remove from dynamic borrowers array
      this.dynamicBorrowers.splice(index, 1)

      // Remove from co-applicants array if it exists there
      if (borrowerToRemove && borrowerToRemove.customerId) {
        this.coApplicants = this.coApplicants.filter((ca) => ca._id !== borrowerToRemove.customerId)
      }

      // Clear any search state
      this.coApplicantCustomers = []
      this.searchCoApplicant = ''

      this.calculateBorrowerShare()
    },

    debounceDynamicBorrower: debounce(async function (text, index) {
      if (!text) return
      if (this.dynamicBorrowers[index].customerId) return

      // Use the existing co-applicant search functionality
      this.searchCoApplicant = text
      await this.fetchCoApplicantCustomers()
    }, 800),

    onDynamicBorrowerChange(customerId, index) {
      if (!customerId) return

      const selectedCustomer = this.coApplicantCustomers.find((c) => c._id === customerId)
      if (!selectedCustomer) return

      // Add selected customer to main customers array if not already there
      const existsInMainArray = this.customers.some((c) => c._id === customerId)
      if (!existsInMainArray) {
        this.customers.push(selectedCustomer)
      }

      // Check if already selected as primary customer
      if (customerId === this.customerId) {
        this.$toast.warning('This customer is already selected as primary merchant')
        this.dynamicBorrowers[index].customerId = null
        return
      }

      // Check if already selected in other dynamic borrower fields
      const alreadySelected = this.dynamicBorrowers.some(
        (borrower, i) => i !== index && borrower.customerId === customerId
      )

      if (alreadySelected) {
        this.$toast.warning('This customer is already selected as a co-borrower')
        this.dynamicBorrowers[index].customerId = null
        return
      }

      // Set searching to false after selection
      this.$set(this.dynamicBorrowers[index], 'isSearching', false)

      // Sync to co-applicants array
      this.syncDynamicBorrowersToCoApplicants()
    },

    syncDynamicBorrowersToCoApplicants() {
      // Get current dynamic borrower IDs
      const dynamicBorrowerIds = this.dynamicBorrowers
        .filter((db) => db.customerId)
        .map((db) => db.customerId)

      // Remove co-applicants that are no longer in dynamic borrowers
      this.coApplicants = this.coApplicants.filter((ca) => dynamicBorrowerIds.includes(ca._id))

      // Add new dynamic borrowers to co-applicants (avoid duplicates)
      this.dynamicBorrowers.forEach((borrower) => {
        if (borrower.customerId) {
          // Check if already exists in co-applicants
          const existingIndex = this.coApplicants.findIndex((ca) => ca._id === borrower.customerId)

          const customer = this.customers.find((c) => c._id === borrower.customerId)
          if (customer) {
            const coApplicantWithShare = {
              ...customer,
              sharePercentage: borrower.sharePercentage || 0
            }

            if (existingIndex >= 0) {
              // Update existing co-applicant
              this.$set(this.coApplicants, existingIndex, coApplicantWithShare)
            } else {
              // Add new co-applicant
              this.coApplicants.push(coApplicantWithShare)
            }
          }
        }
      })

      this.calculateBorrowerShare()
    },

    // Co-Applicants methods (kept for backward compatibility)
    debounceCoApplicant: debounce(async function (text) {
      if (!text) {
        this.coApplicantCustomers = []
        return
      }
      if (this.selectedCoApplicant) return
      await this.fetchCoApplicantCustomers()
    }, 800),

    async fetchCoApplicantCustomers() {
      try {
        this.loadingCoApplicantCustomers = true
        const params = {
          search: this.searchCoApplicant,
          limit: 10
        }
        const response = await this.$axios.get('/workforce/customers', { params })
        // Filter out already selected co-applicants and main customer
        const existingIds = [this.customerId, ...this.coApplicants.map((ca) => ca._id)].filter(
          Boolean
        )

        this.coApplicantCustomers = (response.data.customers || []).filter(
          (customer) => !existingIds.includes(customer._id)
        )
      } catch (error) {
        console.error('Error fetching co-applicant customers:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to fetch customers'
        this.$toast.error(errorMessage)
        this.coApplicantCustomers = []
      } finally {
        this.loadingCoApplicantCustomers = false
      }
    },

    addCoApplicant() {
      if (!this.selectedCoApplicant) return

      const selectedCustomer = this.coApplicantCustomers.find(
        (customer) => customer._id === this.selectedCoApplicant
      )

      if (selectedCustomer) {
        // Check if already added
        const alreadyExists = this.coApplicants.some((ca) => ca._id === selectedCustomer._id)
        if (alreadyExists) {
          this.$toast.warning('This customer is already added as co-applicant')
          return
        }

        // Check if it's the main customer
        if (selectedCustomer._id === this.customerId) {
          this.$toast.warning('Main customer cannot be added as co-applicant')
          return
        }

        // Add share percentage property to the co-applicant
        const coApplicantWithShare = {
          ...selectedCustomer,
          sharePercentage: 0 // Default to 0%, user can modify
        }

        this.coApplicants.push(coApplicantWithShare)
      }
    },

    removeCoApplicant(index) {
      const coApplicant = this.coApplicants[index]

      // Remove from co-applicants array
      this.coApplicants.splice(index, 1)

      // Also remove from dynamic borrowers array if it exists there
      const dynamicBorrowerIndex = this.dynamicBorrowers.findIndex(
        (db) => db.customerId === coApplicant._id
      )
      if (dynamicBorrowerIndex >= 0) {
        this.dynamicBorrowers.splice(dynamicBorrowerIndex, 1)
      }

      this.calculateBorrowerShare() // Recalculate borrower share
      this.$toast.success(`${coApplicant.customerFullName} removed from co-applicants`)
    },
    debounceGuarantorSearch: debounce(async function (text) {
      if (!text) {
        return
      }
      // Set the search term and fetch customers
      this.searchCustomer = text
      await this.fetchCustomers()
    }, 800),

    addGuarantor() {
      if (!this.selectedGuarantor) {
        this.$toast.error('Please select a guarantor')
        return
      }

      const selectedCustomer = this.customers.find(
        (customer) => customer._id === this.selectedGuarantor
      )

      if (!selectedCustomer) {
        this.$toast.error('Selected customer not found. Please search again.')
        return
      }

      if (selectedCustomer) {
        // Check if already added
        const alreadyExists = this.guarantors.some((g) => g._id === selectedCustomer._id)
        if (alreadyExists) {
          this.$toast.warning('This customer is already added as guarantor')
          return
        }

        // Check if it's the main customer
        if (selectedCustomer._id === this.customerId) {
          this.$toast.warning('Main customer cannot be added as guarantor')
          return
        }

        // Check if it's a co-applicant
        const isCoApplicant = this.coApplicants.some((ca) => ca._id === selectedCustomer._id)
        if (isCoApplicant) {
          this.$toast.warning('Co-applicant cannot be added as guarantor')
          return
        }

        this.guarantors.push(selectedCustomer)
        this.cancelGuarantorSearch()
        this.$toast.success('Guarantor added successfully')
      }
    },

    removeGuarantor(index) {
      this.guarantors.splice(index, 1)
    },

    // Calculate borrower share based on co-applicants shares
    calculateBorrowerShare() {
      const coApplicantsTotal = this.coApplicantsTotalShare
      this.borrowerSharePercentage = Math.max(0, 100 - coApplicantsTotal)

      // Show warning if over-allocated
      if (coApplicantsTotal > 100) {
        this.$toast.warning('Co-applicant shares exceed 100%. Please adjust the percentages.')
      }
    },

    // Format share amount based on percentage and loan amount
    formatShareAmount(percentage) {
      if (!percentage || !this.loanAmountSanctioned) return '0'
      if (percentage > 100) return '100'
      const amount = (parseFloat(percentage) / 100) * parseFloat(this.loanAmountSanctioned)
      return new Intl.NumberFormat('en-IN').format(amount)
    },

    // Get selected customer name
    getSelectedCustomerName(item) {
      const customer = this.customers.find((c) => c._id === this.customerId)
      return customer ? customer.customerFullName : item?.customer?.customerFullName
    },

    // Workflow steps methods
    addWorkflowStep() {
      const newIndex = this.loanWorkflowSteps.length
      this.loanWorkflowSteps.push({
        step: '',
        status: 'Pending',
        documents: [],
        updatedBy: null,
        remarks: '',
        updatedAt: new Date().toISOString().substr(0, 10)
      })

      // Initialize date menu and formatted date
      this.$set(this.dateMenus, newIndex, false)
      this.$set(this.formattedDates, newIndex, this.$dayjs(new Date()).format('DD/MM/YYYY'))

      // Initialize step employee search
      this.$set(this.stepSearchEmployees, newIndex, '')
      this.$set(this.stepEmployees, newIndex, [])
      this.$set(this.stepLoadingEmployees, newIndex, false)

      // Expand the newly added panel
      this.expandedPanel = [...this.expandedPanel, newIndex]
    },
    removeWorkflowStep(index) {
      this.loanWorkflowSteps.splice(index, 1)

      // Update expanded panels
      this.expandedPanel = this.expandedPanel
        .filter((i) => i !== index)
        .map((i) => (i > index ? i - 1 : i))
    },

    formatStepDate(index) {
      this.formattedDates[index] = this.$dayjs(this.loanWorkflowSteps[index].updatedAt).format(
        'DD/MM/YYYY'
      )
      this.dateMenus[index] = false
    },

    // Status color
    getStatusColor(status) {
      switch (status) {
        case 'Completed':
          return 'success'
        case 'Rejected':
          return 'error'
        case 'Pending':
        default:
          return 'warning'
      }
    },

    // Employee methods
    debounceEmployee: debounce(async function (text) {
      if (!text) {
        return
      }
      if (this.assignedTo) return
      await this.fetchEmployees()
    }, 800),
    async fetchEmployees() {
      try {
        this.loadingEmployees = true
        const params = {
          search: this.searchEmployee,
          limit: 10
        }
        const response = await this.$axios.get('/workforce/v2/users', { params })
        this.employees = response.data.users || []
      } catch (error) {
        console.error('Error fetching employees:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to fetch employees'
        this.$toast.error(errorMessage)
        this.employees = []
      } finally {
        this.loadingEmployees = false
      }
    },
    getEmployeeName(item) {
      return `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Unknown Employee'
    },

    // Add new method for step employee search
    debounceStepEmployee: debounce(async function (text, index) {
      if (!text) return
      if (this.loanWorkflowSteps[index].updatedBy) return
      await this.fetchStepEmployees(index)
    }, 800),

    async fetchStepEmployees(index) {
      try {
        this.$set(this.stepLoadingEmployees, index, true)
        const params = {
          search: this.stepSearchEmployees[index] || '',
          limit: 10
        }
        const response = await this.$axios.get('/workforce/v2/users', { params })
        this.$set(this.stepEmployees, index, response.data.users || [])
      } catch (error) {
        console.error('Error fetching employees for step:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to fetch employees'
        this.$toast.error(errorMessage)
        this.$set(this.stepEmployees, index, [])
      } finally {
        this.$set(this.stepLoadingEmployees, index, false)
      }
    },

    // Loan disbursements methods
    addDisbursement() {
      const newIndex = this.loanDisbursements.length
      this.loanDisbursements.push({
        amount: '',
        date: new Date().toISOString().substr(0, 10),
        status: 'Pending',
        paymentMode: 'Bank Transfer',
        referenceNo: '',
        remarks: '',
        bankDetails: {
          bankName: '',
          accountNumber: '',
          ifscCode: ''
        },
        chequeDetails: {
          chequeNumber: '',
          bankName: '',
          branch: ''
        }
      })

      // Initialize date menu and formatted date
      this.$set(this.disbursementDateMenus, newIndex, false)
      this.$set(
        this.formattedDisbursementDates,
        newIndex,
        this.$dayjs(new Date()).format('DD/MM/YYYY')
      )
      // Expand the newly added panel
      this.expandedDisbursementPanel = [...this.expandedDisbursementPanel, newIndex]
    },

    // Initialize default disbursement form
    initializeDefaultDisbursement() {
      if (this.loanDisbursements.length === 0 && !this.isFullyDisbursed) {
        this.addDisbursement()
      }
    },

    // Check if we need to add another disbursement form
    checkAndAddDisbursementForm() {
      const sanctionedAmount = parseFloat(this.loanAmountSanctioned) || 0
      const totalDisbursed = this.totalDisbursementAmount

      // If in edit mode and there's remaining amount to disburse, add one more form
      if (this.editCase && totalDisbursed < sanctionedAmount && sanctionedAmount > 0) {
        const hasEmptyForm = this.loanDisbursements.some(
          (d) => !d.amount || parseFloat(d.amount) === 0
        )
        if (!hasEmptyForm) {
          this.addDisbursement()
        }
      }
    },
    removeDisbursement(index) {
      this.loanDisbursements.splice(index, 1)

      // Update expanded panels
      this.expandedDisbursementPanel = this.expandedDisbursementPanel
        .filter((i) => i !== index)
        .map((i) => (i > index ? i - 1 : i))
    },
    formatDisbursementDate(index) {
      this.formattedDisbursementDates[index] = this.$dayjs(
        this.loanDisbursements[index].date
      ).format('DD/MM/YYYY')
      this.disbursementDateMenus[index] = false
    },
    handlePaymentModeChange(index) {
      const disbursement = this.loanDisbursements[index]
      if (disbursement.paymentMode === 'Bank Transfer' || disbursement.paymentMode === 'Online') {
        disbursement.chequeDetails = {
          chequeNumber: '',
          bankName: '',
          branch: ''
        }
      } else if (disbursement.paymentMode === 'Cheque') {
        disbursement.bankDetails = {
          bankName: '',
          accountNumber: '',
          ifscCode: ''
        }
      }
    },
    formatCurrency(value) {
      if (!value || value === null || value === undefined) return 0
      return parseFloat(value).toLocaleString('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0
      })
    },

    // Processing Fee Split Methods - Updated for new structure
    addFeeParty() {
      if (this.processingFeeParties.length >= 2) return
      this.processingFeeParties.push({ partner: this.partnerId, self: false, share: 0 })
    },

    calculateTotalProcessingFee() {
      const total = this.processingFeeParties.reduce((sum, party) => {
        return sum + (parseFloat(party.share) || 0)
      }, 0)
      this.processingFee = total.toString()
    },

    calculatePartyAmount(percentage) {
      if (!percentage || !this.loanAmountSanctioned) return 0
      const amount = (parseFloat(percentage) / 100) * parseFloat(this.loanAmountSanctioned)
      return amount
    },

    // Additional Charges Methods
    addAdditionalCharge() {
      this.additionalCharges.push({ feeType: '', percentage: 0, linkWithEmi: false })
    },

    removeAdditionalCharge(index) {
      this.additionalCharges.splice(index, 1)
      this.calculateTotalAdditionalCharges()
    },

    calculateTotalAdditionalCharges() {
      // This is just for display purposes, no need to store total
    },

    calculateChargeAmount(percentage) {
      if (!percentage || !this.loanAmountSanctioned) return 0
      const amount = (parseFloat(percentage) / 100) * parseFloat(this.loanAmountSanctioned)
      return amount
    },

    // Collateral Methods
    handleCollateralDocuments(files) {
      if (files && files.length > 0) {
        const fileNames = files.map((file) => file.name)
        this.uploadedCollateralDocuments = [...this.uploadedCollateralDocuments, ...fileNames]
      }
    },

    removeCollateralDocument(index) {
      this.uploadedCollateralDocuments.splice(index, 1)
    },

    // Disbursement workflow steps methods
    addDisbursementStep(disbursementIndex) {
      if (!this.loanDisbursements[disbursementIndex].disbursementWorkflowSteps) {
        this.$set(this.loanDisbursements[disbursementIndex], 'disbursementWorkflowSteps', [])
      }

      const newStepIndex =
        this.loanDisbursements[disbursementIndex].disbursementWorkflowSteps.length

      this.loanDisbursements[disbursementIndex].disbursementWorkflowSteps.push({
        step: '',
        status: 'Pending',
        updatedAt: new Date().toISOString().substr(0, 10),
        updatedBy: null,
        remarks: ''
      })

      // Initialize date menu and formatted date for this step
      const stepKey = `${disbursementIndex}-${newStepIndex}`
      this.$set(this.disbursementStepDateMenus, stepKey, false)
      this.$set(
        this.formattedDisbursementStepDates,
        stepKey,
        this.$dayjs(new Date()).format('DD/MM/YYYY')
      )

      // Expand this step panel
      if (!this.disbursementStepPanels[disbursementIndex]) {
        this.$set(this.disbursementStepPanels, disbursementIndex, [])
      }
      this.disbursementStepPanels[disbursementIndex].push(newStepIndex)
    },

    removeDisbursementStep(disbursementIndex, stepIndex) {
      this.loanDisbursements[disbursementIndex].disbursementWorkflowSteps.splice(stepIndex, 1)

      // Update expanded panels
      if (this.disbursementStepPanels[disbursementIndex]) {
        this.disbursementStepPanels[disbursementIndex] = this.disbursementStepPanels[
          disbursementIndex
        ]
          .filter((i) => i !== stepIndex)
          .map((i) => (i > stepIndex ? i - 1 : i))
      }
    },

    formatDisbursementStepDate(disbursementIndex, stepIndex) {
      const key = `${disbursementIndex}-${stepIndex}`
      const step = this.loanDisbursements[disbursementIndex].disbursementWorkflowSteps[stepIndex]

      this.formattedDisbursementStepDates[key] = this.$dayjs(step.updatedAt).format('DD/MM/YYYY')
      this.disbursementStepDateMenus[key] = false
    },

    async saveLoanDetails() {
      // Delegate to bulk uploader when in bulk mode
      if (this.activeType === 'bulk') {
        this.$refs.bulkUploader?.processFile()
        return
      }

      if (!this.$refs.form?.validate()) {
        this.$toast.error('Please fill all required fields')
        return
      }

      // Validate share distribution if co-applicants exist
      // if (this.coApplicants.length > 0 && this.totalSharePercentage !== 100) {
      //   this.$toast.error(
      //     `Share distribution must equal 100%. Current total: ${this.totalSharePercentage.toFixed(
      //       1
      //     )}%`
      //   )
      //   return
      // }

      try {
        this.saving = true

        // Create payload with basic loan details
        const payload = {
          caseNo: this.caseNo,
          typeOfLoan: this.typeOfLoan,
          customer: this.customerId,
          partner: this.partnerId,
          coApplicants: this.coApplicants.map((ca) => ({
            customer: ca._id,
            share: parseFloat(ca.sharePercentage) || 0
          })),
          guarantors: this.guarantors.map((g) => g._id),
          borrowerSharePercentage: this.borrowerSharePercentage,
          loanAmountRequested: parseFloat(this.loanAmountRequested),
          tenure: parseInt(this.tenureMonths),
          typeOfInterest: this.typeOfInterest,
          interestRate: parseFloat(this.interestRate),
          sanctionedAmount: parseFloat(this.sanctionedAmount),
          assignedTo: this.assignedTo,
          caseType: this.caseConfig?.name,
          useCase: this.caseConfig?.useCase,
          subCase: this.caseConfig?._id,
          emiStartDate: this.emiStartDate,
          loanAmountSanctioned: parseFloat(this.loanAmountSanctioned),
          sanctionDate: this.sanctionDate,
          processingFee: this.processingFeeParties.map((party) => ({
            partner: party.self ? null : party.partner,
            self: party.self,
            share: parseFloat(party.share) || 0
          })),
          additionalCharges: this.additionalCharges.map((charge) => ({
            feeType: charge.feeType,
            percentage: parseFloat(charge.percentage) || 0,
            linkWithEmi: charge.linkWithEmi
          })),
          collateralType: this.collateralType,
          collateralDetails: {
            documents: this.uploadedCollateralDocuments,
            remarks: this.collateralRemarks
          },
          emiFrequency: this.emiWorkflowData?.emiFrequency || null,
          lateFeeValue: this.emiWorkflowData?.lateFeeValue || 0,
          // lateFeeType: this.emiWorkflowData?.lateFeeType || undefined,

          // Prepayment settings
          prepaymentAllowed: this.prepaymentAllowed,
          preEmiBuffer: this.prepaymentAllowed ? parseInt(this.preEmiBuffer) || null : null,

          // Late payment fee settings
          lateFeeType: this.lateFeeType,
          latePaymentFee: parseFloat(this.latePaymentFee) || 0,

          // Add complete workflow data for the loan
          loanWorkflowData: this.loanWorkflowData
            ? {
                workflowData: this.loanWorkflowData
              }
            : undefined,

          // Add complete workflow data for EMI
          emiWorkflowData: this.emiWorkflowData
            ? {
                workflowData: this.emiWorkflowData
              }
            : undefined,

          // Map disbursements with their complete workflow data
          loanDisbursementsData: this.loanDisbursements.map((disbursement, index) => ({
            ...disbursement,
            date: disbursement.date,
            amount: parseFloat(disbursement.amount),
            bankDetails:
              disbursement.paymentMode === 'Bank Transfer' || disbursement.paymentMode === 'Online'
                ? disbursement.bankDetails
                : null,
            chequeDetails:
              disbursement.paymentMode === 'Cheque' ? disbursement.chequeDetails : null,
            workflowData: this.disbursementWorkflowData[index] || undefined
          }))
        }
        // console.log(payload)
        // return
        let response
        if (this.editCase) {
          response = await this.$axios.put(
            `/workforce/collection/loan/${this.editCase._id}`,
            payload
          )
        } else {
          response = await this.$axios.post('/workforce/collection/loan', payload)
        }

        this.$toast.success(`Loan details ${this.editCase ? 'updated' : 'added'} successfully`)
        this.$emit('caseAdded', response.data)
      } catch (error) {
        console.error('Error saving loan details:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to save loan details'
        this.$toast.error(errorMessage)
      } finally {
        this.saving = false
      }
    },
    formatEmiStartDate() {
      this.formattedEmiStartDate = this.$dayjs(this.emiStartDate).format('DD/MM/YYYY')
      this.emiStartDateMenu = false
    },
    formatSanctionDate() {
      this.formattedSanctionDate = this.$dayjs(this.sanctionDate).format('DD/MM/YYYY')
      this.sanctionDateMenu = false
    },
    // Populate form with existing data
    async populateForm() {
      // console.log(this.editCase, 'editCase')
      if (!this.editCase) return
      // Basic loan details
      this.caseNo = this.editCase.caseNo || ''
      this.typeOfLoan = this.editCase.typeOfLoan || ''
      this.customerId = this.editCase.customer?._id || null
      this.partnerId = this.editCase.loanDetails?.partner?._id || null
      this.loanAmountRequested = this.editCase.loanDetails?.loanAmountRequested?.toString() || ''
      this.tenureMonths = this.editCase.loanDetails?.tenure?.toString() || ''
      this.typeOfInterest = this.editCase.loanDetails?.typeOfInterest || 'Fixed'
      this.interestRate = this.editCase.loanDetails?.interestRate?.toString() || ''
      this.loanAmountSanctioned = this.editCase.loanDetails?.loanAmountSanctioned?.toString() || ''
      this.sanctionDate =
        this.editCase.loanDetails?.sanctionDate || new Date().toISOString().substr(0, 10)
      this.formattedSanctionDate = this.sanctionDate
        ? this.$dayjs(this.sanctionDate).format('DD/MM/YYYY')
        : ''

      // Prepayment settings
      this.prepaymentAllowed = this.editCase.loanDetails?.prepaymentAllowed || false
      this.preEmiBuffer = this.editCase.loanDetails?.preEmiBuffer?.toString() || ''

      // Late payment fee settings
      this.lateFeeType = this.editCase.loanDetails?.lateFeeType || 'Fixed'
      this.latePaymentFee = this.editCase.loanDetails?.latePaymentFee || 0

      // Populate processing fee parties if they exist
      if (
        this.editCase.loanDetails?.processingFee &&
        this.editCase.loanDetails.processingFee.length > 0
      ) {
        this.processingFeeParties = this.editCase.loanDetails.processingFee.map((party) => ({
          partner: party.partner || null,
          self: party.self || false,
          share: party.share || 0
        }))
      } else {
        this.processingFeeParties = [{ partner: null, self: true, share: 0 }]
      }

      // Populate additional charges if they exist
      if (
        this.editCase.loanDetails?.additionalCharges &&
        this.editCase.loanDetails.additionalCharges.length > 0
      ) {
        this.additionalCharges = this.editCase.loanDetails.additionalCharges.map((charge) => ({
          feeType: charge.feeType || '',
          percentage: charge.percentage || 0,
          linkWithEmi: charge.linkWithEmi || false
        }))
      } else {
        this.additionalCharges = []
      }

      // Populate collateral data if it exists
      if (this.editCase.loanDetails?.collateralType) {
        this.collateralType = this.editCase.loanDetails.collateralType
      }

      if (this.editCase.loanDetails?.collateralDetails) {
        this.uploadedCollateralDocuments =
          this.editCase.loanDetails.collateralDetails.documents || []
        this.collateralRemarks = this.editCase.loanDetails.collateralDetails.remarks || ''
      }
      this.emiStartDate =
        this.editCase.loanDetails?.emiStartDate || new Date().toISOString().substr(0, 10)
      this.formattedEmiStartDate = this.emiStartDate
        ? this.$dayjs(this.emiStartDate).format('DD/MM/YYYY')
        : ''
      this.assignedTo = this.editCase.assignedTo[0] || null
      // Workflow data - properly structure for WorkflowStepRenderer
      if (this.editCase.loanDetails?.loanWorkflow) {
        // If it's already in the correct format, use it directly
        if (
          this.editCase.loanDetails.loanWorkflow.templateId &&
          this.editCase.loanDetails.loanWorkflow.steps
        ) {
          this.loanWorkflowData = this.editCase.loanDetails.loanWorkflow
        } else {
          // If it's in old format, transform it
          this.loanWorkflowData = {
            templateId: this.editCase.loanDetails.loanWorkflow.templateId || null,
            entityType: 'LOAN',
            steps: this.editCase.loanDetails.loanWorkflow.steps || []
          }
        }
      }

      if (this.editCase.loanDetails?.emiWorkflow) {
        // If it's already in the correct format, use it directly
        if (
          this.editCase.loanDetails.emiWorkflow.templateId &&
          this.editCase.loanDetails.emiWorkflow.steps
        ) {
          this.emiWorkflowData = this.editCase.loanDetails.emiWorkflow
        } else {
          // If it's in old format, transform it
          this.emiWorkflowData = {
            templateId: this.editCase.loanDetails.emiWorkflow.templateId || null,
            entityType: 'EMI',
            steps: this.editCase.loanDetails.emiWorkflow.steps || []
          }
        }
      }

      // Fetch related data
      if (this.customerId) {
        await this.fetchCustomerById(this.customerId)
      }

      if (this.partnerId) {
        this.searchPartner = this.editCase.partner?.partnerName || ''
        this.fetchPartners()
      }

      if (this.assignedTo) {
        this.searchEmployee = `${this.editCase.assignedToUser?.firstName || ''} ${
          this.editCase.assignedToUser?.lastName || ''
        }`.trim()
        this.fetchEmployees()
      }

      // Populate co-applicants with share percentages
      if (
        this.editCase.loanDetails.coApplicants &&
        this.editCase.loanDetails?.coApplicants.length > 0
      ) {
        this.coApplicants = await Promise.all(
          this.editCase.loanDetails.coApplicants.map(async (coApplicantData) => {
            // Handle both old format (just ID) and new format (object with customer and share)
            const customerId = coApplicantData.customer || coApplicantData
            const share = coApplicantData.share || 0

            const coApplicant = await this.fetchCustomerById(customerId)
            return {
              _id: coApplicant._id || coApplicant.customerId,
              customerFullName: coApplicant.customerFullName,
              email: coApplicant.email,
              phone: coApplicant.phone,
              sharePercentage: share
            }
          })
        )

        // Populate dynamic borrowers from co-applicants for edit mode
        this.dynamicBorrowers = this.coApplicants.map((ca) => ({
          customerId: ca._id,
          searchTerm: '',
          sharePercentage: ca.sharePercentage,
          isSearching: false
        }))

        // Calculate borrower share based on existing co-applicant shares
        this.calculateBorrowerShare()
      }

      // Set borrower share percentage if available
      if (this.editCase.loanDetails?.borrowerSharePercentage !== undefined) {
        this.borrowerSharePercentage = this.editCase.loanDetails.borrowerSharePercentage
      }

      // Populate guarantors
      if (
        this.editCase.loanDetails.guarantors &&
        this.editCase.loanDetails?.guarantors.length > 0
      ) {
        this.guarantors = await Promise.all(
          this.editCase.loanDetails.guarantors.map(async (guarantorId) => {
            const guarantor = await this.fetchCustomerById(guarantorId)
            return {
              _id: guarantor._id || guarantor.customerId,
              customerFullName: guarantor.customerFullName,
              email: guarantor.email,
              phone: guarantor.phone
            }
          })
        )
      }

      // Populate loan disbursements
      if (
        this.editCase.loanDetails?.loanDisbursements &&
        this.editCase.loanDetails?.loanDisbursements.length > 0
      ) {
        // Set initial disbursement count for edit mode tracking
        this.initialDisbursementCount = this.editCase.loanDetails.loanDisbursements.length

        this.loanDisbursements = this.editCase.loanDetails?.loanDisbursements.map(
          (disbursement, index) => {
            // Initialize disbursement step panels
            this.$set(this.disbursementStepPanels, index, [])

            // Format disbursement date
            this.$set(this.disbursementDateMenus, index, false)
            this.$set(
              this.formattedDisbursementDates,
              index,
              this.$dayjs(disbursement.date).format('DD/MM/YYYY')
            )

            // Set workflow data if it exists
            if (disbursement.workflow) {
              // If it's already in the correct format, use it directly
              if (disbursement.workflow.templateId && disbursement.workflow.steps) {
                this.$set(this.disbursementWorkflowData, index, disbursement.workflow)
              } else {
                // If it's in old format, transform it
                this.$set(this.disbursementWorkflowData, index, {
                  templateId: disbursement.workflow.templateId || null,
                  entityType: 'DISBURSEMENT',
                  steps: disbursement.workflow.steps || []
                })
              }
            }

            return {
              ...disbursement,
              date: disbursement.date,
              amount: disbursement.amount.toString(),
              bankDetails: disbursement.bankDetails || {},
              chequeDetails: disbursement.chequeDetails || {}
            }
          }
        )

        this.expandedDisbursementPanel = this.loanDisbursements.map((_, index) => index)
      } else {
        this.initialDisbursementCount = 0
      }

      // After populating, check if we need to add another disbursement form
      this.$nextTick(() => {
        this.checkAndAddDisbursementForm()
      })
    }
  },
  computed: {
    processingFeeRules() {
      return [(v) => (v > -1 && v <= 100) || 'Interest Rate must be between 0 and 100']
    },
    loanLang() {
      return this.$props.caseConfig?.fieldMappings
    },

    orgName() {
      // Get organization name from storage or default to 'QualityWork'
      const orgData = this.$storage.getUniversal('organization_name')
      return orgData || 'CualityWork'
    },

    // Filter customers available for guarantor selection
    availableGuarantorCustomers() {
      if (!this.customers || this.customers.length === 0) {
        return []
      }

      // Filter out already selected guarantors, co-applicants, and main customer
      const existingIds = [
        this.customerId,
        ...this.coApplicants.map((ca) => ca._id),
        ...this.guarantors.map((g) => g._id)
      ].filter(Boolean)

      return this.customers.filter((customer) => !existingIds.includes(customer._id))
    },

    // Calculate total disbursement amount
    totalDisbursementAmount() {
      return this.loanDisbursements.reduce((total, disbursement) => {
        return total + (parseFloat(disbursement.amount) || 0)
      }, 0)
    },

    // Check if the loan is fully disbursed
    isFullyDisbursed() {
      const sanctionedAmount = parseFloat(this.loanAmountSanctioned) || 0
      return this.totalDisbursementAmount >= sanctionedAmount && sanctionedAmount > 0
    },

    // Calculate formatted late fee amount for percentage type
    formattedLateFeeAmount() {
      if (
        this.lateFeeType === 'Percentage' &&
        this.latePaymentFee > 0 &&
        this.loanAmountSanctioned
      ) {
        // Calculate based on a typical EMI amount (simplified calculation)
        const principal = parseFloat(this.loanAmountSanctioned) || 0
        const rate = parseFloat(this.interestRate) || 0
        const tenure = parseFloat(this.tenureMonths) || 1

        // Simple EMI calculation: P * r * (1+r)^n / ((1+r)^n - 1)
        const monthlyRate = rate / (12 * 100)
        const emiAmount =
          monthlyRate > 0
            ? (principal * monthlyRate * Math.pow(1 + monthlyRate, tenure)) /
              (Math.pow(1 + monthlyRate, tenure) - 1)
            : principal / tenure

        const lateFeeAmount = (parseFloat(this.latePaymentFee) / 100) * emiAmount
        return this.formatCurrency(lateFeeAmount)
      }
      return '₹0'
    },

    // Always show disbursement forms, but disable empty ones when fully disbursed
    shouldShowDisbursementForms() {
      return true
    },

    // Dynamic button text based on context
    // addDisbursementButtonText() {
    //   if (!this.editCase || this.initialDisbursementCount === 0) {
    //     return 'Add Disbursement'
    //   }

    //   return 'Add Next Disbursement'
    // },

    // Calculate total processing fee percentage from all parties
    totalProcessingFeePercentage() {
      return this.processingFeeParties.reduce((sum, party) => {
        return sum + (parseFloat(party.share) || 0)
      }, 0)
    },

    // Calculate total additional charges percentage
    totalAdditionalChargesPercentage() {
      return this.additionalCharges.reduce((sum, charge) => {
        return sum + (parseFloat(charge.percentage) || 0)
      }, 0)
    },

    // Calculate total share percentage from co-applicants
    coApplicantsTotalShare() {
      return this.coApplicants.reduce((sum, coApplicant) => {
        return sum + (parseFloat(coApplicant.sharePercentage) || 0)
      }, 0)
    },

    // Calculate total share percentage (borrower + co-applicants)
    totalSharePercentage() {
      return this.borrowerSharePercentage + this.coApplicantsTotalShare
    },

    // Get selected customer name for display
    selectedCustomerName() {
      const customer = this.customers.find((c) => c._id === this.customerId)
      return customer ? customer.customerFullName : 'Select Customer'
    },

    // Get all borrowers (primary + dynamic)
    allBorrowers() {
      const borrowers = []

      // Add primary borrower
      if (this.customerId) {
        const primaryCustomer = this.customers.find((c) => c._id === this.customerId)
        if (primaryCustomer) {
          borrowers.push({
            ...primaryCustomer,
            isPrimary: true,
            sharePercentage: this.borrowerSharePercentage
          })
        }
      }

      // Add co-applicants
      borrowers.push(
        ...this.coApplicants.map((ca) => ({
          ...ca,
          isPrimary: false
        }))
      )

      return borrowers
    }
  },
  async mounted() {
    // Initialize first processing fee party as self (organization)
    if (this.processingFeeParties.length > 0 && this.processingFeeParties[0].self) {
      this.processingFeeParties[0].self = true
    }

    // Fetch partners for fee party dropdown
    if (this.partners.length === 0) {
      await this.fetchPartners()
    }

    // Initialize default disbursement form for new loans
    if (!this.editCase) {
      this.initializeDefaultDisbursement()
    }
    // populateForm is handled by the watch handler with immediate: true
  }
}
</script>

<style scoped>
/* Late Payment Fee Section Styles */
.late-payment-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.late-payment-header {
  margin-bottom: 12px;
}

.late-payment-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #21427d;
  margin: 0;
  display: flex;
  align-items: center;
}

.late-payment-content {
  background-color: white;
  border-radius: 4px;
  padding: 12px;
}

.late-fee-info .v-alert {
  border-radius: 4px;
}

/* Readonly field styling */
.readonly-field :deep(.v-input__slot) {
  background-color: #f5f5f5 !important;
}

.readonly-field :deep(.v-text-field__slot input) {
  color: #666 !important;
}
</style>
<style scoped>
.loan-form-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05) !important;
  margin: 0 auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #f8f9fa;
}

.form-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #21427d;
  margin: 0;
}

.form-content {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1rem;
  font-weight: 500;
  color: #21427d;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-content {
  padding-left: 8px;
}

.add-step-btn {
  text-transform: none;
  font-weight: 500;
}

.workflow-panels {
  background: transparent;
}

.workflow-step-panel {
  margin-bottom: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: none !important;
}

.workflow-step-panel:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.workflow-panel-header {
  padding: 12px 16px;
  min-height: 48px;
  background-color: #f8f9fa;
}

.workflow-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.step-info {
  display: flex;
  align-items: center;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #21427d;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  margin-right: 12px;
}

.step-name {
  font-weight: 500;
  margin-right: 12px;
}

.status-chip {
  font-size: 0.625rem;
  height: 20px !important;
}

.workflow-panel-content {
  padding: 16px;
}

.fully-disbursed-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  background-color: #f0f8f0;
  border-radius: 8px;
  border: 1px solid #4caf50;
}

.fully-disbursed-message p {
  margin-top: 16px;
  color: #2e7d32;
  text-align: center;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  background-color: #f8f9fa;
}

.cancel-btn {
  margin-right: 12px;
}

.save-btn {
  min-width: 120px;
  text-transform: none;
  font-weight: 500;
}

/* Dark mode support */
:deep(.theme--dark) .form-header,
:deep(.theme--dark) .form-actions,
:deep(.theme--dark) .workflow-panel-header {
  background-color: #2a2a2a;
}

:deep(.theme--dark) .form-title {
  color: #8cb6e1;
}

:deep(.theme--dark) .section-title {
  color: #8cb6e1;
}

:deep(.theme--dark) .fully-disbursed-message {
  background-color: #1b2e1b;
  border-color: #4caf50;
}

:deep(.theme--dark) .fully-disbursed-message p {
  color: #81c784;
}

/* Disabled disbursement styling */
.disbursement-disabled {
  opacity: 0.6;
}

.disbursement-disabled .workflow-panel-header {
  background-color: #f5f5f5 !important;
}

:deep(.theme--dark) .disbursement-disabled .workflow-panel-header {
  background-color: #2a2a2a !important;
}

:deep(.theme--dark) .workflow-step-panel {
  border-color: #444;
}

.payment-details-section {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
  background-color: #f9f9f9;
}

.payment-details-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #21427d;
  margin-bottom: 8px;
}

.disbursement-steps-section {
  border-top: 1px dashed #e0e0e0;
  padding-top: 16px;
}

.disbursement-step-panels {
  background: transparent;
}

.disbursement-step-panel {
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: none !important;
}

.disbursement-step-header {
  padding: 8px 12px;
  min-height: 40px;
  background-color: #f8f9fa;
}

.disbursement-step-content {
  padding: 12px;
}

:deep(.theme--dark) .payment-details-section {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .payment-details-title {
  color: #aaa;
}

/* Processing Fee Split Styles - Compact */
.processing-fee-section,
.additional-charges-section,
.collateral-section {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.form-content-border {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  background-color: #ffff;
  margin-bottom: 16px;
}

.fee-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.fee-title {
  font-size: 0.85rem;
  font-weight: 500;
  color: #21427d;
  margin: 0;
  display: flex;
  align-items: center;
}

.add-party-btn-compact {
  text-transform: none;
  font-weight: 500;
  font-size: 0.75rem;
}

.fee-parties-compact {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.fee-party-compact {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 4px;
}

.fee-party-compact:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.fee-party-content {
  display: grid;
  grid-template-columns: 4fr 2fr 1fr auto;
  gap: 8px;
  align-items: center;
}

.party-name-section,
.party-percentage-section {
  min-width: 0;
}

.party-name-field,
.party-percentage-field {
  font-size: 0.8rem;
}

.party-amount-section {
  display: flex;
  align-items: center;
  justify-content: end;
  min-height: 32px;
}

.amount-display {
  font-size: 0.9rem;
  font-weight: 500;
  color: #21427d;
  text-align: right;
}

.fee-summary-compact {
  border-top: 1px solid #e0e0e0;
  padding-top: 8px;
}

.total-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.8rem;
}

.total-label {
  font-weight: 500;
  color: #21427d;
}

.total-chip {
  margin: 0 8px;
}

.total-amount {
  font-weight: 500;
  color: #21427d;
}

/* Additional Charges Styles */
.additional-charge-content {
  display: grid;
  grid-template-columns: 3fr 2fr 1fr 1fr auto;
  gap: 8px;
  align-items: center;
}

.charge-type-section,
.charge-percentage-section {
  min-width: 0;
}

.charge-type-field,
.charge-percentage-field {
  font-size: 0.8rem;
}

.charge-amount-section {
  display: flex;
  align-items: center;
  justify-content: end;
  min-height: 32px;
}

.charge-emi-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.charge-emi-checkbox {
  margin: 0;
}

.charge-actions-section {
  display: flex;
  justify-content: center;
  width: 32px;
}

/* Collateral Section Styles */
.collateral-content {
  padding: 8px 0;
}

.uploaded-documents {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.documents-title {
  font-size: 0.85rem;
  font-weight: 500;
  color: #21427d;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.documents-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Dark mode support for compact fee split and additional charges */
:deep(.theme--dark) .fee-parties-compact {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .processing-fee-section,
:deep(.theme--dark) .additional-charges-section,
:deep(.theme--dark) .collateral-section {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .uploaded-documents {
  background-color: #2a2a2a;
  border-color: #444;
}

:deep(.theme--dark) .documents-title {
  color: #8cb6e1;
}

:deep(.theme--dark) .fee-title,
:deep(.theme--dark) .total-label,
:deep(.theme--dark) .total-amount,
:deep(.theme--dark) .amount-display {
  color: #8cb6e1;
}

:deep(.theme--dark) .fee-party-compact {
  border-color: #444;
}

/* Add these styles to your existing styles */
.requirements-section {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.requirements-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.requirement-card {
  border-radius: 4px;
  overflow: hidden;
}

.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
}

.requirement-name {
  font-size: 14px;
}

.requirement-content {
  background-color: white;
}

.requirement-actions {
  background-color: white;
  border-top: 1px solid #f0f0f0;
}
/* Co-Applicants Styles - Compact Grid Layout */
.co-applicants-section {
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 16px;
  background-color: #fafafa;
}

.co-applicants-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.co-applicants-title {
  font-size: 0.95rem;
  font-weight: 500;
  color: #21427d;
  margin: 0;
  display: flex;
  align-items: center;
}

/* Compact Grid Layout */
.co-applicants-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.co-applicants-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.co-applicant-card-compact {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  background-color: white;
  transition: all 0.2s ease;
}

.co-applicant-card-compact:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.co-applicant-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.co-applicant-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.co-applicant-details {
  flex: 1;
  min-width: 0;
}

.co-applicant-name {
  font-size: 0.85rem;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  margin-bottom: 2px;
}

.co-applicant-contact {
  font-size: 0.7rem;
  color: #666;
  line-height: 1.2;
}

.co-applicant-share {
  margin-top: 8px;
}

.share-input-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.share-percentage-input {
  font-size: 0.8rem;
}

.share-amount-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 3px;
  border: 1px solid #e0e0e0;
}

.amount-label {
  font-size: 0.7rem;
  color: #666;
}

.amount-value {
  font-size: 0.75rem;
  font-weight: 500;
  color: #21427d;
}

/* Primary Borrower Summary - Compact */
.primary-borrower-summary {
  border: 1px solid #4caf50;
  border-radius: 6px;
  padding: 12px;
  background-color: #f0f8f0;
}

.borrower-info {
  display: flex;
  align-items: center;
}

.borrower-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.borrower-name {
  font-size: 0.85rem;
  font-weight: 500;
  color: #333;
}

.borrower-share {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.share-percentage {
  font-size: 0.85rem;
  font-weight: 600;
  color: #4caf50;
}

.share-amount {
  font-size: 0.7rem;
  color: #666;
}

/* Share Summary Alert - Compact */
.share-summary-alert {
  margin-top: 8px;
  margin-bottom: 0;
}

.share-summary-content {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
}

.summary-text {
  font-weight: 500;
}

.status-text {
  font-weight: 400;
  font-style: italic;
}

.co-applicants-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  text-align: center;
}

/* Dark mode support for compact co-applicants */
:deep(.theme--dark) .co-applicants-section {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .co-applicants-title {
  color: #8cb6e1;
}

:deep(.theme--dark) .co-applicant-card-compact {
  background-color: #2a2a2a;
  border-color: #444;
}

:deep(.theme--dark) .co-applicant-name {
  color: #e0e0e0;
}

:deep(.theme--dark) .co-applicant-contact {
  color: #aaa;
}

:deep(.theme--dark) .share-amount-compact {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .amount-label {
  color: #aaa;
}

:deep(.theme--dark) .amount-value {
  color: #8cb6e1;
}

:deep(.theme--dark) .primary-borrower-summary {
  background-color: #1e2e1e;
  border-color: #4caf50;
}

:deep(.theme--dark) .borrower-name {
  color: #e0e0e0;
}

:deep(.theme--dark) .share-amount {
  color: #aaa;
}

/* Dark mode support for guarantor styles */
:deep(.theme--dark) .guarantor-chip {
  background-color: #ff9800 !important;
}

/* Prepayment Section Styles */
.prepayment-section {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
  margin-bottom: 16px;
}

.prepayment-header {
  margin-bottom: 8px;
}

.prepayment-title {
  font-size: 0.85rem;
  font-weight: 500;
  color: #21427d;
  margin: 0;
  display: flex;
  align-items: center;
}

.prepayment-content {
  padding: 8px 0;
}

.prepayment-label {
  line-height: 1.4;
}

/* Dark mode support for prepayment section */
:deep(.theme--dark) .prepayment-section {
  background-color: #2d2d2d;
  border-color: #444;
}

:deep(.theme--dark) .prepayment-title {
  color: #8cb6e1;
}

/* Read-only field styling */
.readonly-field :deep(.v-input__control) {
  background-color: #f5f5f5;
}

.readonly-field :deep(.v-text-field__details) {
  display: none;
}

:deep(.theme--dark) .readonly-field :deep(.v-input__control) {
  background-color: #2d2d2d;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .form-content {
    padding: 16px;
  }

  .form-section {
    margin-bottom: 24px;
  }

  .co-applicants-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  /* Compact fee party layout for mobile */
  .fee-party-content {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .party-amount-section {
    justify-content: flex-start;
    min-height: auto;
  }

  /* Additional charges mobile layout */
  .additional-charge-content {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .charge-amount-section {
    justify-content: flex-start;
    min-height: auto;
  }

  .charge-emi-section {
    justify-content: flex-start;
  }

  .charge-actions-section {
    justify-content: flex-start;
    width: auto;
  }

  /* Co-applicants mobile layout */
  .co-applicants-cards-grid {
    grid-template-columns: 1fr;
  }

  .borrower-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .borrower-share {
    align-items: flex-start;
  }

  .share-input-group {
    gap: 4px;
  }
}

@media (max-width: 960px) {
  .co-applicants-cards-grid {
    grid-template-columns: 1fr;
  }
}

/* Dynamic Borrower Styles */
.merchant-field-container {
  position: relative;
}

/* Bulk Loan Create Form Styles */
.form-content[v-if="activeType === 'bulk'"] {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.primary-merchant-field {
  width: 100%;
}

.add-borrower-btn {
  margin-left: 8px;
}

.dynamic-borrower-field {
  position: relative;
}

.borrower-autocomplete {
  width: 100%;
}

.borrower-text-field {
  width: 100%;
}

.borrower-text-field :deep(.v-input__control) {
  background-color: #f8f9fa;
}

.remove-borrower-btn {
  margin-left: 8px;
}

.no-borrowers-message {
  margin-bottom: 16px;
}

:deep(.theme--dark) .borrower-text-field :deep(.v-input__control) {
  background-color: #2d2d2d;
}
</style>
