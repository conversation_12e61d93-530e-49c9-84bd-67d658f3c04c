<template>
  <div class="w-full border border-gray-200 rounded bg-gray-50">
    <div class="flex justify-between items-center p-3 mb-2">
      <h6 class="text-sm font-medium text-blue-900 m-0 flex items-center">
        <v-icon small class="mr-1">mdi-cash-plus</v-icon>
        Additional Charges
      </h6>
      <v-btn
        x-small
        color="primary"
        outlined
        @click="addAdditionalCharge"
        class="normal-case font-medium text-xs"
      >
        <v-icon x-small class="mr-1">mdi-plus</v-icon>
        Add
      </v-btn>
    </div>

    <div class="rounded border-t p-2 bg-gray-50 mt-2">
      <div v-for="(charge, index) in localCharges" :key="`additional-charge-${index}`">
        <div class="grid grid-cols-[3fr_2fr_1fr_1fr_auto] gap-2 pt-2 items-center">
          <div class="w-full">
            <v-text-field
              v-model="charge.feeType"
              label="Fee Type"
              outlined
              dense
              prepend-inner-icon="mdi-tag-outline"
              placeholder="e.g., Documentation Fee"
              class="text-xs"
            ></v-text-field>
          </div>
          <div class="">
            <v-text-field
              v-model="charge.percentage"
              label="Percentage"
              type="number"
              outlined
              dense
              suffix="%"
              placeholder="0.5"
              max="99"
              min="0"
              :rules="[(v) => (v >= 0 && v <= 99) || 'Percentage must be between 0 and 99']"
              @input="validatePercentage(charge, index)"
              class="text-xs"
            ></v-text-field>
          </div>
          <div class="flex items-center justify-center w-full">
            <v-checkbox
              v-if="showLinkWithEmi"
              v-model="charge.linkWithEmi"
              label="Link with EMI"
              color="primary"
              dense
              class="m-0 text-xs"
            ></v-checkbox>
          </div>
          <div class="flex justify-center w-8" v-if="localCharges.length > 1"></div>
          <div class="flex items-center justify-end min-h-[32px] relative w-[120px] max-w-[120px]">
            <span class="text-sm font-medium text-blue-900 text-right pr-4">
              {{ formatCurrency(calculateChargeAmount(charge.percentage)) || '₹0' }}
            </span>
            <v-btn
              v-if="localCharges.length > 1"
              icon
              x-small
              color="error"
              @click="removeAdditionalCharge(index)"
              class="absolute top-0 right-0 z-10"
              style="transform: translate(50%, -50%)"
            >
              <v-icon small>mdi-close</v-icon>
            </v-btn>
          </div>
        </div>
      </div>
    </div>
    <v-divider class="pb-4"></v-divider>
    <div class="" v-if="localCharges.length > 0">
      <div class="flex justify-end pr-2">
        <span class="font-medium text-blue-900 text-sm">Total Charges:</span>
        <p class="mx-2">{{ totalChargesPercentage.toFixed(1) }}%</p>
        <span prefix="₹" class="font-medium text-blue-900"> ( {{ formattedTotalAmount }}) </span>
      </div>
    </div>
  </div>
</template>

<script>
import { getAdditionalCharges } from '@/utils/workforce/utils'

export default {
  name: 'AdditionalChargesSection',
  props: {
    charges: {
      type: Array,
      default: () => []
    },
    loanAmount: {
      type: [String, Number],
      default: 0
    },
    formatCurrency: {
      type: Function,
      default: (amount) => `₹${amount?.toLocaleString() || '0'}`
    },
    emiAmount: Number,
    showLinkWithEmi: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localCharges: [{ feeType: '', percentage: 0, linkWithEmi: false }]
    }
  },
  computed: {
    totalChargesPercentage() {
      return this.localCharges.reduce((sum, charge) => {
        return sum + (parseFloat(charge.percentage) || 0)
      }, 0)
    },
    formattedTotalAmount() {
      const totalAmount = this.calculateChargeAmount(this.totalChargesPercentage)
      return this.formatCurrency(totalAmount)
    }
  },
  watch: {
    charges: {
      handler(newCharges) {
        if (newCharges && newCharges.length > 0) {
          this.localCharges = [...newCharges]
        } else {
          this.localCharges = [{ feeType: '', percentage: 0, linkWithEmi: false }]
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addAdditionalCharge() {
      this.localCharges.push({ feeType: '', percentage: 0, linkWithEmi: false })
      this.emitChanges()
    },
    removeAdditionalCharge(index) {
      this.localCharges.splice(index, 1)
      this.emitChanges()
    },
    calculateChargeAmount(percentage) {
      if (!percentage || !this.emiAmount) return 0

      try {
        // Use the utility function for validation
        getAdditionalCharges(percentage, this.emiAmount)

        // If validation passes, calculate the amount
        const amount = (parseFloat(percentage) / 100) * parseFloat(this.emiAmount)
        return amount
      } catch (error) {
        // Show error message and return 0
        this.$toast.error(error.message)
        return 0
      }
    },
    validatePercentage(charge, index) {
      const percentage = parseFloat(charge.percentage)

      if (percentage > 99) {
        charge.percentage = 99
      }

      if (percentage < 0) {
        charge.percentage = 0
      }

      this.emitChanges()
    },
    onChargeChange() {
      this.emitChanges()
    },
    emitChanges() {
      this.$emit('update:charges', this.localCharges)
      this.$emit('charges-changed', this.localCharges)
    }
  }
}
</script>
