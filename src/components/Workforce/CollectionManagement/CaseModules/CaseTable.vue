<template>
  <div class="py-2">
    <!-- Filters and actions moved to parent component slot -->

    <div class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="cases"
        class="text-sm"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30, 50] }"
        :options.sync="options"
        :server-items-length="totalItems"
        :loading="loadTable"
        fixed-header
        show-select
        item-key="caseNo"
        v-model="selected"
      >
        <template v-slot:item.caseNo="{ item }">
          <td
            class="px-4 py-6 font-semibold hover:text-blue-700 dark-hover-text cursor-pointer"
            @click="caseDetails(item)"
          >
            {{ item.caseNo }}
            <span v-if="item.loanDetails?._id">({{ item.loanDetails?.emiSchedule?.length }})</span>
            <span v-else>({{ item.tasks?.length }})</span>
            <div class="d-flex flex-column" style="width: fit-content">
              <p
                class="font-semibold text-gray-500 mb-0"
                v-if="isLoanManagement && loanCompletedStatus(item.loanDetails?.loanWorkflow)?.step"
              >
                {{ loanCompletedStatus(item.loanDetails?.loanWorkflow)?.step }}
              </p>
            </div>
            <v-chip
              v-if="isLoanManagement"
              small
              outlined
              class="font-semibold"
              :color="
                statusColor(loanCompletedStatus(item.loanDetails?.loanWorkflow)?.status)?.textColor
              "
            >
              {{ convertTitle(loanCompletedStatus(item.loanDetails?.loanWorkflow)?.status) || '-' }}
            </v-chip>
          </td>
        </template>
        <template v-slot:item.customer="{ item }">
          <td
            class="px-4 py-6 font-semibold cursor-pointer hover:text-blue-700 dark-hover-text"
            @click="routeToCustomer(item)"
          >
            {{ item.customer?.customerFullName || '-' }}
          </td>
        </template>
        <template v-slot:item.emi(s)="{ item }">
          <td class="font-semibold px-4 py-6">
            <p class="mb-0">
              <span :style="{ color: statusColor('pending').textColor }">Pending - </span>
              <span>{{ totalEMI(item.loanDetails?.emiSchedule)?.pending || '0' }}</span>
            </p>
            <v-divider class="my-1"></v-divider>
            <p class="mb-0">
              <span :style="{ color: statusColor('paid').textColor }">Paid - </span>
              <span>{{ totalEMI(item.loanDetails?.emiSchedule)?.paid || '0' }}</span>
            </p>
            <v-divider class="my-2"></v-divider>
            <p class="mb-0" :style="{ color: statusColor('overdue').textColor }">
              OverDue-{{ totalEMI(item.loanDetails?.emiSchedule)?.overdue || '0' }}
            </p>
            <v-divider class="my-2"></v-divider>
            <p>
              <span :style="{ color: statusColor('late_paid').textColor }">Late Paid - </span>
              <span>
                {{
                  lateFeeCalculation(item.loanDetails?.emiSchedule)?.latePaidCount?.toLocaleString(
                    'en-IN'
                  ) || '0'
                }}
              </span>
            </p>
          </td>
        </template>
        <template v-slot:item.disbursements="{ item }">
          <td class="px-4 py-6 font-semibold">
            <div
              v-if="
                item.loanDetails?.loanDisbursements && item.loanDetails.loanDisbursements.length > 0
              "
            >
              <div
                v-for="(disbursement, index) in formatDisbursementDetails(
                  item.loanDetails.loanDisbursements
                )"
                :key="index"
                class="disbursement-row mb-2"
              >
                <div class="d-flex align-center">
                  <span class="disbursement-amount"
                    >₹{{ disbursement.amount?.toLocaleString('en-IN') }}</span
                  >
                  <span class="disbursement-date mx-1">({{ disbursement.date }})</span>
                </div>
                <v-chip class="mt-1" small outlined :color="getStatusColor(disbursement.status)">
                  {{ disbursement.status }}
                </v-chip>
                <v-divider
                  v-if="
                    index < formatDisbursementDetails(item.loanDetails.loanDisbursements).length - 1
                  "
                  class="my-2"
                ></v-divider>
              </div>
            </div>
            <div v-else class="text-center text--secondary">No disbursements</div>
          </td>
        </template>
        <template v-slot:item.lender="{ item }">
          <td
            class="px-4 py-6 font-semibold cursor-pointer hover:text-blue-700 dark-hover-text"
            @click="routeToPartner(item)"
          >
            {{ item.loanDetails?.partner?.partnerName || '-' }}
          </td>
        </template>
        <template v-slot:item.principal="{ item }">
          <td class="px-4 py-6 font-semibold">
            <!-- ₹{{ outstandingAmt(item.loanDetails?.emiSchedule)?.toLocaleString('en-IN') || '-' }} -->
            <p class="mb-0">
              <span :style="{ color: statusColor('outstanding').textColor }">Outstanding - </span>
              <span
                >₹{{
                  emiAmtStats(
                    item.loanDetails?.emiSchedule
                  )?.totalPricipalOutstanding?.toLocaleString('en-IN') || '-'
                }}</span
              >
            </p>
            <v-divider class="my-2"></v-divider>
            <p>
              <span :style="{ color: statusColor('recovered').textColor }">Recovered - </span>
              <span>
                ₹{{
                  emiAmtStats(
                    item.loanDetails?.emiSchedule
                  )?.totalPrincipalRecovered?.toLocaleString('en-IN') || '-'
                }}</span
              >
            </p>
          </td>
        </template>
        <template v-slot:item.interest="{ item }">
          <td class="px-4 py-6 font-semibold">
            <!-- ₹{{ outstandingAmt(item.loanDetails?.emiSchedule)?.toLocaleString('en-IN') || '-' }} -->
            <p class="mb-0">
              <span :style="{ color: statusColor('expected').textColor }">Expected - </span>
              <span
                >₹{{
                  emiAmtStats(item.loanDetails?.emiSchedule)?.totalInterestExpected?.toLocaleString(
                    'en-IN'
                  ) || '-'
                }}</span
              >
            </p>
            <v-divider class="my-2"></v-divider>
            <p>
              <span :style="{ color: statusColor('recovered').textColor }">Recovered - </span>
              <span>
                ₹{{
                  emiAmtStats(
                    item.loanDetails?.emiSchedule
                  )?.totalInterestRecovered?.toLocaleString('en-IN') || '-'
                }}</span
              >
            </p>
          </td>
        </template>
        <template v-slot:item.latefee="{ item }">
          <td class="px-4 py-6 font-semibold">
            <p class="mb-0">
              <span :style="{ color: statusColor('pending').textColor }">Pending - </span>
              <span
                >₹{{
                  lateFeeCalculation(item.loanDetails?.emiSchedule)?.pendingLateFee?.toLocaleString(
                    'en-IN'
                  ) || '0'
                }}
              </span>
            </p>
            <v-divider class="my-2"></v-divider>
            <p>
              <span :style="{ color: statusColor('late_paid').textColor }">Paid - </span>
              <span>
                ₹{{
                  lateFeeCalculation(item.loanDetails?.emiSchedule)?.paidLateFee?.toLocaleString(
                    'en-IN'
                  ) || '0'
                }}
              </span>
            </p>
          </td>
        </template>
        <template v-slot:item.loanamount="{ item }">
          <td class="px-4 py-6 font-semibold">
            <p class="mb-0">
              <span :style="{ color: statusColor('requested').textColor }">Requested - </span>
              <span
                >₹{{ item.loanDetails?.loanAmountRequested?.toLocaleString('en-IN') || '-' }}</span
              >
            </p>
            <v-divider class="my-2"></v-divider>
            <p>
              <span :style="{ color: statusColor('sanctioned').textColor }">Sanctioned - </span>
              <span>
                ₹{{ item.loanDetails?.loanAmountSanctioned?.toLocaleString('en-IN') || '-' }}</span
              >
            </p>
          </td>
        </template>
        <template v-slot:item.typeOfLoan="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ convertTitle(item?.typeOfLoan) || '-' }}
          </td>
        </template>
        <template v-slot:item.bkt="{ item }">
          <td class="px-4 py-6 font-semibold">{{ extractValue(item.bkt) }}</td>
        </template>
        <template v-slot:item.pos="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.pos }}</td>
        </template>
        <template v-slot:item.distanceFromBranch="{ item }">
          <td v-if="getBranchDistance(item)" class="px-4 py-6 font-semibold">
            {{ getBranchDistance(item) }}
          </td>
          <td v-else class="px-4 py-6 font-semibold">
            <button class="btn btn-primary" @click="getDistanceFromBranch(item)">
              Calculate Distance
            </button>
          </td>
        </template>
        <template v-slot:item.entityName="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.entity?.entityName || '-' }}
          </td>
        </template>
        <template v-slot:item.emiAmount="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.emiAmount }}</td>
        </template>
        <template v-slot:item.tenure="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.tenure }}
          </td>
        </template>
        <template v-for="option in dynamicColumns" v-slot:[`item.${option.value}`]="{ item }">
          <td :key="option.value" class="px-4 py-6 font-semibold">
            {{
              item?.tasks[0] && item?.tasks[0].meetingDetails?.meetingOutCome === option?.value
                ? item?.tasks[0].meetingDetails?.fundCollected || '-'
                : '-'
            }}
          </td>
        </template>

        <!-- <template v-slot:item.tat="{ item }">
          <td class="px-4 py-6 font-semibold">{{ calculateTAT(item) }}</td>
        </template>
        <template v-slot:item.caseStartDate="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.caseStartDate ? $dayjs(item.caseStartDate).format('DD/MM/YYYY') : '' }}
            <br />
            {{ item.caseStartDate ? $dayjs(item.caseStartDate).format('hh:mm A') : '-' }}
          </td>
        </template> -->
        <template v-slot:item.branchName="{ item }">
          <td class="px-4 py-6 font-semibold">{{ findBranch(item) }}</td>
        </template>
        <template v-slot:item.teamName="{ item }">
          <td class="px-4 py-6 font-semibold">{{ findTeam(item.team) }}</td>
        </template>
        <template v-slot:item.assignedTo="{ item }">
          <td class="px-2 py-6 font-semibold">
            <template>
              <v-autocomplete
                v-model="item.assignedTo"
                :items="users"
                item-text="fullName"
                item-value="_id"
                label="Assigned To"
                placeholder="Assigned To"
                hide-details
                flat
                solo
                multiple
                :disabled="item.status === 'CLOSED'"
                @change="caseStatusUpdate($event, item, 'assignedTo')"
              >
                <!-- v-tooltip="{ text: `${employeeFullName(item.assignedTo)}` }" -->
                <template v-slot:prepend-item>
                  <v-list-item
                    ripple
                    @mousedown.prevent
                    v-show="item.assignedTo"
                    @click="caseStatusUpdate('null', item, 'assignedTo')"
                    class="unassignedOptions"
                  >
                    <v-list-item-content>
                      <v-list-item-title class="py-0"> Unassigned </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <v-divider class=""></v-divider>
                </template>
              </v-autocomplete>
            </template>
          </td>
        </template>
        <template v-slot:item.outcome="{ item }">
          <td class="px-2 py-6 font-semibold">
            <v-select
              v-model="item.userDefinedCaseStatus"
              label="Outcome"
              :items="userDefinedStatusOptions"
              hide-details
              flat
              solo
              :disabled="item.status === 'CLOSED'"
              @change="(outcome) => caseStatusUpdate(outcome, item, 'userDefinedCaseStatus')"
            >
              <template v-slot:prepend-item>
                <v-list-item
                  ripple
                  @mousedown.prevent
                  v-show="item.userDefinedCaseStatus"
                  @click="caseStatusUpdate('null', item, 'userDefinedCaseStatus')"
                  class="noOutcomeOptions"
                >
                  <v-list-item-content>
                    <v-list-item-title class="py-0"> Reset </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
                <v-divider class=""></v-divider>
              </template>
            </v-select>
          </td>
        </template>
        <template v-slot:item.status="{ item }">
          <div class="d-flex flex-column" style="width: fit-content">
            <OrderStatuses
              v-if="!isLoanManagement"
              :orderStatus="item.status"
              :statuses="caseStatusOptions"
              @statusChange="(status) => caseStatusUpdate(status, item, 'status')"
            />
          </div>
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4 py-6">
            <action-button
              :item="item"
              :canEdit="canEdit"
              :canWrite="canWrite"
              :canDelete="canDelete"
              :handleEdit="() => casesEdit(item)"
              :addTaskAction="() => addCaseTask(item)"
              :handleDelete="() => handleDelete(item)"
              :showAddIcon="'mdi-plus'"
              :plusIconText="'Add Visit'"
              :showEditButton="canEdit"
              :showAddTaskButton="canWrite && !isLoanManagement"
              :showDeleteButton="canDelete"
            />
          </td>
        </template>
      </v-data-table>
    </div>

    <v-dialog v-model="openEditCase" width="90rem" persistent v-if="isLoanManagement">
      <AddEditLoanManagement
        @caseAdded="handleAdded"
        @emittedEntity="handleEntity"
        :editCase="editCase"
        :caseConfig="caseConfig"
        :entities="entities"
      />
    </v-dialog>
    <v-dialog v-model="openEditCase" width="auto" persistent v-else>
      <AddEditCase
        @caseAdded="handleAdded"
        @emittedEntity="handleEntity"
        :editCase="editCase"
        :caseConfig="caseConfig"
        :entities="entities"
      />
    </v-dialog>
    <v-dialog v-model="openUploadCases" max-width="600" persistent>
      <UploadCases
        @updateUploadData="handleAdded"
        @close="closeUpload"
        @downloadSample="exportXlsxFormat"
        :caseConfig="caseConfig"
        :caseAction="caseAction"
      />
    </v-dialog>
    <v-dialog v-model="openAddCaseTask" max-width="600" persistent>
      <AddEditCaseTask @caseTaskAdded="handleAdded" :editCase="editCase" :caseConfig="caseConfig" />
    </v-dialog>
    <v-dialog v-model="openCaseDetails" max-width="98rem" persistent>
      <CaseDetails
        :selectedCase="selectedCase"
        :caseConfig="caseConfig"
        @closeDialog="closeDialog"
      />
    </v-dialog>
    <AlertPopUp
      :showConfirmationDialog="showConfirmationDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="'Are you sure you want to delete the case?'"
      :confirmationActionText="'DELETE'"
      :confirmationActionColor="'red'"
      :performAction="caseDelete"
      :cancel="cancel"
    />
    <AlertPopUp
      :showConfirmationDialog="showUpdateVisitDialog"
      :confirmationTitle="'Confirm Update'"
      :confirmationMessage="`Do you want to change pending visits to ${newAssignedTo}?`"
      :confirmationActionText="'UPDATE'"
      :confirmationActionColor="'blue'"
      :performAction="updateVisit"
      :cancel="cancelVisitUpdate"
    />

    <AlertPopUp
      :showConfirmationDialog="showDeleteDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="'Are you sure you want to delete cases?'"
      :confirmationCustomerMessage="'Do you want to delete associated customer with selected cases?'"
      :afterConfirmationMessage="`Please make sure customer is not linked with other cases which you don't want to delete.`"
      :confirmationActionText="'DELETE'"
      :confirmationActionColor="'red'"
      :performAction="casesDeleteFunc"
      :deleteCustomerFunc="handleCheckedUpdate"
      :cancel="handleShowDialogDelete"
      :dialogLable="dialogLable"
      :loading="loading"
      :deleteCustomer="deleteCustomer"
    />

    <AlertPopUp
      :showConfirmationDialog="showDeleteAllDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="'Are you sure you want to delete all cases? Deleted cases cannot be reverted.'"
      :confirmationActionText="'DELETE'"
      :confirmationActionColor="'red'"
      :performAction="deleteAllCases"
      :cancel="handleShowDialogDeleteAll"
      :dialogLable="dialogLable"
      :loading="loading"
    />

    <UpdateCases
      :visible.sync="showDialog"
      :handleShowDialog="handleShowDialog"
      :handleDeleteSelected="handleDeleteSelected"
      :items="selected"
      :users="users"
      :branches="branches"
      :userDefinedStatusOptions="userDefinedStatusOptions"
      :caseConfig="caseConfig"
      :dialogLable="dialogLable"
    >
    </UpdateCases>
    <ExportDynamicColumns
      :showExportDialog="exportDialog"
      :exportKeys="exportColumnHeaders"
      :exportLoading="exportLoading"
      :caseType="caseConfig.name"
      @closeExportDialog="closeExportDialog"
      @exportData="(selectedKeys) => exportCases(selectedKeys)"
    />

    <!-- Loan Bulk Upload Dialog -->
    <v-dialog v-model="openLoanBulkUploadDialog" max-width="1000" persistent>
      <LoanBulkUpload
        :caseConfig="caseConfig"
        @close="closeLoanBulkUpload"
        @uploadComplete="handleLoanUploadComplete"
      />
    </v-dialog>
  </div>
</template>
<script>
import ActionButton from '@/components/ActionButton'
import AddEditCase from './AddEditCase'
import AddEditLoanManagement from './AddEditLoanManagement'
import CaseDetails from '../CaseDetails'
import UploadCases from './UploadCases'
import AddEditCaseTask from '../AddEditCaseTask'
import AlertPopUp from '@/components/AlertPopUp'
import OrderStatuses from '../../InventoryManagement/OrderStatuses.vue'
import LoanBulkUpload from './LoanBulkUpload.vue'
import { chipStatusColors } from '@/utils/workforce/statusColors'
import {
  convertToTitleCase,
  fullName,
  debounce,
  dayHourFormat,
  isModuleFeatureAllowed,
  hasPermission,
  mergeOptions,
  mergeOptionsDetails,
  calculateOverdueDays
} from '@/utils/common'
import FilterPopOver from '@/components/FilterPopOver.vue'
import ExportDynamicColumns from '../ExportDynamicColumns.vue'
import UpdateCases from './UpdateCases.vue'
import { setDataOnEnter } from '@/utils/workforce/utils'
import permissionData from '@/utils/permissions'
import _ from 'lodash'

export default {
  name: 'CaseTable',
  props: {
    caseConfig: Object,
    orgConfig: Object
    // entities: Array,
  },
  components: {
    ActionButton,
    AddEditCase,
    UploadCases,
    AddEditCaseTask,
    CaseDetails,
    AlertPopUp,
    OrderStatuses,
    // FilterPopOver,
    UpdateCases,
    ExportDynamicColumns,
    AddEditLoanManagement,
    LoanBulkUpload
  },
  data() {
    return {
      userOrg: this.$storage.getUniversal('user'),
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      headers: [],
      subCases: this.$store.state.subCases,
      openEditCase: false,
      openUploadCases: false,
      menu: false,
      showConfirmationDialog: false,
      downloadMenu: false,
      openAddCaseTask: false,
      openCaseDetails: false,
      showUpdateVisitDialog: false,
      selectedCase: null,
      filterCaseOutcome: '',
      filterStatus: '',
      cases: [],
      employees: [],
      editCase: null,
      selectedCaseId: null,
      filterAssignedTo: '',
      filterCase: '',
      loadTable: true,
      exportLoading: false,
      options: { sortBy: [], sortDesc: [], itemsPerPage: 10, page: 1 },
      totalItems: 0,
      filterCustomer: '',
      filterBranch: '',
      filterTeam: '',
      newAssignedTo: null,
      visitAssignedTo: null,
      visitId: null,
      dates: [],
      branches: [],
      teams: [],
      entities: [],
      startDate: null,
      endDate: null,
      awsBaseUrl: process.env.VUE_APP_S3_BASE_URL,
      caseStatusOptions: [
        { label: 'Open', value: 'OPEN' },
        { label: 'Closed', value: 'CLOSED' },
        { label: 'In-Progress', value: 'IN_PROGRESS' }
      ],
      selectedCount: 0,
      selected: [],
      showDialog: false,
      showDeleteDialog: false,
      dialogLable: '',
      deleteCustomer: false,
      loading: false,
      caseAction: '',
      showDeleteAllDialog: false,
      selectValue: 'CASE_NO',
      exportDialog: false,
      exportColumnHeaders: [],
      dynamicColumns: [],
      entityId: null,
      searchValue: '',
      // Partner filter properties
      filterPartner: null,
      partners: [],
      searchPartnerValue: '',
      // Loan bulk upload properties
      openLoanBulkUploadDialog: false
    }
  },
  beforeCreate() {
    this.debouncedFetchCases = () => {}
  },
  created() {
    this.debouncedFetchCases = debounce(this.fetchCases, 500)
  },
  watch: {
    '$route.query.search': {
      handler(newQuery) {
        try {
          this.filterCase = newQuery || ''
          this.options.page = 1
          this.debouncedFetchCases()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },

    '$route.query.customer': {
      handler(customer) {
        try {
          this.filterCustomer = customer || ''
          this.options.page = 1
          this.debouncedFetchCases()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },

    '$route.query.empId': {
      handler(empId) {
        try {
          this.filterAssignedTo = empId || ''
          this.options.page = 1
          this.debouncedFetchCases()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },

    filterCase(val) {
      if (!val && this.$route.query.search) {
        this.$router.replace({ query: {} }).catch((err) => {
          if (err.name !== 'NavigationDuplicated') {
            throw err
          }
        })
      }
    },
    caseConfig: {
      async handler(config) {
        const caseQuery = this.$route.query?.search
        await this.fetchUsers()

        if (caseQuery) {
          this.filterCase = caseQuery
        }
        if (config) {
          this.fetchCases()
          const fm = config?.fieldMappings
          const mergedOptionsDetails = mergeOptionsDetails(config.userDefinedCaseStatus)
          const isMergedOptions = mergedOptionsDetails.length > 0
          const getText = (displayName, key) => displayName || key
          const branch = {
            text: 'Linked Branch',
            value: 'branchName',
            sortable: false
          }

          const action = {
            text: 'Action',
            value: 'action',
            sortable: false,
            width: 'auto'
          }
          const loanamount = {
            text: 'Loan Amount',
            value: 'loanamount',
            sortable: false,
            width: '240px'
          }

          const customerName = {
            text: 'Customer',
            value: 'customer',
            sortable: false,
            width: '120px'
          }
          const partnerName = {
            text: 'Lender',
            value: 'lender',
            sortable: false,
            width: '150px'
          }
          const outcome = {
            key: 'outcome',
            text: getText(fm.caseOutcomeStatus?.displayName, 'Follow-Up Outcome'),
            width: '150px'
          }
          const disbursements = {
            text: 'Disbursements',
            value: 'disbursements',
            sortable: false,
            width: '280px'
          }
          const totalPendingEMI = {
            text: 'EMI(s)',
            value: 'emi(s)',
            sortable: false,
            width: '120px'
          }
          const principalAmount = {
            text: 'Principal',
            value: 'principal',
            sortable: false,
            width: '240px'
          }
          const interest = {
            text: 'Interest',
            value: 'interest',
            sortable: false,
            width: '240px'
          }
          const lateFee = {
            text: 'Late Fee',
            value: 'latefee',
            sortable: false,
            width: '200px'
          }
          const loanType = {
            key: 'typeOfLoan',
            text: getText(fm.typeOfLoan?.displayName, 'typeOfLoan'),
            width: '180px'
          }
          const status = {
            key: 'status',
            text: getText(fm.status?.displayName, 'Status'),
            width: '180px'
          }
          const headers = [
            {
              key: 'caseNo',
              text: getText(fm.caseNo?.displayName, 'caseNo'),
              width: '200px'
            },
            ...(this.isLoanManagement ? [] : [loanType]),
            ...(this.isLoanManagement ? [partnerName] : []),
            ...(this.isLoanManagement ? [customerName] : []),
            ...(this.isLoanManagement ? [loanamount] : []),
            ...(this.isLoanManagement ? [disbursements] : []),
            ...(this.isLoanManagement ? [principalAmount] : []),
            ...(this.isLoanManagement ? [interest] : []),
            ...(this.isLoanManagement ? [totalPendingEMI] : []),
            ...(this.isLoanManagement ? [lateFee] : []),
            ...(isMergedOptions ? [outcome] : []),
            ...(config.branch ? [branch] : []),
            ...(this.isLoanManagement ? [] : [status]),
            {
              key: 'assignedTo',
              text: getText(fm.assignedTo?.displayName, 'assignedTo'),
              width: '180px'
            },
            ...(this.canEdit || this.canDelete || this.canWrite ? [action] : [])
          ]

          if (!this.isLoanManagement) {
            headers.splice(
              2,
              0,
              {
                key: 'bkt',
                text: getText(fm.bkt?.displayName, 'bkt'),
                width: '80px'
              },
              {
                key: 'pos',
                text: getText(fm.pos?.displayName, 'pos'),
                width: '80px'
              },
              {
                key: 'emiAmount',
                text: getText(fm.emiAmount?.displayName, 'emiAmount'),
                width: '120px'
              },
              {
                key: 'tenure',
                text: getText(fm.tenure?.displayName, 'tenure'),
                width: '100px'
              }
              // { key: 'tat', text: 'TAT', width: null }
              // { key: 'caseStartDate', text: 'Case Start Date/Time', width: null }
            )
          }

          const caseOutcomeIndex = headers.findIndex((header) => header.key === 'outcome')

          if (isMergedOptions) {
            const filteredOptions = mergedOptionsDetails.filter((el) =>
              el.fields.some((f) => f.name === 'amount' && f.visibility === true)
            )

            const Columns = filteredOptions.map((option) => ({
              text: `${option.code} Amt.`,
              value: `${option.code}`,
              key: `${option.code}`,
              width: '100px',
              sortable: false
            }))

            this.dynamicColumns = Columns
            if (caseOutcomeIndex !== -1) {
              headers.splice(caseOutcomeIndex + 1, 0, ...Columns)
            }
          }

          const x = headers
            .filter((col) => !col.key || fm[col.key]?.visible !== false || fm[col.key]?.visibility)
            // .filter((col) => !col.key || fm[col.key]?.visible !== false)
            .map((col) => ({
              text: col.text,
              value: col.key || col.text.toLowerCase().replace(/ /g, ''),
              sortable: false,
              width: col.width || undefined
            }))

          // Adding a fixed column
          // if (this.caseConfig?.branch) {
          //   x.splice(2, 0, {
          //     text: 'Distance From Branch',
          //     value: 'distanceFromBranch',
          //     sortable: false,
          //     width: '150px'
          //   })
          // }
          if (this.isEntityEnabled) {
            x.splice(2, 0, {
              text: config?.entity?.displayName,
              value: 'entityName',
              sortable: false,
              width: '150px'
            })
          }
          if (this.isTeamLinked) {
            x.splice(2, 0, {
              text: 'Team',
              value: 'teamName',
              sortable: false,
              width: '150px'
            })
          }

          this.headers = x
        }
      },
      immediate: true
    },

    options: {
      handler(newOptions, oldOptions) {
        if (
          newOptions.itemsPerPage !== oldOptions.itemsPerPage ||
          newOptions.page !== oldOptions.page
        ) {
          this.fetchCases()
        }
      },
      deep: true
    },
    formattedDate() {
      this.updateSelectedCount()
    }
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.collectionEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.collectionDelete)
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.collectionWrite)
    },
    isLoanManagement() {
      return this.$route?.params?.useCase === 'loan_management'
    },
    selectItems() {
      const caseLang = this.caseConfig?.fieldMappings
      if (caseLang?.bkt?.displayName) {
        return [
          { text: caseLang?.caseNo?.displayName, value: 'CASE_NO' },
          { text: caseLang?.bkt?.displayName || 'Bucket', value: 'BKT' }
        ]
      }
      return [{ text: caseLang?.caseNo?.displayName, value: 'CASE_NO' }]
    },
    searchPlaceholder() {
      const caseLang = this.caseConfig?.fieldMappings
      if (this.selectValue === 'BKT') {
        return `Search by ${caseLang?.bkt?.displayName}`
      }
      return `Search by ${caseLang?.caseNo?.displayName}`
    },
    caseAssignedLang() {
      // const caseLang = this.caseConfig?.fieldMappings;
      return 'caseLang.assignedTo?.displayName'
    },
    tabText() {
      return this.caseConfig?.tableTabMapping || {}
    },
    users() {
      return [
        ...this.employees.map((emp) => ({
          ...emp,
          fullName: `${fullName(emp)} ${emp.wfmRole?.role ? ` - ${emp.wfmRole.role}` : ''}`
        })),
        {
          _id: 'null',
          fullName: 'Unassigned'
        }
      ]
    },
    userDefinedStatusOptions() {
      const options = mergeOptions(this.caseConfig?.userDefinedCaseStatus)
      return options
    },
    userDefinedOutcomeOptions() {
      const options = mergeOptions(this.caseConfig?.userDefinedCaseStatus).map((option) => ({
        text: option,
        value: option
      }))
      options.push({ text: 'Not-Set', value: 'null' })
      return options
    },
    formattedDate() {
      if (this.dates.length === 2) {
        const startDate = this.visibleDate(this.dates[0])
        const endDate = this.visibleDate(this.dates[1])
        return `${startDate} to ${endDate}`
      } else if (this.dates.length === 1) {
        return this.visibleDate(this.dates[0])
      }
      return ''
    },
    isEntityEnabled() {
      return this.caseConfig.entity?.enabled || false
    },
    isTeamLinked() {
      const selectedTeam = this.teams?.find((team) => team?.linkedLicense === this.caseConfig?._id)
      return selectedTeam?._id ? true : false
    },
    entityTitle() {
      return this.caseConfig.entity?.displayName || 'Entity'
    },
    orgLanguage() {
      return this.orgLang.data.leftNav
    },
    routeApiCollection() {
      return !this.isLoanManagement
    }
  },
  methods: {
    setDataOnEnter,
    async getDistanceFromBranch(item) {
      const params = {
        lat: item.lat,
        long: item.long
      }
      const distanceFromBranch = await this.getDistanceFromBranch(item)
      this.caseStatusUpdate(distanceFromBranch, item, 'distanceFromBranch')

      return this.$axios.get('/workforce/distance-from-branch', params)
    },
    loanCompletedStatus(loanWorkflow) {
      if (!loanWorkflow) return ''
      const lastStep = loanWorkflow?.steps[loanWorkflow?.steps?.length - 1]
      return { step: lastStep?.name, status: lastStep?.status }
    },
    extractValue(item) {
      if (!item) return '-'

      try {
        if (typeof item === 'string' && /^{.*}$/.test(item)) {
          const parsed = JSON.parse(item)
          return parsed.text || parsed.value || item
        }
        return typeof item === 'object' ? item.text || item.value || JSON.stringify(item) : item
      } catch {
        return item
      }
    },
    statusColor(colors) {
      const colorInfo = chipStatusColors[colors?.toLowerCase()]
      return colorInfo
    },
    getBranchDistance(item) {
      const distance = _.get(item, 'distanceFromBranch.[0].distance')
      return distance ? `${distance} km` : '-'
    },
    isBranchFeatureVisible(feature) {
      return isModuleFeatureAllowed('BRANCH_MANAGEMENT', feature)
    },
    filterParams() {
      const params = {}
      if (this.startDate && this.endDate) {
        params.start = this.startDate
        params.end = this.endDate
      }
      if (this.caseConfig?.name && !this.isLoanManagement) {
        params.caseType = this.caseConfig?.name
      }
      if (this.filterAssignedTo) {
        params.assignedTo = this.filterAssignedTo
      }

      // Only include userDefinedCaseStatus if it has a value
      if (this.filterCaseOutcome) {
        params.userDefinedCaseStatus = this.filterCaseOutcome
      }

      if (this.filterStatus) {
        params.status = this.filterStatus
      }

      if (this.filterCase) {
        if (this.selectValue === 'BKT') {
          params.bktSearch = this.filterCase
        } else {
          params.search = this.filterCase
        }
      }
      if (this.filterCustomer) {
        params.customerSearch = this.filterCustomer
      }
      if (this.entityId) {
        params.entity = this.entityId
      }
      if (this.filterPartner) {
        params.partner = this.filterPartner
      }
      return params
    },
    getAllEntities: debounce(async function (text) {
      try {
        // if (!text || this.entityId) return;
        const params = {}
        if (this.searchValue) {
          params.search = this.searchValue
        }
        const response = await this.$axios.get('/workforce/collection/entities', { params })
        this.entities = response?.data?.entities
      } catch (error) {
        console.log(error)
      }
    }, 500),
    getAllPartners: debounce(async function (text) {
      try {
        const params = {}
        if (this.searchPartnerValue) {
          params.search = this.searchPartnerValue
        }
        const response = await this.$axios.get('/workforce/ecom/partners', { params })
        this.partners = response?.data?.partners || []
      } catch (error) {
        console.log('Error fetching partners:', error)
      }
    }, 500),
    async fetchCases() {
      try {
        if (!this.caseConfig?.name) return
        const { sortBy, sortDesc, page, itemsPerPage } = this.options
        const filterParams = this.filterParams()
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems,
          ...filterParams
        }
        if (sortBy.length > 0) {
          params.sortBy = sortBy[0]
        }
        if (sortDesc.length > 0) {
          params.sortOrder = sortDesc[0] ? 'desc' : 'asc'
        }

        if (this.filterBranch) {
          params.branch = this.filterBranch
        }
        if (this.filterTeam) {
          params.team = this.filterTeam
        }
        this.loadTable = true
        let url
        let key = 'cases'
        if (this.routeApiCollection) {
          url = '/workforce/collection/cases'
        } else {
          url = `/workforce/collection/loan?subCase=${this.caseConfig._id}`
          key = 'loans'
        }
        const response = await this.$axios.get(url, {
          params
        })
        this.cases = response.data?.[key]
        this.loadTable = false
        this.totalItems = response.data?.pagination?.totalCount

        if (this.$route.query.caseNo && this.cases?.length === 1) {
          this.caseDetails(this.cases[0])
        }
      } catch (error) {
        this.loadTable = false
        console.log(error)
        const errorMessage = error?.response?.data?.message || error
        this.$toast.error(errorMessage)
      }
    },
    async caseStatusUpdate(newValue, item, field) {
      try {
        let payload
        if (field === 'assignedTo') {
          payload = {
            [field]: newValue.map((el) => el),
            acknowledged: ''
          }
        } else {
          payload = {
            [field]: newValue
          }
        }
        const id = item._id
        const response = await this.$axios.put(`/workforce/collection/case/${id}`, payload)
        if (field === 'assignedTo') {
          this.visitAssignedTo = newValue
          // const newUser = this.users.find(
          //   (user) => user._id === item.assignedTo
          // );
          // this.newAssignedTo = newUser?.fullName;
          // this.visitId = item.tasks[0]?._id;
          // this.showUpdateVisitDialog = true;
        }
        const successMessage = response?.data?.message || 'Case updated successfully'
        this.$toast.success(successMessage)
        this.cancel()
      } catch (error) {
        this.$toast.error(error.response?.data?.message)
        console.error(error)
      }
    },
    async updateVisit() {
      const payload = {
        assignedTo: this.visitAssignedTo
      }
      await this.$axios.put(`/workforce/collection/case/task/${this.visitId}`, payload)
      this.$toast.success(`Pending visits assigned to ${this.newAssignedTo} successfully.`)
      this.cancelVisitUpdate()
    },
    handleDelete(item) {
      this.showConfirmationDialog = true
      this.selectedCaseId = item._id
    },
    async caseDelete() {
      try {
        const params = {}
        if (this.caseConfig.name) {
          params.caseType = this.caseConfig.name
        }
        const response = await this.$axios.delete(
          `/workforce/collection/case/${this.selectedCaseId}`,

          params
        )
        const index = this.cases.findIndex((caseEl) => caseEl._id === this.selectedCaseId._id)
        if (index !== -1) {
          this.cases.splice(index, 1)
        }
        this.selectedCaseId = null
        const successMessage = response?.data?.message || 'Case deleted successfully.'
        this.$toast.success(successMessage)
        this.cancel()
      } catch (error) {
        const errorMessage =
          error?.response?.data?.message || 'An error occurred while deleting the case.'
        this.$toast.error(errorMessage)
        console.error(error)
      }
    },
    async deleteAllCases() {
      this.loading = true
      try {
        const caseType = this.caseConfig?.name
        const response = await this.$axios.delete(`/workforce/collection/case?caseType=${caseType}`)
        this.loading = false
        if (response?.data?.success) {
          this.$toast.success(response?.data?.message)
          this.handleShowDialogDeleteAll(false)
        }
      } catch (error) {
        this.loading = false
        const errorMessage =
          error?.response?.data?.message || 'An error occurred while deleting the case.'
        this.$toast.error(errorMessage)
        console.error(error)
      }
    },

    async singleCaseDelete(item, params) {
      try {
        let customerId = item?.customer?._id
        let selectedCaseId = item._id
        let url
        if (this.deleteCustomer) {
          url = `/workforce/collection/case/${selectedCaseId}?customerId=${customerId}`
        } else {
          url = `/workforce/collection/case/${selectedCaseId}`
        }
        let response = await this.$axios.delete(url, params)
        return response.data
      } catch (error) {
        console.error(error)
      }
    },
    async casesDeleteFunc() {
      this.loading = true
      const params = {}
      if (this.caseConfig.name) {
        params.caseType = this.caseConfig.name
      }
      const promises = []

      // Make API calls and store promises in the array
      this.selected?.forEach(async (item) => {
        promises.push(this.singleCaseDelete(item, params))
      })

      // Wait for all API calls to complete
      try {
        const results = await Promise.all(promises)

        if (!results.includes(undefined)) {
          this.$toast.success('Cases deleted successfully.')
        }
        this.loading = false
        this.deleteCustomer = false
        this.selected = []
        this.handleShowDialogDelete(false)
      } catch (error) {
        const errorMessage =
          error?.response?.data?.message || 'An error occurred while deleting the case.'
        this.$toast.error(errorMessage)
        console.error(error)
      }
    },

    handleCheckedUpdate(value) {
      this.deleteCustomer = value
    },

    async fetchUsers() {
      try {
        const params = {
          limit: '*',
          sortBy: 'firstName',
          sortOrder: 'asc'
        }
        if (this.userOrg?.branch && !this.userOrg?.role?.includes('WFM_ADMIN')) {
          params.branch = this.userOrg?.branch
        }
        if (this.caseConfig.branch) {
          params.branch = this.caseConfig?.branch
        }
        const response = await this.$axios.get('/workforce/v2/users', {
          params
        })
        this.employees = response.data?.users
      } catch (error) {
        console.log(error)
      }
    },
    onDateSelected() {
      if (this.dates?.length === 2) {
        this.startDate = this.formatDate(this.dates[0])
        this.endDate = this.formatDate(this.dates[1])
        this.options.page = 1
        this.fetchCases()
      } else {
        this.startDate = this.formatDate(this.dates[0])
        this.endDate = null
      }
    },
    async onDateCleared() {
      this.dates = []
      this.startDate = null
      this.endDate = null
      await this.fetchCases()
    },
    exportXlsxFormat() {
      const XLSX = require('xlsx')
      const restructuredData = this.prepareData(this.subCases)
      const caseType = this.caseConfig?.name
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(restructuredData)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      XLSX.writeFile(workbook, `${caseType}_Case.xlsx`)
    },
    prepareData(data) {
      const tableData = []

      data.forEach(() => {
        const row = []
        tableData.push(row)
      })

      const headerRow = []
      const uniqueHeaders = new Set()
      const subCase = this.caseConfig

      if (subCase.fieldMappings) {
        Object.values(subCase?.fieldMappings).forEach((field) => {
          // field?.visibility &&
          if (field && field?.displayName && !uniqueHeaders.has(field?.displayName)) {
            headerRow.push(field?.displayName)
            uniqueHeaders.add(field?.displayName)
          }
        })
      }

      if (subCase?.additionalColumns) {
        Object.values(subCase?.additionalColumns)?.forEach((add) => {
          if (add?.displayName && !uniqueHeaders.has(add.displayName)) {
            headerRow.push(add.displayName)
            uniqueHeaders.add(add.displayName)
          }
        })
      }

      if (this.isEntityEnabled) {
        headerRow.push(subCase.entity?.displayName)
        uniqueHeaders.add(subCase.entity?.displayName)
      }

      const addressFields = []
      subCase?.fieldMappings?.addresses?.forEach((address) => {
        Object.values(address).forEach((field) => {
          if (
            field.displayName &&
            field.displayName.trim() &&
            !uniqueHeaders.has(field.displayName)
          ) {
            addressFields.push(field.displayName)
            uniqueHeaders.add(field.displayName)
          }
        })
      })

      addressFields?.forEach((fieldDisplayName) => {
        headerRow.push(fieldDisplayName)
        uniqueHeaders.add(fieldDisplayName)
      })

      tableData.unshift(headerRow)
      return tableData
    },
    totalEMI(emis) {
      if (!emis || !Array.isArray(emis)) return 0
      const totalEmi = emis.length
      const paidEmi = emis.filter((el) => el.emiStatus.toLowerCase().includes('paid')).length

      const today = new Date()
      const overdueEmi = emis.filter((emi) => {
        const dueDate = new Date(emi.dueDate)
        return !emi.emiStatus.toLowerCase().includes('paid') && dueDate < today
      }).length

      return {
        total: totalEmi,
        paid: paidEmi,
        pending: totalEmi - (paidEmi + overdueEmi),
        overdue: overdueEmi
      }
    },
    totalDisbursed(allDisbursements) {
      if (!allDisbursements || !Array.isArray(allDisbursements)) return 0
      return allDisbursements.reduce((acc, curr) => {
        return acc + (curr.amount || 0)
      }, 0)
    },

    formatDisbursementDetails(allDisbursements) {
      if (!allDisbursements || !Array.isArray(allDisbursements)) return []

      return allDisbursements.map((disbursement, index) => ({
        serialNo: index + 1,
        amount: disbursement.amount || 0,
        date: disbursement.date ? this.$dayjs(disbursement.date).format('DD/MM/YYYY') : 'N/A',
        status: disbursement.status || 'N/A'
      }))
    },

    getStatusColor(status) {
      const statusLower = status?.toLowerCase()
      switch (statusLower) {
        case 'completed':
        case 'success':
        case 'disbursed':
          return 'success'
        case 'pending':
        case 'processing':
          return 'warning'
        case 'failed':
        case 'rejected':
        case 'cancelled':
          return 'error'
        default:
          return 'grey'
      }
    },

    emiAmtStats(emis) {
      if (!emis || !Array.isArray(emis)) return 0
      let totalPrincipalRecovered = 0
      let totalPricipalOutstanding = 0
      let totalInterestExpected = 0
      let totalInterestRecovered = 0
      emis.forEach((el) => {
        totalInterestExpected += el.interestAmount
        if (el.emiStatus === 'Paid') {
          totalPrincipalRecovered += el.principalAmount
          totalInterestRecovered += el.interestAmount
        } else if (el.emiStatus === 'Minimum Amount Paid') {
          totalInterestRecovered += el.interestAmount
        } else {
          totalPricipalOutstanding += el.principalAmount
        }
      })
      return {
        totalPrincipalRecovered,
        totalPricipalOutstanding,
        totalInterestExpected,
        totalInterestRecovered
      }

      // const firstPaidEmiIndx = emis.findIndex((emi) => emi.emiStatus === 'Paid')
      // if (firstPaidEmiIndx === -1) {
      //   return emis[0]?.outstandingAmount || 0
      // }
      // return firstPaidEmiIndx + 1 < emis.length
      //   ? emis[firstPaidEmiIndx + 1].outstandingAmount
      //   : emis[emis.length - 1]?.outstandingAmount || 0
    },
    lateFeeCalculation(emis) {
      if (!emis || !Array.isArray(emis)) return 0
      let paidLateFee = 0
      let pendingLateFee = 0
      let latePaidCount = 0
      emis.forEach((emi) => {
        const overdueInfo = calculateOverdueDays(
          emi.dueDate,
          emi.emiStatus,
          emi.paidDate,
          emi.emiAmount,
          emi.lateFeeType,
          emi.lateFeeValue
        )
        if (overdueInfo.isLatePaid) {
          latePaidCount++
          paidLateFee += overdueInfo.lateFeeAmount || 0
        } else {
          pendingLateFee += overdueInfo.lateFeeAmount || 0
        }
      })

      return { latePaidCount, paidLateFee, pendingLateFee }
    },
    closeExportDialog() {
      this.exportDialog = false
    },
    employeeFullName(item) {
      if (item && item?.length) {
        const mergedName = item.map((el) => fullName(el))
        return mergedName?.join(', ')
      }
      return ''
    },
    mergedEmployeeId(item) {
      if (item && item?.length) {
        const mergedId = item.map((el) => el.employeeId)
        return mergedId.join(', ')
      }
      return ''
    },
    mergedEmpRole(item) {
      if (item && item?.length) {
        const mergedRole = item.map((el) => el.wfmRole?.role)
        return mergedRole.join(', ')
      }
      return ''
    },
    reportingManager(item) {
      if (item && item.length) {
        const managers = item.map((el) => {
          if (el.reportesTo) {
            return `${el.reportesTo.firstName} ${el.reportesTo.lastName}`
          }
          return ''
        })

        return managers.join(', ')
      }
      return ''
    },
    handleExportKeys() {
      // For loan management, export directly without showing dialog
      if (this.isLoanManagement) {
        this.exportLoanManagementData()
        return
      }

      // For other use cases, show the export dialog
      const exportOptions = this.prepareExportData(this.cases)
      if (exportOptions?.length > 0) {
        let maxKeysObject = exportOptions.reduce((prev, current) => {
          return Object.keys(current)?.length > Object.keys(prev)?.length ? current : prev
        })
        this.exportColumnHeaders = Object.keys(maxKeysObject).map((key) => ({
          text: key,
          value: key
        }))
      } else {
        this.exportColumnHeaders = []
      }
      this.exportDialog = true
    },

    prepareExportData(dataCopy) {
      const configLang = this.caseConfig.fieldMappings

      return dataCopy.flatMap((item, index) => {
        const {
          taskDescription,
          taskTitle,
          caseNo,
          bkt,
          pos,
          emiAmount,
          tenure,
          assignedTo,
          typeOfLoan,
          crn,
          customerFullName,
          productInfo,
          status,
          caseOutcomeStatus
        } = configLang || {}

        const descriptionDisplayName = taskDescription?.displayName || 'Description'
        const taskDisplayName = taskTitle?.displayName || 'Task Title'
        const tasks = [...(item.tasks || [])].reverse()

        const mergeRemarks = (type) =>
          tasks
            .filter((task) => (type === 'call' ? task.callInfo?.length : !task.callInfo))
            .map(
              ({ actualStartDateTime, meetingDetails }) =>
                `${this.$dayjs(actualStartDateTime).format('DD/MM/YYYY hh:mm:ss A')} : ${
                  meetingDetails?.meetingNotes
                }`
            )
            .join('  ||  ')

        const mergeVisitRemarks = mergeRemarks('visit')
        const mergeCallRemarks = mergeRemarks('call')

        const taskFields = tasks.reduce((fields, task, taskIndex) => {
          const {
            actualStartDateTime,
            actualEndDateTime,
            taskTitle,
            taskDescription,
            taskStatus,
            meetingDetails,
            additionalInfo
          } = task

          return {
            ...fields,
            [`Merged ${this.tabText.visitTable?.displayName || 'Visit'} Remarks`]:
              mergeVisitRemarks,
            'Merged Call Remarks': mergeCallRemarks,
            [`${this.tabText.visitTable?.displayName || 'Visit'} ${taskIndex + 1}`]: taskTitle,
            [`${descriptionDisplayName}`]: taskDescription,
            [`${this.tabText.visitTable?.displayName || 'Visit'} Start ${taskIndex + 1}`]:
              actualStartDateTime
                ? this.$dayjs(actualStartDateTime).format('DD/MM/YYYY hh:mm A')
                : '',
            [`${this.tabText.visitTable?.displayName || 'Visit'} End ${taskIndex + 1}`]:
              actualEndDateTime ? this.$dayjs(actualEndDateTime).format('DD/MM/YYYY hh:mm A') : '',
            [`Next ${this.tabText.visitTable?.displayName || 'Visit'} date of ${
              this.tabText.visitTable?.displayName || 'Visit'
            } ${taskIndex + 1}`]: actualEndDateTime
              ? this.$dayjs(meetingDetails?.nextTaskScheduledOn).format('DD/MM/YYYY hh:mm A')
              : '',
            [`${this.tabText.visitTable?.displayName || 'Visit'} Status ${taskIndex + 1}`]:
              taskStatus,
            [`Meeting Note ${taskIndex + 1}`]: meetingDetails?.meetingNotes,
            [`Meeting Outcome ${taskIndex + 1}`]: meetingDetails?.meetingOutCome,
            [`${meetingDetails?.meetingOutCome || ''} Amount ${taskIndex + 1}`]:
              meetingDetails?.fundCollected,
            [`Image Link ${taskIndex + 1}`]: additionalInfo?.selfie
              ? `${this.awsBaseUrl}${additionalInfo.selfie.doc_physical_path}`
              : '',
            [`Audio Link ${taskIndex + 1}`]: additionalInfo?.audio
              ? `${this.awsBaseUrl}${additionalInfo.audio.doc_physical_path}`
              : ''
          }
        }, {})

        const additionalData = Object.entries(item?.additionalColumns || {}).reduce(
          (columns, [key, value]) => {
            columns[key] = value || ''
            return columns
          },
          {}
        )

        // Repeat rows based on assignedTo
        return (item.assignedTo || [{}]).map((employee, empIndex) => ({
          'S.No': index + 1,
          [`${caseNo?.displayName || 'Case No'}`]: item.caseNo || '',
          [`${bkt?.displayName || 'BKT'}`]: item.bkt || '',
          [`${pos?.displayName || 'POS'}`]: item.pos || '',
          [`${emiAmount?.displayName || 'EMI Amount'}`]: item.emiAmount || '',
          [`${tenure?.displayName || 'Tenure'}`]: item.tenure || '',
          [`${this.orgLanguage?.employees || 'Employees'} Name`]: employee
            ? `${employee.firstName || ''} ${employee.lastName || ''}`.trim()
            : '',
          [`${this.orgLanguage?.employees || 'Employees'} Id`]: employee?.employeeId || '',
          Role: employee?.wfmRole?.role || '',
          [`${assignedTo?.displayName || 'FOS NUMBER'}`]: employee?.mobile || '',
          'Employee Reporting Manager': employee?.reportesTo
            ? `${employee.reportesTo.firstName || ''} ${employee.reportesTo.lastName || ''}`.trim()
            : '',
          [`${typeOfLoan?.displayName || 'Type of Loan'}`]: item.typeOfLoan || '',
          [`${crn?.displayName || 'CRN'}`]: item.customer?.crn || '',
          [`${customerFullName?.displayName || 'Customer Full Name'}`]:
            item.customer?.customerFullName || '',
          ...(this.isEntityEnabled
            ? {
                [`${this.caseConfig?.entity?.displayName}`]: item.entity?.entityName || ''
              }
            : {}),
          [`${productInfo?.displayName || 'Product Info'}`]: item.productInfo,
          'Start On': item.caseStartDate
            ? this.$dayjs(item.caseStartDate).format('DD/MM/YYYY hh:mm A')
            : '',
          'Closed On': item.caseEndDate
            ? this.$dayjs(item.caseEndDate).format('DD/MM/YYYY hh:mm A')
            : '',
          TAT: this.calculateTAT(item),
          [`${status?.displayName || 'Status'}`]: item.status,
          [`${caseOutcomeStatus?.displayName || 'Outcome'}`]: item.userDefinedCaseStatus || '',
          ...taskFields,
          ...additionalData
        }))
      })
    },
    async exportCases(selectedKeys) {
      try {
        const chunkSize = 50
        this.exportLoading = true
        const totalPages = Math.ceil(this.totalItems / chunkSize)

        const promises = []

        for (let index = 0; index < totalPages; index++) {
          const filterParams = this.filterParams()

          const params = {
            limit: chunkSize,
            page: index + 1,
            ...filterParams
          }
          const promise = this.$axios.get('/workforce/collection/cases', {
            params
          })
          promises.push(promise)
        }

        const responses = await Promise.all(promises)

        const data = responses.reduce((acc, response) => {
          return acc.concat(response.data.cases)
        }, [])
        const XLSX = require('xlsx')
        const dataCopy = [...data]
        const restructuredData = this.prepareExportData(dataCopy)
        const filteredData = restructuredData.map((row) => {
          const filteredRow = {}
          selectedKeys.forEach((key) => {
            filteredRow[key] = row[key]
          })
          return filteredRow
        })
        const ws = XLSX.utils.json_to_sheet(filteredData)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Task Data')
        const file_date = new Date().toISOString()
        const filename = `${this.caseConfig?.name}_${file_date}.xlsx`
        XLSX.writeFile(wb, filename)

        const storageKeyName = 'exportColumnSelectedKey'
        const existingStorage = JSON.parse(localStorage.getItem(storageKeyName)) || {}
        const userId = this.userOrg?._id
        const caseType = this.caseConfig.name

        if (!existingStorage[userId]) {
          existingStorage[userId] = []
        }

        const caseIndex = existingStorage[userId].findIndex((entry) => entry.caseType === caseType)

        if (caseIndex > -1) {
          existingStorage[userId][caseIndex].selectedKeys = selectedKeys
        } else {
          existingStorage[userId].push({
            useCase: 'collection',
            caseType: caseType,
            selectedKeys: selectedKeys
          })
        }

        localStorage.setItem(storageKeyName, JSON.stringify(existingStorage))
      } catch (error) {
        console.error('Error exporting data:', error)
      } finally {
        this.exportLoading = false
      }
    },

    async exportLoanManagementData() {
      try {
        const chunkSize = 50
        this.exportLoading = true
        const totalPages = Math.ceil(this.totalItems / chunkSize)

        const promises = []

        for (let index = 0; index < totalPages; index++) {
          const filterParams = this.filterParams()

          const params = {
            // limit: chunkSize,
            // page: index + 1
            ...filterParams
          }
          const promise = this.$axios.get(
            '/workforce/collection/loan?subCase=68724b5d8e15f2046ee23837&page=1&limit=50',
            {
              params
            }
          )
          promises.push(promise)
        }

        const responses = await Promise.all(promises)

        const data = responses.reduce((acc, response) => {
          return acc.concat(response.data.loans)
        }, [])

        const XLSX = require('xlsx')
        const restructuredData = this.prepareLoanManagementExportData(data)

        const ws = XLSX.utils.json_to_sheet(restructuredData)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Loan Management Data')
        const file_date = new Date().toISOString()
        const filename = `Loan_Management_${file_date}.xlsx`
        XLSX.writeFile(wb, filename)
      } catch (error) {
        console.error('Error exporting loan management data:', error)
      } finally {
        this.exportLoading = false
      }
    },

    prepareLoanManagementExportData(data) {
      return data.map((item, index) => {
        const loanDetails = item.loanDetails || {}
        const customer = item.customer || {}
        const loanWorkflow = loanDetails.loanWorkflow || {}
        const emiSchedule = loanDetails.emiSchedule || []
        const partner = loanDetails.partner || {}

        // Basic loan information
        const baseData = {
          'S.No': index + 1,
          'Case No': item.caseNo || '',
          'Case Type': item.caseType || '',
          'Use Case': item.useCase || '',
          'Sub Case': item.subCase || '',
          Status: item.status || '',

          // Customer Information
          'Customer CRN': customer.crn || '',
          'Customer Full Name': customer.customerFullName || '',
          'Customer Gender': customer.gender || '',

          // Loan Details
          'Application Number': loanDetails.applicationNumber || '',
          'Type of Loan': loanDetails.typeOfLoan || '',
          'Loan Amount Requested': loanDetails.loanAmountRequested || '',
          'Loan Amount Sanctioned': loanDetails.loanAmountSanctioned || '',
          'Total Amount Disbursed': loanDetails.totalAmountDisbursed || 0,
          'Type of Interest': loanDetails.typeOfInterest || '',
          'Interest Rate': loanDetails.interestRate || '',
          'Tenure Type': loanDetails.tenureType || '',
          Tenure: loanDetails.tenure || '',
          'EMI Start Date': loanDetails.emiStartDate
            ? this.$dayjs(loanDetails.emiStartDate).format('DD/MM/YYYY')
            : '',
          'Total EMI Paid': loanDetails.totalEmiPaid || 0,
          'Overdue Amount': loanDetails.overdueAmount || 0,
          'Processing Fee': loanDetails.processingFee || 0,
          'Late Payment Fee': loanDetails.latePaymentFee || 0,
          'Prepayment Charges': loanDetails.prepaymentCharges || 0,
          'Advisory Fee': loanDetails.advisoryFee || 0,
          'Annual Processing Rate': loanDetails.annualProcessingRate || 0,
          'Debt Service Ratio': loanDetails.debtServiceRatio || 0,
          'Sanction Date': loanDetails.sanctionDate
            ? this.$dayjs(loanDetails.sanctionDate).format('DD/MM/YYYY')
            : '',
          'KYC Status': loanDetails.kycStatus || '',
          'Current Status': loanDetails.currentStatus || '',

          // Partner Information
          'Partner Name': partner.partnerName || '',
          'Partner Status': partner.partnerStatus || '',

          // EMI Schedule Summary
          'Total EMI Count': emiSchedule.length || 0,
          'Pending EMI Count': emiSchedule.filter((emi) => !emi.isPaid).length || 0,
          'Paid EMI Count': emiSchedule.filter((emi) => emi.isPaid).length || 0
        }

        // Add workflow steps information
        if (loanWorkflow.steps && loanWorkflow.steps.length > 0) {
          loanWorkflow.steps.forEach((step, stepIndex) => {
            const stepPrefix = `Step ${stepIndex + 1}`
            baseData[`${stepPrefix} - ID`] = step.stepId || ''
            baseData[`${stepPrefix} - Name`] = step.name || ''
            baseData[`${stepPrefix} - Status`] = step.status || ''
            baseData[`${stepPrefix} - Started At`] = step.startedAt
              ? this.$dayjs(step.startedAt).format('DD/MM/YYYY hh:mm A')
              : ''
            baseData[`${stepPrefix} - Completed At`] = step.completedAt
              ? this.$dayjs(step.completedAt).format('DD/MM/YYYY hh:mm A')
              : ''
            baseData[`${stepPrefix} - Due Date`] = step.dueDate
              ? this.$dayjs(step.dueDate).format('DD/MM/YYYY hh:mm A')
              : ''
            baseData[`${stepPrefix} - Remarks`] = step.remarks || ''
            baseData[`${stepPrefix} - Rejection Reason`] = step.rejectionReason || ''

            // Add requirements for each step
            if (step.requirements && step.requirements.length > 0) {
              step.requirements.forEach((req, reqIndex) => {
                const reqPrefix = `${stepPrefix} Req ${reqIndex + 1}`
                baseData[`${reqPrefix} - Type`] = req.type || ''
                baseData[`${reqPrefix} - Name`] = req.name || ''
                baseData[`${reqPrefix} - Status`] = req.status || ''
                baseData[`${reqPrefix} - Submitted At`] = req.submittedAt
                  ? this.$dayjs(req.submittedAt).format('DD/MM/YYYY hh:mm A')
                  : ''
                baseData[`${reqPrefix} - Document URL`] = req.documentUrl || ''
                baseData[`${reqPrefix} - Remarks`] = req.remarks || ''
              })
            }
          })
        }

        // Add EMI schedule details
        emiSchedule.forEach((emi, emiIndex) => {
          const emiPrefix = `EMI ${emiIndex + 1}`
          baseData[`${emiPrefix} - Due Date`] = emi.dueDate
            ? this.$dayjs(emi.dueDate).format('DD/MM/YYYY')
            : ''
          baseData[`${emiPrefix} - EMI Amount`] = emi.emiAmount || 0
          baseData[`${emiPrefix} - Principal Amount`] = emi.principalAmount || 0
          baseData[`${emiPrefix} - Interest Amount`] = emi.interestAmount || 0
          baseData[`${emiPrefix} - Outstanding Amount`] = emi.outstandingAmount || 0
          baseData[`${emiPrefix} - Is Paid`] = emi.isPaid ? 'Yes' : 'No'
          baseData[`${emiPrefix} - Status`] = emi.emiStatus || ''

          // Add activities for each EMI
          if (emi.activities && emi.activities.length > 0) {
            emi.activities.forEach((activity, actIndex) => {
              const actPrefix = `${emiPrefix} Activity ${actIndex + 1}`
              baseData[`${actPrefix} - Type`] = activity.activityType || ''
              baseData[`${actPrefix} - Document`] = activity.activityDoc || ''
              baseData[`${actPrefix} - Status`] = activity.status || ''
              baseData[`${actPrefix} - Updated At`] = activity.updatedAt
                ? this.$dayjs(activity.updatedAt).format('DD/MM/YYYY hh:mm A')
                : ''
            })
          }
        })

        // Add loan disbursements
        if (loanDetails.loanDisbursements && loanDetails.loanDisbursements.length > 0) {
          loanDetails.loanDisbursements.forEach((disbursement, disbIndex) => {
            const disbPrefix = `Disbursement ${disbIndex + 1}`
            baseData[`${disbPrefix} - Amount`] = disbursement.amount || 0
            baseData[`${disbPrefix} - Date`] = disbursement.date
              ? this.$dayjs(disbursement.date).format('DD/MM/YYYY')
              : ''
            baseData[`${disbPrefix} - Status`] = disbursement.status || ''
            baseData[`${disbPrefix} - Remarks`] = disbursement.remarks || ''
            baseData[`${disbPrefix} - Reference No`] = disbursement.referenceNo || ''
            baseData[`${disbPrefix} - Payment Mode`] = disbursement.paymentMode || ''

            if (disbursement.bankDetails) {
              baseData[`${disbPrefix} - Bank Name`] = disbursement.bankDetails.bankName || ''
              baseData[`${disbPrefix} - Account Number`] =
                disbursement.bankDetails.accountNumber || ''
              baseData[`${disbPrefix} - IFSC Code`] = disbursement.bankDetails.ifscCode || ''
            }
          })
        }

        return baseData
      })
    },
    async fetchBranches() {
      try {
        const response = await this.$axios.get('/workforce/branches')
        this.branches = response.data.branches
      } catch (error) {
        console.error('Error fetching branches:', error)
      }
    },
    async fetchTeams() {
      try {
        const response = await this.$axios.get('/workforce/teams')
        this.teams = response.data.teams || []
      } catch (error) {
        console.error('Error fetching branches:', error)
      }
    },
    calculateTAT(item) {
      const caseStart = item.caseStartDate
      const caseEnd = item.caseEndDate
      if (caseEnd) {
        return dayHourFormat(caseStart, caseEnd)
      }
      return '-'
    },
    findBranch(item) {
      if (item) {
        const branch = this.branches.find((el) => el._id === item.branch)
        return branch?.branchName || '-'
      }
      return '-'
    },
    findTeam(id) {
      if (id) {
        const team = this.teams?.find((el) => el?._id === id)
        return team?.teamName || '-'
      }
      return '-'
    },
    casesEdit(item) {
      this.openEditCase = true
      this.editCase = item
    },
    employeeMobile(arr) {
      if (arr && arr.length) {
        const mergedNumber = arr.map((el) => {
          return el.mobile
        })
        return mergedNumber.join(', ') || '-'
      }

      return 'N/A'
    },
    toolTipName(arr) {
      if (arr && arr.length) {
        const mergedName = arr.map((el) => {
          return `${el.firstName} ${el.lastName}`
        })
        return mergedName.join(', ') || 'Un-assigned'
      }
      return arr?.firstName || 'Un-assigned'
    },
    addCaseTask(item) {
      this.editCase = item
      this.openAddCaseTask = true
    },
    uploadCases(text) {
      this.caseAction = text
      this.openUploadCases = true
    },
    closeUpload() {
      this.openUploadCases = false
    },
    caseDetails(item) {
      this.openCaseDetails = true
      this.selectedCase = item
    },
    formatDate(dateString) {
      return this.$dayjs(dateString).format('YYYY-MM-DD')
    },
    visibleDate(date) {
      return this.$dayjs(date).format('DD-MM-YYYY')
    },
    cancel() {
      this.showConfirmationDialog = false
      this.fetchCases()
    },
    cancelVisitUpdate() {
      this.showUpdateVisitDialog = false
      this.visitAssignedTo = null
      this.visitId = null
      this.newAssignedTo = null
      this.fetchCases()
    },
    handleAdded() {
      this.openEditCase = false
      this.openAddCaseTask = false
      this.fetchCases()
    },
    async handleEntity(val) {
      this.searchValue = val
      await this.getAllEntities()
    },
    closeDialog() {
      this.openCaseDetails = false
      if (this.$route.query.caseNo) {
        const query = { ...this.$route.query, caseNo: undefined }
        this.$router.replace({ query })
      }
      this.fetchCases()
    },
    addNewCase() {
      this.editCase = null
      this.openEditCase = true
    },
    convertTitle(str) {
      if (str) {
        return convertToTitleCase(str)
      }
      return '-'
    },
    handleInput: debounce(async function () {
      this.options.page = 1
      await this.fetchCases()
    }, 800),
    updateSelectedCount() {
      this.selectedCount = [
        this.filterAssignedTo,
        this.filterCaseOutcome,
        this.filterStatus,
        this.formattedDate,
        this.filterPartner
      ].filter((item) => item !== null && item !== '' && item.length > 0).length
    },

    handleChange() {
      this.handleInput()
      this.updateSelectedCount()
    },
    handleShowDialog(label) {
      this.dialogLable = label
      this.showDialog = !this.showDialog
      if (this.showDialog === false) {
        this.fetchCases()
      }
    },
    handleShowDialogDelete(label) {
      this.dialogLable = label
      this.showDeleteDialog = !this.showDeleteDialog
      if (this.showDeleteDialog === false) {
        this.fetchCases()
      }
      this.deleteCustomer = false
    },

    handleShowDialogDeleteAll(label) {
      this.dialogLable = label
      this.showDeleteAllDialog = !this.showDeleteAllDialog
      if (this.showDeleteAllDialog === false) {
        this.fetchCases()
      }
    },
    handleDeleteSelected() {
      this.selected = []
    },
    handleUnassignedCases() {
      this.filterAssignedTo = 'null'
      this.handleChange()
    },
    handleNoOutcome() {
      // this.filterCaseOutcome = 'null'
      this.handleChange()
    },
    clearOutcomeFilter() {
      this.filterCaseOutcome = null
      this.handleChange()
    },
    async handleSelect() {
      this.filterCase = ''
      await this.fetchCases()
    },

    // Loan bulk upload methods
    openLoanBulkUpload() {
      this.openLoanBulkUploadDialog = true
    },

    closeLoanBulkUpload() {
      this.openLoanBulkUploadDialog = false
    },

    handleLoanUploadComplete() {
      // Refresh the table data when upload is complete
      this.fetchCases()
    },
    routeToPartner(item) {
      const partnerName = item.loanDetails?.partner?.partnerName || ''
      if (partnerName) {
        const url = this.$router.resolve(`/workforce/partners?search=${partnerName}`).href
        window.open(url, '_blank')
      }
    },
    routeToCustomer(item) {
      const customerName = item.customer?.customerFullName || ''
      if (customerName) {
        const url = this.$router.resolve(`/workforce/customers?search=${customerName}`).href
        window.open(url, '_blank')
      }
    }
  },
  async mounted() {
    if (this.$route.query.caseNo) {
      this.filterCase = this.$route.query.caseNo
    }
    if (this.$route.query.userDefinedCaseStatus) {
      this.filterCaseOutcome = this.$route.query.userDefinedCaseStatus
      this.updateSelectedCount()
    }
    await this.fetchTeams()
    await this.fetchBranches()
    if (this.isLoanManagement) {
      await this.getAllPartners()
    }
    this.$emit('component-mounted')
  }
}
</script>
<style scoped>
.v-data-table-header th span {
  font-size: 14px;
  color: #262626;
  font-weight: 400;
}

.v-data-table >>> .v-data-table__wrapper > table > tbody > tr > td > .v-data-table__checkbox {
  text-align: center;
}
</style>

<style scoped>
.wfm-search-fields.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 40px !important;
}
.unassignedOptions {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}
.noOutcomeOptions {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}
.v-list {
  padding-top: 0px !important;
}
.custom-input-group {
  display: flex;
  align-items: center;
  padding: 2px;
  background-color: white;
  border-radius: 5px;
}

.select-input {
  flex-grow: 1;
  width: 15%;
  font-size: smaller;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.text-input {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

/* Disbursement styles */
.disbursement-row {
  font-size: 0.875rem;
  line-height: 1.2;
}

.disbursement-serial {
  font-weight: 600;
  color: #666;
}

.disbursement-amount {
  font-weight: 800;
}

.disbursement-date {
  font-size: 0.8rem;
  color: #666;
}
</style>
