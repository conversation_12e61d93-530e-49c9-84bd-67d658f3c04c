<template>
  <div>
    <v-card>
      <div class="flex justify-between align-center">
        <v-card-title class="text-md">Select File(XLSX)</v-card-title>
        <v-btn
          outlined
          small
          @click="openOrgMapping"
          :color="$vuetify.theme.currentTheme.primary"
          class="mr-2"
          >Verify columns before create/update</v-btn
        >
      </div>
      <div class="flex justify-end">
        <v-btn
          outlined
          small
          @click="downloadSample"
          :color="$vuetify.theme.currentTheme.primary"
          class="mr-2"
          v-show="caseAction === 'Create'"
          >Sample Download</v-btn
        >
      </div>
      <v-card-text>
        <v-file-input
          label="Choose a file"
          v-model="selectedFile"
          multiple
          accept=".xlsx"
        ></v-file-input>
        <v-progress-linear
          v-if="uploadProgress !== null"
          :value="uploadProgress"
          absolute
          bottom
        ></v-progress-linear>
        <div v-if="uploadStatus">
          <p>Success : {{ created }}</p>
          <div class="flex align-center justify-between">
            <p>Failed : {{ failed }}</p>
            <v-btn small v-if="failed" @click="downloadFile">
              Download Failed Status<v-icon color="red">mdi-download</v-icon>
            </v-btn>
          </div>
        </div>
        <div v-if="invalid && invalid.length > 0" class="mt-4">
          <v-alert type="error" text outlined class="mt-2" :icon="false">
            <strong>Failed Upload Details:</strong>
            <div style="max-height: 300px; overflow-y: auto">
              <v-list dense>
                <v-list-item v-for="(item, index) in invalid" :key="index">
                  <v-list-item-content>
                    <v-list-item-title>
                      {{ index + 1 }} -
                      {{ caseConfig.fieldMappings?.caseNo?.displayName }}:
                      {{ getDynamicField(item) }} - Reason:
                      <span class="multiline-text">{{
                        item.failedReason
                      }}</span>
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </div>
          </v-alert>
        </div>
        <v-alert
          v-else-if="validationErrors.length > 0 || validationPassed"
          :type="validationPassed ? 'success' : 'error'"
          text
          outlined
          class="mt-2"
          :icon="false"
        >
          <strong v-if="!validationPassed"
            >Validation failed: Please ensure the columns in your uploaded file
            match the required headers listed below. The file may have missing
            or mismatched headers.</strong
          >
          <strong v-else
            >Validation passed: Column mapping matched successfully!</strong
          >
          <v-divider class="my-2" v-if="!validationPassed"></v-divider>
          <ul v-if="!validationPassed">
            <li v-for="(error, index) in validationErrors" :key="index">
              {{ error }}
            </li>
          </ul>
          <p v-else>
            Please click on <strong>{{ caseAction }}</strong> to proceed.
          </p>
        </v-alert>
        <v-alert v-if="uploadStatus" :type="uploadStatusType" class="mt-4">{{
          uploadStatus
        }}</v-alert>

        <!-- matched header alert -->

        <v-alert
          v-if="matchedHeaders.length && !validationPassed"
          type="success"
          text
          outlined
          class="mt-2"
          :icon="false"
        >
          <strong>Validation passed: Below Column matched successfully!</strong>
          <v-divider class="my-2" v-if="!validationPassed"></v-divider>
          <ul v-if="!validationPassed">
            <li v-for="(passedCol, index) in matchedHeaders" :key="index">
              {{ passedCol }}
              <v-icon> mdi-check </v-icon>
            </li>
          </ul>
        </v-alert>
      </v-card-text>
      <v-card-actions class="flex items-center justify-evenly mb-4">
        <v-btn @click="closeUploadDialog" class="rounded-md w-40" outlined
          >Close</v-btn
        >
        <v-btn
          @click="handleAction"
          :loading="uploading"
          :disabled="!selectedFile || uploading"
          class="white--text rounded-md w-40"
          :color="$vuetify.theme.currentTheme.primary"
        >
          {{ caseAction === "Update" ? "Upload" : (validationPassed ? caseAction : "Validate") }}
        </v-btn>
      </v-card-actions>
    </v-card>
    <v-dialog v-model="showTableMapping" max-width="53rem">
      <OrgMapping
        :editSubCase="subCases"
        :isUploadCase="isUploadCase"
        @cancel="closeMapping"
      />
    </v-dialog>
  </div>
</template>
<script>
import OrgMapping from "@/components/Workforce/Settings/OrgMapping";
import { extractExcelHeaders } from "@/utils/common";
export default {
  data() {
    return {
      selectedFile: null,
      uploading: false,
      created: null,
      failed: null,
      invalid: null,
      uploadProgress: null,
      uploadStatus: "",
      showTableMapping: false,
      isUploadCase: false,
      subCases: {},
      headers: [],
      validationErrors: [],
      matchedHeaders: [],
      validationPassed: false,
    };
  },
  components: {
    OrgMapping,
  },
  props: {
    caseConfig: Object,
    caseAction: String,
  },
  computed: {
    uploadStatusType() {
      return this.uploadStatus === "File uploaded successfully"
        ? "success"
        : "error";
    },
  },
  watch: {
    selectedFile: {
      handler(newFile) {
        if (newFile) {
          this.clearData();
        }
      },
    },
  },
  methods: {
    uploadFile() {
      if (!this.selectedFile) {
        return;
      }
      const formData = new FormData();

      this.selectedFile.forEach((file) => {
        formData.append("attachments", file);
      });

      if (this.caseConfig?.branch) {
        formData.append("branch", this.caseConfig?.branch);
      }
      formData.append("caseType", this.caseConfig?.name);
      if (this.caseAction === "Update") {
        formData.append("action", "UPDATE");
      }
      const config = {
        onUploadProgress: (progressEvent) => {
          this.uploadProgress = Math.round(
            (progressEvent.loaded / progressEvent.total) * 100
          );
        },
      };

      this.uploading = true;
      this.$axios
        .post("/workforce/case/bulk", formData, config)
        .then((response) => {
          this.created = response.data.data.caseCreatedSuccessfully;
          this.failed = response.data.data.caseCreationFailed;
          this.invalid = response.data.data.invalidCases;
          if (this.created) {
            this.uploadStatus = "File uploaded successfully";
            this.$emit("updateUploadData");
          } else {
            this.uploadStatus = "File uploading failed";
          }
        })
        .catch((error) => {
          this.uploadStatus = "Error uploading file";
          console.error("Error uploading file:", error);
        })
        .finally(() => {
          this.uploading = false;
        });
    },
    closeUploadDialog() {
      this.clearData();
      this.selectedFile = null;
      this.$emit("close");
    },
    clearData() {
      this.created = "";
      this.failed = "";
      this.invalid = [];
      this.uploadStatus = "";
      this.validationErrors = [];
      this.matchedHeaders = [];
      this.validationPassed = false;
    },
    async downloadFile() {
      try {
        const XLSX = require("xlsx");
        if (!this.selectedFile || this.selectedFile.length === 0) {
          console.error("No file selected for download.");
          return;
        }

        const file = this.selectedFile[0];
        const buffer = await file.arrayBuffer();

        const workbook = XLSX.read(buffer, { type: "array" });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        const excelData = XLSX.utils.sheet_to_json(worksheet);

        const enrichedData = excelData.map((row) => {
          const dN = this.caseConfig.fieldMappings?.caseNo?.displayName;
          const kN = this.caseConfig.fieldMappings?.caseNo?.key;

          const invalidEntry = this.invalid?.find(
            (item) => item[kN] === row[dN]
          );

          return {
            ...row,
            failedReason: invalidEntry?.failedReason || "",
          };
        });

        const newWorksheet = XLSX.utils.json_to_sheet(enrichedData);

        const newWorkbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, "Data");

        XLSX.writeFile(newWorkbook, "Failed_Status.xlsx");
      } catch (error) {
        console.error("Error generating download file:", error);
      }
    },
    openOrgMapping() {
      this.showTableMapping = true;
      this.isUploadCase = true;
      this.subCases = { ...this.$props.caseConfig };
    },
    closeMapping() {
      this.showTableMapping = false;
    },
    downloadSample() {
      this.$emit("downloadSample");
    },
    getDynamicField(item) {
      const dynamicField = this.caseConfig.fieldMappings?.caseNo?.displayName
        ?.toLowerCase()
        .replace(/\s+/g, "_");

      return item[dynamicField];
    },

    async handleAction() {
      if (this.caseAction === "Update") {
        this.uploadFile();
      } else if (!this.validationPassed) {
        await this.validateFile();
      } else {
        this.uploadFile();
      }
    },
    async validateFile() {
      try {
        this.headers = await extractExcelHeaders(this.selectedFile);
        const fieldMappings = this.caseConfig?.fieldMappings || {};

        const missingFields = [];
        const matchedFields = [];
        let missedCount = 0;
        let matchedCount = 0;

        Object.entries(fieldMappings).forEach(([key, mapping]) => {
          if (!mapping) {
            missingFields.push(
              `${++missedCount}: Make any of the addresses as default in mapping.`
            );
            return;
          }

          if (key === "addresses" && Array.isArray(mapping)) {
            mapping.forEach((address) => {
              const addressLineDisplayName = address.addressLine1?.displayName;

              if (!addressLineDisplayName) {
                missingFields.push(
                  `${++missedCount}: In ${
                    address.typeOfAddress
                  }, addressLine1 column is missing`
                );
              } else if (!this.headers.includes(addressLineDisplayName)) {
                missingFields.push(
                  `${++missedCount}: ${addressLineDisplayName} column for ${
                    address.typeOfAddress
                  } is missing in the file headers`
                );
              }
            });
          } else {
            const { displayName } = mapping;
            const isHeaderIncluded =
              displayName && this.headers.includes(displayName);

            if (displayName) {
              if (isHeaderIncluded) {
                matchedFields.push(`${++matchedCount}: ${displayName}`);
              } else {
                missingFields.push(`${++missedCount}: ${displayName}`);
              }
            }
          }
        });

        if (fieldMappings.customerMobile?.displayName) {
          const customerMobileColumn = fieldMappings.customerMobile.displayName;
          const XLSX = require("xlsx");

          const file = this.selectedFile[0];
          const buffer = await file.arrayBuffer();
          const workbook = XLSX.read(buffer, { type: "array" });
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const excelData = XLSX.utils.sheet_to_json(worksheet);

          const invalidNumbers = excelData.filter((row) => {
            const mobile = row[customerMobileColumn];
            if (!mobile) return false;

            // Split numbers by commas and validate each one

            const mobileString = String(mobile).trim();

            // Check if a comma is present and split if necessary
            const numbers = mobileString.includes(",")
              ? mobileString.split(",").map((num) => num.trim())
              : [mobileString];

            return numbers.some((num) => !/^\d{10}$/.test(num));
          });

          if (invalidNumbers.length > 0) {
            this.validationErrors.push(
              `Invalid mobile numbers found in column "${customerMobileColumn}". Ensure all numbers are exactly 10 digits and properly formatted.`
            );
          }
        }

        this.validationErrors = [...this.validationErrors, ...missingFields];
        this.matchedHeaders = matchedFields;
        if (this.validationErrors.length === 0) {
          this.validationPassed = true;
        }
      } catch (error) {
        console.error("Error during validation:", error);
        this.validationErrors = ["Error processing the file."];
        this.validationPassed = false;
      }
    },
  },
};
</script>
<style scoped>
.multiline-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
  font-weight: 400;
}
</style>
