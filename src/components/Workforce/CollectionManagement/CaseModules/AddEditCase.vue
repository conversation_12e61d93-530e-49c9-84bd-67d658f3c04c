<template>
  <v-card class="w-[43rem]" flat>
    <div class="flex justify-between p-4 bg-gray-200 dark-bg-custom">
      <h4 class="text-xl">{{ editCase ? 'Edit Case' : 'Add Case' }}</h4>
      <v-icon @click="cancel">mdi-close</v-icon>
    </div>
    <div class="p-4">
      <v-form ref="form" v-model="valid" lazy-validation>
        <!-- Dynamic fields from baseFieldMappings when available -->

        <!-- Original hardcoded fields when no baseFieldMappings -->
        <template>
          <v-text-field
            v-model="caseNo"
            :label="configLang.caseNo?.displayName"
            :rules="[(v) => !!v || `${configLang.caseNo?.displayName} is required`]"
            v-show="configLang.caseNo?.formVisibility || configLang.caseNo?.displayName"
            outlined
            dense
          />
          <v-text-field
            v-model="typeOfLoan"
            :label="configLang.typeOfLoan?.displayName || 'Type of loan'"
            v-show="configLang.typeOfLoan?.formVisibility || configLang.typeOfLoan?.displayName"
            outlined
            dense
          ></v-text-field>
          <v-text-field
            v-model="bkt"
            :label="configLang.bkt?.displayName || 'Bucket'"
            v-show="configLang.bkt?.formVisibility || configLang.bkt?.displayName"
            outlined
            dense
          ></v-text-field>
          <v-text-field
            v-model="pos"
            :label="configLang.pos?.displayName || 'POS'"
            v-show="configLang.pos?.formVisibility || configLang.pos?.displayName"
            outlined
            dense
          ></v-text-field>
          <v-text-field
            v-model="emiAmount"
            :label="configLang.emiAmount?.displayName || 'EMI Amount'"
            v-show="configLang.emiAmount?.formVisibility || configLang.emiAmount?.displayName"
            outlined
            dense
          ></v-text-field>
          <v-text-field
            v-model="tenure"
            :label="configLang.tenure?.displayName || 'Tenure'"
            v-show="configLang.tenure?.formVisibility || configLang.tenure?.displayName"
            outlined
            dense
          ></v-text-field>
        </template>

        <!-- Entity autocomplete -->
        <v-autocomplete
          v-model="selectedEntity"
          :label="`Search ${caseConfig.entity?.displayName || 'Entity'}`"
          :items="entities"
          item-text="entityName"
          :search-input.sync="searchEntity"
          item-value="_id"
          @update:search-input="debounceEntity"
          hide-no-data
          outlined
          dense
          v-show="isEntityEnabled"
        >
        </v-autocomplete>

        <!-- Assigned To autocomplete -->
        <v-autocomplete
          v-model="assignedTo"
          :label="`Search ${configLang.assignedTo?.displayName || 'Assigned To'}`"
          :items="employees"
          :item-text="getEmpFullName"
          :search-input.sync="searchUser"
          item-value="_id"
          @update:search-input="debounceUser"
          hide-no-data
          v-show="configLang.assignedTo?.formVisibility || configLang.assignedTo?.displayName"
          hide-details
          outlined
          dense
        >
        </v-autocomplete>

        <div class="flex items-center justify-between mt-4">
          <v-btn outlined class="w-72 h-14 mr-8 rounded-md" @click="cancel"> Cancel </v-btn>
          <v-btn
            class="white--text w-72 h-14 rounded-md"
            :color="$vuetify.theme.currentTheme.primary"
            @click="addCase"
          >
            {{ editCase ? 'Update' : 'Add' }}
          </v-btn>
        </div>
      </v-form>
    </div>
  </v-card>
</template>
<script>
import { fullName, debounce } from '@/utils/common'
export default {
  name: 'AddCases',
  data() {
    return {
      userData: this.$storage.getUniversal('user'),
      caseType: this.caseConfig.name,
      // Fields for original implementation
      caseNo: '',
      typeOfLoan: '',
      bkt: '',
      pos: null,
      emiAmount: null,
      tenure: null,
      // Fields for dynamic implementation
      formData: {},
      // Date handling
      dateMenus: {},
      formattedDates: {},
      // Common fields
      customerId: null,
      searchCustomer: '',
      assignedTo: null,
      valid: true,
      customers: [],
      employees: [],
      searchUser: '',
      searchEntity: '',
      selectedEntity: null,
      // Partner search fields
      partnerId: null,
      searchPartner: '',
      partners: []
    }
  },
  props: {
    editCase: Object,
    caseConfig: Object,
    entities: Array
  },
  watch: {
    editCase: {
      handler(newVal) {
        this.editCase = newVal
        this.populate()
      },
      deep: true
    }
  },
  computed: {
    configLang() {
      return this.$props.caseConfig.fieldMappings
    },
    isEntityEnabled() {
      return this.caseConfig.entity?.enabled || false
    }
  },
  methods: {
    async addCase() {
      try {
        const isValid = this.$refs.form.validate()
        if (!isValid) {
          this.$toast.error('Please fill the required fields')
          return
        }

        const payload = {
          caseType: this.caseType,
          caseNo: this.caseNo,
          typeOfLoan: this.typeOfLoan || undefined,
          bkt: this.bkt || undefined,
          pos: this.pos || undefined,
          emiAmount: this.emiAmount || undefined,
          tenure: this.tenure || undefined,
          customer: this.customerId,
          assignedTo: this.assignedTo
        }

        if (this.$props.caseConfig?.branch) {
          payload.branch = this.$props.caseConfig?.branch
        }
        if (this.selectedEntity) {
          payload.entity = this.selectedEntity
        }

        let url = '/workforce/collection/case'
        let method = 'post'

        if (this.editCase) {
          method = 'put'
          url += `/${this.editCase._id}`
        }

        await this.$axios[method](url, payload)

        this.cancel()
      } catch (error) {
        this.$toast.error(error.response?.data?.message)
        console.error(error)
      }
    },
    debounceUser: debounce(async function (text) {
      if (!text || this.assignedTo) return true
      this.fetchUsers()
    }, 1000),
    async fetchUsers() {
      try {
        const params = {}
        if (this.searchUser) {
          params.search = this.searchUser
        }
        if (this.userData?.branch && !this.userData?.role?.includes('WFM_ADMIN')) {
          params.branch = this.userData?.branch
        }
        const response = await this.$axios.get('/workforce/v2/users', {
          params
        })
        this.employees = response.data?.users
      } catch (error) {
        console.log(error)
      }
    },
    debounceCustomer: debounce(async function (text) {
      if (!text || this.customerId) return true
      this.fetchCustomers()
    }, 800),
    async fetchCustomers() {
      try {
        const params = {}
        if (this.searchCustomer) {
          params.search = this.searchCustomer
        }
        const response = await this.$axios.get('/workforce/customers', {
          params
        })
        this.customers = response.data.customers
      } catch (error) {
        console.log(error)
        const errorMessage = error?.response?.data?.message || error
        this.$toast.error(errorMessage)
      }
    },
    debounceEntity() {
      this.$emit('emittedEntity', this.searchEntity)
    },
    getFullName(item) {
      return item.customerFullName
    },
    getEmpFullName(item) {
      return fullName(item)
    },
    getPartnerName(item) {
      return item.partnerName || 'Unknown Partner'
    },
    cancel() {
      this.resetForm()
      this.$refs.form.reset()
      this.$emit('caseAdded')
    },
    resetForm() {
      this.caseType = this.caseConfig.name
      this.partnerId = null
      this.searchPartner = ''

      this.caseNo = ''
      this.typeOfLoan = ''
      this.bkt = ''
      this.pos = null
      this.emiAmount = null
      this.tenure = null
    },
    populate() {
      if (this.editCase) {
        this.caseType = this.editCase?.caseType

        // Populate original fields
        this.caseNo = this.editCase?.caseNo
        this.typeOfLoan = this.editCase?.typeOfLoan
        this.bkt = this.editCase?.bkt
        this.pos = this.editCase?.pos
        this.emiAmount = this.editCase?.emiAmount
        this.tenure = this.editCase?.tenure

        this.assignedTo = this.editCase?.assignedTo?._id
        this.customerId = this.editCase?.customer?._id
        this.selectedEntity = this.editCase?.entity?._id
        this.partnerId = this.editCase?.partner?._id

        if (this.editCase?.customer?.customerFullName) {
          this.searchCustomer = this.editCase?.customer?.customerFullName
          this.fetchCustomers()
        }
        if (this.editCase?.assignedTo?.firstName) {
          this.searchUser = this.editCase?.assignedTo?.firstName
          this.fetchUsers()
        }
      }
    },
    formatDate(key) {
      this.formattedDates[key] = this.$dayjs(this.formData[key]).format('DD/MM/YYYY')
      this.dateMenus[key] = false
    },
    clearDate(key) {
      this.formData[key] = null
      this.formattedDates[key] = ''
    }
  },
  async mounted() {
    this.populate()
  }
}
</script>
<style></style>
