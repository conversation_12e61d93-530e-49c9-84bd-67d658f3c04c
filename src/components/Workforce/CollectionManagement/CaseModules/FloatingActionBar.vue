<template>
  <div>
    <v-slide-y-reverse-transition>
      <v-card v-if="selected.length > 0" class="floating-actions-bar elevation-4">
        <v-card-text class="py-3">
          <div class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-progress-circular
                v-if="bulkUpdating"
                indeterminate
                size="20"
                width="2"
                color="primary"
                class="mr-2"
              ></v-progress-circular>
              <v-icon v-else class="mr-2" color="primary">mdi-checkbox-marked-circle</v-icon>
              <span class="font-weight-medium text--primary">
                {{ bulkUpdating ? 'Updating...' : `${selected.length} record(s) selected` }}
              </span>
            </div>
            <div>
              <!-- DELETE SELECTED BUTTON -->
              <v-btn
                v-if="showDeleteSelected"
                small
                color="primary"
                @click="$emit('delete-selected')"
                :disabled="bulkUpdating || selected.length === 0"
                class="mr-2"
              >
                <v-icon left small>mdi-delete</v-icon>
                Delete Selected
              </v-btn>
              <v-btn
                v-if="showDeleteAll"
                small
                color="primary"
                @click="$emit('delete-all')"
                :disabled="bulkUpdating"
                class="mr-2"
              >
                <v-icon left small>mdi-delete-sweep</v-icon>
                Delete All
              </v-btn>
              <v-menu v-if="showUpdateStatus" offset-y top :disabled="bulkUpdating">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    small
                    color="primary"
                    v-bind="attrs"
                    v-on="on"
                    class="mr-2"
                    :disabled="bulkUpdating"
                  >
                    <v-icon left small>mdi-update</v-icon>
                    Update Status
                  </v-btn>
                </template>
                <v-list color="white" dense class="pa-0">
                  <v-list-item
                    v-for="status in statusOptions"
                    :key="status.value"
                    @click="handleBulkStatusUpdate(status.value)"
                    class="px-3 py-1"
                    dense
                  >
                    <v-list-item-title class="text-caption">{{ status.text }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
              <v-btn
                v-if="showClear"
                small
                color="primary"
                @click="$emit('clear-selection')"
                :disabled="bulkUpdating"
              >
                <v-icon left small>mdi-close</v-icon>
                Clear Selection
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-slide-y-reverse-transition>
  </div>
</template>

<script>
export default {
  name: 'FloatingActionBar',
  props: {
    selected: {
      type: Array,
      default: () => []
    },
    bulkUpdating: {
      type: Boolean,
      default: false
    },
    statusOptions: {
      type: Array,
      default: () => []
    },
    // Control which buttons to show
    showDeleteSelected: {
      type: Boolean,
      default: false
    },
    showDeleteAll: {
      type: Boolean,
      default: false
    },
    showUpdateStatus: {
      type: Boolean,
      default: false
    },
    showClear: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleBulkStatusUpdate(status) {
      this.$emit('bulk-status-update', status)
    }
  }
}
</script>
<style scoped>
.floating-actions-bar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  min-width: 60vw;
  max-width: 70vw;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background-color: rgb(222, 221, 221);
}
</style>
