<template>
  <div>
    <div
      class="justify-end items-center my-3"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
    >
      <div
        class="items-center justify-end mb-0"
        :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']"
      >
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? 'py-0 pr-0 mb-2' : 'ml-auto py-2']"
        >
          <div class="custom-input-group">
            <v-select
              :items="selectItems"
              v-model="selectValue"
              solo
              flat
              dense
              hide-details
              outlined
              class="select-input"
              label="Select"
              :style="{ width: '130px' }"
              @change="handleSelect"
            />
            <v-text-field
              v-model="filterValue"
              :label="searchPlaceholder"
              :placeholder="searchPlaceholder"
              hide-details
              class="text-input"
              solo
              flat
              outlined
              clearable
              dense
              :style="{ width: '300px' }"
              @input="handleInput"
            ></v-text-field>
          </div>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? 'py-0 pr-0 mb-2' : 'ml-auto py-2']"
        >
          <v-select
            v-model="filterStatus"
            label="Call Status"
            :items="statusOptions"
            placeholder="Call Status"
            hide-details
            solo
            flat
            dense
            outlined
            @change="handleInput"
          ></v-select>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? 'py-0 pr-0 mb-2' : 'ml-auto py-2']"
        >
          <v-menu
            v-model="menu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
          >
            <template v-slot:activator="{ on }">
              <v-text-field
                label="Select Call Date"
                placeholder="Select Call Date"
                v-model="filterDate"
                prepend-inner-icon="mdi-calendar"
                readonly
                hide-details
                outlined
                flat
                solo
                dense
                clearable
                v-on="on"
                @click:clear="clearDate"
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="filterDate"
              @input="handleInput"
              no-title
              @change="menu = false"
              scrollable
              clearable
            />
          </v-menu>
        </v-col>
        <div
          class="justify-end mb-2 items-center"
          :class="[$vuetify.breakpoint.lgAndUp ? 'inline-flex' : 'inline-flex']"
        >
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="ml-2 mr-2 white--text"
            @click="exportToXLSX"
            :loading="exportBtnLoad"
            v-tooltip="{
              text: 'Export Call History'
            }"
          >
            Export Call History
            <v-icon right dark>mdi-download</v-icon>
          </v-btn>

          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            @click="handleRefresh"
            class="white--text"
          >
            Refresh Call History
          </v-btn>
        </div>
      </div>
    </div>
    <div class="overflow-x-auto overflow-y-auto">
      <v-data-table
        v-model="selected"
        :headers="headers"
        :items="formattedHistory"
        class="pl-7 pr-7 pt-4 text-sm font-semibold custom-table"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :options.sync="options"
        :server-items-length="totalItems"
        fixed-header
        :loading="loadTable"
        :items-per-page="rowsPerPage"
      >
        <template v-slot:item.caseNo="{ item }">
          <td
            class="font-semibold hover:text-blue-700 dark-hover-text cursor-pointer px-4 py-6"
            @click="caseDetails(item)"
          >
            {{ formattedData(item?.additionalInfo?.caseInfo.caseNo) }}
          </td>
        </template>
        <template v-slot:item.CALL_ID="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.response?.CALL_ID || '-' }}
          </td>
        </template>

        <template v-slot:item.agentName="{ item }">
          <td
            class="px-4 py-6 font-semibold"
            v-html="formattedNameNumber(item.additionalInfo.agentName, item.agentNumber)"
          ></td>
        </template>
        <template v-slot:item.customerFullName="{ item }">
          <td
            class="px-4 py-6 font-semibold"
            v-html="
              formattedNameNumber(
                item.additionalInfo.caseInfo.customerFullName,
                item.customerNumber
              )
            "
          ></td>
        </template>
        <template v-slot:item.createdAt="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ $dayjs(item.createdAt).format('DD/MM/YYYY hh:mm:ss A') }}
          </td>
        </template>
        <template v-slot:item.A_PARTY_CONNECTED_TIME="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ formatTime(item.A_PARTY_CONNECTED_TIME) }}
          </td>
        </template>

        <template v-slot:item.B_PARTY_END_TIME="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ formatTime(item.B_PARTY_END_TIME) }}
          </td>
        </template>
        <template v-slot:item.comments="{ item }">
          <td class="px-4 font-semibold">
            <div class="flex items-center gap-2">
              <p class="ellipsisClass w-20 mb-0">
                {{ item.comments[item.comments.length - 1]?.comment }}
              </p>

              <v-icon
                v-tooltip="{
                  text: 'See Comments'
                }"
                @click.stop="toggleMenu(item._id, item.comments)"
                v-if="item.comments.length"
                style="font-size: 20px"
                color="#fb8c00"
              >
                mdi-information
              </v-icon>

              <CommentsPopOver
                v-if="menuState[item._id]"
                :comments="selectedComments[item._id]"
                :menuState="menuState[item._id]"
                @close="closeMenu(item._id)"
              ></CommentsPopOver>
            </div>
          </td>
        </template>
        <template v-slot:item.duration="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ calculateDuration(item.A_PARTY_CONNECTED_TIME, item.B_PARTY_END_TIME) }}
          </td>
        </template>
        <template v-slot:item.callStatus="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.response ? callDialStatus(item.response)?.message : '' }}
          </td>
        </template>

        <template v-slot:item.RecordVoice="{ item }">
          <td
            v-if="
              item.response &&
              item.response?.RecordVoice &&
              item.response?.RecordVoice !== 'No Voice'
            "
            class="px-4 py-6 font-semibold"
          >
            <audio controls class="w-24 h-8">
              <source :src="item.response.RecordVoice" type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </td>
          <td v-else class="px-4 py-6 font-semibold">
            <span>No Voice</span>
          </td>
        </template>
        <template v-slot:item.retry="{ item }">
          <v-icon
            class="text-green-600 cursor-pointer hover:text-green-800"
            size="24"
            @click="handleRetry(item)"
            color="green"
            v-show="isCallingEnabled"
            v-tooltip="{
              text: 'Retry Call'
            }"
          >
            mdi-phone-refresh
          </v-icon>
        </template>
      </v-data-table>
    </div>
    <v-dialog v-model="openCaseDetails" max-width="70rem" persistent>
      <CaseDetails
        :selectedCase="selectedCase"
        :caseConfig="caseConfig"
        @closeDialog="closeDialog"
        :callTab="true"
      />
    </v-dialog>
  </div>
</template>

<script>
import CommentsPopOver from '@/components/CommentsPopOver.vue'
import CaseDetails from '../CaseDetails'
// import DialerPopOver from "@/components/DialerPopOver.vue";
import { isModuleFeatureAllowed, debounce } from '@/utils/common'
import { callStatusMap } from '@/utils/workforce/utils.js'
export default {
  name: 'CallHistory',
  data() {
    return {
      analytics: [],
      filterEmployee: '',
      singleSelect: false,
      selected: [],
      callHistory: [],
      options: { sortBy: [], sortDesc: [], itemsPerPage: 10, page: 1 },
      headers: [],
      totalItems: 0,
      loadTable: true,
      baseRowHeight: 90,
      baseTableHeight: 100,
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      menuState: {},
      selectedComments: {},
      openCaseDetails: false,
      selectedCase: null,
      isDialerOpen: false,
      exportCallHistory: [],
      exportBtnLoad: false,
      filterValue: '',
      filterCustomer: '',
      filterStatus: '1',
      filterDate: this.$dayjs().format('YYYY-MM-DD'),
      menu: false,
      selectItems: [
        { text: 'Customer', value: 'CUSTOMER' },
        { text: 'Employee', value: 'EMPLOYEE' }
      ],
      selectValue: 'CUSTOMER',
      statusOptions: [
        { text: 'Connected', value: '1' },
        { text: 'Not-Connected', value: '0' },
        { text: 'All', value: null }
      ]
    }
  },
  props: {
    caseConfig: Object
  },
  components: {
    CommentsPopOver,
    CaseDetails
  },
  computed: {
    formattedHistory() {
      return this.callHistory.map((entry) => ({
        ...entry,
        response: entry.response,
        A_PARTY_CONNECTED_TIME: entry?.response?.A_PARTY_CONNECTED_TIME || 0,
        B_PARTY_END_TIME: entry?.response?.B_PARTY_END_TIME || 0,
        duration: this.calculateDuration(
          entry?.response?.A_PARTY_CONNECTED_TIME || 0,
          entry?.response?.B_PARTY_END_TIME || 0
        ),
        RecordVoice: entry?.response?.RecordVoice || 'No voice'
      }))
    },
    // rowsPerPage() {
    //   const availableHeight = this.windowHeight - this.baseTableHeight;
    //   this.options.itemsPerPage =
    //     Math.floor(availableHeight / this.baseRowHeight) || 5;
    //   return Math.floor(availableHeight / this.baseRowHeight) || null;
    // },
    rowsPerPage() {
      const availableHeight = this.windowHeight - this.baseTableHeight
      return Math.floor(availableHeight / this.baseRowHeight) || null
    },
    isCallingEnabled() {
      return isModuleFeatureAllowed('ADDONS', 'CALL_A_CUSTOMER')
    },
    searchPlaceholder() {
      if (this.selectValue === 'EMPLOYEE') {
        return 'Search by employee number'
      }
      return 'Search by customer number'
    }
  },
  methods: {
    openDialerCard() {
      this.isDialerOpen = !this.isDialerOpen
    },
    handleRetry(item) {
      this.openDialerCard()
      this.callId = item._id
      this.$emit('retry-call', item)
    },
    async handleRefresh() {
      await this.fetchCallHistory()
    },
    closeDialog() {
      this.openCaseDetails = false
    },
    async caseDetails(item) {
      if (item?.additionalInfo?.caseInfo.caseNo) {
        await this.fetchCases(item?.additionalInfo?.caseInfo.caseNo)
      }
    },
    toggleMenu(itemId, comments) {
      Object.keys(this.menuState).forEach((key) => {
        if (key !== itemId) {
          this.$set(this.menuState, key, false)
        }
      })
      this.$set(this.menuState, itemId, !this.menuState[itemId])

      if (this.menuState[itemId]) {
        this.$set(this.selectedComments, itemId, comments)
      } else {
        this.$delete(this.selectedComments, itemId)
      }
    },
    closeMenu(id) {
      this.$set(this.menuState, id, false)
    },
    formattedNameNumber(name, number) {
      return `${name}<br> +91-${number}`
    },
    formattedData(name) {
      return name
    },
    formatTime(timeString) {
      if (!timeString) return '-'
      const formattedDate = this.$dayjs(timeString, 'DDMMYYYYHHmmss').format(
        'DD/MM/YYYY hh:mm:ss A'
      )
      return formattedDate
    },
    calculateDuration(start, end) {
      if (!start || !end) return '-'
      const startTime = this.$dayjs(start, 'DDMMYYYYHHmmss')
      const endTime = this.$dayjs(end, 'DDMMYYYYHHmmss')

      const diff = this.$dayjs.duration(endTime.diff(startTime))
      const hours = diff.hours().toString().padStart(2, '0')
      const minutes = diff.minutes().toString().padStart(2, '0')
      const seconds = diff.seconds().toString().padStart(2, '0')

      return `${hours}:${minutes}:${seconds}`
    },
    filterParams() {
      const params = {}

      if (this.caseConfig?.name) {
        params.caseType = this.caseConfig?.name
      }
      return params
    },

    async exportToXLSX() {
      this.exportBtnLoad = true
      await this.fetchCallHistory(true)
      if (this.exportCallHistory.length > 0) {
        const excelHeaders = [
          'SL No.',
          'Date',
          'Call ID',
          this.caseConfig?.fieldMappings?.caseNo?.displayName,
          'Agent Name',
          'Customer Name',
          'Initiated Time',
          'Start Date/Time',
          'End Date/Time',
          'Duration',
          'Comments',
          'Status',
          this.caseConfig?.fieldMappings?.caseOutcomeStatus?.displayName || 'Case Outcome',
          'Call Recording'
        ]

        const excelData = this.exportCallHistory?.map((item, index) => [
          index + 1,
          this.$dayjs(item?.createdAt).format('DD/MM/YYYY'),
          item?.response?.CALL_ID,
          item?.additionalInfo?.caseInfo?.caseNo,
          `${item?.additionalInfo?.agentName}\n (${item?.agentNumber})`,
          `${item?.additionalInfo?.caseInfo?.customerFullName}\n (${item?.customerNumber})`,
          `${this.$dayjs(item?.createdAt).format('DD/MM/YYYY hh:mm:ss A')}`,
          `${this.formatTime(item?.response?.A_PARTY_CONNECTED_TIME)}`,
          `${this.formatTime(item?.response?.B_PARTY_END_TIME)}`,
          `${this.calculateDuration(
            item?.response?.A_PARTY_CONNECTED_TIME,
            item?.response?.B_PARTY_END_TIME
          )}`,
          item?.comments[item.comments.length - 1]?.comment,
          item.response ? this.callDialStatus(item.response)?.message : '-',
          item?.additionalInfo?.caseInfo?.userDefinedCaseStatus || ' - ',
          item?.voiceRecord
        ])

        const worksheetData = [excelHeaders, ...excelData]
        const XLSX = require('xlsx')

        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.aoa_to_sheet(worksheetData)

        XLSX.utils.book_append_sheet(wb, ws, 'Call History')

        XLSX.writeFile(wb, `${this.caseConfig.name}_Call_History.xlsx`)
      }
      this.exportBtnLoad = false
    },
    async fetchCallHistory(exportData = false) {
      try {
        const { page, itemsPerPage } = this.options
        const filterParams = this.filterParams()
        let params = null
        if (exportData) {
          params = {
            page: 1,
            limit: '*',
            sendStats: 1,
            ...filterParams
          }
        } else {
          params = {
            page,
            limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems,
            sendStats: 1,
            ...filterParams
          }
          this.loadTable = true
        }

        if (this.filterValue && this.selectValue === 'CUSTOMER') {
          params.crn = this.filterValue
        }

        if (this.filterValue && this.selectValue === 'EMPLOYEE') {
          params.agentPhoneNo = this.filterValue
        }

        if (this.filterCustomer) {
          params.crn = this.filterCustomer
        }

        if (this.filterEmployee) {
          params.agentPhoneNo = this.filterEmployee
        }

        if (this.filterStatus) {
          params.hasAudio = this.filterStatus
        }

        if (this.filterDate) {
          params.startDate = this.filterDate
        }

        const response = await this.$axios.get('workforce/tele-caller/history', { params })

        if (response?.data?.success && !exportData) {
          this.callHistory = response?.data?.history
          this.totalItems = response.data?.pagination?.totalCount
          const stats = response.data?.stats
          this.$emit('callStats', stats)
        } else if (exportData) {
          this.exportCallHistory = response?.data?.history
        }
        this.loadTable = false
      } catch (error) {
        this.loadTable = false
        console.error(error)
      }
    },
    async fetchCases(item) {
      try {
        const filterParams = this.filterParams()
        const params = {
          search: item,
          ...filterParams
        }

        const response = await this.$axios.get('/workforce/collection/cases', {
          params
        })
        if (response?.data?.success) {
          if (!response.data?.cases || !response.data?.cases.length) {
            this.$toast.error('Case details not found')
            return
          }
          this.selectedCase = response.data?.cases[0]
          this.openCaseDetails = true
        }
      } catch (error) {
        console.log(error)
        const errorMessage = error?.response?.data?.message || error
        this.$toast.error(errorMessage)
      }
    },
    callDialStatus(response) {
      const { EVENT_TYPE = '', A_DIAL_STATUS = '', B_DIAL_STATUS = '' } = response

      const key = `${EVENT_TYPE.trim()}|${A_DIAL_STATUS.trim()}|${B_DIAL_STATUS.trim()}`

      // console.log("kkl", callStatusMap.get(key), key);
      return callStatusMap.get(key)
    },
    handleResize() {
      this.windowHeight = window.innerHeight
      this.windowWidth = window.innerWidth
    },
    async handleInput() {
      this.options.page = 1
      await this.fetchCallHistory()
    },
    async clearDate() {
      this.filterDate = ''
      await this.fetchCallHistory()
    },
    async handleSelect() {
      this.filterValue = ''
      this.options.page = 1
      await this.fetchCallHistory()
    }
  },
  beforeCreate() {
    this.debouncedFetchCallHistory = () => {}
  },
  created() {
    this.debouncedFetchCallHistory = debounce(this.fetchCallHistory, 800)
  },
  watch: {
    '$route.query.customer': {
      handler(customer) {
        try {
          this.filterCustomer = customer || ''
          this.options.page = 1
          this.debouncedFetchCallHistory()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },
    '$route.query.empNo': {
      async handler(empNo) {
        try {
          this.filterEmployee = empNo || ''
          this.options.page = 1
          await this.debouncedFetchCallHistory()
        } catch (error) {
          console.error('Error updating employee search:', error)
        }
      },
      immediate: true
    },
    options: {
      async handler() {
        await this.fetchCallHistory()
      },
      deep: true
    },
    caseConfig: {
      handler(config) {
        if (config) {
          const fm = config.fieldMappings

          const columns = [
            {
              text: fm.caseNo?.displayName,
              value: 'caseNo',
              align: 'start',
              sortable: false,
              width: '10%'
            },
            {
              text: 'Call ID',
              value: 'CALL_ID',
              align: 'start',
              sortable: false,
              width: '10%'
            },
            {
              text: 'Agent Name',
              value: 'agentName',
              align: 'start',
              sortable: false,
              width: '12%'
            },
            {
              text: 'Customer Name',
              value: 'customerFullName',
              align: 'start',
              sortable: false,
              width: '12%'
            },
            {
              text: 'Initiated Date/Time',
              value: 'createdAt',
              align: 'start',
              sortable: false
            },
            {
              text: 'Start Date/Time',
              value: 'A_PARTY_CONNECTED_TIME',
              align: 'start',
              sortable: false
            },
            {
              text: 'End Date/Time',
              value: 'B_PARTY_END_TIME',
              align: 'start',
              sortable: false
            },
            {
              text: 'Duration',
              value: 'duration',
              align: 'start',
              sortable: false
            },
            { text: 'Comments', value: 'comments', align: 'start' },
            {
              text: 'Status',
              value: 'callStatus',
              align: 'start',
              sortable: false
            },
            {
              text: 'Call Recording',
              value: 'RecordVoice',
              align: 'start',
              sortable: false
            },
            { value: 'retry', align: 'start', sortable: false }
          ]

          this.headers = columns
        }
      },
      immediate: true
    }
  },
  async mounted() {
    window.addEventListener('resize', this.handleResize)
    this.handleResize()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style scoped>
.custom-table .v-data-table-header th,
.custom-table .v-data-table td {
  text-align: left;
  padding-left: 16px;
  padding-right: 16px;
}

.custom-input-group {
  display: flex;
  align-items: center;
  padding: 2px;
  background-color: white;
  /* border: 1px solid #abaaaa; */
  border-radius: 5px;
}

.custom-input-group .v-select .v-input__control,
.custom-input-group .v-autocomplete .v-input__control {
  background-color: white !important;
}

.select-input {
  flex-grow: 1;
  width: 15%;
  font-size: smaller;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.text-input {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
</style>
