<template>
  <div>
    <div class="bg-white rounded-lg h-[70vh] w-full flex flex-col">
      <!-- Modal Header -->
      <!-- <div class="p-5 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-xl font-semibold text-[#21427d]">Bulk Create via Excel</h2>
        <button
          @click="closeDialog"
          :disabled="uploading"
          class="text-2xl text-gray-600 hover:text-gray-800"
        >
          &times;
        </button>
      </div> -->

      <!-- Header: Tab Navigation (fixed) -->
      <div class="p-5 border-b border-gray-200">
        <div class="flex">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'px-5 py-2 cursor-pointer border-b-3 font-medium transition-all duration-200',
              activeTab === tab.key
                ? 'text-blue-600 border-blue-500'
                : 'text-gray-600 border-transparent hover:text-blue-600'
            ]"
          >
            {{ tab.label }}
          </button>
        </div>
      </div>

      <!-- Body: Scrollable content -->
      <div class="flex-1 overflow-y-auto p-5">
        <!-- Upload CSV Tab -->
        <div v-if="activeTab === 'upload'" class="tab-content">
          <div
            @drop="handleDrop"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            :class="[
              'border-2 border-dashed rounded-lg p-8 text-center h-[55vh] mb-5 transition-all duration-200 cursor-pointer',
              isDragOver ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:border-blue-500'
            ]"
            @click="$refs.fileInput.click()"
          >
            <div class="tab-items">
              <div class="text-4xl mb-3">📁</div>
              <h3 class="text-lg font-semibold mb-2">Drag & Drop your Excel file here</h3>
              <p class="text-gray-600 mb-3">or</p>
              <button
                class="px-5 py-2 border border-blue-500 text-blue-500 rounded hover:bg-blue-50 transition-colors"
              >
                Browse Files
              </button>
              <input
                ref="fileInput"
                type="file"
                accept=".xlsx,.xls"
                @change="handleFileSelect"
                class="hidden"
              />

              <!-- Selected File Display -->
              <div
                v-if="selectedFile"
                class="flex items-center bg-gray-100 p-3 rounded-lg mt-4 w-full"
              >
                <span class="flex-1 text-left truncate">{{ selectedFile.name }}</span>
                <button @click.stop="removeFile" class="text-red-500 text-xl ml-3">&times;</button>
              </div>
            </div>
          </div>

          <!-- Validation Results -->
          <div v-if="validationMessages.length > 0" class="mb-5">
            <div
              v-for="(message, index) in validationMessages"
              :key="index"
              :class="[
                'p-3 mb-2 border-l-4',
                message.type === 'error'
                  ? 'border-red-500 bg-red-50 text-red-700'
                  : 'border-green-500 bg-green-50 text-green-700'
              ]"
            >
              {{ message.text }}
            </div>
          </div>

          <!-- Upload Progress -->
          <div v-if="uploading" class="mt-5">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: uploadProgress + '%' }"
              ></div>
            </div>
            <p class="text-center mt-2 text-sm text-gray-600">{{ Math.ceil(uploadProgress) }}%</p>
          </div>

          <!-- Upload Results -->
          <div v-if="uploadComplete" class="mt-5">
            <!-- Success Summary -->
            <div
              v-if="uploadResults.success > 0"
              class="border border-green-200 rounded-lg p-4 mb-4"
            >
              <div class="flex items-center">
                <div class="text-green-500 text-2xl mr-4">✓</div>
                <div>
                  <h4 class="text-lg font-semibold text-green-700">
                    {{ uploadResults.success }} Loans Created Successfully
                  </h4>
                  <p class="text-sm text-gray-600">These loans have been added to your system.</p>
                </div>
              </div>
            </div>

            <!-- Failed Summary -->
            <div v-if="uploadResults.failed > 0" class="border border-red-200 rounded-lg p-4 mb-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="text-red-500 text-2xl mr-4">⚠</div>
                  <div>
                    <h4 class="text-lg font-semibold text-red-700">
                      {{ uploadResults.failed }} Loans Failed
                    </h4>
                    <p class="text-sm text-gray-600">
                      These loans could not be processed due to errors.
                    </p>
                  </div>
                </div>
                <button
                  @click="downloadFailedLoans"
                  :disabled="downloadingFailed"
                  class="px-4 py-2 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                >
                  <span v-if="downloadingFailed">Downloading...</span>
                  <span v-else>Download Failed</span>
                </button>
              </div>
            </div>

            <!-- Failed Loans Details -->
            <div v-if="invalidLoans.length > 0" class="border border-gray-200 rounded-lg">
              <button
                @click="showFailedDetails = !showFailedDetails"
                class="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <div class="flex items-center">
                  <span class="text-red-500 mr-2">⚠</span>
                  <span class="font-medium"
                    >View Failed Loans Details ({{ invalidLoans.length }})</span
                  >
                </div>
                <span class="text-gray-400">{{ showFailedDetails ? '−' : '+' }}</span>
              </button>
              <div
                v-if="showFailedDetails"
                class="border-t border-gray-200 max-h-80 overflow-y-auto"
              >
                <div
                  v-for="(loan, index) in invalidLoans"
                  :key="index"
                  class="p-3 border-b border-gray-100 last:border-b-0"
                >
                  <div class="flex items-start">
                    <span class="text-red-500 mr-2 mt-1">×</span>
                    <div>
                      <div class="font-medium text-sm">
                        <strong>Row {{ index + 2 }}:</strong> {{ getLoanIdentifier(loan) }}
                      </div>
                      <div class="text-red-600 text-sm mt-1">{{ loan.failedReason }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Instructions Tab -->
        <div v-if="activeTab === 'instructions'" class="tab-content">
          <h3 class="text-lg font-semibold mb-4">CSV File Requirements</h3>
          <ul class="space-y-2 mb-6">
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span
                >File must be in CSV (Comma Separated Values) format or Excel (.xlsx, .xls)</span
              >
            </li>
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>First row must contain column headers exactly as specified</span>
            </li>
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>File should not contain empty rows</span>
            </li>
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>Maximum file size: 10MB</span>
            </li>
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>Maximum records per file: 1000</span>
            </li>
          </ul>

          <h3 class="text-lg font-semibold mb-4">Required Fields</h3>
          <div class="overflow-x-auto mb-6">
            <table class="w-full border-collapse">
              <thead>
                <tr class="bg-gray-100">
                  <th class="p-3 text-left border-b border-gray-200 text-gray-800">Field Name</th>
                  <th class="p-3 text-left border-b border-gray-200 text-gray-800">Type</th>
                  <th class="p-3 text-left border-b border-gray-200 text-gray-800">Required</th>
                  <th class="p-3 text-left border-b border-gray-200 text-gray-800">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="p-3 border-b border-gray-200">CRN</td>
                  <td class="p-3 border-b border-gray-200">Text</td>
                  <td class="p-3 border-b border-gray-200">Yes</td>
                  <td class="p-3 border-b border-gray-200">Customer Reference Number</td>
                </tr>
                <tr>
                  <td class="p-3 border-b border-gray-200">
                    {{ caseConfig?.fieldMappings?.caseNo?.displayName || 'Case No' }}
                  </td>
                  <td class="p-3 border-b border-gray-200">Text</td>
                  <td class="p-3 border-b border-gray-200">Yes</td>
                  <td class="p-3 border-b border-gray-200">Unique case identifier</td>
                </tr>
                <tr v-for="(mapping, key) in caseConfig?.fieldMappings" :key="key">
                  <td
                    v-if="mapping.displayName && key !== 'caseNo'"
                    class="p-3 border-b border-gray-200"
                  >
                    {{ mapping.displayName }}
                  </td>
                  <td
                    v-if="mapping.displayName && key !== 'caseNo'"
                    class="p-3 border-b border-gray-200"
                  >
                    Text
                  </td>
                  <td
                    v-if="mapping.displayName && key !== 'caseNo'"
                    class="p-3 border-b border-gray-200"
                  >
                    No
                  </td>
                  <td
                    v-if="mapping.displayName && key !== 'caseNo'"
                    class="p-3 border-b border-gray-200"
                  >
                    Optional field
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <h3 class="text-lg font-semibold mb-4">Tips for Success</h3>
          <ul class="space-y-2">
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>Use text qualifiers (") if your data contains commas</span>
            </li>
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>Dates should be formatted as YYYY-MM-DD</span>
            </li>
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>Avoid special characters in field names</span>
            </li>
            <li class="flex items-start">
              <span class="text-blue-500 font-bold mr-3">•</span>
              <span>Save your file with UTF-8 encoding to preserve special characters</span>
            </li>
          </ul>
        </div>

        <!-- Template Tab -->
        <div v-if="activeTab === 'template'" class="tab-content">
          <div class="border border-gray-200 rounded-lg p-5 mb-5">
            <h3 class="text-lg font-semibold mb-3">Download Template</h3>
            <p class="text-gray-600 mb-4">
              Use this template to ensure your CSV file has the correct format and required fields.
            </p>
            <button
              @click="downloadSample"
              :disabled="downloadingSample"
              class="px-5 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              <span v-if="downloadingSample">Downloading...</span>
              <span v-else>Download Template</span>
            </button>
          </div>

          <h3 class="text-lg font-semibold mb-3">Template Preview</h3>
          <div
            class="bg-gray-50 border border-gray-200 p-4 mb-4 overflow-x-auto font-mono text-sm whitespace-pre"
          >
            {{ templatePreview }}
          </div>
          <p><strong>Note:</strong> The first line must contain these exact column headers.</p>
        </div>

        <!-- Error Messages -->
        <div v-if="errorMessage" class="mt-5 p-4 border-l-4 border-red-500 bg-red-50 text-red-700">
          <div class="flex justify-between items-start">
            <span>{{ errorMessage }}</span>
            <button @click="errorMessage = null" class="text-red-500 ml-3">&times;</button>
          </div>
        </div>

        <!-- Success Messages -->
        <div
          v-if="successMessage"
          class="mt-5 p-4 border-l-4 border-green-500 bg-green-50 text-green-700"
        >
          <div class="flex justify-between items-start">
            <span>{{ successMessage }}</span>
            <button @click="successMessage = null" class="text-green-500 ml-3">&times;</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { extractExcelHeaders } from '@/utils/common'

export default {
  name: 'LoanBulkUpload',
  props: {
    caseConfig: {
      type: Object,
      required: true
    },
    saving: {
      type: Boolean,
      default: false
    },
    editCase: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // Tab management
      activeTab: 'upload',
      tabs: [
        { key: 'upload', label: 'Upload CSV' },
        { key: 'instructions', label: 'Instructions' },
        { key: 'template', label: 'Template' }
      ],

      // File handling
      selectedFile: null,
      isDragOver: false,
      uploading: false,
      validating: false,
      uploadComplete: false,
      uploadProgress: 0,

      // Validation
      validationPassed: false,
      validationErrors: [],
      matchedHeaders: [],
      fileErrors: [],
      validationMessages: [],

      // Upload Results
      uploadResults: {
        success: 0,
        failed: 0
      },
      invalidLoans: [],
      showFailedDetails: false,

      // Messages
      errorMessage: null,
      successMessage: null,

      // Download states
      downloadingFailed: false,
      downloadingSample: false
    }
  },
  computed: {
    templatePreview() {
      const fieldMappings = this.caseConfig?.fieldMappings || {}
      const headers = ['crn', 'partner']

      // Add field mappings headers
      Object.keys(fieldMappings).forEach((key) => {
        const mapping = fieldMappings[key]
        if (mapping && mapping.displayName && mapping.displayName.trim()) {
          headers.push(mapping.displayName)
        }
      })

      // Create sample rows
      const sampleRow1 = ['CRN001', 'Bank ABC']
      const sampleRow2 = ['CRN002', 'Bank XYZ']

      // Add sample data for each field mapping
      Object.keys(fieldMappings).forEach((key) => {
        const mapping = fieldMappings[key]
        if (mapping && mapping.displayName && mapping.displayName.trim()) {
          switch (key) {
            case 'caseNo':
              sampleRow1.push('LOAN001')
              sampleRow2.push('LOAN002')
              break
            case 'customerFullName':
              sampleRow1.push('John Doe')
              sampleRow2.push('Jane Smith')
              break
            case 'customerMobile':
              sampleRow1.push('**********')
              sampleRow2.push('**********')
              break
            case 'loanAmount':
              sampleRow1.push('100000')
              sampleRow2.push('250000')
              break
            default:
              sampleRow1.push('Sample Data 1')
              sampleRow2.push('Sample Data 2')
          }
        }
      })

      return `${headers.join(',')}\n${sampleRow1.join(',')}\n${sampleRow2.join(',')}`
    }
  },
  mounted() {
    console.log({ d: this.caseConfig })
  },
  methods: {
    // Drag and drop handlers
    handleDragOver(e) {
      e.preventDefault()
      e.stopPropagation()
    },

    handleDragEnter(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = true
    },

    handleDragLeave(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false
    },

    handleDrop(e) {
      e.preventDefault()
      e.stopPropagation()
      this.isDragOver = false

      const files = e.dataTransfer.files
      if (files.length > 0) {
        this.handleFileSelect({ target: { files: [files[0]] } })
      }
    },

    handleFileSelect(event) {
      this.clearMessages()
      this.validationMessages = []

      const file = event.target.files ? event.target.files[0] : event

      if (file) {
        // Validate file type
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel',
          'text/csv',
          'application/csv'
        ]

        const isValidType =
          allowedTypes.includes(file.type) ||
          file.name.endsWith('.csv') ||
          file.name.endsWith('.xlsx') ||
          file.name.endsWith('.xls')

        if (!isValidType) {
          this.validationMessages.push({
            type: 'error',
            text: 'Please select a valid CSV or Excel file (.csv, .xlsx, .xls)'
          })
          this.selectedFile = null
          return
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
          this.validationMessages.push({
            type: 'error',
            text: 'File size should be less than 10MB'
          })
          this.selectedFile = null
          return
        }

        this.selectedFile = file
        this.validationMessages.push({
          type: 'success',
          text: 'File format appears valid'
        })

        // Auto-validate file
        setTimeout(() => {
          this.validateFile()
        }, 500)
      }
    },

    removeFile() {
      this.selectedFile = null
      this.validationMessages = []
      this.resetValidation()
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }
    },

    processFile() {
      if (!this.selectedFile) return
      this.uploadFile()
    },

    async validateFile() {
      if (!this.selectedFile) return

      this.validating = true
      this.validationMessages = this.validationMessages.filter((msg) => msg.type !== 'error')

      try {
        // Extract headers from Excel/CSV file
        const headers = await extractExcelHeaders([this.selectedFile])

        // Define required loan fields
        const requiredFields = this.getRequiredLoanFields()

        // Check for missing required fields
        const missingFields = []
        const matchedFields = []

        requiredFields.forEach((field) => {
          if (headers.includes(field)) {
            matchedFields.push(field)
          } else {
            missingFields.push(`Missing required column: ${field}`)
          }
        })

        this.matchedHeaders = matchedFields
        this.validationErrors = missingFields
        this.validationPassed = missingFields.length === 0

        // Update validation messages
        if (this.validationPassed) {
          this.validationMessages.push({
            type: 'success',
            text: 'Required columns found'
          })
          if (matchedFields.length > 0) {
            this.validationMessages.push({
              type: 'success',
              text: `Matched ${matchedFields.length} columns successfully`
            })
          }
        } else {
          missingFields.forEach((error) => {
            this.validationMessages.push({
              type: 'error',
              text: error
            })
          })
        }
      } catch (error) {
        console.error('Validation error:', error)
        this.validationMessages.push({
          type: 'error',
          text: 'Error processing the file. Please check the file format.'
        })
        this.validationPassed = false
      } finally {
        this.validating = false
      }
    },

    async uploadFile() {
      if (!this.selectedFile) return

      this.uploading = true
      this.uploadProgress = 0

      try {
        const formData = new FormData()
        formData.append('attachments', this.selectedFile)
        formData.append('subCaseId', this.caseConfig?._id)
        formData.append('useCase', this.caseConfig?.useCase)
        formData.append('caseType', this.caseConfig?.name)

        const config = {
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
          }
        }

        const response = await this.$axios.post('/workforce/loan/bulk', formData, config)

        // Process response
        this.uploadResults = {
          success: response.data?.data?.loansCreatedSuccessfully || 0,
          failed: response.data?.data?.loanCreationFailed || 0
        }

        this.invalidLoans = response.data?.data?.invalidLoans || []

        if (this.uploadResults.success > 0) {
          this.successMessage = `${this.uploadResults.success} loans uploaded successfully!`
          this.$emit('uploadComplete')
        }

        this.uploadComplete = true
      } catch (error) {
        console.error('Upload error:', error)
        this.errorMessage =
          error.response?.data?.message || 'Failed to upload loans. Please try again.'
      } finally {
        this.uploading = false
      }
    },

    async downloadFailedLoans() {
      if (this.invalidLoans.length === 0) return

      this.downloadingFailed = true

      try {
        const XLSX = require('xlsx')

        // Prepare failed loans data with reasons
        const failedData = this.invalidLoans.map((loan, index) => ({
          'Row Number': index + 2,
          'Failed Reason': loan.failedReason,
          ...loan
        }))

        // Create workbook and worksheet
        const worksheet = XLSX.utils.json_to_sheet(failedData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Failed Loans')

        // Download file
        const fileName = `Failed_Loans_${new Date().toISOString().split('T')[0]}.xlsx`
        XLSX.writeFile(workbook, fileName)
      } catch (error) {
        console.error('Download error:', error)
        this.errorMessage = 'Failed to download failed loans data.'
      } finally {
        this.downloadingFailed = false
      }
    },

    async downloadSample() {
      this.downloadingSample = true

      try {
        const XLSX = require('xlsx')

        // Create sample data structure based on caseConfig.fieldMappings
        const sampleData = this.generateSampleData()

        // Create workbook
        const worksheet = XLSX.utils.json_to_sheet(sampleData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Loan Sample')

        // Download file
        XLSX.writeFile(workbook, 'Loan_Upload_Sample.xlsx')
      } catch (error) {
        console.error('Sample download error:', error)
        this.errorMessage = 'Failed to download sample file.'
      } finally {
        this.downloadingSample = false
      }
    },

    getLoanIdentifier(loan) {
      return (
        loan['Application Number'] || loan['Customer Name'] || loan['Loan Amount'] || 'Unknown Loan'
      )
    },

    resetValidation() {
      this.validationPassed = false
      this.validationErrors = []
      this.matchedHeaders = []
    },

    clearMessages() {
      this.errorMessage = null
      this.successMessage = null
    },

    generateSampleData() {
      const sampleRows = []
      const fieldMappings = this.caseConfig?.fieldMappings || {}

      // Create sample data structure based on fieldMappings
      const sampleRow1 = {}
      const sampleRow2 = {}

      // Process all fieldMappings with displayName
      Object.keys(fieldMappings).forEach((key) => {
        const mapping = fieldMappings[key]
        if (mapping && mapping.displayName && mapping.displayName.trim()) {
          const displayName = mapping.displayName

          // Add sample data based on field type
          switch (key) {
            case 'caseNo':
              sampleRow1[displayName] = 'LOAN001'
              sampleRow2[displayName] = 'LOAN002'
              break
            case 'customerFullName':
              sampleRow1[displayName] = 'John Doe'
              sampleRow2[displayName] = 'Jane Smith'
              break
            case 'customerMobile':
              sampleRow1[displayName] = '**********'
              sampleRow2[displayName] = '**********'
              break
            case 'loanAmount':
              sampleRow1[displayName] = 100000
              sampleRow2[displayName] = 250000
              break
            case 'interestRate':
              sampleRow1[displayName] = 12.5
              sampleRow2[displayName] = 10.5
              break
            case 'tenure':
              sampleRow1[displayName] = 24
              sampleRow2[displayName] = 36
              break
            case 'emiAmount':
              sampleRow1[displayName] = 4707
              sampleRow2[displayName] = 8068
              break
            case 'typeOfLoan':
              sampleRow1[displayName] = 'Personal Loan'
              sampleRow2[displayName] = 'Home Loan'
              break
            case 'annualIncome':
              sampleRow1[displayName] = 600000
              sampleRow2[displayName] = 800000
              break
            case 'employmentType':
              sampleRow1[displayName] = 'Salaried'
              sampleRow2[displayName] = 'Self Employed'
              break
            case 'companyName':
              sampleRow1[displayName] = 'ABC Corp'
              sampleRow2[displayName] = 'XYZ Business'
              break
            case 'purpose':
              sampleRow1[displayName] = 'Home Renovation'
              sampleRow2[displayName] = 'Property Purchase'
              break
            case 'city':
              sampleRow1[displayName] = 'Mumbai'
              sampleRow2[displayName] = 'Delhi'
              break
            case 'state':
              sampleRow1[displayName] = 'Maharashtra'
              sampleRow2[displayName] = 'Delhi'
              break
            case 'pincode':
              sampleRow1[displayName] = '400001'
              sampleRow2[displayName] = '110001'
              break
            default:
              // Generic sample data for other fields
              sampleRow1[displayName] = 'Sample Data 1'
              sampleRow2[displayName] = 'Sample Data 2'
          }
        }
      })

      // Add mandatory CRN and Partner columns
      sampleRow1['crn'] = 'CRN001'
      sampleRow2['crn'] = 'CRN002'
      sampleRow1['partner'] = 'Bank ABC'
      sampleRow2['partner'] = 'Bank XYZ'

      sampleRows.push(sampleRow1, sampleRow2)
      return sampleRows
    },

    getRequiredLoanFields() {
      // Simplified validation - only check for CRN and Case No
      const fieldMappings = this.caseConfig?.fieldMappings || {}
      const requiredFields = []

      // Add CRN as mandatory
      requiredFields.push('crn')

      // Add Case No if it exists in fieldMappings
      if (fieldMappings.caseNo && fieldMappings.caseNo.displayName) {
        requiredFields.push(fieldMappings.caseNo.displayName)
      }

      return requiredFields
    },

    closeDialog() {
      this.$emit('close-bulk-upload')
    }
  }
}
</script>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.tab-content {
  min-height: 300px;
}
.tab-items {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}
/* Custom scrollbar for failed loans list */
.max-h-80::-webkit-scrollbar {
  width: 6px;
}

.max-h-80::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-80::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-80::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure proper spacing for dynamic content */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

/* Responsive table */
@media (max-width: 768px) {
  .overflow-x-auto table {
    font-size: 0.875rem;
  }

  .overflow-x-auto th,
  .overflow-x-auto td {
    padding: 0.5rem;
  }
}
</style>
