<template>
  <div>
    <div class="flex justify-end items-center">
      <div class="justify-end" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '3 pt-2 pb-3' : '12 py-2 pb-3'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-autocomplete
            class="wfm-search-fields"
            v-model="filterEmployee"
            label="Select Assignee"
            placeholder="Search Assignee"
            :items="users"
            item-text="fullName"
            item-value="_id"
            solo
            flat
            clearable
            outlined
            hide-details
            @change="handleInput"
          ></v-autocomplete>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '3 pt-2 pb-3' : '12 py-2 pb-3'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-menu
            v-model="menu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
          >
            <template v-slot:activator="{ on }">
              <v-text-field
                label="Select Date Range"
                placeholder="Select Date Range"
                v-model="formattedDate"
                prepend-inner-icon="mdi-calendar"
                readonly
                hide-details
                outlined
                dense
                clearable
                v-on="on"
                @click:clear="onDateCleared"
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="dates"
              @input="onDateSelected"
              no-title
              @change="menu = false"
              range
              scrollable
            />
          </v-menu>
        </v-col>
      </div>

      <v-btn
        :color="$vuetify.theme.currentTheme.primary"
        class="ml-2 mb-2 white--text"
        v-tooltip="{
          text: 'Export Analytics'
        }"
        v-show="canEdit"
        @click="exportToXLSX"
      >
        Export Analytics
        <v-icon right dark>mdi-download</v-icon>
      </v-btn>
    </div>
    <div class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="analytics"
        class="pt-4"
        :options.sync="options"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        fixed-header
      >
        <template v-slot:item="{ item }">
          <tr>
            <td v-for="(column, index) in headers" :key="index" class="px-4 py-6 font-semibold">
              {{ item[column.value] }}
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </div>
</template>

<script>
import {
  fullName,
  hasPermission,
  mergeOptions,
  mergeOptionsDetails,
  debounce
} from '@/utils/common'
import permissionData from '@/utils/permissions'
export default {
  props: {
    caseConfig: Object,
    employees: Array
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      headers: [],
      analytics: [],
      dates: [],
      filterEmployee: '',
      startDate: null,
      menu: false,
      endDate: null,
      unAttended: 'UN-ATTENDED/IN-COMPLETE',
      options: { sortBy: [], sortDesc: [], itemsPerPage: 10, page: 1 }
    }
  },
  computed: {
    users() {
      return this.employees.map((emp) => ({
        ...emp,
        fullName: fullName(emp)
      }))
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.collectionEdit)
    },
    formattedDate() {
      if (this.dates.length === 2) {
        const startDate = this.visibleDate(this.dates[0])
        const endDate = this.visibleDate(this.dates[1])
        return `${startDate} to ${endDate}`
      } else if (this.dates.length === 1) {
        return this.visibleDate(this.dates[0])
      }
      return ''
    },
    orgLanguage() {
      return this.orgLang.data.leftNav
    }
  },
  methods: {
    async fetchCaseAnalytics() {
      try {
        const params = {
          caseType: this.caseConfig?.name
        }
        if (this.startDate && this.endDate) {
          params.start = this.startDate
          params.end = this.endDate
        }
        if (this.filterEmployee) {
          params.search = this.filterEmployee
        }

        const response = await this.$axios.get('/workforce/collection/cases/analytics', {
          params
        })

        // Ensure userCollections is structured correctly
        const userCollections = response.data?.caseAnalytics.userCollectionStats.reduce(
          (collections, el) => {
            if (!collections[el.userId]) {
              collections[el.userId] = []
            }
            // collections[el.userId] = collections[el.userId].concat(el.fundsByOutcome || [])
            collections[el.userId] = collections[el.userId].concat(
              { outcome: el.userDefinedCaseStatus, amount: el.totalFundCollected } || []
            )

            return collections
          },
          {}
        )

        const data = response.data?.caseAnalytics.userDefinedAnalytics
          .filter((el) => !this.filterEmployee || el._id === this.filterEmployee)
          .map((el) => {
            const allStatuses = el.statuses.reduce(
              (acc, { status, totalCount }) => {
                if (status) {
                  acc[status] = totalCount
                  acc[this.unAttended] += totalCount
                }
                return acc
              },
              { [this.unAttended]: 0 }
            )

            allStatuses[this.unAttended] = el.globalCount - allStatuses[this.unAttended]

            mergeOptions(this.caseConfig.userDefinedCaseStatus).forEach((status) => {
              if (!allStatuses[status]) {
                allStatuses[status] = 0
              }
            })

            const filteredOptions = mergeOptionsDetails(
              this.caseConfig.userDefinedCaseStatus
            ).filter((el) => el.fields.some((f) => f.name === 'amount' && f.visibility === true))

            const amountCollected = filteredOptions.reduce((acc, option) => {
              const userCollection = userCollections[el._id] || []
              const matchedOutcomes = userCollection.filter(
                (fund) =>
                  fund.outcome &&
                  String(fund.outcome).trim().toLowerCase() ===
                    String(option.code).trim().toLowerCase()
              )

              const totalAmount = matchedOutcomes.reduce((sum, fund) => sum + (fund.amount || 0), 0)

              acc[`${option.code}_amount`] = totalAmount ?? 0
              return acc
            }, {})

            // Construct userStatus
            const userStatus = {
              empName: el.assignedTo,
              total: el.globalCount,
              ...amountCollected,
              ...allStatuses
            }

            if (!userStatus.empName || userStatus.empName.trim() === '') {
              return null
            }

            return userStatus
          })
          .filter((userStatus) => userStatus !== null)

        this.analytics = data
      } catch (error) {
        console.error('Error in fetchCaseAnalytics:', error)
      }
    },

    onDateSelected() {
      if (this.dates.length === 2) {
        this.startDate = this.formatDate(this.dates[0])
        this.endDate = this.formatDate(this.dates[1])
        this.fetchCaseAnalytics()
      } else {
        this.startDate = this.formatDate(this.dates[0])
        this.endDate = null
      }
    },
    formatDate(dateString) {
      return this.$dayjs(dateString).format('YYYY-MM-DD')
    },
    visibleDate(date) {
      return this.$dayjs(date).format('DD-MM-YYYY')
    },
    exportToXLSX() {
      const dataToExport = this.analytics.map((item) => {
        const exportItem = {}
        this.headers.forEach((header) => {
          exportItem[header.text] = item[header.value]
        })
        return exportItem
      })
      const XLSX = require('xlsx')
      const ws = XLSX.utils.json_to_sheet(dataToExport)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'Case Analytics')

      XLSX.utils.sheet_add_aoa(ws, [this.headers.map((header) => header.text)], { origin: 'A1' })

      const currentDate = this.$dayjs().format('DD/MM/YYYY')
      XLSX.writeFile(wb, `${this.caseConfig.name}_Case_Analytics_${currentDate}.xlsx`)
    },
    handleInput() {
      this.fetchCaseAnalytics()
    },
    onDateCleared() {
      this.dates = []
      this.startDate = null
      this.endDate = null
      this.fetchCaseAnalytics()
    }
  },
  watch: {
    '$route.query.empId': {
      async handler(empId) {
        try {
          this.filterEmployee = empId || ''
          this.options.page = 1
          await this.debouncedFetchCaseAnalytics()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },
    caseConfig: {
      handler(config) {
        if (config) {
          const fm = mergeOptions(config.userDefinedCaseStatus)
          const mergedOptionsDetails = mergeOptionsDetails(config.userDefinedCaseStatus)
          this.fetchCaseAnalytics()
          this.headers = [
            {
              text: `${this.orgLanguage.employees || 'Employees'} Name`,
              value: 'empName',
              sortable: false
            }
          ]

          fm.forEach((status) => {
            if (!this.headers.some((header) => header.value === status)) {
              this.headers.push({
                text: status,
                value: status,
                sortable: false
              })
            }
          })
          this.headers.push({
            text: this.unAttended,
            value: this.unAttended,
            sortable: false
          })
          this.headers.push({
            text: 'Grand Total',
            value: 'total',
            sortable: false
          })

          const filteredOptions = mergeOptionsDetails(this.caseConfig.userDefinedCaseStatus).filter(
            (el) => el.fields.some((f) => f.name === 'amount' && f.visibility === true)
          )

          const Columns = filteredOptions.map((option) => ({
            text: `${option.code} Amt.`,
            value: `${option.code}_amount`,
            key: `${option.code}`,
            width: '100px',
            sortable: false
          }))
          this.headers.push(...Columns)
        }
      },
      immediate: true
    }
  },
  mounted() {},
  beforeCreate() {
    this.debouncedFetchCaseAnalytics = () => {}
  },
  created() {
    this.debouncedFetchCaseAnalytics = debounce(this.fetchCaseAnalytics, 800)
  }
}
</script>

<style>
.wfm-search-fields.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 40px !important;
}
</style>
