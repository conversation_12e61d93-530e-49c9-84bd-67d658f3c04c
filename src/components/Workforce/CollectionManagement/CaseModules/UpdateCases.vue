<template>
  <v-dialog v-model="visible" max-width="550px" persistent>
    <v-card>
      <v-card-title>
        <span class="headline">{{ headingTitle }}</span>
      </v-card-title>

      <v-card-text>
        <v-container>
          <v-row v-if="dialogLable === 'FEName'">
            <v-col cols="12">
              <v-autocomplete
                item-text="fullName"
                item-value="_id"
                label="Assigned To"
                hide-details
                flat
                solo
                outlined
                dense
                :items="users"
                v-model="userId"
                class="mt-2 mb-2"
              ></v-autocomplete>
              <div class="r">
                <v-checkbox
                  :color="$vuetify.theme.currentTheme.primary"
                  v-model="updateVisits"
                  label="Do you want to assign associated visit to selected assignee ?"
                ></v-checkbox>
              </div>
            </v-col>
          </v-row>

          <v-row v-if="dialogLable === 'status'">
            <v-col cols="12">
              <v-autocomplete
                label="Outcome"
                :items="userDefinedStatusOptions"
                hide-details
                flat
                solo
                outlined
                dense
                v-model="statusOption"
                class="mt-2 mb-2"
              ></v-autocomplete>
            </v-col>
          </v-row>

          <v-row v-if="dialogLable === 'Teams' || dialogLable === 'Branches'">
            <v-col cols="12">
              <v-autocomplete
                :label="dialogLable === 'Teams' ? 'Teams' : 'Branches'"
                :items="
                  dialogLable === 'Teams'
                    ? teamWithUnassigned
                    : branchWithUnassigned
                "
                :item-text="dialogLable === 'Teams' ? 'teamName' : 'branchName'"
                item-value="_id"
                hide-details
                flat
                solo
                outlined
                dense
                v-model="selectedOption"
                class="mt-2 mb-2"
              ></v-autocomplete>
            </v-col>
          </v-row>
          <v-row v-if="dialogLable === 'SalaryTemplate'">
            <v-col cols="12">
              <v-autocomplete
                label="Salary Template"
                :items="orgSalaryTemplates"
                item-text="template"
                item-value="_id"
                hide-details
                flat
                solo
                outlined
                dense
                v-model="templateId"
                class="mt-2 mb-2"
              ></v-autocomplete>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn outlined text @click="closeDialog">Cancel</v-btn>
        <v-btn
          :color="$vuetify.theme.currentTheme.primary"
          class="white--text"
          v-if="isTeamEnabled"
          @click="updateItem"
          :loading="isLoading"
        >
          Apply
        </v-btn>
        <v-btn
          v-else
          :color="$vuetify.theme.currentTheme.primary"
          class="white--text"
          @click="selectedStatusUpdate(userId)"
          :loading="isLoading"
          :disabled="!userId && !statusOption && !selectedOption && !templateId"
        >
          Apply
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    items: {
      type: Array,
      required: true,
    },
    users: {
      type: Array,
    },
    userDefinedStatusOptions: {
      type: Array,
    },
    teams: { tyepe: Array },
    branches: { tyepe: Array },
    handleShowDialog: {
      type: Function,
      required: true,
    },
    caseConfig: {
      type: Object,
    },
    handleDeleteSelected: {
      type: Function,
    },
    dialogLable: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      localItems: [...this.items],
      userId: null,
      statusOption: "",
      selectedTeam: "",
      selectedBranch: "",
      isLoading: false,
      updateVisits: false,
      selectedOption: "",
      orgSalaryTemplates: [],
      templateId: "",
    };
  },
  watch: {
    items: {
      handler(newItems) {
        this.localItems = [...newItems];
      },
      deep: true,
    },
  },
  async mounted() {
    await this.getOrgSalary();
  },
  computed: {
    isTeamEnabled() {
      return (
        (this.dialogLable === "Teams" || this.dialogLable === "Branches") &&
        !this.items[0]?.caseNo
      );
    },
    headingTitle() {
      if (this.dialogLable === "Teams") {
        return "Assign Team";
      } else if (this.dialogLable === "Branches") {
        return "Assign Branch";
      } else if (this.dialogLable === "SalaryTemplate") {
        return "Assign Salary Template";
      }
      return "Assign Cases";
    },
    teamWithUnassigned() {
      return [
        ...this.teams,
        {
          teamName: "Unassigned",
          _id: null,
        },
      ];
    },
    branchWithUnassigned() {
      return [
        ...this.branches,
        {
          branchName: "Unassigned",
          _id: null,
        },
      ];
    },
  },
  methods: {
    closeDialog() {
      this.userId = null;
      this.statusOption = "";
      this.updateVisits = false;
      this.handleShowDialog(false);
    },
    deleteSelectedArray() {
      this.handleDeleteSelected();
    },

    async caseUpdate(item, field, value, payload) {
      try {
        let id = item._id;
        let response = await this.$axios.put(
          `/workforce/collection/case/${id}`,
          payload
        );
        if (field === "assignedTo" && this.updateVisits) {
          let visitAssignedTo = value;
          const newUser = this.users.find(
            (user) => user._id === item.assignedTo
          );

          let newAssignedTo = newUser?.fullName;
          let visitId = item.tasks[0]?._id;
          await this.updateVisit(visitAssignedTo, visitId, newAssignedTo);
        }
        // const response = await axios.get(url);
        return response.data;
      } catch (error) {
        this.$toast.error(error.response?.data?.message);
        console.error(error);
      }
    },
    async updateTemplateId(item) {
      try {
        const payload = {};
        payload.userInfo = {
          salaryTemplate: this.templateId,
        };
        await this.$axios.patch(
          `/workforce/users/${item._id}?action=UPDATE_USER_INFO`,
          payload
        );
      } catch (error) {
        console.log(error);
      }
    },
    async getOrgSalary() {
      try {
        const response = await this.$axios.get(
          "/workforce/org/salary-templates"
        );
        this.orgSalaryTemplates = response.data.salaryTemplates?.filter(
          (template) => {
            return template.isActive === true;
          }
        );
      } catch (error) {
        console.log(error);
      }
    },
    async selectedStatusUpdate() {
      this.isLoading = true;
      let field;
      let value;
      if (this.dialogLable === "FEName") {
        field = "assignedTo";
        value = this.userId;
      } else if (this.dialogLable === "status") {
        field = "userDefinedCaseStatus";
        value = this.statusOption;
      } else if (this.dialogLable === "Branches") {
        field = "branch";
        value = this.selectedOption;
      }
      const payload = {
        [field]: value,
      };

      const promises = [];

      if (this.dialogLable === "SalaryTemplate") {
        this.items.forEach(async (item) => {
          promises.push(this.updateTemplateId(item));
        });

        try {
          await Promise.all(promises);
          this.userId = null;
          this.$toast.success("Template Id updated successfully");
          this.isLoading = false;
          this.closeDialog();
        } catch (error) {
          this.$toast.error(error.response?.data?.message);
          console.error(error);
        }

        return;
      }

      this.items.forEach(async (item) => {
        promises.push(this.caseUpdate(item, field, value, payload));
      });

      try {
        await Promise.all(promises);
        this.userId = null;
        this.statusOption = "";
        this.$toast.success("Cases updated successfully");
        this.isLoading = false;
        this.deleteSelectedArray();
        this.closeDialog();
      } catch (error) {
        this.$toast.error(error.response?.data?.message);
        console.error(error);
      }
    },

    async updateVisit(visitAssignedTo, visitId) {
      if (!visitAssignedTo) return;
      const payload = {
        assignedTo: visitAssignedTo,
      };
      await this.$axios.put(
        `/workforce/collection/case/task/${visitId}`,
        payload
      );
    },
    async updateItem() {
      this.isLoading = true;
      const payload = {};

      if (this.dialogLable === "Teams") {
        payload.userInfo = {
          team: this.selectedOption,
        };
      } else if (this.dialogLable === "Branches") {
        payload.userInfo = {
          branch: this.selectedOption,
        };
      }

      const promises = [];

      this.items.forEach((item) => {
        const promise = this.updateEmployeeInfo(item, payload);
        promises.push(promise);
      });

      try {
        await Promise.all(promises);
        this.isLoading = false;
        this.selectedOption = "";
        this.closeDialog();
        const successMessage =
          this.dialogLable === "Teams"
            ? "Employees updated with new team successfully"
            : "Employees updated with new branch successfully";
        this.$toast.success(successMessage);
      } catch (error) {
        this.isLoading = false;
        this.$toast.error(
          error.response?.data?.message || "Failed to update employees"
        );
        console.error(error);
      }
    },
    async updateEmployeeInfo(employee, payload) {
      try {
        const response = await this.$axios.patch(
          `/workforce/users/${employee._id}?action=UPDATE_USER_INFO`,
          payload
        );
        return response.data;
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style scoped></style>
