<template>
  <div class="loan-emi-schedule">
    <!-- No EMIs message -->
    <div v-if="!emiSchedule.length" class="text-center py-8">
      <v-icon size="64" color="grey lighten-1">mdi-calendar-remove</v-icon>
      <div class="mt-4 text-gray-600">
        No EMI schedule available for {{ borrowerName || 'this borrower' }}
      </div>
    </div>

    <v-expansion-panels v-else v-model="expandedPanel">
      <v-expansion-panel v-for="(emi, i) in emiSchedule" :key="emi._id" class="mb-3">
        <v-expansion-panel-header>
          <div class="w-full grid grid-cols-9 gap-4 text-center">
            <div>
              <p class="text-sm font-medium text-gray-600">EMI No.</p>
              <p class="font-semibold">{{ i + 1 }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">Due Date</p>
              <p class="font-semibold">{{ formatDate(emi.dueDate) }}</p>
              <div
                v-if="getOverdueInfo(emi).isOverdue || getOverdueInfo(emi).isLatePaid"
                class="mt-1"
              >
                <v-chip
                  small
                  outlined
                  label
                  :color="getOverdueInfo(emi).isLatePaid ? 'warning' : 'error'"
                >
                  <v-icon left x-small>
                    {{ getOverdueInfo(emi).isLatePaid ? 'mdi-clock-alert' : 'mdi-alert-circle' }}
                  </v-icon>
                  {{
                    getOverdueInfo(emi).isLatePaid
                      ? getOverdueInfo(emi).latePaidText
                      : getOverdueInfo(emi).overdueText
                  }}
                </v-chip>
              </div>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">@Interest</p>
              <p class="font-semibold">{{ emi?.atRateOfInterest }}%</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">EMI Amount</p>
              <p class="font-semibold">₹{{ formatCurrency(emi.emiAmount) }}</p>
              <div v-if="getOverdueInfo(emi).lateFeeAmount > 0" class="mt-1">
                <p class="text-xs text-red-600 font-medium">
                  Late Fee: ₹{{ formatCurrency(getOverdueInfo(emi).lateFeeAmount) }}
                </p>
              </div>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">Principal</p>
              <p class="font-semibold">₹{{ formatCurrency(emi.principalAmount) }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">Interest</p>
              <p class="font-semibold">₹{{ formatCurrency(emi.interestAmount) }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">Principal Outstanding</p>
              <p class="font-semibold">₹{{ formatCurrency(emi.outstandingAmount) }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">PDD Status</p>
              <v-chip small outlined label :color="getPDDStatus(emi.activities).color">
                {{ getPDDStatus(emi.activities).status }}
              </v-chip>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-600">EMI Status</p>
              <v-chip
                small
                label
                :color="getStatusColor(emi.emiStatus).bgColor"
                :text-color="getStatusColor(emi.emiStatus).textColor"
              >
                {{ emi.emiStatus }}
              </v-chip>
              <div v-if="emi.emiStatus === 'Minimum Amount Paid'" class="mt-1">
                <p class="text-xs text-orange-600 font-medium">
                  Min: ₹{{ formatCurrency(calculateMinimumAmountForEmi(emi)) }}
                </p>
              </div>
            </div>
          </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <div class="activities-container mt-4">
            <div class="flex justify-between items-center mb-3">
              <h4 class="text-md font-semibold">
                {{ emi.activities && emi.activities.length ? 'Required Documents' : '' }}
              </h4>
              <v-btn small color="primary" outlined @click="openEditEmiDialog(emi._id)">
                <v-icon left small>mdi-pencil</v-icon>
                Edit EMI Details
              </v-btn>
            </div>

            <div
              v-for="activity in emi.activities"
              :key="activity._id"
              class="activity-item mb-3 border rounded-md bg-gray-50 dark-bg-custom"
            >
              <div
                class="flex justify-between items-center pl-2 pr-2"
                v-if="emi.activities && emi.activities.length"
              >
                <div class="flex items-center justify-between w-full">
                  <p class="font-medium pt-3">{{ activity.activityType }}</p>
                  <div class="flex items-center space-x-2 justify-end">
                    <p class="mb-0 text-xs text-gray-600">
                      Last Updated: {{ formatDateTime(activity.updatedAt) }}
                    </p>
                    <v-chip
                      small
                      label
                      outlined
                      :color="getActivityStatusColor(activity.status).bgColor"
                      :text-color="getActivityStatusColor(activity.status).textColor"
                    >
                      {{ activity.status }}
                    </v-chip>
                  </div>
                </div>
              </div>

              <div v-if="activity.activityDoc" class="mt-3">
                <div class="flex items-center">
                  <v-icon size="20" color="primary" class="mr-2">mdi-file-document-outline</v-icon>
                  <a
                    :href="activity.activityDoc"
                    target="_blank"
                    class="text-blue-600 hover:underline"
                  >
                    View Document
                  </a>
                </div>
              </div>
            </div>
          </div>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Edit EMI Dialog -->
    <AddEditEmi
      v-model="editEmiDialog"
      :emi-data="selectedEmi"
      @save-emi="handleEmiUpdate"
      @show-notification="$emit('show-notification', $event)"
    />
  </div>
</template>

<script>
import { calculateOverdueDays, getEmiStatusColor } from '@/utils/common'
import AddEditEmi from './VisitModules/AddEditEmi.vue'

export default {
  name: 'LoanEmiScheduleView',
  components: {
    AddEditEmi
  },
  props: {
    emiSchedule: {
      type: Array,
      required: true
    },
    borrowerId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      expandedPanel: null,
      editEmiDialog: false,
      selectedEmi: {}
    }
  },
  methods: {
    formatDate(dateString) {
      return this.$dayjs(dateString).format('DD MMM YYYY')
    },

    formatDateTime(dateString) {
      return this.$dayjs(dateString).format('DD MMM YYYY, hh:mm A')
    },

    formatCurrency(value) {
      return new Intl.NumberFormat('en-IN').format(value)
    },
    getPDDStatus(activities) {
      if (activities?.length) {
        const isAllStepsCompleted = activities.every((activity) => activity.status === 'Completed')
        return isAllStepsCompleted
          ? { status: 'Completed', color: 'green' }
          : { status: 'Not Completed', color: 'red' }
      }
      return { status: 'No PDD Steps', color: 'grey' }
    },

    getStatusColor(status) {
      return getEmiStatusColor(status)
    },

    getOverdueInfo(emi) {
      return calculateOverdueDays(
        emi.dueDate,
        emi.emiStatus,
        emi.paymentDate,
        emi.emiAmount,
        emi.lateFeeType,
        emi.lateFeeValue
      )
    },

    getActivityStatusColor(status) {
      switch (status) {
        case 'Completed':
          return { bgColor: 'green lighten-5', textColor: 'green darken-2' }
        case 'Not Completed':
          return { bgColor: 'orange lighten-5', textColor: 'orange darken-2' }
        case 'In Progress':
          return { bgColor: 'blue lighten-5', textColor: 'blue darken-2' }
        default:
          return { bgColor: 'grey lighten-3', textColor: 'grey darken-2' }
      }
    },

    openEditEmiDialog(emiId) {
      const emi = this.emiSchedule.find((e) => e._id === emiId)
      if (!emi) return

      this.selectedEmi = emi
      this.editEmiDialog = true
    },

    handleEmiUpdate(payload) {
      // Emit event to parent component to handle the update
      this.$emit('update-emi', payload)
    },

    calculateMinimumAmountForEmi(emi) {
      const interestAmount = parseFloat(emi.interestAmount) || 0
      const overdueInfo = this.getOverdueInfo(emi)
      const lateFeeAmount = overdueInfo.lateFeeAmount || 0
      return interestAmount + lateFeeAmount
    }
  }
}
</script>

<style scoped>
.loan-emi-schedule {
  border-radius: 5px;
  padding: 20px;
}

.activity-item {
  transition: all 0.2s ease;
}

.activity-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Dark mode support */
.dark-mode .loan-emi-schedule {
  background-color: #1e1e1e;
  color: #e0e0e0;
}
</style>
