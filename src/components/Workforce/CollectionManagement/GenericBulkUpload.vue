<template>
  <v-card class="generic-bulk-upload">
    <!-- Header Section -->
    <v-card-title class="d-flex align-center justify-space-between pa-6">
      <div class="d-flex align-center">
        <v-icon large color="primary" class="mr-3">mdi-upload-multiple</v-icon>
        <div>
          <h2 class="text-h5 font-weight-bold mb-1">{{ title }}</h2>
          <p class="text-subtitle-2 text--secondary mb-0">{{ subtitle }}</p>
        </div>
      </div>
      <v-btn icon @click="closeDialog" :disabled="uploading">
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </v-card-title>

    <v-divider></v-divider>

    <v-card-text class="pa-6">
      <!-- Step Indicator -->
      <v-stepper v-model="currentStep" class="elevation-0 mb-6">
        <v-stepper-header>
          <v-stepper-step :complete="currentStep > 1" step="1" color="primary">
            Select File
          </v-stepper-step>
          <v-divider></v-divider>
          <v-stepper-step :complete="currentStep > 2" step="2" color="primary">
            Validate
          </v-stepper-step>
          <v-divider></v-divider>
          <v-stepper-step :complete="uploadComplete" step="3" color="primary">
            Upload
          </v-stepper-step>
        </v-stepper-header>
      </v-stepper>

      <!-- File Selection Section -->
      <div v-if="currentStep === 1" class="file-selection-section">
        <div class="text-center mb-6">
          <v-icon size="80" color="primary" class="mb-4">mdi-file-excel-outline</v-icon>
          <h3 class="text-h6 mb-2">Choose Your Excel File</h3>
          <p class="text-body-2 text--secondary">
            Select an Excel file (.xlsx, .xls) containing {{ entityName }} data
          </p>
        </div>

        <v-file-input
          v-model="selectedFile"
          label="Select Excel File"
          placeholder="Choose your Excel file"
          accept=".xlsx,.xls"
          prepend-icon="mdi-file-excel"
          outlined
          dense
          show-size
          :loading="uploading"
          :disabled="uploading"
          :error-messages="fileErrors"
          @change="handleFileSelect"
          class="mb-4"
        ></v-file-input>

        <!-- File Requirements -->
        <v-card outlined class="mb-4">
          <v-card-subtitle class="pb-2">
            <v-icon small class="mr-2">mdi-information</v-icon>
            File Requirements
          </v-card-subtitle>
          <v-card-text class="pt-0">
            <ul class="text-body-2">
              <li>Supported formats: .xlsx, .xls</li>
              <li>Maximum file size: 10MB</li>
              <li>First row should contain column headers</li>
              <li v-if="requiredColumns.length">
                <strong>Required columns:</strong> {{ requiredColumns.join(', ') }}
              </li>
              <li>Sample file includes all configured field mappings</li>
            </ul>
          </v-card-text>
        </v-card>

        <!-- Sample Download -->
        <div class="d-flex justify-space-between align-center">
          <v-btn
            outlined
            color="primary"
            @click="downloadSample"
            :disabled="uploading"
            v-if="sampleData && sampleData.length"
          >
            <v-icon left>mdi-download</v-icon>
            Download Sample File
          </v-btn>

          <div v-else></div>

          <v-btn color="primary" :disabled="!selectedFile || uploading" @click="nextStep">
            Next: Validate File
            <v-icon right>mdi-arrow-right</v-icon>
          </v-btn>
        </div>
      </div>

      <!-- Validation Section -->
      <div v-if="currentStep === 2" class="validation-section">
        <div class="text-center mb-4">
          <v-icon size="60" :color="validationPassed ? 'success' : 'warning'" class="mb-3">
            {{ validationPassed ? 'mdi-check-circle' : 'mdi-alert-circle' }}
          </v-icon>
          <h3 class="text-h6 mb-2">
            {{ validationPassed ? 'Validation Passed!' : 'Validating File...' }}
          </h3>
        </div>

        <!-- Validation Progress -->
        <v-progress-linear
          v-if="validating"
          indeterminate
          color="primary"
          class="mb-4"
        ></v-progress-linear>

        <!-- Validation Results -->
        <div v-if="!validating">
          <!-- Success Alert -->
          <v-alert v-if="validationPassed" type="success" outlined class="mb-4">
            <div class="d-flex align-center">
              <v-icon class="mr-2">mdi-check-circle</v-icon>
              <div>
                <strong>File validation successful!</strong>
                <p class="mb-0 mt-1">
                  Required columns ({{ requiredColumns.join(', ') }}) are present.
                </p>
              </div>
            </div>
          </v-alert>

          <!-- Matched Headers -->
          <v-card v-if="matchedHeaders.length > 0" outlined class="mb-4">
            <v-card-subtitle class="d-flex align-center">
              <v-icon small color="success" class="mr-2">mdi-check</v-icon>
              Matched Columns ({{ matchedHeaders.length }})
            </v-card-subtitle>
            <v-card-text class="pt-0">
              <v-chip-group column>
                <v-chip
                  v-for="header in matchedHeaders"
                  :key="header"
                  small
                  color="success"
                  text-color="white"
                >
                  <v-icon left small>mdi-check</v-icon>
                  {{ header }}
                </v-chip>
              </v-chip-group>
            </v-card-text>
          </v-card>

          <!-- Validation Errors -->
          <v-card v-if="validationErrors.length > 0" outlined class="mb-4">
            <v-card-subtitle class="d-flex align-center">
              <v-icon small color="error" class="mr-2">mdi-alert</v-icon>
              Missing or Invalid Columns ({{ validationErrors.length }})
            </v-card-subtitle>
            <v-card-text class="pt-0">
              <v-list dense>
                <v-list-item v-for="(error, index) in validationErrors" :key="index">
                  <v-list-item-icon>
                    <v-icon small color="error">mdi-close</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title class="text-body-2">{{ error }}</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </div>

        <!-- Navigation Buttons -->
        <div class="d-flex justify-space-between">
          <v-btn outlined @click="previousStep" :disabled="validating">
            <v-icon left>mdi-arrow-left</v-icon>
            Back
          </v-btn>

          <div>
            <v-btn
              v-if="!validationPassed"
              color="warning"
              @click="validateFile"
              :loading="validating"
              :disabled="validating"
              class="mr-2"
            >
              <v-icon left>mdi-refresh</v-icon>
              Re-validate
            </v-btn>

            <v-btn color="primary" :disabled="!validationPassed || validating" @click="nextStep">
              Next: Upload File
              <v-icon right>mdi-arrow-right</v-icon>
            </v-btn>
          </div>
        </div>
      </div>

      <!-- Upload Section -->
      <div v-if="currentStep === 3" class="upload-section">
        <div class="text-center mb-4">
          <v-icon
            size="60"
            :color="uploadComplete ? 'success' : uploading ? 'primary' : 'grey'"
            class="mb-3"
          >
            {{
              uploadComplete ? 'mdi-check-circle' : uploading ? 'mdi-upload' : 'mdi-cloud-upload'
            }}
          </v-icon>
          <h3 class="text-h6 mb-2">
            {{
              uploadComplete ? 'Upload Complete!' : uploading ? 'Uploading...' : 'Ready to Upload'
            }}
          </h3>
        </div>

        <!-- Upload Progress -->
        <v-progress-linear
          v-if="uploading"
          :value="uploadProgress"
          color="primary"
          height="8"
          class="mb-4"
        >
          <template v-slot:default="{ value }">
            <strong>{{ Math.ceil(value) }}%</strong>
          </template>
        </v-progress-linear>

        <!-- Upload Results -->
        <div v-if="uploadComplete">
          <!-- Success Summary -->
          <v-card outlined class="mb-4" v-if="uploadResults.success > 0">
            <v-card-text class="d-flex align-center">
              <v-icon large color="success" class="mr-4">mdi-check-circle</v-icon>
              <div>
                <h4 class="text-h6 success--text">
                  {{ uploadResults.success }} {{ entityName }}s Updated Successfully
                </h4>
                <p class="mb-0 text-body-2">
                  These {{ entityName }}s have been updated in your system.
                </p>
              </div>
            </v-card-text>
          </v-card>

          <!-- Failed Summary -->
          <v-card outlined class="mb-4" v-if="uploadResults.failed > 0">
            <v-card-text class="d-flex align-center justify-space-between">
              <div class="d-flex align-center">
                <v-icon large color="error" class="mr-4">mdi-alert-circle</v-icon>
                <div>
                  <h4 class="text-h6 error--text">
                    {{ uploadResults.failed }} {{ entityName }}s Failed
                  </h4>
                  <p class="mb-0 text-body-2">
                    These {{ entityName }}s could not be processed due to errors.
                  </p>
                </div>
              </div>
              <v-btn
                color="error"
                outlined
                @click="downloadFailedItems"
                :loading="downloadingFailed"
              >
                <v-icon left>mdi-download</v-icon>
                Download Failed
              </v-btn>
            </v-card-text>
          </v-card>

          <!-- Invalid Items Details -->
          <v-expansion-panels v-if="invalidItems.length > 0" class="mb-4">
            <v-expansion-panel>
              <v-expansion-panel-header>
                <div class="d-flex align-center">
                  <v-icon color="error" class="mr-2">mdi-alert-circle</v-icon>
                  <span class="font-weight-medium">
                    View Failed {{ entityName }}s Details ({{ invalidItems.length }})
                  </span>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div class="failed-items-list" style="max-height: 300px; overflow-y: auto">
                  <v-list dense>
                    <v-list-item v-for="(item, index) in invalidItems" :key="index">
                      <v-list-item-icon>
                        <v-icon small color="error">mdi-close-circle</v-icon>
                      </v-list-item-icon>
                      <v-list-item-content>
                        <v-list-item-title class="text-body-2">
                          <strong>Row {{ index + 2 }}:</strong> {{ getItemIdentifier(item) }}
                        </v-list-item-title>
                        <v-list-item-subtitle class="error--text">
                          {{ item.failedReason }}
                        </v-list-item-subtitle>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </div>

        <!-- Navigation Buttons -->
        <div class="d-flex justify-space-between">
          <v-btn outlined @click="previousStep" :disabled="uploading" v-if="!uploadComplete">
            <v-icon left>mdi-arrow-left</v-icon>
            Back
          </v-btn>

          <div class="ml-auto">
            <v-btn
              v-if="!uploadComplete"
              color="primary"
              @click="uploadFile"
              :loading="uploading"
              :disabled="uploading"
            >
              <v-icon left>mdi-upload</v-icon>
              Start Upload
            </v-btn>

            <v-btn v-if="uploadComplete" color="success" @click="closeDialog">
              <v-icon left>mdi-check</v-icon>
              Done
            </v-btn>
          </div>
        </div>
      </div>

      <!-- Error Messages -->
      <v-alert
        v-if="errorMessage"
        type="error"
        dismissible
        class="mt-4"
        @input="errorMessage = null"
      >
        {{ errorMessage }}
      </v-alert>

      <!-- Success Messages -->
      <v-alert
        v-if="successMessage"
        type="success"
        dismissible
        class="mt-4"
        @input="successMessage = null"
      >
        {{ successMessage }}
      </v-alert>
    </v-card-text>
  </v-card>
</template>

<script>
import { extractExcelHeaders } from '@/utils/common'

export default {
  name: 'GenericBulkUpload',
  props: {
    title: {
      type: String,
      default: 'Bulk Upload'
    },
    subtitle: {
      type: String,
      default: 'Upload multiple items using Excel file'
    },
    entityName: {
      type: String,
      default: 'Item'
    },
    requiredColumns: {
      type: Array,
      default: () => []
    },
    uploadEndpoint: {
      type: String,
      required: true
    },
    method: {
      type: String,
      require: true
    },
    sampleData: {
      type: Array,
      default: () => []
    },
    additionalPayload: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      currentStep: 1,
      selectedFile: null,
      uploading: false,
      validating: false,
      uploadComplete: false,
      uploadProgress: 0,

      // Validation
      validationPassed: false,
      validationErrors: [],
      matchedHeaders: [],
      fileErrors: [],

      // Upload Results
      uploadResults: {
        success: 0,
        failed: 0
      },
      invalidItems: [],

      // Messages
      errorMessage: null,
      successMessage: null,

      // Download states
      downloadingFailed: false,
      downloadingSample: false
    }
  },
  methods: {
    handleFileSelect(file) {
      this.clearMessages()
      this.fileErrors = []

      if (file) {
        // Validate file type
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel'
        ]

        if (!allowedTypes.includes(file.type)) {
          this.fileErrors = ['Please select a valid Excel file (.xlsx or .xls)']
          this.selectedFile = null
          return
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
          this.fileErrors = ['File size should be less than 10MB']
          this.selectedFile = null
          return
        }

        // Reset validation state
        this.resetValidation()
      }
    },

    async validateFile() {
      if (!this.selectedFile) return

      this.validating = true
      this.validationErrors = []
      this.matchedHeaders = []

      try {
        // Extract headers from Excel file
        const headers = await extractExcelHeaders([this.selectedFile])

        // Check for missing required fields
        const missingFields = []
        const matchedFields = []

        this.requiredColumns.forEach((field) => {
          const found = headers.find(
            (header) =>
              header.toLowerCase().includes(field.toLowerCase()) ||
              field.toLowerCase().includes(header.toLowerCase())
          )

          if (found) {
            matchedFields.push(found)
          } else {
            missingFields.push(`Missing required column: ${field}`)
          }
        })

        this.matchedHeaders = matchedFields
        this.validationErrors = missingFields
        this.validationPassed = missingFields.length === 0
      } catch (error) {
        console.error('Validation error:', error)
        this.validationErrors = ['Error processing the file. Please check the file format.']
        this.validationPassed = false
      } finally {
        this.validating = false
      }
    },

    async uploadFile() {
      if (!this.selectedFile) return

      this.uploading = true
      this.uploadProgress = 0

      try {
        const formData = new FormData()
        formData.append('attachments', this.selectedFile)

        // Add additional payload
        Object.keys(this.additionalPayload).forEach((key) => {
          formData.append(key, this.additionalPayload[key])
        })

        const config = {
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
          }
        }

        const response = await this.$axios[this.method](this.uploadEndpoint, formData, config)

        // Process response - adjust based on your API response structure
        this.uploadResults = {
          success: response.data?.data?.successCount || response.data?.success || 0,
          failed: response.data?.data?.failedCount || response.data?.failed || 0
        }

        this.invalidItems = response.data?.data?.invalidItems || response.data?.invalidItems || []

        if (this.uploadResults.success > 0) {
          this.successMessage = `${this.uploadResults.success} ${this.entityName}s uploaded successfully!`
          this.$emit('uploadComplete', response.data)
        }

        this.uploadComplete = true
      } catch (error) {
        console.error('Upload error:', error)
        this.errorMessage =
          error.response?.data?.message || `Failed to upload ${this.entityName}s. Please try again.`
      } finally {
        this.uploading = false
      }
    },

    async downloadFailedItems() {
      if (this.invalidItems.length === 0) return

      this.downloadingFailed = true

      try {
        const XLSX = require('xlsx')

        // Prepare failed items data with reasons
        const failedData = this.invalidItems.map((item, index) => ({
          'Row Number': index + 2,
          'Failed Reason': item.failedReason,
          ...item
        }))

        // Create workbook and worksheet
        const worksheet = XLSX.utils.json_to_sheet(failedData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, `Failed ${this.entityName}s`)

        // Download file
        const fileName = `Failed_${this.entityName}s_${new Date().toISOString().split('T')[0]}.xlsx`
        XLSX.writeFile(workbook, fileName)
      } catch (error) {
        console.error('Download error:', error)
        this.errorMessage = `Failed to download failed ${this.entityName}s data.`
      } finally {
        this.downloadingFailed = false
      }
    },

    async downloadSample() {
      this.downloadingSample = true

      try {
        const XLSX = require('xlsx')

        const worksheet = XLSX.utils.json_to_sheet(this.sampleData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, `${this.entityName} Sample`)

        XLSX.writeFile(workbook, `${this.entityName}_Upload_Sample.xlsx`)
      } catch (error) {
        console.error('Sample download error:', error)
        this.errorMessage = 'Failed to download sample file.'
      } finally {
        this.downloadingSample = false
      }
    },

    getItemIdentifier(item) {
      // Try to get a meaningful identifier for the item
      return (
        item['Application Number'] ||
        item['Customer Name'] ||
        item['EMI Amount'] ||
        item['Due Date'] ||
        `Unknown ${this.entityName}`
      )
    },

    nextStep() {
      if (this.currentStep < 3) {
        this.currentStep++

        // Auto-validate when moving to step 2
        if (this.currentStep === 2) {
          this.validateFile()
        }
      }
    },

    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    resetValidation() {
      this.validationPassed = false
      this.validationErrors = []
      this.matchedHeaders = []
    },

    clearMessages() {
      this.errorMessage = null
      this.successMessage = null
    },

    closeDialog() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.generic-bulk-upload {
  max-width: 1000px;
  margin: 0 auto;
}

.file-selection-section,
.validation-section,
.upload-section {
  min-height: 400px;
}

.v-stepper {
  box-shadow: none !important;
  background: transparent !important;
}

.failed-items-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.v-chip-group {
  margin: -4px;
}

.v-chip {
  margin: 4px;
}

.v-progress-linear {
  border-radius: 4px;
}

.v-expansion-panel-content >>> .v-expansion-panel-content__wrap {
  padding: 16px;
}
</style>
