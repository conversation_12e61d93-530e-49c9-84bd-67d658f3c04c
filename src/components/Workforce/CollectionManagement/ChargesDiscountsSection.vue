<template>
  <div class="w-full border rounded" :class="sectionClasses.border">
    <div class="flex justify-between items-center p-3 mb-2">
      <h6 class="text-sm font-medium m-0 flex items-center" :class="sectionClasses.text">
        <v-icon small class="mr-1" :color="sectionClasses.iconColor">{{ sectionIcon }}</v-icon>
        {{ sectionTitle }}
      </h6>
      <v-btn
        x-small
        :color="sectionClasses.buttonColor"
        outlined
        @click="addItem"
        class="normal-case font-medium text-xs"
      >
        <v-icon x-small class="mr-1">mdi-plus</v-icon>
        Add
      </v-btn>
    </div>

    <div class="border rounded p-2 mt-2" :class="sectionClasses.innerBorder">
      <div
        v-for="(item, index) in localItems"
        :key="`${type}-item-${index}`"
        class="border-b pb-1 mb-1 last:border-b-0 last:pb-0 last:mb-0"
        :class="sectionClasses.itemBorder"
      >
        <div class="grid grid-cols-[3fr_2fr_auto] gap-2 items-center">
          <div class="w-full">
            <v-text-field
              v-model="item[itemTypeKey]"
              :label="itemTypeLabel"
              outlined
              dense
              hide-details
              :prepend-inner-icon="itemIcon"
              :placeholder="itemPlaceholder"
              class="text-xs"
              @input="emitChanges"
            ></v-text-field>
          </div>
          <div class="">
            <v-text-field
              v-model.number="item.amount"
              label="Amount"
              type="number"
              outlined
              dense
              hide-details
              prefix="₹"
              :min="0"
              placeholder="0"
              @input="emitChanges"
              class="text-xs"
            ></v-text-field>
          </div>
          <div class="flex items-center justify-end min-h-[32px] relative w-[40px]">
            <v-btn
              v-if="localItems.length > 1"
              icon
              x-small
              color="error"
              @click="removeItem(index)"
              class="absolute top-0 right-0 z-10"
              style="transform: translate(50%, -50%)"
            >
              <v-icon small>mdi-close</v-icon>
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <div
      class="border-t pt-2 mt-2"
      :class="sectionClasses.footerBorder"
      v-if="localItems.length > 0"
    >
      <div class="flex justify-end pr-2">
        <span class="font-medium text-sm" :class="sectionClasses.text">{{ totalLabel }}:</span>
        <span class="font-medium ml-2" :class="sectionClasses.text"
          >₹{{ formatCurrency(totalAmount) }}</span
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChargesDiscountsSection',
  props: {
    type: {
      type: String,
      required: true,
      validator: (value) => ['penalties', 'discounts'].includes(value)
    },
    items: {
      type: Array,
      default: () => []
    },
    formatCurrency: {
      type: Function,
      default: (amount) => `${amount?.toLocaleString() || '0'}`
    }
  },
  data() {
    return {
      localItems: [this.getDefaultItem()]
    }
  },
  computed: {
    isPenalties() {
      return this.type === 'penalties'
    },

    sectionClasses() {
      if (this.isPenalties) {
        return {
          border: 'border-red-200 bg-red-50',
          text: 'text-red-900',
          iconColor: 'red',
          buttonColor: 'error',
          innerBorder: 'border-red-200 bg-red-50',
          itemBorder: 'border-red-200',
          footerBorder: 'border-red-200'
        }
      } else {
        return {
          border: 'border-green-200 bg-green-50',
          text: 'text-green-900',
          iconColor: 'green',
          buttonColor: 'success',
          innerBorder: 'border-green-200 bg-green-50',
          itemBorder: 'border-green-200',
          footerBorder: 'border-green-200'
        }
      }
    },

    sectionTitle() {
      return this.isPenalties ? 'Penalties & Charges' : 'Discounts & Waivers'
    },

    sectionIcon() {
      return this.isPenalties ? 'mdi-alert-circle' : 'mdi-tag-minus'
    },

    itemTypeKey() {
      return this.isPenalties ? 'chargeType' : 'discountType'
    },

    itemTypeLabel() {
      return this.isPenalties ? 'Charge Type' : 'Discount Type'
    },

    itemIcon() {
      return this.isPenalties ? 'mdi-alert-outline' : 'mdi-tag-minus-outline'
    },

    itemPlaceholder() {
      return this.isPenalties
        ? 'e.g., Late Fee, Processing Fee'
        : 'e.g., Penalty Discount, Interest Waiver'
    },

    totalLabel() {
      return this.isPenalties ? 'Total Penalties & Charges' : 'Total Discounts & Waivers'
    },

    totalAmount() {
      return this.localItems.reduce((sum, item) => {
        return sum + (parseFloat(item.amount) || 0)
      }, 0)
    }
  },
  watch: {
    items: {
      handler(newItems) {
        if (newItems && newItems.length > 0) {
          this.localItems = [...newItems]
        } else {
          this.localItems = [this.getDefaultItem()]
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getDefaultItem() {
      return this.isPenalties ? { chargeType: '', amount: 0 } : { discountType: '', amount: 0 }
    },

    addItem() {
      this.localItems.push(this.getDefaultItem())
      this.emitChanges()
    },

    removeItem(index) {
      this.localItems.splice(index, 1)
      this.emitChanges()
    },

    emitChanges() {
      const eventName = this.isPenalties ? 'charges-changed' : 'discounts-changed'
      const updateProp = this.isPenalties ? 'update:charges' : 'update:discounts'

      this.$emit(updateProp, this.localItems)
      this.$emit(eventName, this.localItems)
    }
  }
}
</script>
