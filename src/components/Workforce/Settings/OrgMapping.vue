<template>
  <div class="bg-gray-100 dark-bg-custom p-6 pt-0">
    <!-- Main Tabs -->
    <div class="sticky top-0 z-50 pt-4 bg-[#f3f4f6] dark-bg-custom">
      <div class="flex justify-between">
        <p class="text-md" v-if="isUploadCase">
          VERIFY MAPPING - <b>({{ editSubCase?.name }})</b>
        </p>
        <p class="text-md font-semibold" v-else>
          CREATE MAPPING - <b>({{ titleCase(useCase) }})</b>
        </p>
        <v-btn plain @click="onCancel">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </div>
      <v-alert color="blue" type="info" outlined class="text-sm font-medium">
        Here, we are considering the {{ titleCase(useCase) || 'Collection' }} use case. By default,
        naming conventions follow industry standards. However, they can be overridden and customized
        based on your company's requirements.
      </v-alert>
      <v-tabs v-model="mainTab">
        <v-tab>Core Settings</v-tab>
        <v-tab>Advanced Settings</v-tab>
      </v-tabs>
    </div>
    <v-tabs-items v-model="mainTab">
      <!-- Core Settings Tab -->
      <v-tab-item>
        <v-form ref="form" v-model="valid" lazy-validation>
          <v-expansion-panels v-model="fieldPanel" flat>
            <!-- Field Mapping Panel -->

            <v-card class="w-full mt-4" outlined>
              <v-card-title class="pb-2 text-base">
                <v-icon class="mr-2 text-base" color="primary">mdi-database-cog-outline</v-icon>
                Data Mapping
              </v-card-title>
              <v-card-text>
                <!-- Replace the DataMapping component with conditional rendering -->
                <template v-if="showMappingDetails">
                  <v-tabs v-model="dataMappingTab" class="text-base" grow>
                    <v-tab>Sub-case Fields</v-tab>
                    <v-tab>Customer Fields</v-tab>
                  </v-tabs>

                  <v-tabs-items v-model="dataMappingTab">
                    <v-tab-item>
                      <v-text-field
                        v-model="caseType.value"
                        label="Subcase Name"
                        required
                        :rules="caseType.rules"
                        :disabled="!!isUploadCase"
                        class="mt-4"
                      ></v-text-field>
                      <v-data-table
                        :headers="[
                          { text: 'Field Name', value: 'text', width: '30%' },
                          { text: 'Mapping Name', value: 'value', width: '40%' },
                          { text: 'Visibility', value: 'settings', width: '15%' },
                          { text: 'Required', value: 'settings', width: '15%' }
                        ]"
                        :items="orgKeys"
                        hide-default-footer
                        dense
                        class="mt-4"
                      >
                        <template v-slot:item="{ item }">
                          <tr>
                            <td class="px-4 font-semibold">{{ item.text }}</td>
                            <td>
                              <div class="d-flex align-center">
                                <v-select
                                  v-if="item.key === 'pos' || item.key === 'emiAmount'"
                                  v-model="item.selectedPrefix"
                                  :items="['₹', '$', '€', '£', '¥', '₽']"
                                  dense
                                  class="mr-2"
                                  style="max-width: 80px"
                                  hide-details
                                  outlined
                                ></v-select>
                                <v-text-field
                                  v-model="item.value"
                                  dense
                                  :disabled="!!isUploadCase"
                                  hide-details
                                  outlined
                                ></v-text-field>
                                <v-select
                                  v-if="item.key === 'tenure'"
                                  v-model="item.selectedSuffix"
                                  :items="['Days', 'Months', 'Years']"
                                  dense
                                  outlined
                                  class="ml-2"
                                  style="max-width: 120px"
                                  hide-details
                                ></v-select>
                              </div>
                            </td>
                            <td>
                              <div class="ml-4">
                                <v-checkbox
                                  v-model="item.visible"
                                  dense
                                  hide-details
                                  class="mr-2"
                                ></v-checkbox>
                              </div>
                            </td>
                            <td>
                              <div>
                                <v-checkbox v-model="item.required" dense hide-details></v-checkbox>
                              </div>
                            </td>
                          </tr>
                        </template>
                      </v-data-table>

                      <v-divider class="my-4"></v-divider>

                      <v-expansion-panel flat>
                        <v-expansion-panel-header class="font-semibold"
                          >Custom Fields</v-expansion-panel-header
                        >
                        <v-expansion-panel-content>
                          <v-data-table
                            :headers="[
                              { text: 'Field Name', value: 'text', width: '50%' },
                              { text: 'Type', value: 'type', width: '20%' },
                              { text: 'Actions', value: 'actions', sortable: false, width: '10%' }
                            ]"
                            :items="extraColumns"
                            hide-default-footer
                            dense
                          >
                            <template v-slot:item="{ item, index }">
                              <tr>
                                <td class="px-2 py-1">
                                  <v-text-field
                                    v-model="item.text"
                                    dense
                                    hide-details
                                    outlined
                                  ></v-text-field>
                                </td>
                                <td>
                                  <v-select
                                    v-model="item.value"
                                    :items="getFilteredItems(index)"
                                    dense
                                    hide-details
                                    outlined
                                  ></v-select>
                                </td>
                                <td class="px-4">
                                  <v-btn icon small color="error" @click="removeExtraColumn(index)">
                                    <v-icon small>mdi-delete</v-icon>
                                  </v-btn>
                                </td>
                              </tr>
                            </template>
                          </v-data-table>

                          <div class="flex justify-end">
                            <v-btn
                              color="primary"
                              text
                              small
                              outlined
                              class="mt-2"
                              @click="addExtraColumn"
                              v-if="!isUploadCase"
                            >
                              <v-icon left small>mdi-plus</v-icon>
                              Add
                            </v-btn>
                          </div>
                        </v-expansion-panel-content>
                      </v-expansion-panel>
                    </v-tab-item>

                    <v-tab-item>
                      <v-card flat>
                        <v-card-text>
                          <div class="d-flex flex-wrap">
                            <v-text-field
                              v-model="staticFields.crn"
                              label="CRN No"
                              required
                              :rules="staticFields.crnRules"
                              :disabled="!!isUploadCase"
                              class="mr-4"
                              outlined
                              dense
                              style="width: 200px"
                            ></v-text-field>
                            <v-text-field
                              v-model="staticFields.customerFullName"
                              label="Customer Name"
                              required
                              :rules="staticFields.customerFullNameRules"
                              :disabled="!!isUploadCase"
                              class="mr-4"
                              outlined
                              dense
                              style="width: 200px"
                            ></v-text-field>
                            <v-text-field
                              v-model="staticFields.customerMobile"
                              label="Mobile"
                              :disabled="!!isUploadCase"
                              class="mr-4"
                              style="width: 200px"
                              outlined
                              dense
                            ></v-text-field>
                            <v-text-field
                              v-model="staticFields.customerEmail"
                              label="Email"
                              :disabled="!!isUploadCase"
                              style="width: 200px"
                              outlined
                              dense
                            ></v-text-field>
                          </div>

                          <!-- Address section -->
                          <v-expansion-panels class="mt-4">
                            <v-expansion-panel v-for="(address, index) in addresses" :key="index">
                              <v-expansion-panel-header>
                                <div class="d-flex align-center">
                                  <span>Address {{ index + 1 }}</span>
                                  <v-chip
                                    x-small
                                    label
                                    class="ml-2"
                                    :color="address.default ? 'primary' : 'grey'"
                                  >
                                    {{ address.typeOfAddress || 'Select Type' }}
                                  </v-chip>
                                </div>
                              </v-expansion-panel-header>
                              <v-expansion-panel-content>
                                <div class="d-flex justify-between align-center mb-4">
                                  <v-select
                                    v-model="address.typeOfAddress"
                                    :items="getFilteredAddressTypes(index)"
                                    label="Type"
                                    :disabled="!!isUploadCase"
                                    class="mr-4"
                                    style="max-width: 300px"
                                    outlined
                                    dense
                                    hide-details
                                  ></v-select>
                                  <v-checkbox
                                    v-model="address.default"
                                    label="Set as default"
                                    @change="setDefault(index)"
                                    class="mr-4"
                                  ></v-checkbox>
                                  <v-btn
                                    icon
                                    small
                                    color="error"
                                    @click="removeAddress(index)"
                                    v-show="addresses.length > 1"
                                  >
                                    <v-icon small>mdi-delete</v-icon>
                                  </v-btn>
                                </div>

                                <div class="d-flex flex-wrap">
                                  <v-text-field
                                    v-for="(field, key) in addressFields"
                                    :key="key"
                                    v-model="address[key].displayName"
                                    :label="field.text"
                                    :disabled="!!isUploadCase"
                                    class="mr-4 mb-4"
                                    style="width: 200px"
                                    @input="validateAddress"
                                    outlined
                                    dense
                                    hide-details
                                  ></v-text-field>
                                </div>
                              </v-expansion-panel-content>
                            </v-expansion-panel>
                          </v-expansion-panels>

                          <div class="flex justify-end mt-4">
                            <v-btn
                              color="primary"
                              text
                              small
                              outlined
                              @click="addAddress"
                              v-show="addresses.length < addressTypes.length"
                            >
                              <v-icon left small>mdi-plus</v-icon>
                              Add Address
                            </v-btn>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-tab-item>
                  </v-tabs-items>
                </template>
                <template v-else>
                  <DataMapping
                    :caseType="caseType"
                    :orgKeys="orgKeys"
                    :extraColumns="extraColumns"
                    :staticFields="staticFields"
                    :addresses="addresses"
                    :addressFields="addressFields"
                    :addressTypes="addressTypes"
                    :isUploadCase="isUploadCase"
                    @remove-extra-column="removeExtraColumn"
                    @add-extra-column="addExtraColumn"
                    @remove-address="removeAddress"
                    @add-address="addAddress"
                    @set-default-address="setDefault"
                    @validate-address="checkForDuplicateAddressLine1"
                  />
                </template>
              </v-card-text>
            </v-card>
          </v-expansion-panels>

          <v-expansion-panels v-model="statusPanel" flat>
            <!-- Case Status Panel -->
            <v-card class="w-full mt-4" outlined>
              <v-card-title class="pb-2 text-base">
                <v-icon class="mr-2" color="primary">mdi-state-machine</v-icon>
                DEFINE STATES FOR YOUR SUB-CASE JOURNEY
              </v-card-title>
              <v-card-text>
                <p>
                  Create the possible outcomes for this sub-case and design the workflow for each.
                </p>
                <v-expansion-panel v-model="panel" flat>
                  <v-expansion-panel-header class="px-4">
                    <div>
                      <span>Outcomes</span>
                      <div>
                        <span class="text-xs font-medium text-gray-600"
                          >(Specify the different result states your sub-case can reach.)</span
                        >
                      </div>
                    </div>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-text-field
                      v-model="caseOutcomeStatus"
                      label="Outcome Column"
                      :disabled="!!isUploadCase"
                      required
                      dense
                      outlined
                    >
                      <template v-slot:append>
                        <v-tooltip top>
                          <template v-slot:activator="{ on, attrs }">
                            <v-icon
                              v-bind="attrs"
                              v-on="on"
                              class="info-icon"
                              :color="$vuetify.theme.currentTheme.primary"
                            >
                              mdi-information-outline
                            </v-icon>
                          </template>
                          <span
                            >The binded text will be displayed in the teams/cases table and the same
                            text will be used of update/create the case data.</span
                          >
                        </v-tooltip>
                      </template>
                    </v-text-field>
                    <CaseOutcomeStatus
                      :categories="categories"
                      :removeCategory="removeCategory"
                      :addCategory="addCategory"
                      :isUploadCase="isUploadCase"
                      v-model="caseOutcomeStatus"
                    />
                  </v-expansion-panel-content>
                </v-expansion-panel>
                <!-- <v-expansion-panel v-model="panel" flat v-show="!isUploadCase">
                  <v-expansion-panel-header class="px-4">
                    <div>
                      <span> Create Questionnaire (for each Outcome individually) </span>
                      <div>
                        <span class="text-xs font-medium text-gray-600"
                          >(Design a custom questionnaire for each outcome to capture relevant
                          information.)</span
                        >
                      </div>
                    </div>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <div v-if="catOptionTabs && catOptionTabs.length">
                      <v-tabs v-model="tabs" align-with-title>
                        <v-tabs-slider color="blue"></v-tabs-slider>
                        <v-tab v-for="item in catOptionTabs" :key="item" class="ml-0">
                          {{ item }}
                        </v-tab>
                      </v-tabs>
                      <v-tabs-items v-model="tabs">
                        <v-tab-item v-for="(item, index) in catOptionTabs" :key="item">
                          <div class="mt-4">
                            <Questionnaire
                              :questionTypes="allowedQuestionsTypes"
                              @questions="(val) => handleQuestions(val, index)"
                              :orgMapping="'orgMapping'"
                              :subCaseQuestions="subCaseQuestions[item]"
                              :outcomeQuestions="
                                getFilteredOutcomeQuestions(subCaseQuestions, item)
                              "
                              @clonedQuestions="
                                (val) => handleCloneAction(val, item, 'subCaseQuestions')
                              "
                            />
                          </div>
                        </v-tab-item>
                      </v-tabs-items>
                    </div>
                    <div v-else>
                      <v-alert color="orange" type="warning" text>
                        Add Outcome first to create questions
                      </v-alert>
                    </div>
                  </v-expansion-panel-content>
                </v-expansion-panel>

                <v-expansion-panel v-model="panel" flat v-show="!isUploadCase">
                  <v-expansion-panel-header class="px-4">
                    <div>
                      Create Customer feedback form (for each Outcome individually)
                      <div>
                        <span class="text-xs font-medium text-gray-600"
                          >(Set up a feedback form for each outcome to gather customer insights and
                          improve your process.)</span
                        >
                      </div>
                    </div>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <div v-if="isWhatsAppEnabled">
                      <div v-if="catOptionTabs && catOptionTabs.length">
                        <v-tabs v-model="tabs" align-with-title>
                          <v-tabs-slider color="blue"></v-tabs-slider>
                          <v-tab v-for="item in catOptionTabs" :key="item" class="ml-0">
                            {{ item }}
                          </v-tab>
                        </v-tabs>
                        <v-tabs-items v-model="tabs">
                          <v-tab-item v-for="(item, index) in catOptionTabs" :key="item">
                            <div class="mt-4">
                              <Questionnaire
                                :questionTypes="allowedQuestionsTypesCustomerFeedback"
                                @questions="(val) => handleFeedback(val, index)"
                                :orgMapping="'orgMapping'"
                                :subCaseQuestions="customerFeedback[item]"
                                :outcomeQuestions="
                                  getFilteredOutcomeQuestions(customerFeedback, item)
                                "
                                @clonedQuestions="
                                  (val) => handleCloneAction(val, item, 'customerFeedback')
                                "
                              />
                            </div>
                          </v-tab-item>
                        </v-tabs-items>
                      </div>
                      <div v-else>
                        <v-alert color="orange" type="warning" text>
                          Add Outcome first to create questions
                        </v-alert>
                      </div>
                    </div>
                    <div v-else>
                      <v-alert color="orange" type="warning" text>
                        Whatsapp integration is not enabled. Integrate whatsapp business account in
                        cualitywork's platform-integration.
                      </v-alert>
                    </div>
                  </v-expansion-panel-content>
                </v-expansion-panel> -->
              </v-card-text>
            </v-card>
          </v-expansion-panels>
        </v-form>
      </v-tab-item>

      <!-- Advanced Settings Tab -->
      <v-tab-item>
        <div class="p-4">
          <!-- Advanced Settings Selection -->
          <div class="mb-6">
            <v-card outlined>
              <v-card-title class="pb-2 text-base">
                <v-icon class="mr-2" color="primary">mdi-cog-outline</v-icon>
                Advanced Settings Configuration
              </v-card-title>
              <v-card-text>
                <!-- Settings Selection Checkboxes -->
                <div class="grid grid-cols-1 gap-4">
                  <!-- Branch Mapping Checkbox -->
                  <div class="flex items-center" v-if="branches.length">
                    <v-checkbox
                      v-model="enableBranchMapping"
                      :color="$vuetify.theme.currentTheme.primary"
                      class="mt-0 pt-0"
                      :disabled="!!isUploadCase"
                    ></v-checkbox>
                    <div class="ml-2">
                      <p class="font-semibold mb-1">Branch Linking</p>
                      <p class="text-sm text-gray-600 mb-0">
                        Associate this sub-case with one or more branches of your organization.
                      </p>
                    </div>
                  </div>
                  <!-- Branch Mapping Section -->
                  <div v-if="enableBranchMapping && branches.length">
                    <v-select
                      v-model="selectedBranch"
                      :items="branches"
                      item-value="_id"
                      item-text="branchName"
                      label="Select Branch(s)"
                      :disabled="!!isUploadCase"
                      class="pl-10"
                    ></v-select>
                  </div>

                  <!-- Entity Mapping Checkbox -->
                  <div class="flex items-center">
                    <v-checkbox
                      v-model="enableEntityMapping"
                      :color="$vuetify.theme.currentTheme.primary"
                      class="mt-0 pt-0"
                      :disabled="!!isUploadCase"
                    ></v-checkbox>
                    <div class="ml-2">
                      <p class="font-semibold mb-1">Entity / Group Assignment</p>
                      <p class="text-sm text-gray-600 mb-0">
                        Organize sub-case and customer data by assigning them to a custom group or
                        entity.
                      </p>
                    </div>
                  </div>

                  <!-- Entity Mapping Section -->
                  <div v-if="enableEntityMapping">
                    <div class="flex justify-between">
                      <v-text-field
                        v-model="entityName"
                        label="Enter Entity / Group Name"
                        required
                        :disabled="!!isUploadCase"
                        hide-details
                        class="w-3/4 pl-10"
                      >
                      </v-text-field>
                      <!-- <div class="flex justify-end gap-4 mb-3">
                        <v-switch
                          v-model="entityEnabled"
                          label="Enable"
                          class=""
                          hide-details
                        ></v-switch>
                      </div> -->
                    </div>
                  </div>

                  <!-- Data Category Mapping Checkbox -->
                  <div class="flex items-center">
                    <v-checkbox
                      v-model="enableDataCategoryMapping"
                      :color="$vuetify.theme.currentTheme.primary"
                      class="mt-0 pt-0"
                      :disabled="!!isUploadCase"
                    ></v-checkbox>
                    <div class="ml-2">
                      <p class="font-semibold mb-1">Data Categorization</p>
                      <p class="text-sm text-gray-600 mb-0">
                        Classify sub-case data into custom categories for better segmentation and
                        reporting.
                      </p>
                    </div>
                  </div>

                  <!-- Data Category Mapping Section -->
                  <div v-if="enableDataCategoryMapping">
                    <v-combobox
                      v-model="addedDataCategories"
                      :items="dataCategorizations"
                      item-text="text"
                      item-value="value"
                      multiple
                      chips
                      small-chips
                      label="Data Category Options"
                      :disabled="!!isUploadCase"
                      class="pl-10"
                    >
                      <template v-slot:selection="{ item }">
                        <v-chip>
                          {{ item }}
                          <v-icon
                            small
                            @click="removeDataCategory(item)"
                            color="red"
                            class="ml-2"
                            v-tooltip="{
                              text: 'Delete'
                            }"
                          >
                            $delete
                          </v-icon>
                        </v-chip>
                      </template>
                    </v-combobox>
                  </div>
                </div>
              </v-card-text>
              <v-card-title class="pb-2 text-base">
                <v-icon class="mr-2" color="primary">mdi-cog-outline</v-icon>
                SET NAMING CONVENTIONS
              </v-card-title>
              <v-card-text>
                <p class="px-4">
                  Define the primary status key for your workflow and customize tab names to match
                  your organization's terminology.
                </p>

                <div>
                  <v-alert type="info" color="blue" dense outlined class="text-sm font-medium">
                    The case status column value should be
                    <strong>OPEN, CLOSE, or IN_PROGRESS</strong>.
                  </v-alert>
                  <v-text-field
                    v-model="caseStatusName"
                    label="Case Status (OPEN/CLOSE/IN_PROGRESS)"
                    :disabled="!!isUploadCase"
                    required
                    outlined
                    dense
                  ></v-text-field>
                </div>
                <div>
                  <v-alert color="blue" type="info" dense outlined class="text-sm font-medium">
                    Below Tabs display in
                    <strong> {{ titleCase(useCase) }} </strong>
                    ->
                    <strong> {Sub-case} </strong>
                  </v-alert>
                  <p class="font-semibold text-base">Table Mappings</p>
                  <div class="bg-white border rounded overflow-hidden">
                    <div class="flex border-b">
                      <div
                        v-for="(tab, index) in tableTabKeys"
                        :key="index"
                        class="px-4 py-3 font-medium text-sm cursor-pointer transition-colors"
                        :class="[
                          index === 0
                            ? 'text-green-600 border-b-2 border-green-500'
                            : 'text-gray-600 hover:bg-gray-100'
                        ]"
                      >
                        {{ tab.text }}
                      </div>
                    </div>
                    <div class="p-4 bg-gray-50">
                      <div class="text-sm text-gray-500 text-center">
                        <div class="grid grid-cols-3 gap-4">
                          <div v-for="(key, i) in tableTabKeys" :key="i" class="mb-5 w-full">
                            <v-text-field
                              v-model="key.value"
                              :label="key.text"
                              :disabled="!!isUploadCase"
                              required
                              outlined
                              dense
                              hide-details
                            ></v-text-field>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </div>
        </div>
      </v-tab-item>
      <div class="flex justify-end gap-6 m-4" v-if="!isUploadCase">
        <div class="flex justify-end gap-4">
          <v-btn elevation="2" small text @click="onCancel"> Cancel </v-btn>
          <!-- @click="editSubCase?.key === 'COLLECTION' ? onSubmit() : createExtendUseCase()" -->
          <v-btn
            depressed
            @click="editSubCase?.key === 'COLLECTION' ? onSubmit() : createExtendUseCase()"
            color="primary"
            elevation="2"
            small
          >
            Save
          </v-btn>
        </div>
      </div>
    </v-tabs-items>

    <AlertPopUp
      :showConfirmationDialog="showConfirmationClone"
      :confirmationTitle="'Confirmation for existing questions'"
      :confirmationMessage="'Questions already exist in this case outcome type, you want to keep the previous questions or replace with new questions'"
      :confirmationActionText="'Replace with new questions'"
      :cancelActionText="'Keep previous questions'"
      :confirmationActionColor="'green'"
      :performAction="cloneNewQuestions"
      :cancel="cloneWithExistingQuestions"
    />
  </div>
</template>

<script>
// import Questionnaire from './OrgConfigComponents/Questionnaire'
import CaseOutcomeStatus from './OrgConfigComponents/CaseOutcomeStatus'
import AlertPopUp from '@/components/AlertPopUp'
import { mergeOptions } from '@/utils/common'
import DataMapping from './OrgConfigComponents/DataMapping.vue'
import { convertToTitleCase } from '@/utils/common'

export default {
  name: 'OrgMapping',
  components: { CaseOutcomeStatus, AlertPopUp, DataMapping },
  data() {
    return {
      userOrg: this.$storage.getUniversal('user'),
      caseType: {
        text: 'Sub-case Name',
        key: 'caseType',
        value: '',
        rules: [(v) => !!v || 'Subcase Name is required']
      },
      tabs: 0,
      dataMappingTab: 0,
      orgKeys: [],
      tableTabKeys: [
        { text: 'Cases', key: 'caseTable', value: 'Case' },
        { text: 'Visits', key: 'visitTable', value: 'Visit' },
        {
          text: 'Case Analytics',
          key: 'caseAnalyticsTable',
          value: 'Case Analytics'
        },
        {
          text: 'Call History',
          key: 'callHistoryTable',
          value: 'Call History'
        },
        {
          text: 'Customers',
          key: 'customerTable',
          value: 'Customers'
        }
      ],
      staticFields: {
        crn: '',
        customerFullName: '',
        customerMobile: '',
        customerEmail: '',
        crnRules: [(v) => !!v || 'CRN No is required'],
        customerFullNameRules: [(v) => !!v || 'Customer Name is required']
      },
      addressTypes: [
        'CURRENT_ADDRESS',
        'PERMANENT_ADDRESS',
        'RESIDENTIAL_ADDRESS',
        'OFFICE_ADDRESS',
        'BILLING_ADDRESS',
        'MAILING_ADDRESS'
      ],
      addressFields: {
        addressLine1: { key: '', text: 'Address Line 1' },
        addressLine2: { key: '', text: 'Address Line 2' },
        landMark: { key: '', text: 'Land Mark' },
        city: { key: '', text: 'City' },
        state: { key: '', text: 'State' },
        country: { key: '', text: 'Country' },
        pinCode: { key: '', text: 'Pincode' }
      },
      addresses: [
        {
          typeOfAddress: 'CURRENT_ADDRESS',
          default: false,
          addressLine1: { key: '', displayName: '' },
          addressLine2: { key: '', displayName: '' },
          landMark: { key: '', displayName: '' },
          city: { key: '', displayName: '' },
          state: { key: '', displayName: '' },
          country: { key: '', displayName: '' },
          pinCode: { key: '', displayName: '' }
        }
      ],
      loading: false,
      caseOutcomeStatus: '',
      caseStatusName: '',
      fieldPanel: 0,
      statusPanel: 0,
      panel: 0,
      extraColumns: [],
      branches: [],
      selectedBranch: '',
      valid: true,
      subCaseQuestions: {},
      customerFeedback: {},
      alertMessage: '',
      showConfirmationClone: false,
      clonedValue: null,
      clonedKey: null,
      cloneTarget: null,
      entityName: 'Entity',
      entityEnabled: false,
      dataCategorizations: [],
      addedDataCategories: [],
      enableBranchMapping: false,
      enableEntityMapping: false,
      enableDataCategoryMapping: false,
      mainTab: 0,
      categories: [
        // {
        //   categoryName: '',
        //   options: [],
        //   optionsList: [],
        //   assignedTemplates: {},
        //   caseOutcomeFormId: {},
        //   customerFormId: {}
        // }
      ],
      baseFieldMappings: {}
    }
  },
  props: {
    useCase: String,
    enabledSubCases: {
      type: Array,
      default: () => []
    },
    useCaseLabel: {
      type: [Object, Array],
      default: () => ({})
    },
    baseMapping: Object,
    editSubCase: Object,
    cloneSubCase: Object,
    isNewSubCase: Boolean,
    isUploadCase: Boolean,
    allowedQuestionsTypes: Array,
    allowedQuestionsTypesCustomerFeedback: Array,
    isWhatsAppEnabled: Boolean,
    showMappingDetails: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    catOptionTabs() {
      return mergeOptions(this.categories) || []
    },
    mendatoryFields() {
      return this.orgKeys
        .filter((key) =>
          [
            'pos',
            'emiAmount',
            'tenure',
            'typeOfLoan',
            'productInfo',
            'taskTitle',
            'taskDescription',
            'bkt'
          ].includes(key.key)
        )
        .map((key) => ({
          text: key.text,
          value: key.value || key.text,
          key: key.key
        }))
    },
    dynamicOrgKeys() {
      const keys = []
      if (this.useCaseLabel || this.baseMapping) {
        const combinedLabels = {
          ...this.baseMapping,
          ...this.useCaseLabel
        }
        Object.entries(combinedLabels).forEach(([key, value]) => {
          keys.push({
            text: value.text || key,
            key: key,
            value: value.displayName || '',
            visible: value.visible !== undefined ? value.visible : true,
            required: value.required !== undefined ? value.required : false,
            prefix: value.prefix || undefined,
            suffix: value.suffix || undefined,
            formVisibility: value.formVisibility !== undefined ? value.formVisibility : false,
            tableVisibility: value.tableVisibility !== undefined ? value.tableVisibility : false
            // ...(key === 'caseNo' && { rules: [(v) => !!v || 'Case No is required'] })
          })
        })
      }

      return keys
    }
  },
  methods: {
    titleCase(str) {
      return convertToTitleCase(str)
    },

    buildStaticFieldsAndAddresses() {
      const formatKey = (displayName) => {
        return (displayName || '').trim().replace(/\s+/g, '_').toLowerCase()
      }

      let staticFieldMappings = {}

      // Add static fields
      staticFieldMappings['crn'] = {
        key: formatKey(this.staticFields?.crn),
        displayName: this.staticFields?.crn,
        visibility: true,
        formVisibility: false,
        tableVisibility: false
      }
      staticFieldMappings['customerFullName'] = {
        key: formatKey(this.staticFields?.customerFullName),
        displayName: this.staticFields?.customerFullName,
        visibility: true,
        formVisibility: false,
        tableVisibility: false
      }
      staticFieldMappings['customerMobile'] = {
        key: formatKey(this.staticFields?.customerMobile),
        displayName: this.staticFields?.customerMobile,
        visibility: true,
        formVisibility: false,
        tableVisibility: false
      }
      staticFieldMappings['customerEmail'] = {
        key: formatKey(this.staticFields?.customerEmail),
        displayName: this.staticFields?.customerEmail,
        visibility: true,
        formVisibility: false,
        tableVisibility: false
      }

      // Add addresses if present
      if (this.addresses[0]?.addressLine1?.displayName) {
        staticFieldMappings.addresses = this.addresses?.map((address) => {
          if (!address.addressLine1 || !address.addressLine1.displayName) {
            throw new Error(`Address Line 1 is required for ${address.typeOfAddress}.`)
          }

          let addressMapping = { typeOfAddress: address.typeOfAddress }
          Object.keys(this.addressFields)?.forEach((fieldKey) => {
            const field = address[fieldKey]
            const transformedKey = formatKey(field.displayName)
            addressMapping[fieldKey] = {
              key: transformedKey || fieldKey,
              displayName: field.displayName
            }
          })
          return addressMapping
        })

        const defaultAddress = this.addresses.find((address) => address.default)
        staticFieldMappings.defaultAddress = defaultAddress ? defaultAddress.typeOfAddress : null
      }

      return staticFieldMappings
    },
    removeExtraColumn(index) {
      this.extraColumns.splice(index, 1)
    },
    getFilteredItems(index) {
      const search = this.extraColumns[index].search || this.extraColumns[index].text
      if (search) {
        const baseItems = ['STRING', 'NUMBER', 'DATE', 'CURRENCY']
        return baseItems.map((type) => ({
          text: `${search} - ${type}`,
          value: `${search} - ${type}`
        }))
      }
      return []
    },
    getItemType(item) {
      const itemType = item?.split('-')
      const type = itemType[1]?.trim()
      // console.log(itemType[1]?.trim());
      return type
    },

    updateColumnValue(index, value) {
      this.extraColumns[index].value = value
    },
    addExtraColumn() {
      const newColumnIndex = this.extraColumns.length
      const newColumn = {
        text: `Column ${newColumnIndex + 1}`,
        key: `column_${newColumnIndex + 1}`,
        value: '',
        search: ''
      }

      this.extraColumns = [...this.extraColumns, newColumn]
    },

    removeCategory(index) {
      this.categories.splice(index, 1)
    },
    removeAddress(index) {
      this.checkForDuplicateAddressLine1()
      this.addresses.splice(index, 1)
    },
    addAddress() {
      const newAddress = {
        typeOfAddress: '',
        addressLine1: { key: '', displayName: '' },
        default: false,
        addressLine2: { key: '', displayName: '' },
        landMark: { key: '', displayName: '' },
        city: { key: '', displayName: '' },
        state: { key: '', displayName: '' },
        country: { key: '', displayName: '' },
        pinCode: { key: '', displayName: '' }
      }
      this.addresses.push(newAddress)
      this.checkForDuplicateAddressLine1()
    },
    setDefault(index) {
      this.addresses.forEach((address, i) => {
        if (i !== index) {
          address.default = false
        }
      })
    },

    getFilteredAddressTypes(currentIndex) {
      const selectedTypes = this.addresses
        .filter((_, index) => index !== currentIndex)
        .map((address) => address.typeOfAddress)

      return this.addressTypes.filter((type) => !selectedTypes.includes(type))
    },
    async createExtendUseCase() {
      try {
        let fieldMappings = {}
        let additionalColumns = {}
        let tableTabMapping = {}
        let hasLoanManagementKeys = false

        const formatKey = (displayName) => {
          return (displayName || '').trim().replace(/\s+/g, '_').toLowerCase()
        }

        this.orgKeys.forEach((orgKey) => {
          if (this.baseMapping[orgKey.key]) {
            this.baseMapping[orgKey.key].displayName = orgKey.value
            this.baseMapping[orgKey.key].key = formatKey(orgKey.value)
            this.baseMapping[orgKey.key].formVisibility =
              orgKey.formVisibility !== undefined ? orgKey.formVisibility : false
            this.baseMapping[orgKey.key].tableVisibility =
              orgKey.tableVisibility !== undefined ? orgKey.tableVisibility : false
          } else if (this.useCaseLabel[orgKey.key]) {
            this.useCaseLabel[orgKey.key].displayName = orgKey.value
            this.useCaseLabel[orgKey.key].formVisibility =
              orgKey.formVisibility !== undefined ? orgKey.formVisibility : false
            this.useCaseLabel[orgKey.key].tableVisibility =
              orgKey.tableVisibility !== undefined ? orgKey.tableVisibility : false
            hasLoanManagementKeys = true
          } else {
            fieldMappings[orgKey.key] = {
              key: formatKey(orgKey.value),
              displayName: orgKey.value,
              visible: orgKey.visible !== undefined ? orgKey.visible : true,
              formVisibility: orgKey.formVisibility !== undefined ? orgKey.formVisibility : false,
              tableVisibility:
                orgKey.tableVisibility !== undefined ? orgKey.tableVisibility : false,
              required: orgKey.required !== undefined ? orgKey.required : false
            }
          }
        })

        // Add static fields and addresses using helper function
        try {
          const staticFieldMappings = this.buildStaticFieldsAndAddresses()
          fieldMappings = { ...fieldMappings, ...staticFieldMappings }
        } catch (error) {
          this.$toast.error(error.message)
          return
        }

        // Add case outcome status and case status
        fieldMappings['caseOutcomeStatus'] = {
          key: formatKey(this.caseOutcomeStatus),
          displayName: this.caseOutcomeStatus,
          visibility: true
        }

        this.tableTabKeys.forEach((tk) => {
          const newKey = formatKey(tk.value)
          tableTabMapping[tk.key] = {
            text: tk.text,
            key: newKey,
            displayName: tk.value?.trim()
          }
        })

        this.extraColumns.forEach((el) => {
          const display = el.value?.split('-')
          const newKey = display[0]?.trim().split(' ').join('_').toLowerCase()
          const type = display[1]?.trim()
          additionalColumns[newKey] = {
            key: newKey,
            displayName: display[0].trim(),
            fieldType: type,
            prefix: el.selectedPrefix
          }
        })

        let loanManagementFieldMappingId = null

        // Handle loan management API update if editing
        if (this.$props.editSubCase && this.$props.editSubCase.loanManagementFieldMapping) {
          const loanManagementPayload = {
            ...this.useCaseLabel
          }

          // Update existing loan management mapping
          const loanMappingResponse = await this.$axios.put(
            `/workforce/orgSetup/loan-management-field-mapping/${this.$props.editSubCase.loanManagementFieldMapping?._id}`,
            loanManagementPayload
          )

          loanManagementFieldMappingId = this.$props.editSubCase.loanManagementFieldMapping?._id
        } else if (hasLoanManagementKeys) {
          // Create new loan management mapping
          const loanManagementPayload = {
            ...this.useCaseLabel
          }

          const loanMappingResponse = await this.$axios.post(
            '/workforce/orgSetup/loan-management-field-mapping',
            loanManagementPayload
          )

          loanManagementFieldMappingId = loanMappingResponse.data?.data?._id
        }

        const userDefinedCaseStatus = this.categories
        const dataCategories = this.addedDataCategories.map((category) => {
          return { name: category }
        })

        const entityKey = this.entityName?.trim()?.split(' ')?.join('_').toLowerCase()

        let extendUseCasePayload = {
          useCase: this.$props.useCase.toLowerCase(),
          branch: this.selectedBranch || undefined,
          name: this.caseType.value,
          customerFieldMappings: fieldMappings,
          additionalColumns,
          tableTabMapping,
          entity: {
            displayName: this.entityName,
            key: entityKey,
            enabled: this.entityEnabled
          },
          baseFieldMappings: this.baseMapping,
          questionnaire: this.subCaseQuestions || {},
          userFeedbackQuestionnaire: this.customerFeedback || {},
          userDefinedCaseStatus,
          dataCategories
        }
        console.log({ extendUseCasePayload })
        if (loanManagementFieldMappingId) {
          extendUseCasePayload.loanManagementFieldMapping = loanManagementFieldMappingId
        }

        let extendUseCaseResponse

        // Update or create extend usecase based on whether we're editing
        if (this.$props.editSubCase && this.$props.editSubCase._id) {
          extendUseCaseResponse = await this.$axios.put(
            `/workforce/orgSetup/extend-usecase/${this.$props.editSubCase._id}`,
            extendUseCasePayload
          )
          this.$toast.success('Use case updated successfully')
        } else {
          extendUseCaseResponse = await this.$axios.post(
            '/workforce/orgSetup/extend-usecase',
            extendUseCasePayload
          )
          this.$toast.success('Use case saved as draft successfully')
        }

        this.$emit('orgMapKeys', extendUseCaseResponse.data.data)
      } catch (error) {
        console.error('Error in createExtendUseCase:', error)
        this.$toast.error(
          error.response?.data?.message || 'An error occurred while saving the draft'
        )
      }
    },

    async onSubmit() {
      let fieldMappings = {}
      let additionalColumns = {}
      let tableTabMapping = {}
      const isFormValid = this.$refs.form.validate()
      if (!isFormValid) {
        this.$toast.error('Validation failed. Please fill all the required field in the form.')
        return
      }

      const formatKey = (displayName) => {
        return (displayName || '').trim().replace(/\s+/g, '_').toLowerCase()
      }

      // Add static fields and addresses using helper function
      let isValid = true
      try {
        const staticFieldMappings = this.buildStaticFieldsAndAddresses()
        fieldMappings = { ...fieldMappings, ...staticFieldMappings }
      } catch (error) {
        this.$toast.error(error.message)
        isValid = false
      }

      if (!isValid) {
        return
      }

      const userDefinedCaseStatus = this.categories

      this.orgKeys.forEach((el) => {
        fieldMappings[el.key] = {
          key: formatKey(el.value),
          displayName: el.value,
          visibility: el.visible,
          formVisibility: el.formVisibility !== undefined ? el.formVisibility : false,
          tableVisibility: el.tableVisibility !== undefined ? el.tableVisibility : false,
          required: el.required,
          prefix: el.selectedPrefix,
          suffix: el.selectedSuffix
        }
      })

      fieldMappings['caseOutcomeStatus'] = {
        key: formatKey(this.caseOutcomeStatus),
        displayName: this.caseOutcomeStatus,
        visibility: true
      }

      fieldMappings['status'] = {
        key: formatKey(this.caseStatusName),
        displayName: this.caseStatusName,
        visibility: true
      }

      this.tableTabKeys.forEach((tk) => {
        const newKey = formatKey(tk.value)
        tableTabMapping[tk.key] = {
          text: tk.text,
          key: newKey,
          displayName: tk.value?.trim()
        }
      })

      const dataCategories = this.addedDataCategories.map((category) => {
        return { name: category }
      })

      this.extraColumns.forEach((el) => {
        const display = el.value?.split('-')
        const newKey = display[0]?.trim().split(' ').join('_').toLowerCase()
        const type = display[1].trim()
        additionalColumns[newKey] = {
          key: newKey,
          displayName: display[0].trim(),
          fieldType: type,
          prefix: el.selectedPrefix
        }
      })
      // console.log(this.extraColumns);
      // console.log(additionalColumns);

      const existingSubCase = this.$props.enabledSubCases.flatMap((sub) => sub.subCase)

      let enabledSubCases
      const entityKey = this.entityName?.trim()?.split(' ')?.join('_').toLowerCase()

      if (this.$props.cloneSubCase?.cloneIndex) {
        if (this.checkDuplicateCaseType()) return
        enabledSubCases = [
          {
            useCase: this.$props.useCase.toLowerCase(),
            subCase: [
              ...existingSubCase,
              {
                branch: this.selectedBranch || undefined,
                name: this.caseType.value,
                entity: {
                  displayName: this.entityName,
                  key: entityKey,
                  enabled: this.entityEnabled
                },
                fieldMappings,
                additionalColumns,
                tableTabMapping,
                userDefinedCaseStatus,
                questionnaire: this.subCaseQuestions || {},
                userFeedbackQuestionnaire: this.customerFeedback || {},
                dataCategories
              }
            ]
          }
        ]
      }

      if (this.$props.editSubCase?.indx !== undefined) {
        existingSubCase[this.$props.editSubCase.indx] = {
          branch: this.selectedBranch || undefined,
          name: this.caseType.value,
          entity: {
            displayName: this.entityName,
            key: entityKey,
            enabled: this.entityEnabled
          },
          fieldMappings,
          additionalColumns,
          tableTabMapping,
          userDefinedCaseStatus,
          questionnaire: this.subCaseQuestions || {},
          userFeedbackQuestionnaire: this.customerFeedback || {},
          dataCategories
        }

        enabledSubCases = [
          {
            useCase: this.$props.useCase.toLowerCase(),
            subCase: existingSubCase
          }
        ]
      } else {
        if (this.checkDuplicateCaseType()) return
        enabledSubCases = [
          {
            useCase: this.$props.useCase.toLowerCase(),
            subCase: [
              ...existingSubCase,
              {
                branch: this.selectedBranch || undefined,
                name: this.caseType.value,
                entity: {
                  displayName: this.entityName,
                  key: entityKey,
                  enabled: this.entityEnabled
                },
                fieldMappings,
                additionalColumns,
                tableTabMapping,
                userDefinedCaseStatus,
                questionnaire: this.subCaseQuestions || {},
                userFeedbackQuestionnaire: this.customerFeedback || {},
                dataCategories
              }
            ]
          }
        ]
      }

      // console.log(enabledSubCases, ' enabled')
      this.$emit('orgSubCaseMapKeys', enabledSubCases)
    },
    handleQuestions(val, index) {
      const status = this.catOptionTabs[index]
      if (!this.subCaseQuestions || Array.isArray(this.subCaseQuestions)) {
        this.subCaseQuestions = {}
      }
      this.$set(this.subCaseQuestions, status, val)
    },
    handleCloneAction(val, item, targetType) {
      this.clonedValue = val.value
      this.clonedKey = item
      this.cloneTarget = targetType

      if (Object.keys(this[targetType]).includes(item)) {
        this.showConfirmationClone = true
      } else {
        this.cloneNewQuestions()
      }
    },

    cloneWithExistingQuestions() {
      this[this.cloneTarget] = {
        ...this[this.cloneTarget],
        [this.clonedKey]: [...(this[this.cloneTarget][this.clonedKey] || []), ...this.clonedValue]
      }
      this.resetCloneState()
    },

    cloneNewQuestions() {
      this[this.cloneTarget] = {
        ...this[this.cloneTarget],
        [this.clonedKey]: this.clonedValue
      }
      this.resetCloneState()
    },

    resetCloneState() {
      this.showConfirmationClone = false
      this.clonedValue = null
      this.clonedKey = null
      this.cloneTarget = null
      this.$toast.success(`Questions copied successfully`)
    },
    removeDataCategory(item) {
      const index = this.addedDataCategories.indexOf(item)
      if (index !== -1) {
        this.addedDataCategories.splice(index, 1)
      }
    },
    handleFeedback(val, index) {
      const status = this.catOptionTabs[index]
      if (!this.customerFeedback || Array.isArray(this.customerFeedback)) {
        this.customerFeedback = {}
      }
      this.$set(this.customerFeedback, status, val)
    },
    async fetchBranches() {
      try {
        const response = await this.$axios.get('/workforce/branches')
        this.branches = response.data.branches
      } catch (error) {
        console.error('Error fetching bracnhes:', error)
      }
    },
    onCancel() {
      this.orgKeys = []
      this.$emit('cancel')
      this.$refs.form.reset()
      // this.$refs.formAddress.reset();
      this.extraColumns = []
      this.categories = [
        {
          categoryName: '',
          options: [],
          optionsList: [],
          assignedTemplates: {},
          caseOutcomeFormId: {},
          customerFormId: {}
        }
      ]
    },
    addCategory() {
      this.categories.push({
        categoryName: '',
        options: [],
        optionsList: [],
        assignedTemplates: {},
        caseOutcomeFormId: {},
        customerFormId: {}
      })
    },
    checkForDuplicateAddressLine1() {
      const keyMap = new Map()

      this.addresses.forEach((address, index) => {
        const key = address.addressLine1.key

        if (key && keyMap.has(key)) {
          this.alertMessage = `Duplicate addressLine1 key found in Address ${
            keyMap.get(key) + 1
          } and Address ${index + 1}`
        } else {
          keyMap.set(key, index)
        }
      })
    },
    populateFields(subCase) {
      // console.log(this.useCaseLabel, 'useCaseLabel')
      if (!subCase) {
        this.$refs?.form?.reset()
        // this.orgKeys = []
        this.extraColumns = []
        this.categories = []
        return
      }

      this.subCaseQuestions = subCase?.questionnaire || {}
      this.customerFeedback = subCase?.userFeedbackQuestionnaire || {}
      const updatedOrgKeys = subCase.baseFieldMappings ? [...this.orgKeys] : this.useCaseLabel

      const fieldMappings = subCase.fieldMappings || {}
      const useCaseMapping = subCase.baseFieldMappings
        ? { ...subCase.baseFieldMappings, ...subCase.loanManagementFieldMapping }
        : subCase.fieldMappings || {} // Add fallback empty object
      // console.log({ updatedOrgKeys })
      const addressesArray = fieldMappings.addresses || []
      this.enableBranchMapping = subCase.branch ? true : false
      this.enableEntityMapping = subCase.entity ? true : false
      this.enableDataCategoryMapping = subCase.dataCategories?.length ? true : false
      this.caseOutcomeFormId = subCase.caseOutcomeFormId || {}
      this.customerFormId = subCase.customerFormId || {}

      updatedOrgKeys?.forEach((k) => {
        if (k && useCaseMapping[k.key]) {
          // Add null check for k and useCaseMapping[k.key]
          k.value = useCaseMapping[k.key]?.displayName || ''
          k.visibility = useCaseMapping[k.key]?.visible ? useCaseMapping[k.key]?.visible : false
          k.required = useCaseMapping[k.key]?.required ? useCaseMapping[k.key]?.required : false
          k.selectedPrefix = useCaseMapping[k.key]?.prefix
          ;(k.selectedSuffix = useCaseMapping[k.key]?.suffix),
            (k.formVisibility = useCaseMapping[k.key]?.formVisibility)
          k.tableVisibility = useCaseMapping[k.key]?.tableVisibility
        }
      })

      if (addressesArray.length) {
        const defaultAddressType = fieldMappings.defaultAddress || ''
        this.addresses = addressesArray.map((address) => ({
          ...address,
          default: address.typeOfAddress === defaultAddressType
        }))
      }

      this.selectedBranch = subCase.branch || null
      this.entityName = subCase.entity?.displayName || 'Entity'
      this.entityEnabled = subCase.entity?.enabled
      this.orgKeys = updatedOrgKeys
      this.addedDataCategories = subCase.dataCategories?.map((cat) => cat.name) || []

      if (subCase.cloneIndex == 0 || subCase.cloneIndex) {
        this.caseType.value = ''
      } else {
        this.caseType.value = subCase.name
      }
      this.caseOutcomeStatus = fieldMappings.caseOutcomeStatus?.displayName || ''
      this.caseStatusName = fieldMappings.status?.displayName || ''
      this.staticFields.crn =
        fieldMappings.crn?.displayName || subCase.customerFieldMappings?.crn?.displayName
      this.staticFields.customerFullName =
        fieldMappings.customerFullName?.displayName ||
        subCase.customerFieldMappings?.customerFullName?.displayName
      this.staticFields.customerMobile =
        fieldMappings.customerMobile?.displayName ||
        subCase.customerFieldMappings?.customerMobile?.displayName
      this.staticFields.customerEmail =
        fieldMappings.customerEmail?.displayName ||
        subCase.customerFieldMappings?.customerEmail?.displayName

      this.extraColumns = Object.keys(subCase.additionalColumns || {}).map((k) => {
        const column = subCase.additionalColumns[k]
        const displayName = column.displayName
        const fieldType = column.fieldType
        let value = fieldType ? `${displayName} - ${fieldType}` : `${displayName} - STRING`
        return {
          text: displayName,
          key: column.key,
          value: value,
          search: displayName,
          prefix: column.prefix
        }
      })

      this.tableTabKeys = subCase?.tableTabMapping
        ? Object.entries(subCase.tableTabMapping).map(([key, obj]) => ({
            text: obj.text,
            key,
            value: obj.displayName
          }))
        : this.tableTabKeys.map((el) => ({
            text: el.text,
            key: el.key,
            value: el.text
          }))

      this.categories = (subCase.userDefinedCaseStatus || []).map((el) => ({
        categoryName: el.categoryName || 'Category 1',
        options: el.options || [],
        optionsList: el.optionsList || [],
        assignedTemplates: el.assignedTemplates || {},
        caseOutcomeFormId: el.caseOutcomeFormId || {},
        customerFormId: el.customerFormId || {}
      }))
    },
    checkDuplicateCaseType() {
      const existingSubCaseNames = this.$props.enabledSubCases.flatMap((sub) =>
        sub.subCase.map((s) => s.name.toLowerCase())
      )
      const isDuplicateCaseName = existingSubCaseNames.includes(this.caseType?.value?.toLowerCase())

      if (isDuplicateCaseName) {
        this.$toast.error('Case type name must be unique. A case with this name already exists.')
        return isDuplicateCaseName
      }
    },
    validateAddress() {
      this.checkForDuplicateAddressLine1()
    },
    getFilteredOutcomeQuestions(questions, currentKey) {
      return Object.fromEntries(Object.entries(questions).filter(([key]) => key !== currentKey))
    }
  },
  watch: {
    addresses: {
      deep: true,
      handler() {
        this.checkForDuplicateAddressLine1()
      }
    },
    editSubCase: {
      async handler(subCase) {
        if (subCase) {
          console.log({ subCase })
          this.populateFields(subCase)
        }
      },
      immediate: true
    },
    // cloneSubCase: {
    //   handler(subCase) {
    //     if (subCase) {
    //       console.log({ subCase })

    //       this.populateFields(subCase)
    //     }
    //   },
    //   immediate: true
    // },
    dynamicOrgKeys: {
      handler(newKeys) {
        // console.log({ newKeys })
        if (newKeys.length > 0 && (!this.editSubCase || !this.orgKeys.length)) {
          this.orgKeys = [...newKeys]
        }
      },
      immediate: true
    }
  },
  async mounted() {
    await this.fetchBranches()
    this.subCaseQuestions = this.editSubCase?.questionnaire || []
    this.customerFeedback = this.editSubCase?.userFeedbackQuestionnaire || []
    this.checkForDuplicateAddressLine1()
  }
}
</script>
<style scoped>
.custom-input-group {
  display: flex;
  align-items: center;
  padding: 2px;
  background-color: white;
}

.custom-input-group .v-select .v-input__control,
.custom-input-group .v-autocomplete .v-input__control {
  background-color: white !important;
}

.select-input {
  flex-grow: 1;
  width: 20%;
  /* font-size: smaller; */
}

.text-input {
  flex-grow: 2;
  margin-left: 2px;
  width: 70%;
}
</style>
