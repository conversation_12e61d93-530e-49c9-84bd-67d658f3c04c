<template>
  <div>
    <div
      class="justify-end items-center ml-auto"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex mb-4' : 'flex flex-col']"
    >
      <v-col
        :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
        :class="[$vuetify.breakpoint.lgAndUp ? 'py-0 pr-0' : 'ml-auto mt-2 p-0']"
      >
        <v-text-field
          outlined
          dense
          hide-details
          placeholder="Search Role"
          v-model="filterRole"
          @input="handleInput"
          clearable
        ></v-text-field>
      </v-col>
      <v-col
        :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
        :class="[$vuetify.breakpoint.lgAndUp ? 'py-0 pr-0' : 'ml-auto mt-2 p-0']"
      >
        <v-select
          label="Privileges"
          placeholder="Select Privileges"
          v-model="selectedPrivileges"
          :items="additionalPermissions"
          item-text="value"
          item-value="key"
          :menu-props="{ offsetY: true }"
          solo
          flat
          multiple
          small-chips
          clearable
          hide-details
          dense
          outlined
          @change="updateTableHeaders"
        >
          <v-list-item class="pl-4 select-all-item" slot="prepend-item" @click="toggleSelectAll">
            <v-list-item-action>
              <v-icon :color="selectedPrivileges.length > 0 ? 'blue darken-2' : ''">{{
                icon
              }}</v-icon>
            </v-list-item-action>
            <v-list-item-title>Select All</v-list-item-title>
          </v-list-item>
          <v-divider slot="prepend-item" />

          <template v-slot:selection="{ item, index }">
            <v-chip class="chip-width" v-if="index === 0">
              <span class="chip-text">{{ item.value }}</span>
            </v-chip>
            <span v-if="index === 1" class="grey--text text-caption">
              (+{{ selectedPrivileges.length - 1 }} more)
            </span>
          </template>
        </v-select>
      </v-col>
      <!-- <v-btn
        v-if="!$attrs.hideActionButton"
        :color="$vuetify.theme.currentTheme.primary"
        class="white--text"
        :class="[$vuetify.breakpoint.lgAndUp ? 'ml-4 mr-2' : 'mr-auto mt-2 ml-3']"
        @click="openForm"
        v-show="canAdd"
        dense
      >
        Add
        <v-icon right dark> mdi-plus-circle </v-icon>
      </v-btn> -->
    </div>
    <v-dialog
      v-model="showForm"
      max-width="75%"
      class="px-4"
      content-class="dialog-content"
      persistent
    >
      <v-card>
        <v-card-title class="sticky top-0 bg-white dark-bg-custom shadow-sm z-50">
          <div class="flex flex-col">
            <div class="flex justify-between mb-3">
              <span class="headline">Add/Edit a Role</span>
              <v-btn plain class="p-4 text-lg" @click="closeForm">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
            <v-text-field
              autofocus
              v-model="roleName"
              label="Role Name"
              :error-messages="roleNameValidationRule"
              ref="roleNameField"
              outlined
              dense
            ></v-text-field>
          </div>
        </v-card-title>
        <v-card-text class="mt-2">
          <v-expansion-panels v-model="panel" accordion>
            <v-expansion-panel class="my-3">
              <v-expansion-panel-header class="py-0">
                <h3 class="font-medium text-lg text-black dark-text-color">Privilege</h3>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div class="flex justify-end mb-2">
                  <v-btn
                    :color="$vuetify.theme.currentTheme.primary"
                    class="white--text"
                    @click="toggleAllPrivilegePermissions"
                    small
                  >
                    {{ allPrivilegePermissionsText }}
                  </v-btn>
                </div>
                <v-row>
                  <v-col cols="6">
                    <v-checkbox
                      :color="$vuetify.theme.currentTheme.primary"
                      @click="checkFormChanges"
                      v-for="(permission, index) in additionalPermissions.slice(
                        0,
                        Math.ceil(additionalPermissions.length / 2)
                      )"
                      :key="index"
                      v-model="permission.checked"
                      :label="permission.value"
                    ></v-checkbox>
                  </v-col>
                  <v-col cols="6">
                    <v-checkbox
                      :color="$vuetify.theme.currentTheme.primary"
                      @click="checkFormChanges"
                      v-for="(permission, index) in additionalPermissions.slice(
                        Math.ceil(additionalPermissions.length / 2)
                      )"
                      :key="index + Math.ceil(additionalPermissions.length / 2)"
                      v-model="permission.checked"
                      :label="permission.value"
                    ></v-checkbox>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel class="mb-3">
              <v-expansion-panel-header class="py-0">
                <h3 class="font-medium text-lg text-black dark-text-color">Permissions</h3>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div class="flex justify-end mb-2">
                  <v-btn
                    :color="$vuetify.theme.currentTheme.primary"
                    class="white--text"
                    @click="toggleAllPermissions"
                  >
                    {{ allPermissionsText }}
                  </v-btn>
                </div>
                <div
                  class="mb-2 text-red-600"
                  :class="{
                    'opacity-0': !isFormSubmitted || areAnyPermissionsSelected
                  }"
                >
                  Please select at least one permission.
                </div>
                <v-data-table
                  :headers="permissionHeaders"
                  :items="filteredPermissions"
                  disable-pagination
                  hide-default-footer
                >
                  <template v-slot:item="{ item }">
                    <tr>
                      <td class="px-4">{{ item.name }}</td>
                      <td class="px-4">
                        <v-checkbox
                          v-model="item.read"
                          :color="$vuetify.theme.currentTheme.primary"
                        ></v-checkbox>
                      </td>
                      <td class="px-4">
                        <v-checkbox
                          :color="$vuetify.theme.currentTheme.primary"
                          v-model="item.write"
                          :disabled="checkIsReadChecked(item)"
                        ></v-checkbox>
                      </td>
                      <td class="px-4">
                        <v-checkbox
                          :color="$vuetify.theme.currentTheme.primary"
                          v-model="item.edit"
                          :disabled="checkIsReadChecked(item)"
                        ></v-checkbox>
                      </td>
                      <td class="px-4">
                        <v-checkbox
                          :color="$vuetify.theme.currentTheme.primary"
                          v-model="item.delete"
                          :disabled="checkIsReadChecked(item)"
                        ></v-checkbox>
                      </td>
                      <td class="px-4">
                        <v-chip
                          @click="toggleSelectAllPermissions(item)"
                          class="w-full text-center"
                        >
                          {{ isAllPermissionsSelected(item) ? 'Unselect All' : 'Select All' }}
                        </v-chip>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-expansion-panel-content>
            </v-expansion-panel>
            <v-expansion-panel class="mb-3">
              <v-expansion-panel-header class="py-0">
                <h3 class="font-medium text-lg text-black dark-text-color">
                  Link On-Boarding/Off-Boarding Forms
                </h3>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-container fluid class="pa-0">
                  <v-row>
                    <v-col cols="6" class="pr-6">
                      <v-card outlined class="pa-4">
                        <h4 class="text-subtitle-1 font-weight-medium mb-4">Onboarding Settings</h4>
                        <v-switch
                          v-model="onboard_data.enabled"
                          label="Enable Onboarding"
                          :color="$vuetify.theme.currentTheme.primary"
                          dense
                          class="mb-2"
                          hide-details
                        ></v-switch>
                        <v-switch
                          v-model="onboard_data.mandatory"
                          label="Mandatory Onboarding"
                          :color="$vuetify.theme.currentTheme.primary"
                          disabled
                          dense
                          class="mb-4"
                          hide-details
                        ></v-switch>
                        <v-text-field
                          v-model="onboard_data.formId"
                          label="Onboarding Form ID"
                          outlined
                          dense
                          placeholder="Enter onboarding form ID"
                          hide-details
                          class="mt-2"
                        ></v-text-field>
                        <v-text-field
                          v-model="onboard_data.buttonText"
                          label="Onboarding Button Text"
                          outlined
                          dense
                          placeholder="Enter Button Text"
                          hide-details
                          class="mt-2"
                        ></v-text-field>
                      </v-card>
                    </v-col>
                    <v-col cols="6" class="pl-6">
                      <v-card outlined class="pa-4">
                        <h4 class="text-subtitle-1 font-weight-medium mb-4">
                          Offboarding Settings
                        </h4>
                        <v-switch
                          v-model="exit_data.enabled"
                          label="Enable Offboarding"
                          :color="$vuetify.theme.currentTheme.primary"
                          dense
                          class="mb-2"
                          hide-details
                        ></v-switch>
                        <v-switch
                          v-model="exit_data.mandatory"
                          label="Mandatory Offboarding"
                          :color="$vuetify.theme.currentTheme.primary"
                          disabled
                          dense
                          class="mb-4"
                          hide-details
                        ></v-switch>
                        <v-text-field
                          v-model="exit_data.formId"
                          label="Offboarding Form ID"
                          outlined
                          dense
                          placeholder="Enter offboarding form ID"
                          hide-details
                          class="mt-2"
                        ></v-text-field>
                        <v-text-field
                          v-model="exit_data.buttonText"
                          label="Offboarding Button Text"
                          outlined
                          dense
                          placeholder="Enter Button Text"
                          hide-details
                          class="mt-2"
                        ></v-text-field>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-container>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
        <v-card-actions>
          <div class="flex justify-end gap-x-4 mb-6">
            <v-btn outlined @click="closeForm">Cancel</v-btn>
            <v-btn
              v-if="!isEditingRole"
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text mr-5"
              @click="saveNewRole"
              >Save</v-btn
            >
            <v-btn
              v-if="isEditingRole"
              :color="$vuetify.theme.currentTheme.primary"
              class="mr-5 white--text"
              @click="updateRole"
              :disabled="isUpdateButtonDisabled"
              >Update</v-btn
            >
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-data-table
      :headers="tableHeaders"
      :items="roles"
      class="pt-4 rounded-lg"
      item-key="_id"
      :key="rolesTableKey"
      :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
      :options.sync="options"
      :server-items-length="totalRoles"
      :loading="loadTable"
    >
      <template v-slot:item="{ item }">
        <tr class="py-4">
          <td class="px-4 py-5 font-semibold">{{ item.role }}</td>
          <template v-for="privilege in selectedPrivileges">
            <td :key="privilege" class="px-4 py-5 td_font">
              {{ getDynamicColumnValue(item, privilege) }}
            </td>
          </template>
          <td class="bord">
            <action-button
              :item="item"
              :canEdit="canEdit"
              :showEditButton="canEdit"
              :showDeleteButton="canDelete"
              :canDelete="canDelete"
              class="justify-end mx-5"
              :handleEdit="() => editRole(item)"
              :handleDelete="() => deleteRole(item._id)"
            />
          </td>
        </tr>
      </template>
      <template v-for="(header, index) in tableHeaders" v-slot:[`header.${header.value}`]>
        <v-tooltip :key="index" top>
          <template v-slot:activator="{ on }">
            <span v-on="on">{{ header.text }}</span>
          </template>
          <span>{{ header.text }}</span>
        </v-tooltip>
      </template>
    </v-data-table>

    <v-dialog v-model="showDeleteConfirmation" max-width="600" persistent>
      <v-card>
        <v-card-title class="headline mt-1 text-h5 justify-center text-[#6B6D78]"
          >Confirm Deletion</v-card-title
        >
        <v-card-text class="text-center mt-2">
          Are you sure you want to delete this role?
        </v-card-text>
        <v-card-actions class="flex justify-center mb-2">
          <v-btn outlined class="mr-4" @click="showDeleteConfirmation = false">
            <span class="text-black">Cancel</span>
          </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="confirmDeleteRole"
          >
            Yes, Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { hasPermission, isModuleVisible, debounce } from '@/utils/common'
import permissionData from '@/utils/permissions'
import ActionButton from '../../ActionButton'
import {
  selectAtLeastOne,
  roleCreationSuccess,
  roleCreationError,
  roleDeleteSuccess,
  roleDeleteError,
  roleUpdateSuccess,
  roleUpdateError
} from '@/utils/toastMessages'
export default {
  layout: 'Workforce/default',
  name: 'RolesComponent',
  components: {
    ActionButton
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      options: { sortBy: [], sortDesc: [], itemsPerPage: 10, page: 1 },
      totalRoles: 0,
      showFilters: false,
      roles: [],
      useCases: [],
      filterRole: '',
      panel: 0,
      roleName: '',
      showForm: false,
      loadTable: true,
      showDeleteConfirmation: false,
      roleToDeleteId: null,
      roleToEditId: null,
      isEditingRole: false,
      isFormSubmitted: false,
      originalRoleData: null,
      isUpdateButtonDisabled: true,
      selectedPrivileges: ['WORKFORCE_ADMIN_ACCESS'],
      onboard_data: { enabled: true, mandatory: false, formId: '', buttonText: '' },
      exit_data: { enabled: true, mandatory: false, formId: '', buttonText: '' },
      permissionHeaders: [
        { text: 'Permission Name', value: 'name' },
        { text: 'View', value: 'read', sortable: false, width: '12%' },
        { text: 'Create', value: 'write', sortable: false, width: '12%' },
        { text: 'Edit', value: 'edit', sortable: false, width: '12%' },
        { text: 'Delete', value: 'delete', sortable: false, width: '12%' },
        { text: 'Action', value: 'action', sortable: false, width: '15%' }
      ],
      permissions: [],
      additionalPermissions: [],
      selectedPermissionRows: [],
      rolesTableKey: 0,
      tableHeaders: [
        {
          text: 'Role Name',
          value: 'role',
          class: 'table-header',
          width: '60px'
        },
        {
          text: 'Workforce management Admin portal access',
          value: 'attendancePolicy',
          width: '20%',
          class: 'table-header',
          sortable: false
        },
        {
          text: 'Action',
          value: 'action',
          width: '20%',
          align: 'end',
          class: 'table-header',
          sortable: false
        }
      ]
    }
  },

  watch: {
    options: {
      handler() {
        this.fetchRoles()
      },
      deep: true,
      immediate: false
    },
    roleName() {
      this.checkFormChanges()
    },
    permissions: {
      handler() {
        this.checkFormChanges()
      },
      deep: true
    }
  },
  async mounted() {
    await this.populatePermissions()
  },
  methods: {
    forceRerender() {
      this.rolesTableKey += 1
    },
    resetForm() {
      this.roleName = ''
      this.permissions.forEach((permission) => {
        permission.read = false
        permission.write = false
        permission.edit = false
        permission.delete = false
      })
      this.additionalPermissions.forEach((permission) => {
        permission.checked = false
      })
    },

    isRoleModuleShouldVisible(module) {
      return isModuleVisible(module)
    },

    openForm() {
      this.isFormSubmitted = false
      this.isEditingRole = false
      this.resetForm()
      this.showForm = true
    },
    async populatePermissions() {
      try {
        const response = await this.$axios.get('/workforce/org/role/modules/v2')

        await this.getConfig()
        const availableModules = response.data.modules

        this.additionalPermissions = response.data.privileges

        if (!this.useCases.includes('inventory')) {
          delete availableModules.INVENTORY_MANAGEMENT
          delete availableModules.INVENTORY_REQUEST
        }

        this.permissions = Object.keys(availableModules).map((moduleName) => {
          return {
            key: moduleName,
            name: availableModules[moduleName],
            read: false,
            write: false,
            edit: false,
            delete: false,
            visibility: this.isRoleModuleShouldVisible(moduleName)
          }
        })
      } catch (error) {
        console.error('Error fetching available modules:', error)
      }
    },
    handleInput: debounce(async function () {
      this.options.page = 1
      await this.fetchRoles()
    }, 500),
    async fetchRoles() {
      try {
        const { sortBy, sortDesc, page, itemsPerPage } = this.options
        const params = {
          page: page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalTasks
        }

        if (sortBy.length > 0) {
          params.sortBy = sortBy[0]
        }
        if (sortDesc.length > 0) {
          params.sortOrder = sortDesc[0] ? 'desc' : 'asc'
        }
        if (this.filterRole) [(params.search = this.filterRole)]

        this.loadTable = true
        const response = await this.$axios.get('workforce/org/role/v2', {
          params: params
        })

        if (response.data.success) {
          this.roles = response.data.roles
          this.loadTable = false

          this.totalRoles = response.data.pagination?.totalCount || this.roles.length
        } else {
          console.error('Error fetching roles:', response.data.message)
        }
      } catch (error) {
        console.error('Error fetching roles:', error)
        this.loadTable = false
      }
    },

    async saveNewRole() {
      try {
        this.isFormSubmitted = true

        const isValid = !!this.roleName

        if (!isValid) {
          return
        }

        const isAnyPermissionSelected = this.permissions.some((permission) => {
          return permission.read || permission.write || permission.edit || permission.delete
        })

        if (!isAnyPermissionSelected) {
          this.$toast.error(selectAtLeastOne)
          return
        }

        const selectedPrivilege = []

        this.additionalPermissions.forEach((permission) => {
          if (permission.checked) {
            selectedPrivilege.push(`${permission.key}`)
          }
        })
        const selectedPermissions = []

        this.permissions.forEach((permission) => {
          if (permission.read) {
            selectedPermissions.push(`${permission.key}:read`)
          }
          if (permission.write) {
            selectedPermissions.push(`${permission.key}:write`)
          }
          if (permission.edit) {
            selectedPermissions.push(`${permission.key}:edit`)
          }
          if (permission.delete) {
            selectedPermissions.push(`${permission.key}:delete`)
          }
        })

        const requestBody = {
          role: this.roleName,
          permissions: selectedPermissions,
          extraPrivileges: selectedPrivilege,
          onBoardingProcess: this.onboard_data,
          offBoardingProcess: this.exit_data
        }

        const response = await this.$axios.post('workforce/org/role/v2', requestBody)

        if (response.data.success) {
          this.roles.push(response.data.role)
          this.roles.length === 1 ? this.forceRerender() : this.$toast.success(roleCreationSuccess)
          this.resetForm()
          this.showForm = false
        } else {
          this.$toast.error(roleCreationError)
          console.error(roleCreationError, response.data.message)
        }
      } catch (error) {
        this.$toast.error(roleCreationError)
        console.error(roleCreationError, error)
      }
    },

    async editRole(roleData) {
      this.originalRoleData = { ...roleData }

      this.roleName = roleData.role

      this.permissions.forEach((permission) => {
        permission.read = roleData.permissions.includes(`${permission.key}:read`)
        permission.write = roleData.permissions.includes(`${permission.key}:write`)
        permission.edit = roleData.permissions.includes(`${permission.key}:edit`)
        permission.delete = roleData.permissions.includes(`${permission.key}:delete`)
      })
      this.additionalPermissions.forEach((permission) => {
        permission.checked = roleData.extraPrivileges.includes(permission.key)
      })

      this.onboard_data = roleData.onBoardingProcess
      this.exit_data = roleData.offBoardingProcess
      this.isUpdateButtonDisabled = true

      this.isEditingRole = true
      this.roleToEditId = roleData._id

      this.showForm = true
    },

    checkFormChanges() {
      // Compare the form data with the original roleData
      if (this.originalRoleData) {
        const isRoleNameChanged = this.roleName.trim() !== this.originalRoleData.role

        const isPermissionsChanged = this.permissions.some((permission) => {
          return (
            permission.read !==
              this.originalRoleData.permissions.includes(`${permission.key}:read`) ||
            permission.write !==
              this.originalRoleData.permissions.includes(`${permission.key}:write`) ||
            permission.edit !==
              this.originalRoleData.permissions.includes(`${permission.key}:edit`) ||
            permission.delete !==
              this.originalRoleData.permissions.includes(`${permission.key}:delete`)
          )
        })

        const originalKeys = this.originalRoleData.extraPrivileges.sort() // Sort for consistent comparison
        const currentKeys = this.additionalPermissions
          .filter((permission) => permission.checked)
          .map((permission) => permission.key)
          .sort()

        const isAdditionalPermissionsChanged =
          JSON.stringify(originalKeys) !== JSON.stringify(currentKeys)

        this.isUpdateButtonDisabled = !(
          isRoleNameChanged ||
          isPermissionsChanged ||
          isAdditionalPermissionsChanged ||
          !this.onboard_data.onBoardFormId ||
          !this.exit_data.offBoardFormId
        )
      }
    },
    async updateRole() {
      try {
        this.isFormSubmitted = true

        const isValid = !!this.roleName

        if (!isValid) {
          return
        }

        const isAnyPermissionSelected = this.permissions.some((permission) => {
          return permission.read || permission.write || permission.edit || permission.delete
        })

        if (!isAnyPermissionSelected) {
          this.$toast.error(selectAtLeastOne)
          return
        }
        const selectedPrivilege = []

        this.additionalPermissions.forEach((permission) => {
          if (permission.checked) {
            selectedPrivilege.push(`${permission.key}`)
          }
        })

        const selectedPermissions = []

        this.permissions.forEach((permission) => {
          if (permission.read) {
            selectedPermissions.push(`${permission.key}:read`)
          }
          if (permission.write) {
            selectedPermissions.push(`${permission.key}:write`)
          }
          if (permission.edit) {
            selectedPermissions.push(`${permission.key}:edit`)
          }
          if (permission.delete) {
            selectedPermissions.push(`${permission.key}:delete`)
          }
        })

        const requestBody = {
          role: this.roleName,
          permissions: selectedPermissions,
          extraPrivileges: selectedPrivilege,
          onBoardingProcess: this.onboard_data,
          offBoardingProcess: this.exit_data
        }

        const response = await this.$axios.put(
          `workforce/org/role/v2/${this.roleToEditId}`,
          requestBody
        )

        if (response.data.success) {
          const updatedRoleIndex = this.roles.findIndex((role) => role._id === this.roleToEditId)
          if (updatedRoleIndex !== -1) {
            this.roles = [
              ...this.roles.slice(0, updatedRoleIndex),
              response.data.role,
              ...this.roles.slice(updatedRoleIndex + 1)
            ]
          }
          this.$toast.success(roleUpdateSuccess)
          this.resetForm()
          this.isEditingRole = false

          this.showForm = false
        } else {
          this.$toast.error(roleUpdateError)
          console.error(roleUpdateError, response.data.message)
        }
      } catch (error) {
        this.$toast.error(roleUpdateError)
        console.error(roleUpdateError, error)
      }
    },
    deleteRole(roleId) {
      this.roleToDeleteId = roleId
      this.showDeleteConfirmation = true
    },
    async confirmDeleteRole() {
      try {
        const res = await this.$axios.delete(`workforce/org/role/v2/${this.roleToDeleteId}`)
        if (res.data.success) {
          const roleIndex = this.roles.findIndex((role) => role._id === this.roleToDeleteId)
          if (roleIndex !== -1) {
            this.roles.splice(roleIndex, 1)
          }
          this.$toast.success(roleDeleteSuccess)
        } else {
          this.$toast.error(roleDeleteError)
          console.error(roleDeleteError, res.data.message)
        }
        this.showDeleteConfirmation = false
      } catch (error) {
        this.$toast.error(roleDeleteError)
        console.error(error)
      }
    },

    closeForm() {
      this.isFormSubmitted = false
      this.showForm = false
      this.resetForm()
    },
    toggleFilters() {
      this.showFilters = !this.showFilters
    },
    getDynamicColumnValue(item, privilege) {
      return item.extraPrivileges.includes(privilege) ? 'Yes' : 'No'
    },
    getWorkforceManagementAccess(role) {
      const hasAccess = role.extraPrivileges.includes('WORKFORCE_ADMIN_ACCESS')
      return hasAccess ? 'Yes' : 'No'
    },
    updateTableHeaders() {
      this.tableHeaders = [
        {
          text: 'Role Name',
          value: 'role',
          class: 'table-header',
          width: '60px'
        }
      ]

      if (this.selectedPrivileges && this.selectedPrivileges.length > 0) {
        this.selectedPrivileges.forEach((privilege) => {
          const selectedPrivilege = this.additionalPermissions.find(
            (item) => item.key === privilege
          )
          if (selectedPrivilege) {
            this.tableHeaders.push({
              text: `${
                selectedPrivilege.value.charAt(0).toUpperCase() +
                selectedPrivilege.value.slice(1).toLowerCase()
              }`,
              value: privilege,
              class: 'table-header',
              width: '20%',
              sortable: false
            })
          }
        })
      }

      this.tableHeaders.push({
        text: 'Action',
        value: 'action',
        width: '20%',
        align: 'end',
        class: 'table-header',
        sortable: false
      })
    },
    toggleSelectAll() {
      this.$nextTick(() => {
        if (this.allTaskStatusSelected) {
          this.selectedPrivileges = []
        } else {
          this.selectedPrivileges = this.additionalPermissions.map((step) => step.key)
        }

        this.updateTableHeaders()
      })
    },
    checkIsReadChecked(item) {
      if (!item.read) {
        item.write = false
        item.edit = false
        item.delete = false
      }
      return !item.read
    },
    toggleAllPermissions() {
      if (this.areAllPermissionsSelected()) {
        this.permissions.forEach((permission) => {
          permission.read = false
          permission.write = false
          permission.edit = false
          permission.delete = false
        })
      } else {
        this.permissions.forEach((permission) => {
          permission.read = true
          permission.write = true
          permission.edit = true
          permission.delete = true
        })
      }
    },
    toggleAllPrivilegePermissions() {
      if (this.allPrivilegePermissionsText == 'Deselect All') {
        this.additionalPermissions = this.additionalPermissions.map((permission) => {
          return {
            ...permission,
            checked: false
          }
        })
      } else {
        this.additionalPermissions = this.additionalPermissions.map((permission) => {
          return {
            ...permission,
            checked: true
          }
        })
      }
    },
    areAllPermissionsSelected() {
      return this.permissions.every((permission) => {
        return permission.read && permission.write && permission.edit && permission.delete
      })
    },
    toggleSelectAllPermissions(item) {
      const isSelected = this.isAllPermissionsSelected(item)

      if (isSelected) {
        this.deselectAllPermissions(item)
        this.selectedPermissionRows = this.selectedPermissionRows.filter((row) => row !== item)
      } else {
        this.selectAllPermissions(item)
        this.selectedPermissionRows.push(item)
      }
    },

    selectAllPermissions(item) {
      item.read = true
      item.write = true
      item.edit = true
      item.delete = true
    },

    deselectAllPermissions(item) {
      item.read = false
      item.write = false
      item.edit = false
      item.delete = false
    },
    isAllPermissionsSelected(item) {
      return item.read && item.write && item.edit && item.delete
    },
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config
        this.useCases = data.enabledUseCases

        return data || {}
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },

    // Method to be called from parent component (OrganizationHierarchy.vue)
    addRole() {
      this.openForm()
    }
  },
  computed: {
    allPrivilegePermissionsText() {
      const allChecked = this.additionalPermissions.every((permission) => permission.checked)
      return allChecked ? 'Deselect All' : 'Select All'
    },
    areAnyPermissionsSelected() {
      return this.permissions.some((permission) => {
        return permission.read || permission.write || permission.edit || permission.delete
      })
    },
    allPermissionsText() {
      return this.areAllPermissionsSelected() ? 'Unselect All' : 'Select All'
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.settingDelete)
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.settingEdit)
    },
    canAdd() {
      return hasPermission(this.userData, permissionData.settingWrite)
    },
    roleNameValidationRule() {
      return !this.roleName && this.isFormSubmitted ? ['Required.'] : []
    },
    allTaskStatusSelected() {
      return this.selectedPrivileges.length === this.additionalPermissions.length
    },
    someTaskStatusSelected() {
      return this.selectedPrivileges.length > 0 && !this.allTaskStatusSelected
    },
    icon() {
      if (this.allTaskStatusSelected) return 'mdi-close-box'
      if (this.someTaskStatusSelected) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },
    useCaseLeadConfig() {
      const currentUsecase = this.$route.params.usecase
      return currentUsecase
    },
    filteredPermissions() {
      return this.permissions.filter((permission) => permission.visibility)
    }
  }
}
</script>

<style scoped>
.permission-fade-enter-active,
.permission-fade-leave-active {
  transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.permission-fade-enter,
.permission-fade-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.opacity-0 {
  transition: opacity 0.3s;
  opacity: 0;
  margin-bottom: 0;
}

.appear-delay {
  transition: opacity 0.3s ease-in-out 0.1s;
}

.td_font {
  color: #262626;
  font-weight: 700;
}

.dark .td_font {
  color: #ffffff;
}
.chip-width {
  width: 65%;
  overflow: hidden;
}

.chip-text {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-header {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.bord {
  border-top: thin solid rgba(0, 0, 0, 0.12);
}

.v-input--is-disabled .v-input__control {
  opacity: 0.5;
}

.select-all-item {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}
</style>
