<template>
  <div>
    <section class="section special-filters">
      <h2 class="mb-2 text-md font-semibold">Customize dashboard search</h2>
      <p class="text-sm font-medium">
        * Manage visibility and labels for Case, Customer, and Bucket labels.
      </p>
      <v-row>
        <v-col v-for="(filter, key) in searchFilters" :key="key" cols="12" sm="4">
          <v-card outlined>
            <div class="flex justify-between">
              <v-card-title>{{ key.toUpperCase() }}</v-card-title>
              <v-switch v-model="filter.visible" label="Visibility" class="px-2"></v-switch>
            </div>

            <v-card-text>
              <v-text-field v-model="filter.label" label="Overwrite Label" dense></v-text-field>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </section>
    <section class="section regular-filters">
      <h2 class="mb-2 text-md font-semibold">Customize dashboard filters</h2>
      <p class="text-sm font-medium">
        * Customize labels, placeholders, and visibility of your filters.
      </p>
      <v-data-table
        :headers="filterHeaders"
        :items="filters"
        item-value="key"
        class="filters-table"
        hide-default-footer
      >
        <template v-slot:[`item.label`]="{ item }">
          <v-text-field
            v-model="item.label"
            dense
            outlined
            class="px-4 py-4"
            placeholder="Label"
            hide-details
          ></v-text-field>
        </template>
        <template v-slot:[`item.placeholder`]="{ item }">
          <v-text-field
            v-model="item.placeholder"
            dense
            outlined
            class="px-4 py-4 mr-8"
            placeholder="Placeholder"
            hide-details
          ></v-text-field>
        </template>
        <template v-slot:[`item.visible`]="{ item }">
          <v-switch
            v-model="item.visible"
            outlined
            class="px-4 py-4 w-8"
            inset
            hide-details
          ></v-switch>
        </template>
        <template v-slot:[`item.mainFilter`]="{ item }">
          <v-checkbox
            :input-value="item.key == mainFilter"
            @click="setMainFilter(item.key)"
            class="px-4"
            hide-details
          ></v-checkbox>
        </template>
      </v-data-table>
    </section>

    <!-- <section class="section main-filter">
      <h2 class="mb-2 text-lg font-medium">Main Filter</h2>
      <p>
        Choose the primary filter to be displayed on the dashboard homepage.
      </p>
      <v-select
        v-model="mainFilter"
        :items="
          filters.map((filter) => ({
            label: filter.label,
            value: filter.key,
          }))
        "
        label="Main Filter"
        item-text="label"
        item-value="value"
        class="main-filter-select"
      ></v-select>
    </section> -->

    <!-- <section class="section preview">
      <h2 class="mb-2 text-md font-semibold">Dashboard Preview</h2>
      <p>Here’s how your filters will look on the mobile app dashboard:</p>
      <div class="dashboard-preview">
        <p>UNDER DEVELOPMENT</p>
      </div>
    </section> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      mainFilter: this.empAppSettings.appDashboardCustomization.mainFilter || 'sortBy',
      filterHeaders: [
        {
          text: 'System Ref Key',
          value: 'key',
          align: 'center',
          sortable: false
        },
        {
          text: 'Overwrite Label',
          value: 'label',
          sortable: false
        },
        {
          text: 'Overwrite Placeholder',
          value: 'placeholder',
          sortable: false
        },
        {
          text: 'Visibility',
          value: 'visible',
          sortable: false
        },
        {
          text: 'Main Filter',
          value: 'mainFilter',
          sortable: false
        }
      ]
    }
  },
  props: {
    empAppSettings: {
      type: Object,
      default: null
    }
  },
  computed: {
    filters() {
      const defaultFilters = [
        {
          key: 'sortBy',
          label: 'Sort By',
          placeholder: 'created At',
          visible: true,
          type: 'dropdown'
        },
        {
          key: 'assignedTo',
          label: 'Assigned To',
          placeholder: 'Select Assigned To',
          visible: true,
          type: 'dropdown'
        },
        {
          key: 'subCase',
          label: 'Category',
          placeholder: 'Select Category',
          visible: true,
          type: 'dropdown'
        },
        {
          key: 'status',
          label: 'Status',
          placeholder: 'Select Status',
          visible: true,
          type: 'dropdown'
        },
        {
          key: 'outcome',
          label: 'Outcome',
          placeholder: 'Select Outcome',
          visible: true,
          type: 'dropdown'
        },
        {
          key: 'date',
          label: 'Select Date',
          placeholder: 'dd/mm/yyyy',
          visible: true,
          type: 'date'
        },
        {
          key: 'entity',
          label: 'Entity',
          placeholder: 'Select Entity',
          visible: true,
          type: 'dropdown'
        }
      ]

      const receivedFilters = this.empAppSettings?.appDashboardCustomization?.availableFilters || []

      const mergedFilters = defaultFilters.map((defaultFilter) => {
        const matchingFilter = receivedFilters.find((filter) => filter.key === defaultFilter.key)
        return matchingFilter ? { ...defaultFilter, ...matchingFilter } : defaultFilter
      })

      const extraFilters = receivedFilters.filter(
        (receivedFilter) =>
          !defaultFilters.some((defaultFilter) => defaultFilter.key === receivedFilter.key)
      )

      return [...mergedFilters, ...extraFilters]
    },

    searchFilters() {
      const searchFilterObj = this.empAppSettings.appDashboardCustomization?.searchFilters || {}
      return {
        customer: {
          label: searchFilterObj?.customer?.label || 'Customer',
          visible: searchFilterObj?.customer?.visible
        },
        case: {
          label: searchFilterObj?.case?.label || 'Case',
          visible: searchFilterObj?.case?.visible
        },
        bucket: {
          label: searchFilterObj?.bucket?.label || 'Bucket',
          visible: searchFilterObj?.bucket?.visible
        }
      }
    }
  },
  methods: {
    setMainFilter(key) {
      this.mainFilter = key
    },
    getFilterLabel(key) {
      const filter = this.filters.find((f) => f.key === key)

      return filter ? filter.label : 'N/A'
    },
    saveChanges() {
      const updatedConfig = {
        mainFilter: this.mainFilter,
        availableFilters: this.filters,
        searchFilters: this.searchFilters
      }
      return updatedConfig
    }
  }
}
</script>

<style scoped>
.section {
  margin-bottom: 20px;
}

.filters-table {
  margin-top: 20px;
  padding: 20px;
}

.actions {
  justify-content: flex-end;
  margin-top: 30px;
}

.dashboard-preview {
  border: 1px solid #ccc;
  padding: 20px;
  border-radius: 8px;
}

.filter-chip {
  display: inline-block;
  margin: 5px;
  padding: 8px 12px;
  background-color: #f1f1f1;
  border-radius: 16px;
}
</style>
