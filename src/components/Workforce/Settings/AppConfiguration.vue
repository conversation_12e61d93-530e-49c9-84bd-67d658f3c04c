<template>
  <div class="">
    <AppConfigTabs
      :empAppSettings="empAppSettings"
      :appConfigOptions="appConfigOptions"
      @update-config="updateAppConfig"
      @save-filters="saveFilterChanges"
      @custom-activity-steps="updateCustomActivitySteps"
    />
  </div>
</template>
<script>
import { orgConfigSuccess, orgConfigError } from '@/utils/toastMessages'
import AppConfigTabs from './AppConfigTabs'

export default {
  props: {
    receivedData: {
      type: Object,
      default: null
    }
  },
  components: {
    AppConfigTabs
  },
  data() {
    return {
      appConfigOptions: [
        {
          label: 'Enable clock-in / clock-out',
          deactivateLabel: 'Deactivate',
          activateLabel: 'Activate',
          enabled: false
        },
        {
          label: 'Create data',
          deactivateLabel: 'Deactivate',
          activateLabel: 'Activate',
          enabled: false
        },
        {
          label: 'Create recurring data',
          deactivateLabel: 'Deactivate',
          activateLabel: 'Activate',
          enabled: false
        },
        {
          label: 'Direct ToDo start after data creation',
          deactivate<PERSON>abel: 'Deactivate',
          activateLabel: 'Activate',
          enabled: false
        },
        {
          label: 'Employee can close the his/her assignments.',
          deactivateLabel: 'Deactivate',
          activateLabel: 'Activate',
          enabled: false
        },
        {
          label: 'Daily timeline',
          deactivateLabel: 'Deactivate',
          activateLabel: 'Activate',
          enabled: false
        }
      ],
      taskSteps: null,
      taskTypes: null,
      dashboardFilters: null,
      empAppSettings: {}
    }
  },
  methods: {
    async updateAppConfig() {
      const isCheckInAllowed = this.appConfigOptions[0].enabled
      const isTaskCreationAllowed = this.appConfigOptions[1].enabled
      const isTaskRecurrenceAllowed = this.appConfigOptions[2].enabled
      const isClosingCaseAllowed = this.appConfigOptions[3].enabled
      const isDirectVisitStartAllowed = this.appConfigOptions[4].enabled
      if (this.empAppSettings.enabledType === 'CUSTOM') {
        this.taskSteps = this.empAppSettings.taskCompletionSteps
        this.taskTypes = 'CUSTOM'
      } else {
        this.taskSteps = this.empAppSettings.taskCompletionDefaultSteps
        this.taskTypes = 'DEFAULT'
      }
      const requestBody = {
        isCheckInAllowed,
        isTaskCreationAllowed,
        isTaskRecurrenceAllowed,
        isClosingCaseAllowed,
        isDirectVisitStartAllowed,
        taskCompletionStepsConfig: this.taskSteps,
        type: this.taskTypes,
        stepperSteps: this.stepperConfig,
        clockInWithTimeline: this.appConfigOptions[5].enabled,
        customActivitySteps: this.empAppSettings.customActivitySteps || []
      }
      await this.postConfig(requestBody)
    },
    async getEmpAppSettings() {
      try {
        const { data } = await this.$axios.get('/workforce/orgSetup/empapp-settings')
        if (data.empApp) {
          this.empAppSettings = data.empApp
        }
      } catch (error) {
        console.log(error)
        this.$toast.error('Failed to fetch employee application settings')
      }
    },
    async postConfig(data) {
      try {
        await this.$axios
          .post('/workforce/orgSetup/empapp-settings', data)
          .then(() => {
            this.$toast.success(orgConfigSuccess)
          })
          .catch((error) => {
            this.$toast.error(orgConfigError)
            console.log(error)
          })
      } catch (error) {
        console.log(error)
      }
    },
    async updateCustomActivitySteps(updatedSteps) {
      if (!updatedSteps) {
        this.$toast.error('No custom activity steps provided')
        return
      }
      try {
        this.empAppSettings.customActivitySteps = updatedSteps
        const requestBody = {
          customActivitySteps: updatedSteps
        }
        await this.postConfig(requestBody)
      } catch (error) {
        console.log(error)
        this.$toast.error('Failed to update custom activity steps')
      }
    },
    async getData() {
      this.appConfigOptions[0].enabled = this.empAppSettings.isCheckInAllowed
      this.appConfigOptions[1].enabled = this.empAppSettings.isTaskCreationAllowed
      this.appConfigOptions[2].enabled = this.empAppSettings.isTaskRecurrenceAllowed
      this.appConfigOptions[3].enabled = this.empAppSettings.isClosingCaseAllowed
      this.appConfigOptions[4].enabled = this.empAppSettings.isDirectVisitStartAllowed
      this.appConfigOptions[5].enabled = this.empAppSettings.clockInWithTimeline
      if (this.empAppSettings?.stepperSteps) {
        this.stepperConfig = this.empAppSettings?.stepperSteps
      }
    },
    async saveFilterChanges(val) {
      const data = {
        appDashboardCustomization: val
      }
      await this.postConfig(data)
    },
    formatStepName(step) {
      return step
        .replace(/([A-Z])/g, ' $1')
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    },
    isDisableAllowed(step) {
      return ['caseType', 'taskTitle', 'caseStartAndEndTime'].includes(step)
    }
  },
  computed: {
    settingLanguage() {
      const Cases = this.receivedData?.enabledUseCases
      if (Cases && Cases.includes('inventory') && Cases.length > 1) {
        return 'Task/Complaint'
      } else if (Cases && Cases.includes('inventory')) {
        return 'Complaint'
      } else {
        return 'Task'
      }
    }
  },
  async mounted() {
    await this.getEmpAppSettings()
    await this.getData()
  }
}
</script>
<style scoped>
.v-switch {
  margin: 0;
  padding: 0;
  justify-content: center;
}
</style>
