<template>
  <div>
    <div class="dashboard">
      <!-- Loading state -->
      <div v-if="isLoading" class="text-center py-4">
        <v-progress-circular indeterminate color="primary"></v-progress-circular>
        <p class="mt-2">Loading dashboard settings...</p>
      </div>

      <!-- Main content -->
      <div v-else>
        <DraggableWrapper :items="visibleSections" @reordered="updateSectionOrder">
          <template v-slot:default="{ item }">
            <div class="section">
              <div class="header flex justify-between items-center">
                <div class="flex-grow">
                  <div class="flex items-center">
                    <h2 class="font-semibold mr-2">{{ getSectionTitle(item).title }}</h2>
                    <p class="mt-4 text-sm">-> {{ getSectionTitle(item).description }}</p>
                  </div>
                </div>
                <div class="flex-shrink-0">
                  <v-switch
                    v-model="sectionVisibility[item]"
                    inset
                    @change="toggleSwitch"
                  ></v-switch>
                </div>
              </div>
            </div>
          </template>
        </DraggableWrapper>
      </div>
    </div>
  </div>
</template>

<script>
import DraggableWrapper from '@/components/common/DraggableWrapper.vue'
import { isModuleVisible, isModuleFeatureAllowed } from '@/utils/common'

export default {
  data() {
    return {
      sectionVisibility: {
        employeeStats: true,
        progressStats: true,
        caseAnalyticsStats: true,
        visitAnalyticsStats: true,
        visitCollectionStats: true,
        teleCallingStats: true
      },
      adminDashboardSettings: {},
      isLoading: false
    }
  },
  components: {
    DraggableWrapper
  },
  computed: {
    visibleSections() {
      if (!this.sectionVisibility) return []

      return Object.keys(this.sectionVisibility).filter((key) => {
        const sectionTitle = this.getSectionTitle(key)
        return sectionTitle !== null
      })
    }
  },
  receivedData: {
    type: Object,
    default: null
  },
  async created() {
    await this.getConfig()
    await this.loadSectionVisibility()
  },
  methods: {
    updateSectionOrder(newOrder) {
      if (Array.isArray(newOrder)) {
        // Create new object with reordered keys
        const reorderedSections = {}
        newOrder.forEach((key) => {
          if (this.sectionVisibility && key in this.sectionVisibility) {
            reorderedSections[key] = this.sectionVisibility[key]
          }
        })
        this.sectionVisibility = reorderedSections
      }
    },

    async loadSectionVisibility() {
      try {
        // Ensure we have default values even if API returns null/undefined
        const defaultSections = {
          employeeStats: true,
          progressStats: true,
          caseAnalyticsStats: true,
          visitAnalyticsStats: true,
          visitCollectionStats: true,
          teleCallingStats: true
        }

        if (
          this.adminDashboardSettings &&
          this.adminDashboardSettings.adminDashboardCustomization &&
          typeof this.adminDashboardSettings.adminDashboardCustomization === 'object'
        ) {
          // Merge API data with defaults to ensure all sections exist
          this.sectionVisibility = {
            ...defaultSections,
            ...this.adminDashboardSettings.adminDashboardCustomization
          }
        } else {
          // Use defaults if no valid data from API
          this.sectionVisibility = defaultSections
        }
      } catch (error) {
        console.error('Error loading section visibility:', error)
        // Fallback to defaults on error
        this.sectionVisibility = {
          employeeStats: true,
          progressStats: true,
          caseAnalyticsStats: true,
          visitAnalyticsStats: true,
          visitCollectionStats: true,
          teleCallingStats: true,
          loanMangmentStats: true
        }
      }
    },
    moduleEnabled(module, feature) {
      if (feature) {
        return isModuleFeatureAllowed(module, feature)
      }
      return isModuleVisible(module)
    },
    getSectionTitle(key) {
      const titles = {
        employeeStats: {
          title: 'Employee Stats',
          description:
            'Total number of active employees, the number of employees clocked in today, and the number of employees not clocked in today.',
          visibility: true
        },
        progressStats: {
          title: 'Progress Stats',
          description:
            'Overview of the number of PENDING, IN-PROGRESS, and COMPLETED cases, visits, or tasks.',
          visibility: true
        },
        caseAnalyticsStats: {
          title: 'Case Analytics',
          description:
            'Bar graph representation of cases categorized by user-defined case outcomes.',
          visibility: this.moduleEnabled('COLLECTION_MANAGEMENT')
        },
        visitAnalyticsStats: {
          title: 'Visit Analytics',
          description:
            'Bar graph representation of visits categorized as PENDING, IN-PROGRESS, or COMPLETED.',
          visibility: this.moduleEnabled('COLLECTION_MANAGEMENT')
        },
        visitCollectionStats: {
          title: 'Visit Collection',
          description: 'Card view displaying the top five most collected visits.',
          visibility: this.moduleEnabled('COLLECTION_MANAGEMENT')
        },
        teleCallingStats: {
          title: 'Tele Calling',
          description:
            'Bar graph representation of calls categorized as TOTAL, RECEIVED BY AGENT, or RECEIVED BY CUSTOMER.',
          visibility: this.moduleEnabled('ADDONS', 'CALL_A_CUSTOMER')
        },
        loanStats: {
          title: 'Loan Stats',
          description: 'Overview of active loans, total loan amount, and loan status distribution.',
          visibility: true
        },
        emiStats: {
          title: 'Emi Stats',
          description: 'Summary of EMI payments including pending, overdue, and completed EMIs.',
          visibility: true
        },
        recentEmis: {
          title: 'Recent EMIs',
          description: 'List of most recent EMI transactions with payment status and details.',
          visibility: true
        },
        emiActions: {
          title: 'EMI Actions',
          description:
            'Quick actions for managing EMIs including payment processing and reminders.',
          visibility: true
        }
      }

      if (titles[key] && titles[key].visibility) {
        return {
          title: titles[key].title,
          description: titles[key].description
        }
      }

      return null
    },

    async toggleSwitch() {
      try {
        // Ensure sectionVisibility is valid before sending
        if (!this.sectionVisibility || typeof this.sectionVisibility !== 'object') {
          this.$toast.error('Invalid section visibility data')
          return
        }

        const payload = {
          adminDashboardCustomization: this.sectionVisibility
        }

        await this.$axios
          .post('/workforce/orgSetup/admin-dashboard-settings', payload)
          .then(() => {
            this.$toast.success('Dashboard visibility changed successfully')
          })
          .catch((error) => {
            console.error('Error updating dashboard settings:', error)
            this.$toast.error('Failed to update dashboard settings')
          })
      } catch (error) {
        console.error('Error in toggleSwitch:', error)
        this.$toast.error('An error occurred while updating settings')
      }
    },

    async getConfig() {
      try {
        this.isLoading = true
        const response = await this.$axios.get('/workforce/orgSetup/admin-dashboard-settings')

        // Handle various response structures safely
        if (response && response.data) {
          if (response.data.dashboard) {
            this.adminDashboardSettings = response.data.dashboard
          } else if (response.data.data) {
            this.adminDashboardSettings = response.data.data
          } else {
            // If response structure is different, use the response data directly
            this.adminDashboardSettings = response.data
          }
        } else {
          // No valid response, use empty object
          this.adminDashboardSettings = {}
        }
      } catch (error) {
        console.error('Error fetching config:', error)
        this.$toast.error('Failed to load dashboard configuration')
        // Set empty object to prevent further errors
        this.adminDashboardSettings = {}
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style scoped>
/* .dashboard {
  display: flex;
  flex-direction: column;
  gap: 15px;
} */
.section {
  padding: 5px 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background: #f9f9f9;
}
/* .header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
} */
</style>
