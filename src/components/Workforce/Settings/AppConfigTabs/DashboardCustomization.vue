<template>
  <div class="mt-5 p-3">
    <v-row class="flex justify-between">
      <p class="font-semibold mb-0">Dashboard Customization</p>
      <v-btn color="primary" @click="saveChanges" small>
        <v-icon left>mdi-content-save</v-icon>
        Save
      </v-btn>
    </v-row>

    <!-- Tabs -->
    <div class="flex border-b mt-4">
      <button
        :class="[
          'px-6 py-3 font-medium',
          activeTab === 'filters'
            ? 'text-primary border-b-2 border-primary'
            : 'text-gray-500 hover:text-gray-700'
        ]"
        @click="activeTab = 'filters'"
      >
        Dashboard Filters
      </button>
      <button
        :class="[
          'px-6 py-3 font-medium',
          activeTab === 'actions'
            ? 'text-primary border-b-2 border-primary'
            : 'text-gray-500 hover:text-gray-700'
        ]"
        @click="activeTab = 'actions'"
      >
        Action Buttons
      </button>
    </div>

    <!-- Dashboard Filters Tab -->
    <div v-if="activeTab === 'filters'" class="mt-4">
      <AppFilterCustomization ref="dashboardFilters" :empAppSettings="empAppSettings" />
    </div>

    <!-- Action Buttons Tab -->
    <div v-if="activeTab === 'actions'" class="mt-4">
      <CustomActionPoints
        :empAppSettings="empAppSettings"
        @custom-activity-steps="handleCustomActivitySteps"
        ref="customActions"
      />
    </div>
  </div>
</template>

<script>
import AppFilterCustomization from '../AppFilterCustomization'
import CustomActionPoints from './CustomActionPoints'

export default {
  components: {
    AppFilterCustomization,
    CustomActionPoints
  },
  props: {
    empAppSettings: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      activeTab: 'filters'
    }
  },
  methods: {
    saveChanges() {
      if (this.activeTab === 'filters') {
        const filterConfig = this.$refs.dashboardFilters.saveChanges()
        this.$emit('save-filters', filterConfig)
      } else {
        this.$refs.customActions.saveSettings()
        console.log('hjgjhgjhg')
      }
    },
    handleCustomActivitySteps(steps) {
      this.$emit('custom-activity-steps', steps)
    }
  }
}
</script>

<style scoped>
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
