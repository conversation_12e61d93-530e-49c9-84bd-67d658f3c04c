<template>
  <div>
    <v-tabs
      v-model="activeTab"
      background-color="transparent"
      show-arrows
      vertical
      left
      :color="$vuetify.theme.currentTheme.primary"
    >
      <v-tab :ripple="false" v-for="tab in configTabs" :key="tab.name" class="tabsClass w-full">
        <div class="flex flex-col items-start">
          <span>
            {{ tab.label }}
          </span>
          <span v-show="tab.subHeading" class="text-xs mt-1 text-gray-600">
            ({{ tab.subHeading }})
          </span>
        </div>
      </v-tab>

      <v-tab-item v-for="tab in configTabs" :key="tab.name">
        <div class="pl-4">
          <component
            :is="tab.component"
            :empAppSettings="empAppSettings"
            :appConfigOptions="appConfigOptions"
            @update-config="$emit('update-config')"
            @save-filters="$emit('save-filters', $event)"
            @custom-activity-steps="$emit('custom-activity-steps', $event)"
          />
        </div>
      </v-tab-item>
    </v-tabs>
  </div>
</template>

<script>
import GeneralSettings from './GeneralSettings.vue'
import DashboardCustomization from './DashboardCustomization.vue'
import StepperConfiguration from './StepperConfiguration.vue'
import OrgRepository from '../OrgConfigComponents/OrgUploadedDocs.vue'

export default {
  name: 'AppConfigTabsPanel',
  components: {
    GeneralSettings,
    DashboardCustomization,
    StepperConfiguration,
    OrgRepository
  },
  props: {
    empAppSettings: {
      type: Object,
      default: null
    },
    appConfigOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeTab: 0,
      configTabs: [
        {
          name: 'generalSettings',
          label: 'General settings',
          subHeading: '',
          component: GeneralSettings
        },
        {
          name: 'stepperConfiguration',
          label: 'Data creation flow',
          // subHeading: 'Lead/Task',
          //subheading should be dynamic based on the plan and license
          component: StepperConfiguration
        },
        {
          name: 'dashboardCustomization',
          label: 'Dashboard customization',
          subHeading: '',
          component: DashboardCustomization
        },
        {
          name: 'orgRepository',
          label: 'Media repository',
          component: OrgRepository
        }
      ]
    }
  }
}
</script>
<style scoped>
.tabsClass {
  text-transform: capitalize !important;
  display: flex;
  align-self: flex-start;
  justify-content: flex-start;
}
</style>
