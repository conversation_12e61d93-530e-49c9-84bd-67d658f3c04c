<template>
  <div class="mt-5">
    <p class="font-semibold mb-0">Configure case and task creation flow</p>
    <v-simple-table>
      <template v-slot:default>
        <thead>
          <tr>
            <th>Step</th>
            <th>Required</th>
            <th>Visible</th>
            <th>Skippable</th>
            <th>Disabled</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(config, step) in stepperConfig" :key="step">
            <td class="px-4">{{ formatStepName(step) }}</td>
            <td class="px-4">
              <v-switch
                :ripple="false"
                dense
                v-model="config.required"
                @change="updateConfig"
              ></v-switch>
            </td>
            <td class="px-4">
              <v-switch
                :ripple="false"
                dense
                v-model="config.visibility"
                @change="updateConfig"
              ></v-switch>
            </td>
            <td class="px-4">
              <v-switch
                :ripple="false"
                dense
                v-model="config.skip"
                @change="updateConfig"
              ></v-switch>
            </td>
            <td class="px-4">
              <v-switch
                :ripple="false"
                dense
                v-model="config.disabled"
                :disabled="!isDisableAllowed(step)"
                @change="updateConfig"
              ></v-switch>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
  </div>
</template>

<script>
export default {
  props: {
    empAppSettings: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      stepperConfig: {
        caseType: { required: true, visibility: true, skip: false, disabled: false },
        addOrSelectCustomer: { required: true, visibility: true, skip: false, disabled: false },
        customerAddress: { required: true, visibility: true, skip: false, disabled: false },
        taskTitle: { required: true, visibility: true, skip: false, disabled: false },
        caseStartAndEndTime: { required: true, visibility: true, skip: false, disabled: false }
      }
    }
  },
  methods: {
    updateConfig() {
      this.$emit('update-config')
    },
    formatStepName(step) {
      return step
        .replace(/([A-Z])/g, ' $1')
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    },
    isDisableAllowed(step) {
      return ['caseType', 'taskTitle', 'caseStartAndEndTime'].includes(step)
    }
  },
  mounted() {
    if (this.empAppSettings?.stepperSteps) {
      this.stepperConfig = this.empAppSettings.stepperSteps
    }
  }
}
</script>

<style scoped>
.v-switch {
  margin: 0;
  padding: 0;
  justify-content: center;
}
</style>
