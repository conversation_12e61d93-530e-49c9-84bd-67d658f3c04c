<template>
  <div class="mt-5">
    <p class="font-semibold mb-0">Settings</p>
    <div
      class="flex align-center space-x-4 border border-blue-200 bg-white dark-bg-custom px-6 my-3"
      v-for="(configOption, index) in appConfigOptions"
      :key="index"
    >
      <div>{{ configOption.label }}</div>
      <div class="flex align-center justify-end">
        <v-label>{{ configOption.deactivateLabel }}</v-label>
        <v-switch
          :ripple="false"
          class="ml-5 mb-1"
          inset
          dense
          :label="configOption.activateLabel"
          v-model="configOption.enabled"
          @change="updateConfig"
        ></v-switch>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    empAppSettings: {
      type: Object,
      default: null
    },
    appConfigOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  methods: {
    updateConfig() {
      this.$emit('update-config')
    }
  }
}
</script>
