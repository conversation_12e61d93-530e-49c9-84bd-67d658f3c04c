<template>
  <div>
    <div class="flex justify-between items-center">
      <p class="font-semibold mb-0">Action Button Customization</p>
    </div>

    <div class="bg-white">
      <div class="py-6">
        <div class="mb-6">
          <h2 class="font-semibold mb-4 text-gray-800">Button Type</h2>
          <div class="flex space-x-4">
            <button
              :class="[
                'px-4 py-2 rounded-lg border font-medium',
                currentButtonType === 'single'
                  ? 'bg-blue-100 text-blue-700 border-blue-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300'
              ]"
              @click="currentButtonType = 'single'"
            >
              Single Button
            </button>
            <button
              :class="[
                'px-4 py-2 rounded-lg border font-medium',
                currentButtonType === 'toggle'
                  ? 'bg-blue-100 text-blue-700 border-blue-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300'
              ]"
              @click="currentButtonType = 'toggle'"
            >
              Toggle Button
            </button>
          </div>
        </div>

        <div v-if="currentButtonType === 'single'" class="space-y-4 fade-in">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Button Name</label>
            <v-text-field
              v-model="singleBtn.step"
              outlined
              dense
              placeholder="e.g. Submit, Download"
              hide-details
            ></v-text-field>
          </div>
          <div class="flex items-center justify-between">
            <v-switch v-model="singleBtn.enabled" label="Enabled" color="primary"></v-switch>
            <v-btn color="primary" class="mt-4 white--text" @click="addSingleButton" small outlined>
              Add Single Button
            </v-btn>
          </div>
        </div>

        <div v-if="currentButtonType === 'toggle'" class="space-y-4 fade-in">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Toggle Name</label>
            <v-text-field
              v-model="toggleBtn.name"
              outlined
              dense
              placeholder="e.g. Dark Mode, Notifications"
              hide-details
            ></v-text-field>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">On State Label</label>
              <v-text-field
                v-model="toggleBtn.onLabel"
                outlined
                dense
                placeholder="e.g. On, Enabled"
                hide-details
              ></v-text-field>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Off State Label</label>
              <v-text-field
                v-model="toggleBtn.offLabel"
                outlined
                dense
                placeholder="e.g. Off, Disabled"
                hide-details
              ></v-text-field>
            </div>
          </div>
          <div class="flex items-center justify-between">
            <v-switch
              v-model="toggleBtn.defaultState"
              label="Default State (On)"
              color="primary"
            ></v-switch>
            <v-btn color="primary" class="mt-4 white--text" @click="addToggleButton" small outlined>
              Add Toggle Button
            </v-btn>
          </div>
        </div>
      </div>

      <div class="py-6 border-t border-gray-200">
        <div class="mb-4 flex justify-between items-center">
          <h2 class="font-semibold text-gray-800">Your Buttons</h2>
          <div class="text-sm text-gray-500">
            <span>{{ buttons.length }}</span> buttons created
          </div>
        </div>

        <div class="space-y-3">
          <div v-if="buttons.length === 0" class="text-center py-8 text-gray-500">
            No buttons created yet. Start by adding some buttons!
          </div>

          <div
            v-for="button in buttons"
            :key="button._id"
            class="bg-gray-50 rounded-lg p-4 border border-gray-200 slide-in"
          >
            <div class="flex justify-between items-start mb-2">
              <div>
                <h3 class="font-medium text-gray-800">{{ button.step }}</h3>
                <p class="text-sm mb-0 text-gray-500">Action Button</p>
              </div>
              <div class="flex justify-end space-x-2">
                <v-btn icon small color="primary" @click="editButton(button._id)">
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn icon small color="error" @click="confirmDelete(button._id)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </div>
            </div>

            <div class="flex items-center justify-between mt-2">
              <span :class="['text-sm', button.enabled ? 'text-green-600' : 'text-gray-500']">
                {{ button.enabled ? 'Enabled' : 'Disabled' }}
              </span>
              <v-btn :disabled="!button.enabled" small outlined color="primary">
                {{ button.step }}
              </v-btn>
            </div>
          </div>
        </div>
      </div>
    </div>

    <v-dialog v-model="deleteDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">Delete Button</v-card-title>
        <v-card-text>Are you sure you want to delete this button?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialog = false">Cancel</v-btn>
          <v-btn color="error" text @click="deleteButton">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'ButtonSettings',
  props: {
    empAppSettings: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      currentButtonType: 'single',
      buttons: [],
      singleBtn: {
        step: '',
        enabled: true
      },
      toggleBtn: {
        name: '',
        onLabel: 'On',
        offLabel: 'Off',
        defaultState: false
      },
      toastMessage: '',
      deleteDialog: false,
      buttonToDelete: null
    }
  },
  mounted() {
    this.loadButtons()
  },
  methods: {
    loadButtons() {
      this.buttons = this.empAppSettings.customActivitySteps || []
    },
    saveSettings() {
      this.$emit('custom-activity-steps', this.buttons)
      this.$toast.success('Settings saved successfully!')
    },
    addSingleButton() {
      if (!this.singleBtn.step.trim()) {
        this.$toast.error('Please enter a button name')
        return
      }

      const newButton = {
        step: this.singleBtn.step.trim(),
        enabled: this.singleBtn.enabled
      }

      this.buttons.push(newButton)
      this.singleBtn.step = ''
    },
    editButton(id) {
      const button = this.buttons.find((b) => b._id === id)
      if (!button) return

      this.currentButtonType = 'single'
      this.singleBtn.step = button.step
      this.singleBtn.enabled = button.enabled

      this.buttons = this.buttons.filter((b) => b._id !== id)
    },
    confirmDelete(id) {
      this.buttonToDelete = id
      this.deleteDialog = true
    },
    deleteButton() {
      this.buttons = this.buttons.filter((b) => b._id !== this.buttonToDelete)
      this.$toast.success('Button deleted!')
      this.deleteDialog = false
      this.buttonToDelete = null
    }
  }
}
</script>

<style scoped>
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.slide-in {
  animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
