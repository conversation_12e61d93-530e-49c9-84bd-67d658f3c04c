<template>
  <v-dialog
    v-model="dialog"
    width="800"
    origin="top center"
    persistent
    class="dialogOverflow"
  >
    <v-card class="bg-white dark-bg-custom px-8 pb-6">
      <div class="flex justify-between items-center btnClass">
        <p class="text-md mb-0">
          <span class="font-semibold">Add/Edit Geofencing</span>
        </p>
        <v-btn icon @click="handleClose" :color="$vuetify.theme.currentTheme.primary">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </div>
      <div ref="map" style="width: 100%; height: 500px"></div>
      <CommonAutocomplete
        class="mt-4"
        :addressLine1="addressLine1"
        :branch="branches"
        v-model="selectedAddress"
        @selected-place="handleSelectedPlace"
      />
      <v-slider
        thumb-label="always"
        tick-size="4"
        v-model="geofenceData.radius"
        :min="200"
        :max="4000"
        label="Radius (meters)"
        @input="updateRadius"
        :disabled="!isEnable"
        hide-details
        class="mt-9"
      />
      <div class="flex justify-end">
        <v-btn
          @click="clearFields"
          outlined
          :disabled="!isEnable"
          class="white--text mr-4"
          >Clear</v-btn
        >
        <v-btn @click="handleSave" :color="$vuetify.theme.currentTheme.primary" class="white--text"
          >Save</v-btn
        >
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
/* global google */
import CommonAutocomplete from "~/components/CommonAutoComplete.vue";

export default {
  props: {
    dialog: {
      type: Boolean,
      required: true,
    },
    value: {
      type: Object,
      required: true,
      default: null,
    },
  },
  components: {
    CommonAutocomplete,
  },
  data() {
    return {
      map: null,
      center: { lat: 10.0, lng: 10.0 },
      zoom: 10,
      markers: [],
      circle: null,
      radius: 200,
      googleMapsLoaded: false,
      isEnable: false,
      geofenceData: { radius: 200 },
      geoAddress: "",
      addLine1: "",
      branches: [],
      addLine2: "",
      latitude: "",
      longitude: "",
      city: "",
      state: "",
      country: "",
      pinCode: "",
      addressLine1: "",
      selectedAddress: "",
      placesServiceData: null,
    };
  },

  mounted() {
    this.geofenceData = this.$props?.value;
    this.addressLine1 = this.geofenceData?.formatted_address;
    this.branches.push({ label: this.geofenceData?.formatted_address });
    this.loadGoogleMapsScript();
  },

  methods: {
    handleSelectedPlace(place) {
      this.geofenceData.location = place.location;
      this.selectedAddress = place.formatted_address;
      // Update map and marker
      if (place.location) {
        this.placeMarker(place.location, place.formatted_address);
        this.isEnable = true;
      }
    },
    handleClose() {
      this.$emit("mapClose", false);
    },
    async loadGoogleMapsScript() {
      const scriptTag = document.querySelector(
        'script[src*="maps.googleapis.com"]'
      );
      if (!scriptTag) {
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.VUE_APP_MAP_KEY}&libraries=places&callback=Function.prototype`;
        script.defer = true;
        script.async = true;
        script.onload = this.initMap; // Initialize the map after the script is loaded
        document.head.appendChild(script);
      } else {
        setTimeout(() => {
          this.initMap();
        }, 1000);
      }
    },
    async initMap() {
      this.map = new google.maps.Map(this.$refs.map, {
        center: this.center,
        zoom: this.zoom,
      });

      if (this.geofenceData?.location?.lat && this.geofenceData?.location?.lng) {
        this.placeMarker(
          this.geofenceData?.location,
          this.geofenceData?.formatted_address
        );
        this.isEnable = true;
      }

      google.maps.event.clearListeners(this.map, "click");

      this.placesServiceData = new google.maps.places.PlacesService(
        document.createElement("div")
      );
    },
    placeMarker(location, address) {
      if (!location) return;
      const marker = new google.maps.Marker({
        position: location,
        map: this.map,
      });
      this.markers.push(marker);
      const infowindowContent = `
        <div class="flex no-close-button-on-info-windows">
          <div>                               
            <div class="ml-1">
              <strong>${address}</strong>
            <div>
          </div>
        </div>
      `;

      const infowindow = new google.maps.InfoWindow({
        content: infowindowContent,
      });
      infowindow.open(this.map, marker);
      this.updateCircleCenter(location);
    },
    clearMarkers() {
      this.markers.forEach((marker) => {
        marker.setMap(null);
      });
      this.markers = [];
      if (this.circle) {
        this.circle.setMap(null);
        this.circle = null;
      }
    },

    updateRadius(value) {
      this.geofenceData.radius = value;
      if (this.circle) {
        this.circle.setRadius(this.geofenceData?.radius);
        this.map.fitBounds(this.circle.getBounds());
      }
    },
    updateCircleCenter(location) {
      if (this.circle) {
        this.circle.setCenter(location);
      } else {
        this.circle = new google.maps.Circle({
          strokeColor: "#2A83FF",
          strokeOpacity: 0.8,
          strokeWeight: 2,
          fillColor: "#2A83FF",
          fillOpacity: 0.35,
          map: this.map,
          center: location,
          radius: this.geofenceData.radius || 200,
        });
      }
      this.map.fitBounds(this.circle.getBounds());
    },
    handleSave() {
      if (
        !this.selectedAddress &&
        !this.geofenceData.radius &&
        !this.geofenceData.location
      ) {
        this.$emit("handleGeofence", {});
      } else {
        this.geofenceData.formatted_address = this.selectedAddress;
        this.$emit("handleGeofence", this.geofenceData);
      }
      this.handleClose();
    },
    clearFields() {
      this.geofenceData = {};
      this.branches = [];
      this.clearMarkers();
      this.isEnable = false;
    },
  },
  computed: {
    rules() {
      return {
        geoAddress: [(val) => (val || "").length > 0 || "Address is required"],
      };
    },
  },
};
</script>

<style scoped>
.gm-style-iw button[title="Close"] {
  display: none !important;
}
</style>
