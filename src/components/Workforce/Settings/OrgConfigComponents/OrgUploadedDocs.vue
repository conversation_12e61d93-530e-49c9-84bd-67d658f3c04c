<template>
  <div class="bg-gray-50 min-h-screen">
    <v-container class="px-4 py-8">
      <!-- No Repository State -->
      <div v-if="!hasRepository">
        <NoData
          title="No Repository Found"
          subTitle="Create a repository to organize and manage your content"
          btnText="Create Repository"
          :btnAction="openCreateRepoDialog"
        />
      </div>

      <!-- Repository Exists -->
      <v-row v-else>
        <!-- Left: Uploaded Files -->
        <v-col cols="12" :md="orgRepo.categories && orgRepo.categories.length > 0 ? '7' : '12'">
          <v-card class="rounded-xl shadow-md p-6">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-md font-semibold text-gray-800">
                {{ orgRepo.name || 'Your Repository' }}
              </h2>
              <div class="flex justify-end items-center gap-2">
                <v-btn color="primary" text @click="handleAddButtonClick">
                  <v-icon left>mdi-plus</v-icon>
                  {{
                    !orgRepo.categories || orgRepo.categories.length === 0
                      ? 'Add Content'
                      : 'Add Category'
                  }}
                </v-btn>
                <v-btn icon @click="showRepoSettingsDialog = true" title="Repository Settings">
                  <v-icon color="grey darken-1">mdi-cog</v-icon>
                </v-btn>
              </div>
            </div>
            <!-- No categories message -->
            <div
              v-if="!orgRepo.categories || orgRepo.categories.length === 0"
              class="text-center py-12"
            >
              <v-icon size="64" color="grey lighten-1">mdi-folder-open</v-icon>
              <h3 class="text-xl font-medium text-gray-700 mb-2 mt-4">
                No media categories created yet
              </h3>
              <p class="text-gray-500">Create media categories to organize your media content</p>
            </div>

            <!-- Categories with dividers -->
            <div v-else class="categories-container">
              <div
                v-for="(category, categoryIndex) in orgRepo.categories"
                :key="categoryIndex"
                class="category-section mb-6"
              >
                <!-- Category header with right-aligned actions -->
                <div class="flex justify-between items-center mb-2">
                  <h3 class="text-lg font-medium text-gray-800">{{ category.categoryName }}</h3>
                  <div class="flex justify-end">
                    <v-btn icon small @click="editCategory(categoryIndex)" class="mr-1">
                      <v-icon color="blue">mdi-pencil</v-icon>
                    </v-btn>
                    <v-btn icon small @click="deleteCategory(categoryIndex)">
                      <v-icon color="red">mdi-delete</v-icon>
                    </v-btn>
                  </div>
                </div>

                <!-- Divider -->
                <v-divider class="mb-4"></v-divider>

                <!-- Category content -->
                <div v-if="category.content && category.content.length > 0">
                  <v-row>
                    <v-col
                      v-for="(item, itemIndex) in category.content"
                      :key="itemIndex"
                      cols="12"
                      sm="6"
                    >
                      <v-card class="file-card overflow-hidden">
                        <!-- Image Preview -->
                        <div
                          v-if="item.type === 'org-images'"
                          class="h-48 bg-gray-100 flex items-center justify-center overflow-hidden"
                        >
                          <v-img
                            :src="getFullUrl(item.link)"
                            :alt="item.title"
                            height="100%"
                            contain
                            @click="openInNewTab(item.link)"
                            class="cursor-pointer"
                          ></v-img>
                        </div>
                        <!-- YouTube Preview -->
                        <div v-else-if="item.type === 'youtube-link'" class="video-container">
                          <iframe
                            :src="getYoutubeEmbedUrl(item.link)"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                          ></iframe>
                        </div>
                        <!-- Document Preview -->
                        <div
                          v-else-if="item.type === 'org-docs'"
                          class="h-48 bg-gray-100 flex items-center justify-center cursor-pointer"
                          @click="openInNewTab(item.link)"
                        >
                          <div class="text-center p-4">
                            <v-icon size="64" color="blue">mdi-file-document</v-icon>
                            <p class="text-gray-700 truncate mt-2">{{ item.title }}</p>
                          </div>
                        </div>

                        <!-- Content Info -->
                        <v-card-text class="p-4">
                          <div class="flex justify-between items-start">
                            <div>
                              <h3
                                class="font-medium text-gray-800 mb-1 truncate"
                                :title="item.title"
                              >
                                {{ item.title }}
                              </h3>
                              <p class="text-sm text-gray-500">{{ item.type }}</p>
                            </div>
                            <div class="flex justify-end">
                              <v-btn icon small @click="copyItemLink(item)" title="Copy Link">
                                <v-icon small>mdi-link</v-icon>
                              </v-btn>
                              <v-btn
                                icon
                                small
                                @click="editContent(categoryIndex, itemIndex)"
                                title="Edit"
                              >
                                <v-icon small color="blue">mdi-pencil</v-icon>
                              </v-btn>
                              <v-btn
                                icon
                                small
                                @click="deleteContent(categoryIndex, itemIndex)"
                                title="Delete"
                              >
                                <v-icon small color="red lighten-1">mdi-delete</v-icon>
                              </v-btn>
                            </div>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>

                <!-- Empty category message -->
                <div v-else class="text-center py-4 bg-gray-50 rounded">
                  <p class="text-gray-500">No content in this category</p>
                </div>
              </div>
            </div>
          </v-card>
        </v-col>

        <!-- Right: Content Form -->
        <v-col cols="12" md="5" v-if="orgRepo.categories && orgRepo.categories.length > 0">
          <StickyWrapper :offsetTop="50">
            <!-- Content Addition Form -->
            <v-card class="rounded-xl shadow-md p-6 mb-8">
              <h3 class="text-lg font-medium text-gray-800 mb-4">Add New Content</h3>
              <v-row>
                <v-col cols="12">
                  <v-select
                    v-model="selectedCategory"
                    :items="categoryOptions"
                    label="Category"
                    placeholder="Select a category"
                    outlined
                    dense
                    hide-details
                  ></v-select>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contentTitle"
                    label="Title"
                    placeholder="Enter title"
                    outlined
                    dense
                    hide-details
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="contentType"
                    :items="[
                      { text: 'YouTube Video Id', value: 'youtube-link' },
                      { text: 'Image', value: 'org-images' },
                      { text: 'Document', value: 'org-docs' }
                    ]"
                    label="Type"
                    outlined
                    hide-details
                    dense
                  ></v-select>
                </v-col>
                <!-- YouTube Link Input -->
                <v-col cols="12" v-if="contentType === 'youtube-link' && contentTitle">
                  <v-text-field
                    v-model="contentLink"
                    label="YouTube Video Id"
                    placeholder="https://www.youtube.com/embed/{VIDEO_ID}"
                    outlined
                    dense
                    hide-details
                  ></v-text-field>
                </v-col>
                <!-- Image Upload Input -->
                <v-col cols="12" v-if="contentType === 'org-images' && contentTitle">
                  <v-btn color="primary" @click="triggerFileInput('image')">Browse Image</v-btn>
                  <input
                    ref="imageInput"
                    type="file"
                    class="hidden"
                    accept="image/*"
                    @change="handleFileUpload($event, 'image')"
                  />
                  <span v-if="filePreview" class="ml-2">
                    <img
                      :src="filePreview"
                      alt="Preview"
                      style="height: 40px; vertical-align: middle"
                    />
                  </span>
                </v-col>
                <!-- Document Upload Input -->
                <v-col cols="12" v-if="contentType === 'org-docs' && contentTitle">
                  <v-btn color="primary" @click="triggerFileInput('document')"
                    >Browse Document</v-btn
                  >
                  <input
                    ref="documentInput"
                    type="file"
                    class="hidden"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                    @change="handleFileUpload($event, 'document')"
                  />
                  <span v-if="contentLink && contentType === 'org-docs'" class="ml-2 text-gray-600">
                    File selected
                  </span>
                </v-col>
                <!-- Save Button -->
                <v-col cols="12" v-if="contentTitle && contentLink" class="flex justify-end">
                  <v-btn color="primary" @click="addContent">Save</v-btn>
                </v-col>
              </v-row>
            </v-card>
          </StickyWrapper>
        </v-col>
      </v-row>

      <!-- Add/Edit Category Dialog -->
      <v-dialog v-model="showAddCategoryDialog" max-width="500px">
        <v-card>
          <v-card-title class="text-md">
            {{ editingCategoryIndex !== null ? 'Edit Category' : 'Add Category' }}
          </v-card-title>
          <v-card-text>
            <v-text-field
              v-model="newCategoryName"
              label="Category Name"
              outlined
              dense
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn text @click="showAddCategoryDialog = false">Cancel</v-btn>
            <v-btn color="primary" outline @click="saveCategory">Save</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Edit Content Dialog -->
      <v-dialog v-model="showEditContentDialog" max-width="500px">
        <v-card>
          <v-card-title class="text-h6">Edit Content</v-card-title>
          <v-card-text>
            <v-text-field
              v-model="editContentData.title"
              label="Title"
              outlined
              dense
              hide-details
              class="mb-4"
            ></v-text-field>
            <v-text-field
              v-if="editContentData.type === 'youtube-link'"
              v-model="editContentData.link"
              label="YouTube Video Id"
              outlined
              hide-details
              dense
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="blue darken-1" text @click="showEditContentDialog = false">Cancel</v-btn>
            <v-btn color="blue darken-1" text @click="updateContent">Update</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Create Repository Dialog -->
      <v-dialog v-model="showCreateRepoDialog" max-width="600px" persistent>
        <v-card>
          <v-card-title class="text-h6">Create Repository</v-card-title>
          <v-card-text>
            <v-text-field
              v-model="newRepoName"
              label="Repository Name"
              placeholder="Enter repository name"
              outlined
              dense
              class="mb-4"
            ></v-text-field>

            <h4 class="text-md font-medium text-gray-800 mb-3">Repository Settings</h4>
            <v-switch
              v-model="orgRepo.enable"
              label="Enable Repository"
              hide-details
              class="mb-2"
            ></v-switch>
            <v-switch
              v-model="orgRepo.expansionState"
              label="Allow Expanded Panel Open"
              hide-details
              class="mb-2"
            ></v-switch>
            <v-switch
              v-model="orgRepo.openInNewTab"
              label="Open Links in New Tab"
              hide-details
              class="mb-2"
            ></v-switch>
            <v-switch
              v-model="orgRepo.visibleInDashboard"
              label="View In Dashboard"
              hide-details
            ></v-switch>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn text @click="showCreateRepoDialog = false">Cancel</v-btn>
            <v-btn color="primary" @click="createRepository">Create Repository</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Add Content with Category Dialog (when no categories exist) -->
      <v-dialog v-model="showAddContentWithCategoryDialog" max-width="600px">
        <v-card>
          <v-card-title class="text-h6">Add Content</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="newCategoryName"
                  label="Category Name"
                  placeholder="Enter category name"
                  outlined
                  hide-details
                  dense
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-text-field
                  v-model="contentTitle"
                  label="Content Title"
                  placeholder="Enter content title"
                  hide-details
                  outlined
                  dense
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-select
                  v-model="contentType"
                  :items="[
                    { text: 'YouTube Video Id', value: 'youtube-link' },
                    { text: 'Image', value: 'org-images' },
                    { text: 'Document', value: 'org-docs' }
                  ]"
                  label="Content Type"
                  outlined
                  hide-details
                  dense
                ></v-select>
              </v-col>

              <v-col cols="12" v-if="contentType === 'youtube-link' && contentTitle">
                <v-text-field
                  v-model="contentLink"
                  label="YouTube Video Id"
                  placeholder="https://www.youtube.com/embed/{VIDEO_ID}"
                  outlined
                  hide-details
                  dense
                ></v-text-field>
              </v-col>

              <v-col cols="12" v-if="contentType === 'org-images' && contentTitle">
                <v-btn color="primary" @click="triggerFileInput('image')">Browse Image</v-btn>
                <input
                  ref="imageInput"
                  type="file"
                  class="hidden"
                  accept="image/*"
                  @change="handleFileUpload($event, 'image')"
                />
                <span v-if="filePreview" class="ml-2">
                  <img
                    :src="filePreview"
                    alt="Preview"
                    style="height: 40px; vertical-align: middle"
                  />
                </span>
              </v-col>

              <v-col cols="12" v-if="contentType === 'org-docs' && contentTitle">
                <v-btn color="primary" @click="triggerFileInput('document')">Browse Document</v-btn>
                <input
                  ref="documentInput"
                  type="file"
                  class="hidden"
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                  @change="handleFileUpload($event, 'document')"
                />
                <span v-if="contentLink && contentType === 'org-docs'" class="ml-2 text-gray-600">
                  File selected
                </span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn text @click="cancelAddContentWithCategory">Cancel</v-btn>
            <v-btn
              color="primary"
              @click="saveContentWithCategory"
              :disabled="!newCategoryName || !contentTitle || !contentType || !contentLink"
            >
              Save
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Repository Settings Dialog -->
      <v-dialog v-model="showRepoSettingsDialog" max-width="500px">
        <v-card>
          <v-card-title class="text-h6">Repository Settings</v-card-title>
          <v-card-text>
            <v-text-field
              v-model="orgRepo.name"
              label="Repository Name"
              outlined
              dense
              class="mb-4"
            ></v-text-field>
            <v-switch
              v-model="orgRepo.enable"
              label="Enable Repository"
              hide-details
              class="mb-2"
            ></v-switch>
            <v-switch
              v-model="orgRepo.expansionState"
              label="Allow Expanded Panel Open"
              hide-details
              class="mb-2"
            ></v-switch>
            <v-switch
              v-model="orgRepo.openInNewTab"
              label="Open Links in New Tab"
              hide-details
              class="mb-2"
            ></v-switch>
            <v-switch
              v-model="orgRepo.visibleInDashboard"
              label="View In Dashboard"
              hide-details
            ></v-switch>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn text @click="showRepoSettingsDialog = false">Cancel</v-btn>
            <v-btn color="primary" @click="saveRepoSettings">Save Settings</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-container>
  </div>
</template>

<script>
import StickyWrapper from '@/components/common/StickyWrapper.vue'
import NoData from '@/components/NoData.vue'

export default {
  components: {
    StickyWrapper,
    NoData
  },
  data() {
    return {
      // Repository data structure
      orgRepo: {
        name: 'My Repository',
        enable: true,
        expansionState: false,
        openInNewTab: false,
        categories: []
      },
      selectedCategory: '',
      contentTitle: '',
      contentType: 'youtube-link',
      contentLink: '',
      filePreview: '',
      uploadedFile: null,
      // Dialog controls
      showAddCategoryDialog: false,
      showEditContentDialog: false,
      showCreateRepoDialog: false,
      showRepoSettingsDialog: false,
      showAddContentWithCategoryDialog: false,
      newCategoryName: '',
      editingCategoryIndex: null,
      newRepoName: '',
      s3BaseUrl: process.env.VUE_APP_S3_BASE_URL,
      editContentData: {
        categoryIndex: null,
        contentIndex: null,
        title: '',
        type: '',
        link: ''
      }
    }
  },
  computed: {
    categoryOptions() {
      return this.orgRepo.categories.map((category, index) => ({
        text: category.categoryName,
        value: index
      }))
    },
    filteredFiles() {
      let allContent = []

      // Flatten all content from all categories
      this.orgRepo.categories.forEach((category) => {
        if (category.content && category.content.length > 0) {
          category.content.forEach((item) => {
            allContent.push({
              ...item,
              categoryName: category.categoryName
            })
          })
        }
      })

      return allContent
    },
    hasRepository() {
      return this.orgRepo && this.orgRepo.enable
    }
  },
  async mounted() {
    await this.getRepoData()
  },
  methods: {
    async getRepoData() {
      try {
        const res = await this.$axios.get('/workforce/orgSetup/empapp-settings')
        if (res.data.empApp?.orgRepo) {
          this.orgRepo = res.data.empApp.orgRepo
        } else if (res.data.empApp?.orgTutorial) {
          const oldData = res.data.empApp.orgTutorial
          this.orgRepo = {
            name: 'Migrated Repository',
            enable: oldData.enable || true,
            expansionState: false,
            openInNewTab: false,
            categories: [
              {
                categoryName: 'General',
                content: oldData.content.map((item) => ({
                  title: item.title,
                  type: this.migrateContentType(item.type),
                  link: item.link
                }))
              }
            ]
          }
          await this.saveRepoData()
        }
      } catch (error) {
        console.error('Error fetching repository data:', error)
      }
    },

    migrateContentType(oldType) {
      switch (oldType) {
        case 'image':
          return 'org-images'
        case 'link':
          return 'youtube-link'
        default:
          return 'org-docs'
      }
    },

    async saveRepoData() {
      try {
        const payload = {
          orgRepo: this.orgRepo
        }
        await this.$axios.post('/workforce/orgSetup/empapp-settings', payload)
        this.$toast.success('Repository saved successfully')
      } catch (error) {
        console.error('Error saving repository:', error)
        this.$toast.error('Failed to save repository')
      }
    },

    // Category management
    saveCategory() {
      if (!this.newCategoryName.trim()) {
        this.$toast.error('Category name cannot be empty')
        return
      }

      if (this.editingCategoryIndex !== null) {
        this.orgRepo.categories[this.editingCategoryIndex].categoryName = this.newCategoryName
      } else {
        this.orgRepo.categories.push({
          categoryName: this.newCategoryName,
          content: []
        })
      }

      this.showAddCategoryDialog = false
      this.newCategoryName = ''
      this.editingCategoryIndex = null
      this.saveRepoData()
    },

    editCategory(index) {
      this.editingCategoryIndex = index
      this.newCategoryName = this.orgRepo.categories[index].categoryName
      this.showAddCategoryDialog = true
    },

    deleteCategory(index) {
      this.orgRepo.categories.splice(index, 1)
      this.saveRepoData()
    },

    triggerFileInput(type) {
      if (type === 'image') {
        this.$refs.imageInput.click()
      } else if (type === 'document') {
        this.$refs.documentInput.click()
      }
    },

    async handleFileUpload(event, type) {
      const file = event.target.files[0]
      if (!file) return

      const categoryName =
        this.selectedCategory !== ''
          ? this.orgRepo.categories[this.selectedCategory].categoryName
          : ''

      const formData = new FormData()
      formData.append('attachments', file)
      formData.append('type', categoryName)

      try {
        const response = await this.$axios.post('/workforce/org/logo', formData)
        if (response.data.success && response.data.url) {
          this.contentLink = response.data.url.doc_physical_path
          this.uploadedFile = file

          if (type === 'image') {
            this.filePreview = URL.createObjectURL(file)
          }
        } else {
          this.$toast.error(`Failed to upload ${type}`)
        }
      } catch (error) {
        console.error(`Error uploading ${type}:`, error)
        this.$toast.error(`Error uploading ${type}`)
      }
    },

    addContent() {
      if (
        !this.contentTitle ||
        !this.contentType ||
        !this.contentLink ||
        this.selectedCategory === ''
      ) {
        this.$toast.error('Please fill all fields')
        return
      }

      const newContent = {
        title: this.contentTitle,
        type: this.contentType,
        link: this.contentLink
      }

      this.orgRepo.categories[this.selectedCategory].content.push(newContent)
      this.saveRepoData()

      // Reset form
      this.contentTitle = ''
      this.contentType = 'youtube-link'
      this.contentLink = ''
      this.filePreview = ''
      this.uploadedFile = null
    },

    editContent(categoryIndex, contentIndex) {
      const content = this.orgRepo.categories[categoryIndex].content[contentIndex]
      this.editContentData = {
        categoryIndex,
        contentIndex,
        title: content.title,
        type: content.type,
        link: content.link
      }
      this.showEditContentDialog = true
    },

    updateContent() {
      const { categoryIndex, contentIndex, title, link } = this.editContentData
      const content = this.orgRepo.categories[categoryIndex].content[contentIndex]

      content.title = title
      if (content.type === 'youtube-link') {
        content.link = link
      }

      this.showEditContentDialog = false
      this.saveRepoData()
    },

    deleteContent(categoryIndex, contentIndex) {
      this.orgRepo.categories[categoryIndex].content.splice(contentIndex, 1)
      this.saveRepoData()
    },

    copyItemLink(item) {
      const url = this.getFullUrl(item.link)
      navigator.clipboard
        .writeText(url)
        .then(() => {
          this.$vuetify.snackbar = {
            text: 'Link copied to clipboard!',
            color: 'success',
            timeout: 2000
          }
        })
        .catch((err) => {
          console.error('Could not copy text: ', err)
          this.$vuetify.snackbar = {
            text: 'Failed to copy link',
            color: 'error',
            timeout: 2000
          }
        })
    },

    getYoutubeVideoId(url) {
      const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
      const match = url.match(regExp)
      return match && match[2].length === 11 ? match[2] : null
    },

    getYoutubeEmbedUrl(url) {
      const videoId = url
      return `https://www.youtube.com/embed/${videoId}`
    },
    getFullUrl(link) {
      if (link && !link.includes('/')) {
        return `https://www.youtube.com/embed/${link}`
      }
      if (link && !link.startsWith('http')) {
        return this.s3BaseUrl + link
      }
      return link
    },
    openInNewTab(link) {
      const url = this.getFullUrl(link)
      window.open(url, '_blank')
    },

    // Repository management methods
    openCreateRepoDialog() {
      this.newRepoName = ''
      this.showCreateRepoDialog = true
    },

    async createRepository() {
      if (!this.newRepoName.trim()) {
        this.$toast.error('Repository name cannot be empty')
        return
      }

      this.orgRepo = {
        name: this.newRepoName,
        enable: true,
        expansionState: false,
        openInNewTab: false,
        visibleInDashboard: false,
        categories: []
      }

      await this.saveRepoData()
      this.showCreateRepoDialog = false
      this.newRepoName = ''
    },

    async saveRepoSettings() {
      if (!this.orgRepo.name.trim()) {
        this.$toast.error('Repository name cannot be empty')
        return
      }

      await this.saveRepoData()
      this.showRepoSettingsDialog = false
    },

    // Handle the dynamic button click (Add Content vs Add Category)
    handleAddButtonClick() {
      if (!this.orgRepo.categories || this.orgRepo.categories.length === 0) {
        this.resetContentForm()
        this.showAddContentWithCategoryDialog = true
      } else {
        this.showAddCategoryDialog = true
      }
    },

    async saveContentWithCategory() {
      if (!this.newCategoryName.trim()) {
        this.$toast.error('Category name cannot be empty')
        return
      }

      if (!this.contentTitle.trim()) {
        this.$toast.error('Content title cannot be empty')
        return
      }

      if (!this.contentType) {
        this.$toast.error('Please select content type')
        return
      }

      if (!this.contentLink.trim()) {
        this.$toast.error('Please provide content link or upload file')
        return
      }

      const newCategory = {
        categoryName: this.newCategoryName,
        content: [
          {
            title: this.contentTitle,
            type: this.contentType,
            link: this.contentLink
          }
        ]
      }

      this.orgRepo.categories.push(newCategory)
      await this.saveRepoData()
      this.showAddContentWithCategoryDialog = false
      this.resetContentForm()
      this.newCategoryName = ''
    },

    cancelAddContentWithCategory() {
      this.showAddContentWithCategoryDialog = false
      this.resetContentForm()
      this.newCategoryName = ''
    },

    // Reset content form fields
    resetContentForm() {
      this.contentTitle = ''
      this.contentType = 'youtube-link'
      this.contentLink = ''
      this.filePreview = ''
      this.uploadedFile = null
      this.selectedCategory = ''
    }
  }
}
</script>

<style scoped>
.hidden {
  display: none;
}
.file-card {
  transition: all 0.2s ease;
}
.file-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.video-container {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
}
.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.category-section {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}
</style>
