<template>
  <div>
    <!-- Custom Button Tabs -->
    <div class="custom-tab-buttons mb-6">
      <v-btn
        :class="['tab-btn', { 'tab-btn--active': dataMappingTab === 0 }]"
        @click="dataMappingTab = 0"
        large
        depressed
      >
        <v-icon left>mdi-file-document-outline</v-icon>
        Sub-case Fields
      </v-btn>
      <v-btn
        :class="['tab-btn', { 'tab-btn--active': dataMappingTab === 1 }]"
        @click="dataMappingTab = 1"
        large
        depressed
      >
        <v-icon left>mdi-account-outline</v-icon>
        Customer Fields
      </v-btn>
    </div>

    <!-- Tab Content -->
    <div v-show="dataMappingTab === 0">
      <v-text-field
        v-model="caseType.value"
        label="Subcase Name"
        required
        :rules="caseType.rules"
        :disabled="!!isUploadCase"
        class="mt-4"
      ></v-text-field>

      <v-data-table
        :headers="[
          { text: 'Field Name', value: 'text', width: '25%' },
          { text: 'Mapping Name', value: 'value', width: '45%' },
          { text: 'Visibility', value: 'visibility', width: '10%', align: 'center' },
          { text: 'Required', value: 'required', width: '10%' }
        ]"
        :items="orgKeys"
        :items-per-page="-1"
        hide-default-footer
        disable-sort
        dense
        class="mt-4"
      >
        <template v-slot:header.visibility>
          <div>Visibility</div>
          <div class="d-flex justify-center align-center w-100">
            <div class="text-center px-4">Form</div>
            <v-divider vertical class="mx-2"></v-divider>
            <div class="text-center px-4">Table</div>
          </div>
        </template>
        <template v-slot:item="{ item }">
          <tr>
            <td class="px-4 font-semibold">{{ item.text }}</td>
            <td class="py-2">
              <div class="d-flex align-center">
                <v-select
                  v-if="item?.prefix"
                  v-model="item.selectedPrefix"
                  :items="item?.prefix"
                  dense
                  class="mr-2"
                  style="max-width: 80px"
                  hide-details
                  outlined
                ></v-select>
                <v-text-field
                  v-model="item.value"
                  dense
                  :label="`Display Name of ${item.text}`"
                  :disabled="!!isUploadCase"
                  hide-details
                  outlined
                ></v-text-field>
                <v-select
                  v-if="item?.suffix"
                  v-model="item.selectedSuffix"
                  :items="item?.suffix"
                  dense
                  outlined
                  class="ml-2"
                  style="max-width: 120px"
                  hide-details
                ></v-select>
              </div>
            </td>
            <td>
              <div class="d-flex justify-center align-center">
                <div class="d-flex justify-center" style="width: 50%">
                  <v-checkbox
                    v-model="item.formVisibility"
                    dense
                    hide-details
                    @change="updateVisibility(item)"
                  ></v-checkbox>
                </div>
                <v-divider vertical></v-divider>
                <div class="d-flex justify-center" style="width: 50%">
                  <v-checkbox
                    v-model="item.tableVisibility"
                    dense
                    hide-details
                    @change="updateVisibility(item)"
                  ></v-checkbox>
                </div>
              </div>
            </td>
            <td class="text-center px-4">
              <v-checkbox v-model="item.required" dense hide-details></v-checkbox>
            </td>
          </tr>
        </template>
      </v-data-table>

      <v-divider class="my-4"></v-divider>

      <h2 class="text-base font-semibold">Custom Fields</h2>

      <v-data-table
        :headers="[
          { text: 'Field Name', value: 'text', width: '50%' },
          { text: 'Type', value: 'type', width: '20%' },
          { text: 'Actions', value: 'actions', width: '10%' }
        ]"
        :items="extraColumns"
        :items-per-page="-1"
        hide-default-footer
        disable-sort
        dense
      >
        <template v-slot:item="{ item, index }">
          <tr>
            <td class="px-2 py-1">
              <v-text-field v-model="item.text" dense hide-details outlined></v-text-field>
            </td>
            <td>
              <v-select
                v-model="item.value"
                :items="getFilteredItems(index)"
                dense
                hide-details
                outlined
              ></v-select>
            </td>
            <td class="px-4">
              <v-btn icon small color="error" @click="removeExtraColumn(index)">
                <v-icon small>mdi-delete</v-icon>
              </v-btn>
            </td>
          </tr>
        </template>
      </v-data-table>

      <div class="flex justify-end">
        <v-btn
          color="primary"
          text
          small
          outlined
          class="mt-2"
          @click="addExtraColumn"
          v-if="!isUploadCase"
        >
          <v-icon left small>mdi-plus</v-icon>
          Add Custom Field
        </v-btn>
      </div>
    </div>

    <div v-show="dataMappingTab === 1">
      <v-card flat>
        <v-card-text>
          <div class="d-flex flex-wrap">
            <v-text-field
              v-model="staticFields.crn"
              label="CRN No"
              required
              :rules="staticFields.crnRules"
              :disabled="!!isUploadCase"
              class="mr-4"
              outlined
              dense
              style="width: 200px"
            ></v-text-field>
            <v-text-field
              v-model="staticFields.customerFullName"
              label="Customer Name"
              required
              :rules="staticFields.customerFullNameRules"
              :disabled="!!isUploadCase"
              class="mr-4"
              outlined
              dense
              style="width: 200px"
            ></v-text-field>
            <v-text-field
              v-model="staticFields.customerMobile"
              label="Mobile"
              :disabled="!!isUploadCase"
              class="mr-4"
              style="width: 200px"
              outlined
              dense
            ></v-text-field>
            <v-text-field
              v-model="staticFields.customerEmail"
              label="Email"
              :disabled="!!isUploadCase"
              style="width: 200px"
              outlined
              dense
            ></v-text-field>
          </div>

          <v-expansion-panels class="mt-4">
            <v-expansion-panel v-for="(address, index) in addresses" :key="index">
              <v-expansion-panel-header>
                <div class="d-flex align-center">
                  <span>Address {{ index + 1 }}</span>
                  <v-chip x-small label class="ml-2" :color="address.default ? 'primary' : 'grey'">
                    {{ address.typeOfAddress || 'Select Type' }}
                  </v-chip>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div class="d-flex justify-between align-center mb-4">
                  <v-select
                    v-model="address.typeOfAddress"
                    :items="getFilteredAddressTypes(index)"
                    label="Type"
                    :disabled="!!isUploadCase"
                    class="mr-4"
                    style="max-width: 300px"
                    outlined
                    dense
                    hide-details
                  ></v-select>
                  <v-checkbox
                    v-model="address.default"
                    label="Set as default"
                    @change="setDefault(index)"
                    class="mr-4"
                  ></v-checkbox>
                  <v-btn
                    icon
                    small
                    color="error"
                    @click="removeAddress(index)"
                    v-show="addresses.length > 1"
                  >
                    <v-icon small>mdi-delete</v-icon>
                  </v-btn>
                </div>

                <div class="d-flex flex-wrap">
                  <v-text-field
                    v-for="(field, key) in addressFields"
                    :key="key"
                    v-model="address[key].displayName"
                    :label="field.text"
                    :disabled="!!isUploadCase"
                    class="mr-4 mb-4"
                    style="width: 200px"
                    @input="validateAddress"
                    outlined
                    dense
                    hide-details
                  ></v-text-field>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <div class="flex justify-end mt-4">
            <v-btn
              color="primary"
              text
              small
              outlined
              @click="addAddress"
              v-show="addresses.length < addressTypes.length"
            >
              <v-icon left small>mdi-plus</v-icon>
              Add Address
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataMapping',
  props: {
    caseType: {
      type: Object,
      required: true
    },
    orgKeys: {
      type: Array,
      required: true
    },
    extraColumns: {
      type: Array,
      required: true
    },
    staticFields: {
      type: Object,
      required: true
    },
    addresses: {
      type: Array,
      required: true
    },
    addressFields: {
      type: Object,
      required: true
    },
    addressTypes: {
      type: Array,
      required: true
    },
    selectedAmountKey: {
      type: String,
      default: null
    },
    isUploadCase: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataMappingTab: 0
    }
  },
  methods: {
    updateVisibility(item) {
      item.visibility = item.formVisibility || item.tableVisibility
      this.$emit('update-visibility', item)
    },
    getFilteredItems(index) {
      const search = this.extraColumns[index].search || this.extraColumns[index].text
      if (search) {
        const baseItems = ['STRING', 'NUMBER', 'DATE', 'CURRENCY']
        return baseItems.map((type) => ({
          text: `${search} - ${type}`,
          value: `${search} - ${type}`
        }))
      }
      return []
    },
    removeExtraColumn(index) {
      this.$emit('remove-extra-column', index)
    },
    addExtraColumn() {
      this.$emit('add-extra-column')
    },
    getFilteredAddressTypes(currentIndex) {
      const selectedTypes = this.addresses
        .filter((_, index) => index !== currentIndex)
        .map((address) => address.typeOfAddress)

      return this.addressTypes.filter((type) => !selectedTypes.includes(type))
    },
    removeAddress(index) {
      this.$emit('remove-address', index)
    },
    addAddress() {
      this.$emit('add-address')
    },
    setDefault(index) {
      this.$emit('set-default-address', index)
    },
    validateAddress() {
      this.$emit('validate-address')
    },
    setDefaultPrefixSuffix() {
      if (this.orgKeys) {
        this.orgKeys.forEach((item) => {
          if (item.prefix && item.prefix.length) {
            item.selectedPrefix = '₹'
          }
          if (item.suffix && item.suffix.length) {
            item.selectedSuffix = 'Weekly'
          }
        })
      }
    }
  },
  watch: {
    orgKeys: {
      handler() {
        this.setDefaultPrefixSuffix()
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
.custom-tab-buttons {
  display: flex;
  gap: 8px;
  padding: 6px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tab-btn {
  flex: 1;
  height: 48px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background-color: transparent !important;
  color: #6c757d !important;
  border: 1px solid transparent !important;
  box-shadow: none !important;
}

.tab-btn:hover {
  background-color: rgba(13, 110, 253, 0.08) !important;
  color: #0d6efd !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15) !important;
}

.tab-btn--active {
  background-color: #0d6efd !important;
  color: white !important;
  border: 1px solid #0d6efd !important;
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3) !important;
  transform: translateY(-2px);
}

.tab-btn--active:hover {
  background-color: #0b5ed7 !important;
  color: white !important;
  transform: translateY(-2px);
}

.tab-btn--active .v-icon {
  color: white !important;
}

.tab-btn .v-icon {
  color: inherit !important;
  margin-right: 8px;
}

/* Remove default button styles */
.tab-btn::before {
  display: none !important;
}

.tab-btn .v-btn__content {
  font-size: 14px;
  font-weight: 500;
}
</style>
