<template>
  <div class="bg-gray-100 min-h-screen ">
    <v-container fluid class="px-4 py-8 max-w-none">
      <!-- Header -->
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-xl font-bold text-gray-800">Module & Feature Permissions</h1>
      </div>

      <!-- Legend -->
      <div class="mb-8">
        <div class="flex flex-wrap gap-4 mb-4">
          <span
            class="px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 flex items-center"
          >
            <v-icon small class="mr-1">mdi-check-circle</v-icon> Allow
          </span>
          <span
            class="px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 flex items-center"
          >
            <v-icon small class="mr-1">mdi-lock</v-icon> Deny
          </span>
          <span
            class="px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 flex items-center"
          >
            <v-icon small class="mr-1">mdi-toggle-switch</v-icon> Enabled
          </span>
          <span
            class="px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-800 flex items-center"
          >
            <v-icon small class="mr-1">mdi-toggle-switch-off</v-icon> Disabled
          </span>
        </div>

        <!-- Summary Card -->
        <v-card class="p-4">
          <h2 class="text-xl font-semibold mb-4">Summary</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-blue-800 font-bold text-lg">{{ summaryStats.totalModules }}</div>
              <div class="text-blue-600">Modules</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-green-800 font-bold text-lg">{{ summaryStats.enabledModules }}</div>
              <div class="text-green-600">Enabled Modules</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <div class="text-yellow-800 font-bold text-lg">
                {{ summaryStats.modulesWithoutFeatures }}
              </div>
              <div class="text-yellow-600">Modules with no features</div>
            </div>
            <div class="bg-indigo-50 p-4 rounded-lg">
              <div class="text-indigo-800 font-bold text-lg">{{ summaryStats.totalFeatures }}</div>
              <div class="text-indigo-600">Total Features</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
              <div class="text-purple-800 font-bold text-lg">
                {{ summaryStats.allowedFeatures }}
              </div>
              <div class="text-purple-600">Allowed Features</div>
            </div>
          </div>
        </v-card>
      </div>

      <!-- Modules Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <v-card
          v-for="(moduleData, moduleName) in Object.fromEntries(Object.entries(filteredModules).filter(([key, value]) => value.enable))"
          :key="moduleName"
          class="module-card overflow-hidden border border-gray-200"
        >
          <!-- Module Header -->
          <div
            class="px-4 py-3 border-b border-gray-200 flex justify-between items-center"
            :class="moduleData.enable ? 'bg-green-50' : 'bg-gray-100'"
          >
            <div class="flex items-center">
              <v-icon :color="moduleData.enable ? 'green' : 'grey'" class="mr-2">
                {{ getModuleIcon(moduleName) }}
              </v-icon>
              <h3 class="font-semibold text-gray-800">{{ formatModuleName(moduleName) }}</h3>
            </div>
            <v-chip :color="moduleData.enable ? 'green' : 'grey'" text-color="white" small>
              <v-icon left small>
                {{ moduleData.enable ? 'mdi-toggle-switch' : 'mdi-toggle-switch-off' }}
              </v-icon>
              {{ moduleData.enable ? 'Enabled' : 'Disabled' }}
            </v-chip>
          </div>

          <!-- Module Body -->
          <v-card-text class="p-4">
            <div class="flex justify-between items-center mb-3">
              <h4 class="text-sm font-medium text-gray-600">Features</h4>
              <v-chip color="blue" text-color="white" x-small>
                {{ Object.keys(moduleData.features).length }}
              </v-chip>
            </div>

            <!-- Features List -->
            <div class="space-y-2">
              <div
                v-if="Object.keys(moduleData.features).length === 0"
                class="text-sm text-gray-500 italic"
              >
                No features configured
              </div>
              <div
                v-else
                v-for="(featureStatus, featureName) in moduleData.features"
                :key="featureName"
                class="feature-item flex justify-between items-center py-2 px-3 border border-gray-100 rounded hover:bg-gray-50 transition-colors"
              >
                <div class="flex items-center">
                  <v-icon small :color="featureStatus === 'allow' ? 'green' : 'red'" class="mr-2">
                    {{ featureStatus === 'allow' ? 'mdi-check-circle' : 'mdi-lock' }}
                  </v-icon>
                  <span class="text-sm text-gray-700">{{ formatFeatureName(featureName) }}</span>
                </div>
                <v-chip
                  :color="featureStatus === 'allow' ? 'green' : 'red'"
                  text-color="white"
                  x-small
                >
                  {{ featureStatus === 'allow' ? 'Allowed' : 'Denied' }}
                </v-chip>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </div>

      <!-- Feature Settings & Addons -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Feature Settings & Addons</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <v-card
            v-for="(categoryData, categoryName) in featureSettings"
            :key="categoryName"
            class="module-card overflow-hidden border border-gray-200"
          >
            <!-- Category Header -->
            <div class="px-4 py-3 border-b border-gray-200 bg-indigo-50">
              <div class="flex items-center">
                <v-icon color="indigo" class="mr-2">mdi-cog</v-icon>
                <h3 class="font-semibold text-indigo-800">{{ formatModuleName(categoryName) }}</h3>
              </div>
            </div>

            <!-- Category Body -->
            <v-card-text class="p-4">
              <div class="flex justify-between items-center mb-3">
                <h4 class="text-sm font-medium text-gray-600">Settings</h4>
                <v-chip color="indigo" text-color="white" x-small>
                  {{ Object.keys(categoryData.features).length }}
                </v-chip>
              </div>

              <!-- Settings List -->
              <div class="space-y-2">
                <div
                  v-for="(featureStatus, featureName) in categoryData.features"
                  :key="featureName"
                  class="feature-item flex justify-between items-center py-2 px-3 border border-gray-100 rounded hover:bg-gray-50 transition-colors"
                >
                  <div class="flex items-center">
                    <v-icon small :color="featureStatus === 'allow' ? 'green' : 'red'" class="mr-2">
                      {{ featureStatus === 'allow' ? 'mdi-check-circle' : 'mdi-lock' }}
                    </v-icon>
                    <span class="text-sm text-gray-700">{{ formatFeatureName(featureName) }}</span>
                  </div>
                  <v-chip
                    :color="featureStatus === 'allow' ? 'green' : 'red'"
                    text-color="white"
                    x-small
                  >
                    {{ featureStatus === 'allow' ? 'Allowed' : 'Denied' }}
                  </v-chip>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </div>
      </div>
    </v-container>
  </div>
</template>

<script>
export default {
  name: 'ModulePermissionsDashboard',
  props: { planDetails: Object },
  data() {
    return {
      filteredModules: {}
    }
  },
  computed: {
    summaryStats() {
      const modules = this.$props.planDetails.MODULES
      const totalModules = Object.keys(modules).length
      const enabledModules = Object.values(modules).filter((m) => m.enable).length
      const modulesWithoutFeatures = Object.values(modules).filter(
        (m) => Object.keys(m.features).length === 0
      ).length

      let totalFeatures = 0
      let allowedFeatures = 0

      Object.values(modules).forEach((module) => {
        totalFeatures += Object.keys(module.features).length
        allowedFeatures += Object.values(module.features).filter((f) => f === 'allow').length
      })

      Object.values(this.$props.planDetails.FEATURE_LIST_AND_SETTINGS).forEach((category) => {
        totalFeatures += Object.keys(category.features).length
        allowedFeatures += Object.values(category.features).filter((f) => f === 'allow').length
      })

      return {
        totalModules,
        enabledModules,
        modulesWithoutFeatures,
        totalFeatures,
        allowedFeatures
      }
    },
    featureSettings() {
      return this.$props.planDetails.FEATURE_LIST_AND_SETTINGS
    }
  },
  mounted() {
    this.filteredModules = { ...this.$props.planDetails.MODULES }
  },
  methods: {
    formatModuleName(name) {
      return name
        .replace(/_/g, ' ')
        .toLowerCase()
        .replace(/\b\w/g, (l) => l.toUpperCase())
    },
    formatFeatureName(name) {
      return name
        .replace(/_/g, ' ')
        .toLowerCase()
        .replace(/\b\w/g, (l) => l.toUpperCase())
    },
    getModuleIcon(moduleName) {
      const icons = {
        AMC: 'mdi-file-document-outline',
        SELLS: 'mdi-cart',
        TASKS: 'mdi-clipboard-list',
        CLAIMS: 'mdi-file-document-edit',
        REPORTS: 'mdi-chart-bar',
        SETTINGS: 'mdi-cog',
        ANALYTICS: 'mdi-chart-line',
        CUSTOMERS: 'mdi-account-group',
        DASHBOARD: 'mdi-view-dashboard',
        EMPLOYEES: 'mdi-account-tie',
        ATTENDANCE: 'mdi-calendar-check',
        NOTIFICATIONS: 'mdi-bell',
        LEAD_MANAGEMENT: 'mdi-target',
        STOCK_MANAGEMENT: 'mdi-package-variant',
        ALLOCATE_PRODUCTS: 'mdi-truck-delivery',
        PROJECT_MANAGEMENT: 'mdi-folder-multiple',
        COLLECTION_MANAGEMENT: 'mdi-cash-multiple'
      }

      return icons[moduleName] || 'mdi-cube'
    }
  }
}
</script>

<style scoped>
.feature-item:hover {
  background-color: #f9fafb;
}

.search-field {
  min-width: 250px;
}

.module-card {
  transition: all 0.3s ease;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
