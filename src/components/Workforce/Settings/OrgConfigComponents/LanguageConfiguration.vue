<template>
  <div>
    <div>
      <!-- Main Content -->
      <!-- Action Buttons -->
      <div class="flex-1 overflow-auto">
        <div
          class="flex justify-between items-center bg-white shadow-sm border-b border-gray-200 px-4 py-2"
        >
          <!-- Tabs -->
          <v-tabs v-model="activeTab">
            <v-tab>Navigation</v-tab>
            <v-tab>Modules</v-tab>
            <v-tab>Forms</v-tab>
            <!-- <v-tab>Import/Export</v-tab> -->
          </v-tabs>

          <div class="flex items-center">
            <v-btn @click="resetToDefaults" outlined color="red" class="mr-2">
              Reset to Defaults
            </v-btn>
            <v-btn @click="saveChanges" color="primary" :loading="saving"> Save Changes </v-btn>
          </div>
        </div>

        <div class="p-6">
          <!-- Search Bar -->
          <div class="mb-6">
            <v-text-field
              v-model="searchTerm"
              placeholder="Search for labels to customize..."
              prepend-inner-icon="mdi-magnify"
              outlined
              dense
              class="max-w-md"
              hide-details
            ></v-text-field>
          </div>

          <v-tabs-items v-model="activeTab">
            <!-- Navigation Tab -->
            <v-tab-item>
              <v-card class="mb-6 rounded-lg shadow-md" flat>
                <v-card-text>
                  <div class="w-full">
                    <h3 class="text-lg sm:text-xl font-medium text-gray-900">Navigation Labels</h3>
                    <p class="text-gray-600 mt-1 text-sm sm:text-base">
                      Customize the names and icons of your left navigation panel
                    </p>

                    <v-divider class="my-4"></v-divider>
                    <v-row class="hidden md:flex px-2 pb-2 border-b">
                      <v-col cols="6" class="text-sm font-medium text-gray-900 pl-6">
                        Default Name
                      </v-col>
                      <v-col cols="6" class="text-sm font-medium text-gray-900 text-start pr-4">
                        Custom Name
                      </v-col>
                    </v-row>
                    <div
                      v-for="(item, index) in defaultNomenclature.navigation"
                      :key="item.id"
                      :class="{
                        'border-b': index < defaultNomenclature.navigation.length - 1,
                        'search-highlight':
                          isHighlighted(item.defaultName, searchTerm) ||
                          isHighlighted(getCustomName(item.id, 'navigation'), searchTerm)
                      }"
                    >
                      <div class="md:hidden p-4">
                        <v-row>
                          <v-col cols="12" class="d-flex align-center">
                            <v-icon class="mr-3 text-gray-500">{{
                              item.defaultIcon || item.icon
                            }}</v-icon>
                            <div>
                              <div class="text-xs font-medium text-gray-500">Default Name</div>
                              <span class="font-medium text-gray-900">{{ item.defaultName }}</span>
                            </div>
                          </v-col>

                          <v-col cols="12">
                            <div
                              class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1"
                            >
                              Custom Name
                            </div>
                            <v-text-field
                              dense
                              outlined
                              hide-details
                              class="w-full"
                              :value="getCustomName(item.id, 'navigation')"
                              @input="updateCustomName(item.id, 'navigation', $event)"
                              placeholder="Custom name (leave blank for default)"
                              :prepend-inner-icon="getDisplayIcon(item)"
                              @click:prepend-inner="handleIcon('open', item)"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </div>
                      <v-row class="hidden md:flex px-4 py-4 items-center">
                        <v-col cols="6" class="d-flex align-center">
                          <v-icon class="mr-3 text-gray-500 text-xl">{{
                            item.defaultIcon || item.icon
                          }}</v-icon>
                          <span class="font-medium text-gray-900">{{ item.defaultName }}</span>
                        </v-col>
                        <v-col cols="6">
                          <v-text-field
                            dense
                            outlined
                            hide-details
                            class="w-full"
                            :value="getCustomName(item.id, 'navigation')"
                            @input="updateCustomName(item.id, 'navigation', $event)"
                            placeholder="Custom name (leave blank for default)"
                            :prepend-inner-icon="getDisplayIcon(item)"
                            @click:prepend-inner="handleIcon('open', item)"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-tab-item>

            <!-- Modules Tab -->
            <v-tab-item>
              <v-card class="mb-6" elevation="2">
                <v-card-title>
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">Module Labels</h3>
                    <p class="text-gray-600 mt-1 text-sm">
                      Customize the names of your CRM modules and tabs
                    </p>
                  </div>
                </v-card-title>
                <v-divider></v-divider>
                <v-card-text class="pa-0">
                  <div
                    v-for="(module, moduleIndex) in defaultNomenclature.modules"
                    :key="module.id"
                    class="px-6 py-4"
                    :class="{
                      'border-b border-gray-200':
                        moduleIndex < defaultNomenclature.modules.length - 1,
                      'search-highlight':
                        isHighlighted(module.defaultName, searchTerm) ||
                        isHighlighted(getCustomModuleName(module.id), searchTerm)
                    }"
                  >
                    <div class="mb-4">
                      <div class="flex items-center justify-between">
                        <div class="min-w-[200px]">
                          <span class="font-medium">{{ module.defaultName }}</span>
                          <span class="text-gray-500 ml-2">(Module)</span>
                        </div>
                        <v-text-field
                          :value="getCustomModuleName(module.id)"
                          @input="updateCustomModuleName(module.id, $event)"
                          placeholder="Custom module name"
                          outlined
                          dense
                          class="flex-1 max-w-[400px]"
                          hide-details
                        ></v-text-field>
                      </div>
                    </div>
                    <div class="pl-6">
                      <h4 class="text-sm font-medium text-gray-500 mb-2">Tabs:</h4>
                      <div class="space-y-3">
                        <div
                          v-for="tab in module.tabs"
                          :key="tab.id"
                          class="flex items-center justify-between"
                          :class="{
                            'search-highlight':
                              isHighlighted(tab.defaultName, searchTerm) ||
                              isHighlighted(getCustomTabName(module.id, tab.id), searchTerm)
                          }"
                        >
                          <div class="min-w-[200px]">
                            <span>{{ tab.defaultName }}</span>
                            <span class="text-gray-500 ml-2">(Tab)</span>
                          </div>
                          <v-text-field
                            :value="getCustomTabName(module.id, tab.id)"
                            @input="updateCustomTabName(module.id, tab.id, $event)"
                            placeholder="Custom tab name"
                            outlined
                            dense
                            class="flex-1 max-w-[400px]"
                            hide-details
                          ></v-text-field>
                        </div>
                      </div>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-tab-item>

            <!-- Forms Tab -->
            <v-tab-item>
              <v-card class="mb-6" elevation="2">
                <v-card-title>
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">Form Labels</h3>
                    <p class="text-gray-600 mt-1 text-sm">
                      Customize form titles, subtitles, and field labels
                    </p>
                  </div>
                </v-card-title>
                <v-divider></v-divider>
                <v-card-text class="pa-0">
                  <div
                    v-for="(form, formIndex) in defaultNomenclature.forms"
                    :key="form.id"
                    class="px-6 py-4"
                    :class="{
                      'border-b border-gray-200': formIndex < defaultNomenclature.forms.length - 1
                    }"
                  >
                    <div class="mb-4">
                      <div class="flex items-center justify-between mb-2">
                        <div class="min-w-[200px]">
                          <span class="font-medium">{{ form.defaultTitle }}</span>
                          <span class="text-gray-500 ml-2">(Form Title)</span>
                        </div>
                        <v-text-field
                          :value="getCustomFormTitle(form.id)"
                          @input="updateCustomFormTitle(form.id, $event)"
                          placeholder="Custom form title"
                          outlined
                          dense
                          class="flex-1 max-w-[400px]"
                          hide-details
                        ></v-text-field>
                      </div>
                      <div class="flex items-center justify-between">
                        <div class="min-w-[200px]">
                          <span>{{ form.defaultSubtitle }}</span>
                          <span class="text-gray-500 ml-2">(Form Subtitle)</span>
                        </div>
                        <v-text-field
                          :value="getCustomFormSubtitle(form.id)"
                          @input="updateCustomFormSubtitle(form.id, $event)"
                          placeholder="Custom form subtitle"
                          outlined
                          dense
                          class="flex-1 max-w-[400px]"
                          hide-details
                        ></v-text-field>
                      </div>
                    </div>
                    <div class="pl-6">
                      <h4 class="text-sm font-medium text-gray-500 mb-2">Fields:</h4>
                      <div class="space-y-3">
                        <div
                          v-for="field in form.fields"
                          :key="field.id"
                          class="flex items-center justify-between"
                        >
                          <div class="min-w-[200px]">
                            <span>{{ field.defaultLabel }}</span>
                            <span class="text-gray-500 ml-2">(Field)</span>
                          </div>
                          <v-text-field
                            :value="getCustomFieldLabel(form.id, field.id)"
                            @input="updateCustomFieldLabel(form.id, field.id, $event)"
                            placeholder="Custom field label"
                            outlined
                            dense
                            class="flex-1 max-w-[400px]"
                            hide-details
                          ></v-text-field>
                        </div>
                      </div>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-tab-item>
          </v-tabs-items>
        </div>
      </div>
    </div>

    <!-- Icon Selector Dialog -->
    <v-dialog v-model="iconSelectorDialog" max-width="600px">
      <v-card>
        <v-card-title class="mb-8">
          Select an Icon
          <v-spacer></v-spacer>
          <v-btn icon @click="handleIcon('close')">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-subtitle>
          <v-text-field
            v-model="iconSearchTerm"
            placeholder="Search icons..."
            prepend-inner-icon="mdi-magnify"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-card-subtitle>
        <v-card-text>
          <div class="icon-selector">
            <div class="grid grid-cols-6 gap-2">
              <div
                v-if="loading"
                class="col-span-6 flex flex-col items-center justify-center h-64 w-full"
              >
                <v-progress-circular
                  :color="$vuetify.theme.currentTheme.primary"
                  indeterminate
                  :size="50"
                  :width="5"
                ></v-progress-circular>
                <div class="mt-4 text-center"><span>Please Wait...</span></div>
              </div>
              <template v-else>
                <div
                  v-for="icon in filteredIcons"
                  :key="icon.icon"
                  class="icon-option flex flex-col justify-center items-center text-center rounded-lg"
                  :class="{ selected: selectedIcon === icon }"
                  @click="selectedIcon = icon"
                >
                  <v-icon :class="icon">{{ icon.icon }}</v-icon>
                  <p class="text-sm">{{ icon.text }}</p>
                </div>
              </template>
            </div>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="handleIcon('cancel')"> Cancel </v-btn>
          <v-btn color="primary" @click="selectIcon" :disabled="!selectedIcon"> Select </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar v-model="snackbar.show" :color="snackbar.color" :timeout="snackbar.timeout">
      {{ snackbar.text }}
      <template v-slot:action="{ attrs }">
        <v-btn text v-bind="attrs" @click="snackbar.show = false"> Close </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script>
export default {
  name: 'LanguageConfiguration',
  data() {
    return {
      activeTab: 0,
      searchTerm: '',
      iconSearchTerm: '',
      loading: false,
      iconSelectorDialog: false,
      selectedIcon: '',
      currentEditingItem: null,
      currentEditingType: '',
      importFile: null,
      saving: false,
      snackbar: {
        show: false,
        text: '',
        color: 'success',
        timeout: 3000
      },
      customNomenclature: {},
      defaultNomenclature: {
        navigation: [
          { id: 'dashboard', defaultName: 'Dashboard', icon: 'mdi-apps' },
          { id: 'user', defaultName: 'Employees', icon: 'mdi-account-multiple' },
          {
            id: 'organization',
            defaultName: 'Team Tasks',
            icon: 'mdi-clipboard-text-clock-outline'
          },
          { id: 'customers', defaultName: 'Customers', icon: 'mdi-account-multiple' },
          { id: 'campaigns', defaultName: 'Campaigns', icon: 'mdi-bullhorn' },
          {
            id: 'inventory-management',
            defaultName: 'Part Management',
            icon: 'mdi-playlist-check'
          },
          { id: 'inventory-settings', defaultName: 'Part Settings', icon: 'mdi-tools' },
          { id: 'amc-management', defaultName: 'AMC', icon: 'mdi-shield' },
          { id: 'report-management', defaultName: 'Reports', icon: 'mdi-shield' },
          { id: 'notification', defaultName: 'Notifications', icon: 'mdi-bell-ring' },
          { id: 'org-settings', defaultName: 'Settings', icon: 'mdi-cog' }
        ],
        modules: [
          {
            id: 'leads-module',
            defaultName: 'Leads',
            tabs: [
              { id: 'leads-list', defaultName: 'List' },
              { id: 'leads-kanban', defaultName: 'Kanban' },
              { id: 'leads-calendar', defaultName: 'Calendar' }
            ]
          },
          {
            id: 'contacts-module',
            defaultName: 'Contacts',
            tabs: [
              { id: 'contacts-list', defaultName: 'List' },
              { id: 'contacts-map', defaultName: 'Map' }
            ]
          }
        ],
        forms: [
          {
            id: 'lead-form',
            defaultTitle: 'New Lead',
            defaultSubtitle: 'Enter lead details',
            fields: [
              { id: 'first-name', defaultLabel: 'First Name' },
              { id: 'last-name', defaultLabel: 'Last Name' },
              { id: 'company', defaultLabel: 'Company' },
              { id: 'email', defaultLabel: 'Email' },
              { id: 'phone', defaultLabel: 'Phone' }
            ]
          }
        ]
      },
      apiIcons: []
    }
  },
  computed: {
    filteredIcons() {
      if (!this.iconSearchTerm) {
        return this.apiIcons
      }
      return this.apiIcons.filter((icon) =>
        icon.icon.toLowerCase().includes(this.iconSearchTerm.toLowerCase())
      )
    }
  },
  methods: {
    // Display methods
    getDisplayName(item, type) {
      const customName = this.getCustomName(item.id, type)
      return customName || item.defaultName
    },
    getDisplayIcon(item) {
      const customIcon = this.customNomenclature.navigation?.[item.id]?.icon
      return customIcon || item.icon
    },
    getCustomName(id, type) {
      return this.customNomenclature[type]?.[id]?.name || ''
    },

    // Navigation methods
    updateCustomName(id, type, value) {
      if (!this.customNomenclature[type]) {
        this.$set(this.customNomenclature, type, {})
      }
      if (!this.customNomenclature[type][id]) {
        this.$set(this.customNomenclature[type], id, {})
      }
      this.$set(this.customNomenclature[type][id], 'name', value.trim())
    },

    // Module methods
    getCustomModuleName(moduleId) {
      return this.customNomenclature.modules?.[moduleId]?.name || ''
    },
    updateCustomModuleName(moduleId, value) {
      if (!this.customNomenclature.modules) {
        this.$set(this.customNomenclature, 'modules', {})
      }
      if (!this.customNomenclature.modules[moduleId]) {
        this.$set(this.customNomenclature.modules, moduleId, {})
      }
      this.$set(this.customNomenclature.modules[moduleId], 'name', value.trim())
    },
    getCustomTabName(moduleId, tabId) {
      return this.customNomenclature.modules?.[moduleId]?.tabs?.[tabId] || ''
    },
    updateCustomTabName(moduleId, tabId, value) {
      if (!this.customNomenclature.modules) {
        this.$set(this.customNomenclature, 'modules', {})
      }
      if (!this.customNomenclature.modules[moduleId]) {
        this.$set(this.customNomenclature.modules, moduleId, {})
      }
      if (!this.customNomenclature.modules[moduleId].tabs) {
        this.$set(this.customNomenclature.modules[moduleId], 'tabs', {})
      }
      this.$set(this.customNomenclature.modules[moduleId].tabs, tabId, value.trim())
    },

    // Form methods
    getCustomFormTitle(formId) {
      return this.customNomenclature.forms?.[formId]?.title || ''
    },
    updateCustomFormTitle(formId, value) {
      if (!this.customNomenclature.forms) {
        this.$set(this.customNomenclature, 'forms', {})
      }
      if (!this.customNomenclature.forms[formId]) {
        this.$set(this.customNomenclature.forms, formId, {})
      }
      this.$set(this.customNomenclature.forms[formId], 'title', value.trim())
    },
    getCustomFormSubtitle(formId) {
      return this.customNomenclature.forms?.[formId]?.subtitle || ''
    },
    updateCustomFormSubtitle(formId, value) {
      if (!this.customNomenclature.forms) {
        this.$set(this.customNomenclature, 'forms', {})
      }
      if (!this.customNomenclature.forms[formId]) {
        this.$set(this.customNomenclature.forms, formId, {})
      }
      this.$set(this.customNomenclature.forms[formId], 'subtitle', value.trim())
    },
    getCustomFieldLabel(formId, fieldId) {
      return this.customNomenclature.forms?.[formId]?.fields?.[fieldId] || ''
    },
    updateCustomFieldLabel(formId, fieldId, value) {
      if (!this.customNomenclature.forms) {
        this.$set(this.customNomenclature, 'forms', {})
      }
      if (!this.customNomenclature.forms[formId]) {
        this.$set(this.customNomenclature.forms, formId, {})
      }
      if (!this.customNomenclature.forms[formId].fields) {
        this.$set(this.customNomenclature.forms[formId], 'fields', {})
      }
      this.$set(this.customNomenclature.forms[formId].fields, fieldId, value.trim())
    },

    // Icon selector methods
    openIconSelector(id, type) {
      this.currentEditingItem = id
      this.currentEditingType = type
      this.selectedIcon = this.customNomenclature[type]?.[id]?.icon || ''
      this.iconSelectorDialog = true
    },
    selectIcon() {
      if (!this.customNomenclature[this.currentEditingType]) {
        this.$set(this.customNomenclature, this.currentEditingType, {})
      }
      if (!this.customNomenclature[this.currentEditingType][this.currentEditingItem]) {
        this.$set(this.customNomenclature[this.currentEditingType], this.currentEditingItem, {})
      }
      this.$set(
        this.customNomenclature[this.currentEditingType][this.currentEditingItem],
        'icon',
        this.selectedIcon.icon || this.selectedIcon // Handle both object and string formats
      )
      this.iconSelectorDialog = false
      this.iconSearchTerm = ''

      // Emit event to update navigation immediately
      this.$root.$emit('navigationConfigUpdated', this.customNomenclature)
    },

    handleIcon(action, item) {
      if (action == 'open') {
        this.iconSelectorDialog = true
        this.loading = true
        if (this.apiIcons.length === 0) {
          this.fetchApiIcons().then(() => {
            this.processIconSelection(item)
          })
        } else {
          // If we already have icons, just process the selection
          this.processIconSelection(item)
        }
      } else if (action == 'close' || action == 'cancel') {
        this.iconSelectorDialog = false
        this.iconSearchTerm = ''
        this.selectedIcon = ''
      }
    },

    // New helper method to process icon selection
    processIconSelection(item) {
      const matchedIconIndex = this.apiIcons.findIndex((icon) => icon.icon === item.icon)
      if (matchedIconIndex !== -1) {
        const matchedIcon = this.apiIcons[matchedIconIndex]
        this.selectedIcon = matchedIcon
        this.apiIcons.splice(matchedIconIndex, 1)
        this.apiIcons.unshift(matchedIcon)
      }

      this.currentEditingItem = item.id
      this.currentEditingType = 'navigation'
      this.loading = false
    },

    // Search highlight
    isHighlighted(text, searchTerm) {
      if (!searchTerm || !text) return false
      return text.toLowerCase().includes(searchTerm.toLowerCase())
    },

    // Import/Export methods
    exportSettings() {
      const dataStr = JSON.stringify(this.customNomenclature, null, 2)
      const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr)
      const exportFileDefaultName = 'crm-nomenclature-settings.json'

      const linkElement = document.createElement('a')
      linkElement.setAttribute('href', dataUri)
      linkElement.setAttribute('download', exportFileDefaultName)
      linkElement.click()

      this.$toast.success('Settings exported successfully')
    },
    async importSettings() {
      if (!this.importFile) return

      try {
        const fileContent = await this.readFileAsText(this.importFile)
        const importedSettings = JSON.parse(fileContent)

        // Replace current settings with imported ones
        this.customNomenclature = importedSettings

        this.$toast.success('Settings imported successfully')
      } catch (error) {
        this.$toast.error('Error importing settings: ' + error.message)
      }
    },
    readFileAsText(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (event) => resolve(event.target.result)
        reader.onerror = (error) => reject(error)
        reader.readAsText(file)
      })
    },

    // Action methods
    resetToDefaults() {
      this.customNomenclature = {}
      localStorage.removeItem('orgLanguageSettings')
      this.$root.$emit('navigationConfigUpdated', this.customNomenclature)
      this.$toast.success('Reset to default settings')
    },
    async saveChanges() {
      this.saving = true
      try {
        // Save to localStorage
        this.saveToLocalStorage()
        this.$root.$emit('navigationConfigUpdated', this.customNomenclature)
        this.$toast.success('Settings saved successfully')
      } catch (error) {
        this.$toast.error('Error saving settings: ' + error.message)
      } finally {
        this.saving = false
      }
    },

    saveToLocalStorage() {
      if (!this.customNomenclature || typeof this.customNomenclature !== 'object') {
        this.$toast.error('Invalid settings format')
        throw new Error('Invalid settings format')
      }

      const languageConfig = {
        customNomenclature: this.customNomenclature,
        timestamp: new Date().toISOString()
      }

      localStorage.setItem('orgLanguageSettings', JSON.stringify(languageConfig))
      console.log('Language configuration saved to localStorage:', languageConfig)
    },

    loadFromLocalStorage() {
      try {
        const savedConfig = localStorage.getItem('orgLanguageSettings')
        if (savedConfig) {
          const parsedConfig = JSON.parse(savedConfig)
          if (
            parsedConfig.customNomenclature &&
            typeof parsedConfig.customNomenclature === 'object'
          ) {
            this.customNomenclature = parsedConfig.customNomenclature
            // Emit event to update navigation when loading from storage
            this.$root.$emit('navigationConfigUpdated', this.customNomenclature)
            return true
          }
        }
        return false
      } catch (error) {
        console.error('Error loading from localStorage:', error)
        return false
      }
    },

    async fetchApiIcons() {
      this.loading = true
      try {
        const res = await this.$axios.get('/workforce/mdi-icons')
        if (res.data && res.data.data && Array.isArray(res.data.data.i)) {
          this.apiIcons = res.data?.data?.i.map((icon) => {
            return {
              text: icon.n,
              icon: `mdi-${icon.n}`
            }
          })
        } else {
          throw new Error('Invalid API response format')
        }
      } catch (error) {
        console.error('Error fetching API icons:', error)
      } finally {
        this.loading = false
      }
    }
  },

  async mounted() {
    // Fetch available icons from API
    this.loadFromLocalStorage()
    // await this.fetchApiIcons()
  }
}
</script>

<style>
.sidebar-preview {
  transition: all 0.3s ease;
}
.sidebar-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
}
.icon-selector {
  max-height: 400px;
  overflow-y: auto;
}
.icon-option {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}
.icon-option:hover {
  background-color: rgba(59, 130, 246, 0.1);
}
.icon-option.selected {
  background-color: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
}
.search-highlight {
  background-color: #fef08a;
}
.v-application .elevation-2 {
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
}
</style>
