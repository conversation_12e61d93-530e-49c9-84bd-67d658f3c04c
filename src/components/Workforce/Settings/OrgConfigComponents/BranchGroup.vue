<template>
  <div class="p-5 overflow-auto max-h-[70vh] px-4">
    <CustomSwitch
      :expansion="false"
      :heading="``"
      infoMessage="Define the branches in the organization to categorize the employees/teams."
      :title="`Enable Branch Mapping`"
      :leftOption="`Dectivate`"
      :rightOption="`Activate`"
      :value="receivedData?.enableBranchMapping"
      @switchValue="handleSwitch"
      :visibility="isFeatureVisible('CREATE_DIRECT_BRANCHES')"
    />
    <div class="my-4">
      <v-tabs
        v-model="tabs"
        align-with-title
        :background-color="$vuetify.theme.currentTheme.backGroundColor"
        :color="$vuetify.theme.currentTheme.primary"
      >
        <v-tab
          :ripple="false"
          v-for="(tab, index) in tabItems"
          :key="index"
          class="ml-2"
          :color="$vuetify.theme.currentTheme.primary"
        >
          {{ tab.name }}
        </v-tab>
        <v-tabs-slider color="#32D583"></v-tabs-slider>
      </v-tabs>
    </div>
    <div
      class="flex justify-between flex-col items-center w-full my-4"
      v-if="currentTab === 'branch'"
    >
      <div class="flex justify-between w-full mb-3">
        <h2 class="text-md font-semibold mb-0">Branches</h2>
        <v-text-field
          label="Search branch"
          v-model="searchTerm"
          outlined
          flat
          solo
          dense
          class="mx-16"
          clearable
          prepend-inner-icon="mdi-magnify"
          placeholder="Search branch"
        >
        </v-text-field>
        <v-btn
          @click="branchDrawer = true"
          :color="$vuetify.theme.currentTheme.primary"
          outlined
          :disabled="!enableSwitch"
          >Create Branch</v-btn
        >
      </div>
      <div class="flex flex-col w-full space-y-4 gap-4" v-if="!!filteredBranches?.length">
        <div
          v-for="(branch, index) in filteredBranches"
          :key="index"
          class="bg-white dark-bg-custom p-4 pt-0 pb-4 border border-blue-300 overflow-hidden w-full"
        >
          <div class="mb-2">
            <div class="flex justify-start gap-4 my-2">
              <v-label class="mt-2">({{ index + 1 }})</v-label>
              <div>
                <h4 class="text-lg font-bold">{{ branch.branchName }}</h4>
                <p class="text-sm pb-0 mb-0" v-if="branch.geoFencingData?.formatted_address">
                  <span class="font-semibold"> Address: </span>
                  {{ branch.geoFencingData?.formatted_address }}
                </p>
                <p class="text-sm" v-if="branch.geoFencingData?.radius">
                  <span class="font-semibold"> Radius: </span
                  >{{ branch.geoFencingData?.radius }} meters
                </p>
              </div>
              <div class="flex justify-end align-center">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn fab small text>
                      <v-icon
                        v-bind="attrs"
                        v-on="on"
                        @click="editBranchFunc(index, branch)"
                        :color="$vuetify.theme.currentTheme.primary"
                        outlined
                        hide-details
                        :disabled="!isBranchMappingEnabled"
                        >{{ branch.disabled ? 'mdi-pencil' : 'mdi-check' }}</v-icon
                      >
                    </v-btn>
                  </template>
                  <span>{{ branch.disabled ? 'Edit Branch' : 'Save Changes' }}</span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn fab small text>
                      <v-icon
                        v-bind="attrs"
                        v-on="on"
                        @click="confirmRemoveBranch(index)"
                        color="red"
                        outlined
                        :disabled="!isBranchMappingEnabled"
                        >mdi-delete</v-icon
                      >
                    </v-btn>
                  </template>
                  <span>Delete Branch</span>
                </v-tooltip>
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn fab small text>
                      <v-icon
                        v-bind="attrs"
                        v-on="on"
                        @click="openFencingModal(index)"
                        :color="branch.geoFencingData?.radius ? 'blue' : 'red'"
                        outlined
                        :disabled="!branchFence"
                        v-show="isGeoFeatureVisible('ENABLE_BRANCH_WISE_GEO_FENCING_MODE')"
                        >{{
                          branch.geoFencingData?.location?.lat
                            ? 'mdi-map-marker-radius'
                            : 'mdi-map-marker-off'
                        }}
                      </v-icon>
                    </v-btn>
                  </template>
                  <span>{{
                    branch.geoFencingData?.location?.lat ? 'Disable Geo-Fencing' : 'Set Geo-Fencing'
                  }}</span>
                </v-tooltip>
                <!-- <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-icon
                      v-bind="attrs"
                      v-on="on"
                      class="ml-4"
                      @click="openQRCodeDialog(branch)"
                      v-if="branch?.branchName"
                      v-show="isGeoFeatureVisible('CLOCKIN_WITH_QR_CODE')"
                    >
                      mdi-qrcode
                    </v-icon>
                  </template>
                  <span>View Qr-Code</span>
                </v-tooltip> -->
                <div
                  class="ml-4"
                  v-show="isGeoFeatureVisible('ENABLE_BRANCH_WISE_GEO_FENCING_MODE')"
                >
                  <v-switch
                    v-model="branch.geoFenceStatus"
                    :label="
                      branch.geoFenceStatus ? 'Geofence\u00A0Enabled' : 'Geofence\u00A0Disabled'
                    "
                    dense
                    :ripple="false"
                    inset
                    :disabled="!branchFence"
                    @change="handleGeofenceStatusChange(branch, index)"
                  ></v-switch>
                </div>
              </div>
            </div>
            <div class="w-auto">
              <div v-if="branch.documents?.length">
                <v-divider class="my-2"></v-divider>
                <p class="font-semibold">Branch Documents</p>
                <div class="text-sm mt-4 flex flex-wrap gap-2">
                  <div
                    v-for="(document, index) in branch.documents"
                    :key="document._id"
                    class="ml-2"
                  >
                    Document ({{ index + 1 }}) -
                    <a
                      :href="`${baseUrl}${document.url}`"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {{ document?.typeOfDocument || 'Unknown Type' }}
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div v-show="isBranchFeatureVisible('CREATE_BRANCH_TEAMS')">
              <v-divider class="my-2"></v-divider>
              <div class="w-auto">
                <div class="flex w-full justify-between items-center">
                  <div class="mt-2">
                    <div class="flex text-sm flex-wrap gap-2">
                      <v-chip
                        v-for="(team, teamIndex) in getTeamsByBranch(branch._id)"
                        :key="teamIndex"
                        label
                        class="white--text"
                      >
                        <span class="pr-2 text-lg text-black font-semibold">
                          {{ team.teamName }}
                        </span>
                        <v-icon
                          small
                          @click="confirmRemoveTeam(team, teamIndex)"
                          color="red"
                          class="ml-2"
                        >
                          $delete
                        </v-icon>
                        <v-icon
                          small
                          @click="editItem(team, teamIndex, branch)"
                          color="green"
                          class="ml-1"
                        >
                          $edit
                        </v-icon>
                      </v-chip>
                    </div>
                    <!-- <div v-else class="flex justify-center items-center">
                      <span>No team found</span>
                    </div> -->
                  </div>
                  <v-btn
                    v-show="isBranchFeatureVisible('LINK_TEAM_WITH_BRANCH')"
                    outlined
                    :color="$vuetify.theme.currentTheme.primary"
                    @click="addNewTeam(branch)"
                    >Create Team Inside {{ branch.branchName
                    }}<v-icon color="green">mdi-plus</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <NoData class="flex w-full justify-center" title="Nothing to Display" v-else />
    </div>
    <div v-show="isBranchFeatureVisible('CREATE_DIRECT_TEAMS')" v-if="currentTab === 'direactTeam'">
      <div class="flex w-full justify-between items-center">
        <p class="text-md font-semibold my-3">Direct teams</p>
        <v-btn outlined :color="$vuetify.theme.currentTheme.primary" @click="drawer = true">
          Create new team
        </v-btn>
      </div>
      <div class="flex justify-between mb-6">
        <div class="text-sm flex flex-wrap gap-2" v-if="!!teamWithNoBranch.length">
          <v-chip v-for="(team, index) in teamWithNoBranch" :key="index" label class="white--text">
            <span class="pr-2 text-lg text-black font-semibold">
              {{ team?.teamName }}
            </span>
            <v-icon small @click="confirmRemoveTeam(team, index)" color="red" class="ml-2">
              $delete
            </v-icon>
            <v-icon small @click="editItem(team, index, '')" color="green" class="ml-1">
              $edit
            </v-icon>
          </v-chip>
        </div>
        <NoData class="flex w-full justify-center" title="Nothing to Display" v-else />
      </div>
    </div>
    <AlertPopUp
      :showConfirmationDialog="showConfirmationBranchDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="'Are you sure you want to delete the branch?'"
      :confirmationActionText="'Delete'"
      :confirmationActionColor="'red'"
      :performAction="branchDelete"
      :cancel="cancel"
    />
    <MapRadiusModal
      :dialog="fencingModal"
      v-if="fencingModal"
      @mapClose="handleClose"
      @handleGeofence="saveGeofence"
      :value="branches[this.selectedBranchIndex].geoFencingData || {}"
    />
    <v-dialog v-model="showQRCodeDialog" max-width="500px">
      <v-card>
        <v-card-title>
          <span class="text-md">Branch QR Code</span>
        </v-card-title>
        <v-card-text>
          <QRCode :qrValue="qrCodeValue" :width="450" />
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-navigation-drawer
      :color="$vuetify.theme.currentTheme.navigationColor"
      class="teams-drawer"
      width="50%"
      height="100vh"
      v-model="drawer"
      absolute
      right
    >
      <div class="overflow-auto">
        <v-card-title class="text-xl font-semibold m-0">{{
          selectedTeamName ? 'Edit Team' : 'Add Team'
        }}</v-card-title>
        <div class="p-4 border flex justify-between">
          <div class="mt-2 w-full">
            <v-text-field
              class=""
              v-model="selectedTeamName"
              label="Team Name *"
              dense
              outlined
              hide-details
            ></v-text-field>
            <v-textarea
              class="mt-3"
              v-model="selectedTeamDescription"
              label="Description"
              dense
              outlined
              hide-details
              rows="3"
            ></v-textarea>
            <v-autocomplete
              class="mt-3"
              v-model="selectedNumber"
              :items="phoneNumbers"
              item-text="phoneNumber"
              item-value="_id"
              outlined
              dense
              hide-details
              label="Select Virtual Number For Telecalling"
            >
            </v-autocomplete>
            <v-select
              :items="licenceOptions"
              v-model="selectedLicense"
              label="Select License"
              item-text="name"
              outlined
              clearable
              dense
              hide-details
              class="mt-3"
            >
            </v-select>
            <v-autocomplete
              v-model="selectedDataCategories"
              class="mt-3"
              :items="dataCategories"
              item-text="name"
              item-value="_id"
              outlined
              dense
              multiple
              clearable
              hide-details
              label="Select Data Category"
            >
            </v-autocomplete>
          </div>
        </div>
        <div class="flex justify-end mr-4 mt-4">
          <v-btn @click="cancelDrawer" class="mr-2" outlined>Cancel</v-btn>
          <v-btn @click="saveTeam" :color="$vuetify.theme.currentTheme.primary" class="white--text"
            >Save</v-btn
          >
        </div>
        <AlertPopUp
          :showConfirmationDialog="showConfirmationDialog"
          :confirmationTitle="'Confirm Delete'"
          :confirmationMessage="'Are you sure you want to delete the team?'"
          :confirmationActionText="'Delete'"
          :confirmationActionColor="'red'"
          :performAction="teamDelete"
          :cancel="cancelTeam"
        />
      </div>
    </v-navigation-drawer>
    <v-navigation-drawer
      :color="$vuetify.theme.currentTheme.navigationColor"
      class="teams-drawer"
      width="50%"
      height="100vh"
      v-model="branchDrawer"
      absolute
      right
    >
      <div class="flex flex-col h-full p-4">
        <div>
          <div class="flex justify-between mb-4">
            <h2 class="text-xl font-semibold m-0">
              {{ editBranchDrawer ? 'Edit Branch' : 'Create Branch' }}
            </h2>
            <v-icon @click="closeBranchDrawer">mdi-close</v-icon>
          </div>
          <div v-if="!branchCreated || editBranchDrawer">
            <v-text-field
              v-model="newBranchName"
              label="Branch Name *"
              dense
              outlined
              hide-details
              :disabled="branchCreated && !editBranchDrawer"
              @input="handleChangeBranch"
            ></v-text-field>
            <v-textarea
              v-model="newBranchDescription"
              class="mt-3"
              label="Description *"
              dense
              outlined
              hide-details
              rows="3"
              :disabled="branchCreated && !editBranchDrawer"
              @input="handleChangeBranch"
            ></v-textarea>
            <v-text-field
              class="mt-3 w-full"
              v-model="typeOfDocument"
              dense
              outlined
              :disabled="branchCreated && !editBranchDrawer"
              label="Attachment Type"
              :rules="[(val) => (val || '').length > 0 || 'Type of document is required']"
              validate-on-blur
              v-show="editBranchDrawer"
            ></v-text-field>
            <v-file-input
              v-show="editBranchDrawer"
              v-model="attachment"
              truncate-length="15"
              class="mt-3 w-full"
              outlined
              label="Attachment"
              dense
              multiple
              :disabled="branchCreated && !editBranchDrawer"
              hide-details
            ></v-file-input>
            <v-btn
              v-if="!editBranchDrawer"
              :color="$vuetify.theme.currentTheme.primary"
              @click="addNewBranch"
              class="w-full mt-3 white--text"
            >
              Create Branch
            </v-btn>
            <v-btn
              v-else
              :color="$vuetify.theme.currentTheme.primary"
              @click="updateBranchFunc"
              class="w-full mt-3 white--text"
            >
              Edit Branch
            </v-btn>
          </div>
        </div>

        <!-- Team Creation Form -->
        <div v-if="branchCreated" class="mt-4 h-[65vh] overflow-y-scroll overflow-x-hidden">
          <h2 class="text-xl font-semibold mb-4">Create Team (Optional)</h2>

          <div v-for="(team, index) in teamsData" :key="index" class="mb-6">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-lg font-semibold">Team {{ index + 1 }}</h3>
              <v-btn icon small @click="removeTeam(index)" v-if="teamsData.length > 1">
                <v-icon color="red">mdi-trash-can</v-icon>
              </v-btn>
            </div>
            <v-text-field
              class=""
              v-model="team.teamName"
              label="Team Name *"
              dense
              outlined
              hide-details
            ></v-text-field>
            <v-textarea
              class="mt-3"
              v-model="team.teamDescription"
              label="Description"
              dense
              outlined
              hide-details
              rows="3"
            ></v-textarea>
            <v-autocomplete
              class="mt-3"
              v-model="team.connectVirtualNumber"
              :items="phoneNumbers"
              item-text="phoneNumber"
              item-value="_id"
              outlined
              dense
              hide-details
              label="link telecaller virtual number"
            >
            </v-autocomplete>
          </div>
          <div class="flex justify-end mr-3 mb-2">
            <v-btn @click="addTeam" small outlined>
              Add Team<v-icon color="green">mdi-plus</v-icon>
            </v-btn>
          </div>
          <v-btn color="primary" @click="createTeam" class="w-full white--text">
            Submit Teams
          </v-btn>
        </div>
      </div>
    </v-navigation-drawer>
  </div>
</template>

<script>
import AlertPopUp from '@/components/AlertPopUp'
import CustomSwitch from './CustomSwitch'
import MapRadiusModal from '../MapRadiusModal.vue'
import QRCode from './qrCode.vue'
import NoData from '@/components/NoData.vue'
import { isModuleFeatureAllowed } from '@/utils/common'

export default {
  components: {
    AlertPopUp,
    CustomSwitch,
    MapRadiusModal,
    QRCode,
    NoData
  },
  data() {
    return {
      orgCode: this.$storage.getUniversal('organization_code'),
      originalBranchData: {},
      showConfirmationDialog: false,
      branchIndexToDelete: null,
      fencingModal: false,
      selectedBranchIndex: null,
      showQRCodeDialog: false,
      qrCodeValue: '',
      newBranchName: '',
      newBranchDescription: '',
      enableSwitch: this.receivedData?.enableBranchMapping || false,
      branchFence: this.receivedData?.enableBranchGeofencing || false,
      drawer: false,
      teams: [{ teamName: '', teamDescription: '' }],
      selectedTeam: null,
      selectedBranch: null,
      selectedTeamName: '',
      selectedTeamDescription: '',
      selectedNumber: null,
      phoneNumbers: [],
      branchDrawer: false,
      editBranchDrawer: false,
      typeOfDocument: '',
      attachment: [],
      editBranchDataNew: null,
      branchCreated: false,
      baseUrl: process.env.VUE_APP_S3_BASE_URL,
      searchTerm: '',
      selectedLicense: null,
      selectedDataCategories: [],
      teamsData: [
        {
          teamName: '',
          teamDescription: '',
          connectVirtualNumber: null,
          branch: ''
        }
      ],
      branchIndex: null,
      showConfirmationBranchDialog: false,
      tabs: 0,
      tabItems: [
        {
          key: 'branch',
          name: 'Branch'
        },
        {
          key: 'direactTeam',
          name: 'Direct Team'
        }
      ]
    }
  },
  props: {
    receivedData: Object,
    branches: Array
  },
  computed: {
    currentTab() {
      return this.tabItems[this.tabs]?.key
    },
    isBranchMappingEnabled() {
      return this.receivedData?.enableBranchMapping
    },
    branchFencingValue() {
      return this.receivedData?.geoFencingEnabled
    },
    allBranchFencing() {
      return this.branchFencingValue && this.receivedData?.enableBranchGeofencing
    },
    teamWithNoBranch() {
      return this.teams.filter((el) => !el.branch)
    },
    filteredBranches() {
      if (!this.searchTerm) return this.branches
      return this.branches.filter((branch) =>
        branch.branchName.toLowerCase().includes(this.searchTerm.toLowerCase())
      )
    },
    licenceOptions() {
      const subCase = this.receivedData?.enabledSubCases?.find(
        (sub) => sub.useCase === 'collection'
      )
      const options =
        subCase?.subCase.map((el) => {
          return { name: el.name, value: el._id, dataCategories: el.dataCategories || [] }
        }) || []
      return options
    },
    dataCategories() {
      if (!this.selectedLicense) return []
      const selectedOption = this.licenceOptions.find(
        (option) => option.value === this.selectedLicense
      )
      return selectedOption?.dataCategories || []
    }
  },
  watch: {
    receivedData: {
      handler(newVal) {
        if (newVal !== null) {
          this.receivedData = newVal
          this.branchFence = this.receivedData?.enableBranchGeofencing || false
        }
      },
      deep: true,
      immediate: true
    }
  },
  async mounted() {
    await this.fetchTeams()
    await this.getVoiceNumbers()
  },
  methods: {
    removeTeam(index) {
      if (this.teamsData.length > 1) {
        this.teamsData.splice(index, 1)
      }
    },
    async createTeam() {
      this.teamsData.map((data) => {
        data.branch = this.selectedBranch?._id
      })
      try {
        let teamPayload
        if (this.selectedBranch?._id) {
          teamPayload = this.teamsData
        } else {
          teamPayload = [
            {
              teamName: this.selectedTeamName,
              teamDescription: this.selectedTeamDescription,
              connectVirtualNumber: this.selectedNumber,
              dataCategories: this.selectedDataCategories || [],
              linkedLicense: this.selectedLicense || null
            }
          ]
        }
        const response = await this.$axios.post('/workforce/team', teamPayload)
        if (response?.data?.success) {
          this.$toast.success(response?.data?.message)
          this.branchDrawer = false
          this.drawer = false
          this.selectedTeam = null
          this.selectedTeamName = ''
          this.selectedBranch = null
          this.selectedTeamDescription = ''
          this.selectedNumber = null
          this.selectedBranch = null
          this.branchCreated = false
          this.newTeam()
          await this.fetchTeams()
        }
      } catch (error) {
        console.log(error)
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
      }
    },
    addTeam() {
      this.teamsData.push({ teamName: '', teamDescription: '' })
    },

    newTeam() {
      this.teamsData = [
        {
          teamName: '',
          teamDescription: '',
          connectVirtualNumber: null,
          branch: ''
        }
      ]
    },

    editBranchFunc(index, branch) {
      this.branchDrawer = true
      this.editBranchDrawer = true
      this.editBranchDataNew = branch
      this.newBranchName = branch?.branchName
      this.newBranchDescription = branch.branchDescription
      this.attachment = branch.attachment
      this.typeOfDocument = branch.typeOfDocument
      this.branchCreated = false
      this.teamsData = this.getTeamsByBranch(branch._id)

      this.branchIndex = index
    },

    closeBranchDrawer() {
      this.branchDrawer = false
      this.editBranchDrawer = false
      this.newBranchName = ''
      this.newBranchDescription = ''
      this.newTeam()
      this.selectedBranch = null
    },
    cancelDrawer() {
      this.drawer = false
      this.selectedTeamName = ''
      this.selectedTeamDescription = ''
      this.selectedNumber = null
    },
    async getVoiceNumbers() {
      try {
        const response = await this.$axios.get('/workforce/tele-caller/credentials')
        if (response.data.success) {
          this.phoneNumbers = response?.data?.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    openDrawer() {
      this.drawer = true
    },
    addNewTeam(branch) {
      this.selectedTeam = null
      this.selectedTeamName = ''
      this.selectedTeamDescription = ''
      this.selectedBranch = branch
      this.selectedNumber = null
      this.drawer = true
    },
    editItem(team, teamIndex, branch) {
      this.selectedTeam = team
      this.selectedBranch = branch
      this.selectedTeamName = team.teamName
      this.selectedTeamDescription = team.teamDescription
      this.selectedNumber = team.connectVirtualNumber
      this.selectedDataCategories = team.dataCategories || []
      this.selectedLicense = team.linkedLicense || null
      this.drawer = true
    },

    cancelTeam() {
      this.drawer = false
      this.selectedTeamName = ''
      this.selectedTeamDescription = ''
      this.showConfirmationDialog = false
      this.selectedTeam = null

      this.selectedNumber = null
    },
    async teamDelete() {
      const team = this.selectedTeam
      try {
        if (!team?._id) {
          return
        }
        const response = await this.$axios.delete(`/workforce/team/${team._id}`)
        await this.fetchTeams()
        const successMessage = response?.data?.message || 'Team deleted successfully'
        this.$toast.success(successMessage)
        this.showConfirmationDialog = false
        this.selectedTeam = null
      } catch (error) {
        console.error('Error deleting team:', error)
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
      }
    },
    async fetchTeams() {
      try {
        const response = await this.$axios.get('/workforce/teams')
        this.teams = response.data.teams.map((team) => ({
          ...team,
          disabled: true
        }))
      } catch (error) {
        console.error('Error fetching teams:', error)
      }
    },
    async updateTeam(team) {
      try {
        const payload = {
          teamName: this.selectedTeamName,
          teamDescription: this.selectedTeamDescription,
          connectVirtualNumber: this.selectedNumber,
          dataCategories: this.selectedDataCategories || [],
          linkedLicense: this.selectedLicense || null
        }
        const response = await this.$axios.put(`/workforce/team/${team._id}`, payload)
        const successMessage = response?.data?.message || 'Team Updated successfully'
        this.$toast.success(successMessage)
        this.drawer = false
        this.selectedTeam = null
        this.selectedTeamName = ''
        this.selectedBranch = null
        this.selectedTeamDescription = ''
        this.selectedNumber = null
        this.selectedBranch = null
        this.branchCreated = false
        await this.fetchTeams()
      } catch (error) {
        console.error('Error updating team:', error)
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
      }
    },

    async saveTeam() {
      if (this.selectedTeam) {
        await this.updateTeam(this.selectedTeam)
      } else if (this.selectedBranch) {
        this.teamsData = []
        const payload = {
          teamName: this.selectedTeamName,
          teamDescription: this.selectedTeamDescription,
          branch: this.selectedBranch?._id,
          connectVirtualNumber: this.selectedNumber,
          dataCategories: this.selectedDataCategories || [],
          linkedLicense: this.selectedLicense || null
        }
        this.teamsData.push(payload)
        await this.createTeam()
      } else {
        await this.createTeam()
      }
    },
    confirmRemoveTeam(team) {
      this.selectedTeam = team
      this.showConfirmationDialog = true
    },

    getTeamsByBranch(branchId) {
      if (!branchId) return
      return this.teams.filter((team) => team.branch === branchId)
    },
    handleChangeBranch() {
      // this.$emit("orgBranches", this.branches);
    },

    async handleBranchAttachment(branch) {
      if (!this.typeOfDocument) {
        this.$toast.error('Type of document is required')
        return
      }

      const attachments = this.attachment

      try {
        const uploadPromises = attachments.map(async (file) => {
          const formData = new FormData()
          formData.append('attachments', file)
          formData.append('branchId', branch._id)
          formData.append('typeOfDocument', this.typeOfDocument)

          const response = await this.$axios.post(`/workforce/add/org/documents`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          })
          return {
            _id: response.data?.doc?._id,
            url: response.data?.doc?.doc_physical_path,
            typeOfDocument: this.typeOfDocument
          }
        })

        const uploadedDocuments = await Promise.all(uploadPromises)
        branch.documents.push(...uploadedDocuments)

        this.$toast.success('Documents uploaded successfully for the branch')
      } catch (error) {
        console.error('An error occurred:', error)
        this.$toast.error(error?.response?.data?.message)
      }
    },
    isGeoFeatureVisible(feature) {
      return isModuleFeatureAllowed('ATTENDANCE_MANAGEMENT', feature)
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('BRANCH_MANAGEMENT', feature)
    },

    isBranchFeatureVisible(feature) {
      return isModuleFeatureAllowed('TEAM_MANAGEMENT', feature)
    },
    openQRCodeDialog(branch) {
      this.qrCodeValue = this.getQRCodeValue(branch)
      this.showQRCodeDialog = true
    },
    getQRCodeValue(branch) {
      const branchName = branch?.branchName
      const branchLat = branch?.geoFencingData?.location?.lat
      const branchLng = branch?.geoFencingData?.location?.lng
      const branchRadius = branch?.geoFencingData?.radius
      if (branchLat && branchLng && branchRadius) {
        return `${process.env.VUE_APP_BASE_URL}/mark-attendance?orgCode=${
          this.orgCode
        }&branchName=${encodeURIComponent(
          branchName
        )}&lat=${branchLat}&lng=${branchLng}&radius=${branchRadius}`
      }
      if (branchLat || branchLng) {
        return `${process.env.VUE_APP_BASE_URL}/mark-attendance?orgCode=${
          this.orgCode
        }&branchName=${encodeURIComponent(branchName)}&lat=${branchLat}&lng=${branchLng}`
      }

      return `${process.env.VUE_APP_BASE_URL}/mark-attendance?orgCode=${
        this.orgCode
      }&branchName=${encodeURIComponent(branchName)}`
    },
    addNewBranch() {
      if (!this.newBranchName) {
        this.$toast.error('Branch name is required.')
        return
      }
      if (!this.newBranchDescription) {
        this.$toast.error('Branch description is required.')
        return
      }
      this.branches.push({
        branchName: this.newBranchName,
        branchDescription: this.newBranchDescription,
        disabled: false
      })
      this.editBranch(this.branches?.length - 1)
      this.newBranchName = ''
      this.newBranchDescription = ''
      this.branchId = ''
    },
    confirmRemoveBranch(index) {
      this.branchIndexToDelete = index
      this.showConfirmationBranchDialog = true
    },
    cancel() {
      this.showConfirmationBranchDialog = false
      this.branchIndexToDelete = null
      this.selectedBranch = null
    },
    async branchDelete() {
      const index = this.branchIndexToDelete
      if (index !== null) {
        try {
          const branch = this.branches[index]
          if (!branch?._id) {
            this.branches.splice(index, 1)
            this.showConfirmationDialog = false
            this.branchIndexToDelete = null
            return
          }
          const response = await this.$axios.delete(`/workforce/branch/${branch._id}`)
          this.branches.splice(index, 1)
          this.$emit('orgBranches', this.branches)
          const successMessage = response?.data?.message || 'Branch deleted successfully'
          this.$toast.success(successMessage)
          this.showConfirmationBranchDialog = false
          this.branchIndexToDelete = null
        } catch (error) {
          console.error('Error deleting branch:', error)
          const errorMessage = error.response.data.message || error
          this.$toast.error(errorMessage)
        }
      }
    },
    async updateBranch(branch, index) {
      try {
        const response = await this.$axios.put(`/workforce/branch/${branch._id}`, branch)
        this.branches[index].disabled = true
        this.originalBranchData[index] = { ...branch }
        this.$toast.success(response.data.message)
        if (this.typeOfDocument) {
          this.handleBranchAttachment(this.editBranchDataNew)
        }
        this.branchDrawer = false
        this.editBranchDrawer = false
        this.editBranchDataNew = null
        this.branchCreated = false
      } catch (error) {
        console.error('Error updating branch:', error)
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
        this.branchCreated = false
      }
    },
    async createBranch(branch, index) {
      try {
        const branchPayload = [branch]
        const response = await this.$axios.post('/workforce/branch', branchPayload)
        this.$set(this.branches, index, {
          ...response.data.branch[0],
          disabled: true
        })
        this.originalBranchData[index] = { ...response.data.branch[0] }
        this.branches[index].disabled = true

        this.selectedBranch = response.data.branch[0]
        const successMessage = response?.data?.message || 'Branch Created successfully'
        this.$toast.success(successMessage)
        this.branchCreated = true
      } catch (error) {
        console.error('Error creating branch:', error)
        this.branchCreated = false
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
      }
    },
    updateBranchFunc() {
      this.editBranchDataNew.branchName = this.newBranchName
      this.editBranchDataNew.branchDescription = this.newBranchDescription
      this.updateBranch(this.editBranchDataNew, this.branchIndex)
    },

    editBranch(index) {
      const branch = this.branches[index]
      if (!branch.disabled) {
        if (JSON.stringify(branch) !== JSON.stringify(this.originalBranchData[index])) {
          if (branch._id) {
            this.updateBranch(branch, index)
          } else {
            this.createBranch(branch, index)
          }
        } else {
          branch.disabled = true
        }
      } else {
        this.originalBranchData[index] = { ...branch }
        branch.disabled = false
      }
    },
    openFencingModal(index) {
      this.selectedBranchIndex = index
      this.fencingModal = true
    },
    handleClose() {
      this.fencingModal = !this.fencingModal
    },
    async saveGeofence(val) {
      if (this.selectedBranchIndex !== null) {
        if (val.location?.lat && val.location?.lng) {
          this.branches[this.selectedBranchIndex].geoFencingData = val
        } else {
          this.branches[this.selectedBranchIndex].geoFencingData = {}
        }

        const data = this.branches[this.selectedBranchIndex]
        await this.updateBranch(data, this.selectedBranchIndex)
        this.fencingModal = false
      }
    },
    handleSwitch(val) {
      this.enableSwitch = val.value
      this.$emit('branchSwitchValue', val)
    },
    async handleGeofenceStatusChange(branch, index) {
      await this.updateBranch(branch, index)
    }
  }
}
</script>

<style></style>
