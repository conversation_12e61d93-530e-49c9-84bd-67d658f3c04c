<template>
  <div>
    <div class="mt-4" v-show="visibility">
      <div class="flex items-center border border-blue-200 bg-white dark-bg-custom">
        <span v-tooltip="{ text: infoMessage }">
          <v-icon class="ml-4">mdi-information</v-icon>
        </span>
        <v-slider
          v-model="sliderModel"
          :label="sliderLabel"
          :step="step"
          :max="max"
          :min="min"
          class="pl-4 pr-8 py-4 h-16"
          thumb-label="always"
          tick-size="4"
          @change="sliderChange"
          :color="$vuetify.theme.currentTheme.primary"
        ></v-slider>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      sliderModel: null
    }
  },
  watch: {
    value(newValue) {
      this.sliderModel = newValue
    }
  },
  props: {
    sliderLabel: String,
    infoMessage: String,
    value: Number,
    max: Number,
    min: Number,
    step: { type: Number, default: null },
    visibility: { type: Boolean, default: true }
  },
  methods: {
    sliderChange() {
      this.$emit('emitSlider', this.sliderModel)
    }
  },
  mounted() {
    this.sliderModel = this.$props.value
  }
}
</script>
<style></style>
