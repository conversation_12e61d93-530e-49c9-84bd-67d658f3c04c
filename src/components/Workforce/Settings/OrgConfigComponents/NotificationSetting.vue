<template>
  <div>
    <div class="p-5 overflow-auto max-h-[70vh]">
      <div>
        <div>
          <div>
            <h6 class="mb-4 font-semibold">Select notification triggers</h6>
            <!-- add the below code when new keys are available in the plan -->
            <!-- v-show="isFeatureVisible(`SET_${key}_NOTIFICATION`)" -->
            <v-list>
              <v-list-item-group>
                <v-list-item
                  v-for="(label, key, index) in notify"
                  :key="key"
                  class="space-x-4 border border-blue-200 h-14"
                >
                  <v-label class="">{{ index + 1 }}</v-label>
                  <v-checkbox
                    :color="$vuetify.theme.currentTheme.primary"
                    :label="label"
                    v-model="selectedNotification[key]"
                    @change="emitNotificationData"
                  ></v-checkbox>
                </v-list-item>
              </v-list-item-group>
            </v-list>
          </div>
        </div>
      </div>

      <div class="pb-2 mt-6 w-full" v-show="isFeatureVisible('CEATE_NOTIFICATION_TRIGGER')">
        <div class="p-4 border-2">
          <h6 class="mb-4 font-semibold">Create new notification configurartion</h6>
          <v-form ref="form" lazy-validation>
            <v-row dense no-gutters class="gap-4 mb-4">
              <v-col cols="" class="pb-0 px-0 w-full">
                <v-autocomplete
                  v-model="module"
                  :items="modules"
                  :rules="rules.module"
                  label="module"
                  placeholder="module Name"
                  hide-details
                  outlined
                  dense
                  clearable
                  class=""
                  required
                  @change="updateFilteredNotifications"
                >
                </v-autocomplete>
              </v-col>

              <v-col cols="" class="pb-0 px-0">
                <v-autocomplete
                  v-model="notificationType"
                  :items="filteredNotifications"
                  :rules="rules.notificationType"
                  label="Type"
                  placeholder="Type"
                  hide-details
                  outlined
                  dense
                  clearable
                  class=""
                >
                </v-autocomplete>
              </v-col>
            </v-row>
            <v-row dense no-gutters class="gap-4 mb-4">
              <v-col cols="" class="pb-0 px-0">
                <v-autocomplete
                  v-model="notifyBefore"
                  :items="formattedNotifyBeforeData"
                  :rules="rules.notifyBefore"
                  label="Notify Before"
                  item-text="text"
                  item-value="value"
                  placeholder="Notify Before"
                  hide-details
                  outlined
                  dense
                  clearable
                >
                </v-autocomplete>
              </v-col>
            </v-row>
            <v-row dense no-gutters class="gap-4 mb-4">
              <v-col cols="" class="pb-0 px-0">
                <v-combobox
                  v-model="newNotificationUsers"
                  :label="`Search user`"
                  :items="usersData"
                  :item-text="getEmpFullName"
                  item-value="_id"
                  :rules="rules.notifyGroupOfPeople"
                  @update:search-input="debounceUser"
                  :search-input.sync="searchSelectedUser"
                  hide-no-data
                  hide-details
                  outlined
                  dense
                  clearable
                  multiple
                ></v-combobox>
              </v-col>
            </v-row>
          </v-form>
          <div class="flex justify-end">
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              @click="addModule"
            >
              <v-icon>mdi-plus</v-icon>
              Add notification config to below list
            </v-btn>
          </div>
        </div>
      </div>
      <div
        v-if="notificationModule?.length > 0"
        class="mt-4"
        v-show="isFeatureVisible('ENABLE_APPROVAL_VERIFICATION')"
      >
        <h6 class="mb-4 font-semibold">List of notification configs</h6>
        <div class="mt-4 grid grid-cols-2 gap-4">
          <div class="" v-for="(item, index) in notificationModule" :key="index">
            <div class="border-2 p-4">
              <div class="flex justify-between items-center mb-2">
                <span class="mr-2 font-semibold"
                  >({{ index + 1 }}) {{ item.module }} module
                  {{ item.notificationType }} notification config</span
                >
                <v-icon style="color: rgb(219, 77, 77)" @click="removeModule(index)"
                  >mdi-delete</v-icon
                >
              </div>
              <v-col cols="" class="pb-0 px-0 w-full">
                <v-autocomplete
                  v-model="item.module"
                  :items="modules"
                  label="Module"
                  hide-details
                  outlined
                  dense
                  clearable
                  class=""
                  disabled
                >
                </v-autocomplete>
              </v-col>
              <v-col cols="" class="pb-0 px-0">
                <v-text-field
                  v-model="item.notificationType"
                  label="Type"
                  hide-details
                  outlined
                  dense
                  clearable
                  class=""
                  disabled
                >
                </v-text-field>
              </v-col>
              <v-col cols="" class="pb-0 px-0">
                <v-autocomplete
                  v-model="item.notifyBefore"
                  :items="formattedNotifyBeforeData"
                  label="Notify Before"
                  hide-details
                  outlined
                  dense
                  clearable
                  class=""
                  @change="updateObjectField(item._id, notifyBefore, item)"
                >
                </v-autocomplete>
              </v-col>
              <v-col cols="" class="pb-0 px-0">
                <v-combobox
                  v-model="item.notifyGroupOfPeople"
                  :label="`Search user`"
                  :items="usersData || item.notifyGroupOfPeople"
                  :item-text="getEmpFullName"
                  item-value="_id"
                  :rules="rules.notifyGroupOfPeople"
                  @update:search-input="debounceUser"
                  :search-input.sync="searchUser"
                  hide-details
                  outlined
                  dense
                  clearable
                  multiple
                  class=""
                  @change="updateObjectField(item._id, notifyGroupOfPeople, item)"
                >
                </v-combobox>
              </v-col>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { fullName, debounce, isModuleFeatureAllowed } from '@/utils/common'
export default {
  data() {
    return {
      notify: {
        SMS: 'SMS',
        EMAIL: 'E-Mail',
        IN_APP: 'In-App Message',
        WHATSAPP: 'Whatsapp Message',
        PUSH_NOTIFICATION: 'Push Notification'
      },
      selectedNotification: {
        SMS: false,
        EMAIL: false,
        INAPP: false
      },
      userData: this.$storage.getUniversal('user'),
      notificationModule: [],
      modules: ['AMC', 'TEAM_REVIEW'],
      module: '',
      notifyBeforeData: [1, 2, 3, 4],
      notifyBefore: null,
      notifyAfterData: [1, 2, 3, 4],
      notifyAfter: null,
      frequency: '',
      notificationTypesData: [
        'SERVICE_DUE',
        'PAYMENT_DUE',
        'EXPIRING',
        'EMPLOYEE_ATTENDANCE_EMAIL_REPORT'
      ],
      notificationType: '',
      notifyGroupOfPeople: [],
      newNotificationUsers: [],
      usersData: [],
      messageTemplate: '',
      filteredNotifications: [],
      searchUser: '',
      searchSelectedUser: ''
    }
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    }
  },
  watch: {
    receivedData(newVal) {
      this.populateField(newVal)
    }
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('NOTIFICATION_MANAGEMENT', feature)
    },
    updateFilteredNotifications() {
      this.filteredNotifications = this.filterNotifications(this.module)
    },
    populateField(val) {
      this.selectedNotification.SMS = val?.isSmsNotificationEnable
      this.selectedNotification.EMAIL = val?.isEmailNotificationEnable
      this.selectedNotification.INAPP = val?.isInAppNotificationEnable
      this.notificationModule = val?.notificationConfig
      if (this.notificationModule?.length > 0) {
        this.notifyGroupOfPeople = val.notificationConfig?.flatMap((el) => el.notifyGroupOfPeople)
      }
    },
    filterNotifications(selectedModule) {
      return this.notificationTypesData.filter((notification) => {
        return !this.notificationModule.some(
          (item) => item.module === selectedModule && item.notificationType === notification
        )
      })
    },
    async addModule() {
      const isValid = this.$refs.form.validate()
      if (!isValid) return

      if (!Array.isArray(this.notificationModule)) {
        this.notificationModule = []
      }
      const userIds = this.newNotificationUsers.map((u) => u._id)

      const result = this.usersData
        .filter((user) => userIds.includes(user._id))
        .map((user) => ({
          _id: user._id,
          firstName: user.firstName,
          fullName: user.fullName,
          lastName: user.lastName,
          mobile: user.mobile,
          email: user.email,
          id: user.id
        }))

      if (
        this.module !== '' &&
        this.notifyBefore !== null &&
        this.notificationType !== '' &&
        this.newNotificationUsers.length > 0
      ) {
        this.notificationModule.push({
          module: this.module,
          notifyBefore: this.notifyBefore,
          notifyAfter: this.notifyAfter,
          frequency: this.frequency,
          notificationType: this.notificationType,
          notifyGroupOfPeople: result,
          messageTemplate: this.messageTemplate
        })

        // Clear the selected values after adding
        this.module = ''
        this.notifyBefore = null
        this.notifyAfter = null
        this.frequency = ''
        this.notificationType = ''
        this.newNotificationUsers = []
        this.messageTemplate = ''
        this.$emit('notification', this.notificationModule)
        this.$refs.form.resetValidation()
      } else {
        this.$toast.error('Please fill all the details')
      }
    },

    updateObjectField(id, fieldToUpdate, newValue) {
      const index = this.notificationModule.findIndex((obj) => obj._id === id)
      if (index !== -1) {
        if (fieldToUpdate in this.notificationModule[index]) {
          this.notificationModule[index][fieldToUpdate] = newValue
        }
      }
      this.$emit('notification', this.notificationModule)
    },

    debounceUser: debounce(async function (text) {
      if (!text) return true
      this.fetchUsers()
    }, 1000),
    async fetchUsers() {
      try {
        const params = {}

        if (this.searchUser || this.searchSelectedUser) {
          params.search = this.searchUser || this.searchSelectedUser
        }

        if (this.userData?.branch && !this.userData?.role?.includes('WFM_ADMIN')) {
          params.branch = this.userData?.branch
        }
        const response = await this.$axios.get('/workforce/v2/users', {
          params
        })
        this.usersData = response.data?.users
      } catch (error) {
        console.log(error)
      }
    },
    getEmpFullName(item) {
      return fullName(item)
    },
    removeModule(index) {
      this.notificationModule.splice(index, 1)
      this.$emit('notification', this.notificationModule)
    },
    getNotifybefore(item) {
      return `${item}d`
    },
    emitNotificationData() {
      this.$emit('selectedNotification', this.selectedNotification)
    }
  },
  computed: {
    rules() {
      return {
        module: [(val) => (val || '').length > 0 || 'Module name is required'],
        notifyBefore: [(value) => !!value || 'Notify Before is required'],
        notifyAfter: [(val) => val || '' || 'notifyAfter line 1 is required'],
        frequency: [(val) => (val || '').length > 0 || 'frequency is required'],
        notificationType: [(val) => (val || '').length > 0 || 'notificationType is required'],
        notifyGroupOfPeople: [(val) => (val || '').length > 0 || 'notifyGroupOfPeople is required'],
        messageTemplate: [(val) => (val || '').length > 0 || 'messageTemplate is required']
      }
    },
    formattedNotifyBeforeData() {
      return this.notifyBeforeData.map((item) => ({
        text: `${item}d`,
        value: item
      }))
    },
    formattedNotifyAfterData() {
      return this.notifyAfterData.map((item) => ({
        text: `${item}d`,
        value: item
      }))
    }
  },
  mounted() {
    this.populateField(this.receivedData)
  }
}
</script>
<style></style>
