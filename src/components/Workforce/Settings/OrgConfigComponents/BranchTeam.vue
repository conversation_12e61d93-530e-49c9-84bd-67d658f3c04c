<template>
  <div>
    <!-- Main Content -->
    <!-- Header -->

    <div class="pt-4">
      <v-row>
        <!-- Left Sidebar - Branch List -->
        <v-col cols="12" md="4" lg="3" class="pr-4">
          <v-card flat>
            <v-card-title class="text-lg font-semibold text-gray-800 pb-2"> Branches </v-card-title>
            <v-card-text class="pt-0">
              <div class="space-y-3">
                <v-card
                  v-for="branch in branches"
                  :key="branch.id"
                  class="cursor-pointer"
                  :style="
                    selectedBranch?.id === branch.id
                      ? `border: 1px solid ${$vuetify.theme.currentTheme.primary} !important`
                      : ''
                  "
                  :class="{
                    'bg-gray-50': selectedBranch?.id === branch.id
                  }"
                  outlined
                  @click="selectBranch(branch)"
                >
                  <v-card-text class="py-3">
                    <h3 class="font-semibold text-gray-800 mb-1">{{ branch.name }}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ branch.location }}</p>
                    <v-chip small color="grey lighten-3" class="text-xs">
                      {{ branch.teams.length }} teams
                    </v-chip>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Right Panel - Branch Details -->
        <v-col cols="12" md="8" lg="9">
          <v-card class="h-full" v-if="selectedBranch" flat>
            <!-- Branch Header -->
            <v-card-title class="border-b border-gray-200 pb-4">
              <div class="flex-1">
                <h2 class="text-xl font-bold text-gray-800 mb-2">{{ selectedBranch.name }}</h2>
                <div class="space-y-1 text-sm text-gray-600">
                  <p><strong>Location:</strong> {{ selectedBranch.location }}</p>
                  <p><strong>Manager:</strong> {{ selectedBranch.manager }}</p>
                  <p>
                    <strong>Contact:</strong> {{ selectedBranch.email }} |
                    {{ selectedBranch.phone }}
                  </p>
                </div>
              </div>
              <v-btn color="success" @click="showTeamModal = true" class="font-medium">
                <v-icon left>mdi-plus</v-icon>
                Create Team
              </v-btn>
            </v-card-title>

            <v-card-text class="flex-1 overflow-y-auto">
              <!-- Team Management -->
              <div class="mt-4">
                <h3 class="text-lg font-semibold mb-4">Teams in this Branch</h3>

                <!-- Team List -->
                <div class="space-y-2 mb-6">
                  <v-card
                    v-for="team in selectedBranch.teams"
                    :key="team.id"
                    :class="[
                      'team-item-hover cursor-pointer transition-colors duration-200',
                      selectedTeam?.id === team.id ? 'bg-blue-50 border-blue-200' : ''
                    ]"
                    outlined
                    @click="selectTeam(team)"
                  >
                    <v-card-text class="py-3">
                      <h4 class="font-semibold text-gray-800">{{ team.name }}</h4>
                      <p class="text-sm text-gray-600">
                        Team Lead: {{ team.lead }} | {{ team.members.length }} members
                      </p>
                    </v-card-text>
                  </v-card>
                </div>

                <!-- Team Details -->
                <v-card v-if="selectedTeam" class="bg-gray-50" outlined>
                  <v-card-title class="text-lg font-semibold">
                    {{ selectedTeam.name }} Details
                  </v-card-title>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="6">
                        <v-select
                          v-model="selectedTeam.lead"
                          :items="availableMembers"
                          label="Team Lead"
                          outlined
                          dense
                        ></v-select>
                      </v-col>
                    </v-row>

                    <!-- Team Members -->
                    <div class="mt-4">
                      <h4 class="font-semibold mb-3">Members</h4>
                      <div class="space-y-2">
                        <div
                          v-for="(member, memberIndex) in selectedTeam.members"
                          :key="memberIndex"
                          class="flex justify-between items-center py-2 member-divider"
                        >
                          <span class="text-sm">
                            {{ member }}
                            <v-chip
                              v-if="member === selectedTeam.lead"
                              x-small
                              color="primary"
                              class="ml-2"
                            >
                              Lead
                            </v-chip>
                          </span>
                          <v-btn text x-small color="error" @click="removeMember(memberIndex)">
                            Remove
                          </v-btn>
                        </div>

                        <v-select
                          v-model="newMember"
                          :items="availableMembersToAdd"
                          label="Add a member..."
                          outlined
                          dense
                          clearable
                          @change="addMember"
                        ></v-select>
                      </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-end space-x-2 mt-6">
                      <v-btn outlined color="error" @click="deleteTeam"> Delete Team </v-btn>
                      <v-btn color="primary" @click="saveTeamChanges"> Save Changes </v-btn>
                    </div>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
          </v-card>

          <!-- No Branch Selected -->
          <v-card v-else class="h-full flex items-center justify-center">
            <v-card-text class="text-center">
              <v-icon size="64" color="grey lighten-2" class="mb-4">mdi-office-building</v-icon>
              <p class="text-lg text-gray-500">Select a branch to view details</p>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </div>

    <!-- Add Branch Modal -->
    <v-dialog v-model="showBranchModal" max-width="500px" persistent>
      <v-card>
        <v-card-title class="text-lg font-semibold">
          Add New Branch
          <v-spacer></v-spacer>
          <v-btn icon @click="closeBranchModal">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-form ref="branchForm" v-model="branchFormValid">
            <v-text-field
              v-model="newBranch.name"
              label="Branch Name"
              outlined
              :rules="[(v) => !!v || 'Branch name is required']"
            ></v-text-field>
            <v-text-field
              v-model="newBranch.location"
              label="Location"
              outlined
              :rules="[(v) => !!v || 'Location is required']"
            ></v-text-field>
            <v-select
              v-model="newBranch.manager"
              :items="availableManagers"
              label="Manager"
              outlined
              :rules="[(v) => !!v || 'Manager is required']"
            ></v-select>
            <v-text-field
              v-model="newBranch.email"
              label="Contact Email"
              type="email"
              outlined
              :rules="emailRules"
            ></v-text-field>
            <v-text-field
              v-model="newBranch.phone"
              label="Phone"
              type="tel"
              outlined
              :rules="[(v) => !!v || 'Phone is required']"
            ></v-text-field>
          </v-form>
        </v-card-text>
        <v-card-actions class="px-6 pb-4">
          <v-spacer></v-spacer>
          <v-btn outlined @click="closeBranchModal">Cancel</v-btn>
          <v-btn color="primary" @click="saveBranch" :disabled="!branchFormValid">
            Save Branch
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Add Team Modal -->
    <v-dialog v-model="showTeamModal" max-width="500px" persistent>
      <v-card>
        <v-card-title class="text-lg font-semibold">
          Create New Team
          <v-spacer></v-spacer>
          <v-btn icon @click="closeTeamModal">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-form ref="teamForm" v-model="teamFormValid">
            <v-text-field
              v-model="newTeam.name"
              label="Team Name"
              outlined
              :rules="[(v) => !!v || 'Team name is required']"
            ></v-text-field>
            <v-select
              v-model="newTeam.lead"
              :items="availableMembers"
              label="Team Lead"
              outlined
              :rules="[(v) => !!v || 'Team lead is required']"
            ></v-select>
            <v-select
              v-model="newTeam.members"
              :items="availableMembers"
              label="Initial Members"
              outlined
              multiple
              chips
              hint="Select initial team members"
              persistent-hint
            ></v-select>
          </v-form>
        </v-card-text>
        <v-card-actions class="px-6 pb-4">
          <v-spacer></v-spacer>
          <v-btn outlined @click="closeTeamModal">Cancel</v-btn>
          <v-btn color="primary" @click="saveTeam" :disabled="!teamFormValid"> Create Team </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import Vue from 'vue'
// import Vuetify from 'vuetify'

export default {
  name: 'BranchGroup',
  // vuetify: new Vuetify({
  //   theme: {
  //     themes: {
  //       light: {
  //         secondary: '#3498db',
  //         accent: '#2ecc71',
  //         error: '#e74c3c',
  //         warning: '#f39c12',
  //         info: '#3498db',
  //         success: '#2ecc71'
  //       }
  //     }
  //   }
  // }),
  data() {
    return {
      // Modal states
      showBranchModal: false,
      showTeamModal: false,
      branchFormValid: false,
      teamFormValid: false,

      // Selected items
      selectedBranch: null,
      selectedTeam: null,
      newMember: null,

      // Form data
      newBranch: {
        name: '',
        location: '',
        manager: '',
        email: '',
        phone: ''
      },
      newTeam: {
        name: '',
        lead: '',
        members: []
      },

      // Available options
      availableManagers: ['John Smith', 'Sarah Johnson', 'Michael Brown', 'Emily Davis'],
      availableMembers: [
        'Sarah Johnson',
        'Michael Brown',
        'Emily Davis',
        'Robert Wilson',
        'Alex Turner',
        'Lisa Wong',
        'John Doe',
        'Jane Smith',
        'David Lee'
      ],

      // Email validation rules
      emailRules: [
        (v) => !!v || 'Email is required',
        (v) => /.+@.+\..+/.test(v) || 'Email must be valid'
      ],

      // Sample data
      branches: [
        {
          id: 1,
          name: 'Main Office',
          location: '123 Main Street, City',
          manager: 'John Smith',
          email: '<EMAIL>',
          phone: '(*************',
          teams: [
            {
              id: 1,
              name: 'Sales Team',
              lead: 'Sarah Johnson',
              members: [
                'Sarah Johnson',
                'Alex Turner',
                'Lisa Wong',
                'David Lee',
                'Jane Smith',
                'Robert Wilson',
                'Emily Davis',
                'Michael Brown'
              ]
            },
            {
              id: 2,
              name: 'Support Team',
              lead: 'Michael Brown',
              members: ['Michael Brown', 'John Doe', 'Lisa Wong', 'Alex Turner', 'Jane Smith']
            },
            {
              id: 3,
              name: 'Marketing Team',
              lead: 'Emily Davis',
              members: ['Emily Davis', 'Robert Wilson', 'David Lee', 'John Doe']
            }
          ]
        },
        {
          id: 2,
          name: 'North Region',
          location: '456 North Ave, City',
          manager: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '(*************',
          teams: [
            {
              id: 4,
              name: 'Regional Sales',
              lead: 'Alex Turner',
              members: ['Alex Turner', 'Lisa Wong', 'John Doe']
            },
            {
              id: 5,
              name: 'Operations',
              lead: 'David Lee',
              members: ['David Lee', 'Jane Smith', 'Robert Wilson']
            }
          ]
        },
        {
          id: 3,
          name: 'East Region',
          location: '789 East Blvd, City',
          manager: 'Michael Brown',
          email: '<EMAIL>',
          phone: '(*************',
          teams: [
            {
              id: 6,
              name: 'Customer Service',
              lead: 'Lisa Wong',
              members: ['Lisa Wong', 'John Doe', 'Jane Smith', 'Alex Turner']
            }
          ]
        }
      ]
    }
  },
  computed: {
    availableMembersToAdd() {
      if (!this.selectedTeam) return this.availableMembers
      return this.availableMembers.filter((member) => !this.selectedTeam.members.includes(member))
    }
  },
  mounted() {
    // Select first branch by default
    if (this.branches.length > 0) {
      this.selectBranch(this.branches[0])
    }
  },
  methods: {
    selectBranch(branch) {
      this.selectedBranch = branch
      this.selectedTeam = null
      if (branch.teams.length > 0) {
        this.selectTeam(branch.teams[0])
      }
    },

    selectTeam(team) {
      this.selectedTeam = team
    },

    addMember() {
      if (this.newMember && !this.selectedTeam.members.includes(this.newMember)) {
        this.selectedTeam.members.push(this.newMember)
        this.newMember = null
      }
    },

    removeMember(index) {
      const member = this.selectedTeam.members[index]
      if (member === this.selectedTeam.lead) {
        this.showSnackbar('Cannot remove team lead. Please assign a new lead first.', 'warning')
        return
      }
      this.selectedTeam.members.splice(index, 1)
    },

    deleteTeam() {
      if (!this.selectedTeam || !this.selectedBranch) return

      const teamIndex = this.selectedBranch.teams.findIndex((t) => t.id === this.selectedTeam.id)
      if (teamIndex > -1) {
        this.selectedBranch.teams.splice(teamIndex, 1)
        this.selectedTeam = null
        this.showSnackbar('Team deleted successfully', 'success')
      }
    },

    saveTeamChanges() {
      this.showSnackbar('Team changes saved successfully', 'success')
    },

    // Modal methods
    closeBranchModal() {
      this.showBranchModal = false
      this.resetBranchForm()
    },

    closeTeamModal() {
      this.showTeamModal = false
      this.resetTeamForm()
    },

    resetBranchForm() {
      this.newBranch = {
        name: '',
        location: '',
        manager: '',
        email: '',
        phone: ''
      }
      if (this.$refs.branchForm) {
        this.$refs.branchForm.resetValidation()
      }
    },

    resetTeamForm() {
      this.newTeam = {
        name: '',
        lead: '',
        members: []
      }
      if (this.$refs.teamForm) {
        this.$refs.teamForm.resetValidation()
      }
    },

    saveBranch() {
      if (!this.branchFormValid) return

      const branch = {
        id: Date.now(),
        ...this.newBranch,
        teams: []
      }

      this.branches.push(branch)
      this.showSnackbar('Branch created successfully', 'success')
      this.closeBranchModal()
    },

    saveTeam() {
      if (!this.teamFormValid || !this.selectedBranch) return

      const team = {
        id: Date.now(),
        name: this.newTeam.name,
        lead: this.newTeam.lead,
        members: [...new Set([this.newTeam.lead, ...this.newTeam.members])]
      }

      this.selectedBranch.teams.push(team)
      this.showSnackbar('Team created successfully', 'success')
      this.closeTeamModal()
    },

    // Method to be called from parent component (OrganizationHierarchy.vue)
    addBranch() {
      this.showBranchModal = true
    }
  }
}
</script>
