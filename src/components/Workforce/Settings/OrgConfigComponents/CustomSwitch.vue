<template>
  <v-card
    class="wfm-emp-card border-none"
    :disabled="onDisabled"
    :color="$vuetify.theme.currentTheme.navigationColor"
  >
    <div v-if="expansion" class="pt-0 py-5 overflow-auto max-h-[70vh]">
      <div>
        <div>
          <div>
            <div
              class="flex align-center border border-blue-200 bg-white mt-4 dark-bg-custom"
            >
              <p v-tooltip="{ text: infoMessage }">
                <v-icon class="ml-4">mdi-information</v-icon>
              </p>

              <div class="ml-4">{{ title }}</div>
              <div class="flex align-center justify-end">
                <v-label>{{ leftOption }}</v-label>
                <v-switch
                  :ripple="false"
                  class="mr-10 ml-5 mb-1"
                  inset
                  dense
                  :label="rightOption"
                  v-model="switchData"
                  @change="emitValue"
                ></v-switch>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else>
      <div
        class="flex align-center border border-blue-200 bg-white mt-4 dark-bg-custom"
        v-show="visibility"
      >
        <span v-tooltip="{ text: infoMessage }">
          <v-icon class="ml-4">mdi-information</v-icon>
        </span>
        <div class="ml-4">{{ title }}</div>
        <v-btn
          @click="handleOpen"
          v-if="
            title ===
            'Restrict Clock-In/Clock-Out based on Global Level Geofencing'
          "
          :color="$vuetify.theme.currentTheme.primary"
          small
          class="white--text ml-4"
          :disabled="!switchData"
        >
          Create GeoFencing
        </v-btn>
        <v-btn
          @click="refreshReschdule"
          v-if="title === 'Enable auto-rescheduling'"
          :color="$vuetify.theme.currentTheme.primary"
          small
          class="white--text ml-4"
          :disabled="!switchData"
        >
          Manual Auto-Rescheduling
        </v-btn>
        <v-icon
          v-if="
            title ===
            'Restrict Clock-In/Clock-Out based on Global Level Geofencing'
          "
          class="ml-4"
          @click="openQr"
          >mdi-qrcode</v-icon
        >
        <div class="flex align-center justify-end">
          <v-label>{{ leftOption }}</v-label>
          <v-switch
            :ripple="false"
            class="mr-10 ml-5 mb-1"
            inset
            dense
            :label="rightOption"
            v-model="switchData"
            @change="emitValue"
          ></v-switch>
        </div>
      </div>
    </div>
  </v-card>
</template>
<script>
export default {
  data() {
    return {
      switchData: false,
      panel: [0],
    };
  },
  props: {
    id: String,
    heading: String,
    infoMessage: String,
    title: String,
    leftOption: String,
    rightOption: String,
    expansion: Boolean,
    value: Boolean,
    onDisabled: Boolean,
    visibility: { type: Boolean, default: true },
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal !== null) {
          this.switchData = newVal;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    emitValue() {
      const obj = {
        id: this.id,
        title: this.title.toUpperCase().split(" ").join("_"),
        value: this.switchData,
      };
      this.$emit("switchValue", obj);
    },
    handleOpen() {
      this.$emit("mapDialog", true);
    },
    refreshReschdule() {
      this.$emit("refreshReschdule", true);
    },
    openQr() {
      this.$emit("openQrPopup");
    },
  },
  mounted() {
    this.switchData = this.$props?.value;
  },
};
</script>
<style scoped>
.wfm-emp-card {
  border: none !important;
  box-shadow: none !important;
  margin-top: 0 !important;
}
</style>
