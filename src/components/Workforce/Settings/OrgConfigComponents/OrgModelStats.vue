<template>
  <div class="bg-gray-50 min-h-screen w-full">
    <!-- Back button when viewing MongoDB interface -->
    <div v-if="showMongoDbInterface" class="ml-4 my-4">
      <v-btn icon @click="emitCancel" class="mr-2">
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>
      <span class="uppercase gradient-label font-semibold text-xl text-gray-700 ml-4"
        >NoSQL Query Interface</span
      >
    </div>

    <!-- Main stats view -->
    <div v-if="!showMongoDbInterface" class="w-full">
      <!-- Main Content -->
      <div class="px-6 py-6 w-full">
        <!-- Summary Cards -->
        <v-row class="mb-8 mx-0">
          <v-col cols="12" md="6" lg="3">
            <v-card class="h-100 border border-gray-100" elevation="0">
              <v-card-text class="pb-4">
                <div class="d-flex justify-space-between align-start">
                  <div class="flex-grow-1">
                    <p class="text-gray-500 text-sm font-medium mb-1">Total Storage Used</p>
                    <h3 class="text-2xl font-bold mb-1">{{ stats.totalStorage }}</h3>
                    <p class="text-gray-500 text-sm mb-1">
                      <span class="text-blue-600">{{ stats.databaseStorage }}</span> database
                    </p>
                    <p class="text-gray-500 text-sm">{{ stats.s3Storage }} S3</p>
                  </div>
                  <div class="bg-blue-100 pa-3 rounded-lg">
                    <v-icon color="blue darken-1" size="24">mdi-database</v-icon>
                  </div>
                </div>
                <div class="mt-4">
                  <div class="progress-bar">
                    <div
                      class="progress-bar-fill"
                      :style="{ width: stats.storagePercentage + '%' }"
                    ></div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    {{ stats.storagePercentage }}% of 50MB allocated
                  </p>
                </div>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="6" lg="3">
            <v-card class="h-100 border border-gray-100" elevation="0">
              <v-card-text class="pb-4">
                <div class="d-flex justify-space-between align-start">
                  <div class="flex-grow-1">
                    <p class="text-gray-500 text-sm font-medium mb-1">Total Active Collections</p>
                    <h3 class="text-2xl font-bold mb-1">{{ stats.totalCollections }}</h3>
                    <p class="text-gray-500 text-sm mb-1">
                      <span class="text-green-600">{{ stats.activeCollections }}</span> active
                    </p>
                    <p class="text-gray-500 text-sm">
                      <span class="text-gray-400">{{ stats.emptyCollections }}</span> empty
                    </p>
                  </div>
                  <div class="bg-green-100 pa-3 rounded-lg">
                    <v-icon color="green darken-1" size="24">mdi-layers</v-icon>
                  </div>
                </div>
                <div class="mt-4">
                  <div class="progress-bar">
                    <div
                      class="progress-bar-fill bg-green-500"
                      :style="{ width: stats.collectionsInUsePercentage + '%' }"
                    ></div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    {{ stats.collectionsInUsePercentage }}% collections in use
                  </p>
                </div>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="6" lg="3">
            <v-card class="h-100 border border-gray-100" elevation="0">
              <v-card-text class="pb-4">
                <div class="d-flex justify-space-between align-start">
                  <div class="flex-grow-1">
                    <p class="text-gray-500 text-sm font-medium mb-1">Total Documents</p>
                    <h3 class="text-2xl font-bold mb-1">
                      {{ stats.totalDocuments?.toLocaleString?.() || '0' }}
                    </h3>

                    <p class="text-gray-500 text-sm mb-1">
                      <span class="text-purple-600">{{ stats.dataSize }}</span> data size
                    </p>
                    <p class="text-gray-500 text-sm">{{ stats.indexSize }} indexes</p>
                  </div>
                  <div class="bg-purple-100 pa-3 rounded-lg">
                    <v-icon color="purple darken-1" size="24">mdi-file-document</v-icon>
                  </div>
                </div>
                <div class="mt-4">
                  <div class="progress-bar">
                    <div
                      class="progress-bar-fill bg-purple-500"
                      :style="{ width: stats.dataIndexRatio + '%' }"
                    ></div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">
                    {{ stats.dataIndexRatio }}% data to index ratio
                  </p>
                </div>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="6" lg="3">
            <v-card class="h-100 border border-gray-100" elevation="0">
              <v-card-text class="pb-4">
                <div class="d-flex justify-space-between align-start">
                  <div class="flex-grow-1">
                    <p class="text-gray-500 text-sm font-medium mb-1">S3 Storage consumed</p>
                    <h3 class="text-2xl font-bold mb-1">{{ stats.s3StorageMB }}</h3>
                    <p class="text-gray-500 text-sm mb-1">{{ stats.s3StorageGB }}</p>
                    <p class="text-gray-500 text-sm">~{{ stats.s3Files }} files</p>
                  </div>
                  <div class="bg-yellow-100 pa-3 rounded-lg">
                    <v-icon color="yellow darken-2" size="24">mdi-cloud</v-icon>
                  </div>
                </div>
                <div class="mt-4">
                  <div class="progress-bar">
                    <div
                      class="progress-bar-fill bg-yellow-500"
                      :style="{ width: stats.s3Percentage + '%' }"
                    ></div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">{{ stats.s3Percentage }}% of 20GB limit</p>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <!-- Collections Section -->
        <v-card class="mb-8" elevation="0">
          <v-card-title class="d-flex justify-space-between align-center border-b border-gray-100">
            <h2 class="text-lg font-semibold text-gray-800">Database Collections</h2>
            <div class="d-flex align-center">
              <v-text-field
                v-model="searchQuery"
                placeholder="Search collections..."
                prepend-inner-icon="mdi-magnify"
                hide-details
                outlined
                dense
                class="mr-3"
                style="max-width: 250px"
              ></v-text-field>
              <v-btn icon small class="ml-2" @click="openMongoDB">
                <v-icon>mdi-database-edit</v-icon>
              </v-btn>
              <!-- <v-btn icon>
                <v-icon>mdi-filter-variant</v-icon>
              </v-btn> -->
            </div>
          </v-card-title>

          <!-- Collections Table -->
          <v-data-table
            :headers="tableHeaders"
            :items="filteredCollections"
            :items-per-page="showAllCollections ? -1 : 5"
            hide-default-footer
            :disable-sort="true"
            class="collections-table justify-between"
          >
            <template v-slot:item.name="{ item }">
              <span
                class="font-medium p-4 whitespace-nowrap overflow-hidden text-ellipsis max-w-xs inline-block"
                v-tooltip="{
                  text: splitTitleCase(item.name)
                }"
                >{{ truncateName(splitTitleCase(item.name)) }}</span
              >
            </template>
            <template v-slot:item.documents="{ item }">
              <span class="text-gray-500 ml-4 whitespace-nowrap p-4">{{
                item.documents?.toLocaleString()
              }}</span>
            </template>
            <template v-slot:item.storageUsed="{ item }">
              <span class="text-gray-500 whitespace-nowrap p-4">{{ item.storageUsed }}</span>
            </template>
            <template v-slot:item.dataSize="{ item }">
              <span class="text-gray-500 whitespace-nowrap p-4">{{ item.dataSize }}</span>
            </template>
            <template v-slot:item.indexSize="{ item }">
              <span class="text-gray-500 whitespace-nowrap p-4">{{ item.indexSize }}</span>
            </template>
            <template v-slot:item.avgObjectSize="{ item }">
              <span class="text-gray-500 whitespace-nowrap p-4">{{ item.avgObjectSize }}</span>
            </template>
            <template v-slot:item.action="{ item }">
              <div class="flex gap-2">
                <v-btn outlined small color="primary" @click="actionModel(item, 'Sync')">
                  Sync
                </v-btn>
                <v-btn outlined small color="red" @click="actionModel(item, 'Delete')">
                  Delete
                </v-btn>
              </div>
            </template>
          </v-data-table>
          <AlertPopUp
            :showConfirmationDialog="showActionDialog"
            :confirmationTitle="'Confirm'"
            :confirmationMessage="`Are you sure you want to ${selectedAction} the ${selectedKey} module data?`"
            :confirmationActionText="selectedAction"
            :confirmationActionColor="selectedAction === 'Delete' ? 'red' : 'blue'"
            :performAction="triggerAction"
            :cancel="cancel"
          />

          <v-card-actions class="justify-center border-t border-gray-100 bg-gray-50">
            <v-btn
              text
              color="blue darken-1"
              @click="toggleAllCollections"
              class="text-sm font-medium"
            >
              {{ showAllCollections ? 'Show less' : `View all ${collections.length} collections` }}
              <v-icon right>{{
                showAllCollections ? 'mdi-chevron-up' : 'mdi-chevron-down'
              }}</v-icon>
            </v-btn>
          </v-card-actions>
        </v-card>

        <!-- Storage Distribution Section -->
        <v-row class="mb-8">
          <v-col cols="12" lg="6">
            <v-card elevation="0">
              <v-card-title class="d-flex justify-space-between align-center">
                <h2 class="text-lg font-semibold text-gray-800">Database Storage Breakdown</h2>
                <div class="d-flex align-center text-sm">
                  <span class="w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
                  <span class="mr-3">Data</span>
                  <span class="w-3 h-3 rounded-full bg-indigo-400 mr-1"></span>
                  <span>Indexes</span>
                </div>
              </v-card-title>
              <v-card-text>
                <apexchart
                  type="donut"
                  height="200"
                  :options="storageChartOptions"
                  :series="storageChartSeries"
                ></apexchart>
                <v-row>
                  <v-col
                    v-for="item in storageBreakdown"
                    :key="item.name"
                    cols="4"
                    class="text-center"
                  >
                    <p class="text-xs text-gray-500">{{ item.name }}</p>
                    <p class="font-medium">{{ item.size }}</p>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" lg="6">
            <v-card elevation="0">
              <v-card-title class="d-flex justify-space-between align-center">
                <h2 class="text-lg font-semibold text-gray-800">Document Count Distribution</h2>
                <div class="d-flex align-center text-sm">
                  <span class="w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                  <span>Documents</span>
                </div>
              </v-card-title>
              <v-card-text>
                <apexchart
                  type="bar"
                  height="256"
                  :options="documentChartOptions"
                  :series="documentChartSeries"
                ></apexchart>
                <!-- <v-row>
                  <v-col
                    v-for="item in documentBreakdown"
                    :key="item.name"
                    cols="4"
                    class="text-center"
                  >
                    <p class="text-xs text-gray-500">{{ item.name }}</p>
                    <p class="font-medium">{{ item.count.toLocaleString() }}</p>
                  </v-col>
                </v-row> -->
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </div>

    <!-- MongoDB Query Interface view -->
    <div v-if="showMongoDbInterface">
      <MongoDbQueryInterface :collections="collections" />
    </div>
  </div>
</template>

<script>
import { convertToTitleCase, splitAndTitleCase } from '@/utils/common'
import AlertPopUp from '@/components/AlertPopUp'
import VueApexCharts from 'vue-apexcharts'
import { isModuleFeatureAllowed } from '@/utils/common'
import MongoDbQueryInterface from './MongoDbQueryInterface.vue'

export default {
  name: 'DatabaseStatsDashboard',
  components: {
    apexchart: VueApexCharts,
    AlertPopUp,
    MongoDbQueryInterface
  },
  data() {
    return {
      refreshing: false,
      models: [],
      showActionDialog: false,
      selectedKey: '',
      selectedAction: '',
      currentTime: '',
      searchQuery: '',
      showAllCollections: false,
      stats: {},
      showMongoDbInterface: false,
      totalSpace: null,
      // Will be set from API
      collections: [],
      storageBreakdown: [],
      documentBreakdown: [],
      storageChartOptions: {
        chart: { type: 'donut' },
        labels: ['Data', 'Indexes'],
        colors: ['#3b82f6', '#818cf8'],
        legend: { show: false },
        dataLabels: { enabled: true },
        tooltip: { y: { formatter: (val) => val + ' bytes' } }
      },
      storageChartSeries: [0, 0],
      documentChartOptions: {
        chart: { type: 'bar' },
        plotOptions: { bar: { horizontal: false, columnWidth: '50%' } },
        dataLabels: { enabled: false },
        xaxis: { categories: [] },
        colors: ['#10b981'],
        legend: { show: false }
      },
      documentChartSeries: [{ name: 'Documents', data: [] }]
    }
  },
  computed: {
    tableHeaders() {
      const data = [
        { text: 'Collection Name', value: 'name', sortable: true, visible: true },
        { text: 'Documents', value: 'documents', sortable: true, visible: true },
        { text: 'Storage Used', value: 'storageUsed', sortable: true, visible: true },
        { text: 'Data Size', value: 'dataSize', sortable: true, visible: true },
        { text: 'Index Size', value: 'indexSize', sortable: true, visible: true },
        { text: 'Avg. Size', value: 'avgObjectSize', sortable: true, visible: true },
        {
          text: 'Action',
          value: 'action',
          sortable: false,
          visible: isModuleFeatureAllowed('ADDONS', 'SYNC_AND_DELETE_DATA')
        }
      ]
      return data.filter((header) => header.visible)
    },
    filteredCollections() {
      if (!this.searchQuery) {
        return this.collections
      }
      return this.collections.filter((collection) =>
        collection.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      )
    }
  },
  async mounted() {
    await this.fetchModelStats()
    await this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 60000)
  },
  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    truncateName(name) {
      const maxLength = 15
      if (name.length > maxLength) {
        return name.slice(0, maxLength) + '...'
      }
      return name
    },
    actionModel(key, action) {
      this.selectedKey = key.name
      this.selectedAction = action
      this.showActionDialog = true
    },
    triggerAction() {
      try {
        let url =
          this.selectedAction === 'Delete'
            ? '/workforce/org/drop/model'
            : '/workforce/org/sync/model'

        this.$axios.post(url, {
          modelId: this.selectedKey
        })
        if (this.selectedAction === 'Delete') {
          this.$toast.success(`Module ${this.selectedKey} data deleted successfully`)
        } else if (this.selectedAction === 'Sync') {
          this.$toast.success(`Module ${this.selectedKey} data synced successfully`)
        }
        this.cancel()
      } catch (error) {
        console.log(error)
        this.$toast.error(`Error ${error.status}: ${error.message}`)
      }
    },
    cancel() {
      this.showActionDialog = false
    },
    async fetchModelStats() {
      try {
        const res = await this.$axios.get('/workforce/org/stats/model')
        const { total, collections, s3Space } = res.data.stats

        // Parse collection details
        this.collections = Object.entries(collections).map(([name, value]) => ({
          name,
          documents: value.docCount,
          storageUsed: this.formatBytes(value.storageUsed),
          dataSize: this.formatBytes(value.dataSize),
          indexSize: this.formatBytes(value.indexSize),
          avgObjectSize: this.formatBytes(value.avgObjSize)
        }))

        // Update document chart data
        this.updateDocumentChart()

        const activeCollections = this.collections.filter((col) => col.documents > 0).length
        const emptyCollections = this.collections.length - activeCollections
        const totalDocuments = this.collections.reduce((sum, col) => sum + (col.documents || 0), 0)

        this.stats = {
          totalStorage: `${parseFloat(total.totalUsedMB).toFixed(2)} MB`,
          databaseStorage: `${this.formatBytes(total.dataSize)}`,
          s3Storage: `${parseFloat(s3Space.inMB).toFixed(2)} MB`,
          storagePercentage: Math.round((total.totalUsed / (50 * 1024 * 1024)) * 100), // out of 50MB

          totalCollections: this.collections.length,
          activeCollections,
          emptyCollections,
          collectionsInUsePercentage: Math.round(
            (activeCollections / this.collections.length) * 100
          ),

          totalDocuments,
          dataSize: this.formatBytes(total.dataSize),
          indexSize: this.formatBytes(total.indexSize),
          dataIndexRatio: Math.round((total.dataSize / (total.indexSize + total.dataSize)) * 100),

          s3StorageMB: `${s3Space.inMB} MB`,
          s3StorageGB: `${s3Space.inGB} GB`,
          s3Files: 'N/A', // If you get actual file count from backend, replace this
          s3Percentage: Math.round((s3Space.inBytes / (20 * 1024 * 1024 * 1024)) * 100) // out of 20GB
        }

        // Calculate total space by converting both values to numbers and adding them
        this.totalSpace = Number(total.totalUsedGB || 0) + Number(s3Space.inGB || 0)
        this.$emit('totalSpace', this.totalSpace)
        this.storageBreakdown = [
          { name: 'Data', size: this.formatBytes(total.dataSize) },
          { name: 'Indexes', size: this.formatBytes(total.indexSize) }
        ]
        this.storageChartSeries = [total.dataSize, total.indexSize]
      } catch (error) {
        console.error('Error fetching stats:', error)
      }
    },
    updateDocumentChart() {
      const topCollections = this.collections

      this.documentChartOptions = {
        ...this.documentChartOptions,
        xaxis: {
          categories: topCollections.map((col) => this.splitTitleCase(col.name))
        }
      }

      this.documentChartSeries = [
        {
          name: 'Documents',
          data: topCollections.map((col) => col.documents)
        }
      ]
    },
    formatBytes(bytes) {
      if (!bytes || bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    // titleCase(str) {
    //   return convertToTitleCase(str)
    // },
    splitTitleCase(str) {
      return splitAndTitleCase(str)
    },
    async updateTime() {
      const now = new Date()
      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }
      this.currentTime = now.toLocaleDateString('en-US', options)
    },
    toggleAllCollections() {
      this.showAllCollections = !this.showAllCollections
    },
    emitCancel() {
      this.showMongoDbInterface = false
      this.$emit('mongodb-interface-opened', false)
    },
    async openMongoDB() {
      this.showMongoDbInterface = true
      this.$emit('mongodb-interface-opened', true)

      try {
        const response = await fetch('/mongodbQueryInterface.html')

        if (!response.ok) {
          throw new Error(`Failed to fetch MongoDB query interface: ${response.statusText}`)
        }

        let htmlContent = await response.text()
        htmlContent = htmlContent.replace('BASE_URL_PLACEHOLDER', this.$axios.defaults.baseURL)

        // Pass collections data to the HTML
        const collectionsData = JSON.stringify(this.collections)
        htmlContent = htmlContent.replace('COLLECTIONS_DATA_PLACEHOLDER', collectionsData)

        this.mongoDbQueryUi = { data: { content: htmlContent } }
      } catch (error) {
        console.error('Error loading MongoDB query interface:', error)
        this.$toast?.error?.('Failed to load MongoDB query interface')
        this.mongoDbQueryUi = false
      }
    }
  }
}
</script>

<style scoped>
.progress-bar {
  height: 8px;
  border-radius: 4px;
  background-color: #e5e7eb;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

/* Custom styles for the collections table */
.collections-table >>> .v-data-table__wrapper {
  overflow-x: auto;
}

.collections-table >>> tbody tr:hover {
  background-color: #f9fafb !important;
}
</style>
