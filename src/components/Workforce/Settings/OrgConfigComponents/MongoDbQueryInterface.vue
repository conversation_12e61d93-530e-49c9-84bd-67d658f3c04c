<template>
  <div class="flex flex-row gap-4 mongo-query-interface">
    <div :class="'query-section w-1/2'">
      <form @submit.prevent="executeQuery">
        <div class="form-group">
          <label>📊 Select Collection:</label>
          <v-autocomplete
            v-model="selectedCollection"
            :items="collections"
            :item-text="(item) => splitTitleCase(item.name)"
            item-value="name"
            placeholder="Search collections..."
            required
            autocomplete
            outlined
            dense
            hide-details
            clearable
            class="collection-select"
          >
          </v-autocomplete>
        </div>

        <div class="form-group">
          <label>🔍 NoSQL Query (JSON):</label>
          <textarea
            v-model="queryText"
            class="query-editor bg-black text-white"
            placeholder="Enter your MongoDB query here... For sorting, use: {'field': 1} for ascending or {'field': -1} for descending"
            rows="6"
          ></textarea>
          <div class="query-examples">
            <small>Quick Examples:</small>
            <button type="button" class="example-btn" @click="setQuery('{}')">All Records</button>
            <button type="button" class="example-btn" @click="setQuery('{}')">Active Status</button>
            <button type="button" class="example-btn" @click="setQuery('{}')">
              Recent Records
            </button>
            <button type="button" class="example-btn" @click="setQuery('{}')">Has ID</button>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>📄 Limit:</label>
            <input type="number" v-model="queryParams.limit" min="1" max="1000" />
          </div>
          <div class="form-group">
            <label>⏭️ Skip:</label>
            <input type="number" v-model="queryParams.skip" min="0" />
          </div>
          <div class="form-group">
            <label>🔄 Sort Field:</label>
            <input type="text" v-model="queryParams.sortField" placeholder="createdAt" />
          </div>
          <div class="form-group">
            <label>📈 Sort Order:</label>
            <select v-model="queryParams.sortOrder">
              <option value="-1">Descending</option>
              <option value="1">Ascending</option>
            </select>
          </div>
        </div>

        <div class="button-container">
          <button type="submit" class="btn btn-primary" :disabled="loading">
            <span v-if="loading">⏳ Executing...</span>
            <span v-else>🚀 Execute Query</span>
          </button>
          <button type="button" class="btn btn-warning" @click="clearQuery">🧹 Clear</button>
          <button type="button" class="btn btn-info" @click="formatQuery">✨ Format JSON</button>
        </div>
      </form>
    </div>

    <div class="result flex-1">
      <div class="stats">
        <div class="stat-item">
          <strong>{{ queryStats?.recordCount || 0 }}</strong>
          <div>Records</div>
        </div>
        <div class="stat-item">
          <strong>{{ queryStats?.queryTime || 0 }}ms</strong>
          <div>Query Time</div>
        </div>
        <div class="stat-item">
          <strong>{{ queryStats?.collectionName || '-' }}</strong>
          <div>Collection</div>
        </div>
      </div>

      <div class="result-header">
        <h3>📊 Query Results</h3>
        <div class="button-group">
          <button class="btn btn-success" @click="copyToClipboard">📋 Copy JSON</button>
          <button class="btn btn-secondary" @click="toggleExpandCollapse">
            {{ isExpanded ? '⬆ Collapse All' : '⬇ Expand All' }}
          </button>
          <button class="btn btn-info" @click="downloadJSON">💾 Download</button>
        </div>
      </div>

      <div class="json-result">
        <div ref="jsonEditorContainer" class="json-editor-container"></div>
      </div>
      <div v-if="errorMessage" class="error-message">❌ {{ errorMessage }}</div>
    </div>
  </div>
</template>

<script>
import JSONEditor from 'jsoneditor'
import 'jsoneditor/dist/jsoneditor.css'
import { splitAndTitleCase } from '@/utils/common'

export default {
  name: 'MongoDbQueryInterface',
  props: {
    collections: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedCollection: '',
      queryText: '{}',
      queryParams: {
        limit: 10,
        skip: 0,
        sortField: '',
        sortOrder: '-1'
      },
      loading: false,
      showResults: false,
      queryResult: null,
      queryStats: null,
      errorMessage: '',
      isExpanded: true,
      jsonEditor: null
    }
  },
  computed: {
    formattedResult() {
      if (!this.queryResult) return ''
      return JSON.stringify(this.queryResult, null, 2)
    },
    collapsedResultText() {
      if (!this.queryResult) return ''
      if (Array.isArray(this.queryResult)) {
        return `Array(${this.queryResult.length}) [...]`
      }
      return 'Object {...}'
    }
  },
  mounted() {
    this.initializeJsonEditor([])
    if (this.collections.length > 0) {
      this.selectedCollection = this.collections[0].name
    }
  },
  methods: {
    splitTitleCase(str) {
      return splitAndTitleCase(str)
    },
    setQuery(queryStr) {
      this.queryText = queryStr
    },

    clearQuery() {
      this.jsonEditor.destroy()
      this.initializeJsonEditor([])
      this.queryText = '{}'
      this.queryParams = {
        limit: 10,
        skip: 0,
        sortField: '',
        sortOrder: '-1'
      }
      this.showResults = false
      this.errorMessage = ''
    },

    formatQuery() {
      try {
        const parsed = JSON.parse(this.queryText)
        this.queryText = JSON.stringify(parsed, null, 2)
      } catch (e) {
        this.errorMessage = 'Invalid JSON format'
        setTimeout(() => {
          this.errorMessage = ''
        }, 3000)
      }
    },

    async executeQuery() {
      this.loading = true
      this.errorMessage = ''
      this.showResults = false

      const queryStartTime = Date.now()

      try {
        // Get organization ID from localStorage
        let orgId = ''
        try {
          const orgData = JSON.parse(localStorage.getItem('orgData'))
          orgId = orgData?.id || '152'
        } catch (e) {
          console.error('Error parsing orgData:', e)
          orgId = '152'
        }

        const token = JSON.parse(localStorage.getItem('token'))
        const headers = {
          'Content-Type': 'application/json'
        }

        if (token) {
          headers['Authorization'] = token.access_token
        }

        const response = await this.$axios.post(
          '/workforce/orgSetup/execute-query',
          {
            collection: `${this.splitTitleCase(this.selectedCollection)}_${orgId}`,
            query: this.queryText,
            limit: this.queryParams.limit,
            skip: this.queryParams.skip,
            sortField: this.queryParams.sortField,
            sortOrder: this.queryParams.sortOrder
          },
          { headers }
        )

        const queryTime = Date.now() - queryStartTime

        // Handle the API response structure
        let displayData = response.data
        if (response.data.success && response.data.data && response.data.data.result) {
          displayData = response.data.data.result
        }

        this.queryResult = displayData
        this.updateStats(displayData, this.selectedCollection, queryTime, response.data)
        this.showResults = true

        this.$nextTick(() => {
          this.initializeJsonEditor(displayData)
        })
      } catch (error) {
        this.errorMessage =
          error.response?.data?.message || error.message || 'Query execution failed'
      } finally {
        this.loading = false
      }
    },

    updateStats(data, collection, queryTime, originalResult) {
      let recordCount = 0
      if (originalResult && originalResult.data && originalResult.data.count !== undefined) {
        recordCount = originalResult.data.count
      } else if (Array.isArray(data)) {
        recordCount = data.length
      } else {
        recordCount = 1
      }

      this.queryStats = {
        recordCount,
        queryTime,
        collectionName: collection
      }
    },

    initializeJsonEditor(data) {
      const container = this.$refs.jsonEditorContainer

      if (this.jsonEditor) {
        this.jsonEditor.destroy()
      }

      const options = {
        mode: 'view',
        modes: ['view', 'tree'],
        search: true,
        navigationBar: false
      }

      this.jsonEditor = new JSONEditor(container, options)

      // Initialize with empty object if no data
      this.jsonEditor.set(data || [])

      if (this.isExpanded) {
        this.jsonEditor.expandAll()
      } else {
        this.jsonEditor.collapseAll()
      }
    },

    toggleExpandCollapse() {
      this.isExpanded = !this.isExpanded

      if (this.jsonEditor) {
        if (this.isExpanded) {
          this.jsonEditor.expandAll()
        } else {
          this.jsonEditor.collapseAll()
        }
      }
    },

    async copyToClipboard() {
      if (!this.queryResult) return

      const jsonStr = JSON.stringify(this.queryResult, null, 2)

      try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(jsonStr)
        } else {
          // Fallback for older browsers
          const textArea = document.createElement('textarea')
          textArea.value = jsonStr
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }

        this.$toast?.success?.('JSON copied to clipboard!')
      } catch (err) {
        console.error('Failed to copy: ', err)
        this.$toast?.error?.('Failed to copy to clipboard')
      }
    },

    downloadJSON() {
      if (!this.queryResult) return

      const jsonStr = JSON.stringify(this.queryResult, null, 2)
      const blob = new Blob([jsonStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = url
      a.download = `query-result-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  },
  beforeDestroy() {
    if (this.jsonEditor) {
      this.jsonEditor.destroy()
    }
  }
}
</script>

<style scoped>
.mongo-query-interface {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f5f7fa;
  min-height: 50vh;
  padding: 20px;
}

.query-section {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.query-editor {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  resize: vertical;
  min-height: 150px;
}

.query-editor:focus {
  outline: none;
  border-color: #667eea;
}

.query-examples {
  margin-top: 15px;
}

.example-btn {
  background: #e9ecef;
  color: #495057;
  border: 1px solid #ced4da;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin: 2px;
  font-size: 12px;
  transition: background 0.3s;
}

.example-btn:hover {
  background: #dee2e6;
}

.form-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.form-row .form-group {
  flex: 1;
  min-width: 200px;
}

.button-container {
  text-align: center;
  margin-top: 20px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
  margin: 5px;
  display: inline-block;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.result {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stats {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.stat-item {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #dee2e6;
  min-width: 120px;
}

.stat-item strong {
  font-size: 1.2em;
  color: #495057;
  display: block;
}

.stat-item div {
  font-size: 0.9em;
  color: #6c757d;
  margin-top: 5px;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.result-header h3 {
  margin: 0;
  color: #495057;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.json-result {
  border: 1px solid #3883fa;
  border-radius: 6px;
  max-height: 500px;
  overflow: auto;
}

.json-editor-container {
  width: 100%;
  height: 500px;
}

/* Override JSONEditor styles */
.json-editor-container :deep(.jsoneditor) {
  border: none;
}

.json-editor-container :deep(.jsoneditor-menu) {
  background-color: #3883fa;
  border: 1px solid #e1e5e9;
}

.error-message {
  color: #dc3545;
  background: #f8d7da;
  padding: 15px;
  border-radius: 6px;
  margin: 10px 0;
  border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .result-header {
    flex-direction: column;
    align-items: stretch;
  }

  .button-group {
    justify-content: center;
  }

  .stats {
    justify-content: center;
  }
}
</style>
