<template>
  <div class="p-5 pt-0">
    <div class="relative">
      <div @click="onRefreshTemplates" class="flex justify-end items-center gap-3 pt-4 pr-8">
        <Loader v-if="fetchingTemplates" />
        <v-icon
          v-else
          color="blue"
          v-tooltip="{ text: 'Refresh' }"
          class="cursor-pointer border border-gray-300 p-1 rounded"
          >mdi-replay</v-icon
        >
        <v-btn
          @click="onAddNotiTemplate"
          color="primary"
          variant="flat"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow-sm"
        >
          <v-icon class="mr-2">mdi-plus</v-icon>
          ADD TEMPLATE
        </v-btn>
      </div>
      <div
        v-if="!notificationModule?.length"
        class="flex flex-col items-center justify-center py-16"
      >
        <no-data
          title="Nothing to Display"
          :subTitle="`No templates found with the current filters`"
        />
      </div>
      <div v-else class="grid gap-3 md:grid-cols-1 lg:grid-cols-1 xl:grid-cols-1">
        <NotificationCard
          :notifications="notificationModule"
          @edit="(val) => onTemplateEdit(val)"
          @delete="(val) => onTemplateDelete(val)"
        />
      </div>
    </div>

    <v-dialog v-model="addTemplateDialogVisible" persistent width="1000px">
      <AddTemplate
        :templateTriggersList="templateTriggersList"
        :allTriggers="allTriggers"
        :editingTemplate="editingTemplate"
        @close="addTemplateDialogVisible = false"
      />
    </v-dialog>
  </div>
</template>
<script>
import { convertToTitleCase } from '@/utils/common'
import NoData from '@/components/NoData.vue'
import AddTemplate from '../PlatformIntegrations/AddTemplate.vue'
import NotificationCard from './NotificationCard.vue'

const fields = {
  assigned_to: {
    label: 'Assigned To',
    value: 'assigned_to',
    description: 'The user who is assigned to the task',
    eg: 'John Doe'
  },
  task_end_time: {
    label: 'Task End Time',
    value: 'task_end_time',
    description: 'Recorded time when the task is completed',
    eg: '15/08/2024'
  },
  total_pending_tasks: {
    label: 'Total pending tasks',
    value: 'total_pending_tasks',
    description: 'Number of tasks that are in pending status',
    eg: 5
  },
  task_title: {
    label: 'Task title',
    value: 'task_title',
    description: 'The title of the task',
    eg: 'Customer complaint'
  },
  customer_name: {
    label: 'Customer name',
    value: 'customer_name',
    description: 'Name of the customer for the assigned case',
    eg: 'John Doe'
  },
  amc_data: {
    label: 'AMC stats',
    value: 'amc_data',
    description: 'AMC data',
    eg: 'Company name - Plan name - Task title'
  },
  total_in_progress_tasks: {
    label: 'Total in-progress tasks',
    value: 'total_in_progress_tasks',
    description: 'Total number of tasks, that are in in-progress',
    eg: 45
  },
  total_completed_tasks: {
    label: 'Total completed tasks',
    value: 'total_completed_tasks',
    description: 'Total completed tasks',
    eg: 50
  },
  reference_id: {
    label: 'Reference Id',
    value: 'reference_id',
    description: 'Reference Id of complaint',
    eg: 'RID-1234567890'
  },
  status: {
    label: 'Status',
    value: 'status',
    description: 'Status of the task',
    eg: 'Completed'
  }
}

const templateTriggersList = {
  task_assign: {
    title: 'Agent Task Assign',
    params: [fields.assigned_to, fields.task_title]
  },
  task_pending: {
    title: 'Agent Task Pending',
    params: [
      fields.total_tasks,
      fields.total_in_progress_tasks,
      fields.total_completed_tasks,
      fields.total_pending_tasks,
      fields.assigned_to
    ]
  },
  task_end_agent: {
    title: 'Task End Agent',
    params: [fields.assigned_to, fields.task_title, fields.customer_name]
  },
  task_end_customer: {
    title: 'Task End Customer',
    params: [fields.assigned_to, fields.task_title, fields.customer_name, fields.status]
  },
  amc_notification: {
    title: 'AMC Notification',
    params: [fields.amc_data]
  },
  not_checked_in_yet: {
    title: 'Not Checked In Notification',
    params: [fields.assigned_to]
  },
  not_started_any_task_yet: {
    title: 'Not Started Any Task Notification',
    params: [fields.assigned_to]
  },
  tele_caller_notification_agent: {
    title: 'Agent Call End',
    params: []
  },
  tele_caller_notification_customer: {
    title: 'Customer Call End',
    params: []
  },
  customer_case_details: {
    title: 'Customer Case Details',
    params: [fields.customer_name, fields.assigned_to, fields.task_title, fields.reference_id]
  },
  dynamic_form: {
    title: 'Dynamic Form',
    params: [fields.customer_name, fields.assigned_to, fields.reference_id, fields.task_title]
  }
}
export default {
  name: 'NotificationComponent',
  components: { NoData, AddTemplate, NotificationCard },
  data() {
    return {
      templateTriggersList,
      mainTab: 0, // New property for main tabs
      addTemplateDialogVisible: false,
      notificationModule: [],
      editingTemplate: null,
      loading: false,
      fetchingTemplates: false
    }
  },
  async mounted() {
    await this.fetchNotificationTemplates()
  },
  computed: {
    allTriggers() {
      return Object.keys(templateTriggersList).reduce((prev, key) => {
        prev.push({
          label: templateTriggersList[key]?.title,
          value: key
        })

        return prev
      }, [])
    }
  },
  methods: {
    onTemplateEdit(i) {
      this.editingTemplate = JSON.parse(JSON.stringify(this.notificationModule[i]))
      this.addTemplateDialogVisible = true
    },
    titleCase(str) {
      return convertToTitleCase(str)
    },
    async fetchNotificationTemplates() {
      try {
        this.loading = true
        const response = await this.$axios.get('/workforce/orgSetup/notification-templates')
        this.notificationModule = (response.data?.templates || []).map((template) => ({
          ...template,
          showDummy: false
        }))
      } catch (error) {
        console.error('Error fetching templates:', error)
      } finally {
        this.loading = false
      }
    },
    async onTemplateDelete(index) {
      try {
        const template = this.notificationModule[index]
        await this.$axios.delete(`/workforce/whatsapp/templates/${template.templateName}`)
        await this.fetchNotificationTemplates()
        this.$toast.success('Template deleted successfully')
      } catch (error) {
        console.error('Error deleting template:', error)
        this.$toast.error('Failed to delete template')
      }
    },
    onAddNotiTemplate() {
      this.addTemplateDialogVisible = true
    },
    onRefreshTemplates() {
      if (this.fetchingTemplates) return
      this.getBindedTemplates()
      this.fetchTemplates()
    },
    async getBindedTemplates() {
      try {
        const { data } = await this.$axios.get('/workforce/whatsapp/templates/assign')
        console.log(data)
        await this.fetchNotificationTemplates()
      } catch (err) {
        console.error('Error fetching assigned templates:', err)
        this.$toast.error('Error fetching template assignments')
      }
    },
    async fetchTemplates() {
      this.fetchingTemplates = true
      await this.$axios.get('/workforce/whatsapp/templates')
      this.getBindedTemplates()
      this.fetchingTemplates = false
    }
  }
}
</script>
