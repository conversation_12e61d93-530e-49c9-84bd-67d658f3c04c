<template>
  <div class="bg-gray-50 p-4 md:p-6">
    <div class="max-w-full">
      <div class="grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2">
        <div
          v-for="(notification, index) in notifications"
          :key="notification._id"
          class="bg-white rounded-xl shadow-sm overflow-hidden relative border border-gray-200"
        >
          <!-- Header -->
          <div class="flex justify-between items-start p-5 pb-0">
            <div>
              <h3 class="text-lg font-semibold text-gray-800 mb-1">
                {{ titleCase(notification.templateName) }}
              </h3>
              <div class="flex items-center justify-between text-xs text-gray-500 space-x-2">
                <p class="text-sm text-gray-600 mb-0">
                  <strong class="text-gray-700">Event Type:</strong>
                </p>
                <span class="bg-blue-100 text-green-700 px-2 py-1 rounded-full font-semibold">
                  {{ titleCase(notification.eventType) }}
                </span>

                <span class="flex justify-end">
                  <strong>Last updated: {{ formatDate(notification.updatedAt) }}</strong>
                </span>
              </div>
            </div>
            <div class="relative" @click.stop="toggleDropdown(index)">
              <button class="text-gray-400 hover:text-gray-600 p-1 rounded-full">
                <v-icon>mdi-dots-vertical</v-icon>
              </button>
              <div
                v-if="dropdownIndex === index"
                class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg z-10 border border-gray-100"
              >
                <button
                  @click="handleEdit(index)"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <v-icon class="mr-2" small>mdi-pencil</v-icon> Edit
                </button>
                <button
                  @click="handleDelete(index)"
                  class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-50"
                >
                  <v-icon class="mr-2" small>mdi-delete</v-icon> Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="p-5 pt-3">
            <div class="mb-4">
              <p class="text-sm text-gray-600 mb-1">
                <strong class="text-gray-700">Subject:</strong> {{ notification.templateSubject }}
              </p>
              <div
                class="text-sm text-gray-600 content-toggle"
                :class="{ expanded: expandedIndex === index }"
              >
                <p class="mb-1"><strong class="text-gray-700">Content:</strong></p>
                <p>{{ formatContent(notification) }}</p>
              </div>
              <button class="text-blue-500 text-xs mt-1 font-medium" @click="toggleContent(index)">
                {{ expandedIndex === index ? 'Show less' : 'Show more' }}
              </button>
            </div>

            <!-- Parameters -->
            <div class="mb-4" v-if="notification.params?.length">
              <p class="text-xs font-medium text-gray-500 mb-2">PARAMETERS</p>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="(param, i) in notification.params"
                  :key="i"
                  class="param-chip bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                >
                  {{ titleCase(param) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Channels -->
          <div class="border-t border-gray-100 px-5 py-3 bg-gray-50">
            <p class="text-sm font-semibold text-gray-700 mb-2">CHANNELS</p>
            <div class="space-y-2">
              <div
                v-for="(type, i) in notification.typesOfNotification"
                :key="i"
                class="flex justify-between items-center"
              >
                <div class="flex items-center">
                  <i :class="getIcon(type) + ' mr-2'"></i>
                  <span class="text-sm font-medium text-gray-700">{{ type }}</span>
                </div>
                <div class="flex items-center justify-end">
                  <span
                    class="text-xs font-semibold"
                    :style="{ color: getApporvalStatus(notification, type).color }"
                  >
                    {{ getApporvalStatus(notification, type).text }}
                  </span>
                  <img
                    v-if="getApprovedBy(notification, type)"
                    :src="getApprovalIcon(getApprovedBy(notification, type))"
                    class="w-16 h-6 object-contain ml-2 mix-blend-multiply"
                    alt="Approver"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { convertToTitleCase } from '@/utils/common'
export default {
  name: 'NotificationCards',
  props: {
    notifications: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      expandedIndex: null,
      dropdownIndex: null
    }
  },
  methods: {
    toggleContent(index) {
      this.expandedIndex = this.expandedIndex === index ? null : index
    },
    toggleDropdown(index) {
      this.dropdownIndex = this.dropdownIndex === index ? null : index
    },
    titleCase(str) {
      return convertToTitleCase(str)
    },
    getIcon(type) {
      switch (type) {
        case 'EMAIL':
          return 'fas fa-envelope text-purple-600'
        case 'SMS':
          return 'fas fa-comment-dots text-blue-600'
        case 'PUSH':
          return 'fas fa-bell text-orange-500'
        case 'WHATSAPP':
          return 'fab fa-whatsapp text-green-600'
        default:
          return 'fas fa-bell text-gray-400'
      }
    },
    getApporvalStatus(item, type) {
      const status = item.approvalStatus.find((el) => el.typeOfNotification === type)?.status

      if (status == 'APPROVED') return { text: 'Approved', color: 'green' }
      else if (status == 'REJECTED') return { text: 'Rejected', color: 'red' }
      else return { text: 'Pending', color: 'orange' }
    },
    getApprovedBy(item, type) {
      return (
        item.approvalStatus.find((el) => el.typeOfNotification === type)?.approvedBy ||
        'CUALITYWORK'
      )
    },
    getApprovalIcon(name) {
      if (name === 'META') {
        return require('@/assets/img/meta_logo.jpg')
      } else if (name === 'CUALITYWORK') {
        return require('@/assets/img/cualityWork.png')
      } else {
        return 'mdi-clock'
      }
    },
    formatDate(dateStr) {
      const date = new Date(dateStr)
      return date.toLocaleDateString()
    },
    handleEdit(i) {
      this.$emit('edit', i)
    },
    handleDelete(i) {
      this.$emit('delete', i)
    },
    formatContent(notification) {
      let templateContent = notification?.templateContent
      notification.params?.forEach((element, index) => {
        templateContent = templateContent.replace(`{{${index + 1}}}`, `{{${element} }}`)
      })
      return templateContent
    }
  },
  mounted() {
    document.addEventListener('click', () => {
      this.dropdownIndex = null
    })
  }
}
</script>

<style scoped>
.card-hover-effect {
  transition: all 0.3s ease;
  transform: translateY(0);
}
.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.param-chip {
  transition: all 0.2s ease;
}
.param-chip:hover {
  transform: scale(1.05);
}
.content-toggle {
  max-height: 3.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.content-toggle.expanded {
  max-height: none;
  -webkit-line-clamp: unset;
}
</style>
