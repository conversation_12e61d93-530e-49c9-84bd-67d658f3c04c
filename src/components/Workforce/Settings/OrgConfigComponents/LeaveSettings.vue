<template>
  <div class="overflow-auto max-h-[70vh]">
    <div
      class="flex flex-column my-4 px-4"
      v-show="isFeatureVisible('SET_GLOBAL_LEAVE_POLICY')"
    >
      <v-textarea
        label="Organisation leave policy"
        rows="3"
        outlined
        v-model="orgLeaveDescription"
        hide-details
      ></v-textarea>
      <div class="flex justify-between">
        <v-file-input
          class="w-full mt-4"
          label="Attachments"
          v-model="orgLeaveAttachments"
          outlined
          dense
          hide-details
          @change="handleChangeLeaveType"
        ></v-file-input>
        <span class="mt-4 ml-2">
          <LightBox
            :imagesData="imagesData"
            imageWidth="50px"
            imageHeight="40px"
          />
        </span>
      </div>
    </div>
    <div
      class="p-4 pb-2 border-b flex justify-between"
      v-show="isFeatureVisible('PLAN_ORG_LEAVES')"
    >
      <h3 class="m-0 font-bold">Select {{ yearRange }} leaves</h3>
      <v-btn
        :color="$vuetify.theme.currentTheme.primary"
        :disabled="!selectedQuota.length || !receivedData.commonLeaves?.length"
        @click="assignQuota"
        v-show="isFeatureVisible('BULK_ASSIGN_LEAVE_QUOTA')"
        class="white--text"
        >Assign</v-btn
      >
    </div>
    <div v-if="leaves.length" v-show="isFeatureVisible('PLAN_ORG_LEAVES')">
      <div
        class="bg-white p-4 pb-2 flex justify-between items-start dark-bg-default"
        v-for="(leave, index) in leaves"
        :key="index"
      >
        <div class="grow">
          <div class="flex justify-between items-start">
            <div class="mt-1">({{ index + 1 }})</div>
            <v-checkbox
              :color="$vuetify.theme.currentTheme.primary"
              v-model="leave.selected"
              @click="selectQuota(leave)"
              class="mt-0 ml-4"
            ></v-checkbox>
            <v-select
              class="w-48 px-8"
              :items="availableLeaveTypes(index)"
              v-model="leave.type"
              item-text="text"
              label="Select Leave Type"
              outlined
              dense
              hide-details
              @change="handleChangeLeaveType"
            ></v-select>
            <v-text-field
              class="w-48 px-8"
              v-model="leave.days"
              label="Number of Days"
              dense
              outlined
              hide-details
              @input="handleChangeLeaveType"
            ></v-text-field>
          </div>
          <div class="px-8 pt-2 ml-[3rem]">
            <div class="flex">
              <v-text-field
                v-model="leave.description"
                class="w-48 ml-5 mr-9"
                label="Policy Description"
                v-if="leave.showDescription"
                outlined
                dense
                rows="2"
                hide-details
                @input="handleChangeLeaveType"
              ></v-text-field>
              <v-file-input
                v-if="leave.showDescription"
                v-model="leave.attachment"
                class="pt-0 mt-0 w-48 ml-6"
                label="Attachments"
                truncate-length="15"
                outlined
                dense
                hide-details
                @change="handleFileUpload($event, leave, index)"
              ></v-file-input>
            </div>
            <div v-if="leave.showDescription" class="flex mt-2">
              <ol>
                <li
                  v-for="(attachment, index) in leaveData.filter(
                    (data) => data.crossorigin === leave.type
                  )"
                  :key="index"
                  class="text-blue-600 mt-2 mr-4"
                >
                  <a :href="attachment.src" target="_blank">
                    Document: {{ index + 1 }}
                  </a>
                </li>
              </ol>
              <span
                class="ml-2 w-[50px]"
                v-if="
                  leaveData.filter((data) => data.crossorigin === leave.type)[0]
                    .src
                "
              >
                <LightBox
                  ref="lightbox"
                  :imagesData="
                    leaveData.filter((data) => data.crossorigin === leave.type)
                  "
                  imageWidth="50px"
                  imageHeight="40px"
                />
              </span>
            </div>
          </div>
        </div>

        <div>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-icon
                v-bind="attrs"
                v-on="on"
                @click="togglePolicyDesc(index)"
                :color="$vuetify.theme.currentTheme.primary"
                outlined
                class="ml-4"
                >{{ leave.showDescription ? "mdi-minus" : "mdi-plus" }}</v-icon
              >
            </template>
            <span>
              {{ leave.showDescription ? "Remove Policy" : "Add Policy" }}</span
            >
          </v-tooltip>
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-icon
                v-bind="attrs"
                v-on="on"
                @click="removeLeaveField(index)"
                color="red"
                outlined
                class="ml-4"
                >mdi-delete</v-icon
              >
            </template>
            <span>Delete</span>
          </v-tooltip>
        </div>
      </div>
      <div class="flex justify-end mr-4">
        <v-btn
          @click="addNewLeaveField"
          :color="$vuetify.theme.currentTheme.primary"
          class="white--text"
          v-if="showAddLeaveBtn"
          ><v-icon>mdi-plus</v-icon> Add</v-btn
        >
      </div>
    </div>
    <no-data
      v-else
      title="Nothing to Display"
      :subTitle="`No leave policy set for the organization`"
      :btnText="`Add Leave Policy`"
      :btnAction="addNewLeaveField"
    />

    <!-- Public holidays UI -->
    <div
      class="p-4 pb-2 border-b"
      v-show="isFeatureVisible('PLAN_YEARLY_HOLIDAYS')"
    >
      <h3 class="m-0 font-bold">Select {{ yearRange }} public holidays</h3>
      <span class="text-sm">(* As per the google calendar data)</span>
    </div>
    <div
      v-if="publicHolidays.length"
      v-show="isFeatureVisible('PLAN_YEARLY_HOLIDAYS')"
    >
      <div class="bg-white p-4 mt-6 dark-bg-default">
        <div
          class="flex justify-between mt-4"
          v-for="(holiday, index) in publicHolidays"
          :key="index"
        >
          <div>({{ index + 1 }})</div>
          <v-select
            v-model="holiday.selected"
            :items="holidayOptions"
            item-text="summary"
            class="w-48 px-8"
            label="Select Public Holiday"
            outlined
            dense
            hide-details
            @change="updateHolidayDates(holiday)"
          >
            <template v-slot:selection="{ item }">
              <span v-if="item">{{ item.summary }}</span>
            </template>
          </v-select>
          <v-menu
            ref="startDatePicker"
            v-model="holiday.startDateMenu"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="holiday.startDate"
                label="From"
                v-bind="attrs"
                v-on="on"
                class="w-40 px-8"
                dense
                outlined
                hide-details
                readonly
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="holiday.startDate"
              @input="holiday.startDateMenu = false"
              :min="minDate"
              :max="maxDate"
              @change="handleHoliday(holiday)"
            ></v-date-picker>
          </v-menu>
          <span class="mt-2 px-8">~</span>
          <v-menu
            ref="endDatePicker"
            v-model="holiday.endDateMenu"
            :close-on-content-click="false"
            :nudge-right="40"
            transition="scale-transition"
            offset-y
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="holiday.endDate"
                label="To"
                v-bind="attrs"
                v-on="on"
                class="w-40 px-8"
                dense
                outlined
                hide-details
                readonly
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="holiday.endDate"
              @input="holiday.endDateMenu = false"
              :min="minDate"
              :max="maxDate"
              @change="handleHoliday(holiday)"
            ></v-date-picker>
          </v-menu>
          <v-icon
            @click="removeHoliday(index)"
            color="red"
            outlined
            class="ml-4 mb-8"
            >mdi-delete</v-icon
          >
        </div>
      </div>
      <div class="flex justify-end mr-4">
        <v-btn
          @click="addNewHoliday"
          :color="$vuetify.theme.currentTheme.primary"
          class="white--text"
          ><v-icon>mdi-plus</v-icon> Add</v-btn
        >
      </div>
    </div>
    <no-data
      v-else
      title="Nothing to Display"
      :subTitle="`No public holidays set for the organization`"
      :btnText="`Add Public Holidays`"
      :btnAction="addNewHoliday"
    />

    <v-dialog v-model="openAssignDialog" max-width="800">
      <div class="bg-white dark-bg-custom px-6 py-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="m-0 font-bold">Assign Quota</h3>
          <v-checkbox
            :color="$vuetify.theme.currentTheme.primary"
            v-model="selectAll"
            label="Select All"
            @click="selectAllEmployees"
          ></v-checkbox>
        </div>
        <v-autocomplete
          v-model="selectedEmployees"
          :items="employees"
          :item-text="getFullName"
          item-value="_id"
          :disabled="selectAll"
          hide-selected
          hint="Search Employees"
          persistent-hint
          multiple
          outlined
          dense
        >
        </v-autocomplete>
        <v-sheet class="overflow-y-auto" max-height="300px">
          <v-list v-if="selectedEmployees.length" class="mx-4">
            <v-list-item
              v-for="(employee, index) in selectedEmployees"
              :key="index"
            >
              <v-list-item-content>
                <v-list-item-title>{{
                  getFullName(employees.find((e) => e._id === employee))
                }}</v-list-item-title>
              </v-list-item-content>
              <v-list-item-icon>
                <v-icon @click="removeEmployee(employee)">mdi-close</v-icon>
              </v-list-item-icon>
            </v-list-item>
          </v-list>
        </v-sheet>
        <div class="flex justify-end mt-2">
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="saveQuota"
            :loading="Loading"
            :disabled="Loading"
            >Save</v-btn
          >
        </div>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { fullName } from "@/utils/common";
import LightBox from "@/components/LightBox.vue";
import NoData from "@/components/NoData";
import { isModuleFeatureAllowed } from "@/utils/common";
export default {
  data() {
    return {
      userData: this.$storage.getUniversal("user"),
      orgLeavesTypes: [
        { text: "Privilege Leave", value: "PRIVILEGE_LEAVE" },
        { text: "Earned Leave", value: "EARNED_LEAVE" },
        { text: "Casual leave", value: "CASUAL_LEAVE" },
        { text: "Sick leave", value: "SICK_LEAVE" },
        { text: "Maternity leave", value: "MATERNITY_LEAVE" },
        { text: "Paternity leave", value: "PATERNITY_LEAVE" },
        { text: "Marriage leave", value: "MARRIAGE_LEAVE" },
        { text: "Bereavement leave", value: "BEREAVEMENT_LEAVE" },
        { text: "Compensatory Off", value: "COMPENSATORY_OFF" },
        { text: "Menstruation Leave", value: "MENSTRUATION_LEAVE" },
        { text: "Half-Day Leave", value: "HALF_DAY_LEAVE" },
        { text: "Special Leave", value: "SPECIAL_LEAVE" },
      ],
      leaves: [
        {
          type: null,
          days: "",
          description: "",
          attachment: null,
          showDescription: false,
          selected: false,
        },
      ],
      holidays: [],
      publicHolidays: [{ selected: null, startDate: "", endDate: "" }],
      payload: {},
      selectedQuota: [],
      selectedEmployees: [],
      openAssignDialog: false,
      employees: [],
      orgLeaveDescription: "",
      orgLeaveAttachments: null,
      selectAll: false,
      isChecked: false,
      Loading: false,
    };
  },
  components: {
    LightBox,
    NoData,
  },
  props: {
    receivedData: {
      type: Object,
      default: null,
    },
  },
  watch: {
    employees() {
      this.selectAll = true;
      this.selectedEmployees = this.employees.map((employee) => employee._id);
    },
    receivedData() {
      this.leaves = this.receivedData?.commonLeaves?.map((leave) => {
        return {
          type: leave.typeOfLeave,
          days: leave.numberOfDays,
          description: leave.policyDescription.description,
          supportingDocuments: leave.policyDescription.supportingDocuments,
          showDescription: false,
        };
      });
      this.publicHolidays = this.receivedData?.listOfHolidays?.map((list) => {
        return {
          selected: list.holidayName,
          startDate: this.$dayjs(list.holidayStartDate).format("YYYY-MM-DD"),
          endDate: this.$dayjs(list.holidayEndDate).format("YYYY-MM-DD"),
        };
      });
      this.orgLeaveDescription =
        this.receivedData?.commonLeavePolicy?.description;
      this.setShowDescription();
    },
  },
  computed: {
    yearRange() {
      return `${new Date().getFullYear() - 1} - ${new Date().getFullYear()}`;
    },
    holidayOptions() {
      return this.holidays.map((holiday) => ({
        summary: holiday.summary,
        holiday: holiday,
      }));
    },

    minDate() {
      const currentYear = this.$dayjs().year();
      return this.$dayjs(`${currentYear}-01-01`).format("YYYY-MM-DD");
    },
    maxDate() {
      const currentYear = this.$dayjs().year();
      return this.$dayjs(`${currentYear}-12-31`).format("YYYY-MM-DD");
    },

    showAddLeaveBtn() {
      return this.leaves?.length < this.orgLeavesTypes.length;
    },
    imagesData() {
      if (!this.receivedData) return [];
      return this.receivedData?.commonLeavePolicy?.supportingDocuments.map(
        (el) => ({
          src: process.env.VUE_APP_S3_BASE_URL + el,
          crossorigin: "anonymous",
        })
      );
    },
    leaveData() {
      if (!this.receivedData) return [];
      return this.leaves?.map((leave) => {
        const supportingDocument = leave?.supportingDocuments?.length
          ? leave?.supportingDocuments[0]
          : "";
        return {
          src: supportingDocument
            ? process.env.VUE_APP_S3_BASE_URL + supportingDocument
            : null,
          crossorigin: leave.type,
        };
      });
    },
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed("LEAVE_MANAGEMENT", feature);
    },
    addNewLeaveField() {
      const invalidLeave = this.leaves.some(
        (leave) => !leave.type || !leave.days
      );

      if (invalidLeave) {
        this.$toast.error(
          "Please provide both the type and days for all leaves."
        );
        return;
      }
      this.leaves.push({
        type: null,
        days: "",
        description: "",
        showDescription: false,
      });
    },
    removeLeaveField(index) {
      this.leaves.splice(index, 1);
      this.payload.commonLeaves = this.leaves.map((leave) => ({
        typeOfLeave: leave.type,
        numberOfDays: leave.days,
        policyDescription: {
          description: leave.description,
          supportingDocuments: "",
        },
      }));
      this.$emit("orgLeaves", this.payload);
    },
    togglePolicyDesc(index) {
      this.leaves[index].showDescription = !this.leaves[index].showDescription;
    },
    addNewHoliday() {
      this.publicHolidays.push({
        selected: null,
        startDate: "",
        endDate: "",
        startDateMenu: false,
        endDateMenu: false,
      });
    },
    removeHoliday(index) {
      this.publicHolidays.splice(index, 1);
    },
    availableLeaveTypes(currentIndex) {
      const selectedTypes = this.leaves.map((leave) => leave.type);
      return this.orgLeavesTypes.filter(
        (leaveType) =>
          !selectedTypes.includes(leaveType.value) ||
          this.leaves[currentIndex].type === leaveType.value
      );
    },
    async getHolidaysList() {
      const response = await this.$axios.get("/workforce/list-of-holidays");
      this.holidays = response.data?.data.items.filter(
        (item) => item.description === "Public holiday"
      );
    },
    updateHolidayDates(holiday) {
      const selectedHoliday = this.holidays.find(
        (h) => h.summary === holiday.selected
      );
      const existingHolidayIndex = this.publicHolidays.findIndex(
        (h) => h.selected === holiday.selected
      );

      if (existingHolidayIndex !== -1 && holiday.startDate) {
        const existingHoliday = this.publicHolidays[existingHolidayIndex];
        this.$set(holiday, "startDate", existingHoliday.startDate);
        this.$set(holiday, "endDate", existingHoliday.endDate);
      } else {
        this.$set(
          holiday,
          "startDate",
          selectedHoliday.start ? selectedHoliday.start.date : ""
        );
        this.$set(
          holiday,
          "endDate",
          selectedHoliday.end ? selectedHoliday.end.date : ""
        );
      }

      this.payload["listOfHolidays"] = this.publicHolidays.map((holiday) => ({
        holidayName: holiday.selected,
        holidayStartDate: holiday.startDate,
        holidayEndDate: holiday.endDate,
      }));
      this.$emit("orgLeaves", this.payload);
    },
    handleHoliday(holiday) {
      this.updateHolidayDates(holiday);
    },
    async handleChangeLeaveType() {
      const validLeaves = this.leaves.filter(
        (leave) => leave.type && leave.days
      );
      this.payload["commonLeaves"] = validLeaves.map((leave) => ({
        typeOfLeave: leave.type,
        numberOfDays: leave.days,
        policyDescription: {
          description: leave.description,
          supportingDocuments: leave.supportingDocuments || "",
        },
      }));
      if (this.orgLeaveAttachments && this.orgLeaveDescription) {
        const response = await this.uploadOrgPolicy();
        this.payload["commonLeavePolicy"] = {
          description: this.orgLeaveDescription,
          supportingDocuments: [response.data?.url?.doc_physical_path] || [],
        };
      }

      this.$emit("orgLeaves", this.payload);
    },
    async handleFileUpload(event, leave) {
      const file = leave.attachment;
      const formData = new FormData();
      formData.append("attachments", file);
      formData.append("type", leave.type);
      try {
        const response = await this.$axios.post(
          `/workforce/org/leavepolicies`,
          formData
        );
        const successMessage =
          response?.data?.message ||
          "Leave policy document uploaded successfully";
        this.$toast.success(successMessage);
        leave.supportingDocuments = response.data.url.doc_physical_path;
        this.handleChangeLeaveType();
      } catch (error) {
        console.error("An error occurred:", error);
        const errorMessage = error.response.data.message || error;
        this.$toast.error(errorMessage);
      }
    },
    async uploadOrgPolicy() {
      const file = this.orgLeaveAttachments;
      const formData = new FormData();
      formData.append("attachments", file);
      try {
        const response = await this.$axios.post(
          `/workforce/org/leavepolicies`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        const successMessage =
          response?.data?.message ||
          "Leave policy document uploaded successfully";
        this.$toast.success(successMessage);
        return response;
      } catch (error) {
        console.error("An error occurred:", error);
        const errorMessage = error.response.data.message || "error";
        this.$toast.error(errorMessage);
      }
    },

    assignQuota() {
      this.openAssignDialog = true;
    },
    async fetchUsers() {
      try {
        const params = {
          limit: "*",
          sortBy: "firstName",
          sortOrder: "asc",
        };
        if (
          this.userData?.branch &&
          !this.userData?.role?.includes("WFM_ADMIN")
        ) {
          params.branch = this.userData?.branch;
        }
        const response = await this.$axios.get("/workforce/v2/users", {
          params,
        });
        this.employees = response.data?.users;
      } catch (error) {
        console.log(error);
      }
    },
    selectQuota(leave) {
      const alreadySelected = this.selectedQuota.includes(leave);
      if (alreadySelected) {
        this.selectedQuota.splice(this.selectedQuota.indexOf(leave), 1);
      } else {
        this.selectedQuota.push(leave);
      }
    },
    selectAllEmployees() {
      if (this.selectAll) {
        this.selectedEmployees = this.employees.map((employee) => employee._id);
      } else {
        this.selectedEmployees = [];
      }
    },
    getFullName(employee) {
      return (
        fullName(employee) +
        (employee.wfmRole ? ` (${employee.wfmRole.role})` : "")
      );
    },
    removeEmployee(employeeId) {
      const index = this.selectedEmployees.indexOf(employeeId);
      if (index > -1) {
        this.selectedEmployees.splice(index, 1);
      }
    },
    // saveQuota() {
    //   const promises = this.selectedEmployees.map((employeeId) => {
    //     this.Loading = true;
    //     const filteredLeavePolicies = this.selectedQuota.filter(
    //       (leave) => leave.type !== null
    //     );
    //     const empLeave = filteredLeavePolicies.map((empLeave) => {
    //       return {
    //         typeOfLeave: empLeave.type,
    //         availableBalance: empLeave.days,
    //       };
    //     });
    //     const payload = {
    //       userId: employeeId,
    //       allowedLeaveTypes: filteredLeavePolicies.map((el) => el.type),
    //       leaveBalance: empLeave,
    //     };
    //     return this.$axios.post(`/workforce/emp/leave/quota`, payload);
    //   });
    //   Promise.all(promises)
    //     .then((responses) => {
    //       responses.forEach(() => {
    //         this.$toast.success("Selected employee quota updated successfully");
    //         this.openAssignDialog = false;
    //       });
    //     })
    //     .catch((error) => {
    //       console.error(error);
    //       const errorMessage = error.response.data.message || error;
    //       this.$toast.error(errorMessage);
    //     })
    //     .finally(() => {
    //       this.Loading = false;
    //     });
    // },
    saveQuota() {
      const promises = this.selectedEmployees.map((employeeId) => {
        this.Loading = true;
        const filteredLeavePolicies = this.selectedQuota.filter(
          (leave) => leave.type !== null
        );
        const newLeaveBalance = filteredLeavePolicies.map((empLeave) => {
          return {
            typeOfLeave: empLeave.type,
            availableBalance: empLeave.days,
          };
        });

        const newAllowedLeaveTypes = filteredLeavePolicies.map((el) => el.type);

        // Step 1: Perform GET request to check existing leave data
        return this.$axios
          .get(`/workforce/emp/leave/quota/${employeeId}`)
          .then((response) => {
            const existingQuota = response.data?.employeeLeaveQuota[0];

            // Step 2: If `allowedLeaveTypes` exists, do PUT call; otherwise, do POST call
            if (existingQuota && existingQuota._id) {
              const uniqueAllowedLeaveTypes = newAllowedLeaveTypes.filter(
                (type) => !existingQuota.allowedLeaveTypes.includes(type)
              );

              // Filter new leave balances to exclude those already in leaveBalance
              const uniqueLeaveBalance = newLeaveBalance.filter(
                (newLeave) =>
                  !existingQuota.leaveBalance.some(
                    (existingLeave) =>
                      existingLeave.typeOfLeave === newLeave.typeOfLeave &&
                      existingLeave.availableBalance ===
                        newLeave.availableBalance
                  )
              );

              // If there are no unique types or balances, skip the PUT call
              if (
                uniqueAllowedLeaveTypes.length === 0 &&
                uniqueLeaveBalance.length === 0
              ) {
                return Promise.resolve(); // No changes needed, skip PUT call
              }

              // Prepare payload for PUT request
              const payload = {
                userId: employeeId,
                allowedLeaveTypes: [
                  ...existingQuota.allowedLeaveTypes,
                  ...uniqueAllowedLeaveTypes,
                ],
                leaveBalance: [
                  ...existingQuota.leaveBalance,
                  ...uniqueLeaveBalance,
                ],
              };
              return this.$axios
                .put(`/workforce/emp/leave/quota/${existingQuota._id}`, payload)
                .then(() => {
                  this.$toast.success(`Quota updated for user.`);
                });
            } else {
              const payload = {
                userId: employeeId,
                allowedLeaveTypes: newAllowedLeaveTypes,
                leaveBalance: newLeaveBalance,
              };
              return this.$axios
                .post(`/workforce/emp/leave/quota`, payload)
                .then(() => {
                  this.$toast.success(`Quota created for user.`);
                });
            }
          })
          .catch((error) => {
            console.error(
              `Error during GET/PUT/POST for userId ${employeeId}:`,
              error.response?.data?.message || error
            );
            this.$toast.error(`Failed to process quota for user ${employeeId}`);
          });
      });

      // Step 3: Handle completion of all requests
      Promise.all(promises)
        .then(() => {
          this.$toast.success("Quota updates processed successfully");
          this.openAssignDialog = false;
        })
        .catch((finalError) => {
          console.error("Error in processing quota updates:", finalError);
          this.$toast.error("An error occurred while updating quotas");
        })
        .finally(() => {
          this.Loading = false;
        });
    },
    setShowDescription() {
      this.leaves?.forEach((leave) => {
        if (leave.description) {
          leave.showDescription = true;
        }
      });
    },
  },
  mounted() {
    this.getHolidaysList();
    this.fetchUsers();
    this.leaves = this.receivedData?.commonLeaves?.map((leave) => {
      return {
        type: leave.typeOfLeave,
        days: leave.numberOfDays,
        description: leave.policyDescription.description,
        supportingDocuments: leave.policyDescription.supportingDocuments,
        showDescription: false,
      };
    });
    this.publicHolidays = this.receivedData?.listOfHolidays?.map((list) => {
      return {
        selected: list.holidayName,
        startDate: this.$dayjs(list.holidayStartDate).format("YYYY-MM-DD"),
        endDate: this.$dayjs(list.holidayEndDate).format("YYYY-MM-DD"),
      };
    });
    this.orgLeaveDescription =
      this.receivedData?.commonLeavePolicy?.description;
    this.setShowDescription();
  },
};
</script>
<style scoped>
.border {
  padding: 10px;
  margin-bottom: 10px;
}
</style>
