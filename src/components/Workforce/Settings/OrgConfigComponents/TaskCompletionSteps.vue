<template>
  <div>
    <v-container v-if="!taskSettings" fluid>
      <v-row align="center" justify="center">
        <v-col class="d-flex justify-center">
          <v-progress-circular
            indeterminate
            :color="$vuetify.theme.currentTheme.primary"
            size="64"
            width="6"
          ></v-progress-circular>
        </v-col>
      </v-row>
    </v-container>
    <div v-else>
      <div class="p-5 pt-0 overflow-auto max-h-[70vh]">
        <div class="mb-4">
          <p class="font-medium text-gray-600">
            <i
              >Note: ToDo refers to tasks/visits/complaints assigned to employees that need to be
              completed as part of their workflow.</i
            >
          </p>
        </div>
        <div>
          <div v-show="isFeatureVisible('MANAGE_TASK_COMPLETION_SEQUENCE')">
            <p class="font-semibold mb-0">Arrange ToDo completion steps</p>
            <div class="flex flex-col py-2 text-sm">
              <span>* Hold drag Up'n'Down to re-arrange ToDo completion sequence</span>
              <span>* Click on checkbox to enable/disable ToDo completion step</span>
            </div>
          </div>
          <div>
            <div
              class="flex pb-0 align-center"
              v-show="isFeatureVisible('MANAGE_TASK_COMPLETION_SEQUENCE')"
            >
              <v-label>Org Sequence </v-label>
              <v-switch
                :ripple="false"
                class="mr-16 ml-5 mb-1 custom-switch"
                inset
                dense
                v-model="selectedOptionIsDefault"
                label="Cuality Sequence"
                @change="isCustomListEqual(customList, initialCustomList)"
              ></v-switch>
            </div>
            <transition name="fade" mode="out-in">
              <div class="pb-6 border-b" :key="selectedOptionIsDefault">
                <v-list v-if="selectedOptionIsDefault === false">
                  <!-- @end directive in draggable to be used for handling logic on each drag and drop -->
                  <draggable
                    :disabled="!canEdit"
                    v-model="customList"
                    @end="isCustomListEqual(customList, initialCustomList)"
                  >
                    <v-list-item
                      class="space-x-4 border border-blue-200"
                      :class="index < defaultList?.length - 1 ? 'border-b-0' : ''"
                      v-for="(item, index) in customList"
                      :key="item.key"
                      v-show="item.value.visible"
                    >
                      <v-label class=""> {{ getFilteredIndex(customList, index) + 1 }}. </v-label>
                      <v-list-item-action>
                        <v-checkbox
                          v-model="item.value.checked"
                          :color="$vuetify.theme.currentTheme.primary"
                          @change="isCustomListEqual(customList, initialCustomList)"
                          :readonly="!canEdit"
                        ></v-checkbox>
                      </v-list-item-action>
                      <v-list-item-content>
                        <div class="flex items-center justify-between gap-2">
                          <span> {{ formatLabel(item.key) }} </span>

                          <v-text-field
                            v-model="questionnaireDynamicForm"
                            placeholder="Enter FormId"
                            outlined
                            hide-details
                            label="Enter FormId"
                            dense
                            class="w-24 ml-8"
                            v-if="item.key === 'QUESTIONNAIRE'"
                            @input="handleFormId"
                          ></v-text-field>
                          <div class="flex items-center justify-between gap-2">
                            <div class="w-full flex-grow" />
                            <div
                              class="rounded-md h-8 pl-2 flex bg-gray-100 dark-bg-custom items-center gap-2"
                            >
                              <span class="whitespace-nowrap font-semibold text-xs">Skipable</span>
                              <v-checkbox
                                :color="$vuetify.theme.currentTheme.primary"
                                v-model="item.value.canSkip"
                                @change="isCustomListEqual(customList, initialCustomList)"
                                :readonly="!canEdit"
                              ></v-checkbox>
                            </div>
                          </div>
                        </div>
                      </v-list-item-content>
                    </v-list-item>
                  </draggable>
                </v-list>
                <v-list v-else>
                  <v-list-item
                    class="space-x-4 border border-blue-200"
                    :class="index < defaultList?.length - 1 ? 'border-b-0' : ''"
                    v-for="(item, index) in defaultList"
                    :key="item.key"
                    v-show="item.value.visible"
                  >
                    <v-label class="mr-4">
                      {{ getFilteredIndex(defaultList, index) + 1 }}.
                    </v-label>
                    <v-list-item-content>
                      <div class="flex items-center justify-between gap-2">
                        <span> {{ formatLabel(item.key) }} </span>
                        <v-text-field
                          v-model="questionnaireDynamicForm"
                          placeholder="Enter FormId"
                          outlined
                          hide-details
                          label="Enter FormId"
                          dense
                          class="w-24 ml-8"
                          v-if="item.key === 'QUESTIONNAIRE'"
                          @input="handleFormId"
                        ></v-text-field>
                        <div class="flex items-center justify-between gap-2" />
                      </div>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </div>
            </transition>
          </div>

          <v-card
            :disabled="!isOtpChecked"
            class="wfm-emp-card py-4 border-none"
            v-show="isFeatureVisible('VALIDATE_SERVICE_BASED_ON_EMP_OR_CUSTOMER_OTP')"
          >
            <h3 class="font-bold">Send (6 digit) OTP Verification</h3>
            <CustomSwitch
              :expansion="false"
              :heading="''"
              :infoMessage="'Enable customer OTP verification. By default, OTP will be sent to the employee registered number.'"
              :title="'Send OTP'"
              :leftOption="'Employee Mobile No'"
              :rightOption="'Customer Mobile No'"
              :value="taskSettings?.customerOTPVerification"
              @switchValue="handleSwitch"
              :onDisabled="!isOtpChecked"
            />
          </v-card>
          <v-card
            :disabled="!isComplaintOtpChecked"
            class="wfm-emp-card py-4 border-none"
            v-show="isFeatureVisible('VERIFY_EMPLOYEE_ON_PRODUCT_COLLECTION')"
          >
            <h3 class="font-bold">OTP required while collecting spare part</h3>
            <CustomSwitch
              :expansion="false"
              :heading="''"
              :infoMessage="'Enable employee OTP verification while collecting spare part. OTP will be sent to the employee registered number.'"
              :title="'Send Collection OTP'"
              :leftOption="'Deactivate'"
              :rightOption="'Activate'"
              :value="taskSettings?.isCollectionOtpRequired"
              @switchValue="handleSwitch"
              :onDisabled="!isComplaintOtpChecked"
            />
          </v-card>
          <v-card
            v-if="taskSettings?.allowedProofTypes"
            :disabled="!isTypes"
            class="wfm-emp-card mt-4 border-none"
            v-show="isFeatureVisible('SET_CUSTOM_DOCUMENT_PROOF_COLLECTION')"
          >
            <h3 class="font-bold">Set Document Proof types</h3>
            <CustomComboBox
              heading="Document Proof Types"
              label="Type of Documents"
              infoMessage="Add type of Documents to be collected at the time of employee-customer meeting."
              :comboBoxOptions="taskSettings?.allowedProofTypes"
              @comboBoxValue="handleInput"
            />
          </v-card>
          <v-card
            v-if="taskSettings?.allowedMeetingOutcomesTypes"
            class="wfm-emp-card mt-4 border-none"
            :disabled="!isTypes"
            v-show="isFeatureVisible('SET_CUSTOM_MEETING_OUT_COMES')"
          >
            <h3 class="font-bold">Set Possible Meeting Outcomes</h3>
            <CustomComboBox
              heading="Types of Meeting Outcome"
              label="Types of Meeting Outcome"
              infoMessage="Add types of possible meeting Outcomes expected at the time of employee-customer meeting."
              :comboBoxOptions="taskSettings?.allowedMeetingOutcomesTypes"
              @comboBoxValue="handleInput"
            />
          </v-card>

          <div v-show="isFeatureVisible('AUTO_CARRY_FORWARD_TASK')">
            <h3 class="font-bold">Auto carry forward your ToDo for Next day</h3>
            <CustomSwitch
              :expansion="false"
              :heading="''"
              infoMessage="Reschedule pending ToDo automatically to the next day."
              title="Enable auto-rescheduling"
              :leftOption="'Deactivate'"
              :rightOption="'Activate'"
              :value="taskSettings?.isAutoReschedule"
              @switchValue="handleSwitch"
              @refreshReschdule="refreshReschdule"
            />
          </div>
          <v-card class="wfm-emp-card mt-4 border-none">
            <div class="mt-4">
              <h3 class="font-bold">Allow ToDo accept/reject</h3>
              <CustomSwitch
                :expansion="false"
                :heading="''"
                infoMessage="Employee will be able to accept/reject an assigned ToDo"
                title="Enable ToDo acknowledgement"
                :leftOption="'Deactivate'"
                :rightOption="'Activate'"
                :value="taskSettings?.allowTaskAcknowledgement"
                @switchValue="handleSwitch"
              />
            </div>
          </v-card>
          <v-card
            class="wfm-emp-card mt-4 border-none"
            v-show="isFeatureVisible('SET_TASK_COMPLETION_ESTIMATION')"
          >
            <div class="mt-4">
              <h3 class="font-bold">Auto estimate ToDO end by the system</h3>
              <CustomSwitch
                :expansion="false"
                :heading="''"
                infoMessage="ToDo complete estimation time."
                title="Enable ToDo auto-estimation"
                :leftOption="'Deactivate'"
                :rightOption="'Activate'"
                :value="taskSettings?.allowTaskAutoEstimation"
                @switchValue="handleSwitch"
              />
            </div>
            <div class="mt-4" v-show="isFeatureVisible('SET_TASK_COMPLETION_ESTIMATION')">
              <h3 class="font-bold">ToDo estimation threshold</h3>
              <CustomSlider
                infoMessage="ToDo is expected to complete with this threshold percentage value."
                :sliderLabel="`ToDo estimation threshold`"
                :value="taskSettings?.taskEstimationThreshold"
                :max="12"
                :min="1"
                :step="1"
                @emitSlider="handleSlider"
              />
            </div>
            <!-- currnet using it for complaint only we need to improve for other usecase in app -->
            <div class="mt-4" v-show="isFeatureVisible('SET_TASK_PRE_DEFINED_TITLES')">
              <h3 class="font-bold">Set ToDo Titles</h3>
              <CustomComboBox
                heading="Types of ToDo Titles"
                label="Types of ToDo Titles"
                infoMessage="Add types of ToDo, these options will be visible while logging the ToDo(s)."
                :comboBoxOptions="taskSettings?.predefinedComplaintTitles"
                @comboBoxValue="handleInput"
              />
            </div>
          </v-card>

          <div>
            <h3 class="font-bold" v-show="isFeatureVisible('CREATE_RECURSIVE_TASKS')">
              ToDo Recurrency
            </h3>
            <CustomSwitch
              :expansion="false"
              heading="''"
              infoMessage="Completes the ToDo without going through the ToDo completion steps."
              title="ToDo Direct Complete"
              :leftOption="'Deactivate'"
              :rightOption="'Activate'"
              :value="taskSettings?.isTaskDirectCompletionAllowedOrg"
              @switchValue="handleSwitch"
              :visibility="isFeatureVisible('SET_TASK_DIRECT_COMPLETION')"
            />

            <CustomSwitch
              :expansion="false"
              heading="''"
              infoMessage="System will automatic create ToDo(s)."
              title="ToDo Recurrency Allowed"
              :leftOption="'Deactivate'"
              :rightOption="'Activate'"
              :value="taskSettings?.isTaskRecurrenceAllowedOrg"
              @switchValue="handleSwitch"
              :visibility="isFeatureVisible('CREATE_RECURSIVE_TASKS')"
            />
            <CustomSwitch
              :expansion="false"
              heading="''"
              infoMessage="System will automatic assign the recurring ToDo."
              :title="`ToDo Auto Assign`"
              :leftOption="`Dectivate`"
              :rightOption="`Activate`"
              :value="taskSettings?.isTaskAutoAssignedAllowedOrg"
              @switchValue="handleSwitch"
              :onDisabled="!isTaskAutoAssignAllowed"
              :visibility="isFeatureVisible('ENABLE_AUTO_ASSIGNMENT')"
            />
            <CustomSwitch
              :expansion="false"
              heading="''"
              infoMessage="System will track the employee location."
              :title="`Track employee actions`"
              :leftOption="`Dectivate`"
              :rightOption="`Activate`"
              :value="taskSettings?.isTrackEmployeeActions"
              @switchValue="handleSwitch"
              :onDisabled="!isTrackActionEnabled('DISTANCE_CALCULATION')"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
import CustomSwitch from './CustomSwitch.vue'
import CustomComboBox from './CustomComboBox.vue'
import CustomSlider from './CustomSlider.vue'
import { isModuleFeatureAllowed } from '@/utils/common'

export default {
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      orgUser: this.$storage.getUniversal('user'),
      customList: [],
      initialCustomList: [],
      defaultList: [],
      selectedOptionIsDefault: true,
      hasConfigChanges: false,
      panel: [0],
      isOtpChecked: true,
      isComplaintOtpChecked: true,
      isTypes: false,
      data: {},
      switchState: {},
      taskSettings: {},
      labelMapping: {
        MEETING_NOTES_WITH_ATTACHMENTS: 'Create Meeting Notes & Attach documents',
        QUESTIONNAIRE: 'Collect Survey (Questionnaire)',
        COMPLAINT_INVENTORY: 'Verify Inventory Stock (will be used in complaint management)',
        SELFIE: 'Take a photo for verification (faulty products, amc, bills etc.)',
        AUDIO_RECORDING: 'Record Meeting Conversation',
        OTP_VERIFICATION: 'Verify ToDo closer with OTP verification'
      },
      isTaskAutoAssignAllowed: this.taskSettings?.isTaskAutoAssignedAllowedOrg || false,
      keyFeatureMapping: {
        MEETING_NOTES_WITH_ATTACHMENTS: 'MEETING_NOTES_WITH_ATTACHMENTS',
        QUESTIONNAIRE: 'ENABLE_QUESTIONNAIRE',
        COMPLAINT_INVENTORY: 'ENABLE_INVENTORY_TYPE',
        SELFIE: 'ENABLE_TASK_PICTURE',
        AUDIO_RECORDING: 'ENABLE_MEETING_AUDIO_RECORDING',
        OTP_VERIFICATION: 'ENABLE_OTP_VERIFICATION'
      },
      fallbackDefaultSteps: [
        'MEETING_NOTES_WITH_ATTACHMENTS',
        'QUESTIONNAIRE',
        'COMPLAINT_INVENTORY',
        'SELFIE',
        'AUDIO_RECORDING',
        'OTP_VERIFICATION'
      ],
      questionnaireDynamicForm: null
    }
  },
  components: {
    draggable,
    CustomSwitch,
    CustomComboBox,
    CustomSlider
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    },
    orgData: Object
  },
  watch: {
    receivedData: {
      async handler(newVal) {
        if (newVal !== null) {
          this.receivedData = newVal
          await this.getTaskSettings()
          await this.getConfig()
          this.questionnaireDynamicForm = this.taskSettings?.questionnaireDynamicForm
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getConfig()
    if (this.taskSettings) {
      this.switchState = {
        customerOTPVerification: this.taskSettings?.customerOTPVerification,

        isAutoReschedule: this.taskSettings?.isAutoReschedule,
        isTaskRecurrenceAllowedOrg: this.taskSettings?.isTaskRecurrenceAllowedOrg,
        isTaskDirectCompletionAllowedOrg: this.taskSettings?.isTaskDirectCompletionAllowedOrg,
        isTaskAutoAssignedAllowedOrg: this.taskSettings?.isTaskAutoAssignedAllowedOrg
      }
    }
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.settingEdit)
    },
    disableUpdateButton() {
      return !(this.isSliderChanged || this.hasConfigChanges) || this.checkedCustomCount < 1
    },
    filteredCustomList() {
      return this.customList.filter((item) => item.value.visible)
    },
    filteredDefaultList() {
      return this.defaultList.filter((item) => item.value.visible)
    }
  },
  methods: {
    async getTaskSettings() {
      try {
        const { data } = await this.$axios.get('/workforce/orgSetup/task-settings')
        this.taskSettings = data.taskSettings || {}
        // console.log(!!data.taskSettings._id)
        return !!data.taskSettings?._id
      } catch (error) {
        console.log(error)
      }
    },
    async saveTaskCompletionSteps() {
      try {
        const payload = {
          taskCompletionStepsConfig: this.selectedOptionIsDefault
            ? this.customList.map((item) => ({
                step: item.key,
                canSkip: false
              }))
            : this.customList
                .filter((item) => item.value.checked)
                .map((item) => ({
                  step: item.key,
                  canSkip: item.value.canSkip
                })),
          enableType: this.selectedOptionIsDefault ? 'DEFAULT' : 'CUSTOM',
          customerOTPVerification: this.switchState.customerOTPVerification,
          isCollectionOtpRequired: this.switchState.isCollectionOtpRequired,
          allowedProofTypes: this.data.allowedProofTypes || [],
          allowedMeetingOutcomesTypes: this.data.allowedMeetingOutcomesTypes || [],
          isAutoReschedule: this.switchState.isAutoReschedule,
          allowTaskAcknowledgement: this.switchState.allowTaskAcknowledgement,
          allowTaskAutoEstimation: this.switchState.allowTaskAutoEstimation,
          taskEstimationThreshold: this.data.taskEstimationThreshold || 0,
          predefinedComplaintTitles: this.data.predefinedComplaintTitles || [],
          isTaskDirectCompletionAllowedOrg: this.switchState.isTaskDirectCompletionAllowedOrg,
          isTaskRecurrenceAllowedOrg: this.switchState.isTaskRecurrenceAllowedOrg,
          isTaskAutoAssignedAllowedOrg: this.switchState.isTaskAutoAssignedAllowedOrg,
          isTrackEmployeeActions: this.switchState.isTrackEmployeeActions,
          questionnaireDynamicForm: this.questionnaireDynamicForm
        }
        if (await this.getTaskSettings()) {
          this.$axios.put('/workforce/orgSetup/task-settings', payload)
        } else {
          this.$axios.post('/workforce/orgSetup/task-settings', payload)
        }
        this.$toast.success('Task completion steps updated successfully')
      } catch (error) {
        console.log(error)
      }
    },
    isFeatureVisible(feature) {
      if (feature === 'MEETING_NOTES_WITH_ATTACHMENTS') {
        return true
      }
      return isModuleFeatureAllowed('TASK_MANAGEMENT', feature)
    },
    isTrackActionEnabled(feature) {
      return isModuleFeatureAllowed('ATTENDANCE', feature)
    },
    getFilteredIndex(list, index) {
      const visibleItems = list.filter((item) => item.value.visible)
      return visibleItems.indexOf(list[index])
    },
    formatLabel(key) {
      return this.labelMapping[key] || key
    },
    async refreshReschdule() {
      try {
        const payload = {
          organization_id: this.orgUser.organization_id,
          date: this.$dayjs().format('YYYY-MM-DD')
        }
        this.$axios.post('/workforce/manualcron/task/auto-rescheduled', payload)
        this.$toast.success('Task auto-rescheduled data refreshed successfully')
      } catch (error) {
        console.log(error)
        this.$toast.error(error)
      }
    },
    async getConfig() {
      try {
        if (!this.taskSettings) return

        // Use fallback values if API doesn't return the necessary data
        const defaultSteps =
          this.taskSettings?.taskCompletionDefaultSteps || this.fallbackDefaultSteps
        const configSteps =
          this.taskSettings?.taskCompletionStepsConfig || this.fallbackDefaultSteps

        // Map default steps to defaultList
        this.defaultList = defaultSteps.map((key) => ({
          key,
          value: {
            label: key,
            checked: false,
            visible: this.isFeatureVisible(this.keyFeatureMapping[key] || key),
            canSkip: false
          }
        }))

        // Create customList from taskCompletionStepsConfig
        const customList = configSteps.reduce((acc, config) => {
          const defaultItem = this.defaultList.find((item) => item.key === config.step)
          if (defaultItem) {
            acc.push({
              key: defaultItem.key,
              value: {
                ...defaultItem.value,
                checked: true,
                canSkip: config.canSkip
              }
            })
          }
          return acc
        }, [])

        // Add remaining default items to customList
        this.defaultList?.forEach((defaultItem) => {
          if (!customList.some((item) => item.key === defaultItem.key)) {
            customList.push({ ...defaultItem })
          }
        })

        // Update component state
        this.initialCustomList = this.taskSettings?.taskCompletionStepsConfig
        this.initialType = this.taskSettings?.enableType === 'DEFAULT'
        this.selectedOptionIsDefault = this.initialType
        this.customList = customList

        // Update flags based on checked items
        const checkedItems = new Set(
          customList?.filter((item) => item.value.checked).map((item) => item.value.label)
        )

        this.isOtpChecked = checkedItems.has('OTP_VERIFICATION')
        this.isComplaintOtpChecked = checkedItems.has('COMPLAINT_INVENTORY')
        this.isTypes = checkedItems.has('MEETING_NOTES_WITH_ATTACHMENTS')

        // Filter visible items
        this.customList = this.customList?.filter((item) => item.value.visible)
        this.defaultList = this.defaultList?.filter((item) => item.value.visible)

        // this.$emit('configDataReceived', this.taskSettings)
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    isCustomListEqual(current, old) {
      const OptVerify = current.filter(
        (item) => item.value.label === 'OTP_VERIFICATION' && item.value.checked
      )
      if (OptVerify.length > 0) {
        this.isOtpChecked = true
      } else {
        this.isOtpChecked = false
      }

      const collectionOptVerify = this.customList.filter(
        (item) => item.value.label === 'COMPLAINT_INVENTORY' && item.value.checked
      )
      if (collectionOptVerify.length > 0) {
        this.isComplaintOtpChecked = true
      } else {
        this.isComplaintOtpChecked = false
      }

      const isTypesDoc = current.filter(
        (item) => item.value.label === 'MEETING_NOTES_WITH_ATTACHMENTS' && item.value.checked
      )
      if (isTypesDoc.length > 0) {
        this.isTypes = true
      } else {
        this.isTypes = false
      }
      const currentCheckedKeys = current
        .filter((item) => item.value.checked)
        .map((item) => item.key)

      const currentType = this.selectedOptionIsDefault
      if (
        JSON.stringify(currentCheckedKeys) === JSON.stringify(old) &&
        currentType === this.initialType
      ) {
        this.hasConfigChanges = false
      } else {
        this.hasConfigChanges = true
      }
      this.$emit('isStepOrderChange', this.hasConfigChanges)
      const obj = {
        customList: this.customList,
        isDefault: this.selectedOptionIsDefault
      }
      this.$emit('customList', obj)
    },

    // handle switch case values
    handleSwitch(val) {
      console.log(val)
      switch (val?.title) {
        case 'SEND_OTP':
          this.switchState.customerOTPVerification = val.value
          break
        case 'SEND_COLLECTION_OTP':
          this.switchState.isCollectionOtpRequired = val.value
          break
        case 'ENABLE_AUTO-RESCHEDULING':
          this.switchState.isAutoReschedule = val.value
          break
        case 'TODO_DIRECT_COMPLETE':
          this.switchState.isTaskDirectCompletionAllowedOrg = val.value
          break
        case 'TODO_RECURRENCY_ALLOWED':
          this.switchState.isTaskRecurrenceAllowedOrg = val.value
          this.isTaskAutoAssignAllowed = !this.isTaskAutoAssignAllowed
          break
        case 'TODO_AUTO_ASSIGN':
          this.switchState.isTaskAutoAssignedAllowedOrg = val.value
          break
        case 'ENABLE_TODO_AUTO-ESTIMATION':
          this.switchState.allowTaskAutoEstimation = val.value
          break
        case 'ENABLE_TODO_ACKNOWLEDGEMENT':
          this.switchState.allowTaskAcknowledgement = val.value
          break
        case 'TRACK_EMPLOYEE_ACTIONS':
          this.switchState.isTrackEmployeeActions = val.value
          break
        default:
          break
      }
      this.data = {
        ...this.data,
        ...this.switchState
      }
      this.$emit('Docs', this.data)
    },

    // handle input values
    handleInput(val) {
      if (val?.title === 'DOCUMENT_PROOF_TYPES' && val?.value !== undefined) {
        this.data.allowedProofTypes = val.value
      }

      if (val?.title === 'TYPES_OF_MEETING_OUTCOME' && val?.value !== undefined) {
        this.data.allowedMeetingOutcomesTypes = val.value
      }
      if (val?.title === 'TYPES_OF_COMPLAINT_TITLES' && val?.value !== undefined) {
        this.data.predefinedComplaintTitles = val.value
      }

      this.$emit('Docs', this.data)
    },
    handleSlider(val) {
      this.data = {
        ...this.data,
        ...this.switchState,
        taskEstimationThreshold: val
      }
      this.$emit('Docs', this.data)
    },
    handleFormId() {
      const value = this.questionnaireDynamicForm
      this.$emit('questionnaireDynamicForm', value)
    }
  }
}
</script>
<style scoped>
.wfm-emp-card {
  border: none !important;
  box-shadow: none !important;
  margin-top: 0 !important;
}
</style>
