<template>
  <div v-if="attendanceSettings">
    <div class="p-5 pt-0 overflow-auto max-h-[70vh]">
      <div class="sticky top-0" :style="{ backgroundColor: 'white', zIndex: 100 }">
        <v-tabs
          v-model="tabs"
          align-with-title
          :background-color="$vuetify.theme.currentTheme.backGroundColor"
          :color="$vuetify.theme.currentTheme.primary"
        >
          <v-tab
            :ripple="false"
            v-for="(tab, index) in tabItems"
            :key="index"
            class="ml-2"
            :color="$vuetify.theme.currentTheme.primary"
          >
            {{ tab.name }}
          </v-tab>
          <v-tabs-slider color="#32D583"></v-tabs-slider>
        </v-tabs>
      </div>
      <div>
        <div v-if="currentTab === 'attendance'">
          <div
            class="mt-4 border items-center border-blue-200 bg-white flex dark-bg-custom"
            v-show="isFeatureVisible('SET_SHIFT_TIME')"
          >
            <span
              v-tooltip="{
                text: `Shift start timing as per organization policy`
              }"
            >
              <v-icon class="ml-4">mdi-information</v-icon>
            </span>

            <v-dialog
              ref="startDialog"
              v-model="modal2"
              :return-value.sync="time"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="time"
                  label="Shift start time"
                  prepend-icon="mdi-clock-time-four-outline"
                  readonly
                  class="px-6"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-time-picker v-if="modal2" format="ampm" v-model="time" full-width>
                <v-spacer></v-spacer>
                <v-btn outlined @click="modal2 = false"> Cancel </v-btn>
                <v-btn text :color="$vuetify.theme.currentTheme.primary" @click="handleOkClick">
                  OK
                </v-btn>
              </v-time-picker>
            </v-dialog>
          </div>
          <div
            class="mt-4 border items-center border-blue-200 bg-white flex dark-bg-custom"
            v-show="isFeatureVisible('SET_SHIFT_TIME')"
          >
            <span
              v-tooltip="{
                text: `Shift end timing as per organization policy`
              }"
            >
              <v-icon class="ml-4">mdi-information</v-icon>
            </span>

            <v-dialog
              ref="endDialog"
              v-model="modal3"
              :return-value.sync="time2"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="time2"
                  label="Shift end time"
                  prepend-icon="mdi-clock-time-four-outline"
                  readonly
                  class="px-6"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-time-picker v-if="modal3" format="ampm" v-model="time2" full-width>
                <v-spacer></v-spacer>
                <v-btn outlined @click="modal3 = false"> Cancel </v-btn>
                <v-btn
                  text
                  :color="$vuetify.theme.currentTheme.primary"
                  @click="handleOkClickTime2"
                >
                  OK
                </v-btn>
              </v-time-picker>
            </v-dialog>
          </div>

          <CustomSwitch
            id="attendance_1"
            :expansion="false"
            :heading="``"
            infoMessage="System will allow all the employees to clock-in and clock-out off-working hours."
            :title="`Allow Employees in off-working hours`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.allowCheckInAllTime"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('ENABLE_EMPLOYEE_OFF_WORKIN_HOURS')"
          />

          <div
            class="flex items-center mt-4 px-4 py-2 border border-blue-200 bg-white dark-bg-custom"
            v-show="isFeatureVisible('SET_WORKING_DAYS')"
          >
            <span
              v-tooltip="{
                text: `Organization weekly working days`
              }"
            >
              <v-icon>mdi-information</v-icon>
            </span>
            <div class="flex justify-between align-center">
              <div class="flex align-center gap-4">
                <p class="ml-4 mt-4">Working Days</p>
                <v-menu
                  v-model="menu"
                  transition="slide-x-reverse-transition"
                  :close-on-content-click="false"
                  offset-y
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      small
                      :color="$vuetify.theme.currentTheme.primary"
                      class="white--text"
                      v-bind="attrs"
                      v-on="on"
                      >Manual Attendance Mark</v-btn
                    >
                  </template>
                  <v-card>
                    <v-date-picker v-model="selectedDate" no-title :min="minDate">
                      <v-spacer></v-spacer>
                      <v-btn outlined @click="menu = false">Cancel</v-btn>
                      <v-btn
                        text
                        :color="$vuetify.theme.currentTheme.primary"
                        @click="manualAttendance"
                        >OK</v-btn
                      >
                    </v-date-picker>
                  </v-card>
                </v-menu>
              </div>

              <div class="pl-[7rem] mt-3 mb-2">
                <v-tooltip bottom v-for="day in days" :key="day" class="bg-slate-950">
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      @click="toggleDay(day)"
                      class="white--text mr-3"
                      :color="
                        selectedDays.includes(day) ? $vuetify.theme.currentTheme.primary : '#babfbb'
                      "
                      fab
                      small
                      depressed
                      :ripple="false"
                      v-bind="attrs"
                      v-on="on"
                    >
                      {{ day[0] }}
                    </v-btn>
                  </template>
                  <span>{{ day }}</span>
                </v-tooltip>
              </div>
            </div>
          </div>

          <CustomSwitch
            id="attendance_2"
            :expansion="false"
            :heading="``"
            infoMessage="System will calulate the attendance based on the threshhold set."
            :title="`Allow System Calculation`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.allowSystemCalculation"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('SYSTEM_CALCULATION')"
          />
          <CustomSlider
            infoMessage="Daily working hour duration for each employee ex.Each employee has to work 7hrs a day."
            :sliderLabel="`Daily Working Hour`"
            :value="attendanceSettings?.workingHours"
            :max="12"
            :min="1"
            :step="1"
            @emitSlider="updateWorkHour"
            :visibility="isFeatureVisible('SET_DAILY_WORKING_HOUR_EXPECTED')"
          />

          <CustomSlider
            infoMessage="Minimum working hour required for Full day attendance."
            :sliderLabel="`Full Day Threshold(%)`"
            :value="attendanceSettings?.fullDayThreshold"
            :max="100"
            :min="50"
            :step="5"
            @emitSlider="updateFullDayThreshold"
            :visibility="isFeatureVisible('SET_FULL_DAY_THRESHOLD')"
          />
          <CustomSlider
            infoMessage="Minimum working hour required for Half day."
            :sliderLabel="`Half Day Threshold(%)`"
            :value="attendanceSettings?.halfDayThreshold"
            :max="50"
            :min="0"
            :step="5"
            @emitSlider="updateHalfDayThreshold"
            :visibility="isFeatureVisible('SET_HALF_DAY_THRESHOLD')"
          />
          <CustomSlider
            infoMessage="Minimum working hour required for Overtime."
            :sliderLabel="`Over Time Threshold(%)`"
            :value="attendanceSettings?.overTimeThreshold"
            :max="100"
            :min="0"
            :step="5"
            @emitSlider="updateOverTimeThreshold"
            :visibility="isFeatureVisible('SET_OVER_TIME_THRESHOLD')"
          />

          <CustomSwitch
            id="attendance_3"
            :expansion="false"
            :heading="``"
            infoMessage="System automatically clock-out each clocked-in employee at day ends in b/w 11:30pm - 12:00am)."
            :title="`Auto Attendance Mark`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.autoAttendanceMark"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('AUTO_ATTENDANE_MARK')"
          />
          <v-card :disabled="!isAutoCheckoutEnable" class="wfm-emp-card mt-6 border-none">
            <CustomSwitch
              id="attendance_4"
              :expansion="false"
              :heading="``"
              infoMessage="System automatically clock-out each clocked-in employee at day ends in b/w 11:30pm - 12:00am)."
              :title="`Auto Checkout`"
              :leftOption="`Dectivate`"
              :rightOption="`Activate`"
              :value="attendanceSettings?.autoCheckout"
              @switchValue="handleSwitch"
              :visibility="isFeatureVisible('AUTO_CHECKOUT')"
            />
          </v-card>
          <CustomSwitch
            id="attendance_5"
            :expansion="false"
            :heading="``"
            infoMessage="Restict employees from checking out for the day before completing their daily task assigned."
            :title="`Strict Clock Out`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.strictCheckout"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('STRICT_CLOCKOUT_FOR_DAILY_TASKS')"
          />

          <CustomSwitch
            id="attendance_6"
            :expansion="false"
            :heading="''"
            infoMessage="Enforce employee to capture selfie while clock-in."
            :title="`Clock In (with selfie)`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.isCheckedInSelfie"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('CLOCKIN_WITH_SELFIE')"
          />

          <CustomSwitch
            id="attendance_7"
            :expansion="false"
            :heading="''"
            infoMessage="Enforce employee to capture selfie while clock-out."
            :title="`Clock Out (with selfie)`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.isCheckedOutSelfie"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('CLOCKOUT_WITH_SELFIE')"
          />
          <v-list class="mt-2 dark-bg-custom" v-show="isFeatureVisible('LINK_ROSTER')">
            <v-list-item-group class="dark-bg-custom">
              <v-list-item class="space-x-4 border border-blue-200 h-14 dark-bg-custom">
                <p class="mb-0">Shift Types</p>
                <v-chip
                  v-for="(shift, shiftIndex) in shifts"
                  :key="shiftIndex"
                  label
                  small
                  :color="isShiftDisabled(shift)?.textColor"
                  :text-color="isShiftDisabled(shift)?.bgColor"
                  class="border border-black"
                >
                  <span class="pr-2">
                    {{ shift?.shiftName }}
                  </span>
                  <v-icon
                    small
                    @click="confirmRemoveShift(shift, shiftIndex)"
                    color="red"
                    class="ml-2"
                  >
                    $delete
                  </v-icon>
                  <v-icon small @click="editItem(shift, shiftIndex)" color="green" class="ml-1">
                    $edit
                  </v-icon>
                </v-chip>
                <div class="flex justify-end" v-show="isCreateShiftVisible('CREATE_SHIFTS')">
                  <v-btn text :color="$vuetify.theme.currentTheme.primary" @click="addNewShift"
                    >Create new shift</v-btn
                  >
                </div>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </div>

        <div v-if="currentTab === 'geoFencing'">
          <div
            class="flex mt-4 px-4 py-2 border border-blue-200 bg-white dark-bg-custom"
            v-show="isCreateShiftVisible('DISTANCE_CALCULATION')"
          >
            <v-tooltip bottom>
              <template v-slot:activator="{ on, attrs }">
                <v-icon v-bind="attrs" v-on="on">mdi-information</v-icon>
              </template>
              <span>Manual distance calculation for all the employees of organization</span>
            </v-tooltip>
            <div class="flex justify-between align-center">
              <p class="ml-4 mt-4">Calculate Employees Distance</p>

              <v-menu
                v-model="distanceMenu"
                transition="slide-x-reverse-transition"
                :close-on-content-click="false"
                offset-y
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    small
                    :color="$vuetify.theme.currentTheme.primary"
                    class="white--text"
                    v-bind="attrs"
                    v-on="on"
                    >Manual Distance Calculation</v-btn
                  >
                </template>
                <v-card>
                  <v-date-picker v-model="distanceDate" no-title :min="minDate" :max="maxDate">
                    <v-spacer></v-spacer>
                    <v-btn outlined @click="distanceMenu = false">Cancel</v-btn>
                    <v-btn text :color="$vuetify.theme.currentTheme.primary" @click="manualDistance"
                      >OK</v-btn
                    >
                  </v-date-picker>
                </v-card>
              </v-menu>
            </div>
          </div>
          <CustomSwitch
            id="attendance_8"
            :expansion="false"
            :heading="``"
            infoMessage="Restrict Clock-In/Clock-Out based on Global Level Geofencing"
            :title="`Restrict Clock-In/Clock-Out based on Global Level Geofencing`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="receivedData?.geoFencingEnabled"
            @switchValue="handleSwitch"
            @openQrPopup="onOpenQrPopup"
            geoFenceEnable="true"
            @mapDialog="handleOpen"
            :visibility="isFeatureVisible('ENABLE_GLOBAL_GEO_FENCING_MODE')"
          />

          <CustomSwitch
            id="attendance_9"
            :expansion="false"
            :heading="``"
            infoMessage="Restrict Clock-In/Clock-Out based on Branch Level Geofencing."
            :title="`Restrict Clock-In/Clock-Out based on Branch Level Geofencing`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="receivedData?.enableBranchGeofencing"
            :onDisabled="!branchFencingValue"
            @switchValue="handleFencing"
            :visibility="isGeoFeatureVisible('ENABLE_BRANCH_WISE_GEO_FENCING_MODE')"
          />
          <CustomSwitch
            id="attendance_10"
            :expansion="false"
            :heading="``"
            infoMessage="Enable/Disable Clock-In/Clock-Out Geofencing from any branch location."
            :title="`Enable/Disable Clock-In/Clock-Out Geofencing from any branch location`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="receivedData?.allowCheckInFromAllDefinedLocation"
            @switchValue="handleAllBranchCheckIn"
            :onDisabled="!allBranchFencing"
            :visibility="isGeoFeatureVisible('ALLOW_CLOCKIN_CLOCKOUT_FROM_ALL_GEO_FENCING')"
          />

          <CustomSwitch
            id="attendance_11"
            :expansion="false"
            :heading="''"
            infoMessage="Restrict Clock-In/Clock-Out from user home location (For WFH employees)."
            :title="`Restrict Clock-In/Clock-Out from user home location (For WFH employees)`"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="receivedData?.homeGeofenceEnabled"
            @switchValue="handleSwitch"
            :onDisabled="!globalFence"
            :visibility="isFeatureVisible('ENABLE_GEO_FENCING_FOR_WORK_FROM_HOME_EMPLOYEES')"
          />

          <CustomSlider
            v-if="receivedData?.homeGeofenceEnabled || radiusEnable"
            infoMessage="Set the radius to allow work-from-user to clock-in."
            :sliderLabel="`Geofencing Radius for home employees`"
            :value="receivedData?.homeGeofenceRadius"
            :max="4000"
            :min="200"
            :step="5"
            @emitSlider="updateHomeFenceRadius"
          />

          <!-- <CustomSwitch
           id="attendance_12"
            :expansion="false"
            :heading="''"
            infoMessage="Enforce employee to scan QR code while clock-in."
            title="Clock in (with qr code)"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.clockInWithQrCode"
            :onDisabled="!globalFence"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('CLOCKIN_WITH_QR_CODE')"
          />

          <CustomSwitch
          id="attendance_13"
            :expansion="false"
            :heading="''"
            infoMessage="Enforce employee to scan QR code while clock-out."
            title="Clock out (with qr code)"
            :leftOption="`Dectivate`"
            :rightOption="`Activate`"
            :value="attendanceSettings?.clockOutWithQrCode"
            :onDisabled="!globalFence"
            @switchValue="handleSwitch"
            :visibility="isFeatureVisible('CLOCKOUT_WITH_QR_CODE')"
          /> -->
        </div>
      </div>

      <MapRadiusModal
        :dialog="dialog"
        v-if="dialog"
        @mapClose="handleClose"
        @handleGeofence="saveGeofence"
        :value="receivedData?.geoFencingData"
      />

      <v-navigation-drawer
        :color="$vuetify.theme.currentTheme.navigationColor"
        class="shift-drawer"
        width="50%"
        height="100%"
        v-model="drawer"
        absolute
        right
      >
        <div class="overflow-auto">
          <v-card-title class="text-h6 justify-center"> Add/Edit Shift </v-card-title>
          <div class="p-4 border flex justify-between">
            <div class="mt-2 w-full">
              <v-select
                class="px-8 mb-3"
                v-model="shiftType"
                :items="shiftTypes"
                label="Shift Types"
                dense
                outlined
                hide-details
              >
              </v-select>
              <v-text-field
                class="px-8"
                v-model="shiftName"
                label="Shift Name"
                dense
                outlined
                hide-details
              ></v-text-field>
              <v-text-field
                class="px-8 mt-3"
                v-model="shiftStartTime"
                label="Shift Start Time"
                type="time"
                dense
                outlined
                hide-details
                rows="3"
              ></v-text-field>
              <v-text-field
                class="px-8 mt-3"
                v-model="shiftEndTime"
                label="Shift End Time"
                type="time"
                dense
                outlined
                hide-details
                rows="3"
              ></v-text-field>
              <v-select
                class="px-8 mt-3"
                v-model="status"
                :items="shiftStatus"
                label="Shift Status"
                item-text="text"
                item-value="value"
                dense
                outlined
                hide-details
              >
              </v-select>
            </div>
          </div>
          <div class="flex justify-end mr-4 mt-4">
            <v-btn @click="cancel" class="mr-2" outlined>Cancel</v-btn>
            <v-btn
              @click="saveShift"
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              >Save</v-btn
            >
          </div>
          <AlertPopUp
            :showConfirmationDialog="showConfirmationDialog"
            :confirmationTitle="'Confirm Delete'"
            :confirmationMessage="'Are you sure you want to delete the shift?'"
            :confirmationActionText="'Delete'"
            :confirmationActionColor="'red'"
            :performAction="shiftDelete"
            :cancel="cancel"
          />
        </div>
      </v-navigation-drawer>

      <v-dialog v-model="qrDialog" max-width="500px" class="dark-bg-custom">
        <v-card class="dark-bg-custom">
          <v-card-title>
            <span class="text-md">Organization QR Code(with geofencing)</span>
          </v-card-title>
          <v-card-text>
            <QRCode :qrValue="qrValue" :width="450" />
          </v-card-text>
        </v-card>
      </v-dialog>
    </div>
  </div>
</template>
<script>
import CustomSwitch from './CustomSwitch.vue'
import CustomSlider from './CustomSlider.vue'
import MapRadiusModal from '../MapRadiusModal.vue'
import QRCode from './qrCode.vue'
import AlertPopUp from '@/components/AlertPopUp'
import { isModuleFeatureAllowed } from '@/utils/common'
export default {
  data() {
    return {
      orgCode: this.$storage.getUniversal('organization_code'),
      userOrg: this.$storage.getUniversal('user'),
      selectedDays: [],
      shiftTypes: ['MORNING_SHIFT', 'DAY_SHIFT', 'NIGHT_SHIFT', 'WEEK_OFF', 'CUSTOM_SHIFT'],
      shiftType: '',
      days: ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'],
      shiftStatus: [
        { text: 'Active', value: 'Active' },
        { text: 'In-Active', value: 'In-Active' }
      ],
      modal2: false,
      modal3: false,
      workingHours: this.attendanceSettings?.workingHours,
      fullDayThreshold: this.attendanceSettings?.fullDayThreshold,
      time: null,
      time2: null,
      dialog: false,
      switchState: {},
      shifts: [],
      status: '',
      qrDialog: false,
      drawer: false,
      shiftName: '',
      shiftStartTime: null,
      shiftEndTime: null,
      editMode: false,
      showConfirmationDialog: false,
      radiusEnable: false,
      globalFence: this.attendanceSettings?.geoFencingEnabled || false,
      shiftDeleteIndex: null,
      editIndex: -1,
      isAutoCheckoutEnable: this.attendanceSettings?.autoAttendanceMark || false,
      lat: null,
      lng: null,
      radius: null,
      menu: false,
      distanceMenu: false,
      selectedDate: null,
      distanceDate: null,
      attendanceSettings: {},
      tabs: 0,
      tabItems: [
        {
          key: 'attendance',
          name: 'Attendance'
        },
        {
          key: 'geoFencing',
          name: 'Geo Fencing'
        }
      ]
    }
  },
  components: {
    CustomSwitch,
    CustomSlider,
    MapRadiusModal,
    AlertPopUp,
    QRCode
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    }
  },
  computed: {
    currentTab() {
      return this.tabItems[this.tabs]?.key
    },
    qrValue() {
      if (this.receivedData?.geoFencingEnabled) {
        const { location, radius } = this.receivedData.geoFencingData || {}
        if (location || radius) {
          return `${process.env.VUE_APP_BASE_URL}/mark-attendance?orgCode=${this.orgCode}&lat=${location.lat}&lng=${location.lng}&radius=${radius}`
        }
      }
      return `${process.env.VUE_APP_BASE_URL}/mark-attendance?orgCode=${this.orgCode}`
    },
    minDate() {
      // Get the first day of the previous month
      return this.$dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD')
    },
    maxDate() {
      // Get yesterday's date
      return this.$dayjs().subtract(1, 'day').format('YYYY-MM-DD')
    },
    branchFencingValue() {
      return this.receivedData?.geoFencingEnabled
    },
    allBranchFencing() {
      return this.branchFencingValue && this.receivedData?.enableBranchGeofencing
    }
  },
  watch: {
    receivedData: {
      async handler(newVal) {
        if (newVal !== null) {
          this.shifts = newVal?.orgShifts
          if (this.shifts) {
            this.getShifts()
          }
          this.receivedData = newVal
          // this.selectedDays = this.attendanceSettings?.workingDays || []
          // this.time = this.attendanceSettings?.shiftStartTime
          // this.time2 = this.attendanceSettings?.shiftEndTime
        }
      },
      deep: true,
      immediate: true
    },
    attendanceSettings: {
      handler(newVal) {
        if (newVal) {
          this.selectedDays = this.attendanceSettings?.workingDays || []
          this.time = this.attendanceSettings?.shiftStartTime
          this.time2 = this.attendanceSettings?.shiftEndTime
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getAttendanceSettings() {
      try {
        const { data } = await this.$axios.get('/workforce/orgSetup/attendance-settings')
        this.attendanceSettings = data.attendanceSettings || {}
        return !!data.attendanceSettings?._id
      } catch (error) {
        console.log(error)
      }
    },
    async saveAttendanceSettings() {
      try {
        const payload = {
          shiftStartTime: this.time,
          shiftEndTime: this.time2,
          allowSystemCalculation: this.switchState.allowSystemCalculation,
          fullDayThreshold: this.fullDayThreshold,
          halfDayThreshold: this.halfDayThreshold,
          overTimeThreshold: this.overTimeThreshold,
          autoCheckout: this.switchState.autoCheckout,
          autoAttendanceMark: this.switchState.autoAttendanceMark,
          strictCheckout: this.switchState.strictCheckout,
          workingDays: this.selectedDays,
          workingHours: this.workingHours,
          allowCheckInAllTime: this.switchState.allowCheckInAllTime,
          // clockInWithTimeLine
          // isCheckInAllowed
          clockInWithQrCode: this.switchState.clockInWithQrCode,
          clockOutWithQrCode: this.switchState.clockOutWithQrCode,
          isCheckedInSelfie: this.switchState.isCheckedInSelfie,
          isCheckedOutSelfie: this.switchState.isCheckedOutSelfie
          // geoFencingEnabled: this.switchState.geoFencingEnabled,
          // homeGeofenceEnabled: this.switchState.homeGeofenceEnabled,
          // homeGeofenceRadius: this.receivedData?.homeGeofenceRadius,
          // geoFencingData: {
          //   location: {
          //     lat: this.lat,
          //     lng: this.lng
          //   },
          //   radius: this.radius
          // },
          // orgShifts: this.shifts,
          // enableBranchGeofencing: this.switchState.enableBranchGeofencing,
          // allowCheckInFromAllDefinedLocation: this.switchState.allowCheckInFromAllDefinedLocation,
          // geoFencingEnabled: this.globalFence,
          // branchFence: this.branchFence,
          // allBranchCheckIn: this.allBranchCheckIn,
        }

        //later apply condition to check put or post api
        if (await this.getAttendanceSettings()) {
          this.$axios.put(`/workforce/orgSetup/attendance-settings`, payload)
        } else {
          this.$axios.post(`/workforce/orgSetup/attendance-settings`, payload)
        }
        this.$toast.success('Attendance settings saved successfully!')
      } catch (error) {
        console.log('Error saving attendance settings:', error)
      }
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('ATTENDANCE_MANAGEMENT', feature)
    },
    isGeoFeatureVisible(feature) {
      return isModuleFeatureAllowed('ATTENDANCE_MANAGEMENT', feature)
    },
    isCreateShiftVisible(feature) {
      return isModuleFeatureAllowed('ATTENDANCE', feature)
    },
    handleFencing(val) {
      this.branchFence = val.value
      this.$emit('branchFencing', val)
    },
    handleAllBranchCheckIn(val) {
      this.$emit('allBranchCheckIn', val)
    },
    handleOpen(val) {
      this.dialog = val
    },
    handleClose() {
      this.dialog = !this.dialog
    },
    onOpenQrPopup() {
      this.qrDialog = true
    },
    toggleDay(day) {
      const weekdays = [
        'SUNDAY',
        'MONDAY',
        'TUESDAY',
        'WEDNESDAY',
        'THURSDAY',
        'FRIDAY',
        'SATURDAY'
      ]
      if (this.selectedDays.includes(day)) {
        this.selectedDays = this.selectedDays.filter((selectedDay) => selectedDay !== day)
      } else {
        this.selectedDays.push(day)
      }
      const sortWeekdays = () => {
        return this.selectedDays.sort((a, b) => {
          return weekdays.indexOf(a) - weekdays.indexOf(b)
        })
      }
      this.selectedDays = sortWeekdays()

      this.$emit('attendanceDataFields', this.selectedDays)
    },
    handleSwitch(val) {
      switch (val?.id) {
        case 'attendance_13':
          this.switchState.clockOutWithQrCode = val.value
          break
        case 'attendance_12':
          this.switchState.clockInWithQrCode = val.value
          break
        case 'attendance_2':
          this.switchState.allowSystemCalculation = val.value
          break
        case 'attendance_5':
          this.switchState.strictCheckout = val.value
          break
        case 'attendance_6':
          this.switchState.isCheckedInSelfie = val.value
          break
        case 'attendance_7':
          this.switchState.isCheckedOutSelfie = val.value
          break
        case 'attendance_3':
          this.switchState.autoAttendanceMark = val.value
          this.isAutoCheckoutEnable = !this.isAutoCheckoutEnable
          break
        case 'attendance_4':
          this.switchState.autoCheckout = val.value
          break
        case 'attendance_8':
          this.globalFence = val.value
          this.switchState.geoFencingEnabled = val.value
          break
        case 'attendance_1':
          this.switchState.allowCheckInAllTime = val.value
          break
        case 'attendance_11':
          this.radiusEnable = val.value
          this.switchState.homeGeofenceEnabled = val.value
          break
        default:
          break
      }
      const data = {
        ...val,
        ...this.switchState,
        days: this.selectedDays,
        starttime: this.time,
        endtime: this.time2,
        workingHours: this.workingHours,
        fullDayThreshold: this.fullDayThreshold
      }
      this.$emit('attendanceData', data)
    },
    saveGeofence(val) {
      this.$emit('geoFenceData', val)
    },
    handleOkClick() {
      this.$refs.startDialog.save(this.time)
      this.$emit('attendanceDataTime', this.time)
    },
    handleOkClickTime2() {
      this.$refs.endDialog.save(this.time2)
      this.$emit('attendanceDataTime2', this.time2)
    },
    updateFullDayThreshold(val) {
      this.fullDayThreshold = val
      this.$emit('attendanceTimeThreshold', this.fullDayThreshold)
    },
    updateHalfDayThreshold(val) {
      this.halfDayThreshold = val
      this.$emit('attendanceTimeHalfThreshold', this.halfDayThreshold)
    },
    updateOverTimeThreshold(val) {
      this.overTimeThreshold = val
      this.$emit('attendanceTimeOverThreshold', this.overTimeThreshold)
    },
    updateWorkHour(val) {
      this.workingHours = val
      this.$emit('updateWorkHour', this.workingHours)
    },
    updateHomeFenceRadius(radius) {
      this.$emit('updateHomeFenceRadius', radius)
    },
    getShifts() {
      if (this.shifts) {
        const orgShiftTypes = this.shifts.map((shift) => shift.shiftType)
        this.shiftTypes = this.shiftTypes.filter((type) => !orgShiftTypes.includes(type))
      }
    },
    addNewShift() {
      this.getShifts()
      this.shiftName = ''
      this.shiftStartTime = null
      this.shiftEndTime = null
      this.status = 'Active'
      this.editMode = false
      this.drawer = true
    },
    editItem(shift, index) {
      this.getShifts()
      this.shiftTypes.push(shift.shiftType)
      this.shiftType = shift.shiftType
      this.shiftName = shift.shiftName
      this.shiftStartTime = shift.shiftStartTime
      this.shiftEndTime = shift.shiftEndTime
      this.status = shift.status
      this.editIndex = index
      this.editMode = true
      this.drawer = true
    },
    getDurationInHours(startTime, endTime) {
      const start = this.$dayjs(`1970-01-01T${startTime}:00`)
      const end = this.$dayjs(`1970-01-01T${endTime}:00`)

      let durationInMilliseconds = end.diff(start)

      if (durationInMilliseconds < 0) {
        durationInMilliseconds += 24 * 60 * 60 * 1000
      }

      const durationInHours = durationInMilliseconds / (1000 * 60 * 60)

      return durationInHours
    },
    saveShift() {
      if (!this.shiftType) {
        return this.$toast.error('Please select shift type.')
      }
      if (!this.shiftName) {
        return this.$toast.error('Please select shift name.')
      }
      if (!this.shiftStartTime) {
        return this.$toast.error('Please select shift start time.')
      }
      if (!this.shiftEndTime) {
        return this.$toast.error('Please select shift end time.')
      }

      const durationTime = this.getDurationInHours(this.shiftStartTime, this.shiftEndTime)
      let newShift = {
        shiftType: this.shiftType,
        shiftName: this.shiftName,
        shiftStartTime: this.shiftStartTime,
        shiftEndTime: this.shiftEndTime,
        duration: durationTime,
        status: this.status
      }
      if (this.editMode) {
        this.$set(this.shifts, this.editIndex, newShift)
      } else {
        this.shifts.push(newShift)
      }

      this.$emit('orgShifts', this.shifts)
      this.cancel()
    },

    confirmRemoveShift(shift, index) {
      this.showConfirmationDialog = true
      this.shiftToDelete = shift
      this.shiftDeleteIndex = index
      this.$emit('orgShifts', this.shifts)
    },

    shiftDelete() {
      if (this.shiftDeleteIndex !== -1) {
        this.shifts.splice(this.shiftDeleteIndex, 1)
      }
      this.cancel()
    },

    cancel() {
      this.shiftType = ''
      this.shiftName = ''
      this.shiftStartTime = null
      this.shiftEndTime = null
      this.status = ''
      this.editMode = false
      this.drawer = false
      this.showConfirmationDialog = false
    },
    isShiftDisabled(shift) {
      if (shift.status === 'In-Active') {
        return {
          textColor: '#ededed',
          bgColor: '#818181'
        }
      }
      return {}
    },
    async manualAttendance() {
      try {
        const payload = {
          date: this.$dayjs(this.selectedDate).format('YYYY-MM-DD'),
          organization_id: this.userOrg?.organization_id
        }
        const response = await this.$axios.post('/workforce/manualcron/attendance/v3', payload)
        const successMessage = response?.data?.message || 'Attendance updated successfully'
        this.$toast.success(successMessage)
        this.menu = false
      } catch (error) {
        console.log(error)
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
      }
    },
    async manualDistance() {
      try {
        const payload = {
          date: this.$dayjs(this.distanceDate).format('YYYY-MM-DD'),
          orgId: this.userOrg?.organization_id
        }
        const response = await this.$axios.post('/workforce/distance/calculate-distance', payload)
        const successMessage = response?.data?.message || 'Distance updated successfully'
        this.$toast.success(successMessage)
        this.distanceMenu = false
      } catch (error) {
        console.log(error)
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
      }
    }
  },
  async mounted() {
    await this.getAttendanceSettings()
    this.selectedDays = this.attendanceSettings?.workingDays || []
    this.time = this.attendanceSettings?.shiftStartTime || ''
    this.switchState = {
      strictCheckout: this.attendanceSettings?.strictCheckout,
      isCheckedInSelfie: this.attendanceSettings?.isCheckedInSelfie,
      isCheckedOutSelfie: this.attendanceSettings?.isCheckedOutSelfie,
      autoCheckout: this.attendanceSettings?.autoCheckout,
      geoFencingEnabled: this.attendanceSettings?.geoFencingEnabled,
      allowSystemCalculation: this.attendanceSettings?.allowSystemCalculation,
      allowCheckInAllTime: this.attendanceSettings?.allowCheckInAllTime
    }
    this.shifts = this.$props.receivedData?.orgShifts
    if (this.shifts) {
      this.getShifts()
    }
  }
}
</script>
<style scoped>
.wfm-emp-card {
  border: none !important;
  box-shadow: none !important;
  margin-top: 0 !important;
}

.shift-drawer {
  border: 1px solid rgb(199 199 199) !important;
}
</style>
