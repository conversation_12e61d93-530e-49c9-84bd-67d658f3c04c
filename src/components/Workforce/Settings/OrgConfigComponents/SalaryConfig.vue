<template>
  <div class="p-5 overflow-auto h-[70vh]">
    <div v-if="orgSalaries.length">
      <div
        class="flex justify-end"
        v-show="isFeatureVisible('CREATE_SALARY_TEMPLATES')"
      >
        <v-btn @click="openDrawer" :color="$vuetify.theme.currentTheme.primary"
          class="white--text"
          >Create Salary Template</v-btn
        >
      </div>
      <v-list class="pt-0 pb-0">
        <v-list-item-group>
          <v-list-item
            v-for="(sal, index) in orgSalaries"
            :key="index"
            class="space-x-4 border border-blue-200 mt-4 pt-0 pb-0"
          >
            <v-label class=""> {{ index + 1 }}. </v-label>
            <v-col cols="1" md="1">
              <p class="mb-0">
                {{ titleCase(sal.template) }}
              </p>
              <v-tooltip top>
                <template v-slot:activator="{ on }">
                  <p v-on="on" class="truncate mb-0">
                    {{ sal.customTemlateName }}
                  </p>
                </template>
                <p v-show="sal.customTemlateName" class="truncate">
                  {{
                    sal.customTemlateName && titleCase(sal.customTemlateName)
                  }}
                </p>
              </v-tooltip>
            </v-col>
            <div
              class="flex items-center"
              v-show="isFeatureVisible('CONTROL_SALARY_TEMPLATE_STATUS')"
            >
              <v-label>{{ !sal.isActive ? "Active" : "In-Active" }}</v-label>
              <v-switch
                :ripple="false"
                class="ml-4"
                inset
                dense
                v-model="sal.isActive"
                @change="(e) => editStatus(e, sal)"
              ></v-switch>
              <v-label>{{ sal.isActive ? "Active" : "In-Active" }}</v-label>
            </div>
            <v-col
              cols="5"
              md="5"
              class="flex items-center"
              v-show="isFeatureVisible('GENERATE_SALARY_SLIPS')"
            >
              <v-autocomplete
                v-model="selectedMonth[sal._id]"
                :items="months"
                item-text="name"
                item-value="month"
                label="Select Month"
                hide-details
                dense
                single
                outlined
                :disabled="!sal.isActive"
              ></v-autocomplete>
              <v-btn
                :color="$vuetify.theme.currentTheme.primary"
                :disabled="!(selectedMonth[sal._id] + 1) || !sal.isActive"
                @click="handleGenerate(sal)"
                class="ml-4 white--text"
                >Generate</v-btn
              >
            </v-col>
            <div class="flex justify-end">
              <v-icon color="red" class="ml-2" @click="deleteSal(sal._id)"
                >mdi-delete</v-icon
              >
              <v-icon
                color="green"
                class="ml-1"
                @click="editTemplate(sal)"
                :disabled="!sal.isActive"
                >mdi-pencil</v-icon
              >
            </div>
          </v-list-item>
        </v-list-item-group>
      </v-list>
    </div>
    <no-data
      v-else
      title="Nothing to Display"
      :subTitle="`No active template enabled for the organization`"
      :btnText="`Set Salary Template`"
      :btnAction="openDrawer"
    />

    <div>
      <v-navigation-drawer
        :color="$vuetify.theme.currentTheme.navigationColor"
        class="teams-drawer"
        width="50%"
        v-model="drawer"
        absolute
        right
      >
        <div class="overflow-auto h-full">
          <v-card-title class="text-h6 justify-start">
            Add/Edit Salary Details
            <div class="flex justify-end mr-4 mt-4">
              <v-btn @click="closeDrawer" class="mr-2" color="black" text
                >Cancel</v-btn
              >
              <v-btn @click="saveDetails" color="primary" outlined>Save</v-btn>
            </div>
          </v-card-title>
          <div class="py-4 px-5">
            <h3 class="text-lg font-bold mb-4">Template Type</h3>
            <v-row class="border border-blue-200">
              <v-col cols="12" md="12" class="flex items-center">
                <v-select
                  class="ml-auto"
                  v-model="selectedTemplate"
                  :items="availableTemplates"
                  item-text="text"
                  item-value="value"
                  outlined
                  dense
                  hide-details
                >
                </v-select>
              </v-col>
              <v-col
                cols="12"
                md="12"
                class="flex items-center"
                v-if="selectedTemplate === 'other'"
              >
                <v-text-field
                  class="ml-auto"
                  label="Template Name"
                  v-if="selectedTemplate === 'other'"
                  outlined
                  dense
                  hide-details
                  v-model="customTemlateName"
                ></v-text-field>
              </v-col>
            </v-row>
            <h3 class="text-lg font-bold mb-4 mt-6">Earnings</h3>
            <v-row class="border border-blue-200">
              <v-col cols="12" md="6" class="flex items-center">
                <v-text-field
                  v-model="basic"
                  type="number"
                  suffix="% of CTC"
                  label="Basics"
                  outlined
                  dense
                  hide-details
                  required
                />
              </v-col>
              <v-col cols="12" md="6" class="flex items-center">
                <v-text-field
                  v-model="hra"
                  type="number"
                  label="HRA"
                  suffix="% of Basic"
                  outlined
                  dense
                  hide-details
                />
              </v-col>
              <v-col cols="12" md="6" class="flex items-center">
                <v-text-field
                  v-model="medicalAllowance"
                  type="number"
                  label="Medical Allowance"
                  suffix="Annual"
                  outlined
                  dense
                  hide-details
                  class="textFieldLableWidth"
                />
              </v-col>
              <v-col cols="12" md="6" class="flex items-center">
                <v-text-field
                  v-model="ltaAllowance"
                  label="Leave/Travel Allowance"
                  type="number"
                  suffix="% of Basic"
                  outlined
                  dense
                  hide-details
                  class="textFieldLableWidth"
                />
              </v-col>
              <v-col cols="12" md="6" class="flex items-center">
                <v-text-field
                  v-model="conveyanceAllowance"
                  label="Leave/Travel Allowance"
                  type="number"
                  suffix="yearly"
                  outlined
                  dense
                  hide-details
                  class="textFieldLableWidth"
                />
              </v-col>
              <v-col cols="12" md="6" class="flex items-center">
                <v-text-field
                  v-model="specialAllowance"
                  label="Special Allowance"
                  type="number"
                  suffix="Yearly"
                  outlined
                  dense
                  hide-details
                />
              </v-col>
            </v-row>
          </div>

          <div class="py-4 px-5">
            <h3 class="text-lg font-bold mb-4">Deductions</h3>
            <v-row class="border border-blue-200">
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="professionalTax"
                  type="number"
                  label="Professional Tax"
                  suffix="Yearly"
                  outlined
                  dense
                  hide-details
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="otherTax"
                  type="number"
                  label="Other Tax"
                  suffix="% of Gross"
                  outlined
                  dense
                  hide-details
                />
              </v-col>
            </v-row>
          </div>

          <div class="py-4 px-5">
            <h3 class="text-lg font-bold mb-4">Contributions</h3>
            <v-row class="border border-blue-200">
              <v-col class="flex items-center">
                <v-text-field
                  v-model="epf"
                  type="number"
                  label="EPF"
                  suffix="% of Basic"
                  outlined
                  dense
                  hide-details
                />
              </v-col>
            </v-row>
          </div>

          <div class="py-4 px-5">
            <h3 class="text-lg font-bold mb-4">Salary Calculator</h3>
            <v-row class="border border-blue-200">
              <v-col>
                <v-text-field
                  v-model="ctc"
                  type="number"
                  label="Employee Yearly Salary"
                  suffix="CTC"
                  outlined
                  dense
                  hide-details
                />
              </v-col>
              <v-col>
                <v-text-field
                  label="Net Salary"
                  v-model="netSalary"
                  outlined
                  dense
                  hide-details
                  readonly
                >
                </v-text-field>
              </v-col>
            </v-row>
          </div>
        </div>
      </v-navigation-drawer>
    </div>
    <AlertPopUp
      :showConfirmationDialog="showConfirmationDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="'Are you sure you want to delete the template?'"
      :confirmationActionText="'Delete'"
      :confirmationActionColor="'red'"
      :performAction="deleteTemplate"
      :cancel="cancel"
    />
  </div>
</template>

<script>
import NoData from "@/components/NoData";
import { convertToTitleCase } from "@/utils/common";
import AlertPopUp from "@/components/AlertPopUp";
import { isModuleFeatureAllowed } from "@/utils/common";
export default {
  data() {
    return {
      basic: null,
      hra: null,
      medicalAllowance: null,
      foodAllowance: null,
      professionalTax: null,
      otherTax: null,
      epf: null,
      drawer: false,
      ctc: null,
      ltaAllowance: null,
      conveyanceAllowance: null,
      specialAllowance: null,
      templates: [
        { text: "Yearly", value: "yearly" },
        { text: "Monthly", value: "monthly" },
        { text: "Bi-weekly", value: "bi-weekly" },
        { text: "Weekly", value: "weekly" },
        { text: "Daily", value: "daily" },
        { text: "Other", value: "other" },
      ],
      selectedTemplate: "",
      currentTemplateId: null,
      showConfirmationDialog: false,
      orgSalaries: [],
      status: false,
      selectedMonth: {},
      months: [],
      availableTemplates: [],
      customTemlateName: "",
    };
  },
  components: {
    NoData,
    AlertPopUp,
  },
  computed: {
    mandatoryRule: (value) => !!value || "This field is required",
    positiveNumberRule: (value) => value >= 0 || "Value must be positive",
    netSalary() {
      const basicSalaryPercent = parseFloat(this.basic) || 0;
      const hraPercent = parseFloat(this.hra) || 0;
      const ltaPercent = parseFloat(this.ltaAllowance) || 0;
      const medicalAllowance = parseFloat(this.medicalAllowance) || 0;
      const conveyanceAllowance = parseFloat(this.conveyanceAllowance) || 0;
      // const specialAllowance = parseFloat(this.specialAllowance) || 0;
      const epfPercent = parseFloat(this.epf) || 0;
      const professionalTax = parseFloat(this.professionalTax) || 0;
      const otherTaxPercent = parseFloat(this.otherTax) || 0;
      const ctc = parseFloat(this.ctc) || 0;

      const basicSalary = (ctc * basicSalaryPercent) / 100;
      const hraAmount = (basicSalary * hraPercent) / 100;
      const ltaAmount = (basicSalary * ltaPercent) / 100;

      const specialAllowance =
      ctc -
      (basicSalary +
        hraAmount +
        conveyanceAllowance +
        ltaAmount +
        medicalAllowance);

      const grossSalary =
        basicSalary +
        hraAmount +
        conveyanceAllowance +
        ltaAmount +
        medicalAllowance +
        specialAllowance;

      const employeePF = (basicSalary * epfPercent) / 100;
      const otherTaxAmount = (grossSalary * otherTaxPercent) / 100;
      const totalDeduction = employeePF + professionalTax + otherTaxAmount;
      const netSalary = grossSalary - totalDeduction;

      return netSalary.toFixed(2);
    },
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed("SALARY_MANAGEMENT", feature);
    },
    generateMonths() {
      const currentYear = this.$dayjs().year();
      const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ];

      this.months = monthNames.map((name, index) => {
        return {
          month: index,
          name: `${name} ${currentYear}`,
        };
      });
    },
    async handleGenerate(sal) {
      const selectedMonthData = this.selectedMonth[sal._id];

      const payload = {
        month: selectedMonthData,
        year: this.$dayjs().year(),
      };
      try {
        const response = await this.$axios.post(
          `/workforce/org/generate-salary-slips`,
          payload,
        );
        const successMessage = response?.data?.message || "Salary slips genereated successfully"
        this.$toast.success(successMessage);
      } catch (error) {
        console.error("Error generate salary slips:", error);
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage);
      }
    },
    openDrawer() {
      this.availableTemplates = this.templates.filter(
        (template) =>
          !this.orgSalaries.some((sal) => sal.template === template.value)
      );

      if (this.availableTemplates.length > 0) {
        this.selectedTemplate = this.availableTemplates[0].value;
      }
      this.drawer = !this.drawer;
    },
    closeDrawer() {
      this.basic = null;
      this.hra = null;
      this.medicalAllowance = null;
      this.foodAllowance = null;
      this.professionalTax = null;
      this.otherTax = null;
      this.epf = null;
      this.drawer = false;
      this.ctc = null;
      this.ltaAllowance = null;
      this.conveyanceAllowance = null;
      this.specialAllowance = null;
      this.customTemlateName = "";
      this.drawer = false;
    },
    titleCase(str) {
      return convertToTitleCase(str);
    },
    async editStatus(status, templateDetails) {
      const payload = {
        isActive: status,
      };
      const response = await this.$axios.put(
        `/workforce/org/salary-template/${templateDetails._id}`,
        payload,
      );
      const successMessage = response?.data?.message || "Salary template updated successfully"
      this.$toast.success(successMessage);
    },
    async saveDetails(templateDetails) {
      if (!this.selectedTemplate && !templateDetails.template) {
        this.$toast.error("Select a template type");
        return;
      }

      const requiredFields = [
        { field: this.basic, name: "Basic Salary" },
        { field: this.hra, name: "HRA" },
        { field: this.medicalAllowance, name: "Medical Allowance" },
        { field: this.selectedTemplate, name: "Template Type" },
      ];

      for (let { field, name } of requiredFields) {
        if (!field) {
          this.$toast.error(`${name} is required`);
          return;
        }
      }

      const nonNegativeFields = [
        { field: this.basic, name: "Basic Salary" },
        { field: this.hra, name: "HRA" },
        { field: this.medicalAllowance, name: "Medical Allowance" },
        { field: this.ltaAllowance, name: "LTA Allowance" },
        { field: this.conveyanceAllowance, name: "Conveyance Allowance" },
        { field: this.specialAllowance, name: "Special Allowance" },
        { field: this.professionalTax, name: "Professional Tax" },
        { field: this.otherTax, name: "Other Tax" },
        { field: this.epf, name: "EPF" },
        { field: this.ctc, name: "CTC" },
      ];

      // Validate non-negative fields
      for (let { field, name } of nonNegativeFields) {
        if (field < 0) {
          this.$toast.error(`${name} must be non-negative`);
          return;
        }
      }

      const payload = {
        template: this.selectedTemplate,
        customTemlateName:
          this.selectedTemplate === "other"
            ? this.customTemlateName
            : undefined,
        basicSalaryPercent: Number(this.basic),
        hraPercent: Number(this.hra),
        ltaPercent: Number(this.ltaAllowance),
        medicalAllowance: Number(this.medicalAllowance),
        conveyanceAllowance: Number(this.conveyanceAllowance),
        specialAllowance: Number(this.specialAllowance),
        employeeContribution: Number(this.professionalTax),
        standardPF: Number(this.epf),
        incomeTax: 0,
        taxSavingComponents: [],
        isActive: true,
      };

      try {
        if (this.currentTemplateId) {
          const response = await this.$axios.put(
            `/workforce/org/salary-template/${this.currentTemplateId}`,
            payload,
          );
          const successMessage = response?.data?.message || "Salary template updated successfully"
          this.$toast.success(successMessage);
        } else {
          const response = await this.$axios.post("/workforce/org/salary-template", payload);
          this.$toast.success(response.data.message);
        }
        (this.basic = null),
          (this.hra = null),
          (this.medicalAllowance = null),
          (this.foodAllowance = null),
          (this.professionalTax = null),
          (this.otherTax = null),
          (this.epf = null),
          (this.drawer = false),
          (this.ctc = null),
          (this.ltaAllowance = null),
          (this.conveyanceAllowance = null),
          (this.specialAllowance = null),
          await this.getOrgSalary();
      } catch (error) {
        console.error("Error saving salary details:", error);
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage);
      } finally {
        this.drawer = false;
        this.currentTemplateId = null;
      }
    },

    editTemplate(sal) {
      this.currentTemplateId = sal._id;
      this.selectedTemplate = sal.template;
      this.customTemlateName =
        sal.template === "other" ? sal.customTemlateName : "";
      this.basic = sal.basicSalaryPercent;
      this.hra = sal.hraPercent;
      this.ltaAllowance = sal.ltaPercent;
      this.medicalAllowance = sal.medicalAllowance;
      this.conveyanceAllowance = sal.conveyanceAllowance;
      this.specialAllowance = sal.specialAllowance;
      this.epf = sal.standardPF;
      this.professionalTax = sal.employeeContribution;
      this.otherTax = sal.otherTax;
      const newVal = this.ctc
      this.ctc = newVal;
      const editTemplateData = this.templates.filter(
        (temp) => temp.value === this.selectedTemplate
      );
      if (
        editTemplateData.length > 0 &&
        !this.availableTemplates.some(
          (temp) => temp.value === this.selectedTemplate
        )
      ) {
        this.availableTemplates.push(editTemplateData[0]);
      }
      this.drawer = true;
    },
    deleteSal(salId) {
      this.currentTemplateId = salId;
      this.showConfirmationDialog = true;
    },
    async deleteTemplate() {
      try {
        const response = await this.$axios.delete(
          `/workforce/org/salary-template/${this.currentTemplateId}`
        );
        const successMessage = response?.data?.message || "Salary template deleted successfully"
        this.$toast.success(successMessage);
        await this.getOrgSalary();
        this.cancel();
      } catch (error) {
        console.error("Error deleting salary template:", error);
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage);
      }
    },
    async getOrgSalary() {
      try {
        const response = await this.$axios.get("/workforce/org/salary-templates");
        this.orgSalaries = response.data.salaryTemplates;
        if (this.orgSalaries?.length > 0) {
          this.availableTemplates = this.templates.filter(
            (template) =>
              !this.orgSalaries.some((sal) => sal.template === template.value)
          );
        }
      } catch (error) {
        console.log(error);
      }
    },
    cancel() {
      this.showConfirmationDialog = false;
    },
  },
  async mounted() {
    await this.getOrgSalary();
    this.generateMonths();
  },
};
</script>

<style>
.textFieldLable {
  width: 250px;
}
.textFieldLable .v-input__control .v-input__slot .v-label,
.textFieldLable .v-input__control .v-input__slot .v-input__append {
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
