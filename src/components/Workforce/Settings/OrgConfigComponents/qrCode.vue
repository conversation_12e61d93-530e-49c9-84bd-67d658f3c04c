<template>
  <div>
    <vue-qrcode
      :value="qrValue"
      :width="width"
      :background="background"
      :foreground="foreground"
      :color="qrColors"
      @change="onDataUrlChange"
      type="image/png"
    />
    <img v-if="dataUrl" :src="dataUrl" alt="QR Code" />
    <div class="mt-3">
      <v-btn outlined @click="printQRCode" class="mr-2">Print</v-btn>
      <v-btn outlined @click="downloadQRCode">Download</v-btn>
    </div>
  </div>
</template>

<script>
import VueQrcode from "vue-qrcode";

export default {
  components: {
    VueQrcode,
  },
  props: {
    qrValue: {
      type: String,
      required: true,
    },
    width: {
      type: Number,
      default: 200,
    },
    background: {
      type: String,
      default: "#ffffff",
    },
    foreground: {
      type: String,
      default: "#000000",
    },
    qrColors: {
      type: Object,
      default: () => ({
        dark: '#000000',
        light: '#FFFFFF'
      }),
    },
  },
  data() {
    return {
      dataUrl: null,
    };
  },
  methods: {
    onDataUrlChange(dataUrl) {
      this.dataUrl = dataUrl;
    },
    downloadQRCode() {
      if (this.dataUrl) {
        const link = document.createElement("a");
        link.href = this.dataUrl;
        link.download = "qr-code.png";
        link.click();
      }
    },
    printQRCode() {
      if (this.dataUrl) {
        const printWindow = window.open("", "_blank");
        printWindow.document.write(
          "<html><head><title>Print QR Code</title></head><body>"
        );
        printWindow.document.write(
          '<img src="' + this.dataUrl + '" style="width:50%;" />'
        );
        printWindow.document.write("</body></html>");
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
      }
    },
  },
};
</script>

<style scoped></style>
