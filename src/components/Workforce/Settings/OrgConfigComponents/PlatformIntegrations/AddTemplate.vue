<template>
  <div class="template-container">
    <v-row no-gutters>
      <!-- Main Content -->
      <v-col>
        <v-card flat>
          <!-- Header -->
          <v-card-title
            class="py-4 px-4 d-flex justify-space-between border-b border-gray-200 top-header"
          >
            <span class="text-lg font-semibold">Create New Template</span>
            <div class="d-flex">
              <v-btn color="primary" @click="submitTemplate" small :loading="submitTemplateLoading">
                <v-icon left>mdi-send</v-icon>
                Submit for Approval
              </v-btn>
              <v-btn class="ml-2" outlined color="primary" @click="closeDialog" small>
                Close
              </v-btn>
            </div>
          </v-card-title>

          <!-- Main Editor Area -->
          <v-card-text class="overflow-y-auto">
            <v-sheet max-width="4xl" class="mx-auto pa-6 rounded-lg">
              <!-- Template Info -->
              <div class="">
                <h3 class="text-md font-medium mb-4">Template Information</h3>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="templateName"
                      label="Template Name*"
                      hint="Only lowercase letters, numbers and underscores are allowed. Spaces will be replaced with underscores."
                      persistent-hint
                      dense
                      outlined
                      placeholder="e.g. welcome_message"
                      :error-messages="errors.templateName"
                      @input="formatTemplateName"
                      :disabled="!!editingTemplate"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-select
                      v-model="category"
                      :items="categories"
                      label="Category"
                      dense
                      outlined
                    ></v-select>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="6">
                    <!-- <v-select
                      v-model="language"
                      :items="languages"
                      label="Language*"
                      dense
                      outlined
                      :disabled="!!editingTemplate"
                    ></v-select> -->
                    <v-select
                      :items="allTriggers"
                      v-model="selectedEventType"
                      label="Select Event"
                      item-text="label"
                      item-value="value"
                      dense
                      outlined
                      class="w-full"
                      :clearable="true"
                    ></v-select>
                  </v-col>
                  <v-col>
                    <v-select
                      v-model="typesOfNotification"
                      :items="typeOfNotificationOptions"
                      label="Type of Notification*"
                      dense
                      outlined
                      multiple
                    ></v-select>
                  </v-col>
                </v-row>
              </div>

              <!-- Template Content -->
              <div class="">
                <div
                  class="d-flex justify-space-between align-center mb-4"
                  v-if="availableParamsList.length"
                >
                  <h3 class="text-md font-medium">
                    Select Parameters (Associated with selected event)
                  </h3>
                  <v-menu offset-y>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn text color="primary" v-bind="attrs" v-on="on" class="text-sm">
                        <v-icon left small>mdi-plus-circle</v-icon>
                        Paramters
                        <v-icon right x-small>mdi-chevron-down</v-icon>
                      </v-btn>
                    </template>
                    <v-list dense>
                      <v-list-item
                        v-for="variable in availableParamsList"
                        :key="variable.value"
                        @click="insertVariable(variable.value)"
                      >
                        <v-list-item-title class="text-sm">{{ variable.label }}</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>

                <!-- Header Section -->
                <!-- <div class="">
                  <div class="d-flex align-center mb-2">
                    <span class="text-sm font-medium text-gray-700 mr-2">Header</span>
                    <span class="text-xs text-gray-500">(Optional, max 60 chars)</span>
                  </div>
                  <div class="d-flex space-x-2 mb-2">
                    <v-btn
                      small
                      :color="headerFormat === 'text' ? 'primary' : ''"
                      :outlined="headerFormat !== 'text'"
                      class="text-none rounded-lg px-3"
                      @click="headerFormat = 'text'"
                    >
                      Text
                    </v-btn>
                    <v-btn
                      small
                      :color="headerFormat === 'media' ? 'primary' : ''"
                      :outlined="headerFormat !== 'media'"
                      class="text-none rounded-lg px-3 ml-2"
                      @click="headerFormat = 'media'"
                    >
                      Media
                    </v-btn>
                  </div>
                  <div v-if="headerFormat === 'text'">
                    <v-textarea
                      v-model="headerText"
                      dense
                      outlined
                      auto-grow
                      rows="1"
                      placeholder="Enter header text (max 60 characters)"
                      :counter="60"
                      @input="updatePreview"
                    ></v-textarea>
                  </div>
                  <div
                    v-else
                    class="border-2 border-dashed border-gray-300 rounded-lg pa-4 text-center"
                  >
                    <v-sheet>
                      <v-icon x-large class="text-gray-400 mb-2">mdi-cloud-upload</v-icon>
                      <p class="text-sm text-gray-500">
                        Upload image (JPEG, PNG) or document (PDF)
                      </p>
                      <v-file-input
                        v-model="headerMedia"
                        accept="image/jpeg,image/png,application/pdf"
                        class="d-none"
                        @change="updatePreview"
                        :error-messages="errors.headerMedia"
                      ></v-file-input>
                      <v-btn
                        color="success"
                        class="mt-2"
                        @click="$refs.fileInput ? $refs.fileInput.click() : null"
                      >
                        Select File
                      </v-btn>
                    </v-sheet>
                  </div>
                </div> -->

                <!-- Body Section -->
                <div>
                  <div class="d-flex align-center mb-2">
                    <span class="text-sm font-medium text-gray-700 mr-2">Message Body*</span>
                    <span class="text-xs text-gray-500">(Required, max 1024 chars)</span>
                  </div>
                  <v-textarea
                    v-model="messageBody"
                    outlined
                    auto-grow
                    rows="5"
                    placeholder="Enter your message here (max 1024 characters)"
                    :counter="1024"
                    :error-messages="errors.messageBody"
                    @input="validateBody"
                  ></v-textarea>
                </div>

                <!-- Buttons Section -->
                <div>
                  <div class="d-flex align-center justify-between">
                    <div>
                      <span class="text-sm font-medium text-gray-700 mr-2">Buttons</span>
                      <span class="text-xs text-gray-500">(Max 3 buttons, 25 chars)</span>
                    </div>
                    <div class="">
                      <v-btn
                        text
                        color="success"
                        class="text-sm px-0"
                        @click="addButton"
                        :disabled="buttons.length >= 3"
                      >
                        <v-icon left small>mdi-plus-circle</v-icon>
                        Add Button
                      </v-btn>
                      <div v-if="errors.buttons" class="error--text text-xs mt-1">
                        {{ errors.buttons }}
                      </div>
                    </div>
                  </div>
                  <p class="text-xs text-blue-600 mb-4">
                    Note: These buttons are only available for WhatsApp templates and triggers
                  </p>
                  <div v-for="(button, index) in buttons" :key="index" class="mb-4">
                    <v-sheet class="border border-gray-200 rounded-lg pa-4">
                      <div class="d-flex justify-space-between align-center mb-2">
                        <span class="text-sm font-medium">Button {{ index + 1 }}</span>
                        <v-btn icon x-small color="error" @click="removeButton(index)">
                          <v-icon>mdi-delete</v-icon>
                        </v-btn>
                      </div>
                      <v-select
                        v-model="button.type"
                        :items="buttonTypes"
                        label="Button Type*"
                        dense
                        outlined
                        class="mb-3"
                        @change="updatePreview"
                      ></v-select>
                      <v-text-field
                        v-model="button.text"
                        label="Button Text*"
                        dense
                        outlined
                        placeholder="Button text (max 25 chars)"
                        :counter="25"
                        :error-messages="button.errors.text"
                        @input="validateButtonText(index)"
                        class="mb-3"
                      >
                      </v-text-field>

                      <div v-if="button.type === 'url'">
                        <v-select
                          v-model="button.urlType"
                          :items="urlTypes"
                          label="URL Type*"
                          dense
                          outlined
                          class="mb-3"
                        ></v-select>
                        <v-text-field
                          v-if="button.urlType === 'static'"
                          v-model="button.url"
                          label="URL*"
                          dense
                          outlined
                          placeholder="https://example.com"
                          :error-messages="button.errors.url"
                          @input="validateButtonUrl(index)"
                          class="mb-3"
                          hint="Enter the static URL"
                          persistent-hint
                        ></v-text-field>
                        <v-select
                          v-else
                          v-model="button.dynamicType"
                          :items="dyanamicUrlTypes"
                          label="Dynamic URL Type*"
                          dense
                          outlined
                          class="mb-3"
                        ></v-select>
                      </div>

                      <div v-if="button.type === 'call'">
                        <v-text-field
                          v-model="button.phone"
                          label="Phone Number*"
                          dense
                          outlined
                          placeholder="+1234567890"
                          :error-messages="button.errors.phone"
                          @input="validateButtonPhone(index)"
                        ></v-text-field>
                      </div>
                    </v-sheet>
                  </div>
                </div>
              </div>

              <!-- Template Preview -->
              <div class="">
                <div class="max-w-md mx-auto">
                  <v-card class="rounded-lg overflow-hidden shadow-md">
                    <div class="p-4 text-md font-bold text-gray-800">Whatsapp Preview</div>

                    <!-- WhatsApp Chat Background -->
                    <div class="whatsapp-bg p-4">
                      <div class="flex flex-col space-y-1">
                        <!-- Message Bubble -->
                        <div class="rounded-lg bg-white p-3 relative">
                          <div
                            v-if="previewHeader"
                            class="font-weight-bold mb-2"
                            v-html="previewHeader"
                          ></div>
                          <div
                            class="text-gray-800"
                            v-html="previewBody || '[Message body will appear here]'"
                          ></div>
                        </div>

                        <!-- Buttons -->
                        <template v-if="previewButtons.length > 0">
                          <div
                            v-for="(button, index) in previewButtons"
                            :key="index"
                            class="rounded-lg bg-white p-3 flex justify-center"
                          >
                            <button
                              class="flex items-center font-semibold justify-center"
                              :class="[
                                button.type === 'url' ? 'text-blue-400' : '',
                                button.type === 'call' ? 'text-green-600' : '',
                                button.type === 'quick_reply' ? 'text-gray-700' : ''
                              ]"
                            >
                              <v-icon v-if="button.type === 'url'" class="mr-2 text-blue-400"
                                >mdi-open-in-new</v-icon
                              >
                              <v-icon v-if="button.type === 'call'" class="mr-2 text-green-600"
                                >mdi-phone</v-icon
                              >
                              <v-icon
                                v-if="button.type === 'quick_reply'"
                                class="mr-2 text-gray-700"
                                >mdi-reply</v-icon
                              >
                              {{ button.text || 'Button Text' }}
                            </button>
                          </div>
                        </template>

                        <!-- Default button if no buttons added -->
                        <div
                          v-if="previewButtons.length === 0"
                          class="rounded-lg bg-white p-3 flex justify-center"
                        >
                          <button
                            class="text-blue-400 flex items-center font-semibold justify-center"
                          >
                            <v-icon class="mr-2 text-blue-400">mdi-open-in-new</v-icon>
                            Click For Info
                          </button>
                        </div>
                      </div>
                    </div>
                  </v-card>
                </div>
              </div>
            </v-sheet>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import { APP_URL } from '@/utils/workforce/constants'
export default {
  name: 'WhatsAppTemplateCreator',
  props: {
    templateTriggersList: {
      type: Object,
      default: () => ({})
    },
    allTriggers: {
      type: Array,
      default: () => []
    },
    editingTemplate: {
      type: Object,
      default: null
    }
  },
  watch: {
    editingTemplate: {
      handler(template) {
        if (template) {
          console.log(template)
          this.templateName = template.templateName || ''
          this.category = template.category || ''
          this.language = template.whatsappLangCode || 'en'
          this.selectedEventType = template.eventType || null

          // Process message body and replace numbered params
          let messageBody = template.templateContent || ''
          if (template.params && template.params.length) {
            template.params.forEach((param, index) => {
              messageBody = messageBody.replace(`{{${index + 1}}}`, `{{${param}}}`)
            })
          }
          this.messageBody = messageBody

          this.headerText = template.templateSubject || ''
          this.typesOfNotification = template.typesOfNotification || []

          // Parse buttons if they exist
          if (template.buttons && template.buttons.length) {
            this.buttons = template.buttons.map((btn) => {
              const buttonData = {
                type: this.mapButtonType(btn.type),
                text: btn.text || '',
                errors: { text: '', url: '', phone: '' }
              }

              if (buttonData.type === 'url') {
                buttonData.url = btn.url || ''
                buttonData.urlType = btn.dynamicType ? 'dynamic' : 'static'
                buttonData.dynamicType = btn.dynamicType || ''
              } else if (buttonData.type === 'call') {
                buttonData.phone = btn.phone || ''
              }

              return buttonData
            })
          } else {
            this.buttons = []
          }

          // Update preview
          this.updatePreview()
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      orgData: this.$storage.getUniversal('orgData'),
      selectedEventType: null,
      templateName: '',
      category: 'UTILITY',
      language: 'en',
      submitTemplateLoading: false,

      // Template Content
      headerFormat: 'text',
      headerText: '',
      headerMedia: null,
      messageBody: '',
      buttons: [],

      // Preview data
      previewHeader: '',
      previewBody: '',
      previewButtons: [],

      // Form errors
      errors: {
        templateName: '',
        category: '',
        messageBody: '',
        headerMedia: '',
        buttons: ''
      },
      categories: [
        { text: 'Select a category', value: '' },
        { text: 'Utility', value: 'UTILITY' },
        { text: 'Marketing', value: 'MARKETING' },
        { text: 'Authentication', value: 'AUTHENTICATION' },
        { text: 'Task', value: 'task' }
      ],
      languages: [
        { text: 'English', value: 'en' },
        { text: 'Spanish', value: 'es' },
        { text: 'French', value: 'fr' },
        { text: 'German', value: 'de' },
        { text: 'Portuguese', value: 'pt' }
      ],
      buttonTypes: [
        { text: 'URL', value: 'url' },
        { text: 'Call', value: 'call' },
        { text: 'Quick Reply', value: 'quick_reply' }
      ],
      urlTypes: [
        { text: 'Static URL', value: 'static' },
        { text: 'Dynamic URL', value: 'dynamic' }
      ],
      dyanamicUrlTypes: [
        { text: 'Customer Feedback URL', value: 'customer_feedback_url' },
        { text: 'Customer Detail URL', value: 'customer_details_url' }
      ],
      typeOfNotificationOptions: [
        { text: 'Whatsapp', value: 'WHATSAPP' },
        { text: 'Email', value: 'EMAIL' },
        { text: 'Sms', value: 'SMS' }
      ],
      typesOfNotification: []
    }
  },
  computed: {
    availableParamsList() {
      return this.templateTriggersList[this.selectedEventType]?.params || []
    }
  },

  mounted() {
    this.updatePreview()
  },

  methods: {
    mapButtonType(type) {
      const typeMap = {
        URL: 'url',
        PHONE_NUMBER: 'call',
        QUICK_REPLY: 'quick_reply'
      }
      return typeMap[type] || 'url'
    },
    formatTemplateName() {
      // Replace spaces with underscores and convert to lowercase
      this.templateName = this.templateName.replace(/\s+/g, '_').toLowerCase()
      this.templateName = this.templateName.replace(/[^a-z0-9_]/g, '')
      this.validateTemplateName()
    },

    validateTemplateName() {
      if (!this.templateName) {
        this.errors.templateName = 'Template name is required'
        return false
      } else if (this.templateName.length > 512) {
        this.errors.templateName = 'Template name must be 512 characters or less'
        return false
      } else if (!/^[a-z0-9_]+$/.test(this.templateName)) {
        this.errors.templateName = 'Only lowercase letters, numbers and underscores are allowed'
        return false
      } else {
        this.errors.templateName = ''
        return true
      }
    },

    validateCategory() {
      if (!this.category) {
        this.errors.category = 'Category is required'
        return false
      } else {
        this.errors.category = ''
        return true
      }
    },

    validateBody() {
      if (!this.messageBody) {
        this.errors.messageBody = 'Message body is required'
        return false
      } else if (this.messageBody.length > 1024) {
        this.errors.messageBody = 'Message body must be 1024 characters or less'
        return false
      } else {
        this.errors.messageBody = ''
        this.updatePreview()
        return true
      }
    },
    insertVariable(variable) {
      const textarea = document.getElementById('messageBody')
      const cursorPos = textarea ? textarea.selectionStart : this.messageBody.length
      const variableText = `{{${variable}}}`

      this.messageBody =
        this.messageBody.substring(0, cursorPos) +
        variableText +
        this.messageBody.substring(cursorPos)

      this.validateBody()
    },

    addButton() {
      if (this.buttons.length >= 3) {
        this.errors.buttons = 'Maximum of 3 buttons allowed per template'
        return
      }

      this.buttons.push({
        type: 'url',
        text: '',
        url: '',
        urlType: 'static',
        phone: '',
        errors: {
          text: '',
          url: '',
          phone: ''
        }
      })

      this.errors.buttons = ''
      this.updatePreview()
    },

    removeButton(index) {
      this.buttons.splice(index, 1)
      this.errors.buttons = ''
      this.updatePreview()
    },

    validateButtonText(index) {
      const button = this.buttons[index]
      if (!button.text) {
        button.errors.text = 'Button text is required'
        return false
      } else if (button.text.length > 25) {
        button.errors.text = 'Button text must be 25 characters or less'
        return false
      } else {
        button.errors.text = ''
        this.updatePreview()
        return true
      }
    },

    validateButtonUrl(index) {
      const button = this.buttons[index]
      if (button.urlType === 'dynamic') {
        button.errors.url = ''
        button.url = `${APP_URL[process.env.VUE_APP_ENV]}/open/details/${
          this.orgData?.id
        }?cid={{1}}`
        this.updatePreview()
        return true
      }

      if (!button.url) {
        button.errors.url = 'URL is required'
        return false
      } else if (!/^https?:\/\//i.test(button.url)) {
        button.errors.url = 'URL must start with http:// or https://'
        return false
      } else {
        button.errors.url = ''
        this.updatePreview()
        return true
      }
    },

    validateButtonPhone(index) {
      const button = this.buttons[index]
      if (!button.phone) {
        button.errors.phone = 'Phone number is required'
        return false
      } else if (!/^\+[1-9]\d{1,14}$/.test(button.phone)) {
        button.errors.phone = 'Enter a valid phone number with country code (e.g. +1234567890)'
        return false
      } else {
        button.errors.phone = ''
        this.updatePreview()
        return true
      }
    },

    updatePreview() {
      if (this.headerFormat === 'text' && this.headerText) {
        this.previewHeader = this.headerText
      } else if (this.headerFormat === 'media') {
        this.previewHeader = '<v-icon small class="mr-1">mdi-image</v-icon> [Media]'
      } else {
        this.previewHeader = ''
      }

      let bodyText = this.messageBody
      bodyText = bodyText.replace(
        /\{\{([^}]+)\}\}/g,
        '<span class="variable bg-green-100 text-green-600 rounded px-1 font-medium">{{$1}}</span>'
      )
      this.previewBody = bodyText
      // Buttons
      this.previewButtons = this.buttons.map((button) => {
        return {
          type: button.type,
          text: button.text || 'Button'
        }
      })
    },

    validateForm() {
      const isNameValid = this.validateTemplateName()
      const isCategoryValid = this.validateCategory()
      const isBodyValid = this.validateBody()

      // Validate buttons
      let areButtonsValid = true
      this.buttons.forEach((button, index) => {
        const buttonTextValid = this.validateButtonText(index)

        if (button.type === 'url') {
          const urlValid = this.validateButtonUrl(index)
          areButtonsValid = areButtonsValid && urlValid
        } else if (button.type === 'call') {
          const phoneValid = this.validateButtonPhone(index)
          areButtonsValid = areButtonsValid && phoneValid
        }

        areButtonsValid = areButtonsValid && buttonTextValid
      })

      return isNameValid && isCategoryValid && isBodyValid && areButtonsValid
    },

    closeDialog() {
      this.$nextTick(() => {
        this.submitTemplateLoading = false
        this.$emit('close')
      })
    },
    submitTemplate() {
      if (this.validateForm()) {
        this.submitTemplateLoading = true
        const variableRegex = /\{\{([^}]+)\}\}/g
        const matches = [...this.messageBody.matchAll(variableRegex)]
        const params = matches.map((match) => match[1].trim())

        // Format the message body with {{1}}, {{2}}, etc.
        let formattedBody = this.messageBody
        params.forEach((param, index) => {
          const regex = new RegExp(`\\{\\{${param}\\}\\}`, 'g')
          formattedBody = formattedBody.replace(regex, `{{${index + 1}}}`)
        })

        // Create payload
        const payload = {
          templateName: this.templateName,
          templateContent: formattedBody,
          templateSubject: this.headerText,
          whatsappLangCode: this.language,
          category: this.category.toUpperCase(),
          typesOfNotification: this.typesOfNotification,
          params: params,
          eventType: this.selectedEventType || undefined
        }
        if (this.typesOfNotification && this.typesOfNotification?.length) {
          payload.approvalStatus = this.typesOfNotification.map((el) => {
            return { typeOfNotification: el, status: 'PENDING' }
          })
        } else {
          this.$toast.error('Please select at least one type of notification')
          return
        }

        payload.example = params.map((param) => `Sample ${param}`)

        if (this.buttons.length > 0) {
          payload.buttons = this.buttons.map((button) => {
            const buttonTypeMap = {
              quick_reply: 'QUICK_REPLY',
              url: 'URL',
              call: 'PHONE_NUMBER'
            }

            const buttonPayload = {
              type: buttonTypeMap[button.type] || 'URL',
              text: button.text
            }

            if (button.type === 'url') {
              buttonPayload.url = button.url
              buttonPayload.urlType = button.urlType

              if (button.urlType === 'dynamic') {
                buttonPayload.dynamicType = button.dynamicType
                buttonPayload.example = `https://vk.myfinfi.com/open/details/${this.orgData?.id}?cid=test123`
              }
            }

            if (button.type === 'call') {
              buttonPayload.phone = button.phone
            }

            return buttonPayload
          })
        }

        console.log('Payload:', payload)

        const method = this.editingTemplate ? 'put' : 'post'
        const endpoint = this.editingTemplate
          ? `/workforce/whatsapp/templates/${this.editingTemplate.whatsappTemplateId}`
          : '/workforce/whatsapp/templates'

        // console.log(`${method.toUpperCase()} template with payload:`, payload)
        this.$nextTick(() => {
          this.$axios[method](endpoint, payload)
            .then(() => {
              this.$toast.success(
                `Template ${this.editingTemplate ? 'updated' : 'submitted'} successfully!`
              )
              this.submitTemplateLoading = false
              this.closeDialog()
            })
            .catch((error) => {
              console.error(
                `Error ${this.editingTemplate ? 'updating' : 'submitting'} template:`,
                error
              )
              this.submitTemplateLoading = false
              this.$toast.error(
                error.response?.data?.message ||
                  `Failed to ${
                    this.editingTemplate ? 'update' : 'submit'
                  } template. Please try again.`
              )
            })
        })
      } else {
        this.$nextTick(() => {
          this.$toast.error('Please fix the errors in the form before submitting.')
        })
      }
    }
  }
}
</script>

<style>
.variable {
  background-color: #e5f7e9;
  color: #25d366;
  border-radius: 4px;
  padding: 2px 4px;
  font-weight: 500;
  cursor: pointer;
}
.top-header {
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 500;
}
.whatsapp-bg {
  background-color: #e5ded8;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(0,0,0,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
}
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
</style>
