<template>
  <div class="relative p-4">
    <div class="border border-blue-200 bg-white p-4 rounded-lg shadow-md">
      <p class="font-semibold mb-4">Generate Token For Webhook</p>
      <div class="flex gap-8">
        <v-btn color="primary" @click="generateToken">Generate</v-btn>

        <v-text-field
          v-model="maskedToken"
          outlined
          dense
          :readonly="true"
          :append-icon="showToken ? 'mdi-eye' : 'mdi-eye-off'"
          @click:append="toggleTokenVisibility"
        ></v-text-field>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      token: '',
      maskedToken: '',
      showToken: false
    }
  },
  methods: {
    generateToken() {
      this.token = 'abcd-1234-efgh-5678-ijkl-AH28M'
      this.maskedToken = this.maskToken(this.token)
      this.showToken = false
    },
    toggleTokenVisibility() {
      this.showToken = !this.showToken
      this.maskedToken = this.showToken ? this.token : this.maskToken(this.token)
    },
    maskToken(token) {
      const visiblePart = token.slice(0, 4) + '-****-****-' + token.slice(-4)
      return visiblePart
    }
  }
}
</script>

<style scoped>
.dark-bg-custom {
  background-color: #f8fafc;
}
</style>
