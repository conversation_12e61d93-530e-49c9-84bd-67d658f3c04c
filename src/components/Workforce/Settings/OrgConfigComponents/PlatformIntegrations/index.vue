<template>
  <div v-if="selectedIntegration" class="">
    <div class="flex justify-start items-center gap-4 w-full p-4">
      <v-btn @click="selectedIntegration = null" icon :color="$vuetify.theme.currentTheme.primary">
        <v-icon> mdi-arrow-left </v-icon>
      </v-btn>
      <span class="uppercase gradient-label font-semibold text-xl text-gray-700">
        {{ selectedIntegration.label }} Configuration
      </span>
    </div>
    <component :is="selectedIntegration.component" :receivedData="receivedData" />
  </div>
  <div v-else class="grid grid-cols-2 gap-4 w-full p-4">
    <div
      v-for="(platform, index) in platformIntegrations"
      :key="index"
      class="flex flex-wrap justify-between items-center border rounded-md p-4"
    >
      <div class="flex items-center gap-4">
        <img :src="platform.icon" class="w-16" />
        <span class="font-semibold text-xl text-gray-700">{{ platform.label }}</span>
      </div>
      <div class="flex items-center justify-end gap-2">
        <v-switch
          v-model="platform.enabled"
          @change="platformSwitch(platform)"
          :ripple="false"
          inset
          dense
          :label="platform.enabled ? 'Enable' : 'Disable'"
        ></v-switch>

        <!-- Hide Configure button if enabled is false -->
        <v-btn
          v-if="platform.enabled"
          :color="$vuetify.theme.currentTheme.primary"
          class="text-white px-2 py-1.5 white--text"
          @click="selectedIntegration = platform"
        >
          <v-icon color="white" class="mr-2">mdi-cog</v-icon>
          <span class="font-semibold text-sm">Configure</span>
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script>
import WhatsappIntegration from './WhatsappIntegration.vue'
import TelecallerIntegration from './TeleCallerIntegration.vue'
import WebhookIntegration from './WebhookIntegration.vue'
import RazorPayIntegration from './RazorPayIntegration.vue'
import MailerLiteIntegration from './MailerLiteIntegration.vue'
import ImgWhatsapp from '@/assets/img/whatsapp.png'
import ImgTelecaller from '@/assets/img/phone.png'
import ImgRazorpay from '@/assets/img/razor_pay_icon.png'
import ImgMailerlite from '@/assets/img/mailer_lite_icon.png'
import ImgWebohook from '@/assets/img/webhook.png'
import { isModuleFeatureAllowed } from '@/utils/common'

export default {
  name: 'WhatsappComponent',
  components: {
    WhatsappIntegration,
    TelecallerIntegration,
    WebhookIntegration,
    RazorPayIntegration,
    MailerLiteIntegration
  },
  data() {
    return {
      selectedIntegration: null
    }
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    }
  },
  computed: {
    platformIntegrations() {
      const platforms = [
        {
          name: 'whatsappIntegration',
          label: 'Whatsapp',
          component: 'WhatsappIntegration',
          icon: ImgWhatsapp,
          enabled: true,
          moduleName: 'WHATS_APP_BOT'
        },
        {
          name: 'telecallerIntegration',
          label: 'Telecaller',
          component: 'TelecallerIntegration',
          icon: ImgTelecaller,
          enabled: true,
          moduleName: 'CALL_A_CUSTOMER'
        },
        // {
        //   name: 'webhookIntegration',
        //   label: 'Webhook',
        //   component: 'WebhookIntegration',
        //   icon: ImgWebohook,
        //   enabled: true,
        //   moduleName: 'CALL_A_CUSTOMER'
        // },
        {
          name: 'razorPayIntegration',
          label: 'RazorPay Integration',
          component: 'RazorPayIntegration',
          icon: ImgRazorpay,
          enabled: true,
          moduleName: 'CALL_A_CUSTOMER'
        },
        {
          name: 'mailerLiteIntegration',
          label: 'Mailer Lite Integration',
          component: 'MailerLiteIntegration',
          icon: ImgMailerlite,
          enabled: true,
          moduleName: 'CALL_A_CUSTOMER'
        }
      ]

      return platforms.filter((platform) => this.isFeatureVisible(platform.moduleName))
    }
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('ADDONS', feature)
    },
    platformSwitch(platform) {
      console.log(platform)
    }
  }
}
</script>

<style>
.gradient-label {
  display: inline-block;
  background-image: linear-gradient(135deg, #1e3c72, #2a5298);
  -webkit-background-clip: text;
  color: transparent;
}
</style>
