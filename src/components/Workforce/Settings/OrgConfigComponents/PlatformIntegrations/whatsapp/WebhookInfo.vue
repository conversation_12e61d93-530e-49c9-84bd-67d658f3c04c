<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-8">
      <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="border-b border-gray-200">
          <ul class="flex flex-wrap -mb-px">
            <li class="mr-2" v-for="tab in tabs" :key="tab.id">
              <button
                @click="activeTab = tab.id"
                :class="[
                  'inline-block p-4 font-medium text-sm',
                  activeTab === tab.id ? 'tab-active' : 'text-gray-500 hover:text-gray-700'
                ]"
              >
                {{ tab.name }}
              </button>
            </li>
          </ul>
        </div>

        <div class="p-6">
          <div v-if="activeTab === 'guide'" class="space-y-8">
            <!-- Step 1 -->
            <div class="step-card bg-white rounded-lg border border-gray-200 p-6">
              <div class="flex items-start">
                <div class="step-number bg-blue-100 text-blue-600 rounded-full mr-4 font-bold">
                  1
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-800 mb-3">
                    Generate Verification Token
                  </h3>
                  <p class="text-gray-600 mb-4">
                    First, generate a verification token that you'll need to enter in the WhatsApp
                    Business settings.
                  </p>

                  <div class="flex flex-wrap items-center gap-4 mb-4">
                    <div class="flex-1 min-w-[200px] relative">
                      <input
                        v-model="verificationToken"
                        type="text"
                        readonly
                        class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-50"
                      />
                      <button
                        @click="copyToken(verificationToken)"
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 copy-btn p-1 rounded-md"
                        :class="{ copied: tokenCopied }"
                        :disabled="!verificationToken"
                      >
                        <i class="fas" :class="tokenCopied ? 'fa-check' : 'fa-copy'"></i>
                        {{ tokenCopied ? 'Copied!' : 'Copy' }}
                      </button>
                    </div>
                    <button
                      @click="generateToken"
                      class="px-4 py-2 bg-blue-800 text-white rounded-lg hover:bg-blue-900 transition"
                    >
                      <i class="fas fa-sync-alt mr-2"></i>{{ generateButtonText }}
                    </button>
                  </div>

                  <p class="text-sm text-gray-500">
                    Copy this token as you'll need it in the next steps.
                  </p>
                </div>
              </div>
            </div>

            <!-- Step 2 -->
            <div class="step-card bg-white rounded-lg border border-gray-200 p-6">
              <div class="flex items-start">
                <div class="step-number bg-blue-100 text-blue-600 rounded-full mr-4 font-bold">
                  2
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-800 mb-3">
                    Access WhatsApp Business Settings
                  </h3>
                  <p class="text-gray-600 mb-4">
                    Go to the WhatsApp Business Manager and navigate to your business account
                    settings.
                  </p>

                  <div class="mb-6">
                    <img
                      src="@/assets/img/webh1.png"
                      alt="WhatsApp Business Manager"
                      class="rounded-lg border border-gray-200 w-full max-w-2xl mx-auto"
                    />
                    <p class="text-sm text-gray-500 text-center mt-2">
                      WhatsApp Business Platform Dashboard
                    </p>
                  </div>

                  <div class="bg-gray-100 rounded-lg p-4 mb-4">
                    <div class="flex items-center text-gray-500 mb-2">
                      <i class="fas fa-exclamation-circle mr-2"></i>
                      <span class="text-sm"
                        >You must have admin access to the WhatsApp Business account</span
                      >
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-blue-50 border border-blue-100 rounded-lg p-4">
                      <h4 class="font-medium text-blue-700 mb-2">Where to find it:</h4>
                      <ol class="list-decimal list-inside text-sm text-gray-600 space-y-1">
                        <li>
                          Go to
                          <a
                            href="https://business.facebook.com/"
                            target="_blank"
                            class="text-blue-600 hover:underline font-semibold"
                            >Meta Business Suite</a
                          >
                        </li>
                        <li>Select your business account</li>
                        <li>Navigate to "Whatsapp" in the sidebar</li>
                        <li>Select "Configuration" under "Whatspp"</li>
                      </ol>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 3 -->
            <div class="step-card bg-white rounded-lg border border-gray-200 p-6">
              <div class="flex items-start">
                <div class="step-number bg-blue-100 text-blue-600 rounded-full mr-4 font-bold">
                  3
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-800 mb-3">Configure Webhook URL</h3>
                  <p class="text-gray-600 mb-4">
                    In your WhatsApp Business settings, find the "Webhooks" section and configure
                    your webhook URL.
                  </p>

                  <div class="mb-6">
                    <img
                      src="@/assets/img/webh2.png"
                      alt="WhatsApp Webhook Configuration"
                      class="rounded-lg border border-gray-200 w-full max-w-2xl mx-auto"
                    />
                    <p class="text-sm text-gray-500 text-center mt-2">
                      WhatsApp Webhook Configuration Screen
                    </p>
                  </div>

                  <div class="bg-white border border-gray-200 rounded-lg p-4 mb-4">
                    <h4 class="font-medium text-gray-700 mb-2">Your Webhook URL:</h4>
                    <div class="flex items-center">
                      <div class="flex-1 relative">
                        <input
                          v-model="webhookUrl"
                          type="text"
                          readonly
                          class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-50"
                        />
                        <button
                          @click="copyToken(webhookUrl)"
                          class="absolute right-2 top-1/2 transform -translate-y-1/2 copy-btn p-1 rounded-md"
                          :class="{ copied: urlCopied }"
                        >
                          <i class="fas" :class="urlCopied ? 'fa-check' : 'fa-copy'"></i>
                          {{ urlCopied ? 'Copied!' : 'Copy' }}
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4">
                      <h4 class="font-medium text-yellow-700 mb-2">Required Fields:</h4>
                      <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                        <li>Callback URL: Your webhook URL (shown above)</li>
                        <li>Verify token: The token you generated in Step 1</li>
                        <li>Subscription fields: Select at least "messages"</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 4 -->
            <div class="step-card bg-white rounded-lg border border-gray-200 p-6">
              <div class="flex items-start">
                <div class="step-number bg-blue-100 text-blue-600 rounded-full mr-4 font-bold">
                  4
                </div>
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-800 mb-3">
                    Verify and Test Connection
                  </h3>
                  <p class="text-gray-600 mb-4">
                    After configuring your webhook, verify the connection and test it by sending a
                    message.
                  </p>

                  <div class="mb-6">
                    <img
                      src="@/assets/img/webh3.png"
                      alt="WhatsApp Webhook Verification"
                      class="rounded-lg border border-gray-200 w-full max-w-2xl mx-auto"
                    />
                    <p class="text-sm text-gray-500 text-center mt-2">
                      Successful Webhook Verification
                    </p>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="bg-green-50 border border-green-100 rounded-lg p-4">
                      <h4 class="font-medium text-green-700 mb-2">Success Indicators:</h4>
                      <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                        <li>You'll see a "Webhook verified" message</li>
                        <li>Status will change to "Connected"</li>
                        <li>Test messages will appear in your dashboard</li>
                      </ul>
                    </div>

                    <div class="bg-red-50 border border-red-100 rounded-lg p-4">
                      <h4 class="font-medium text-red-700 mb-2">Troubleshooting:</h4>
                      <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                        <li>Ensure your webhook URL is publicly accessible</li>
                        <li>Verify the token matches exactly</li>
                        <li>Check that you've subscribed to "messages"</li>
                        <li>Confirm your server responds correctly to verification</li>
                      </ul>
                    </div>
                  </div>

                  <div class="bg-gray-100 rounded-lg p-4">
                    <h4 class="font-medium text-gray-700 mb-2">Testing Your Connection:</h4>
                    <ol class="list-decimal list-inside text-sm text-gray-600 space-y-1">
                      <li>Send a test message to your WhatsApp Business number</li>
                      <li>Check our dashboard to see if the message was received</li>
                      <li>If the message appears, your webhook is working correctly</li>
                    </ol>
                  </div>
                </div>
              </div>
              <div class="mt-4 flex items-center justify-end">
                <button
                  @click="testWebhook"
                  class="px-4 py-2 rounded-lg transition"
                  :class="[
                    webhookTestSuccess
                      ? 'bg-green-500 text-white'
                      : 'bg-blue-800 text-white hover:bg-blue-900'
                  ]"
                  :disabled="!verificationToken || webhookTestSuccess || webhookTestInProgress"
                >
                  <i class="fas mr-2" :class="webhookTestSuccess ? 'fa-check' : 'fa-bolt'"></i>
                  {{ testButtonText }}
                </button>

                <span v-if="webhookTestSuccess" class="ml-3 text-green-600">
                  Webhook verified successfully
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'guide',
      tabs: [{ id: 'guide', name: 'Follow Steps' }],
      verificationToken: '',
      webhookUrl: `${process.env.VUE_APP_BASE_URL}/workforce/webhooks/whatsapp`,
      tokenCopied: false,
      urlCopied: false,
      isTokenGenerated: false,
      webhookTested: false,
      webhookTestInProgress: false,
      webhookTestSuccess: false
    }
  },
  computed: {
    generateButtonText() {
      return this.isTokenGenerated ? 'Regenerate' : 'Generate'
    },
    testButtonText() {
      if (this.webhookTestSuccess) return 'Tested'
      if (this.webhookTestInProgress) return 'Testing...'
      return 'Test Webhook'
    }
  },
  mounted() {
    this.fetchWebhookConfig()
  },
  methods: {
    async fetchWebhookConfig() {
      try {
        const response = await this.$axios.get('/workforce/webhooks/whatsapp/webhook-token')
        console.log(response)
        const data = response.data?.token
        if (data?.token) {
          this.verificationToken = data.token
          this.isTokenGenerated = true
        }
        if (data?.isVerified) {
          this.webhookTested = true
          this.webhookTestSuccess = true
        }
      } catch (error) {
        console.error('Error fetching webhook config:', error)
      }
    },
    async generateToken() {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      let token = ''
      for (let i = 0; i < 24; i++) {
        token += characters.charAt(Math.floor(Math.random() * characters.length))
      }
      this.verificationToken = token
      this.isTokenGenerated = true

      await this.saveWebhookConfig()
    },
    async saveWebhookConfig() {
      try {
        const payload = {
          token: this.verificationToken
        }
        await this.$axios.post('/workforce/webhooks/whatsapp/webhook-token', payload)

        this.$toast.success('Webhook configuration saved successfully')
        await this.fetchWebhookConfig()
      } catch (error) {
        console.error('Error saving webhook config:', error)
        this.$toast.error('Failed to save webhook configuration')
      }
    },
    async testWebhook() {
      if (this.webhookTestSuccess) return

      this.webhookTestInProgress = true

      try {
        const response = await this.$axios.get(
          `/workforce/webhooks/whatsapp?hub.verify_token=${this.verificationToken}`
        )
        if (response.status === 200) {
          this.webhookTestSuccess = true
          this.webhookTested = true
          await this.updateWebhookConfig()
          this.$toast.success('Webhook connection verified successfully')
        }
      } catch (error) {
        console.error('Error testing webhook:', error)
        this.$toast.error('Failed to verify webhook connection. Please check your configuration.')
      } finally {
        this.webhookTestInProgress = false
      }
    },
    async updateWebhookConfig() {
      try {
        const payload = {
          isVerified: this.webhookTestSuccess
        }
        await this.$axios.put('/workforce/webhooks/whatsapp/webhook-token/verify', payload)
        this.$toast.success('Webhook configuration saved successfully')
      } catch (error) {
        console.error('Error saving webhook config:', error)
        this.$toast.error('Failed to update webhook configuration')
      }
    },
    copyToken(text) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          if (text === this.verificationToken) {
            this.tokenCopied = true
            setTimeout(() => {
              this.tokenCopied = false
            }, 2000)
          } else if (text === this.webhookUrl) {
            this.urlCopied = true
            setTimeout(() => {
              this.urlCopied = false
            }, 2000)
          }
        })
        .catch((err) => {
          console.error('Could not copy text: ', err)
        })
    },
    toggleFaq(index) {
      this.faqs[index].open = !this.faqs[index].open
    }
  }
}
</script>

<style>
.step-card {
  transition: all 0.3s ease;
}
.step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.step-number {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.copy-btn {
  transition: all 0.2s ease;
}
.copy-btn:hover {
  background-color: #e5e7eb;
}
.copy-btn.copied {
  background-color: #10b981;
  color: white;
}
.tab-active {
  border-bottom: 3px solid #3b82f6;
  color: #3b82f6;
}
</style>
