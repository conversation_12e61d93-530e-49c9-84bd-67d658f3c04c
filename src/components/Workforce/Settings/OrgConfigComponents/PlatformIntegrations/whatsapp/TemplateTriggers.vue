<template>
  <div class="template-triggers-container">
    <v-divider class="my-4"></v-divider>
    <p class="text-lg gradient-label font-bold text-gray-700">WhatsApp Triggers</p>
    <v-tabs class="relative mb-4" v-model="activeTab">
      <v-tab>Webhook Integration</v-tab>
      <v-tab>Message Log</v-tab>
    </v-tabs>
    <div v-if="activeTab === 0" class="relative">
      <WebhookInfo />
    </div>
    <div v-if="activeTab === 1" class="relative">
      <WhatsappMessages />
    </div>
  </div>
</template>

<script>
import WhatsappMessages from '@/components/Workforce/WhatsappMessages'
import WebhookInfo from './WebhookInfo.vue'

export default {
  name: 'TemplateTriggers',
  components: { WhatsappMessages, WebhookInfo },

  data() {
    return {
      activeTab: 0
    }
  }
}
</script>

<style>
.template-triggers-container .v-window__container {
  position: unset;
}
</style>
