<template>
  <div>
    <p class="gradient-label">Integrate your WhatsApp Business Account with <span class="font-bold text-blue-800">CualityWork</span> to trigger notifications to your customers and agents.</p>
    <v-divider></v-divider>
    <h5 class="text-lg my-2 text-gray-700">Prerequisites</h5>
    <ol class="list-decimal text-sm mb-8">
      <li>You must have a verified <span class="font-bold text-gray-800">Facebook Business Account</span>.</li>
      <li class="mt-2">You must have a phone number associated with your <span class="font-bold text-gray-800">Facebook Business Account</span>.</li>
    </ol>
    <h5 class="text-lg my-2 text-gray-700">Steps to integrate with WhatsApp</h5>
    <ol class="list-decimal text-sm">
      <li>Click on the <span class="font-bold text-gray-800">Login with Facebook</span> button on the top right corner.</li>
      <li class="mt-2">
        <p>
          Enter your <span class="font-bold text-gray-800">WhatsApp Business Account</span> ID, then click on <span class="font-bold text-gray-800">Continue</span>.
        </p>
        <div class="border shadow-sm w-3/5">
          <img src="@/assets/img/whatsapp-integration-step-1.png" alt="WhatsApp Login" class="w-full">
        </div>
      </li>
      <li class="mt-2">Follow the instructions to login with your <span class="font-bold text-gray-800">Facebook Business Account</span>.</li>
      <li class="mt-2">
        <p>
          Once you have successfully logged in, you can create message templates and assign them to different triggers.
        </p>
        <div class="border shadow-sm w-3/5">
          <img src="@/assets/img/whatsapp-integration-step-2.png" alt="template triggers" class="w-full">
        </div>
      </li>
    </ol>
  </div>
</template>

<script>
export default {
  name: "IntegrationInfo",
}
</script>
