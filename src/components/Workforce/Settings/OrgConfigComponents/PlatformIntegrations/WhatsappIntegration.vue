<template>
  <div>
    <div v-if="waSystemToken" class="text-white absolute right-3 top-3">
      <v-btn color="red" text @click="confirmDisconnectFbAccount = true"
        >Disconnect facebook account</v-btn
      >
    </div>
    <button
      v-else
      class="px-2 rounded-lg py-1 my-4 absolute right-5 top-0"
      :style="{ backgroundColor: '#1877F2' }"
      @click="onLoginWithFb"
      :disabled="isLoading"
    >
      <v-icon v-if="!isLoading" class="white--text mr-1">mdi-facebook</v-icon>
      <v-progress-circular
        v-else
        indeterminate
        size="20"
        color="white"
        class="mr-1"
      ></v-progress-circular>
      <span class="text-white font-semibold text-sm">
        {{ isLoading ? 'Connecting...' : 'Login with Facebook' }}
      </span>
    </button>
    <div class="p-5 pt-0 overflow-auto max-h-[70vh]">
      <div class="border border-blue-200 relative bg-white dark-bg-default p-3 relative">
        <IntegrationInfo v-if="!waSystemToken" />
        <div>
          <template v-if="businessNumbers.length">
            <p class="gradient-label mb-0">
              Phone number connected with
              <span class="font-bold">WhatsApp</span> integration
            </p>
            <v-radio-group class="-mb-4" v-model="waBusinessNumber">
              <v-radio
                v-for="n in businessNumbers"
                :key="n.id"
                :label="`${maskNumber(n.display_phone_number)} (${n.verified_name})`"
                :value="n.id"
              ></v-radio>
            </v-radio-group>
          </template>
        </div>
        <!-- <div v-if="!!waBusinessNumber" v-show="isFeatureVisible('WHATS_APP_BOT')">
          <div class="absolute top-3 right-3">
            <v-btn
              v-if="botFlows.length < 3"
              @click="showBotBuilderDialog = true"
              elevation="2"
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              >Add bot flow</v-btn
            >
          </div>
          <template v-if="botFlows.length">
            <v-divider class="my-4"></v-divider>
            <p>Chatbot Templates</p>
          </template>
          <div>
            <div v-for="flow in botFlows" :key="flow.id" class="flex items-center justify-between">
              <span class="font-semibold text-gray-500"
                >Flow #{{ flow._id.slice(20).toUpperCase() }}</span
              >
              <span class="font-semibold text-green-700 ml-2" v-if="flow._id === activeBotFlow"
                >ACTIVE</span
              >
              <div class="flex align-center gap-3 justify-end">
                <v-btn
                  v-if="flow._id !== activeBotFlow"
                  @click="setActiveBotFlow(flow)"
                  elevation="2"
                  class="mb-4"
                  >Set as default</v-btn
                >
                <v-btn
                  @click="onEditFlow(flow)"
                  elevation="2"
                  class="mb-4"
                  v-show="isFeatureVisible('CUSTOMISE_BOT_FLOW')"
                  >Edit</v-btn
                >
                <v-btn
                  @click="onFlowDelete(flow)"
                  elevation="2"
                  class="mb-4"
                  color="error"
                  v-show="isFeatureVisible('CUSTOMISE_BOT_FLOW')"
                  >Delete</v-btn
                >
              </div>
            </div>
          </div>
        </div> -->

        <!-- <div>
          <v-divider class="my-4"></v-divider>
          <div class="flex mb-4 items-center justify-between">
            <p class="text-lg gradient-label font-bold text-gray-700">Case Outcome Templates</p>
            <v-btn @click="onAddAppTemplate" color="primary" variant="tonal">
              <v-icon class="mr-2">mdi-plus</v-icon>
              Add Template
            </v-btn>
          </div>
          <div v-if="appTemplates.length" class="grid grid-cols-3 gap-4">
            <div
              v-for="(t, i) in appTemplates"
              :key="i"
              class="flex-col flex items-between gap-2 justify-start border shadow-sm py-2 px-3 relative"
            >
              <div class="flex flex-col gap-1">
                <p class="font-semibold text-gray-600 mb-0">{{ t.name }}</p>
                <p class="text-gray-700 text-sm mb-0">{{ t.templateTxt }}</p>
              </div>
              <div class="flex items-end justify-between">
                <p
                  class="font-semibold text-xs mb-0 pb-0.5 text-gray-700"
                  :class="getStatusColor(t.status)"
                >
                  {{ t.status }}
                </p>
                <div class="flex items-center justify-end gap-2">
                  <v-btn
                    icon
                    :color="$vuetify.theme.currentTheme.primary"
                    @click="onTemplateEdit(t.indx)"
                  >
                    <v-icon>mdi-square-edit-outline</v-icon>
                  </v-btn>
                  <v-btn icon color="red" @click="onTemplateDelete(t.indx)">
                    <v-icon>mdi-trash-can</v-icon>
                  </v-btn>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <TemplateTriggers
          v-if="!!waBusinessNumber"
          :bindTemplateRes="bindTemplateRes"
          @triggerChange="updateTemplateTrigger"
          @triggerDelete="handleDeleteTemplateTrigger"
          @addTemplateTrigger="onAddTrigger"
          :templateTriggersList="templateTriggersList"
          :availableTemplates="availableTemplates"
        >
          <div>
            <div class="absolute z-20 -top-14 right-0 flex items-center justify-between">
              <div class="flex items-center justify-end gap-2 mb-4">
                <div>
                  <div
                    @click="onRefreshTemplates"
                    class="flex items-center justify-center cursor-pointer h-10 w-10 border border-gray-300 shadow-sm"
                  >
                    <Loader v-if="fetchingTemplates" />
                    <v-icon v-else color="blue">mdi-replay</v-icon>
                  </div>
                </div>
                <div class="w-48">
                  <SelectComponent
                    label="Status"
                    :items="templateStatuses"
                    v-model="filters.status"
                  />
                </div>
                <v-btn color="primary" variant="tonal" class="py-5" @click="onAddTemplate">
                  <v-icon class="mr-2">mdi-plus</v-icon>
                  Add Template
                </v-btn>
              </div>
            </div>
            <div>
              <SelectComponent
                label="Select"
                :items="availableTemplates"
                v-model="filters.search"
                item-text="templateName"
                item-value="templateName"
                :clearable="true"
              />
            </div>
            <div
              v-if="!filteredTemplates.length"
              class="flex flex-col items-center justify-center py-16"
            >
              <no-data
                title="Nothing to Display"
                :subTitle="`No templates found with the current filters`"
              />
            </div>
            <div v-else class="grid gap-3 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
              <div
                v-for="(t, i) in filteredTemplates"
                :key="i"
                class="flex-col flex items-between gap-2 justify-start border shadow-sm py-2 px-3 relative"
              >
                <div class="flex flex-col gap-1">
                  <p class="font-semibold text-gray-600 mb-0">
                    {{ titleCase(t.templateName) }}
                  </p>
                  <div label class="bg-gray-100 px-2 py-3 rounded-md">
                    <p
                      class="text-gray-700 text-sm mb-0 whitespace-normal break-words"
                      v-html="destructureTemplateBody(t)"
                    ></p>
                  </div>
                  <p class="text-gray-700 text-sm mb-0 mt-4">
                    <strong>Created On:</strong>
                    {{ $dayjs(t.createdAt).format('DD-MM-YYYY hh:mm A') }}
                  </p>
                  <p class="text-gray-700 text-sm">
                    <strong>Created By:</strong>
                    Anonymous
                  </p>
                </div>
                <p
                  class="flex absolute top-3 right-3 font-semibold text-xs mb-0 pb-0.5 text-gray-700"
                  :class="getStatusColor(t.status || 'PENDING')"
                >
                  {{ t.status || 'PENDING' }} BY
                  <v-img src="@/assets/img/meta_logo.jpg" class="w-14 h-4"></v-img>
                  <v-menu offset-y>
                    <template v-slot:activator="{ on, attrs }">
                      <v-icon size="18" v-bind="attrs" v-on="on"> mdi-dots-vertical </v-icon>
                    </template>
                    <v-list>
                      <v-list-item @click="onTemplateEdit(t)" class="text-sm">
                        <v-list-item-content>
                          <v-list-item-title>Edit</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>

                      <v-list-item v-if="!t.templateType" @click="onTemplateDelete(i)">
                        <v-list-item-content>
                          <v-list-item-title>Delete</v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>

                      <v-list-item>
                        <v-switch
                          :ripple="false"
                          inset
                          dense
                          v-model="t.enabled"
                          @change="templateSwitch(t)"
                          :label="t.enabled ? 'Enable' : 'Disable'"
                        ></v-switch>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </p>
                <div class="flex items-center justify-between">
                  <div v-if="t.templateType">
                    <p class="mb-0 text-xs font-semibold">For Trigger</p>
                    <p class="mb-0 text-green-800 text-xs font-semibold">
                      {{ titleCase(t.templateType) }}
                    </p>
                  </div>
                </div>
                <v-chip
                  v-if="isRecentlyUpdated(t)"
                  small
                  color="warning"
                  class="font-semibold"
                  title="Updated in the last 24 hours. Further updates may be delayed."
                >
                  Updated in the last 24 hours. Further updates may be delayed.
                </v-chip>
              </div>
            </div>
          </div>
        </TemplateTriggers>
      </div>
      <v-dialog
        v-model="isBusinessIdDialogVisible"
        persistent
        max-width="500"
        :color="$vuetify.theme.currentTheme.navigationColor"
      >
        <div>
          <div class="p-4 bg-white dark-bg-custom">
            <v-text-field
              focused
              v-model="waBussinessId"
              label="Whatsapp Business Id"
            ></v-text-field>
            <div class="flex items-center gap-2 justify-end">
              <v-btn @click="isBusinessIdDialogVisible = false" outlined elevation="2" color="red"
                >Cancel</v-btn
              >
              <v-btn
                @click="onAddBusinessId"
                elevation="2"
                :loading="addingBusinessId"
                :color="$vuetify.theme.currentTheme.primary"
                class="white--text"
                >Continue</v-btn
              >
            </div>
          </div>
        </div>
      </v-dialog>
      <v-dialog
        v-model="showBotBuilderDialog"
        persistent
        fullscreen
        hide-overlay
        :color="$vuetify.theme.currentTheme.navigationColor"
      >
        <iframe
          @load="onIframeLoaded"
          ref="botFlow"
          src="https://superadmin.myfinfi.com/admin/bot-builder"
          width="100%"
          height="100%"
        ></iframe>
      </v-dialog>
      <v-dialog
        v-model="showBotBuilderDialog"
        persistent
        fullscreen
        hide-overlay
        :color="$vuetify.theme.currentTheme.navigationColor"
      >
        <iframe
          @load="onIframeLoaded"
          ref="botFlow"
          src="https://bot-builder.myfinfi.com/admin/bot-builder"
          width="100%"
          height="100%"
        ></iframe>
      </v-dialog>
      <v-dialog
        v-model="editTemplateParamsVisible"
        persistent
        width="450px"
        :color="$vuetify.theme.currentTheme.navigationColor"
      >
        <div class="p-4 bg-white dark-bg-custom">
          <v-select
            v-for="(t, i) in templateParams"
            :key="i"
            :items="getTemplateParamOptions()"
            v-model="templateTriggers[templateParamsEditIndx].params[i]"
            :label="`Select param value for ${templateParams[i]}`"
            item-text="label"
            item-value="value"
            dense
            outlined
            :clearable="true"
          ></v-select>
          <div class="flex gap-2 items-center justify-end">
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              @click="editTemplateParamsVisible = false"
            >
              <span class="text-white"> Done </span>
            </v-btn>
          </div>
        </div>
      </v-dialog>
      <v-dialog v-model="addTemplateTriggerDialogVisible" persistent width="450px">
        <div class="p-4 bg-white dark-bg-custom">
          <div class="flex justify-between mb-3">
            <h2 class="text-xl font-bold">Add Trigger</h2>
            <v-icon @click="addTemplateTriggerDialogVisible = false">mdi-close</v-icon>
          </div>
          <v-select
            :items="templateTriggersOptions"
            v-model="newTemplateTrigger"
            label="Trigger type"
            item-text="label"
            item-value="value"
            dense
            outlined
            :clearable="true"
          ></v-select>
          <v-select
            :items="availableTriggerTemplates"
            v-model="newTemplateTriggerTemplate"
            label="Select template"
            item-text="templateName"
            item-value="templateName"
            dense
            outlined
            :clearable="true"
          ></v-select>
          <v-text-field label="Description" dense outlined> </v-text-field>
          <div class="flex gap-2 items-center justify-end">
            <v-btn outlined @click="addTemplateTriggerDialogVisible = false">
              <span> Cancel </span>
            </v-btn>
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              :loading="addingTrigger"
              @click="handleAddTemplateTrigger"
            >
              <span class="text-white"> Save </span>
            </v-btn>
          </div>
        </div>
      </v-dialog>
      <v-dialog v-model="addTemplateDialogVisible" persistent width="1000px">
        <AddTemplate
          :templateTriggersList="templateTriggersList"
          :allTriggers="allTriggers"
          :editingTemplate="editingTemplate"
          @close="addTemplateDialogVisible = false"
        />
      </v-dialog>
      <AlertPopUp
        :showConfirmationDialog="showDeleteTriggerDialog"
        :confirmationTitle="'Do you want to delete this template?'"
        :confirmationMessage="''"
        :confirmationActionText="'Delete'"
        :confirmationActionColor="'red'"
        :performAction="deleteTemplateTrigger"
        :cancel="displayDeleteDialog"
      />
      <AlertPopUp
        :showConfirmationDialog="confirmDisconnectFbAccount"
        :confirmationTitle="'Do you want to disconnect?'"
        :confirmationMessage="'This will remove all your configurations from our platform'"
        :confirmationActionText="'Ok'"
        :confirmationActionColor="'red'"
        :performAction="handleDisconnectFacebookAccount"
        :cancel="onDisconnectFbAccount"
      />
      <AlertPopUp
        :showConfirmationDialog="templateDeleteDialogVisible"
        :confirmationTitle="'Do you want to delete this template?'"
        :confirmationMessage="''"
        :confirmationActionText="'Ok'"
        :confirmationActionColor="'red'"
        :performAction="deleteWhatsappTemplate"
        :cancel="deleteTemplate"
        :loading="deletingTemplate"
      />
      <AlertPopUp
        :showConfirmationDialog="confirmDeleteBotFlow"
        :confirmationTitle="'Do you want to delete this flow?'"
        :confirmationMessage="''"
        :confirmationActionText="'Ok'"
        :confirmationActionColor="'red'"
        :performAction="deleteBotFlow"
        :cancel="deleteFlow"
      />
    </div>
  </div>
</template>
<script>
import { isModuleFeatureAllowed, maskPhoneNumber } from '@/utils/common'
import { debounce, convertToTitleCase } from '@/utils/common'
// import { Mentionable } from 'vue-mention'
import AlertPopUp from '@/components/AlertPopUp'
import SelectComponent from '@/components/UI/Select.vue'
import TemplateTriggers from './whatsapp/TemplateTriggers'
import Loader from '@/components/UI/Loader.vue'
import IntegrationInfo from './whatsapp/IntegrationInfo'
import NoData from '@/components/NoData.vue'
import AddTemplate from './AddTemplate.vue'

const fields = {
  assigned_to: {
    label: 'Assigned To',
    value: 'assigned_to',
    description: 'The user who is assigned to the task',
    eg: 'John Doe'
  },
  task_end_time: {
    label: 'Task End Time',
    value: 'task_end_time',
    description: 'Recorded time when the task is completed',
    eg: '15/08/2024'
  },
  total_pending_tasks: {
    label: 'Total pending tasks',
    value: 'total_pending_tasks',
    description: 'Number of tasks that are in pending status',
    eg: 5
  },
  task_title: {
    label: 'Task title',
    value: 'task_title',
    description: 'The title of the task',
    eg: 'Customer complaint'
  },
  customer_name: {
    label: 'Customer name',
    value: 'customer_name',
    description: 'Name of the customer for the assigned case',
    eg: 'John Doe'
  },
  amc_data: {
    label: 'AMC stats',
    value: 'amc_data',
    description: 'AMC data',
    eg: 'Company name - Plan name - Task title'
  },
  total_in_progress_tasks: {
    label: 'Total in-progress tasks',
    value: 'total_in_progress_tasks',
    description: 'Total number of tasks, that are in in-progress',
    eg: 45
  },
  total_completed_tasks: {
    label: 'Total completed tasks',
    value: 'total_completed_tasks',
    description: 'Total completed tasks',
    eg: 50
  },
  reference_id: {
    label: 'Reference Id',
    value: 'reference_id',
    description: 'Reference Id of complaint',
    eg: 'RID-1234567890'
  },
  status: {
    label: 'Status',
    value: 'status',
    description: 'Status of the task',
    eg: 'Completed'
  }
}

const templateTriggersList = {
  task_assign: {
    title: 'Agent Task Assign',
    params: [fields.assigned_to, fields.task_title]
  },
  task_pending: {
    title: 'Agent Task Pending',
    params: [
      fields.total_tasks,
      fields.total_in_progress_tasks,
      fields.total_completed_tasks,
      fields.total_pending_tasks,
      fields.assigned_to
    ]
  },
  task_end_agent: {
    title: 'Task End Agent',
    params: [fields.assigned_to, fields.task_title, fields.customer_name]
  },
  task_end_customer: {
    title: 'Task End Customer',
    params: [fields.assigned_to, fields.task_title, fields.customer_name, fields.status]
  },
  amc_notification: {
    title: 'AMC Notification',
    params: [fields.amc_data]
  },
  not_checked_in_yet: {
    title: 'Not Checked In Notification',
    params: [fields.assigned_to]
  },
  not_started_any_task_yet: {
    title: 'Not Started Any Task Notification',
    params: [fields.assigned_to]
  },
  tele_caller_notification_agent: {
    title: 'Agent Call End',
    params: []
  },
  tele_caller_notification_customer: {
    title: 'Customer Call End',
    params: []
  },
  customer_case_details: {
    title: 'Customer Case Details',
    params: [fields.customer_name, fields.assigned_to, fields.task_title, fields.reference_id]
  },
  dynamic_form: {
    title: 'Dynamic Form',
    params: [fields.customer_name, fields.assigned_to, fields.reference_id, fields.task_title]
  }
}

export default {
  components: {
    // Mentionable,
    AddTemplate,
    IntegrationInfo,
    SelectComponent,
    TemplateTriggers,
    Loader,
    AlertPopUp,
    NoData
  },
  data() {
    return {
      authData: this.$storage.getUniversal('token'),
      orgCode: this.$storage.getUniversal('organization_code'),
      templateTriggers: [],
      templateTriggersList,
      bindTemplateRes: [],
      templateParams: [],
      whatsappTemplates: [],
      addingTrigger: false,
      addTemplateTriggerDialogVisible: false,
      addTemplateDialogVisible: false,
      newTemplateTrigger: '',
      newTemplateTriggerCategory: '',
      newTemplateTriggerTemplate: '',
      selectedTemplateType: '',
      waBusinessNumber: '',
      waBussinessId: '',
      waSystemToken: '',
      activeBotFlow: null,
      businessNumbers: [],
      confirmDisconnectFbAccount: false,
      isDefaultWhatsappFlow: true,
      isBusinessIdDialogVisible: false,
      addingBusinessId: false,
      showBotBuilderDialog: false,
      templateTriggerTypes: [],
      templateTriggerRest: [],
      botFlows: [],
      botFlow: null,
      confirmDeleteBotFlow: false,
      templateParamsEditIndx: null,
      deletingTemplate: false,
      editTemplateParamsVisible: false,
      templateDeleteDialogVisible: false,
      templateDeleteIndx: null,
      editTemplateIndx: null,
      showDeleteTriggerDialog: false,
      triggerDeleteIndex: null,
      fetchingTemplates: false,
      direction: 'left',
      filters: {
        status: ''
      },
      editingTemplate: null,
      isLoading: false
    }
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    }
  },
  computed: {
    allTriggers() {
      return Object.keys(templateTriggersList).reduce((prev, key) => {
        prev.push({
          label: templateTriggersList[key]?.title,
          value: key
        })

        return prev
      }, [])
    },
    templateTriggersOptions() {
      const alreadyAddedTriggers = this.bindTemplateRes.reduce((prev, curr) => {
        if (curr.templateType) prev[curr.templateType] = true
        return prev
      }, {})

      return Object.keys(templateTriggersList).reduce((prev, key) => {
        if (!alreadyAddedTriggers[key]) {
          prev.push({
            label: templateTriggersList[key]?.title,
            value: key
          })
        }

        return prev
      }, [])
    },
    savedBusinessId() {
      return this.receivedData?.applications?.whatsapp?.waBussinessId
    },
    templateStatuses() {
      return [
        { label: 'All', value: '' },
        { label: 'Approved', value: 'APPROVED' },
        { label: 'Rejected', value: 'REJECTED' },
        { label: 'Pending', value: 'PENDING' }
      ]
    },
    availableTriggers() {
      return ['task_end', 'task_assign']
    },
    availableTemplateTriggers() {
      const availableTriggers = [
        {
          type: 'task_end',
          title: 'Task End',
          template: '',
          availableParams: ['assigned_to'],
          params: [],
          modified: false
        },
        {
          type: 'task_end_agent',
          title: 'Task End Agent',
          template: '',
          availableParams: ['assigned_to'],
          params: [],
          modified: false
        },
        {
          type: 'task_end_customer',
          title: 'Task End Customer',
          template: '',
          availableParams: ['assigned_to'],
          params: [],
          modified: false
        },
        {
          type: 'task_pending',
          title: 'Tasks Pending',
          template: '',
          availableParams: [
            'assigned_to',
            'total_pending_tasks',
            'total_in_progress_tasks',
            'total_completed_tasks'
          ],
          params: [],
          modified: false
        },
        {
          type: 'not_checked_in_yet',
          title: 'Not Checked In',
          template: '',
          availableParams: ['assigned_to'],
          params: [],
          modified: false
        },
        {
          type: 'not_started_any_task_yet',
          title: 'Not Started Any Task',
          template: '',
          availableParams: ['assigned_to'],
          params: [],
          modified: false
        },
        {
          type: 'task_assign',
          title: 'Task Assign',
          template: '',
          availableParams: ['task_title'],
          params: [],
          modified: false
        },
        {
          type: 'amc_notification',
          title: 'AMC Notification',
          template: '',
          availableParams: ['amc_data'],
          params: [],
          modified: false
        },
        {
          type: 'tele_caller_notification_agent',
          title: 'Call End Agent',
          template: '',
          availableParams: ['assigned_to'],
          params: [],
          modified: false
        },
        {
          type: 'tele_caller_notification_customer',
          title: 'Call End Customer',
          template: '',
          availableParams: ['assigned_to'],
          params: [],
          modified: false
        }
      ]

      return availableTriggers
    },
    filteredTemplates() {
      if (this.filters.search) {
        return this.availableTemplates.filter((t) => t.templateName === this.filters.search)
      }
      if (!this.filters.status) return this.availableTemplates

      return this.availableTemplates.filter((t) => t.status === this.filters.status)
    },
    availableTemplates() {
      return this.bindTemplateRes
    },
    appTemplates() {
      let currIndx = 0
      const appTemplates = this.availableTemplates.reduce((templates, template) => {
        if (template.triggerCategory === 'app_template')
          templates.push({ ...template, indx: currIndx })

        currIndx++
        return templates
      }, [])

      return appTemplates
    },
    approvedTemplates() {
      const savedTObj = this.bindTemplateRes.reduce((acc, curr) => {
        acc[curr.templateName] = curr
        return acc
      }, {})
      return this.whatsappTemplates.map((t) => {
        if (t.status === 'APPROVED') {
          const template = savedTObj[t.name]
          return { ...t, params: template?.params || [] }
        }
      })
    },
    availableTriggerTemplates() {
      return this.bindTemplateRes.filter(
        (template) => template.triggerType === this.newTemplateTrigger
      )
    },
    addTriggerBtn() {
      const addedTriggers = this.templateTriggers.reduce((prev, curr) => {
        prev[curr.type] = curr
        return prev
      }, {})
      const isAllTriggersAdded = Object.keys(addedTriggers).length === this.availableTriggers.length
      if (isAllTriggersAdded) return false

      return true
    }
  },
  methods: {
    maskNumber(num) {
      return maskPhoneNumber(num)
    },
    templateSwitch(t) {
      console.log(t)
    },
    onAddTemplate() {
      this.editingTemplate = null
      this.addTemplateDialogVisible = true
    },
    titleCase(str) {
      return convertToTitleCase(str)
    },
    async onAddBusinessId() {
      if (!this.waBussinessId) {
        this.$toast.error('Please add business id')
        return
      }

      this.addingBusinessId = true

      try {
        const payload = {
          applications: {
            ...this.receivedData.applications,
            whatsapp: {
              ...this.receivedData.applications.whatsapp,
              waBussinessId: this.waBussinessId
            }
          }
        }

        await this.$axios.post('/workforce/org/config', payload, {
          headers: {
            Authorization: this.authData.access_token
          }
        })

        this.loginWithFb()
      } catch (err) {
        console.log(err, 'err>>>>>>>>')
        this.$toast.error('Error adding business id')
      }

      this.isBusinessIdDialogVisible = false
      this.addingBusinessId = false
    },
    deleteFlow() {
      this.confirmDeleteBotFlow = false
    },
    deleteTemplate() {
      this.templateDeleteDialogVisible = false
    },
    onDisconnectFbAccount() {
      this.confirmDisconnectFbAccount = false
    },
    getStatusColor(status) {
      if (status === 'REJECTED') {
        return 'text-red-700'
      } else if (status === 'APPROVED' || status === 'ACTIVE') {
        return 'text-green-700'
      } else if (status === 'PENDING') {
        return 'text-orange-700'
      }
    },
    onAddAppTemplate() {
      this.addTemplateDialogVisible = true
      this.newTemplateTrigger = {}
      this.newTemplateTriggerCategory = {
        category: 'app_template',
        triggers: [],
        title: 'App Template'
      }
    },
    onAddTrigger() {
      this.addTemplateTriggerDialogVisible = true
    },
    onRefreshTemplates() {
      if (this.fetchingTemplates) return
      this.getBindedTemplates()
      this.fetchTemplates(true)
    },
    async handleDeleteTemplateTrigger(triggerType) {
      try {
        await this.$axios.delete(`/workforce/whatsapp/templates/assign/${triggerType}`, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.getBindedTemplates()
        this.$toast.success('Trigger removed successfully!')
      } catch {
        this.$toast.error('Error deleting trigger')
      }
    },
    async handleAddTemplateTrigger() {
      try {
        const payload = {
          templateType: this.newTemplateTrigger,
          templateName: this.newTemplateTriggerTemplate
        }
        this.addingTrigger = true

        await this.$axios.post(`/workforce/whatsapp/templates/assign`, payload, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        await this.getBindedTemplates()
        this.newTemplateTrigger = ''
        this.newTemplateTriggerTemplate = ''
        this.addTemplateTriggerDialogVisible = false
        this.$toast.success('Trigger added successfully!')
      } catch {
        this.$toast.error('Error adding trigger')
      }

      this.addingTrigger = false
    },
    displayDeleteDialog(i) {
      this.showDeleteTriggerDialog = !this.showDeleteTriggerDialog
      this.triggerDeleteIndex = i
    },
    async deleteTemplateTrigger() {
      let i = this.triggerDeleteIndex
      const trigger = this.templateTriggers[i]
      this.templateTriggers.splice(i, 1)
      try {
        await this.$axios.delete(`/workforce/whatsapp/templates/assign/${trigger.type}`, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.$toast.success('Trigger deleted successfully!')
        this.templateDeleteDialogVisible = false
        this.fetchTemplates()
      } catch {
        this.$toast.error('Error deleting trigger')
      }
      this.showDeleteTriggerDialog = false
    },
    async updateTemplateTrigger({ trigger, template, category }) {
      try {
        const payload = {
          category: category.category,
          templateType: trigger.type,
          params: template.params,
          languageCode: 'en',
          templateName: template.name,
          templateText: template.templateTxt,
          templateId: template.id
        }

        await this.$axios.post(`/workforce/whatsapp/templates/assign`, payload, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.getBindedTemplates()
        this.$toast.success('Updated successfully!')
      } catch (err) {
        this.$toast.error('Error updating config!')
      }
    },
    handleTemplateChange(i) {
      const prevData = this.bindTemplateRes.find(
        (t) => t.templateType === this.templateTriggers[i].type
      )
      this.templateTriggers[i].modified =
        prevData?.templateName !== this.templateTriggers[i].template
    },
    async deleteWhatsappTemplate() {
      // const template = this.whatsappTemplates[this.templateDeleteIndx]
      const template = this.bindTemplateRes[this.templateDeleteIndx]
      this.deletingTemplate = true
      try {
        await this.$axios.delete(`/workforce/whatsapp/templates/${template.templateName}`, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.$toast.success('Template deleted successfully!')
        this.templateDeleteDialogVisible = false
        this.templateDeleteIndx = null
        this.fetchTemplates()
      } catch {
        this.$toast.error('Error deleting template')
      }

      this.deletingTemplate = false
    },
    destructureTemplateBody(item) {
      const displayItem = { ...item }

      if (displayItem.params && displayItem.params?.length) {
        let formattedText = displayItem.templateText

        displayItem.params.forEach((p, i) => {
          formattedText = formattedText.replace(`{{${i + 1}}}`, `<strong>[ @${p} ]</strong>`)
        })
        return formattedText
      }
      return displayItem.templateText || ''
    },
    isRecentlyUpdated(template) {
      if (!template?.updatedAt) return false

      const updatedTime = this.$dayjs(template?.updatedAt)
      const currentTime = this.$dayjs()
      const hoursDiff = currentTime.diff(updatedTime, 'hour')

      return hoursDiff < 24
    },
    onTemplateEdit(template) {
      if (this.isRecentlyUpdated(template)) {
        this.$toast.warning(
          'This template was updated in the last 24 hours. Further updates may be delayed.'
        )
        return
      }

      this.editingTemplate = JSON.parse(JSON.stringify(template))
      this.addTemplateDialogVisible = true
    },
    onTemplateDelete(i) {
      this.templateDeleteDialogVisible = true
      this.templateDeleteIndx = i
    },
    async getBindedTemplates() {
      try {
        const { data } = await this.$axios.get('/workforce/whatsapp/templates/assign', {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.bindTemplateRes = data.templates || []
      } catch (err) {
        console.error('Error fetching assigned templates:', err)
        this.$toast.error('Error fetching template assignments')
      }
    },
    getTemplateParamOptions() {
      const fields = {
        task_end: [
          { label: 'Task End Time', value: 'task_end_time' },
          { label: 'Assigned To', value: 'assigned_to' }
        ],
        task_assign: [{ label: 'Task title', value: 'task_title' }],
        task_pending: [
          { label: 'Assigned To', value: 'assigned_to' },
          { label: 'Total pending tasks', value: 'total_pending_tasks' }
        ]
      }

      return fields[this.templateTriggers[this.templateParamsEditIndx].type]
    },
    onEditTemplateParams(i) {
      const t = this.templateTriggers[i]
      const template = this.whatsappTemplates.find((template) => template.name === t.template)
      if (!template) return []
      const bodyComponent = template.components.find((c) => c.type === 'BODY')
      const params = bodyComponent.text.match(/{{\d}}/g)
      this.editTemplateParamsVisible = true
      this.templateParams = params
      this.templateParamsEditIndx = i
    },
    templateHasParams(t) {
      const template = this.whatsappTemplates.find((template) => template.name === t.template)
      if (!template) return false
      const bodyComponent = template.components.find((c) => c.type === 'BODY')
      const hasParams = /{{\d}}/.test(bodyComponent.text)
      return hasParams
    },
    async addTemplateTrigger() {
      await this.handleAddTemplateTrigger()
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('ADDONS', feature)
    },
    refetchData() {
      this.$emit('refetchData')
    },
    onIframeLoaded() {
      if (this.botFlow) {
        this.sendMessageToBotFlow({
          type: 'LOAD_FLOW',
          payload: this.botFlow.flow
        })
      }
    },
    onEditFlow(flow) {
      this.showBotBuilderDialog = true
      this.botFlow = flow
      this.sendMessageToBotFlow({
        type: 'LOAD_FLOW',
        payload: this.botFlow.flow
      })
    },
    sendMessageToBotFlow(message) {
      const iframe = this.$refs.botFlow
      if (!iframe) return
      iframe.contentWindow.postMessage(message, 'https://bot-builder.myfinfi.com')
    },
    async fetchTemplates() {
      this.fetchingTemplates = true
      await this.$axios.get('/workforce/whatsapp/templates', {
        headers: {
          Authorization: this.authData.access_token
        }
      })
      this.getBindedTemplates()
      this.fetchingTemplates = false
    },
    async setActiveBotFlow(flow) {
      try {
        const payload = {
          applications: {
            ...this.receivedData.applications,
            whatsapp: {
              ...this.receivedData.applications.whatsapp,
              activeBotFlow: flow._id
            }
          }
        }

        await this.$axios.post('/workforce/org/config', payload, {
          headers: {
            Authorization: this.authData.access_token
          }
        })

        this.$toast.success('Activated bot flow successfully')
        // this.restartBotService()
        this.refetchData()
      } catch (err) {
        this.$toast.error('Error activating bot flow')
      }
    },
    onFlowDelete(flow) {
      if (flow._id === this.activeBotFlow) {
        return this.$toast.error('Cannot delete the active bot flow')
      }
      this.confirmDeleteBotFlow = true
    },
    async onFlowSaved(payload) {
      this.showBotBuilderDialog = false
      if (this.botFlow) {
        // await this.updateBotFlow(payload)
        this.botFlow = null
      } else {
        // await this.addBotFlow(payload)
      }
      this.refetchData()
      // this.restartBotService()
    },
    handleMessage(event, _this) {
      if (event.origin !== 'https://bot-builder.myfinfi.com') {
        return
      }

      const { type, payload } = event.data

      if (type === 'CANCEL') {
        _this.showBotBuilderDialog = false
      } else if (type === 'SAVE') {
        _this.onFlowSaved(payload)
      }
    },
    async handleDisconnectFacebookAccount() {
      try {
        await this.$axios.delete('/workforce/org/config/facebook-access-token', {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.botFlow = null
        // await this.restartBotService()
        this.$toast.success('Account successfully removed!')
        window.location.reload()
      } catch (err) {
        this.$toast.error('Error removing account')
      }
    },
    async deleteBotFlow() {
      try {
        await this.$axios.delete('/workforce/org/config/bot-flow/' + this.activeBotFlow, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.$toast.success('Bot flow deleted successfully')
        this.activeBotFlow = null
        this.confirmDeleteBotFlow = false
        this.refetchData()
        // this.restartBotService()
      } catch (err) {
        this.$toast.error('Error deleting bot flow')
      }
    },
    async updateBotFlow(payload) {
      try {
        await this.$axios.put('/workforce/org/config/bot-flow/' + this.botFlow._id, payload, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.$toast.success('Bot flow updated successfully')
        this.refetchData()
        // this.restartBotService()
      } catch (err) {
        this.$toast.error('Error updating bot flow')
      }
    },
    onLoginWithFb() {
      if (window.location.protocol !== 'https:') {
        this.$toast.error('Facebook login requires HTTPS. Please use a secure connection.')
        return
      }
      this.isBusinessIdDialogVisible = true
    },
    loginWithFb() {
      const _this = this
      this.isLoading = true

      if (!window.FB) {
        this.$toast.error('Facebook SDK not loaded properly. Please refresh the page.')
        this.isLoading = false
        return
      }

      window.FB.login(
        function (res) {
          if (res.authResponse?.code) {
            _this
              .saveFbTokenByCode(res.authResponse.code)
              .then(() => {
                // _this.restartBotService()
              })
              .catch((err) => {
                _this.isLoading = false
                _this.$toast.error('Error processing Facebook login', err)
              })
          } else {
            _this.isLoading = false
            _this.$toast.error('Error logging in with Facebook')
          }
        },
        {
          response_type: 'code',
          override_default_response_type: true,
          config_id: '439111278760748'
        }
      )
    },
    async addBotFlow(payload) {
      try {
        await this.$axios.post('/workforce/org/config/bot-flow', payload, {
          headers: {
            Authorization: this.authData.access_token
          }
        })
        this.$toast.success('Bot flow added successfully')
        this.refetchData()
        // this.restartBotService()
      } catch (err) {
        this.$toast.error('Error adding bot flow')
      }
    },
    async saveFbTokenByCode(code) {
      try {
        const response = await this.$axios.post(
          '/workforce/org/config/facebook-access-token',
          { code },
          {
            headers: {
              Authorization: this.authData.access_token
            }
          }
        )
        this.isLoading = false

        if (
          response.data.success &&
          response.data.orgConfigUpdated?.applications?.whatsapp?.waSystemToken
        ) {
          this.waSystemToken = response.data.orgConfigUpdated.applications.whatsapp.waSystemToken

          if (response.data.orgConfigUpdated?.applications?.whatsapp?.phoneNumbers) {
            this.businessNumbers = response.data.orgConfigUpdated.applications.whatsapp.phoneNumbers
          }
          this.$nextTick(async () => {
            if (response.data.orgConfigUpdated) {
              this.receivedData = {
                ...this.receivedData,
                applications: response.data.orgConfigUpdated.applications
              }
            }
            await this.getBindedTemplates()
            await this.fetchTemplates()

            this.$toast.success('Login successful!')
          })
        } else {
          this.$toast.error('Login failed. Please try again.')
        }
      } catch (err) {
        this.isLoading = false
        this.$toast.error('Error getting token')
      }
    },
    async restartBotService() {
      await this.$axios.post('https://bot.myfinfi.com/re-init-configs', {
        orgCode: this.orgCode
      })
    }
  },
  beforeDestroy() {
    window.removeEventListener('message', (e) => this.handleMessage(e, this))
  },
  watch: {
    bindTemplateRes(newVal) {
      const availableTriggers = this.availableTemplateTriggers

      const triggers = newVal.reduce((acc, curr) => {
        if (curr.templateType) {
          const trigger = availableTriggers.find((t) => t.type === curr.templateType)

          acc.push({
            ...trigger,
            triggerType: curr.triggerType,
            template: curr.templateName || '',
            params: curr.params || []
          })
        }

        return acc
      }, [])

      this.templateTriggerTypes = availableTriggers.reduce((acc, curr) => {
        if (!triggers.find((t) => t.type === curr.type)) {
          acc.push(curr)
        }

        return acc
      }, [])
      this.templateTriggers = triggers
    },
    savedBusinessId: {
      handler(id) {
        if (!id) return
        this.getBindedTemplates()
      },
      immediate: true
    },
    receivedData: {
      handler(newVal) {
        if (!newVal.applications) return
        this.waBusinessNumber = newVal.applications?.whatsapp?.waBusinessNumber
        this.waBussinessId = newVal.applications?.whatsapp?.waBussinessId
        this.waSystemToken = newVal.applications?.whatsapp?.waSystemToken
        this.businessNumbers = newVal.applications?.whatsapp?.phoneNumbers || []
        this.botFlows = newVal.applications?.whatsapp?.botFlows || []
        this.activeBotFlow = newVal.applications?.whatsapp?.activeBotFlow?._id
      },
      deep: true,
      immediate: true
    },
    waSystemToken: {
      handler(newVal) {
        if (newVal) {
          // Force UI update when token is received
          this.$nextTick(() => {
            this.getBindedTemplates()
            this.fetchTemplates()
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    window.addEventListener('message', (e) => this.handleMessage(e, this))
  }
}
</script>
<style></style>
