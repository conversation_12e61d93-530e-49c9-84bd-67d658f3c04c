<template>
  <div class="relative p-4">
    <div class="border border-blue-200 bg-white p-4 dark-bg-custom">
      <p class="font-semibold mb-0">
        Added phone numbers
      </p>
      <v-divider class="my-4"></v-divider>
      <div class="grid gap-3 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2">
        <div
          v-for="(phoneNumber, index) in phoneNumbers"
          :key="index"
          class="flex-col flex items-between gap-2 justify-start border shadow-sm py-2 px-3 relative"
        >
          <div class="flex flex-col gap-1">
            <p class="font-semibold text-gray-600 mb-0">
              <span class="text-blue-900">Username : </span>
              <span class="text-gray-900">{{ phoneNumber.userName }}</span>
            </p>
            <div label class="bg-gray-100 px-2 py-3 rounded-md">
              <p class="mb-0 text-sm text-gray-700">
                <span class="font-semibold text-blue-900">Virtual Number : </span>
                <span class="text-gray-900">{{ maskTeleCallerPhoneNumber(phoneNumber.phoneNumber) }}</span>
              </p>
            </div>
            
            <p class="text-gray-700 text-sm mb-0 mt-2">
              <strong>Created On:</strong>
              {{ $dayjs(phoneNumber.createdAt).format('DD-MM-YYYY hh:mm A') }}
            </p>
            <p class="text-gray-700 text-sm">
              <strong>Created By:</strong>
              Anonymous
            </p>
          </div>
          <p
            class="flex absolute top-3 right-3 font-semibold text-xs mb-0 pb-0.5 text-gray-700"
          >
            <span class="font-semibold text-blue-900">Provider : </span>
            <span class="text-gray-900">Vodafone</span>
            <v-img src="@/assets/img/vi-logo.png" class="mt-1 ml-1 w-3 h-2"></v-img>
            <v-menu offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-icon size="18" v-bind="attrs" v-on="on"> mdi-dots-vertical </v-icon>
              </template>
              <v-list>
                <v-list-item @click="onEditNumberClick(phoneNumber)" class="text-sm">
                  <v-list-item-content>
                    <v-list-item-title>Edit</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>

                <v-list-item @click="onDeleteNumberClick(phoneNumber)">
                  <v-list-item-content>
                    <v-list-item-title>Delete</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-menu>
          </p>
        </div>
      </div>
    </div>
    <div class="absolute right-4 -top-11">
      <v-btn
        @click="onAddNumberClick"
        :color="$vuetify.theme.currentTheme.primary"
        class="white--text"
      >
        Add Credential
      </v-btn>
    </div>
    <v-dialog v-model="addNumberDialogVisible" max-width="500px">
      <v-card :color="$vuetify.theme.currentTheme.navigationColor">
        <div class="flex flex-col">
          <div class="flex justify-between items-center mb-3">
            <v-card-title>
              Add New Credential
            </v-card-title>
            <v-btn plain @click="addNumberDialogVisible = false">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>
        </div>
        <v-card-text>
          <v-form ref="newNumberFieldsRef" lazy-validation
            :model="newNumberFields"
            :rules="newNumberFieldsRules"
          >
            <ProviderField />  
            <v-text-field
              v-model="newNumberFields.phoneNumber"
              label="Phone number"
              :rules="newNumberFieldsRules.phoneNumber"
              required
            />
            <v-text-field
              v-model="newNumberFields.userName"
              label="Username"
              :rules="newNumberFieldsRules.userName"
              required
            />
            <PasswordField 
              v-model="newNumberFields.passWord" 
              label="Password"
              :passwordRule="newNumberFieldsRules.passWord"
              :showPassword="this.showPassword"
              @update:showPassword="showPassword = $event" 
            />
            <PasswordField 
              v-model="newNumberFields.confirmPassword" 
              label="Confirm Password"
              :passwordRule="newNumberFieldsRules.confirmPassword"
              :showPassword="this.showConfirmPassword"
              @update:showPassword="showPassword = $event" 
            />
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="red"
            outlined
            @click="addNumberDialogVisible = false"
          >
            Cancel
          </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="addNumber"
            :loading="addingNumber"
          >
            Save
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="deleteDialogVisible"
      persistent
      width="380px"
    >
      <div class="bg-white dark-bg-custom p-4">
        <p class="text-lg font-bold">Do you want to delete this phone number?</p>
        <div class="flex items-center justify-end gap-2">
          <v-btn
            text
            color="red"
            outlined
            @click="deleteDialogVisible = false"
          >
            Cancel
          </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="deleteNumber(deleteDialogVisiblePhoneNumber)"
          >
            <span class="text-white"> OK </span>
          </v-btn>
        </div>
      </div>
    </v-dialog>
    <v-dialog
      v-model="editDialogVisible"
      persistent
      max-width="500px"
    >  
      <v-card :color="$vuetify.theme.currentTheme.navigationColor">
          <div class="flex flex-col">
            <div class="flex justify-between items-center mb-3">
              <v-card-title>Update Credentials</v-card-title>
              <v-btn plain class="p-4 text-lg" @click="onCancelEditNumberClick">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
          <v-card-text>
          <v-form ref="form"
            :model="newNumberFields"
            :rules="newNumberFieldsRules"
          >
            <ProviderField />  
            <v-text-field
              v-model="newNumberFields.userName"
              label="Username"
              :rules="newNumberFieldsRules.userName"
              required
            />
            <PasswordField 
              v-model="newNumberFields.passWord" 
              label="Password"
              :passwordRule="newNumberFieldsRules.passWord"
              :showPassword="this.showPassword"
              @update:showPassword="showPassword = $event" 
            />
            <PasswordField 
              v-model="newNumberFields.confirmPassword" 
              label="Confirm Password"
              :passwordRule="newNumberFieldsRules.confirmPassword"
              :showPassword="this.showConfirmPassword"
              @update:showPassword="showPassword = $event" 
            />
          </v-form>
          </v-card-text>
          <v-card-actions>
          <div class="flex gap-2 items-center justify-end">
            <v-btn color="red" outlined @click="onCancelEditNumberClick">
              <span> Cancel </span>
            </v-btn>
            <v-btn :color="$vuetify.theme.currentTheme.primary"
                class="white--text" :loading="editingNumber" @click="handleEditNumber">
              <span v-if="editDialogVisible" class="text-white"> Save </span>
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import PasswordField from "@/components/UI/PasswordField.vue";
import ProviderField from "./ProviderField.vue";
import { maskPhoneNumber } from "@/utils/common";
export default {
  props: {
    receivedData: {
      type: Object,
      default: null,
    },
  },
  components: {
    PasswordField,
    ProviderField
  },
  data() {
    return {
      phoneNumbers: [],
      addNumberDialogVisible: false,
      deleteDialogVisiblePhoneNumber: null,
      deleteDialogVisible: false,
      editingNumber: null,
      editDialogVisible: false,
      addingNumber: false,
      newNumberFields: {
        phoneNumber: "",
        userName: "",
        passWord: "",
        confirmPassword: "",
      },
      providers: [
        "Vodafone"
      ],
      showPassword: false,
      showConfirmPassword: false,
    };
  },
  mounted() {
    this.fetchAddedPhoneNumbers();
  },
  computed: {
    newNumberFieldsRules() {
      return {
        phoneNumber: [
          (v) => !!v || "Phone number is required",
        ],
        userName: [
          (v) => !!v || "Username is required",
        ],
        passWord: [
          (v) => !!v || "Password is required",
        ],
        confirmPassword: [
          (v) => !!v || "Confirm password is required",
          (v) => v === this.newNumberFields.passWord || "Passwords do not match",
        ],
      };
    },
  },
  methods: {
    async resetForm() {
      this.newNumberFields = {
        phoneNumber: "",
        userName: "",
        passWord: "",
        confirmPassword: "",
        showConfirmPassword: false,
        showPassword: false, 
      };
      this.$nextTick(() => {
        this.$refs.newNumberFieldsRef?.resetValidation();
      });
    },
    maskTeleCallerPhoneNumber(phoneNumber) {
      return maskPhoneNumber(phoneNumber);
    },
    onEditNumberClick(phoneNumber) {
      this.newNumberFields = { ...phoneNumber };
      this.newNumberFields.confirmPassword = this.newNumberFields.passWord;
      this.showPassword = false;
      this.showConfirmPassword = false;
      this.editDialogVisible = true;
    },
    onCancelEditNumberClick() {
      this.resetForm();
      this.editDialogVisible = false;
    },
    onDeleteNumberClick(phoneNumber) {
      this.deleteDialogVisiblePhoneNumber = this.phoneNumbers.find(
        (n) => n._id === phoneNumber._id
      );
      this.deleteDialogVisible = true;
    },
    onAddNumberClick() {
      this.resetForm();
      this.addNumberDialogVisible = true;
    },
    async handleEditNumber() {
      const isValid = this.$refs.form.validate();
      if (!isValid) {
        return;
      }
      this.editingNumber = true;
      try {
        const response = await this.$axios.put(
          `/workforce/tele-caller/credentials/${this.newNumberFields._id}`,
          {
            teleCaller: {
              phoneNumber: this.newNumberFields.phoneNumber,
              userName: this.newNumberFields.userName,
              passWord: this.newNumberFields.passWord,
            },
          },
        );
        this.phoneNumbers = this.phoneNumbers.map((n) => {
          if (n._id === response.data.teleCaller._id) {
            return response.data.teleCaller;
          }
          return n;
        });
        this.editDialogVisible = false;
        this.editingNumber = false;
      } catch (error) {
        const errorMessage = error.response.data.message || "Error editing phone number"
        this.$toast.error(errorMessage);
      }
    },
    async addNumber() {
      const isValid = this.$refs.newNumberFieldsRef.validate();
      if (!isValid) {
        return;
      }
      try {
        this.addingNumber = true;
        const response = await this.$axios.post(
          "/workforce/tele-caller/credentials",
          {
            teleCaller: {
              phoneNumber: this.newNumberFields.phoneNumber,
              userName: this.newNumberFields.userName,
              passWord: this.newNumberFields.passWord,
            }
          },
        );
        this.phoneNumbers.push(response.data.teleCaller);
        const successMessage = response?.data?.message || "Phone number added successfully"
        this.$toast.success(successMessage);

        this.resetForm();
      } catch (error) {
        this.$toast.error("Error adding phone number");
        const errorMessage = error.response.data.message || "Error adding phone number"
        this.$toast.error(errorMessage);
      }
      this.addNumberDialogVisible = false;
      this.addingNumber = false;
    },
    async deleteNumber(phoneNumber) {
      try {
        const response = await this.$axios.delete(
          `/workforce/tele-caller/credentials/${phoneNumber._id}`
        );
        const successMessage = response?.data?.message || "Phone number deleted successfully"
        this.$toast.success(successMessage);
        this.phoneNumbers = this.phoneNumbers.filter(
          (n) => n._id !== phoneNumber._id
        );
        this.deleteDialogVisiblePhoneNumber = null;
        this.deleteDialogVisible = false;
      } catch (error) {
        console.error("Error deleting phone number:", error);
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage);
      }
    },
    async fetchAddedPhoneNumbers() {
      try {
        const response = await this.$axios.get(
          "/workforce/tele-caller/credentials?orgId=DEVORG"
        );
        this.phoneNumbers = response.data.data;
      } catch (error) {
        console.error("Error fetching phone numbers:", error);
      }
    },
  },
};
</script>
