<template>
  <div>
    <!-- MailerLite Configuration Card -->
    <v-card class="mb-6" flat outlined>
      <v-card-title>
        <v-icon left color="primary">mdi-email-outline</v-icon>
        MailerLite Integration
        <v-spacer></v-spacer>
        <v-chip
          :color="isConnected ? 'success' : 'grey'"
          :text-color="isConnected ? 'white' : 'black'"
          small
        >
          {{ isConnected ? 'Connected' : 'Not Connected' }}
        </v-chip>
      </v-card-title>

      <v-card-text>
        <p class="text-gray-600 mb-4">
          Connect your MailerLite account to manage email campaigns, subscribers, and groups
          directly from your dashboard.
        </p>

        <v-row align="center">
          <v-col cols="12" md="8">
            <v-text-field
              v-model="tempToken"
              label="MailerLite API Token"
              placeholder="Enter your MailerLite API token"
              outlined
              dense
              :type="showToken ? 'text' : 'password'"
              :append-icon="showToken ? 'mdi-eye' : 'mdi-eye-off'"
              @click:append="showToken = !showToken"
              hint="Get your API token from MailerLite Dashboard > Integrations > API"
              persistent-hint
              :rules="tokenRules"
            ></v-text-field>
          </v-col>
          <v-col cols="12" md="4" class="mb-6">
            <v-btn
              color="primary"
              @click="saveToken"
              :disabled="!tempToken || !isValidToken"
              class="mr-2"
              :loading="loading.save"
            >
              <v-icon left>mdi-content-save</v-icon>
              Save Token
            </v-btn>
            <v-btn
              color="success"
              @click="testConnection"
              :disabled="!mailerLiteConfig.bearerToken"
              :loading="loading.test"
            >
              <v-icon left>mdi-connection</v-icon>
              Test
            </v-btn>
          </v-col>
        </v-row>

        <!-- Connection Status -->
        <v-alert
          v-if="mailerLiteConfig.bearerToken"
          :type="connectionStatus.type"
          dense
          text
          class="mt-2"
        >
          <div class="d-flex align-center">
            <v-icon left :color="connectionStatus.type">{{ connectionStatus.icon }}</v-icon>
            {{ connectionStatus.message }}
          </div>
        </v-alert>

        <!-- Integration Stats -->
        <div v-if="isConnected && stats" class="mt-4">
          <v-divider class="mb-4"></v-divider>
          <h4 class="text-md font-medium mb-3">Account Overview</h4>
          <v-row>
            <v-col cols="6" sm="3">
              <v-card outlined class="text-center pa-3">
                <div class="text-h6 primary--text">{{ stats.subscribers || 0 }}</div>
                <div class="text-caption">Subscribers</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card outlined class="text-center pa-3">
                <div class="text-h6">{{ stats.groups || 0 }}</div>
                <div class="text-caption">Groups</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card outlined class="text-center pa-3">
                <div class="text-h6">{{ stats.campaigns || 0 }}</div>
                <div class="text-caption">Campaigns</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card outlined class="text-center pa-3">
                <div class="text-h6">{{ formatDate(stats.lastSync) }}</div>
                <div class="text-caption">Last Sync</div>
              </v-card>
            </v-col>
          </v-row>
        </div>

        <!-- Actions -->
        <div v-if="isConnected" class="mt-4">
          <v-divider class="mb-4"></v-divider>
          <div class="d-flex gap-2">
            <v-btn outlined color="primary" @click="refreshStats" :loading="loading.refresh">
              <v-icon left>mdi-refresh</v-icon>
              Refresh Stats
            </v-btn>
            <v-btn outlined color="error" @click="confirmDisconnect">
              <v-icon left>mdi-link-off</v-icon>
              Disconnect
            </v-btn>
          </div>
        </div>

        <!-- Help Section -->
        <div class="mt-4">
          <v-divider class="mb-4"></v-divider>
          <h4 class="text-md font-medium mb-3">How to get your API Token</h4>
          <ol class="text-sm text-gray-600">
            <li>Log in to your MailerLite account</li>
            <li>Go to Integrations > Developer API</li>
            <li>Generate a new API token or copy an existing one</li>
            <li>Paste the token above and click "Save Token"</li>
          </ol>
          <v-btn
            text
            color="primary"
            href="https://dashboard.mailerlite.com/integrations/api"
            target="_blank"
            class="mt-2"
          >
            <v-icon left>mdi-open-in-new</v-icon>
            Open MailerLite API Settings
          </v-btn>
        </div>
      </v-card-text>
    </v-card>

    <!-- Confirmation Dialog -->
    <v-dialog v-model="confirmDialog.show" max-width="400" persistent>
      <v-card>
        <v-card-title class="text-h6">
          {{ confirmDialog.title }}
        </v-card-title>
        <v-card-text>
          {{ confirmDialog.message }}
        </v-card-text>
        <v-card-actions class="justify-end">
          <v-btn text @click="confirmDialog.show = false">Cancel</v-btn>
          <v-btn color="error" @click="disconnectIntegration" :loading="loading.disconnect">
            Disconnect
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'MailerLiteIntegration',

  data() {
    return {
      // MailerLite Configuration
      mailerLiteConfig: {
        apiUrl: 'https://connect.mailerlite.com/api',
        bearerToken: '',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json'
        }
      },

      // UI State
      tempToken: '',
      showToken: false,

      // Loading states
      loading: {
        save: false,
        test: false,
        refresh: false,
        disconnect: false
      },

      // Connection status
      connectionStatus: {
        type: 'success',
        icon: 'mdi-check-circle',
        message: 'API token configured successfully'
      },

      // Stats
      stats: null,

      // Confirmation dialog
      confirmDialog: {
        show: false,
        title: 'Disconnect MailerLite',
        message:
          'Are you sure you want to disconnect your MailerLite integration? This will remove your API token and you will need to reconfigure it to use email campaign features.'
      },

      // Validation rules
      tokenRules: [(v) => !v || v.length >= 10 || 'API token must be at least 10 characters']
    }
  },

  computed: {
    isConnected() {
      return !!this.mailerLiteConfig.bearerToken
    },

    isValidToken() {
      return this.tempToken && this.tempToken.length >= 10
    }
  },

  mounted() {
    this.loadStoredToken()
    if (this.isConnected) {
      this.refreshStats()
    }
  },

  methods: {
    loadStoredToken() {
      const storedToken = localStorage.getItem('mailerlite_token')
      if (storedToken) {
        this.setMailerLiteToken(storedToken)
      }
    },

    setMailerLiteToken(token) {
      this.mailerLiteConfig.bearerToken = token
      this.mailerLiteConfig.headers.Authorization = `Bearer ${token}`
    },

    async saveToken() {
      if (!this.isValidToken) {
        this.$toast.error('Please enter a valid API token')
        return
      }

      this.loading.save = true
      try {
        // Test the token first
        const testResponse = await axios.get(
          `${this.mailerLiteConfig.apiUrl}/subscribers?limit=0`,
          {
            headers: {
              ...this.mailerLiteConfig.headers,
              Authorization: `Bearer ${this.tempToken}`
            }
          }
        )

        // If test successful, save the token
        this.setMailerLiteToken(this.tempToken)
        localStorage.setItem('mailerlite_token', this.tempToken)

        this.connectionStatus = {
          type: 'success',
          icon: 'mdi-check-circle',
          message: 'API token saved and verified successfully!'
        }

        this.$toast.success('MailerLite integration configured successfully!')
        this.tempToken = ''

        // Refresh stats after successful connection
        await this.refreshStats()

        // Emit event to parent components
        this.$emit('integration-updated', {
          platform: 'mailerlite',
          connected: true,
          token: this.mailerLiteConfig.bearerToken
        })
      } catch (error) {
        console.error('Token validation failed:', error)
        this.connectionStatus = {
          type: 'error',
          icon: 'mdi-alert-circle',
          message: 'Invalid API token. Please check your token and try again.'
        }
        this.$toast.error('Invalid API token. Please check your token and try again.')
      } finally {
        this.loading.save = false
      }
    },

    async testConnection() {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please save your API token first')
        return
      }

      this.loading.test = true
      try {
        const response = await axios.get(`${this.mailerLiteConfig.apiUrl}/subscribers?limit=0`, {
          headers: this.mailerLiteConfig.headers
        })

        this.connectionStatus = {
          type: 'success',
          icon: 'mdi-check-circle',
          message: 'Connection successful! MailerLite API is working perfectly.'
        }

        this.$toast.success('Connection test successful!')

        // Refresh stats after successful test
        await this.refreshStats()
      } catch (error) {
        console.error('Connection test failed:', error)
        this.connectionStatus = {
          type: 'error',
          icon: 'mdi-alert-circle',
          message: 'Connection failed. Please check your API token and try again.'
        }
        this.$toast.error('Connection test failed. Please check your API token.')
      } finally {
        this.loading.test = false
      }
    },

    async refreshStats() {
      if (!this.mailerLiteConfig.bearerToken) return

      this.loading.refresh = true
      try {
        // Fetch basic stats from MailerLite
        const [subscribersResponse, groupsResponse, campaignsResponse] = await Promise.all([
          axios.get(`${this.mailerLiteConfig.apiUrl}/subscribers?limit=0`, {
            headers: this.mailerLiteConfig.headers
          }),
          axios.get(`${this.mailerLiteConfig.apiUrl}/groups`, {
            headers: this.mailerLiteConfig.headers
          }),
          axios.get(`${this.mailerLiteConfig.apiUrl}/campaigns`, {
            headers: this.mailerLiteConfig.headers
          })
        ])

        this.stats = {
          subscribers: subscribersResponse.data.total || 0,
          groups: groupsResponse.data.meta?.total || 0,
          campaigns: campaignsResponse.data.meta?.total || 0,
          lastSync: new Date().toISOString()
        }

        // Store stats in localStorage for persistence
        localStorage.setItem('mailerlite_stats', JSON.stringify(this.stats))
      } catch (error) {
        console.error('Failed to refresh stats:', error)
        // Try to load cached stats
        const cachedStats = localStorage.getItem('mailerlite_stats')
        if (cachedStats) {
          this.stats = JSON.parse(cachedStats)
        }
      } finally {
        this.loading.refresh = false
      }
    },

    confirmDisconnect() {
      this.confirmDialog.show = true
    },

    async disconnectIntegration() {
      this.loading.disconnect = true
      try {
        // Clear stored data
        localStorage.removeItem('mailerlite_token')
        localStorage.removeItem('mailerlite_stats')

        // Reset component state
        this.mailerLiteConfig.bearerToken = ''
        this.mailerLiteConfig.headers.Authorization = ''
        this.stats = null
        this.tempToken = ''

        this.connectionStatus = {
          type: 'info',
          icon: 'mdi-information',
          message: 'MailerLite integration has been disconnected.'
        }

        this.$toast.success('MailerLite integration disconnected successfully')

        // Emit event to parent components
        this.$emit('integration-updated', {
          platform: 'mailerlite',
          connected: false,
          token: null
        })
      } catch (error) {
        console.error('Error disconnecting:', error)
        this.$toast.error('Failed to disconnect integration')
      } finally {
        this.loading.disconnect = false
        this.confirmDialog.show = false
      }
    },

    formatDate(dateString) {
      if (!dateString) return 'Never'
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.gap-2 > * {
  margin-right: 8px;
}

.v-card.outlined {
  border: 1px solid #e0e0e0;
}

.text-gray-600 {
  color: #6b7280;
}
</style>
