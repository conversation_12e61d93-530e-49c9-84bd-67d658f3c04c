<template>
  <div class="bg-white rounded-xl shadow-md overflow-hidden">
    <div class="px-8">
      <v-form ref="form" v-model="valid" @submit.prevent="submitForm">
        <!-- Account Information -->
        <div class="mb-4">
          <h3 class="text-md font-semibold text-gray-800 mb-2">Account Information</h3>
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.keyId"
                label="Razorpay Key ID"
                outlined
                dense
                hide-details
                :rules="requiredRules"
                class="custom-input"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.keySecret"
                label="Razorpay Key Secret"
                type="password"
                outlined
                dense
                hide-details
                :rules="requiredRules"
                class="custom-input"
              ></v-text-field>
            </v-col>
          </v-row>
        </div>

        <!-- Payment Settings -->
        <div class="mb-4">
          <h3 class="text-md font-semibold text-gray-800 mb-2">Payment Settings</h3>
          <v-row>
            <v-col cols="12" md="6">
              <v-select
                v-model="form.defaultCurrency"
                :items="currencies"
                label="Default Currency"
                outlined
                dense
                hide-details
                :rules="requiredRules"
                class="custom-input"
              ></v-select>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model.number="form.paymentTimeout"
                label="Payment Timeout (minutes)"
                type="number"
                outlined
                dense
                hide-details
                :rules="timeoutRules"
                class="custom-input"
              ></v-text-field>
            </v-col>
          </v-row>
        </div>

        <!-- Webhook Configuration -->
        <div class="mb-4">
          <h3 class="text-md font-semibold text-gray-800 mb-2">Webhook Configuration</h3>
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="form.webhookUrl"
                label="Webhook URL"
                placeholder="https://yourcrm.com/razorpay-webhook"
                outlined
                dense
                hide-details
                :rules="urlRules"
                class="custom-input"
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="form.webhookSecret"
                label="Webhook Secret"
                type="password"
                outlined
                dense
                hide-details
                class="custom-input"
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-checkbox
                v-model="form.enableWebhooks"
                label="Enable Webhook Notifications"
                class="mt-0"
              ></v-checkbox>
            </v-col>
          </v-row>
        </div>

        <!-- Payment Methods -->
        <div class="mb-4">
          <h3 class="text-md font-semibold text-gray-800 mb-2">Payment Methods</h3>
          <v-row>
            <v-col v-for="method in paymentMethods" :key="method.value" cols="12" sm="6" md="4">
              <v-checkbox
                v-model="form.selectedPaymentMethods"
                :value="method.value"
                :label="method.text"
                class="mt-0"
                hide-details
              ></v-checkbox>
            </v-col>
          </v-row>
        </div>

        <!-- Additional Settings -->
        <div class="mb-4">
          <h3 class="text-md font-semibold text-gray-800 mb-2">Additional Settings</h3>
          <v-row>
            <v-col cols="12">
              <v-checkbox
                v-model="form.sendEmail"
                label="Send payment receipts to customers"
                class="mt-0"
                hide-details
              ></v-checkbox>
            </v-col>
            <v-col cols="12">
              <v-checkbox
                v-model="form.saveCards"
                label="Allow customers to save cards for future payments"
                class="mt-0"
                hide-details
              ></v-checkbox>
            </v-col>
            <v-col cols="12" md="6">
              <div class="mb-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Checkout Theme Color
                </label>
                <input
                  v-model="form.themeColor"
                  type="color"
                  class="h-10 w-20 rounded border border-gray-300 cursor-pointer"
                />
              </div>
            </v-col>
          </v-row>
        </div>

        <!-- Form Actions -->
        <div class="pt-6 mb-4 border-t border-gray-200">
          <div class="flex justify-end space-x-4">
            <v-btn outlined color="grey darken-1" @click="resetForm"> Cancel </v-btn>
            <v-btn type="submit" color="indigo darken-1" :disabled="!valid" :loading="loading">
              Save Configuration
            </v-btn>
          </div>
        </div>
      </v-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RazorpayConfigForm',
  data() {
    return {
      valid: false,
      loading: false,
      form: {
        keyId: '',
        keySecret: '',
        defaultCurrency: 'INR',
        paymentTimeout: 30,
        webhookUrl: '',
        webhookSecret: '',
        enableWebhooks: false,
        selectedPaymentMethods: ['card', 'netbanking', 'upi'],
        sendEmail: true,
        saveCards: false,
        themeColor: '#4f46e5'
      },
      currencies: [
        { text: 'Indian Rupee (INR)', value: 'INR' },
        { text: 'US Dollar (USD)', value: 'USD' },
        { text: 'Euro (EUR)', value: 'EUR' },
        { text: 'British Pound (GBP)', value: 'GBP' },
        { text: 'Australian Dollar (AUD)', value: 'AUD' }
      ],
      paymentMethods: [
        { text: 'Credit/Debit Cards', value: 'card' },
        { text: 'Net Banking', value: 'netbanking' },
        { text: 'UPI', value: 'upi' },
        { text: 'Wallets', value: 'wallet' },
        { text: 'EMI', value: 'emi' }
      ],
      requiredRules: [(v) => !!v || 'This field is required'],
      timeoutRules: [
        (v) => !!v || 'Payment timeout is required',
        (v) => (v >= 5 && v <= 60) || 'Timeout must be between 5 and 60 minutes'
      ],
      urlRules: [(v) => !v || /^https?:\/\/.+/.test(v) || 'Please enter a valid URL']
    }
  },
  methods: {
    async submitForm() {
      if (!this.$refs.form.validate()) {
        return
      }

      this.loading = true

      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 2000))

        console.log('Form submitted:', this.form)

        // Show success message
        this.$vuetify.theme.dark = false // Ensure we can see the snackbar
        this.$nextTick(() => {
          // You can emit an event or show a snackbar here
          alert('Configuration saved successfully!')
        })
      } catch (error) {
        console.error('Error saving configuration:', error)
        alert('Error saving configuration. Please try again.')
      } finally {
        this.loading = false
      }
    },

    resetForm() {
      this.$refs.form.reset()
      this.form = {
        keyId: '',
        keySecret: '',
        defaultCurrency: 'INR',
        paymentTimeout: 30,
        webhookUrl: '',
        webhookSecret: '',
        enableWebhooks: false,
        selectedPaymentMethods: ['card', 'netbanking', 'upi'],
        sendEmail: true,
        saveCards: false,
        themeColor: '#4f46e5'
      }
    }
  }
}
</script>

<style scoped>
/* Custom styles to blend Tailwind with Vuetify */
.custom-input >>> .v-input__control {
  min-height: 48px;
}

.custom-input >>> .v-input--is-focused .v-input__control .v-input__slot {
  border-color: #6366f1 !important;
}

.custom-input >>> .v-text-field--outlined fieldset {
  border-color: #d1d5db;
}

.custom-input >>> .v-text-field--outlined:hover fieldset {
  border-color: #9ca3af;
}

/* Checkbox styling */
>>> .v-input--checkbox .v-input__control {
  min-height: auto;
}

>>> .v-input--checkbox .v-input__slot {
  margin-bottom: 0;
}

/* Button styling adjustments */
>>> .v-btn {
  text-transform: none;
  font-weight: 500;
}
</style>
