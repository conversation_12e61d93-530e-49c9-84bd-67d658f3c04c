<template>
  <div class="px-2">
    <div class="p-5 overflow-auto max-h-[70vh]">
      <div>
        <div>
          <v-list class="w-full" v-for="(label, key, index) in use_cases" :key="key">
            <v-row class="items-center mb-2">
              <p class="text-md font-semibold mr-4">{{ index + 1 }}.</p>
              <p class="text-md font-semibold">{{ key.toUpperCase() }}</p>
            </v-row>
            <v-list-item-group>
              <v-row>
                <v-list-item class="space-x-4 border border-blue-200 mb-8">
                  <v-col cols="11">
                    <v-chip
                      v-for="(chip, index) in extractSubCases(key)"
                      :key="index"
                      label
                      small
                      class="border border-black mr-2 mb-1"
                    >
                      <span class="pr-2 text-black">
                        {{ chip.text }}
                      </span>

                      <v-icon
                        v-if="canEditOrDeleteSubCase(key, chip)"
                        small
                        @click="removeSubCaseChip(chip, index, key, label)"
                        color="red"
                        class="ml-2"
                        v-tooltip="{
                          text: 'Delete'
                        }"
                      >
                        $delete
                      </v-icon>

                      <v-icon
                        v-if="canEditOrDeleteSubCase(key, chip)"
                        small
                        @click="editItem(chip, label, index, key)"
                        color="green"
                        class="ml-2"
                        v-tooltip="{
                          text: 'Edit'
                        }"
                      >
                        $edit
                      </v-icon>

                      <!-- <v-icon
                        v-if="canEditOrDeleteSubCase(key, chip)"
                        small
                        @click="cloneSubCaseChip(chip, index, key)"
                        color="blue"
                        class="ml-2"
                        v-tooltip="{
                          text: 'Clone'
                        }"
                      >
                        mdi-content-copy
                      </v-icon> -->
                      <v-icon
                        v-else
                        small
                        @click="
                          $toast.error('You dont have suffiecient permissions to view this subcase')
                        "
                        color="grey"
                        class="ml-2"
                        v-tooltip="{
                          text: 'You dont have suffiecient permissions to view this subcase'
                        }"
                      >
                        mdi-lock
                      </v-icon>
                    </v-chip>
                  </v-col>

                  <v-col cols="1">
                    <div class="flex justify-end">
                      <v-btn
                        text
                        :color="$vuetify.theme.currentTheme.primary"
                        @click="handleOrgMapping(key, label)"
                        >Add new subcase</v-btn
                      >
                    </div>
                  </v-col>
                </v-list-item>
              </v-row>
            </v-list-item-group>
          </v-list>
        </div>
      </div>
    </div>
    <v-dialog v-model="openOrgMapping" max-width="63rem" persistent>
      <OrgMapping
        :useCase="mappedUseCase"
        :useCaseLabel="mappedUseCaseLabel"
        :baseMapping="baseCaseMapping"
        @cancel="onCancelOrgMapping"
        @orgMapKeys="handleMapKeys"
        @orgSubCaseMapKeys="handleSubCaseMapKeys"
        :enabledSubCases="enabledSubCases"
        :editSubCase="editSubCase"
        :cloneSubCase="cloneSubCase"
        :isWhatsAppEnabled="!!receivedData?.applications?.whatsapp?.waSystemToken"
        :allowedQuestionsTypes="receivedData?.allowedQuestionTypes"
        :allowedQuestionsTypesCustomerFeedback="receivedData?.allowedQuestionTypesCustomerFeedback"
        :showMappingDetails="mappedUseCase?.toLowerCase() === 'collection'"
      />
    </v-dialog>
    <AlertPopUp
      :showConfirmationDialog="showDeleteSubCaseDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="'Are you sure you want to delete the case?'"
      :confirmationActionText="'DELETE'"
      :confirmationActionColor="'red'"
      :performAction="deleteSubCase"
      :cancel="cancel"
    />
  </div>
</template>
<script>
import OrgMapping from '../OrgMapping'
import AlertPopUp from '@/components/AlertPopUp'
import { convertToTitleCase } from '@/utils/common'
import { cloneDeep } from 'lodash'
// import CustomSwitch from './CustomSwitch.vue'
const defaultOrgKeys = [
  {
    text: 'Case No (Unique Identifier)',
    key: 'caseNo',
    value: '',
    visibility: true,
    rules: [(v) => !!v || 'Case No is required'],
    required: true
  },
  {
    text: 'Type of loan',
    key: 'typeOfLoan',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'Bucket',
    key: 'bkt',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'Product Info',
    key: 'productInfo',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'POS (Number)',
    key: 'pos',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'EMI Amount (Number)',
    key: 'emiAmount',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'Tenure (Number)',
    key: 'tenure',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'Task Title',
    key: 'taskTitle',
    value: '',
    visibility: true,
    rules: [(v) => !!v || 'Task Title is required'],
    required: true
  },
  {
    text: 'Task Description',
    key: 'taskDescription',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'Assigned Person',
    key: 'assignedTo',
    value: '',
    visibility: true,
    required: false
  },
  {
    text: 'EMI Start Date (Date)',
    key: 'emiStartDate',
    value: '',
    visibility: false,
    required: false
  },
  {
    text: 'EMI End Date (Date)',
    key: 'emiEndDate',
    value: '',
    visibility: false,
    required: false
  },
  {
    text: 'Next EMI Due Date (Date)',
    key: 'nextEmiDate',
    value: '',
    visibility: false,
    required: false
  }
]
export default {
  data() {
    return {
      use_cases: {},
      selectedUseCases: [],
      enabledSubCases: [],
      selectedChip: null,
      showDeleteSubCaseDialog: false,
      openOrgMapping: false,
      mappedUseCase: '',
      mappedUseCaseLabel: '',
      editSubCase: null,
      cloneSubCase: null,
      qrCode: '',
      orgData: this.$storage.getUniversal('orgData'),
      orgCode: this.$storage.getUniversal('organization_code'),
      userOrg: this.$storage.getUniversal('user'),
      qrValue: `${process.env.VUE_APP_BASE_URL}/mark-attendance?orgCode=${this.orgCode}`,
      drawer: false,
      useCasesMapping: {},
      baseCaseMapping: {},
      allSubCases: []
    }
  },
  components: {
    OrgMapping,
    AlertPopUp
    // CustomSwitch
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    }
  },
  watch: {
    receivedData(newVal) {
      if (newVal !== null) {
        this.populateFields(newVal)
      }
    }
  },
  methods: {
    async fetchAllUseCases() {
      try {
        const res = await this.$axios.get(`/workforce/orgSetup/extend-usecase`)
        this.allSubCases = res.data?.data
      } catch (error) {
        console.log(error)
      }
    },
    async fetchStaticUseCaseData() {
      try {
        const response = await this.$axios.get(
          '/workforce/orgSetup/loan-management-field-mapping/data'
        )
        const { base, ...remainingUseCases } = response.data?.data
        this.useCasesMapping = remainingUseCases
        this.baseCaseMapping = base

        this.use_cases = { base, ...this.useCasesMapping }
        this.populateFields(this.receivedData)
      } catch (error) {
        console.error('Error fetching static loan data:', error)
      }
    },
    convertTitle(str) {
      return convertToTitleCase(str)
    },
    extractSubCases(useCaseKey) {
      const subCasesForUseCase = this.allSubCases.filter(
        (subCase) => subCase.useCase === useCaseKey.toLowerCase()
      )

      const allSubCases = subCasesForUseCase.map((subCase) => {
        return { text: subCase.name, value: subCase._id, source: 'api' }
      })
      const enabledSubCasesForUseCase = this.enabledSubCases?.find(
        (sub) => sub.useCase === useCaseKey.toLowerCase()
      )

      if (!enabledSubCasesForUseCase || !enabledSubCasesForUseCase.subCase) {
        return allSubCases
      }
      const enabledSubCasesArray = enabledSubCasesForUseCase.subCase.map((sub) => {
        return {
          text: sub.name,
          value: sub._id || `enabled-${sub.name}`,
          source: 'enabled',
          branch: sub.branch
        }
      })

      const combinedSubCases = [...enabledSubCasesArray]
      allSubCases.forEach((apiSubCase) => {
        if (!combinedSubCases.some((enabledSubCase) => enabledSubCase.text === apiSubCase.text)) {
          combinedSubCases.push(apiSubCase)
        }
      })
      return combinedSubCases
    },
    canEditOrDeleteSubCase(useCaseKey, subCaseName) {
      return true
      const { branch, role } = this.userOrg || {}
      const enabledSubCasesForUseCase = this.enabledSubCases?.find(
        (sub) => sub.useCase === useCaseKey.toLowerCase()
      )

      if (role?.includes('WFM_ADMIN') || !branch) {
        return true
      }

      const matchingSubCase = enabledSubCasesForUseCase?.subCase.find(
        (sub) => sub.name === subCaseName && sub.branch === branch
      )

      return !!matchingSubCase
    },
    removeSubCaseChip(chipName, index, key) {
      if (key === 'COLLECTION') {
        const enabledSubCasesForUseCase = this.enabledSubCases?.find(
          (sub) => sub.useCase === key.toLowerCase()
        )
        enabledSubCasesForUseCase.subCase.splice(index, 1)
        this.$emit('subCases', this.enabledSubCases)
        return
      }
      this.selectedChip = chipName
      this.showDeleteSubCaseDialog = true
      this.$emit('subCases', this.enabledSubCases)
    },
    cloneSubCaseChip(chipName, indx, key) {
      for (let i = 0; i < this.enabledSubCases.length; i++) {
        const subCase = this.enabledSubCases[i]
        const selectedSubCase = subCase.subCase.find((item) => item.name === chipName)

        if (selectedSubCase) {
          this.cloneSubCase = cloneDeep(selectedSubCase)
          this.cloneSubCase.cloneIndex = indx
        }
      }

      this.openOrgMapping = true
      this.mappedUseCase = key
    },
    async deleteSubCase() {
      try {
        if (!this.selectedChip) {
          this.$toast.error('No case selected for deletion')
          return
        }

        // If we have a direct ID reference, use it
        if (this.selectedChip.value) {
          // Delete from extend-usecase API
          await this.$axios.delete(`/workforce/orgSetup/extend-usecase/${this.selectedChip.value}`)

          // Check if there's an associated loan management mapping to delete
          const useCaseData = await this.$axios.get(
            `/workforce/orgSetup/extend-usecase/${this.selectedChip.value}`
          )
          const loanMappingId = useCaseData?.data?.data?.loanManagementFieldMapping

          if (loanMappingId) {
            await this.$axios.delete(
              `/workforce/orgSetup/loan-management-field-mapping/${loanMappingId}`
            )
          }

          // Refresh the list after deletion
          await this.fetchAllUseCases()
          this.$toast.success('Subcase deleted successfully')
          this.showDeleteSubCaseDialog = false
          this.selectedChip = null

          // Emit updated list to parent
          this.$emit('subCases', await this.getUpdatedSubCasesList())
          return
        }

        // If we don't have a direct ID, search by name
        const subcaseName = this.selectedChip.text || this.selectedChip

        // Find the subcase in the allSubCases list
        const subcaseToDelete = this.allSubCases.find((subcase) => subcase.name === subcaseName)

        if (subcaseToDelete) {
          // Delete from extend-usecase API
          await this.$axios.delete(`/workforce/orgSetup/extend-usecase/${subcaseToDelete._id}`)

          // Delete from loan-management API if needed
          if (subcaseToDelete.loanManagementFieldMapping) {
            await this.$axios.delete(
              `/workforce/orgSetup/loan-management-field-mapping/${subcaseToDelete.loanManagementFieldMapping}`
            )
          }

          // Refresh the list after deletion
          await this.fetchAllUseCases()
          this.$toast.success('Subcase deleted successfully')
        } else {
          this.$toast.error('Could not find the selected subcase')
        }

        this.showDeleteSubCaseDialog = false
        this.selectedChip = null

        // Emit updated list to parent
        this.$emit('subCases', await this.getUpdatedSubCasesList())
      } catch (error) {
        console.error('Error deleting subcase:', error)
        this.$toast.error(error.response?.data?.message || 'Failed to delete subcase')
      }
    },
    async editItem(chipName, label, indx, key) {
      this.fetchStaticUseCaseData()
      this.openOrgMapping = true
      this.mappedUseCase = key
      this.mappedUseCaseLabel = label
      if (key === 'COLLECTION') {
        console.log('lll')
        for (let i = 0; i < this.enabledSubCases.length; i++) {
          const subCase = this.enabledSubCases[i]
          const selectedSubCase = subCase.subCase.find((item) => item.name === chipName.text)

          // Creating a deep copy of selectedSubCase using Lodash
          if (selectedSubCase) {
            this.editSubCase = { ...cloneDeep(selectedSubCase), key }
            this.editSubCase.indx = indx
          }
        }
        this.mappedUseCaseLabel = defaultOrgKeys
        this.openOrgMapping = true
        this.mappedUseCase = key
        this.$emit('subCases', this.enabledSubCases)
      } else {
        await this.fetchUseCaseById(chipName.value)
      }
    },
    async fetchUseCaseById(id) {
      try {
        const res = await this.$axios.get(`/workforce/orgSetup/extend-usecase/${id}`)
        this.editSubCase = res.data?.data
      } catch (error) {
        console.log(error)
      }
    },
    cancel() {
      this.showDeleteSubCaseDialog = false
      this.showdDeleteAllDialog = false
      this.selectedChip = null
    },
    handleOrgMapping(key, label) {
      this.fetchStaticUseCaseData()
      this.mappedUseCase = key
      this.mappedUseCaseLabel = label
      if (key === 'COLLECTION') {
        this.mappedUseCaseLabel = defaultOrgKeys
      }
      this.editSubCase = null
      this.openOrgMapping = true
    },
    onCancelOrgMapping() {
      this.openOrgMapping = false
      this.cloneSubCase = {}
      this.editSubCase = {}
    },
    handleSwitch(val) {
      if (val?.id) {
        this.$emit('customerEncrypt', val.value)
      }
    },
    async handleMapKeys(keys) {
      this.onCancelOrgMapping()
      this.cloneSubCase = {}
      this.editSubCase = {}
      await this.fetchAllUseCases()
    },
    async handleSubCaseMapKeys(keys) {
      this.onCancelOrgMapping()
      this.cloneSubCase = {}
      this.editSubCase = {}
      this.enabledSubCases = keys
      this.$emit('subCases', this.enabledSubCases)
    },
    populateFields(val) {
      this.selectedUseCases = val?.enabledUseCases?.map((el) => el.toUpperCase() || [])
      this.selectedUseCases?.forEach((el) => {
        this.use_cases[el.toUpperCase()] = el.toLowerCase()
      })
      // console.log(this.use_cases, '>>>>>>')
      this.enabledSubCases = val?.enabledSubCases
    },
    toggleShowAll() {
      this.drawer = !this.drawer
    }
  },
  async mounted() {
    this.populateFields(this.receivedData)
    this.fetchAllUseCases()
    this.fetchStaticUseCaseData()
  },
  computed: {
    is_valid_from() {
      return this.$dayjs(this.orgData?.valid_from).format('MMMM D, YYYY')
    },
    is_valid_to() {
      return this.$dayjs(this.orgData?.valid_to).format('MMMM D, YYYY')
    },
    limitations() {
      return this.orgData?.plan_limitations
    },
    planDetails() {
      return this.orgData?.plan_details
    }
  }
}
</script>
<style scoped>
.status_color {
  color: #33cc33;
}
.chipContainer {
  display: flex;
  flex-wrap: wrap;
}
</style>
