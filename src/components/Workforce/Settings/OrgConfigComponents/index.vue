<template>
  <div>
    <v-tabs
      v-model="activeTab"
      background-color="transparent"
      show-arrows
      vertical
      left
      @change="handleTabChange"
      :color="$vuetify.theme.currentTheme.primary"
    >
      <v-tab
        :ripple="false"
        v-for="tab in tabs"
        :key="tab.name"
        class="tabsClass w-full"
        :color="$vuetify.theme.currentTheme.primary"
      >
        {{ tab.label }}
      </v-tab>

      <v-tab-item v-for="tab in tabs" :key="tab.name" class="">
        <div v-if="shouldShowHeader(tab.name)" class="my-2 mx-5 flex justify-between fixed-bottom">
          <p class="font-semibold mb-0">{{ tab.label?.toUpperCase() }}</p>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="handleUpdateClick(tab)"
            :disabled="disableUpdateButton"
            v-show="shouldShowUpdateButton(tab.name)"
          >
            Update
          </v-btn>
        </div>
        <component
          :is="tab.component"
          :receivedData="receivedData"
          :orgData="orgData"
          :branches="branches"
          :errors="errors"
          @refetchData="refetchData"
          v-on="getEventListeners(tab)"
          v-bind="getProps(tab)"
          :ref="`component-${tab.name}`"
        />
      </v-tab-item>
    </v-tabs>
    <AlertPopUp
      :showConfirmationDialog="showReloadDialog"
      :confirmationTitle="''"
      :confirmationMessage="'Please Reload to update your config changes'"
      :confirmationActionText="'Reload'"
      :confirmationActionColor="'red'"
      :performAction="ReloadFunc"
      :cancel="handleReloadDialog"
      :loading="loading"
    />
  </div>
</template>

<script>
import { convertToTitleCase } from '@/utils/common'
import TaskCompletionSteps from './TaskCompletionSteps.vue'
import Questionnaire from './Questionnaire.vue'
import AttendanceSetting from './AttendanceSetting.vue'
import CustomSwitch from './CustomSwitch.vue'
import CustomComboBox from './CustomComboBox.vue'
// import NotificationIntegration from './NotificationIntegration/index.vue'
import PlatformIntegrations from './PlatformIntegrations/index.vue'
import ExpenseSettings from './ExpenseSettings.vue'
import EnabledUseCase from './EnabledUseCase.vue'
import AlertPopUp from '@/components/AlertPopUp'
import LeaveSettings from './LeaveSettings.vue'
import BranchGroup from './BranchGroup.vue'
import LanguageConfig from './LanguageConfiguration.vue'
import {
  hasPermission,
  isAnyFeatureAllowed,
  isModuleFeatureAllowed,
  isModuleVisible
} from '@/utils/common'
import permissionData from '@/utils/permissions'
import SalaryConfig from './SalaryConfig.vue'
import OrgLicence from './OrgLicence.vue'

export default {
  name: 'platformComponent',
  data() {
    return {
      organizationData: this.$storage.getUniversal('orgData'),
      activeTab: 0,
      userData: this.$store.state.wfmPermissions,
      hasConfigChanges: false,
      isDefault: false,
      notify: {
        SMS: 'SMS',
        EMAIL: 'E-Mail',
        INAPP: 'In-App Message'
      },
      customList: [],
      questionnaire: [],
      isAnyChangeInQuestions: false,
      otpVerification: this.receivedData?.customerOTPVerification,
      isValueChanged: false,
      showReloadDialog: false,
      proofType: [],
      meetingOutcomes: [],
      attendanceData: {},
      attendanceDataFields: null,
      attendanceStartDataTime: '',
      attendanceEndDataTime: '',
      updateWorkHour: null,
      attendanceTimeThreshold: null,
      enabledSubCases: [],
      orgLeaves: {},
      isAutoReschedule: this.receivedData?.isAutoReschedule,
      travelData: this.receivedData?.travelModes,
      allowanceData: this.receivedData?.orgAllowances,
      notificationSetting: [],
      selectedNotification: {},
      listOfHolidays: [],
      shifts: [],
      branches: [{ branchName: '', branchDescription: '', disabled: true }],
      docsData: {},
      geoFenceData: null,
      enableBranchMapping: null,
      branchFenceValue: null,
      allBranchCheckIn: null,
      homeGeofenceRadius: null,
      clockInWithQrCode: false,
      isCustomerDataEncrypted: false,
      clockOutWithQrCode: false,
      loading: false,
      errors: {}
    }
  },
  components: {
    TaskCompletionSteps,
    Questionnaire,
    CustomSwitch,
    CustomComboBox,
    AttendanceSetting,
    PlatformIntegrations,
    ExpenseSettings,
    EnabledUseCase,
    AlertPopUp,
    LeaveSettings,
    BranchGroup,
    SalaryConfig,
    // NotificationIntegration,
    LanguageConfig,
    OrgLicence
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    },
    orgData: Object
  },
  watch: {
    receivedData: {
      handler(newVal) {
        if (newVal !== null) {
          this.receivedData = newVal
        }
      },
      deep: true,
      immediate: true
    },
    activeTab: {
      handler(newVal) {
        if (newVal) {
          const configSetting = this.$route?.query?.config_tab
          if (configSetting) {
            this.activeTab = this.tabs.findIndex((tab) => tab.name === configSetting)
            if (newVal !== this.activeTab) {
              this.$router.replace({ query: null })
            }
          }
        }
      }
    }
  },
  mounted() {
    const currentSubTab = this.$route.query.subActiveTab
    const tabIndex = this.tabs.findIndex((tab) => tab.name === currentSubTab)

    if (tabIndex >= 0) {
      this.activeTab = tabIndex
    } else {
      this.activeTab = 0
      this.handleTabChange(this.activeTab)
    }
    this.isValueChanged = false
    this.fetchBranches()
    const configSetting = this.$route?.query?.config_tab
    const savedTab = localStorage.getItem('settingActiveTab')
    this.activeTab = 0
    if (savedTab) {
      this.activeTab = parseInt(savedTab)
      localStorage.removeItem('settingActiveTab')
    }
    if (configSetting) {
      this.activeTab = this.tabs.findIndex((tab) => tab.name === configSetting)
    }
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.settingEdit)
    },
    disableUpdateButton() {
      return !(this.isValueChanged || this.hasConfigChanges) || this.checkedCustomCount < 1
    },
    settingLanguage() {
      const Cases = this.receivedData?.enabledUseCases
      if (Cases && Cases.includes('inventory') && Cases.length > 1) {
        return 'Task/Complaint'
      } else if (Cases && Cases.includes('inventory')) {
        return 'Complaint'
      } else {
        return 'Task'
      }
    },
    tabs() {
      const allTabs = [
        {
          name: 'enabledUseCase',
          label: 'UseCase Setup',
          component: 'EnabledUseCase',
          visible: true
        },
        {
          name: 'taskCompletionSteps',
          label: 'ToDo Settings',
          component: 'TaskCompletionSteps',
          visible: isAnyFeatureAllowed('TASK_MANAGEMENT')
        },
        // {
        //   name: 'questionnaire',
        //   label: 'Questionnaire',
        //   component: 'Questionnaire',
        //   visible: isModuleFeatureAllowed('TASK_MANAGEMENT', 'ENABLE_QUESTIONNAIRE')
        // },
        {
          name: 'attendanceSetting',
          label: 'Attendance',
          component: 'AttendanceSetting',
          visible: isAnyFeatureAllowed('ATTENDANCE_MANAGEMENT') && isModuleVisible('ATTENDANCE')
        },
        {
          name: 'leaveSettings',
          label: 'Leaves & Holidays',
          component: 'LeaveSettings',
          visible: isAnyFeatureAllowed('LEAVE_MANAGEMENT') && isModuleVisible('EMPLOYEES')
        },
        {
          name: 'expenseSettings',
          label: 'Expense Settings',
          component: 'ExpenseSettings',
          visible: isAnyFeatureAllowed('CLAIM_MANAGEMENT') && isModuleVisible('CLAIMS')
        },
        {
          name: 'branchGroup',
          label: 'Branch & Team',
          component: 'BranchGroup',
          visible: isAnyFeatureAllowed('BRANCH_MANAGEMENT')
        },
        {
          name: 'platformIntegrations',
          label: 'Platform Integrations',
          component: 'PlatformIntegrations',
          visible: isAnyFeatureAllowed('ADDONS')
        },
        // {
        //   name: 'notificationIntegration',
        //   label: 'Notification Templates',
        //   component: 'NotificationIntegration',
        //   visible:
        //     isAnyFeatureAllowed('NOTIFICATION_MANAGEMENT') && isModuleVisible('NOTIFICATIONS')
        // },
        {
          name: 'salaryConfig',
          label: 'Salary Configuration',
          component: 'SalaryConfig',
          visible: isAnyFeatureAllowed('SALARY_MANAGEMENT') && isModuleVisible('EMPLOYEES')
        },

        {
          name: 'orgLicense',
          label: 'License/Plan Info',
          component: 'OrgLicence',
          visible: true
        },
        {
          name: 'languageConfig',
          label: 'Language Configuration',
          component: 'LanguageConfig',
          visible: true
        }
      ]
      return allTabs.filter((tab) => tab.visible)
    }
  },
  methods: {
    handleTabChange(tabKey) {
      localStorage.setItem('settingActiveTab', tabKey)
      const newSubTab = this.tabs[tabKey]?.name
      if (this.$route.query.subActiveTab !== newSubTab) {
        this.$router
          .replace({
            query: {
              ...this.$route.query,
              subActiveTab: newSubTab
            }
          })
          .catch((err) => {
            if (err.name !== 'NavigationDuplicated') throw err
          })
      }
    },

    shouldShowHeader(tabName) {
      const noHeaderTabs = [
        'platformIntegrations',
        'orgLicense',
        'openQuestionnaire',
        'languageConfig'
      ]
      return !noHeaderTabs.includes(tabName)
    },

    shouldShowUpdateButton(tabName) {
      const noUpdateButtonTabs = [
        'salaryConfig',
        'orgLicense',
        'notificationIntegration',
        'openQuestionnaire',
        'languageConfig'
      ]
      return this.canEdit && !noUpdateButtonTabs.includes(tabName)
    },
    handleUpdateClick(tab) {
      const componentMethodMap = {
        attendanceSetting: 'saveAttendanceSettings',
        taskCompletionSteps: 'saveTaskCompletionSteps'
        // Add more mappings as needed for other components
        // 'anotherTabName': 'anotherMethodName',
      }

      // Get the method name for this tab, or default to updateConfig
      const methodName = componentMethodMap[tab.name]
      console.log('Method Name:', methodName)

      if (methodName) {
        // Call the specific component method
        const componentRef = this.$refs[`component-${tab.name}`]
        if (componentRef && componentRef[0] && typeof componentRef[0][methodName] === 'function') {
          componentRef[0][methodName]()
          this.hasConfigChanges = false // Reset the flag after saving
        }
      } else {
        // Default to updateConfig for tabs without specific methods
        this.updateConfig()
      }
    },

    getDisabledState(tab) {
      // Special cases for different tabs
      // if (tab.name === 'openQuestionnaire') {
      //   return !this.hasConfigChanges
      // }

      // Default case
      return this.disableUpdateButton
    },

    refetchData() {
      this.$emit('refetchData')
    },
    async updateConfig() {
      try {
        let taskCompletionSteps = [],
          type = 'DEFAULT',
          taskCompletionStepsConfig = []
        if (!this.isDefault) {
          taskCompletionSteps = this.customList
            .filter((item) => item.value.checked)
            .map((item) => item.key)
          taskCompletionStepsConfig = this.customList
            .filter((item) => item.value.checked)
            .map((item) => ({ step: item.key, canSkip: item.value.canSkip }))
          type = 'CUSTOM'
        } else {
          this.customList.forEach((item) => {
            item.value.checked = false
          })
        }

        const requestBody = {
          customerOTPVerification: this.otpVerification,
          isAutoReschedule: this.isAutoReschedule,
          encryptCustomerDetails: this.isCustomerDataEncrypted
        }
        if (taskCompletionSteps.length) {
          requestBody.taskCompletionStepsConfig = taskCompletionStepsConfig
          requestBody.taskCompletionSteps = taskCompletionSteps
          requestBody.type = type
        }
        if (this.questionnaire.length || this.isAnyChangeInQuestions) {
          requestBody.questionnaire = this.questionnaire
        }
        if (this.meetingOutcomes.length) {
          requestBody.allowedMeetingOutcomesTypes = this.meetingOutcomes
        }
        if (this.proofType.length) {
          requestBody.allowedProofTypes = this.proofType
        }
        if (this.attendanceData) {
          requestBody.shiftStartTime = this.attendanceData?.starttime
          requestBody.shiftEndTime = this.attendanceData?.endtime
          requestBody.workingDays = this.attendanceData?.days
          requestBody.fullDayThreshold = this.attendanceData?.fullDayThreshold
          requestBody.workingHours = this.attendanceData?.workingHours
          requestBody.autoAttendanceMark = this.attendanceData?.autoAttendanceMark
          requestBody.autoCheckout = this.attendanceData?.autoCheckout
          requestBody.strictCheckout = this.attendanceData?.strictCheckout
          requestBody.isCheckedInSelfie = this.attendanceData?.isCheckedInSelfie
          requestBody.clockInWithQrCode = this.attendanceData?.clockInWithQrCode
          requestBody.clockOutWithQrCode = this.attendanceData?.clockOutWithQrCode
          requestBody.isCheckedOutSelfie = this.attendanceData?.isCheckedOutSelfie
          requestBody.geoFencingEnabled = this.attendanceData?.geoFencingEnabled
          requestBody.allowSystemCalculation = this.attendanceData?.allowSystemCalculation
          requestBody.allowCheckInAllTime = this.attendanceData?.allowCheckInAllTime
          requestBody.homeGeofenceEnabled = this.attendanceData?.homeGeofenceEnabled
        }
        if (this.attendanceDataFields) {
          requestBody.workingDays = this.attendanceDataFields
        }
        if (this.attendanceStartDataTime) {
          requestBody.shiftStartTime = this.attendanceStartDataTime
        }
        if (this.attendanceEndDataTime) {
          requestBody.shiftEndTime = this.attendanceEndDataTime
        }
        if (this.updateWorkHour) {
          requestBody.workingHours = this.updateWorkHour
        }
        if (this.attendanceTimeThreshold) {
          requestBody.fullDayThreshold = this.attendanceTimeThreshold
        }
        if (this.attendanceTimeHalfThreshold) {
          requestBody.halfDayThreshold = this.attendanceTimeHalfThreshold
        }
        if (this.attendanceTimeOverThreshold) {
          requestBody.overTimeThreshold = this.attendanceTimeOverThreshold
        }
        if (this.shifts.length) {
          requestBody.orgShifts = this.shifts
        } else {
          requestBody.orgShifts = this.$props.receivedData?.orgShifts
        }
        if (this.waBussinessId) {
          requestBody.applications = {
            ...this.receivedData.applications,
            whatsapp: {
              ...this.receivedData.applications.whatsapp,
              waBussinessId: this.waBussinessId
            }
          }
        }
        if (this.travelData?.length || this.allowanceData) {
          requestBody.travelModes = this.travelData
          requestBody.orgAllowances = this.allowanceData
        }

        if (this.enabledSubCases?.length || this.enabledSubCases?.length === 0) {
          requestBody.enabledSubCases = this.enabledSubCases
        }

        if (this.orgLeaves) {
          requestBody.commonLeaves = this.orgLeaves?.commonLeaves
        }
        if (this.orgLeaves?.commonLeavePolicy) {
          requestBody.commonLeavePolicy = this.orgLeaves?.commonLeavePolicy
        }
        if (this.orgLeaves) {
          requestBody.listOfHolidays = this.orgLeaves?.listOfHolidays
        }

        if (this.notificationSetting.length) {
          requestBody.notificationConfig = this.notificationSetting
        }

        if (this.selectedNotification) {
          requestBody.isSmsNotificationEnable = this.selectedNotification.SMS
          requestBody.isEmailNotificationEnable = this.selectedNotification.EMAIL
          requestBody.isInAppNotificationEnable = this.selectedNotification.INAPP
        }

        if (this.docsData.allowedProofTypes) {
          requestBody.allowedProofTypes = this.docsData.allowedProofTypes
        }
        if (this.docsData.predefinedComplaintTitles) {
          requestBody.predefinedComplaintTitles = this.docsData.predefinedComplaintTitles
        }
        if (this.docsData.taskEstimationThreshold) {
          requestBody.taskEstimationThreshold = this.docsData.taskEstimationThreshold
        }

        if (this.docsData.allowedMeetingOutcomesTypes) {
          requestBody.allowedMeetingOutcomesTypes = this.docsData.allowedMeetingOutcomesTypes
        }

        if (this.docsData) {
          requestBody.customerOTPVerification = this.docsData?.customerOTPVerification
          requestBody.isCollectionOtpRequired = this.docsData?.isCollectionOtpRequired
          requestBody.isAutoReschedule = this.docsData?.isAutoReschedule
          requestBody.isTaskRecurrenceAllowedOrg = this.docsData?.isTaskRecurrenceAllowedOrg
          requestBody.isTaskDirectCompletionAllowedOrg =
            this.docsData?.isTaskDirectCompletionAllowedOrg
          requestBody.isTaskAutoAssignedAllowedOrg = this.docsData?.isTaskAutoAssignedAllowedOrg
          requestBody.allowTaskAutoEstimation = this.docsData?.allowTaskAutoEstimation
          requestBody.allowTaskAcknowledgement = this.docsData?.allowTaskAcknowledgement
          requestBody.isTrackEmployeeActions = this.docsData?.isTrackEmployeeActions
        }

        if (this.geoFenceData) {
          requestBody.geoFencingData = this.geoFenceData
        }
        if (this.enableBranchMapping) {
          requestBody.enableBranchMapping = this.enableBranchMapping.value
        }
        if (this.branchFenceValue) {
          requestBody.enableBranchGeofencing = this.branchFenceValue.value
        }
        if (this.allBranchCheckIn) {
          requestBody.allowCheckInFromAllDefinedLocation = this.allBranchCheckIn.value
        }
        if (this.homeGeofenceRadius) {
          requestBody.homeGeofenceRadius = this.homeGeofenceRadius
        }
        if (this.attendanceData?.geoFencingEnabled == false) {
          requestBody.enableBranchGeofencing = false
          requestBody.allowCheckInFromAllDefinedLocation = false
          requestBody.homeGeofenceEnabled = false
          requestBody.homeGeofenceRadius = null
        }

        const response = await this.$axios.post('/workforce/org/config', requestBody)
        this.isAnyChangeInQuestions = false
        this.showReloadDialog = true
        const successMessage = response?.data?.message || 'Org config updated successfully'
        this.$toast.success(successMessage)
        localStorage.setItem('settingActiveTab', this.activeTab)
      } catch (error) {
        if (error.response && error.response.status === 500 && error.response.data.message) {
          const errorMessages = error.response.data.message

          errorMessages.forEach((errorMessage) => {
            const questionError = errorMessage.error
            const questionBody = errorMessage.questionBody
            const question = questionBody ? questionBody.question : ''
            if (questionError) {
              const errorMessageText = `${questionError}`

              this.$toast.error(errorMessageText)

              const questionIndex = this.questionnaire.findIndex((q) => q.question === question)

              if (questionIndex !== -1) {
                this.$set(this.errors, question, errorMessageText)
              }
            }
            console.log(this.errors)
          })
        } else {
          console.error(error.message)
        }
      }
    },
    async fetchBranches() {
      try {
        const response = await this.$axios.get('/workforce/branches')
        this.branches = response.data.branches.map((branch) => ({
          ...branch,
          disabled: true
        }))
      } catch (error) {
        console.error('Error fetching branches:', error)
      }
    },
    changeTitleCase(str) {
      return convertToTitleCase(str)
    },
    handleOrderChange(value) {
      this.hasConfigChanges = value
    },
    handleTaskSteps(val) {
      this.customList = val.customList
      this.isDefault = val.isDefault
    },
    handleSwitch(val) {
      if (val.title === 'SEND_OTP') {
        this.otpVerification = val.value
      }
      if (val.title === 'AUTO_RESCHEDULE') {
        this.isAutoReschedule = val.value
      }
      this.isValueChanged = true
    },
    handleComboBox(val) {
      if (val.title === 'DOCUMENT_PROOF_TYPE') {
        this.proofType = val.value
      }
      if (val.title === 'MEETING_OUTCOMES_TYPE') {
        this.meetingOutcomes = val.value
      }
      this.isValueChanged = true
    },
    handleReloadDialog() {
      this.showReloadDialog = false
    },
    ReloadFunc() {
      this.loading = true
      window.location.reload()
    },
    getProps(tab) {
      return tab.props || {}
    },
    getEventListeners(tab) {
      if (tab) {
        return {
          customList: this.handleTaskSteps,
          questions: (val) => {
            this.isValueChanged = true
            this.questionnaire = val
            this.isAnyChangeInQuestions = true
          },
          switchValue: this.handleSwitch,
          comboBoxValue: this.handleComboBox,
          attendanceData: (val) => {
            this.isValueChanged = true
            this.attendanceData = val
          },
          attendanceDataFields: (val) => {
            this.isValueChanged = true
            this.attendanceDataFields = val
          },
          attendanceDataTime: (val) => {
            this.isValueChanged = true
            this.attendanceStartDataTime = val
          },
          attendanceDataTime2: (val) => {
            this.isValueChanged = true
            this.attendanceEndDataTime = val
          },
          updateWorkHour: (val) => {
            this.isValueChanged = true
            this.updateWorkHour = val
          },
          attendanceTimeThreshold: (val) => {
            this.isValueChanged = true
            this.attendanceTimeThreshold = val
          },
          attendanceTimeHalfThreshold: (val) => {
            this.isValueChanged = true
            this.attendanceTimeHalfThreshold = val
          },
          waBussinessId: (val) => {
            this.isValueChanged = val !== this.receivedData.applications.whatsapp.waBussinessId
            this.waBussinessId = val
          },
          attendanceTimeOverThreshold: (val) => {
            this.isValueChanged = true
            this.attendanceTimeOverThreshold = val
          },
          travelData: (val) => {
            this.isValueChanged = true
            this.travelData = val
          },
          allowanceData: (val) => {
            this.isValueChanged = true
            this.allowanceData = val
          },
          subCases: (val) => {
            this.isValueChanged = true
            this.enabledSubCases = val
          },
          orgLeaves: (val) => {
            this.isValueChanged = true
            this.orgLeaves = val
          },
          notification: (val) => {
            this.isValueChanged = true
            this.notificationSetting = val
          },
          selectedNotification: (val) => {
            this.isValueChanged = true
            this.selectedNotification = val
          },
          isUpdateDisable: () => {
            this.isValueChanged = false
          },
          orgShifts: (val) => {
            this.isValueChanged = true
            this.shifts = val
          },
          branchSwitchValue: (val) => {
            this.isValueChanged = true
            this.enableBranchMapping = val
          },
          Docs: (val) => {
            this.isValueChanged = true
            this.docsData = val
          },
          geoFenceData: (val) => {
            this.isValueChanged = true
            this.geoFenceData = val
          },
          branchFencing: (val) => {
            this.isValueChanged = true
            this.branchFenceValue = val
          },
          allBranchCheckIn: (val) => {
            this.isValueChanged = true
            this.allBranchCheckIn = val
          },
          updateHomeFenceRadius: (val) => {
            this.isValueChanged = true
            this.homeGeofenceRadius = val
          },
          customerEncrypt: (val) => {
            this.isValueChanged = true
            this.isCustomerDataEncrypted = val
          },
          isStepOrderChange: (val) => {
            this.isValueChanged = val
          },
          questionnaireDynamicForm: () => {
            this.isValueChanged = true
          },
          isDyanmicQuestionsChange: this.handleOrderChange
        }
      }
      return {}
    }
  }
}
</script>

<style scoped>
.tabsClass {
  text-transform: capitalize !important;
  display: flex;
  align-self: flex-start;
  justify-content: flex-start;
}
</style>
