<template>
  <div>
    <div v-if="!showPlanDetails && !showMongoDbInterface" class="p-5 overflow-auto max-h-[70vh]">
      <div>
        <div>
          <div class="flex justify-between mb-4">
            <div class="w-3/4">
              <p>
                <span v-if="this.orgData?.plan_name" class="font-bold">Plan Name :- </span>
                <span>
                  {{ this.orgData?.plan_name }}
                </span>
                <span class="status_color">[ {{ this.orgData?.status }} ]</span>
                <v-btn small class="ml-4" color="primary" outlined @click="togglePlanDetails"
                  >Plan Access</v-btn
                >
                <v-btn small class="ml-2" color="success" outlined @click="openSupport">
                  Support
                  <v-icon right small>mdi-comment-processing-outline</v-icon>
                </v-btn>
              </p>
              <p>
                <span class="font-bold">Validity :-</span> {{ is_valid_from }} -
                {{ is_valid_to }}
              </p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <!-- Total Users Card -->
                <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-lg font-semibold text-gray-700 mb-2">Total Users</h3>
                      <div class="flex items-baseline">
                        <span class="text-3xl font-bold text-blue-600">{{
                          totalUsers.current
                        }}</span>
                        <span class="text-gray-500 ml-2">/ {{ totalUsers.limit }}</span>
                      </div>
                      <div class="mt-2">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                          <div
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            :style="{ width: userUsagePercentage + '%' }"
                          ></div>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{{ userUsagePercentage }}% used</p>
                      </div>
                    </div>
                    <div class="text-blue-600">
                      <v-icon size="48">mdi-account-group</v-icon>
                    </div>
                  </div>
                </div>

                <!-- Total Space Card -->
                <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-lg font-semibold text-gray-700 mb-2">Total Space</h3>
                      <div class="flex items-baseline">
                        <span class="text-3xl font-bold text-green-600">{{ totalSpace.used }}</span>
                        <span class="text-gray-500 ml-2">/ {{ totalSpace.limit }}</span>
                      </div>
                      <div class="mt-2">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                          <div
                            class="bg-green-600 h-2 rounded-full transition-all duration-300"
                            :style="{ width: spaceUsagePercentage + '%' }"
                          ></div>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">{{ spaceUsagePercentage }}% used</p>
                      </div>
                    </div>
                    <div class="text-green-600">
                      <v-icon size="48">mdi-harddisk</v-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <QRCode :qrValue="qrValue" />
            </div>
          </div>
        </div>
      </div>

      <!-- Usage Statistics Cards -->
    </div>

    <div v-else-if="showPlanDetails" class="p-5 overflow-auto max-h-[70vh]">
      <div class="w-full my-2">
        <v-btn @click="togglePlanDetails" icon :color="$vuetify.theme.currentTheme.primary">
          <v-icon> mdi-arrow-left </v-icon>
        </v-btn>
        <span class="uppercase gradient-label font-semibold text-xl text-gray-700 ml-4">
          Plan Access
        </span>
      </div>
      <PlanInfoDetails :planDetails="planDetails" />
    </div>
    <div v-if="!showPlanDetails" class="overflow-auto max-h-[70vh]">
      <OrgModelStats
        @mongodb-interface-opened="handleMongoDbInterface"
        @totalSpace="renderTotalSpace"
      />
    </div>

    <!-- Query Component Overlay -->
    <div v-if="showQueryComponent" class="query-overlay">
      <div class="query-container">
        <QueryComponent @handleCloseComplaint="closeQueryComponent" />
      </div>
    </div>
  </div>
</template>
<script>
import QRCode from './qrCode.vue'
import { convertToTitleCase } from '@/utils/common'
import PlanInfoDetails from './PlanInfoDetails.vue'
import OrgModelStats from './OrgModelStats.vue'
import QueryComponent from '@/components/queryComponent.vue'
export default {
  data() {
    return {
      orgData: this.$storage.getUniversal('orgData'),
      orgCode: this.$storage.getUniversal('organization_code'),
      userOrg: this.$storage.getUniversal('user'),
      qrValue: `${process.env.VUE_APP_BASE_URL}/mark-attendance?orgCode=${this.orgCode}`,
      drawer: false,
      showPlanDetails: false,
      showMongoDbInterface: false,
      showQueryComponent: false,
      // Dummy data for usage statistics
      totalUsers: {
        current: 45,
        limit: 100
      },
      totalSpace: {
        used: '',
        limit: '20 GB'
      }
    }
  },
  components: {
    QRCode,
    PlanInfoDetails,
    OrgModelStats,
    QueryComponent
  },
  computed: {
    is_valid_from() {
      return this.$dayjs(this.orgData?.valid_from).format('MMMM D, YYYY')
    },
    is_valid_to() {
      return this.$dayjs(this.orgData?.valid_to).format('MMMM D, YYYY')
    },
    limitations() {
      return this.orgData?.plan_limitations
    },
    planDetails() {
      return this.orgData?.plan_details
    },
    userUsagePercentage() {
      return Math.round((this.totalUsers.current / this.totalUsers.limit) * 100)
    },
    spaceUsagePercentage() {
      // Convert space values to numbers for calculation (assuming GB)
      const used = parseFloat(this.totalSpace.used)
      const limit = parseFloat(this.totalSpace.limit.replace(' GB', ''))
      return Math.round((used / limit) * 100)
    }
  },
  methods: {
    renderTotalSpace(space) {
      this.totalSpace.used = space.toFixed(3)
    },

    toggleShowAll() {
      this.drawer = !this.drawer
    },
    convertTitle(str) {
      return convertToTitleCase(str)
    },
    togglePlanDetails() {
      this.showPlanDetails = !this.showPlanDetails
    },
    handleMongoDbInterface(val) {
      this.showMongoDbInterface = val
    },
    openSupport() {
      this.supportMenuOpen = false
      this.showQueryComponent = true
    },
    closeQueryComponent() {
      this.showQueryComponent = false
    }
  }
}
</script>
<style scoped>
.status_color {
  color: #33cc33;
}
.chipContainer {
  display: flex;
  flex-wrap: wrap;
}

.query-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.query-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  width: auto;
}
</style>
