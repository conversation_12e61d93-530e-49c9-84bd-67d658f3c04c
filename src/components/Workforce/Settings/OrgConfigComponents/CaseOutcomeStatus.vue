<template>
  <div>
    <div
      v-for="(category, index) in categories"
      :key="index"
      class="border border-blue- rounded-md p-3 my-2"
    >
      <v-text-field
        v-model="category.categoryName"
        label="Category Name"
        required
        outlined
        dense
        :disabled="!!isUploadCase"
      >
        <template v-slot:append>
          <v-icon
            color="red"
            class="mb-0.5"
            size="20"
            :disabled="!!isUploadCase"
            @click="removeCategory(index)"
          >
            mdi-delete-outline
          </v-icon>
        </template>
      </v-text-field>
      <v-combobox
        v-model="category.options"
        multiple
        chips
        small-chips
        outlined
        hide-details=""
        dense
        label="Options (Press enter to create new)"
        :disabled="!!isUploadCase"
        @change="updateOptionsVisibility(index)"
      >
        <template v-slot:selection="{ item }">
          <v-chip small>
            {{ item }}
            <template v-if="getFields(category, item).some((f) => f.name === 'amount')">
              <v-icon class="ml-2" color="green"> mdi-cash </v-icon>
              <span class="ml-2 text-xs"> Amount </span>
            </template>
            <template v-if="getFields(category, item).some((f) => f.name === 'date')">
              <v-icon class="ml-2" color="blue"> mdi-calendar </v-icon>
              <span class="ml-2 text-xs"> Date </span>
            </template>
            <v-menu offset-y>
              <template v-slot:activator="{ on }">
                <v-icon class="ml-2" v-on="on"> mdi-dots-vertical </v-icon>
              </template>
              <v-list>
                <v-item-group multiple>
                  <v-list-item v-for="(option, optIndex) in availableOptions" :key="optIndex">
                    <v-checkbox
                      v-model="tempFields[`${index}-${item}`]"
                      :value="{
                        name: option.toLowerCase(),
                        type: option === 'Amount' ? 'number' : 'date',
                        required: true,
                        visibility: true
                      }"
                      :label="option"
                      hide-details
                      multiple
                      dense
                      @change="updateFields(category, item, tempFields[`${index}-${item}`])"
                    ></v-checkbox>
                  </v-list-item>
                </v-item-group>
              </v-list>
            </v-menu>
          </v-chip>
        </template>
      </v-combobox>
      <div class="mt-4" v-if="category.options && category.options.length">
        <v-expansion-panels flat class="border border-gray-300 rounded-md">
          <v-expansion-panel v-if="availableTemplates && availableTemplates.length">
            <v-expansion-panel-header>
              <div class="flex flex-col">
                <span class="font-semibold text-gray-700">Add Notification</span>
                <p class="text-xs text-gray-500 mb-0 font-semibold">
                  (Select a notification template for each option)
                </p>
              </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <div class="border border-gray-200 rounded-md px-3 my-1.5">
                <!-- Header -->
                <div class="grid grid-cols-2 gap-4 py-2 border-b border-gray-200">
                  <div class="font-semibold text-gray-700">Option</div>
                  <div class="font-semibold text-gray-700">Template</div>
                </div>
                <!-- Body -->
                <div class="py-2">
                  <div
                    v-for="(option, optionIndx) in category.options"
                    :key="optionIndx"
                    class="grid grid-cols-2 gap-4 items-center py-1"
                  >
                    <div class="text-sm">
                      <span class="font-bold">{{ option }}</span>
                    </div>
                    <div>
                      <v-select
                        :items="availableTemplates"
                        v-model="category.assignedTemplates[option]"
                        itemText="templateName"
                        itemValue="_id"
                        label="Select Template"
                        placeholder="Select Template"
                        outlined
                        dense
                        hide-details
                      >
                      </v-select>
                    </div>
                  </div>
                </div>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>

          <!-- Outcome form id link -->
          <v-expansion-panel>
            <v-expansion-panel-header>
              <div class="flex flex-col">
                <span class="font-semibold text-gray-700">Link Outcome Form</span>
                <p class="text-xs text-gray-500 mb-0 font-semibold">
                  (Link the dyanmic formId for each option)
                </p>
              </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <div class="border border-gray-200 rounded-md px-3 my-1.5">
                <!-- Header -->
                <div class="grid grid-cols-2 gap-4 py-2 border-b border-gray-200">
                  <div class="font-semibold text-gray-700">Option</div>
                  <div class="font-semibold text-gray-700">Dynamic Form ID</div>
                </div>
                <!-- Body -->
                <div class="py-2">
                  <div
                    v-for="(option, optionIndx) in category.options"
                    :key="optionIndx"
                    class="grid grid-cols-2 gap-4 items-center py-1"
                  >
                    <div class="text-sm">
                      <span class="font-bold">{{ option }}</span>
                    </div>
                    <div>
                      <v-text-field
                        label="Form Id"
                        hide-details
                        outlined
                        dense
                        v-model="category.caseOutcomeFormId[option]"
                      ></v-text-field>
                    </div>
                  </div>
                </div>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>

          <!-- customer feedback form id link -->
          <v-expansion-panel>
            <v-expansion-panel-header>
              <div class="flex flex-col">
                <span class="font-semibold text-gray-700">Link Customer Feedback Form</span>
                <p class="text-xs text-gray-500 mb-0 font-semibold">
                  (Link the dyanmic formId for each option)
                </p>
              </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <div class="border border-gray-200 rounded-md px-3 my-1.5">
                <!-- Header -->
                <div class="grid grid-cols-2 gap-4 py-2 border-b border-gray-200">
                  <div class="font-semibold text-gray-700">Option</div>
                  <div class="font-semibold text-gray-700">Dynamic Form ID</div>
                </div>
                <!-- Body -->
                <div class="py-2">
                  <div
                    v-for="(option, optionIndx) in category.options"
                    :key="optionIndx"
                    class="grid grid-cols-2 gap-4 items-center py-1"
                  >
                    <div class="text-sm">
                      <span class="font-bold">{{ option }}</span>
                    </div>
                    <div>
                      <v-text-field
                        label="Form Id"
                        hide-details
                        outlined
                        dense
                        v-model="category.customerFormId[option]"
                      ></v-text-field>
                    </div>
                  </div>
                </div>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>
    </div>
    <div class="flex justify-end my-2">
      <v-btn
        @click="addCategory"
        :color="$vuetify.theme.currentTheme.primary"
        small
        depressed
        elevation="2"
        :disabled="!!isUploadCase"
        class="white--text"
        >Add Category</v-btn
      >
    </div>
  </div>
</template>

<script>
export default {
  name: 'CaseOutcomeStatus',
  props: {
    categories: {
      type: Array,
      default: () => []
    },
    addCategory: {
      type: Function,
      default: () => {}
    },
    removeCategory: {
      type: Function,
      default: () => {}
    },
    isUploadCase: Boolean
  },
  data() {
    return {
      authData: this.$storage.getUniversal('token'),
      userOrg: this.$storage.getUniversal('user'),
      fetchingTemplates: false,
      bindTemplateRes: [],
      whatsappTemplates: [],
      panel: 0,
      eyeState: false,
      selectedOptions: [],
      availableOptions: ['Amount', 'Date'],
      tempFields: {}
    }
  },
  computed: {
    availableTemplates() {
      const validTemplateTypes = ['task_end_customer']
      const templates = this.bindTemplateRes.reduce((templates, template) => {
        if (validTemplateTypes.includes(template.templateType)) {
          templates.push({
            ...template,
            params: template.params || [],
            templateTxt: template.templateText,
            templateId: template._id
          })
        }
        return templates
      }, [])

      return templates
    }
  },
  methods: {
    async getBindedTemplates() {
      const { data } = await this.$axios.get('/workforce/whatsapp/templates/assign', {
        headers: {
          Authorization: this.authData.access_token
        }
      })
      this.bindTemplateRes = data.templates
    },
    async fetchTemplates() {
      this.fetchingTemplates = true
      const { data } = await this.$axios.get('/workforce/whatsapp/templates', {
        headers: {
          Authorization: this.authData.access_token
        }
      })
      this.whatsappTemplates = data.templates.filter((t) => t.name)
      this.fetchingTemplates = false
    },
    updateOptionsVisibility(categoryIndex) {
      const category = this.categories[categoryIndex]
      category.optionsList = category.options?.map((option) => {
        const existing = category.optionsList?.find((opt) => opt.code === option)
        return (
          existing || {
            code: option,
            amountVisibility: false,
            fields: []
          }
        )
      })
    },
    getFields(category, option) {
      const optionData = category.optionsList?.find((opt) => opt.code === option)
      return optionData?.fields || []
    },
    updateFields(category, option, updatedFields) {
      const optionData = category.optionsList.find((opt) => opt.code === option)
      if (optionData) {
        optionData.fields = updatedFields
      }
    }
  },
  mounted() {
    //this.getBindedTemplates()
    // this.fetchTemplates()
  }
}
</script>
