<template>
  <div class="bg-gray-50">
    <div class="w-full">
      <v-overlay :value="isLoading">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>

      <v-row class="flex w-full">
        <!-- Left Panel - Question List with integrated previews -->
        <v-col cols="12" lg="7" v-if="categories.length > 0">
          <v-card class="rounded-xl" flat>
            <v-card-text class="p-6">
              <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-700">Forms</h2>
                <v-btn text color="primary" @click="addCategory">
                  <v-icon left>mdi-folder-plus</v-icon> Add Form
                </v-btn>
              </div>

              <div class="space-y-2">
                <v-expansion-panels v-model="expandedCategory">
                  <v-expansion-panel
                    v-for="(category, index) in categories"
                    :key="getCategoryKey(category.categoryName)"
                    class="category-item border rounded-lg overflow-hidden mb-3"
                    :class="{ 'selected-category': expandedCategory === index }"
                  >
                    <v-expansion-panel-header class="category-header bg-gray-100 px-4 py-3">
                      <div class="flex justify-between items-center w-full">
                        <div class="flex items-center">
                          <div class="flex flex-col">
                            <span class="font-medium">{{ category.categoryName }}</span>
                            <span class="font-medium text-xs text-gray-500" v-if="category.formId"
                              >({{ category.formId }})

                              <v-icon
                                small
                                color="green"
                                class="ml-2"
                                v-tooltip="{
                                  text: 'Copy FormId'
                                }"
                                @click.stop="copyFormId(category.formId)"
                                >mdi-content-copy</v-icon
                              >
                            </span>
                          </div>
                        </div>
                        <div class="ml-2 flex items-center justify-end">
                          <div class="relative mr-2">
                            <v-btn
                              icon
                              small
                              @click.stop="showFormConnections(category)"
                              v-tooltip="{
                                text: 'View Connections'
                              }"
                            >
                              <v-icon small color="purple">mdi-connection</v-icon>
                            </v-btn>
                            <v-chip
                              v-if="getConnectionCount(category.formId) > 0"
                              x-small
                              color="purple"
                              text-color="white"
                              class="absolute -top-1 -right-1 connection-badge"
                            >
                              {{ getConnectionCount(category.formId) }}
                            </v-chip>
                          </div>
                          <span class="text-xs text-blue-500 bg-gray-200 px-2 py-1 rounded-full">
                            {{ category.questions.length }} question{{
                              category.questions.length !== 1 ? 's' : ''
                            }}
                          </span>
                          <!-- </div>

                        <div> -->
                          <v-btn
                            v-if="!category.isPrivate"
                            icon
                            class=""
                            @click.stop="
                              copySpecificCategoryLink(category._id, category.categoryName)
                            "
                            v-tooltip="{
                              text: 'Copy Social Link'
                            }"
                          >
                            <v-icon color="green">mdi-link-variant</v-icon>
                          </v-btn>
                          <v-btn
                            icon
                            class=""
                            @click.stop="editCategory(category.categoryName)"
                            v-tooltip="{
                              text: 'Edit Form'
                            }"
                          >
                            <v-icon color="blue">mdi-pencil</v-icon>
                          </v-btn>
                          <v-btn
                            icon
                            @click.stop="deleteCategory(category.categoryName)"
                            v-tooltip="{
                              text: 'Delete Form'
                            }"
                          >
                            <v-icon color="red">mdi-delete</v-icon>
                          </v-btn>
                        </div>
                      </div>
                    </v-expansion-panel-header>
                    <v-expansion-panel-content class="category-questions p-4 space-y-4">
                      <!-- Category Header with Banner and Logo -->
                      <div
                        v-if="category.images && category.images.length > 0"
                        class="mb-6 bg-white rounded-lg p-4 shadow-sm"
                      >
                        <div class="relative">
                          <!-- Banner -->
                          <div v-if="getCategoryBannerImage(category)" class="banner-container">
                            <img
                              :src="getCategoryBannerImage(category)"
                              class="w-full h-32 object-cover rounded-t-lg"
                              alt="Banner"
                            />
                          </div>

                          <div
                            class="flex items-center p-4"
                            :class="{
                              'absolute top-0 left-0 w-full': getCategoryBannerImage(category)
                            }"
                          >
                            <!-- Logo -->
                            <div v-if="getCategoryLogoImage(category)" class="mr-4">
                              <img
                                :src="getCategoryLogoImage(category)"
                                class="h-16 w-16 object-contain rounded-full bg-white p-1 shadow-sm"
                                alt="Logo"
                              />
                            </div>

                            <!-- Header -->
                            <h3
                              class="text-xl font-bold"
                              :class="{ 'text-white': getCategoryBannerImage(category) }"
                            >
                              {{ category.header }}
                            </h3>
                          </div>
                        </div>
                      </div>

                      <div
                        v-if="category.questions.length === 0"
                        class="text-sm text-gray-500 p-4 text-center bg-white rounded-lg shadow-sm"
                      >
                        No questions in this category yet
                      </div>

                      <DraggableWrapper
                        :items="category.questions"
                        @reordered="updateQuestionOrder"
                        key-field="_id"
                      >
                        <template v-slot:default="{ item, index }">
                          <v-card class="question-card mb-4 rounded-lg shadow-sm" outlined>
                            <v-card-text class="p-4">
                              <!-- Question Header -->
                              <div class="flex justify-between items-start mb-3">
                                <div class="flex items-center">
                                  <v-icon :color="getTypeColor(item.questionType)" class="mr-2">
                                    {{ getTypeIcon(item.questionType) }}
                                  </v-icon>
                                  <h3 class="font-medium text-md mb-0">
                                    {{ item.question }}
                                    <span v-if="item.isRequired" class="text-red-500">*</span>
                                  </h3>
                                </div>
                                <div class="flex justify-end">
                                  <v-btn
                                    icon
                                    small
                                    @click="editQuestion(category.categoryName, index)"
                                    v-tooltip="{ text: 'Edit' }"
                                  >
                                    <v-icon small color="blue">mdi-pencil</v-icon>
                                  </v-btn>
                                  <v-btn
                                    icon
                                    small
                                    @click="deleteQuestion(category.categoryName, index)"
                                    v-tooltip="{ text: 'Delete' }"
                                  >
                                    <v-icon small color="red">mdi-delete</v-icon>
                                  </v-btn>
                                </div>
                              </div>

                              <!-- Question Preview -->
                              <div
                                class="preview-container bg-gray-50 p-4 rounded-lg border border-gray-200"
                              >
                                <!-- Text Input Preview -->
                                <v-text-field
                                  v-if="item.questionType === 'TEXT'"
                                  label="Your answer"
                                  outlined
                                  dense
                                  disabled
                                  hide-details
                                  placeholder="Text answer"
                                ></v-text-field>

                                <!-- Textarea Preview -->
                                <v-textarea
                                  v-if="item.questionType === 'TEXTAREA'"
                                  label="Your answer"
                                  outlined
                                  dense
                                  disabled
                                  hide-details
                                  placeholder="Longer text answer"
                                  rows="2"
                                ></v-textarea>

                                <!-- Number Input Preview -->
                                <v-text-field
                                  v-if="item.questionType === 'NUMBER'"
                                  label="Your answer"
                                  outlined
                                  dense
                                  disabled
                                  type="number"
                                  placeholder="0"
                                  hide-details
                                ></v-text-field>

                                <!-- Checkbox Preview -->
                                <div v-if="item.questionType === 'CHECKBOX'">
                                  <v-checkbox
                                    v-for="(option, i) in item.options"
                                    :key="i"
                                    :label="option"
                                    disabled
                                    dense
                                    hide-details
                                    class="mt-0 mb-1"
                                  ></v-checkbox>
                                </div>

                                <!-- Radio Preview -->
                                <div v-if="item.questionType === 'RADIO'">
                                  <v-radio-group disabled>
                                    <v-radio
                                      v-for="(option, i) in item.options"
                                      :key="i"
                                      :label="option"
                                      :value="option"
                                      dense
                                      hide-details
                                      class="mt-0 mb-1"
                                    ></v-radio>
                                  </v-radio-group>
                                </div>

                                <!-- File Upload Preview -->
                                <div
                                  v-if="item.questionType === 'LOCAL_ATTACHMENTS'"
                                  class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"
                                >
                                  <v-icon size="32" color="grey lighten-1">mdi-cloud-upload</v-icon>
                                  <p class="text-sm text-gray-500">
                                    Drag & drop files here or click to browse
                                  </p>
                                  <p class="text-xs text-gray-400 mt-1">
                                    Maximum {{ item.allowedNoOfAttachments || 1 }} file(s)
                                  </p>
                                </div>

                                <!-- Media Capture Previews -->
                                <div
                                  v-if="item.questionType === 'CLICK_PICTURES'"
                                  class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"
                                >
                                  <v-icon size="32" color="grey lighten-1">mdi-camera</v-icon>
                                  <p class="text-sm text-gray-500">Upload image or take a photo</p>
                                </div>

                                <div
                                  v-if="item.questionType === 'AUDIO_CAPTURE'"
                                  class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"
                                >
                                  <v-icon size="32" color="grey lighten-1">mdi-microphone</v-icon>
                                  <p class="text-sm text-gray-500">Record audio or upload file</p>
                                </div>

                                <div
                                  v-if="item.questionType === 'VIDEO_CAPTURE'"
                                  class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"
                                >
                                  <v-icon size="32" color="grey lighten-1">mdi-video</v-icon>
                                  <p class="text-sm text-gray-500">Record video or upload file</p>
                                </div>

                                <!-- SERVICE_PLAN preview -->
                                <div
                                  v-if="item.questionType === 'SERVICE_PLAN'"
                                  class="border-2 border-dashed border-gray-300 rounded-lg p-4"
                                >
                                  <div v-if="item.payment_Plan && item.payment_Plan.length > 0">
                                    <h4 class="font-medium mb-2">Available Plans</h4>
                                    <div
                                      v-for="(plan, planIndex) in item.payment_Plan"
                                      :key="planIndex"
                                      class="mb-3 p-3 border rounded-lg bg-white"
                                    >
                                      <div class="font-medium">{{ plan.planName }}</div>
                                      <div class="text-sm text-gray-600">
                                        {{ plan.planDescription }}
                                      </div>
                                      <div class="flex justify-between mt-2">
                                        <span class="text-sm"
                                          >Duration: {{ plan.planDuration }} days</span
                                        >
                                        <span class="font-medium"
                                          >Amount: {{ plan.planAmount }}</span
                                        >
                                      </div>
                                    </div>
                                    <div
                                      class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center"
                                    >
                                      <div v-if="item.options && item.options.length > 0">
                                        <img
                                          :src="bannerLogoUrl(item.options[0])"
                                          class="mx-auto h-40 object-contain mb-2"
                                          alt="Payment QR"
                                        />
                                        <p class="text-sm text-gray-500">
                                          Scan QR code to make payment
                                        </p>
                                      </div>
                                      <div v-else>
                                        <v-icon size="32" color="grey lighten-1">mdi-qrcode</v-icon>
                                        <p class="text-sm text-gray-500">
                                          Payment QR code will appear here
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div v-else>
                                    <v-icon size="32" color="grey lighten-1"
                                      >mdi-currency-usd</v-icon
                                    >
                                    <p class="text-sm text-gray-500">
                                      Payment plans will appear here
                                    </p>
                                  </div>
                                </div>

                                <!-- DATA_CATEGORY preview -->
                                <div v-if="item.questionType === 'DATA_CATEGORY'" class="mt-2">
                                  <v-select
                                    disabled
                                    :items="item.options || []"
                                    label="Select data category"
                                    outlined
                                    dense
                                    hide-details
                                  ></v-select>
                                </div>
                                <!-- SELECT_OPTIONS preview -->
                                <div v-if="item.questionType === 'SELECT_OPTIONS'" class="mt-2">
                                  <v-select
                                    disabled
                                    :items="item.options || []"
                                    label="Select options"
                                    outlined
                                    dense
                                    hide-details
                                  ></v-select>
                                </div>
                              </div>
                            </v-card-text>
                          </v-card>
                        </template>
                      </DraggableWrapper>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
        <div class="flex justify-center items-center py-32" lg="5" v-else>
          <NoData
            class="flex w-full justify-center"
            title="Nothing to Display"
            subTitle="Start by creating a form"
            btnText="Create Form"
            :btnAction="addCategory"
          />
        </div>

        <!-- Right Panel -Add Question Builder -->
        <v-col cols="12" lg="5" v-if="categories.length > 0">
          <!-- <StickyWrapper :offsetTop="50"> -->
          <v-card class="rounded-xl mb-6 position-relative" flat>
            <!-- Grey tint overlay when no category is selected -->
            <div
              v-if="expandedCategory === null || expandedCategory === undefined"
              class="absolute inset-0 bg-gray-500 bg-opacity-50 z-10 flex items-center justify-center rounded-xl"
            >
              <div class="bg-white p-4 rounded-lg shadow-lg text-center">
                <v-icon large color="info" class="mb-2">mdi-information-outline</v-icon>
                <p class="text-lg font-medium">Please select a form</p>
                <p class="text-sm text-gray-600">
                  Select a form from the left panel to add or edit questions
                </p>
              </div>
            </div>

            <v-card-text class="p-6">
              <h2 class="text-xl font-semibold text-gray-700 mb-4">
                <!-- {{ editingQuestionId ? 'Edit Question' : 'Add Question' }} -->
                Add Question to Selected Form
                <span class="text-base text-gray-500" v-if="selectedFormId">
                  ({{ selectedFormId }})
                </span>
              </h2>

              <v-form ref="form">
                <v-text-field
                  v-model="questionText"
                  label="Ask Question"
                  placeholder="Enter your question here"
                  outlined
                  dense
                  hide-details
                  class="mb-4"
                  :disabled="expandedCategory === null || expandedCategory === undefined"
                ></v-text-field>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1"
                    >Select Field Type
                  </label>
                  <div class="grid grid-cols-3 gap-3 mt-2">
                    <div
                      v-for="type in questionTypes.filter((t) => t.visible)"
                      :key="type.value"
                      @click="type.enabled && selectQuestionType(type.value)"
                    >
                      <div
                        class="question-type-selector p-3 border rounded-lg text-center"
                        :class="{
                          'border-blue-500 bg-blue-50': currentQuestionType === type.value
                        }"
                      >
                        <div
                          :class="{
                            'cursor-pointer': type.enabled,
                            'opacity-50 cursor-not-allowed': !type.enabled
                          }"
                        >
                          <v-icon
                            :color="type.enabled ? type.color : 'grey'"
                            class="text-2xl mb-1"
                            >{{ type.icon }}</v-icon
                          >
                          <p class="text-sm mb-0" :class="{ 'text-gray-400': !type.enabled }">
                            {{ type.label }}
                          </p>
                        </div>
                        <p
                          v-if="type.value === 'DATA_CATEGORY' && !type.enabled"
                          class="text-xs text-orange-600 mb-0 opa"
                        >
                          Already linked in this form.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Dynamic options based on question type -->
                <div v-if="currentQuestionType" class="mt-4">
                  <div
                    v-if="
                      currentQuestionType === 'CHECKBOX' ||
                      currentQuestionType === 'RADIO' ||
                      currentQuestionType === 'SELECT_OPTIONS'
                    "
                    class="mb-4"
                  >
                    <label class="block text-sm font-medium text-gray-700 mb-2"
                      >{{ editingQuestionId ? 'Edit' : 'Add' }} Options</label
                    >
                    <div class="flex flex-col space-y-2">
                      <div
                        v-for="(option, index) in options"
                        :key="index"
                        class="flex items-center"
                      >
                        <div class="flex-grow p-2 bg-gray-50 border rounded-md">
                          {{ option }}
                        </div>
                        <v-btn icon small class="ml-2" @click="removeOption(index)">
                          <v-icon
                            small
                            color="red"
                            v-tooltip="{
                              text: 'Delete Option'
                            }"
                            >mdi-delete</v-icon
                          >
                        </v-btn>
                      </div>
                    </div>

                    <div class="flex items-center mt-3">
                      <v-text-field
                        v-model="newOption"
                        label="Add new option"
                        placeholder="type and press enter"
                        outlined
                        dense
                        hide-details
                        class="flex-grow"
                        @keyup.enter="addOption"
                      ></v-text-field>
                      <v-btn color="primary" small class="ml-2" @click="addOption">
                        <v-icon small>mdi-plus</v-icon>
                      </v-btn>
                    </div>
                  </div>

                  <!-- Common options for all question types -->
                  <div class="flex flex-wrap items-center gap-4 mb-4">
                    <v-checkbox
                      v-model="isRequired"
                      label="Is this question mandatory to answer by user?"
                      hide-details
                      class="my-0 mr-4"
                    ></v-checkbox>

                    <v-text-field
                      v-if="currentQuestionType === 'LOCAL_ATTACHMENTS'"
                      v-model.number="maxAttachments"
                      type="number"
                      label="Max attachments"
                      min="1"
                      max="10"
                      outlined
                      dense
                      style="max-width: 250px"
                      hide-details
                    ></v-text-field>
                  </div>

                  <v-text-field
                    v-model="validationMessage"
                    label="Validation Message"
                    placeholder="Enter validation message"
                    outlined
                    dense
                    hide-details
                    class="mb-4"
                  ></v-text-field>

                  <!-- Media specific options -->
                  <div
                    v-if="
                      ['AUDIO_CAPTURE', 'VIDEO_CAPTURE', 'CLICK_PICTURES'].includes(
                        currentQuestionType
                      )
                    "
                    class="mb-4 flex justify-between items-center"
                  >
                    <v-text-field
                      v-model.number="mediaMaxSize"
                      type="number"
                      label="Maximum file size (MB)"
                      min="1"
                      max="100"
                      outlined
                      dense
                      class="mr-4"
                      style="max-width: 200px"
                    ></v-text-field>

                    <v-select
                      v-model="mediaQuality"
                      :items="['Low', 'Medium', 'High']"
                      label="Quality"
                      outlined
                      dense
                    ></v-select>
                  </div>

                  <!-- Payment Plan options -->
                  <div v-if="currentQuestionType === 'SERVICE_PLAN'" class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2"
                      >Payment Plans</label
                    >
                    <div class="border border-gray-300 rounded-lg p-4">
                      <div
                        v-for="(plan, planIndex) in paymentPlans"
                        :key="planIndex"
                        class="mb-4 pb-4 border-b border-gray-200"
                      >
                        <!-- <v-form ref="paymentForm"> -->
                        <div class="flex justify-between items-center mb-2">
                          <h4 class="font-medium">Plan #{{ planIndex + 1 }}</h4>
                          <v-btn icon small color="red" @click="removePaymentPlan(planIndex)">
                            <v-icon small>mdi-delete</v-icon>
                          </v-btn>
                        </div>
                        <v-text-field
                          v-model="plan.planName"
                          label="Plan Name"
                          placeholder="Enter plan name"
                          :rules="[(v) => !!v || 'Plan name is required']"
                          outlined
                          dense
                          hide-details
                          class="mb-2"
                        ></v-text-field>

                        <v-text-field
                          v-model="plan.planDescription"
                          label="Plan Description"
                          placeholder="Enter plan description"
                          outlined
                          dense
                          hide-details
                          class="mb-2"
                        ></v-text-field>

                        <div class="grid grid-cols-2 gap-4">
                          <v-text-field
                            v-model.number="plan.planDuration"
                            type="number"
                            label="Duration (days)"
                            min="1"
                            outlined
                            dense
                            hide-details
                          ></v-text-field>

                          <v-text-field
                            v-model.number="plan.planAmount"
                            :rules="[(v) => !!v || 'Amount is required']"
                            type="number"
                            label="Amount"
                            min="0"
                            outlined
                            dense
                            hide-details
                          ></v-text-field>
                        </div>
                        <!-- </v-form> -->
                      </div>

                      <v-btn color="primary" text block @click="addPaymentPlan" class="mt-2">
                        <v-icon left>mdi-plus</v-icon> Add Plan
                      </v-btn>
                    </div>
                    <v-select
                      v-model="selectedPaymentType"
                      label="Payment Type"
                      :items="paymentTypes"
                      item-text="text"
                      item-value="value"
                      outlined
                      dense
                      hide-details
                      class="my-4"
                    ></v-select>
                    <div class="my-4" v-if="selectedPaymentType === 'QR_CODE'">
                      <label class="block text-sm font-medium text-gray-700 mb-2"
                        >Payment QR Code</label
                      >
                      <div
                        class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer relative"
                        @click="triggerFileInput('paymentQR')"
                      >
                        <div v-if="paymentQRImage">
                          <img
                            :src="paymentQRImage.preview"
                            class="mx-auto h-40 object-contain mb-2"
                          />
                          <v-btn
                            icon
                            small
                            class="absolute top-0 right-0 m-1"
                            @click.stop="removePaymentQR"
                          >
                            <v-icon small color="red">mdi-delete</v-icon>
                          </v-btn>
                        </div>
                        <div v-else>
                          <v-icon size="48" color="grey lighten-1">mdi-qrcode</v-icon>
                          <p class="text-sm text-gray-500 mt-1">Upload Payment QR Code</p>
                        </div>
                      </div>
                      <input
                        type="file"
                        ref="paymentQRInput"
                        class="hidden"
                        accept="image/*"
                        @change="handlePaymentQRUpload"
                      />
                    </div>

                    <!-- Add Payment Link -->
                    <div v-if="selectedPaymentType === 'PAYMENT_LINK'" class="mb-4">
                      <v-text-field
                        v-model="paymentLink"
                        label="Payment Link"
                        placeholder="Enter payment link"
                        outlined
                        dense
                        hide-details
                        class="mb-4"
                      ></v-text-field>
                    </div>

                    <!-- Add UPI ID -->
                    <div v-if="selectedPaymentType === 'GOOGLE_PAY'" class="mb-4">
                      <v-text-field
                        v-model="payment_UPI_Id"
                        label="UPI ID"
                        placeholder="Enter UPI ID"
                        outlined
                        dense
                        hide-details
                        class="mb-4"
                      ></v-text-field>
                    </div>
                  </div>

                  <!-- Data Category options -->
                  <div v-if="currentQuestionType === 'DATA_CATEGORY'" class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Available Data Categories
                    </label>
                    <div v-if="dataCategories.length > 0" class="border rounded-md p-3 bg-gray-50">
                      <div v-for="(category, index) in dataCategories" :key="index" class="mb-2">
                        <v-chip class="mr-2 mb-2" small>{{ category.text }}</v-chip>
                      </div>
                    </div>
                    <div v-else class="text-sm text-gray-500 p-3 border rounded-md">
                      No data categories found for this license/team.
                      <span v-if="!selectedSubcase" class="text-blue-500"
                        >Please select a license/team first.</span
                      >
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                      These data categories will be available as options when this question is
                      displayed.
                    </p>
                  </div>
                </div>
                <v-divider class="my-4" v-if="selectedSubcase"></v-divider>
                <v-label class="text-sm" v-if="selectedSubcase"
                  >Link this question to Field mapping in Linked UseCase</v-label
                >
                <v-select
                  v-model="linkedKey"
                  label="Link Question"
                  :items="fieldMappings"
                  placeholder="Link question with license column"
                  outlined
                  dense
                  hide-details
                  clearable
                  class="mt-2"
                  v-if="selectedSubcase"
                ></v-select>

                <div class="flex justify-end space-x-3 pt-4">
                  <v-btn text @click="resetQuestionForm" small>Cancel</v-btn>
                  <v-btn
                    color="primary"
                    @click="saveQuestion"
                    small
                    :disabled="isAddButtonEnable"
                    >{{ editingQuestionId ? 'Save' : 'Add' }}</v-btn
                  >
                </div>
              </v-form>
            </v-card-text>
          </v-card>
          <!-- </StickyWrapper> -->
        </v-col>
      </v-row>
    </div>

    <!-- Form Connections Modal -->
    <v-dialog v-model="showConnectionsModal" max-width="900px">
      <v-card class="rounded-lg">
        <v-card-title class="bg-purple-50 px-6 py-4">
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center">
              <v-icon color="purple" class="mr-3">mdi-connection</v-icon>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">Form Connections</h3>
                <p class="text-sm text-gray-600" v-if="selectedFormForConnections">
                  {{ selectedFormForConnections.categoryName }} ({{
                    selectedFormForConnections.formId
                  }})
                </p>
              </div>
            </div>
            <v-btn icon @click="showConnectionsModal = false">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>
        </v-card-title>

        <v-card-text class="p-6">
          <div v-if="formConnections.length === 0" class="text-center py-8">
            <v-icon size="64" color="grey lighten-2">mdi-connection</v-icon>
            <p class="text-gray-500 mt-4">This form is not connected to any modules yet</p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(connection, index) in formConnections"
              :key="index"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0">
                    <v-icon :color="connection.iconColor" size="24">{{ connection.icon }}</v-icon>
                  </div>
                  <div class="flex-grow">
                    <h4 class="font-medium text-gray-800">{{ connection.moduleName }}</h4>
                    <p class="text-sm text-gray-600 mt-1">{{ connection.description }}</p>

                    <!-- Connection Details -->
                    <div class="mt-3 space-y-2">
                      <div class="flex items-center text-xs text-gray-500">
                        <v-icon small class="mr-1">mdi-calendar</v-icon>
                        Connected on: {{ connection.connectedDate }}
                      </div>
                      <div class="flex items-center text-xs text-gray-500">
                        <v-icon small class="mr-1">mdi-account</v-icon>
                        Connected by: {{ connection.connectedBy }}
                      </div>
                      <div class="flex items-center text-xs text-gray-500">
                        <v-icon small class="mr-1">mdi-chart-line</v-icon>
                        Usage: {{ connection.usageCount }} submissions
                      </div>
                    </div>

                    <!-- Status Badge -->
                    <div class="mt-3">
                      <v-chip
                        :color="connection.status === 'active' ? 'green' : 'orange'"
                        small
                        text-color="white"
                      >
                        <v-icon small left>
                          {{
                            connection.status === 'active' ? 'mdi-check-circle' : 'mdi-pause-circle'
                          }}
                        </v-icon>
                        {{ connection.status === 'active' ? 'Active' : 'Inactive' }}
                      </v-chip>
                    </div>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col space-y-2">
                  <v-btn small outlined color="primary" @click="viewConnectionDetails(connection)">
                    <v-icon small left>mdi-eye</v-icon>
                    View
                  </v-btn>
                  <v-btn small outlined color="orange" @click="editConnection(connection)">
                    <v-icon small left>mdi-pencil</v-icon>
                    Edit
                  </v-btn>
                  <v-btn small outlined color="red" @click="disconnectForm(connection)">
                    <v-icon small left>mdi-unlink</v-icon>
                    Disconnect
                  </v-btn>
                </div>
              </div>
            </div>
          </div>

          <!-- Add New Connection Button -->
          <div class="mt-6 pt-4 border-t border-gray-200">
            <v-btn color="primary" outlined block @click="showAddConnectionModal = true">
              <v-icon left>mdi-plus</v-icon>
              Connect to New Module
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Add Connection Modal -->
    <v-dialog v-model="showAddConnectionModal" max-width="600px">
      <v-card class="rounded-lg">
        <v-card-title class="bg-blue-50 px-6 py-4">
          <div class="flex items-center">
            <v-icon color="blue" class="mr-3">mdi-plus-circle</v-icon>
            <h3 class="text-lg font-semibold text-gray-800">Connect Form to Module</h3>
          </div>
        </v-card-title>

        <v-card-text class="p-6">
          <v-select
            v-model="selectedModuleToConnect"
            :items="availableModules"
            item-text="name"
            item-value="id"
            label="Select Module"
            outlined
            dense
            class="mb-4"
          >
            <template v-slot:item="{ item }">
              <div class="flex items-center py-2">
                <v-icon :color="item.iconColor" class="mr-3">{{ item.icon }}</v-icon>
                <div>
                  <div class="font-medium">{{ item.name }}</div>
                  <div class="text-sm text-gray-600">{{ item.description }}</div>
                </div>
              </div>
            </template>
          </v-select>

          <v-textarea
            v-model="connectionNotes"
            label="Connection Notes (Optional)"
            placeholder="Add any notes about this connection..."
            outlined
            dense
            rows="3"
            class="mb-4"
          ></v-textarea>
        </v-card-text>

        <v-card-actions class="px-6 pb-6">
          <v-spacer></v-spacer>
          <v-btn text @click="showAddConnectionModal = false">Cancel</v-btn>
          <v-btn color="primary" @click="connectFormToModule" :disabled="!selectedModuleToConnect">
            Connect
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Add Category Modal -->
    <v-dialog v-model="showAddCategoryModal" max-width="800px">
      <v-card class="rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            {{ editingCategoryName ? 'Edit' : 'Add New' }} Dynamic Form
          </h3>
          <v-btn icon @click="showAddCategoryModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>

        <v-alert type="info" class="text-sm" v-if="subcaseChangeinfo" text
          >Questions linked mappings will be reset if you change the license/team.</v-alert
        >
        <v-stepper v-model="categoryStep" vertical flat>
          <!-- Step 1: Basic Information -->
          <v-stepper-step :complete="categoryStep > 1" step="1"> Basic Information </v-stepper-step>
          <v-stepper-content step="1">
            <v-form>
              <v-text-field
                v-model="newCategoryName"
                label="Form Name"
                placeholder="Enter form name"
                outlined
                hide-details
                dense
                :rules="[(v) => !!v || 'Form Name is required']"
                class="my-4"
              ></v-text-field>
              <v-text-field
                v-model="categoryHeader"
                label="Tag Line"
                placeholder="Enter tag line"
                outlined
                dense
                hide-details
                class="mb-4"
              ></v-text-field>

              <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label class="block text-sm text-gray-600 mb-1">Logo</label>
                  <div
                    class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer relative"
                    @click="triggerFileInput('logo')"
                  >
                    <div v-if="categoryImages.logo">
                      <img
                        :src="categoryImages.logo.preview"
                        class="mx-auto h-20 object-contain mb-2"
                      />
                      <v-btn
                        icon
                        small
                        class="absolute top-0 right-0 m-1"
                        @click.stop="removeImage('logo')"
                      >
                        <v-icon small color="red">mdi-delete</v-icon>
                      </v-btn>
                    </div>
                    <div v-else>
                      <v-icon size="36" color="grey lighten-1">mdi-image-outline</v-icon>
                      <p class="text-sm text-gray-500 mt-1">Upload Logo</p>
                    </div>
                  </div>
                  <input
                    type="file"
                    ref="logoInput"
                    class="hidden"
                    accept="image/*"
                    @change="handleImageUpload($event, 'logo')"
                  />
                </div>

                <div>
                  <label class="block text-sm text-gray-600 mb-1">Banner</label>
                  <div
                    class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer relative"
                    @click="triggerFileInput('banner')"
                  >
                    <div v-if="categoryImages.banner">
                      <img
                        :src="categoryImages.banner.preview"
                        class="mx-auto h-20 object-contain mb-2"
                      />
                      <v-btn
                        icon
                        small
                        class="absolute top-0 right-0 m-1"
                        @click.stop="removeImage('banner')"
                      >
                        <v-icon small color="red">mdi-delete</v-icon>
                      </v-btn>
                    </div>
                    <div v-else>
                      <v-icon size="36" color="grey lighten-1">mdi-image-outline</v-icon>
                      <p class="text-sm text-gray-500 mt-1">Upload Banner</p>
                    </div>
                  </div>
                  <input
                    type="file"
                    ref="bannerInput"
                    class="hidden"
                    accept="image/*"
                    @change="handleImageUpload($event, 'banner')"
                  />
                </div>
                <v-checkbox
                  v-model="isPrivate"
                  label="Private Access "
                  hide-details
                  dense
                  class="flex justify-start mt-4"
                ></v-checkbox>
              </div>
              <div class="flex justify-end mt-4">
                <v-btn color="primary" @click="categoryStep = 2" small>Next</v-btn>
              </div>
            </v-form>
          </v-stepper-content>

          <!-- Step 2: License/Team Connection -->
          <v-stepper-step :complete="categoryStep > 2" step="2">
            Link with Use Case
            <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="2">
            <v-form>
              <v-checkbox
                v-model="linkWithSubLicense"
                label="Do you want to link this form to any Use Case?"
                class="ml-2"
              ></v-checkbox>

              <v-select
                v-if="linkWithSubLicense"
                v-model="selectedSubcase"
                label="Select Sub-case"
                :items="subCases[0]?.subCase"
                :rules="[
                  (v) => (!!v && linkWithSubLicense) || 'Please select a License/Team',
                  (v) => !!v || 'License/Team is required'
                ]"
                item-text="name"
                item-value="name"
                outlined
                dense
                hide-details
                clearable
                class="mb-4"
                @change="resetQuestionCollectionKeyFields"
              ></v-select>

              <div class="flex justify-end mt-4">
                <v-btn text outlined small @click="categoryStep = 1" class="mr-4">Previous</v-btn>
                <v-btn color="primary" @click="handleLicenseStepComplete" small>Next</v-btn>
              </div>
            </v-form>
          </v-stepper-content>

          <!-- Step 3: Data Category Configuration -->
          <v-stepper-step :complete="categoryStep > 3" step="3">
            Link Data Category
            <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="3">
            <v-form>
              <v-checkbox
                v-model="collectDataBasedOnCategory"
                label="Do you want to segregate data into a particular category in the linked sub-case?"
                class="ml-2"
              ></v-checkbox>

              <v-select
                v-if="collectDataBasedOnCategory"
                label="Select Data Category"
                v-model="selectedDataCategory"
                :items="dataCategories"
                placeholder="Select Data Category"
                outlined
                dense
                clearable
                hide-details
                class="mb-4"
              ></v-select>

              <div class="flex justify-end mt-4">
                <v-btn text outlined small @click="categoryStep = 2" class="mr-4">Previous</v-btn>
                <v-btn color="primary" @click="handleDataCategoryStepComplete" small>Next</v-btn>
              </div>
            </v-form>
          </v-stepper-content>
          <!-- Step 4: Auto crate dynamic form -->
          <v-stepper-step :complete="categoryStep > 4" step="4">
            Auto Generate Form Fields
            <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="4">
            <v-form>
              <v-checkbox
                v-model="autoCreateDynamicForm"
                label="Do you want to generate form fields based on the linked sub-case?"
                class="ml-2"
              ></v-checkbox>
              <div class="flex justify-end mt-4">
                <v-btn text outlined @click="categoryStep = 3" small class="mr-4">Previous</v-btn>
                <v-btn color="primary" @click="handleAutoFormComplete" small>Next</v-btn>
              </div>
            </v-form>
          </v-stepper-content>

          <!-- Step 5: Notification Configuration -->
          <v-stepper-step :complete="categoryStep > 5" step="5">
            Add Notifications
            <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="5">
            <v-form>
              <v-checkbox
                v-model="triggerNotifications"
                label="Do you want to trigger a notification on form submission?"
                class="ml-2"
              ></v-checkbox>
              <template v-if="triggerNotifications">
                <v-select
                  label="Select Template(s)"
                  :items="whatsappTriggers"
                  v-model="selectedTemplatesList"
                  item-text="templateName"
                  return-object
                  outlined
                  multiple
                  dense
                  clearable
                  hide-details
                  class="mb-4"
                ></v-select>
              </template>

              <template>
                <div
                  v-if="triggerNotifications && selectedTemplatesList.length > 0"
                  class="mb-4 bg-white border border-gray-200 rounded-lg p-4"
                >
                  <v-row class="mb-1 px-3 pt-3">
                    <v-col cols="6" class="font-weight-bold text-gray-800" style="font-size: 14px">
                      Template Name
                    </v-col>
                    <v-col
                      cols="6"
                      class="font-weight-bold text-gray-800 text-right"
                      style="font-size: 14px"
                    >
                      Notification Type
                    </v-col>
                  </v-row>
                  <v-divider></v-divider>

                  <!-- Data Rows -->
                  <v-row
                    v-for="item in selectedTemplatesListDetails"
                    :key="item._id"
                    class="mt-2 px-3 pb-2"
                  >
                    <v-col cols="6" class="text-gray-900" style="font-size: 13px">
                      {{ titleCase(item.templateName) }}
                    </v-col>
                    <v-col cols="6" class="text-gray-900 text-right" style="font-size: 13px">
                      {{ item.notificationType.join(', ') }}
                    </v-col>
                  </v-row>
                </div>
              </template>
            </v-form>
            <div class="flex justify-end mt-4 pt-4">
              <v-btn
                text
                outlined
                @click="linkWithSubLicense ? (categoryStep = 4) : (categoryStep = 2)"
                small
                >Previous</v-btn
              >
            </div>
          </v-stepper-content>
        </v-stepper>
        <div class="flex justify-end mt-4">
          <v-btn text outlined small @click="showAddCategoryModal = false" class="mr-4"
            >Cancel</v-btn
          >
          <v-btn color="primary" @click="confirmAddCategory" small> Save </v-btn>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { APP_URL } from '@/utils/workforce/constants'
import DraggableWrapper from '@/components/common/DraggableWrapper.vue'
import { convertToTitleCase } from '@/utils/common'
import NoData from '@/components/NoData.vue'
// import StickyWrapper from '@/components/common/StickyWrapper.vue'
export default {
  name: 'CategoryQuestionnaire',
  components: {
    DraggableWrapper,
    NoData
    // StickyWrapper
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      orgData: this.$storage.getUniversal('orgData'),
      subCases: this.$store.state.subCases,
      currentQuestionType: null,
      questionnaire: [],
      dataCategories: [],
      editingQuestionIndex: null,
      editingQuestionId: null,
      editingCategoryName: null,
      expandedCategory: null,
      isLoading: false,
      isPrivate: false,
      showCategoryMenu: false,
      selectedDataCategory: null,

      // Form fields
      questionCategory: null,
      questionText: '',
      isRequired: false,
      validationMessage: 'This field is mandatory',
      maxAttachments: 1,
      maxFileSize: 10,
      mediaMaxSize: 20,
      mediaQuality: 'Medium',

      // Category fields
      editCategoryId: null,
      categoryHeader: '',
      categoryImages: {
        logo: null,
        banner: null
      },

      // UI state
      showAddCategoryModal: false,
      newCategoryName: '',
      options: [],
      newOption: '',

      selectedPaymentType: 'QR_CODE',
      paymentTypes: [
        { text: 'QR Code', value: 'QR_CODE' },
        { text: 'Google Pay', value: 'GOOGLE_PAY' },
        { text: 'Payment Link', value: 'PAYMENT_LINK' },
        { text: 'Razorpay', value: 'RAZORPAY' },
        { text: 'PayPal', value: 'PAYPAL' }
      ],

      // Question types configuration
      questionTypes: [
        {
          value: 'TEXT',
          label: 'Text',
          icon: 'mdi-form-textbox',
          color: 'blue',
          enabled: true,
          visible: true
        },
        {
          value: 'TEXTAREA',
          label: 'Text Area',
          icon: 'mdi-text-box-outline',
          color: 'green',
          enabled: true,
          visible: true
        },
        {
          value: 'NUMBER',
          label: 'Number',
          icon: 'mdi-numeric',
          color: 'orange',
          enabled: true,
          visible: true
        },
        {
          value: 'CHECKBOX',
          label: 'Checkbox',
          icon: 'mdi-checkbox-marked-outline',
          color: 'teal',
          enabled: true,
          visible: true
        },
        {
          value: 'RADIO',
          label: 'Radio',
          icon: 'mdi-radiobox-marked',
          color: 'amber',
          enabled: true,
          visible: true
        },
        {
          value: 'SELECT_OPTIONS',
          label: 'Select Options',
          icon: 'mdi-form-dropdown',
          color: 'purple',
          enabled: true,
          visible: true
        },
        {
          value: 'MULTI_TEXT',
          label: 'Multi Text',
          icon: 'mdi-text-box-multiple-outline',
          color: 'cyan',
          enabled: true,
          visible: true
        },
        {
          value: 'SERVICE_PLAN',
          label: 'Payment Plan',
          icon: 'mdi-credit-card',
          color: 'blue',
          enabled: true,
          visible: true
        },
        {
          value: 'LOCAL_ATTACHMENTS',
          label: 'Attachment',
          icon: 'mdi-paperclip',
          color: 'purple',
          enabled: true,
          visible: true
        },
        {
          value: 'AUDIO_CAPTURE',
          label: 'Audio',
          icon: 'mdi-microphone',
          color: 'red',
          enabled: true,
          visible: true
        },
        {
          value: 'VIDEO_CAPTURE',
          label: 'Video',
          icon: 'mdi-video',
          color: 'indigo',
          enabled: true,
          visible: true
        },
        {
          value: 'CLICK_PICTURES',
          label: 'Live Picture',
          icon: 'mdi-camera',
          color: 'pink',
          enabled: true,
          visible: true
        },
        {
          value: 'DATA_CATEGORY',
          label: 'Data Category',
          icon: 'mdi-folder-multiple-outline',
          color: 'teal',
          enabled: true,
          visible: true
        }
      ],
      optionsItems: [],
      paymentQRImage: null,
      selectedSubcase: null,
      linkedKey: null,
      paymentLink: '',
      payment_UPI_Id: '',
      paymentPlans: [],
      subcaseChangeinfo: false,
      whatsappTriggers: [],
      notificationTypes: ['WHATSAPP', 'SMS', 'EMAIL'],
      selectedNotificationTypes: [],
      selectedTemplates: {
        WHATSAPP: [],
        SMS: [],
        EMAIL: []
      },
      smsTemplates: [
        { _id: 'sms1', templateName: 'Order Confirmation' },
        { _id: 'sms2', templateName: 'Delivery Update' },
        { _id: 'sms3', templateName: 'Payment Reminder' },
        { _id: 'sms4', templateName: 'Appointment Confirmation' }
      ],
      emailTemplates: [
        { _id: 'email1', templateName: 'Welcome Email' },
        { _id: 'email2', templateName: 'Order Receipt' },
        { _id: 'email3', templateName: 'Monthly Newsletter' },
        { _id: 'email4', templateName: 'Password Reset' }
      ],

      // Stepper related fields
      categoryStep: 1,
      linkWithSubLicense: false,
      collectDataBasedOnCategory: false,
      triggerNotifications: false,
      autoCreateDynamicForm: false,
      selectedFormId: null,

      // New state for selected templates
      allTemplates: [],
      selectedTemplatesList: [],
      selectedTemplatesListDetails: [],

      // Form connections state
      showConnectionsModal: false,
      showAddConnectionModal: false,
      selectedFormForConnections: null,
      formConnections: [],
      selectedModuleToConnect: null,
      connectionNotes: '',
      availableModules: [
        {
          id: 'workflow_steps',
          name: 'Workflow Steps',
          description: 'Integration with workflow management system',
          icon: 'mdi-workflow',
          iconColor: 'blue'
        },
        {
          id: 'case_management',
          name: 'Case Management',
          description: 'Link form to case creation and updates',
          icon: 'mdi-briefcase',
          iconColor: 'green'
        },
        {
          id: 'visit_tracking',
          name: 'Visit Tracking',
          description: 'Capture visit information and reports',
          icon: 'mdi-map-marker',
          iconColor: 'orange'
        },
        {
          id: 'employee_onboarding',
          name: 'Employee Onboarding',
          description: 'New employee registration and documentation',
          icon: 'mdi-account-plus',
          iconColor: 'purple'
        },
        {
          id: 'customer_feedback',
          name: 'Customer Feedback',
          description: 'Collect and manage customer feedback',
          icon: 'mdi-comment-account',
          iconColor: 'teal'
        },
        {
          id: 'audit_checklist',
          name: 'Audit Checklist',
          description: 'Quality assurance and audit processes',
          icon: 'mdi-clipboard-check',
          iconColor: 'red'
        }
      ]
    }
  },
  watch: {
    expandedCategory: {
      handler(newVal) {
        this.selectedFormId = this.categories[newVal]?.formId
        if (newVal !== null && this.questionnaire[newVal]) {
          this.questionCategory = this.getCategoryKey(this.questionnaire[newVal].categoryName)
          this.selectedSubcase = this.questionnaire[newVal].collectionSubCase
          this.selectedDataCategory = this.questionnaire[newVal].collectionDataCategory
          this.fetchDataCategories()
        }
      }
    },
    currentQuestionType(newType) {
      if (newType === 'DATA_CATEGORY') {
        this.fetchDataCategories()
      }
    },
    selectedSubcase(newSubcase) {
      if (newSubcase) {
        this.fetchDataCategories()
      }
      const dataCategoryType = this.questionTypes.find((type) => type.value === 'DATA_CATEGORY')
      if (newSubcase) {
        dataCategoryType.visible = true
      } else {
        dataCategoryType.visible = false
      }
    },
    selectedDataCategory: {
      handler(newVal) {
        this.toggleDataCategory()
      },
      immediate: true
    },
    // triggerNotifications: {
    //   handler(newVal) {
    //     if (!newVal) {
    //       this.selectedTemplatesList = []
    //     }
    //   },
    //   immediate: true
    // },
    selectedTemplatesList: {
      handler(newList) {
        this.selectedTemplatesListDetails = newList.map((el) => {
          return {
            _id: el?._id,
            templateName: el?.templateName,
            notificationType: el?.typesOfNotification
          }
        })
      },
      immediate: true
    }
  },
  async created() {
    await this.fetchCategories()

    if (this.questionnaire.length > 0) {
      this.expandedCategory = 0
      this.questionCategory = this.getCategoryKey(this.questionnaire[0].categoryName)
      this.selectedSubcase = this.questionnaire[0].collectionSubCase
      this.selectedDataCategory = this.questionnaire[0].collectionDataCategory || null
    } else {
      this.expandedCategory = null
      this.questionCategory = null
      this.selectedDataCategory = null
    }

    // Fetch all templates for notifications
    await this.fetchAllTemplates()
  },
  computed: {
    categories() {
      return this.questionnaire
    },
    selectedCategory() {
      return this.expandedCategory !== null ? this.questionnaire[this.expandedCategory] : null
    },
    getBannerImage() {
      if (this.selectedCategory?.images) {
        const banner = this.selectedCategory.images.find((img) => img.type === 'banner')
        return banner ? this.bannerLogoUrl(banner.path) : null
      }
      return null
    },
    getLogoImage() {
      if (this.selectedCategory?.images) {
        const logo = this.selectedCategory.images.find((img) => img.type === 'logo')
        return logo ? this.bannerLogoUrl(logo.path) : null
      }
      return null
    },
    isAddButtonEnable() {
      return !this.currentQuestionType || !this.questionText || !this.questionCategory
    },
    fieldMappings() {
      const subCase = this.subCases[0]?.subCase?.find(
        (subCase) => subCase.name === this.selectedSubcase
      )
      if (!subCase || !subCase.fieldMappings) return []

      const items = Object.entries(subCase.fieldMappings).map(([key, value]) => ({
        text: value?.displayName ? `${value?.displayName} - (${key})` : null,
        value: key
      }))
      const filtredItems = items.filter((item) => item.text)
      return filtredItems
    },
    isWhatsappTriggerAvailable() {
      return !!this.receivedData?.applications?.whatsapp?.waSystemToken
    },
    templateNameWithType() {
      return (item) => `${item.templateName} (${item.notificationType})`
    }
  },
  methods: {
    bannerLogoUrl(path) {
      return path ? `${process.env.VUE_APP_S3_BASE_URL}${path}` : null
    },
    getCategoryKey(categoryName) {
      return categoryName.trim().toLowerCase().replace(/\s+/g, '-')
    },

    getCategoryByKey(categoryKey) {
      return this.questionnaire.find((cat) => this.getCategoryKey(cat.categoryName) === categoryKey)
    },

    async fetchCategories() {
      try {
        this.isLoading = true
        const response = await this.$axios.get('/workforce/orgSetup/dynamic-forms')
        if (response.data && response.data.data) {
          this.questionnaire = response.data.data
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
        this.$toast.error('Failed to load questionnaire categories')
      } finally {
        this.isLoading = false
      }
    },
    titleCase(str) {
      return convertToTitleCase(str)
    },

    selectQuestionType(type) {
      this.currentQuestionType = type
    },

    async saveQuestion() {
      if (this.isAddButtonEnable) {
        this.$toast.error('Please select a category, question type, and enter question text')
        return
      }

      try {
        this.isLoading = true

        const question = {
          question: this.questionText,
          questionType: this.currentQuestionType,
          isRequired: this.isRequired,
          options: [],
          validationMessage: this.validationMessage || '',
          collectionKeyField: this.linkedKey,
          userResponse: null
        }

        if (
          this.currentQuestionType === 'CHECKBOX' ||
          this.currentQuestionType === 'RADIO' ||
          this.currentQuestionType === 'SELECT_OPTIONS'
        ) {
          if (!this.options || this.options.length === 0) {
            this.$toast.error('Please add at least one option for Checkbox/Radio question')
            return
          }
          question.options = [...this.options]
        } else if (this.currentQuestionType === 'DATA_CATEGORY') {
          const categoryOptions = this.dataCategories.map((cat) => {
            return { label: cat.text, value: cat.value }
          })
          question.options = JSON.stringify(categoryOptions)
          question.dataCategory = true
        }

        if (this.currentQuestionType === 'LOCAL_ATTACHMENTS') {
          question.allowedNoOfAttachments = this.maxAttachments
        }
        if (this.currentQuestionType === 'SERVICE_PLAN') {
          if (!this.paymentPlans || this.paymentPlans.length === 0) {
            this.$toast.error('Please add at least one payment plan')
            return
          }
          question.payment_Plan = [...this.paymentPlans]
          question.paymentLink = this.paymentLink || null
          question.payment_UPI_Id = this.payment_UPI_Id || null
          question.paymentIntegrationType = this.selectedPaymentType
          if (this.paymentQRImage) {
            question.options = [this.paymentQRImage.path]
          }
        }

        const category = this.getCategoryByKey(this.questionCategory)
        if (!category) {
          this.$toast.error('Category not found')
          return
        }

        // Prepare category data with updated questions
        const updatedQuestions = [...(category.questions || [])]

        if (this.editingQuestionIndex !== null) {
          // Update existing question
          if (this.editingQuestionId) {
            question._id = this.editingQuestionId
          }
          updatedQuestions[this.editingQuestionIndex] = question
        } else {
          // Add new question
          updatedQuestions.push(question)
        }

        // Prepare category data for API call
        const categoryData = {
          isPrivate: category.isPrivate,
          categoryName: category.categoryName,
          header: category.header || '',
          images: category.images || [],
          questions: updatedQuestions,
          collectionSubCase: category.collectionSubCase || '',
          collectionDataCategory: category.collectionDataCategory || null,
          enabledNotifications: category.enabledNotifications || null
        }

        // Make API call to update category with new/updated question
        const response = await this.$axios.put(
          `/workforce/orgSetup/dynamic-form/${category._id}`,
          categoryData
        )

        // Update local data
        const updatedCategory = response.data.data || categoryData
        const categoryIndex = this.questionnaire.findIndex((cat) => cat._id === category._id)
        if (categoryIndex !== -1) {
          this.$set(this.questionnaire, categoryIndex, updatedCategory)
        }

        this.$toast.success(
          this.editingQuestionIndex !== null
            ? 'Question updated successfully!'
            : 'Question added successfully!'
        )

        this.resetQuestionForm()
      } catch (error) {
        console.error('Error saving question:', error)
        this.$toast.error('Failed to save question')
      } finally {
        this.isLoading = false
      }
    },

    resetQuestionForm() {
      this.questionText = ''
      this.isRequired = false
      this.validationMessage = ''
      this.currentQuestionType = null
      this.editingQuestionIndex = null
      this.editingQuestionId = null
      this.maxAttachments = 1
      this.maxFileSize = 10
      this.mediaMaxSize = 20
      this.mediaQuality = 'Medium'
      this.options = []
      this.paymentQRImage = null
      this.paymentLink = ''
      this.payment_UPI_Id = ''
      this.paymentPlans = []
      this.linkedKey = null
    },
    resetCategoryForm() {
      this.newCategoryName = ''
      this.categoryHeader = ''
      this.selectedSubcase = null
      this.selectedDataCategory = null
      this.categoryImages = { logo: null, banner: null }
    },
    async confirmAddCategory() {
      const name = this.newCategoryName.trim()
      if (!name) {
        this.$toast.error('Please enter a category name')
        return
      }

      try {
        this.isLoading = true

        let enabledNotifications = []
        if (this.triggerNotifications && this.selectedTemplatesList.length > 0) {
          this.selectedTemplatesList.forEach((template) => {
            if (template.typesOfNotification && Array.isArray(template.typesOfNotification)) {
              template.typesOfNotification.forEach((notificationType) => {
                enabledNotifications.push({
                  typeOfNotification: notificationType,
                  templateId: [template._id],
                  templateName: template.templateName
                })
              })
            } else {
              enabledNotifications.push({
                typeOfNotification: 'WHATSAPP',
                templateId: [template._id],
                templateName: template.templateName
              })
            }
          })
        }

        const images = []
        if (this.categoryImages.logo) {
          images.push({ type: 'logo', path: this.categoryImages.logo.path })
        }
        if (this.categoryImages.banner) {
          images.push({ type: 'banner', path: this.categoryImages.banner.path })
        }

        const categoryData = {
          isPrivate: this.isPrivate,
          categoryName: name,
          header: this.categoryHeader || '',
          images: images,
          questions: [],
          collectionSubCase: this.selectedSubcase || '',
          collectionDataCategory: this.selectedDataCategory || null,
          enabledNotifications: enabledNotifications.length > 0 ? enabledNotifications : null
        }

        let categoryIndex = -1
        let savedCategory = null

        if (this.editingCategoryName) {
          categoryIndex = this.questionnaire.findIndex(
            (cat) => cat.categoryName === this.editingCategoryName
          )

          if (categoryIndex !== -1) {
            const existingCategory = this.questionnaire[categoryIndex]
            const oldSubcase = existingCategory.collectionSubCase

            categoryData.questions = existingCategory.questions || []

            if (oldSubcase !== this.selectedSubcase && categoryData.questions.length > 0) {
              categoryData.questions.forEach((question) => {
                question.collectionKeyField = null
              })
              this.$toast.info('Question field mappings have been reset due to license/team change')
            }

            const response = await this.$axios.put(
              `/workforce/orgSetup/dynamic-form/${existingCategory._id}`,
              categoryData
            )
            savedCategory = response.data.data || { ...categoryData, _id: existingCategory._id }

            this.$set(this.questionnaire, categoryIndex, savedCategory)
            this.$toast.success('Form updated successfully!')
          }
        } else {
          const response = await this.$axios.post('/workforce/orgSetup/dynamic-form', categoryData)
          savedCategory = response.data.data || categoryData

          this.questionnaire.push(savedCategory)
          categoryIndex = this.questionnaire.length - 1
          this.$toast.success('Form created successfully!')
        }

        if (this.autoCreateDynamicForm && savedCategory) {
          await this.autoGenerateQuestions(savedCategory)
        }

        this.showAddCategoryModal = false
        this.editingCategoryName = null
        this.subcaseChangeinfo = false

        this.$nextTick(() => {
          this.expandedCategory = categoryIndex
          this.questionCategory = this.getCategoryKey(savedCategory.categoryName)
        })
      } catch (error) {
        console.error('Error saving category:', error)
        this.$toast.error('Failed to save form')
      } finally {
        this.isLoading = false
      }
    },
    toggleDataCategory() {
      const dataCategoryType = this.questionTypes.find((type) => type.value === 'DATA_CATEGORY')
      if (dataCategoryType) {
        const currentCategory = this.selectedCategory
        if (currentCategory) {
          dataCategoryType.enabled = !currentCategory.collectionDataCategory
        }
      }
    },
    editCategory(categoryName) {
      const categoryIndex = this.questionnaire.findIndex((cat) => cat.categoryName === categoryName)
      if (categoryIndex !== -1) {
        const category = this.questionnaire[categoryIndex]
        this.newCategoryName = category.categoryName
        this.editingCategoryName = categoryName
        this.editCategoryId = category._id
        this.categoryHeader = category.header || ''
        this.isPrivate = category.isPrivate || false
        // Set stepper toggle values based on existing data
        this.linkWithSubLicense = !!category.collectionSubCase
        this.selectedSubcase = category.collectionSubCase || null
        this.collectDataBasedOnCategory = !!category.collectionDataCategory
        this.selectedDataCategory = category.collectionDataCategory || null

        // Fix notification templates population
        this.triggerNotifications = !!(
          category.enabledNotifications && category.enabledNotifications.length > 0
        )

        // Reset selected templates
        this.selectedTemplatesList = []
        this.selectedTemplatesListDetails = []

        // If category has enabled notifications, populate the templates
        if (category.enabledNotifications && category.enabledNotifications.length > 0) {
          const templateIds = category.enabledNotifications.flatMap((n) => n.templateId || [])
          this.selectedTemplatesList = this.whatsappTriggers.filter((t) =>
            templateIds.includes(t._id)
          )
        }

        // Add preview URLs for existing images
        this.categoryImages = {
          logo: category.images?.find((img) => img.type === 'logo') || null,
          banner: category.images?.find((img) => img.type === 'banner') || null
        }

        if (this.categoryImages.logo) {
          this.categoryImages.logo.preview = this.bannerLogoUrl(this.categoryImages.logo.path)
        }
        if (this.categoryImages.banner) {
          this.categoryImages.banner.preview = this.bannerLogoUrl(this.categoryImages.banner.path)
        }

        // Keep the current category expanded
        this.expandedCategory = categoryIndex

        // Reset stepper to first step
        this.categoryStep = 1
        this.showAddCategoryModal = true
      }
    },

    async deleteCategory(categoryName) {
      if (
        !confirm(
          'Are you sure you want to delete this category? All questions in this category will also be deleted.'
        )
      ) {
        return
      }

      try {
        this.isLoading = true

        const categoryIndex = this.questionnaire.findIndex(
          (cat) => cat.categoryName === categoryName
        )
        if (categoryIndex === -1) return

        const category = this.questionnaire[categoryIndex]

        await this.$axios.delete(`/workforce/orgSetup/dynamic-form/${category._id}`)

        this.questionnaire.splice(categoryIndex, 1)

        if (this.expandedCategory === categoryIndex) {
          this.expandedCategory = null
          this.questionCategory = null
        } else if (this.expandedCategory > categoryIndex) {
          this.expandedCategory = this.expandedCategory - 1
        }

        this.$toast.success('Form deleted successfully!')
      } catch (error) {
        console.error('Error deleting category:', error)
        this.$toast.error('Failed to delete form')
      } finally {
        this.isLoading = false
      }
    },

    editQuestion(categoryName, questionIndex) {
      const category = this.questionnaire.find((cat) => cat.categoryName === categoryName)
      if (!category) return

      const question = category.questions[questionIndex]
      if (!question) return

      // Set form values
      this.questionText = question.question
      this.questionCategory = this.getCategoryKey(categoryName)
      this.isRequired = question.isRequired
      this.validationMessage = question.validationMessage || ''
      this.editingQuestionIndex = questionIndex
      this.editingQuestionId = question._id
      this.currentQuestionType = question.questionType
      this.linkedKey = question.collectionKeyField || null

      // Set options for checkbox and radio types
      if (
        question.options &&
        (question.questionType === 'CHECKBOX' ||
          question.questionType === 'RADIO' ||
          question.questionType === 'SELECT_OPTIONS')
      ) {
        this.options = [...question.options]
      } else {
        this.options = []
      }

      // Set payment QR if available
      if (
        question.questionType === 'SERVICE_PLAN' &&
        question.paymentIntegrationType == 'QR_CODE' &&
        question.options &&
        question.options.length > 0
      ) {
        this.paymentQRImage = {
          path: question.options[0],
          preview: this.bannerLogoUrl(question.options[0])
        }
      } else {
        this.paymentQRImage = null
      }

      // Set specific options based on type
      if (question.questionType === 'LOCAL_ATTACHMENTS') {
        this.maxAttachments = question.allowedNoOfAttachments || 1
      }

      if (question.questionType === 'SERVICE_PLAN') {
        this.paymentLink = question.paymentLink || ''
        this.payment_UPI_Id = question.payment_UPI_Id || ''
      }

      // Set payment plans if available
      if (question.questionType === 'SERVICE_PLAN' && question.payment_Plan) {
        this.paymentPlans = [...question.payment_Plan]
      } else {
        this.paymentPlans = []
      }
    },

    async deleteQuestion(categoryName, questionIndex) {
      if (!confirm('Are you sure you want to delete this question?')) {
        return
      }

      try {
        this.isLoading = true

        const category = this.questionnaire.find((cat) => cat.categoryName === categoryName)
        if (!category) return

        const updatedQuestions = [...(category.questions || [])]
        updatedQuestions.splice(questionIndex, 1)

        const categoryData = {
          isPrivate: category.isPrivate,
          categoryName: category.categoryName,
          header: category.header || '',
          images: category.images || [],
          questions: updatedQuestions,
          collectionSubCase: category.collectionSubCase || '',
          collectionDataCategory: category.collectionDataCategory || null,
          enabledNotifications: category.enabledNotifications || null
        }

        const response = await this.$axios.put(
          `/workforce/orgSetup/dynamic-form/${category._id}`,
          categoryData
        )

        const updatedCategory = response.data.data || categoryData
        const categoryIndex = this.questionnaire.findIndex((cat) => cat._id === category._id)
        if (categoryIndex !== -1) {
          this.$set(this.questionnaire, categoryIndex, updatedCategory)
        }

        this.$toast.success('Question deleted successfully!')
      } catch (error) {
        console.error('Error deleting question:', error)
        this.$toast.error('Failed to delete question')
      } finally {
        this.isLoading = false
      }
    },

    getTypeIcon(type) {
      const typeConfig = this.questionTypes.find((t) => t.value === type)
      return typeConfig ? typeConfig.icon : 'mdi-help-circle'
    },

    getTypeColor(type) {
      const typeConfig = this.questionTypes.find((t) => t.value === type)
      return typeConfig ? typeConfig.color : 'grey'
    },

    formatType(type) {
      const typeConfig = this.questionTypes.find((t) => t.value === type)
      return typeConfig
        ? typeConfig.label
        : type.charAt(0).toUpperCase() + type.slice(1).toLowerCase()
    },

    getOptionsText(question) {
      if (question.questionType === 'LOCAL_ATTACHMENTS' && question.allowedNoOfAttachments) {
        return `Max: ${question.allowedNoOfAttachments} file(s)`
      }
      return ''
    },
    copyLinkToClipboard() {
      this.showCategoryMenu = true
    },
    copySpecificCategoryLink(categoryId, categoryName) {
      if (categoryId) {
        const link = `${APP_URL[process.env.VUE_APP_ENV]}/open/form/${
          this.orgData?.id
        }/${categoryId}`

        navigator.clipboard
          .writeText(link)
          .then(() => {
            this.$toast.success(`Link for "${categoryName}" copied to clipboard!`)
            this.showCategoryMenu = false
          })
          .catch((err) => {
            console.error('Failed to copy link: ', err)
            this.$toast.error('Failed to copy link to clipboard')
          })
      } else {
        this.$toast.warning(
          `Category "${categoryName}" doesn't have an ID yet. Save the questionnaire first.`
        )
      }
    },
    copyFormId(formId) {
      navigator.clipboard
        .writeText(formId)
        .then(() => {
          this.$toast.success(`FormId copied to clipboard!`)
          this.showCategoryMenu = false
        })
        .catch((err) => {
          console.error('Failed to copy link: ', err)
          this.$toast.error('Failed to copy link to clipboard')
        })
    },
    removeOption(index) {
      this.options.splice(index, 1)
    },
    addOption() {
      if (this.newOption && this.newOption.trim() !== '') {
        this.options.push(this.newOption.trim())
        this.newOption = ''
      }
    },
    async handleImageUpload(event, type) {
      const file = event.target.files[0]
      if (!file) return

      const formData = new FormData()
      formData.append('attachments', file)
      formData.append('type', type)
      formData.append('formId', this.editCategoryId)

      try {
        const response = await this.$axios.post('/workforce/org/logo', formData)

        if (response.data.success && response.data.url) {
          this.$set(this.categoryImages, type, {
            type: type,
            path: response.data.url.doc_physical_path,
            preview: URL.createObjectURL(file),
            name: file.name
          })
          this.$toast.success(
            `${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully!`
          )
        } else {
          this.$toast.error('Failed to upload image')
        }
      } catch (error) {
        console.error('Error uploading image:', error)
        this.$toast.error('Error uploading image')
      }
    },
    triggerFileInput(type) {
      if (type === 'logo') {
        this.$refs.logoInput.click()
      } else if (type === 'banner') {
        this.$refs.bannerInput.click()
      } else if (type === 'paymentQR') {
        this.$refs.paymentQRInput.click()
      }
    },
    removeImage(type) {
      this.$set(this.categoryImages, type, null)
    },
    addCategory() {
      this.resetCategoryForm()
      this.editingCategoryName = null
      // Reset stepper values
      this.categoryStep = 1
      this.linkWithSubLicense = false
      this.collectDataBasedOnCategory = false
      this.triggerNotifications = false
      this.showAddCategoryModal = true
    },
    async handlePaymentQRUpload(event) {
      const file = event.target.files[0]
      if (!file) return

      const formData = new FormData()
      formData.append('attachments', file)
      formData.append('type', 'payment_qr')

      // If editing a question, include the question ID
      if (this.editingQuestionId) {
        formData.append('questionId', this.editingQuestionId)
      }

      try {
        const response = await this.$axios.post('/workforce/org/logo', formData)

        if (response.data.success && response.data.url) {
          this.paymentQRImage = {
            path: response.data.url.doc_physical_path,
            preview: URL.createObjectURL(file),
            name: file.name
          }
          this.$toast.success('Payment QR code uploaded successfully!')
        } else {
          this.$toast.error('Failed to upload QR code')
        }
      } catch (error) {
        console.error('Error uploading QR code:', error)
        this.$toast.error('Error uploading QR code')
      }
    },
    removePaymentQR() {
      this.paymentQRImage = null
    },
    addPaymentPlan() {
      this.paymentPlans.push({
        planName: '',
        planDescription: '',
        planDuration: 0,
        planAmount: 0
      })
    },
    removePaymentPlan(index) {
      this.paymentPlans.splice(index, 1)
    },
    async updateQuestionOrder(newOrder) {
      if (!this.selectedCategory) return

      try {
        this.isLoading = true

        // Prepare category data for API call
        const categoryData = {
          isPrivate: this.selectedCategory.isPrivate,
          categoryName: this.selectedCategory.categoryName,
          header: this.selectedCategory.header || '',
          images: this.selectedCategory.images || [],
          questions: newOrder,
          collectionSubCase: this.selectedCategory.collectionSubCase || '',
          collectionDataCategory: this.selectedCategory.collectionDataCategory || null,
          enabledNotifications: this.selectedCategory.enabledNotifications || null
        }

        // Make API call to update category with new question order
        const response = await this.$axios.put(
          `/workforce/orgSetup/dynamic-form/${this.selectedCategory._id}`,
          categoryData
        )

        // Update local data
        const updatedCategory = response.data.data || categoryData
        const categoryIndex = this.questionnaire.findIndex(
          (cat) => cat._id === this.selectedCategory._id
        )
        if (categoryIndex !== -1) {
          this.$set(this.questionnaire, categoryIndex, updatedCategory)
        }

        this.$toast.success('Question order updated successfully!')
      } catch (error) {
        console.error('Error updating question order:', error)
        this.$toast.error('Failed to update question order')
      } finally {
        this.isLoading = false
      }
    },
    getCategoryBannerImage(category) {
      if (!category.images) return null
      const banner = category.images.find((img) => img.type === 'banner')
      return banner ? this.bannerLogoUrl(banner.path) : null
    },
    getCategoryLogoImage(category) {
      if (!category.images) return null
      const logo = category.images.find((img) => img.type === 'logo')
      return logo ? this.bannerLogoUrl(logo.path) : null
    },
    resetQuestionCollectionKeyFields() {
      if (this.selectedCategory && this.selectedCategory.questions) {
        this.linkedKey = null
        this.subcaseChangeinfo = true
        this.fetchDataCategories()
      }
    },
    async fetchDataCategories() {
      if (!this.selectedSubcase) return

      try {
        // Find the subcase in the available subcases
        const subCase = this.subCases[0]?.subCase?.find(
          (subCase) => subCase.name === this.selectedSubcase
        )

        if (subCase && subCase.dataCategories) {
          this.dataCategories = subCase.dataCategories.map((cat) => ({
            text: cat.name,
            value: cat._id
          }))
        } else {
          this.dataCategories = []
        }
      } catch (error) {
        console.error('Error fetching data categories:', error)
        this.$toast.error('Failed to load data categories')
        this.dataCategories = []
      }
    },
    async getwhatsappTriggers() {
      try {
        const response = await this.$axios.get('/workforce/whatsapp/templates/assign')
        this.whatsappTriggers = response.data.templates || []
        console.log(this.whatsappTriggers)
      } catch (error) {
        console.error('Error fetching whatsapp templates:', error)
        this.$toast.error('Failed to load whatsapp templates')
      }
    },
    async fetchAllTemplates() {
      // Combine all templates with notificationType
      const whatsapp = (this.whatsappTriggers || []).map((t) => ({
        ...t,
        notificationType: 'WHATSAPP'
      }))
      const sms = (this.smsTemplates || []).map((t) => ({ ...t, notificationType: 'SMS' }))
      const email = (this.emailTemplates || []).map((t) => ({ ...t, notificationType: 'EMAIL' }))
      this.allTemplates = [...whatsapp, ...sms, ...email]
    },
    handleLicenseStepComplete() {
      if (this.linkWithSubLicense && !this.selectedSubcase) {
        this.$toast.error('Please select a License/Team')
        return
      }
      if (this.selectedSubcase) {
        this.categoryStep = 3
      } else {
        this.categoryStep = 5
      }
    },
    handleDataCategoryStepComplete() {
      if (this.collectDataBasedOnCategory && !this.selectedDataCategory) {
        this.$toast.error('Please select a Data Category')
        return
      }
      this.categoryStep = 4
    },
    handleAutoFormComplete() {
      if (this.autoCreateDynamicForm && (!this.linkWithSubLicense || !this.selectedSubcase)) {
        this.$toast.error(
          'Auto-create requires a linked License/Team. Please go back and select one.'
        )
        return
      }

      this.categoryStep = 5
    },
    async autoGenerateQuestions(category) {
      if (!this.autoCreateDynamicForm || !this.selectedSubcase || !category) {
        return
      }

      try {
        const subCase = this.subCases[0]?.subCase?.find(
          (subCase) => subCase.name === this.selectedSubcase
        )

        if (!subCase || !subCase.fieldMappings) {
          this.$toast.warning('No field mappings found for the selected License/Team')
          return
        }

        // Generate questions based on field mappings
        const generatedQuestions = []
        Object.entries(subCase.fieldMappings).forEach(([key, value]) => {
          if (value?.displayName) {
            let questionType = 'TEXT'
            let isRequired = false
            let validationMessage = ''

            if (['tenure', 'pos', 'emiAmount'].includes(key)) {
              questionType = 'NUMBER'
            }

            if (['caseNo', 'crn'].includes(key)) {
              isRequired = true
              validationMessage = `${value.displayName} is required`
            }

            const question = {
              question: value.displayName,
              questionType: questionType,
              isRequired: isRequired,
              options: [],
              validationMessage: validationMessage,
              collectionKeyField: key,
              userResponse: null
            }

            generatedQuestions.push(question)
          }
        })

        if (generatedQuestions.length > 0) {
          // Combine existing questions with generated ones
          const updatedQuestions = [...(category.questions || []), ...generatedQuestions]

          // Prepare category data for API call
          const categoryData = {
            isPrivate: category.isPrivate,
            categoryName: category.categoryName,
            header: category.header || '',
            images: category.images || [],
            questions: updatedQuestions,
            collectionSubCase: category.collectionSubCase || '',
            collectionDataCategory: category.collectionDataCategory || null,
            enabledNotifications: category.enabledNotifications || null
          }

          // Make API call to update category with generated questions
          const response = await this.$axios.put(
            `/workforce/orgSetup/dynamic-form/${category._id}`,
            categoryData
          )

          // Update local data
          const updatedCategory = response.data.data || categoryData
          const categoryIndex = this.questionnaire.findIndex((cat) => cat._id === category._id)
          if (categoryIndex !== -1) {
            this.$set(this.questionnaire, categoryIndex, updatedCategory)
          }

          this.$toast.success(
            `${generatedQuestions.length} questions auto-generated from field mappings`
          )
        } else {
          this.$toast.warning('No valid field mappings found to generate questions')
        }
      } catch (error) {
        console.error('Error auto-generating questions:', error)
        this.$toast.error('Failed to auto-generate questions')
      }
    },

    // Form connections methods
    showFormConnections(category) {
      this.selectedFormForConnections = category
      this.formConnections = this.getDummyConnections(category.formId)
      this.showConnectionsModal = true
    },

    getConnectionCount(formId) {
      return this.getDummyConnections(formId).length
    },

    getDummyConnections(formId) {
      // Generate dummy connections based on formId
      const allPossibleConnections = [
        {
          id: 'conn_1',
          moduleName: 'Workflow Steps',
          description: 'Form integrated with step-by-step workflow processes for case management',
          icon: 'mdi-workflow',
          iconColor: 'blue',
          connectedDate: '2024-01-15',
          connectedBy: 'John Doe',
          usageCount: 245,
          status: 'active',
          moduleId: 'workflow_steps'
        },
        {
          id: 'conn_2',
          moduleName: 'Case Management',
          description: 'Automatically creates new cases when form is submitted',
          icon: 'mdi-briefcase',
          iconColor: 'green',
          connectedDate: '2024-02-03',
          connectedBy: 'Sarah Wilson',
          usageCount: 189,
          status: 'active',
          moduleId: 'case_management'
        },
        {
          id: 'conn_3',
          moduleName: 'Visit Tracking',
          description: 'Captures location and visit details during field operations',
          icon: 'mdi-map-marker',
          iconColor: 'orange',
          connectedDate: '2024-01-28',
          connectedBy: 'Mike Johnson',
          usageCount: 156,
          status: 'inactive',
          moduleId: 'visit_tracking'
        },
        {
          id: 'conn_4',
          moduleName: 'Employee Onboarding',
          description: 'Used for new employee registration and document collection',
          icon: 'mdi-account-plus',
          iconColor: 'purple',
          connectedDate: '2024-02-10',
          connectedBy: 'Lisa Chen',
          usageCount: 67,
          status: 'active',
          moduleId: 'employee_onboarding'
        },
        {
          id: 'conn_5',
          moduleName: 'Customer Feedback',
          description: 'Collects customer satisfaction and feedback data',
          icon: 'mdi-comment-account',
          iconColor: 'teal',
          connectedDate: '2024-01-20',
          connectedBy: 'David Brown',
          usageCount: 312,
          status: 'active',
          moduleId: 'customer_feedback'
        },
        {
          id: 'conn_6',
          moduleName: 'Audit Checklist',
          description: 'Quality assurance and compliance audit processes',
          icon: 'mdi-clipboard-check',
          iconColor: 'red',
          connectedDate: '2024-02-05',
          connectedBy: 'Emma Davis',
          usageCount: 89,
          status: 'inactive',
          moduleId: 'audit_checklist'
        }
      ]

      // Return random 2-4 connections for each form
      const connectionCount = Math.floor(Math.random() * 3) + 2 // 2-4 connections
      const shuffled = allPossibleConnections.sort(() => 0.5 - Math.random())
      return shuffled.slice(0, connectionCount)
    },

    viewConnectionDetails(connection) {
      this.$toast.info(`Viewing details for ${connection.moduleName}`)
      // Here you would typically navigate to the connection details page
    },

    editConnection(connection) {
      this.$toast.info(`Editing connection to ${connection.moduleName}`)
      // Here you would open an edit modal or navigate to edit page
    },

    async disconnectForm(connection) {
      if (
        !confirm(`Are you sure you want to disconnect this form from ${connection.moduleName}?`)
      ) {
        return
      }

      try {
        // Remove connection from the list
        const index = this.formConnections.findIndex((conn) => conn.id === connection.id)
        if (index !== -1) {
          this.formConnections.splice(index, 1)
        }

        this.$toast.success(`Form disconnected from ${connection.moduleName}`)
      } catch (error) {
        console.error('Error disconnecting form:', error)
        this.$toast.error('Failed to disconnect form')
      }
    },

    async connectFormToModule() {
      if (!this.selectedModuleToConnect) {
        this.$toast.error('Please select a module to connect')
        return
      }

      try {
        const selectedModule = this.availableModules.find(
          (m) => m.id === this.selectedModuleToConnect
        )

        // Create new connection
        const newConnection = {
          id: `conn_${Date.now()}`,
          moduleName: selectedModule.name,
          description: selectedModule.description,
          icon: selectedModule.icon,
          iconColor: selectedModule.iconColor,
          connectedDate: new Date().toISOString().split('T')[0],
          connectedBy: 'Current User', // Replace with actual user
          usageCount: 0,
          status: 'active',
          moduleId: selectedModule.id,
          notes: this.connectionNotes
        }

        this.formConnections.push(newConnection)

        this.$toast.success(`Form connected to ${selectedModule.name}`)
        this.showAddConnectionModal = false
        this.selectedModuleToConnect = null
        this.connectionNotes = ''
      } catch (error) {
        console.error('Error connecting form:', error)
        this.$toast.error('Failed to connect form to module')
      }
    }
  },
  async mounted() {
    await this.getwhatsappTriggers()
  }
}
</script>

<!-- Add CSS for the selected category and banner/logo styling -->
<style scoped>
/* Custom styles */
.question-card {
  transition: all 0.3s ease;
}
.question-card:hover {
  /* transform: translateY(-2px); */
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.question-type-selector {
  transition: all 0.2s ease;
}
.question-type-selector:hover {
  background-color: #f0f9ff;
  border-color: #3b82f6;
}
.drag-handle {
  cursor: move;
}
.preview-container {
  /* min-height: 200px; */
  border: 2px dashed #e5e7eb;
  transition: all 0.3s ease;
}
.preview-container:hover {
  border-color: #3b82f6;
}
.selected-category {
  border: 2px solid #21427d !important;
}
.banner-container {
  position: relative;
  width: 100%;
  margin-bottom: 1rem;
}

/* Custom styling for stepper completed steps */
::v-deep .v-stepper__step--complete .v-stepper__step__step {
  background-color: #4caf50 !important; /* Green color for completed steps */
}

::v-deep .v-stepper__step--complete .v-stepper__label {
  color: #4caf50 !important; /* Green color for completed step labels */
}

::v-deep .v-stepper__step--active .v-stepper__step__step {
  background-color: #1976d2 !important; /* Keep active step blue */
}

/* Connection modal styles */
.connection-card {
  transition: all 0.3s ease;
}

.connection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.connection-status-active {
  background: linear-gradient(45deg, #4caf50, #66bb6a);
}

.connection-status-inactive {
  background: linear-gradient(45deg, #ff9800, #ffb74d);
}

/* Module selection styling */
::v-deep .v-select__selection {
  display: flex;
  align-items: center;
}

/* Connection button hover effects */
.connection-action-btn {
  transition: all 0.2s ease;
}

.connection-action-btn:hover {
  transform: scale(1.05);
}

/* Connection badge styling */
.connection-badge {
  min-width: 18px !important;
  height: 18px !important;
  font-size: 10px !important;
  font-weight: bold !important;
}
</style>
