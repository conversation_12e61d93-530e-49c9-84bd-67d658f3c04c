<template>
  <div>
    <div class="py-5 overflow-auto max-h-[70vh]">
      <div>
        <div class="border border-blue-200 bg-white flex dark-bg-custom">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-icon v-bind="attrs" v-on="on" class="ml-4"
                >mdi-information</v-icon
              >
            </template>
            <span>{{ infoMessage }}</span>
          </v-tooltip>
          <v-container fluid class="flex justify-between">
            <v-combobox
              v-model="meetingOptions"
              :items="comboBoxOptions"
              hide-selected
              hint="type and hit enter"
              :label="label"
              persistent-hint
              multiple
              small-chips
              @input="emitComboBoxData"
            >
              <template v-slot:selection="{ item }">
                <v-chip @click="removeChip(item, meetingOptions)">
                  {{ item }}
                  <v-icon small class="ml-2">mdi-close-circle</v-icon>
                </v-chip>
              </template>
            </v-combobox>
          </v-container>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      meetingOptions: [],
    };
  },
  props: {
    receivedData: {
      type: Object,
      default: null,
    },
    heading: String,
    infoMessage: String,
    comboBoxOptions: Array,
    label: String,
  },
  watch: {
    comboBoxOptions(newVal) {
      if (newVal !== null) this.meetingOptions = newVal;
    },
  },
  methods: {
    removeChip(item, options) {
      const index = options.indexOf(item);
      if (index !== -1) {
        options.splice(index, 1);
      }
      this.emitComboBoxData();
    },
    emitComboBoxData() {
      const obj = {
        title: this.heading.toUpperCase().split(" ").join("_"),
        value: this.meetingOptions,
      };
      this.$emit("comboBoxValue", obj);
    },
  },
  mounted() {
    this.meetingOptions = this.$props.comboBoxOptions || [];
  },
};
</script>
<style></style>
