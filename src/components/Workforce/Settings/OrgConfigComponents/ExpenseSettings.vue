<template>
  <div>
    <div v-if="hasExpenseData && isExpenseEnabled" class="p-5 overflow-auto max-h-[70vh]">
      <div>
        <div class="">
          <TravelAndAllowance
            :travelArray="travelArray"
            :allowanceArray="allowanceArray"
            :travelModes="travelModes"
            :allowanceTypes="allowanceTypes"
            @updateTravelArray="updateTravelArray"
            @updateAllowanceArray="updateAllowanceArray"
            :Expense="'Expense'"
          />
        </div>
      </div>
    </div>
    <no-data
      v-else
      title="Nothing to Display"
      :subTitle="`No active template enabled for the organization`"
      :btnText="`Set Expense Template`"
      :btnAction="openDrawer"
    />
  </div>
</template>
<script>
import TravelAndAllowance from '@/components/Workforce/TravelAndAllowance'
import NoData from '@/components/NoData'
import { isEqual, cloneDeep } from 'lodash'
export default {
  data() {
    return {
      travelArray: [{ mode: '', pricePerKm: null, min: null, max: null }],
      allowanceArray: [{ type: '', amount: null }],
      travelModes: [
        {
          text: 'Bus',
          value: 'BUS',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'Train',
          value: 'TRAIN',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'Toll',
          value: 'TOLL',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'AutoRickshaw',
          value: 'AUTORICKSHAW',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'Taxi',
          value: 'TAXI',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'Bike',
          value: 'BIKE',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'Fuel Bill',
          value: 'FUEL_BILL',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'Other Expenses',
          value: 'OTHER_EXPENSES',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        }
      ],
      allowanceTypes: [
        {
          text: 'In-Station Work',
          value: 'IN_STATION_WORK',
          type: '',
          amount: null
        },
        {
          text: 'Same Day Travel',
          value: 'SAME_DAY_TRAVEL',
          type: '',
          amount: null
        },
        {
          text: 'Overnight Travel',
          value: 'OVERNIGHT_TRAVEL',
          type: '',
          amount: null
        },
        {
          text: 'Entertainment allowance',
          value: 'ENTERTAINMENT_ALLOWANCE',
          type: '',
          amount: null
        },
        {
          text: 'Travel allowance',
          value: 'TRAVEL_ALLOWANCE',
          type: '',
          amount: null
        },
        {
          text: 'Dearness allowance',
          value: 'DEARNESS_ALLOWANCE',
          type: '',
          amount: null
        },
        { text: 'Medicine', value: 'MEDICINE', type: '', amount: '' },
        {
          text: 'Education Allowance',
          value: 'EDUCATION_ALLOWANCE',
          type: '',
          amount: null
        },
        { text: 'House', value: 'HOUSE', type: '', amount: null },
        { text: 'Transport', value: 'TRANSPORT', type: '', amount: null }
      ]
    }
  },
  components: {
    TravelAndAllowance,
    NoData
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    },
    orgData: Object
  },
  watch: {
    receivedData(newVal) {
      if (newVal !== null) {
        this.allowanceArray = newVal?.orgAllowances
        this.travelArray = newVal?.travelModes
      }
    }
  },
  computed: {
    isExpenseEnabled() {
      return this.orgData?.organization_config?.is_corporate_expense_enabled
    },
    originalTravelArray() {
      return cloneDeep(this.travelModes)
    }
  },
  methods: {
    updateTravelArray(newArray) {
      this.travelArray = newArray

      if (!isEqual(this.travelArray, this.originalTravelArray)) {
        this.$emit('travelData', this.travelArray)
      }
    },
    updateAllowanceArray(newArray) {
      this.allowanceArray = newArray
      this.$emit('allowanceData', this.allowanceArray)
    }
  },
  mounted() {
    this.allowanceArray = this.receivedData?.orgAllowances
    this.travelArray = this.receivedData?.travelModes
  }
}
</script>
<style></style>
