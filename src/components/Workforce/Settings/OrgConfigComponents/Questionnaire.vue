<template>
  <div>
    <div v-if="isQuestionnaireEnable" :class="{ 'p-5 overflow-auto max-h-[70vh]': !orgMapping }">
      <div class="mb-3">
        <p class="font-semibold mb-0">Add a new question for your Questionnaire</p>
      </div>
      <div>
        <div class="border border-blue-200 bg-white dark-bg-custom">
          <div class="mt-2">
            <v-text-field
              v-model="question"
              label="Type your question here! ex: Is customer satisfied with our service?"
              class="px-3"
            >
            </v-text-field>
          </div>
          <v-container fluid class="flex justify-between">
            <v-select
              v-model="questionType"
              :items="allowedQuestionsTypes"
              label="Select type of field"
            >
            </v-select>
          </v-container>
          <v-container
            fluid
            class="d-flex justify-space-between px-3"
            v-if="canHaveAttachments(questionType)"
          >
            <v-row align="center" no-gutters>
              <v-col>
                <p class="mb-0">Number of attachments</p>
              </v-col>
              <v-col class="d-flex justify-end align-center">
                <v-btn
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text"
                  small
                  :disabled="count <= 1"
                  @click="decreaseCount"
                  >-</v-btn
                >
                <div class="mx-4 border border-primary rounded-md d-flex align-center px-2">
                  <span class="p-1">{{ count }}</span>
                </div>
                <v-btn
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text"
                  small
                  :disabled="count >= 6"
                  @click="increaseCount"
                  >+</v-btn
                >
              </v-col>
            </v-row>
          </v-container>
          <div v-if="questionType === 'CHECKBOX' || questionType === 'RADIO'" class="flex pl-4">
            <v-combobox
              v-model="options"
              :items="optionsItems"
              :search-input.sync="searchOption"
              hide-selected
              hint="type and hit enter for adding new option"
              label="Add options"
              persistent-hint
              multiple
              small-chips
            >
              <template v-slot:selection="{ item }">
                <v-chip @click="removeChip(item, options)">
                  {{ item }}
                  <v-icon small class="ml-2">mdi-close-circle</v-icon>
                </v-chip>
              </template>
            </v-combobox>
          </div>
          <div class="flex align-center" v-if="questionType">
            <div class="ml-3">Is this question mandatory to answer by user?</div>
            <div class="flex align-center justify-end">
              <v-label>No</v-label>
              <v-switch
                :ripple="false"
                class="mr-10 ml-5 mb-1"
                inset
                dense
                label="Yes"
                v-model="isRequired"
              ></v-switch>
            </div>
          </div>
          <div>
            <v-text-field
              label="Validation Message"
              v-model="validationMessage"
              v-if="isRequired"
              class="px-3"
            ></v-text-field>
          </div>
          <div class="flex align-center" v-if="questionType">
            <div class="ml-3">Is this question only visible once per case?</div>
            <div class="flex align-center justify-end">
              <v-label>No</v-label>
              <v-switch
                :ripple="false"
                class="mr-10 ml-5 mb-1"
                inset
                dense
                label="Yes"
                v-model="oncePerCase"
              ></v-switch>
            </div>
          </div>
        </div>

        <div class="my-4 flex justify-end">
          <v-menu v-model="cloneMenuVisible" offset-y :close-on-content-click="false">
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                v-on="on"
                :color="$vuetify.theme.currentTheme.secondary"
                v-show="canEdit"
                class="white--text mr-3"
                small
                v-if="orgMapping"
                :disabled="!predefinedOptions.length"
              >
                <v-icon>mdi-content-copy</v-icon> Clone Questions
              </v-btn>
            </template>

            <v-list>
              <v-list-item
                v-for="(option, index) in predefinedOptions"
                :key="index"
                @click="selectPredefinedOption(option)"
              >
                <v-list-item-title>{{ option.key }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-btn
            :color="
              isClearAll ? $vuetify.theme.currentTheme.success : $vuetify.theme.currentTheme.error
            "
            v-show="canEdit"
            class="white--text mr-3"
            @click="clearAllQuestions"
            small
          >
            <v-icon>{{ isClearAll ? 'mdi-undo' : 'mdi-delete' }}</v-icon>
            {{ isClearAll ? 'Undo Clear' : 'Clear All Questions' }}
          </v-btn>
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            v-show="canEdit"
            class="white--text"
            @click="addQuestion"
            small
            ><v-icon class="">mdi-plus</v-icon>Add question to below questions list</v-btn
          >
        </div>

        <div v-if="questionnaire?.length" class="mb-3">
          <p class="font-semibold mb-0">Questions</p>
        </div>
        <div v-for="(questionData, index) in questionnaire" :key="index">
          <div
            :class="{
              'border-2 border-red-500': errors[questionData.question]
            }"
            class="border border-blue-400 bg-white dark-bg-custom mt-4"
          >
            <div class="flex justify-between mt-4 mb-2 px-3">
              <strong>({{ index + 1 }})</strong>
              <v-icon style="color: rgb(219, 77, 77)" @click="removeQuestion(index)"
                >mdi-delete</v-icon
              >
            </div>
            <div class="mt-2">
              <v-text-field
                v-model="questionData.question"
                label="Question"
                class="px-3"
                @input="onChange(questionData)"
              >
              </v-text-field>
            </div>
            <v-container fluid class="flex justify-between">
              <v-select
                v-model="questionData.questionType"
                :items="allowedQuestionsTypes"
                label="Type of field"
                @change="onChange(questionData)"
              >
              </v-select>
            </v-container>
            <v-container
              fluid
              class="d-flex justify-space-between px-3"
              v-if="canHaveAttachments(questionData.questionType)"
            >
              <v-row align="center" no-gutters>
                <v-col>
                  <p class="mb-0">Number of attachments</p>
                </v-col>
                <v-col class="d-flex justify-end align-center">
                  <v-btn
                    :color="$vuetify.theme.currentTheme.primary"
                    class="white--text"
                    small
                    :disabled="questionData?.allowedNoOfAttachments <= 1"
                    @click="decreaseCountInLoop(index)"
                    >-</v-btn
                  >
                  <div class="mx-4 border border-primary rounded-md d-flex align-center px-2">
                    <span class="p-1">{{ questionData.allowedNoOfAttachments || 1 }}</span>
                  </div>
                  <v-btn
                    :color="$vuetify.theme.currentTheme.primary"
                    class="white--text"
                    small
                    :disabled="questionData?.allowedNoOfAttachments >= 6"
                    @click="increaseCountInLoop(index)"
                    >+</v-btn
                  >
                </v-col>
              </v-row>
            </v-container>
            <div
              v-if="
                questionData.questionType === 'CHECKBOX' || questionData.questionType === 'RADIO'
              "
              class="flex pl-4"
            >
              <v-combobox
                v-model="questionData.options"
                :items="optionsItems"
                :search-input.sync="searchOption"
                hide-selected
                hint="type and hit enter for adding new option"
                label="Add options"
                persistent-hint
                multiple
                small-chips
                @input="onChange(questionData)"
              >
                <template v-slot:selection="{ item }">
                  <v-chip @click="removeChip(item, questionData.options)">
                    {{ item }}
                    <v-icon small class="ml-2">mdi-close-circle</v-icon>
                  </v-chip>
                </template>
              </v-combobox>
            </div>
            <div class="flex align-center" v-if="questionData.questionType">
              <div class="ml-3">Is this question mandatory to answer by user?</div>
              <div class="flex align-center justify-end">
                <v-label>No</v-label>
                <v-switch
                  :ripple="false"
                  class="mr-10 ml-5 mb-1"
                  inset
                  dense
                  label="Yes"
                  v-model="questionData.isRequired"
                  @change="onChange(questionData)"
                ></v-switch>
              </div>
            </div>
            <div>
              <v-text-field
                label="Validation Message"
                @input="() => changeValidMessage(questionData.validationMessage, index)"
                v-model="questionData.validationMessage"
                v-if="questionData.isRequired"
                class="px-3"
              ></v-text-field>
            </div>
            <div class="flex align-center">
              <div class="ml-3">Is this question only visible once per case?</div>
              <div class="flex align-center justify-end">
                <v-label>No</v-label>
                <v-switch
                  :ripple="false"
                  class="mr-10 ml-5 mb-1"
                  inset
                  dense
                  label="Yes"
                  v-model="questionData.oncePerCase"
                ></v-switch>
              </div>
            </div>

            <span class="text-red-500 text-sm p-3">{{ errors[questionData.question] || '' }}</span>
          </div>
        </div>
      </div>
    </div>
    <no-data
      v-else
      title="Nothing to Display"
      :subTitle="`Enable questionnaire from the task settings`"
    />
  </div>
</template>
<script>
import NoData from '@/components/NoData'
export default {
  name: 'questionnaireComponent',
  data() {
    return {
      question: '',
      questionType: null,
      options: [],
      isRequired: false,
      oncePerCase: false,
      validationMessage: 'This field is mandatory',
      allowedQuestionsTypes: [],
      optionsItems: [],
      questionnaire: [],
      searchOption: null,
      count: 1,
      cloneMenuVisible: false,
      isClearAll: false,
      prevQuestions: []
    }
  },
  components: {
    NoData
  },
  props: {
    receivedData: {
      type: Object,
      default: null
    },
    errors: {
      type: Object,
      default: () => ({})
    },
    orgMapping: {
      type: String,
      default: ''
    },
    subCaseQuestions: Array,
    questionTypes: Array,
    outcomeQuestions: Object
  },
  watch: {
    errors: {
      handler(newVal) {
        this.errors = newVal || {}
      },
      immediate: true
    },
    receivedData(newVal) {
      if (newVal !== null) {
        this.allowedQuestionsTypes = newVal?.allowedQuestionTypes
        this.questionnaire = newVal?.questionnaire || []
      }
    },
    subCaseQuestions: {
      handler(newArray) {
        if (newArray) {
          this.questionnaire = newArray
        }
      },
      immediate: true
    }
  },
  computed: {
    canEdit() {
      return true
    },
    isQuestionnaireEnable() {
      if (this.receivedData?.taskCompletionSteps?.custom) {
        return this.receivedData?.taskCompletionSteps?.custom?.includes('QUESTIONNAIRE')
      }
      return true
    },
    attachmentAllowedTypes() {
      return ['VIDEO_CAPTURE', 'AUDIO_CAPTURE', 'LOCAL_ATTACHMENTS', 'CLICK_PICTURES']
    },
    predefinedOptions() {
      if (this.$props.outcomeQuestions) {
        const optionArray = Object.entries(this.$props.outcomeQuestions)?.map(([key, obj]) => {
          return { key, value: obj }
        })
        return optionArray
      }
      return []
    }
  },
  methods: {
    async addQuestion() {
      if (this.question != '' && this.questionType != '') {
        this.questionnaire.push({
          question: this.question,
          questionType: this.questionType,
          options: this.options || [],
          isRequired: this.isRequired,
          oncePerCase: this.oncePerCase,
          allowedNoOfAttachments: this.count,
          validationMessage: this.isRequired && this.validationMessage ? this.validationMessage : ''
        })
        this.isClearAll = false
        this.question = ''
        this.questionType = ''
        this.options = []
        this.isRequired = false
        this.oncePerCase = false
        this.validationMessage = 'This field is mandatory'
        this.$emit('questions', this.questionnaire)
      } else {
        this.$toast.error('Please fill all the details')
      }
    },
    clearAllQuestions() {
      if (!this.isClearAll) {
        this.prevQuestions = [...this.questionnaire]
        this.questionnaire = []
      } else {
        this.questionnaire = [...this.prevQuestions]
      }
      this.isClearAll = !this.isClearAll
      this.$emit('questions', this.questionnaire)
    },
    toggleCloneMenu() {
      this.cloneMenuVisible = !this.cloneMenuVisible
    },
    selectPredefinedOption(option) {
      this.$emit('clonedQuestions', option)
      this.cloneMenuVisible = false
    },
    removeChip(item, options) {
      const index = options.indexOf(item)
      if (index !== -1) {
        options.splice(index, 1)
      }
      this.$emit('questions', this.questionnaire)
    },
    removeQuestion(index) {
      this.questionnaire.splice(index, 1)
      this.$emit('questions', this.questionnaire)
    },
    changeValidMessage(message, index) {
      this.questionnaire[index].validationMessage = message
      this.$emit('questions', this.questionnaire)
    },
    decreaseCount() {
      if (this.count > 1) this.count--
    },
    increaseCount() {
      if (this.count < 6) this.count++
    },
    decreaseCountInLoop(index) {
      if (this.questionnaire[index]?.allowedNoOfAttachments > 1) {
        this.questionnaire[index].allowedNoOfAttachments--
        this.$emit('questions', this.questionnaire)
      }
    },
    increaseCountInLoop(index) {
      if (this.questionnaire[index]?.allowedNoOfAttachments < 6) {
        this.questionnaire[index].allowedNoOfAttachments++
        this.$emit('questions', this.questionnaire)
      }
    },
    onChange() {
      this.$emit('questions', this.questionnaire)
    },
    canHaveAttachments(type) {
      return this.attachmentAllowedTypes.includes(type)
    }
  },
  mounted() {
    this.allowedQuestionsTypes = this.receivedData?.allowedQuestionTypes || this.questionTypes
    this.questionnaire = this.receivedData?.questionnaire || []
    if (this.subCaseQuestions && this.subCaseQuestions.length) {
      this.questionnaire = this.subCaseQuestions
    }
  }
}
</script>
<style></style>
