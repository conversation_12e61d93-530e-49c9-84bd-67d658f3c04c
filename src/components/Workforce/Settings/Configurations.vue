<template>
  <div class="lead-details-container bg-gray-100 dark-bg-custom">
    <div>
      <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
        <template v-for="tab in visibleTabs" v-slot:[tab.key]="">
          <div v-if="!isLoad">
            <v-main class="bg-[#F7FAFB] dark-bg-default" style="padding: 0px">
              <component
                :is="tab.component"
                @refetchData="handleRefetchData"
                ref="configComponent"
                :receivedData="receivedData"
                :orgData="orgData"
                @configDataReceived="handleDataReceived"
              />
            </v-main>
          </div>
          <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
            <v-progress-circular
              :color="$vuetify.theme.currentTheme.primary"
              indeterminate
              :size="50"
              :width="5"
            ></v-progress-circular>
            <div class="mt-2"><span>Please Wait...</span></div>
          </div>
        </template>
      </ActitiveTabcomponent>
    </div>
  </div>
</template>

<script>
import AppConfiguration from './AppConfiguration.vue'
import OrgConfig from './OrgConfigComponents'
import AdminDashboard from './AdminDashboardCustomization.vue'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'

export default {
  name: 'ConfigurationComponent',
  layout: 'Workforce/default',
  components: {
    AppConfiguration,
    OrgConfig,
    AdminDashboard,
    ActitiveTabcomponent
  },
  data() {
    return {
      tabs: 0,
      isLoad: true,
      receivedData: null,
      orgData: null
    }
  },
  computed: {
    tabItems() {
      const tabItems = [
        {
          key: 'OrgConfig',
          label: 'Organization',
          icon: 'mdi-office-building',
          component: 'OrgConfig',
          visible: true
        },
        {
          key: 'AppConfiguration',
          label: 'Configure Employee Application',
          icon: 'mdi-application-cog',
          component: 'AppConfiguration',
          visible: true
        },
        {
          key: 'AdminDashboardCustomization',
          label: 'Configure Admin Dashboard',
          icon: 'mdi-view-dashboard',
          component: 'AdminDashboard',
          visible: true
        }
      ]
      return tabItems
    },
    visibleTabs() {
      const filtered = this.tabItems.filter((tab) => tab.visible)
      return filtered
    },
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.visibleTabs.length > 0 ? this.visibleTabs[0].key : 'OrgConfig'
    }
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },
    handleRefetchData() {
      this.getConfig()
    },
    handleDataReceived(data) {
      this.receivedData = data
    },
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')
        const data = response.data?.config
        this.receivedData = data
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async fetchOrg() {
      try {
        const result = await this.$axios.get('/organizations')
        this.orgData = result.data[0]
      } catch (error) {
        console.log(error?.response)
      }
    }
  },
  mounted() {
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }

    this.getConfig()
    this.fetchOrg()
    this.$nextTick(() => {
      this.isLoad = false
    })
  }
}
</script>

<style scoped></style>
