<template>
  <v-form
    ref="form"
    v-model="valid"
    lazy-validation
    class="p-12 bg-white dark-bg-custom rounded-lg mt-4"
  >
    <p class="text-[#6B6D78] dark-text-color text-2xl">
      {{ editData ? `Update ${brandName}` : `Add ${brandName}` }}
    </p>

    <v-container>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Name:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="entityName"
            :label="`Name`"
            outlined
            dense
            hide-details
            :rules="[(v) => !!v || `Name is required`]"
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Description:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="entityDescription"
            :label="`Description`"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Location:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="entityLocationFormat"
            :label="`Location`"
            @click="showAddBranch = true"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Status:</p>
        </v-col>
        <v-col cols="">
          <v-select
            :label="`Status`"
            flat
            :menu-props="{ offsetY: true }"
            v-model="entityStatus"
            :items="getStatusOptions(entityType)"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
    </v-container>

    <v-row>
      <v-col cols="12" class="text-right">
        <v-btn outlined class="w-52 h-14 mr-8 rounded-md" @click="cancel"> Cancel </v-btn>
        <v-btn
          class="bg-[#2A83FF] white--text w-52 h-14 rounded-md mr-3"
          :color="$vuetify.theme.currentTheme.primary"
          @click="addEntity"
        >
          {{ editData ? 'Update' : 'Add' }}
        </v-btn>
      </v-col>
    </v-row>
    <v-dialog v-model="showAddBranch" width="auto">
      <CompanyAddress
        :company="selectedCompanyName"
        @cancel="showAddBranch = false"
        :isOpen="showAddBranch"
        @save="addLocation"
        title="Add Address"
      />
    </v-dialog>
  </v-form>
</template>

<script>
import CompanyAddress from '../CompanyAddress.vue'

export default {
  components: {
    CompanyAddress
  },
  props: {
    entityType: {
      type: String,
      required: true
    },
    editPartnerData: {
      type: Object,
      default: null
    },
    editStoreData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      entityName: '',
      entityDescription: '',
      entityLocation: null,
      entityStatus: this.getStatusOptions(this.entityType)[0],
      selectedCompanyName: 'check',
      showAddBranch: false,
      valid: true
    }
  },
  computed: {
    entityLocationFormat() {
      return this.entityLocation && Object.values(this.entityLocation).join(', ')
    },
    editData() {
      return this.entityType === 'Store' ? this.editStoreData : this.editPartnerData
    },
    brandName() {
      if (this.entityType === 'Partner') {
        return 'Partner'
      }
      return 'Store'
    }
  },
  created() {
    if (this.editData) {
      this.entityName = this.editData[this.entityType.toLowerCase() + 'Name'] || ''
      this.entityDescription = this.editData[this.entityType.toLowerCase() + 'Description'] || ''

      if (this.editData[this.entityType.toLowerCase() + 'Location']) {
        this.entityLocation = {
          addressLine1:
            this.editData[this.entityType.toLowerCase() + 'Location'].addressLine1 || '',
          addressLine2:
            this.editData[this.entityType.toLowerCase() + 'Location'].addressLine2 || '',
          city: this.editData[this.entityType.toLowerCase() + 'Location'].city || '',
          state: this.editData[this.entityType.toLowerCase() + 'Location'].state || '',
          country: this.editData[this.entityType.toLowerCase() + 'Location'].country || '',
          pinCode: this.editData[this.entityType.toLowerCase() + 'Location'].pinCode || ''
        }
      }
      this.entityStatus =
        this.editData[this.entityType.toLowerCase() + 'Status'].split('_').join(' ') ||
        this.getStatusOptions(this.entityType)[0]
    }
  },
  methods: {
    async addEntity() {
      let apiEndpoint
      let method
      let message

      const isValid = this.$refs.form.validate()
      if (!isValid) return

      if (this.editData) {
        apiEndpoint = `/workforce/ecom/${this.entityType.toLowerCase()}/${this.editData._id}`
        method = 'put'
        message = 'updated'
      } else {
        apiEndpoint = `/workforce/ecom/${this.entityType.toLowerCase()}`
        method = 'post'
        message = 'created'
      }

      const entityData = {
        [`${this.entityType.toLowerCase()}Name`]: this.entityName,
        [`${this.entityType.toLowerCase()}Description`]: this.entityDescription || undefined,
        [`${this.entityType.toLowerCase()}Location`]: this.entityLocation,
        [`${this.entityType.toLowerCase()}Status`]: this.entityStatus.split(' ').join('_')
      }

      try {
        await this.$axios[method](apiEndpoint, entityData)
        this.$toast.success(`${this.entityType} ${message} successfully !!`)
        this.cancel()
      } catch (error) {
        const errorMessage = error.response.data.message || 'Failed to update order status.'
        this.$toast.error(errorMessage)
      }
    },
    cancel() {
      this.$emit('cancel')
    },
    addLocation(data) {
      this.entityLocation = {
        addressLine1: data.addLine1,
        addressLine2: data.addLine2,
        city: data.city,
        state: data.state,
        country: data.country,
        pinCode: data.pinCode
      }
      this.showAddBranch = false
    },
    getStatusOptions(entityType) {
      if (entityType === 'Store') {
        return ['OPERATIONAL', 'NON OPERATIONAL']
      } else {
        return ['ACTIVE', 'IN ACTIVE']
      }
    }
  }
}
</script>
