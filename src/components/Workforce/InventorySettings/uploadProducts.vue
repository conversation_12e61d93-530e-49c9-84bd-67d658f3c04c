<template>
    <div>
      <v-card>
        <div class="flex justify-between">
          <v-card-title class="text-md pt-0">Select File(XLSX)</v-card-title>
          <v-btn
            outlined
            small
            @click="downloadSample"
            :color="$vuetify.theme.currentTheme.primary"
            class="mr-2"
            >Sample Download</v-btn
          >
        </div>
        <v-card-text>
          <v-file-input
            label="Choose a file"
            v-model="selectedFile"
            multiple
            accept=".xlsx"
          ></v-file-input>
          <v-progress-linear
            v-if="uploadProgress !== null"
            :value="uploadProgress"
            absolute
            bottom
          ></v-progress-linear>
          <div v-if="uploadStatus">
            <p>Success : {{ created }}</p>
            <div class="flex align-center justify-between">
              <p>Failed : {{ failed }}</p>
              <v-btn small v-if="failed" @click="downloadFile">
                Download Failed Status<v-icon color="red">mdi-download</v-icon>
              </v-btn>
            </div>
          </div>
          <div v-if="invalid && invalid.length > 0" class="mt-4">
            <v-alert type="error" text outlined class="mt-2" :icon="false">
              <strong>Failed Upload Details:</strong>
              <div style="max-height: 300px; overflow-y: auto">
                <v-list dense>
                  <v-list-item v-for="(item, index) in invalid" :key="index">
                    <v-list-item-content>
                      Reason:
                      <v-list-item-title>
                        {{ index + 1 }} -
                        <span class="multiline-text">{{
                          item.error
                        }}</span>
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </div>
            </v-alert>
          </div>
          <v-alert
            v-else-if="validationErrors.length > 0 || validationPassed"
            :type="validationPassed ? 'success' : 'error'"
            text
            outlined
            class="mt-2"
            :icon="false"
          >
            <strong v-if="!validationPassed"
              >Validation failed: Please ensure the columns in your uploaded file
              match the required headers listed below. The file may have missing
              or mismatched headers.</strong
            >
            <strong v-else
              >Validation passed: Column mapping matched successfully!</strong
            >
            <v-divider class="my-2" v-if="!validationPassed"></v-divider>
            <ul v-if="!validationPassed">
              <li v-for="(error, index) in validationErrors" :key="index">
                {{ index + 1 }}: {{ error }}
              </li>
            </ul>
            <p v-else>
              Please click on <strong>create</strong> to proceed.
            </p>
          </v-alert>
          <v-alert v-if="uploadStatus" :type="uploadStatusType" class="mt-4">{{
            uploadStatus
          }}</v-alert>
        </v-card-text>
        <v-card-actions class="flex items-center justify-between mb-4 px-4 w-full">
          <v-btn @click="closeUploadDialog" class="w-[50%]" outlined
            >Close</v-btn
          >
          <v-btn
            @click="handleAction"
            :loading="uploading"
            :disabled="!selectedFile || uploading"
            class="white--text w-[50%]"
            :color="$vuetify.theme.currentTheme.primary"
          >
            {{ validationPassed ? "Create" : "Validate" }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </div>
  </template>
  <script>
  import { extractExcelHeaders } from "@/utils/common";
  export default {
    data() {
      return {
        selectedFile: null,
        uploading: false,
        created: null,
        failed: null,
        invalid: null,
        uploadProgress: null,
        uploadStatus: "",
        headers: [],
        validationErrors: [],
        validationPassed: false,
        excelHeaderData: [
            "SL No.",
            "product",
            "productMarker",
        ]
      };
    },
    computed: {
      uploadStatusType() {
        return this.uploadStatus === "File uploaded successfully"
          ? "success"
          : "error";
      },
    },
    methods: {
      
      uploadFile() {
        if (!this.selectedFile) {
          return;
        }
        const formData = new FormData();
        this.selectedFile.forEach((file) => {
          formData.append("attachments", file);
        });
  
        const config = {
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
          },
        };
        this.uploading = true;
        this.$axios
          .post("/workforce/brandMapping/bulk", formData, config)
          .then((response) => {
            this.created = response.data.data.mappingCreatedSuccessfully;
            this.failed = response.data.data.mappingCreationFailed;
            this.invalid = response.data.data.invalidMapping;
            if (this.created) {
              this.uploadStatus = "File uploaded successfully";
              this.uploading = false;
            } else {
              this.uploadStatus = "File uploading failed";
            }
          })
          .catch((error) => {
            this.uploading = false;
            this.uploadStatus = "Error uploading file";
            console.error("Error uploading file:", error);
          })
          .finally(() => {
            this.uploading = false;
          });
      },
      closeUploadDialog() {
        this.uploading = false;
        this.created = "";
        this.failed = "";
        this.invalid = [];
        this.uploadStatus = "";
        this.selectedFile = null;
        this.validationErrors = [];
        this.validationPassed = false;
        this.$emit("close");
      },

      downloadSample() {
        const XLSX = require("xlsx");
        const worksheetData = [this.excelHeaderData];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(worksheetData); 

        XLSX.utils.book_append_sheet(wb, ws, 'Data');

        XLSX.writeFile(wb, `Brands.xlsx`);
      },
      getDynamicField(item) {
        const dynamicField = this.excelHeaderData
          ?.toLowerCase()
          .replace(/\s+/g, "_");
  
        return item[dynamicField];
      },
  
      async handleAction() {
        if (!this.validationPassed) {
          await this.validateFile();
        } else {
          this.uploadFile();
        }
      },
      async validateFile() {
        try {
          this.headers = await extractExcelHeaders(this.selectedFile);
          const fieldMappings = this.excelHeaderData;
          const missingFields = [];
        
          const missingInArray2 = fieldMappings.filter(item => !this.headers.includes(item));
          
          if (missingInArray2.length > 0) {
              missingFields.push(missingInArray2)
          }
          if (missingFields.length > 0) {
            this.validationErrors = missingInArray2;
            this.validationPassed = false;
          } else {
            this.validationErrors = [];
            this.validationPassed = true;
          }
        } catch (error) {
          console.error("Error during validation:", error);
          this.validationErrors = ["Error processing the file."];
          this.validationPassed = false;
        }
      },
    },
  };
  </script>
  <style scoped>
  .multiline-text {
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-word;
    font-weight: 400;
  }
  </style>
  