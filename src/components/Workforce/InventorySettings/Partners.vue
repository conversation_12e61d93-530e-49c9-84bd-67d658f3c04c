<template>
  <div class="p-3">
    <div
      class="justify-end items-center my-3"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
      v-if="!showAddPartner"
    >
      <div class="justify-end mr-4" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterPartner"
            label="Name"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </div>
      <v-tooltip top>
        <template #activator="{ on }">
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text mr-4"
            v-on="on"
            v-show="canWrite"
            @click="uploadBrands('Create')"
          >
            Bulk Create
          </v-btn>
        </template>
        <span>Bulk create new Lender</span>
      </v-tooltip>
      <v-tooltip top>
        <template #activator="{ on, attrs }">
          <v-btn
            @click="toggleshowAddPartner()"
            :color="$vuetify.theme.currentTheme.primary"
            :class="[
              $vuetify.breakpoint.lgAndUp ? ' white--text' : 'ml-3 flex-col white--text mt-2'
            ]"
            v-bind="attrs"
            v-show="canWrite"
            v-on="on"
          >
            <v-icon left dark>mdi-plus</v-icon>
            Add
          </v-btn>
        </template>
        <span>Add </span>
      </v-tooltip>
    </div>
    <div v-if="showTable" class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="partners"
        class="pl-7 pr-7 pt-4"
        :options.sync="options"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :server-items-length="totalItems"
        :loading="loadTable"
        fixed-header
      >
        <template v-slot:item.partnerName="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.partnerName }}</td>
        </template>
        <template v-slot:item.partnerDescription="{ item }">
          <td class="px-4 font-semibold">{{ item.partnerDescription }}</td>
        </template>
        <template v-slot:item.partnerLocation="{ item }">
          <td class="px-4 font-semibold">
            {{ item.partnerLocation ? Object.values(item.partnerLocation).join(', ') : '' }}
          </td>
        </template>
        <template v-slot:item.partnerStatus="{ item }">
          <td :class="getPartnerStatusClr(item.partnerStatus)" class="px-4 font-semibold">
            {{ item.partnerStatus }}
          </td>
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4">
            <action-button
              :item="item"
              :canEdit="canEdit"
              :handleEdit="() => partnerEdit(item)"
              :showDeleteButton="false"
            />
          </td>
        </template>
      </v-data-table>
    </div>
    <div v-else-if="showAddPartner">
      <AddStorePartner
        @cancel="toggleShowTable"
        :entityType="'Partner'"
        :editPartnerData="selectedPartnerData"
      />
    </div>
    <v-dialog v-model="openBrands" max-width="600" persistent>
      <UploadProducts @close="closeUpload" />
    </v-dialog>
  </div>
</template>

<script>
import AddStorePartner from '~/components/Workforce/InventorySettings/AddStorePartner.vue'
import ActionButton from '../../ActionButton.vue'
import { debounce, hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
import UploadProducts from './uploadProducts.vue'

export default {
  name: 'InventoryIn',
  components: {
    AddStorePartner,
    ActionButton,
    UploadProducts
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      partners: [],
      showTable: true,
      loadTable: true,
      options: { itemsPerPage: 10, page: 1 },
      totalItems: 0,
      filterPartner: '',
      showAddPartner: false,
      selectedPartnerData: null,
      openBrands: false
    }
  },
  beforeCreate() {
    this.debouncedFetchPartners = () => {}
  },
  created() {
    this.debouncedFetchPartners = debounce(this.fetchPartners, 500)
  },
  watch: {
    options: {
      handler() {
        this.fetchPartners()
      },
      deep: true
    },
    '$route.query.search': {
      handler(newQuery) {
        try {
          this.filterPartner = newQuery || ''
          this.options.page = 1
          this.debouncedFetchPartners()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },
    filterPartner(val) {
      if (!val && this.$route.query.search) {
        this.$router.replace({ query: {} }).catch((err) => {
          if (err.name !== 'NavigationDuplicated') {
            throw err
          }
        })
      }
    }
  },
  computed: {
    headers() {
      const headers = [
        { text: 'Lender', value: 'partnerName', sortable: false, width: '30%' },
        { text: 'Descrption', value: 'partnerDescription', sortable: false, width: '30%' },
        { text: 'Status', value: 'partnerStatus', sortable: false, width: '30%' },
        this.canEdit ? { text: 'Action', value: 'action', sortable: false } : []
      ]
      return headers
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.inventoryEdit)
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    }
  },
  methods: {
    uploadBrands() {
      this.openBrands = true
    },
    closeUpload() {
      this.openBrands = false
    },
    getPartnerStatusClr(status) {
      const statuses = {
        ACTIVE: 'text-green-500',
        IN_ACTIVE: 'text-red-500'
      }

      return statuses[status]
    },
    toggleShowTable() {
      this.showAddPartner = false
      this.showTable = true
      this.selectedPartnerData = null
      this.fetchPartners()
    },
    toggleshowAddPartner() {
      this.showTable = false
      this.showAddPartner = true
    },
    async fetchPartners() {
      try {
        const { page, itemsPerPage } = this.options
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems
        }
        if (this.filterPartner) {
          params.search = this.filterPartner
        }
        this.loadTable = true
        const response = await this.$axios.get('/workforce/ecom/partners', {
          params
        })
        this.loadTable = false
        this.partners = response.data.partners
        this.totalItems = response.data.pagination?.totalCount
      } catch (error) {
        console.error('An error occurred:', error)
      }
    },
    partnerEdit(item) {
      this.selectedPartnerData = item
      this.toggleshowAddPartner()
    },
    handleInput: debounce(function () {
      this.options.page = 1
      // Update URL query parameter when searching
      if (this.filterPartner) {
        this.$router
          .push({
            path: this.$route.path,
            query: { ...this.$route.query, search: this.filterPartner }
          })
          .catch((err) => {
            if (err.name !== 'NavigationDuplicated') {
              console.error('Navigation error:', err)
            }
          })
      } else {
        // Clear query when filter is empty
        const query = { ...this.$route.query }
        delete query.search
        this.$router.replace({ query }).catch((err) => {
          if (err.name !== 'NavigationDuplicated') {
            console.error('Navigation error:', err)
          }
        })
      }
      this.fetchPartners()
    }, 1000)
  }
}
</script>

<style scoped></style>
