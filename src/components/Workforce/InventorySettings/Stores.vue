<template>
  <div class="p-2">
    <!-- Search section -->
    <div
      v-if="!showAddStore"
      class="justify-end items-center"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
    >
      <div class="justify-end mr-4" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterStore"
            label="Store Name"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </div>
    </div>

    <!-- Add Store Form -->
    <div v-if="showAddStore">
      <AddStorePartner
        @cancel="toggleshowAddStore"
        :entityType="'Store'"
        :editStoreData="selectedStoreData"
      />
    </div>

    <!-- Stores Table -->
    <div v-else class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="stores"
        class="pl-7 pr-7 pt-4"
        :options.sync="options"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :server-items-length="totalItems"
        fixed-header
        :loading="loadTable"
      >
        <template v-slot:item.storeName="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.storeName }}</td>
        </template>
        <template v-slot:item.storeDescription="{ item }">
          <td class="px-4 font-semibold">{{ item.storeDescription }}</td>
        </template>
        <template v-slot:item.storeLocation="{ item }">
          <td class="px-4 font-semibold">
            {{ item.storeLocation ? Object.values(item.storeLocation).join(', ') : '' }}
          </td>
        </template>
        <template v-slot:item.storeStatus="{ item }">
          <td :class="getStoreStatusClr(item.storeStatus)" class="px-4 font-semibold">
            {{ item.storeStatus }}
          </td>
        </template>
        <template v-slot:item.actions="{ item }">
          <ActionButton
            :item="item"
            :canEdit="canEdit"
            :canDelete="canDelete"
            :handleEdit="() => editStore(item)"
            :handleDelete="() => deleteStore(item)"
            :showEditButton="canEdit"
            :showDeleteButton="canDelete"
          />
        </template>
      </v-data-table>
    </div>
  </div>
</template>

<script>
import AddStorePartner from '~/components/Workforce/InventorySettings/AddStorePartner.vue'
import ActionButton from '@/components/ActionButton.vue'
import { debounce, hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'

export default {
  name: 'InventoryIn',
  components: {
    AddStorePartner,
    ActionButton
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      stores: [],
      filterStore: '',
      options: { itemsPerPage: 5, page: 1 },
      totalItems: 0,
      loadTable: true,
      showAddStore: false,
      selectedStoreData: null
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchStores()
      },
      deep: true
    }
  },
  computed: {
    headers() {
      return [
        { text: 'Store Name', value: 'storeName', sortable: false },
        { text: 'Description', value: 'storeDescription', sortable: false },
        { text: 'Location', value: 'storeLocation', sortable: false },
        { text: 'Store Status', value: 'storeStatus', sortable: false },
        { text: 'Actions', value: 'actions', sortable: false }
      ]
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    }
  },
  methods: {
    getStoreStatusClr(status) {
      const statuses = {
        OPERATIONAL: 'text-green-500',
        NON_OPERATIONAL: 'text-red-500'
      }
      return statuses[status]
    },
    async fetchStores() {
      try {
        const { page, itemsPerPage } = this.options
        const params = { page, limit: itemsPerPage }
        if (this.filterStore) {
          params.search = this.filterStore
        }
        this.loadTable = true
        const response = await this.$axios.get('/workforce/ecom/stores', {
          params
        })
        this.loadTable = false
        this.stores = response.data.stores
        this.totalItems = response.data.pagination?.totalCount
      } catch (error) {
        console.error('An error occurred:', error)
      }
    },
    handleInput: debounce(function () {
      this.fetchStores()
      this.options.page = 1
    }, 1000),
    editStore(item) {
      this.selectedStoreData = item
      this.showAddStore = true
    },
    deleteStore(item) {
      // Add delete confirmation logic here
      console.log('Delete store:', item)
    },
    toggleshowAddStore() {
      this.showAddStore = !this.showAddStore
      if (!this.showAddStore) {
        this.selectedStoreData = null
      }
    }
  }
}
</script>

<style scoped></style>
