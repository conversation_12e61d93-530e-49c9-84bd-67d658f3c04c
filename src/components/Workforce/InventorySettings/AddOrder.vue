<template>
  <v-form class="p-12 bg-white dark-bg-custom rounded-lg mt-4">
    <p class="text-[#6B6D78] text-2xl">Refill Inventory</p>
    <v-form ref="form" v-model="valid" lazy-validation>
      <v-container>
        <v-row no-gutters dense>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Order Number:</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="orderNumber"
              label="Order Number"
              outlined
              dense
              hide-details
              :rules="[(v) => !!v || 'Order number is required']"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Spare Part:</p>
          </v-col>
          <v-col cols="">
            <v-autocomplete
              v-model="selectedParts"
              label="Search Part"
              :items="inventoryItems"
              @update:search-input="debounceMethod"
              :search-input.sync="searchPart"
              item-text="label"
              item-value="_id"
              hide-no-data
              clearable
              outlined
              dense
              multiple
              required
              hide-details
              :rules="[(v) => !!v.length || 'Spare parts are required']"
            ></v-autocomplete>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Brands:</p>
          </v-col>
          <v-col cols="">
            <v-autocomplete
              flat
              :menu-props="{ offsetY: true }"
              v-model="selectedPartner"
              label="Select Brand"
              :items="activePartners"
              @update:search-input="fetchPartners"
              :search-input.sync="searchPartner"
              item-text="partnerName"
              item-value="_id"
              outlined
              dense
              hide-details
              :rules="[(v) => !!v || 'Brand name is required']"
            ></v-autocomplete>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Stores:</p>
          </v-col>
          <v-col cols="">
            <v-select
              flat
              :menu-props="{ offsetY: true }"
              v-model="selectedStores"
              label="Select Store"
              :items="activeStores"
              item-text="storeName"
              item-value="_id"
              multiple
              outlined
              hide-details
              dense
              :rules="[(v) => !!v.length || 'Store name is required']"
            ></v-select>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Order Date:</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="orderDate"
              type="date"
              outlined
              dense
              hide-details
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row v-if="selectedParts.length">
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Cost Price(Per Unit) for:</p>
          </v-col>
          <v-col cols="">
            <v-row v-for="part in selectedParts" :key="part">
              <v-col cols="12">
                <v-text-field
                  :label="`Price of ${getPartLabel(part)} per/unit`"
                  v-model="costPrices[part]"
                  outlined
                  dense
                  hide-details
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
        </v-row>

        <v-row v-if="selectedParts.length && selectedStores.length">
          <v-col cols="2">
            <p class="mb-0 mt-4">Quantities for:</p>
          </v-col>
          <v-col cols="">
            <v-col v-for="part in selectedParts" :key="part">
              <v-row v-for="storeId in selectedStores" :key="storeId">
                <v-col cols="12" class="px-0">
                  <v-text-field
                    type="number"
                    :label="`${getPartLabel(part)} quantity in ${getStoreName(
                      storeId
                    )}`"
                    v-model="quantities[part][storeId]"
                    @change="onQuantityChange(part, storeId, $event)"
                    outlined
                    dense
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-col>
        </v-row>
      </v-container>
    </v-form>

    <v-row>
      <v-col cols="12" class="text-right">
        <v-btn outlined class="w-52 h-14 mr-8 rounded-md" @click="cancel">
          Cancel
        </v-btn>
        <v-btn
          class="bg-[#2A83FF] white--text w-52 h-14 mr-3 rounded-md"
          :color="$vuetify.theme.currentTheme.primary"
          @click="addOrder"
        >
          {{ editData ? "Update" : "Add" }}
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<script>
import { debounce } from "@/utils/common";
import { sortDropdownOptions } from "@/utils/common";

export default {
  data() {
    return {
      orderNumber: "",
      selectedParts: [],
      selectedPartner: "",
      searchPart: "",
      searchPartner: "",
      totalOrderAmount: null,
      selectedStores: [],
      partners: [],
      stores: [],
      orderDate: null,
      inventoryItems: [],
      quantities: {},
      costPrices: {},
      valid: true,
    };
  },
  props: {
    editData: {
      type: Object,
      default: null,
    },
  },
  watch: {
    selectedParts: {
      handler() {
        this.resetQuantities();
      },
      immediate: true,
    },
    selectedStores: {
      handler() {
        this.resetQuantities();
      },
      immediate: true,
    },
  },
  async mounted() {
    await this.fetchStores();
    if (this.editData) {
      this.searchPartner = this.editData?.partner?.partnerName;
      this.searchPart = this.editData?.product?.productName;
      await this.fetchInventory();
      await this.fetchPartners();
    }
  },
  computed: {
    activePartners() {
      const partners = this.partners.filter(
        (partner) => partner.partnerStatus !== "IN_ACTIVE"
      );

      return sortDropdownOptions(partners, "partnerName");
    },
    activeStores() {
      return this.stores.filter(
        (store) => store.storeStatus !== "NON_OPERATIONAL"
      );
    },
  },
  created() {
    if (this.editData) {
      this.orderNumber = this.editData.orderNumber || "";
      this.selectedParts = this.editData.product
        ? [this.editData.product._id]
        : [];
      this.selectedPartner = this.editData?.partner._id || "";
      this.selectedStores = this.editData.orderDeliveryLocation
        ? this.editData.orderDeliveryLocation.map(
            (item) => item.storeLocation._id
          )
        : [];
      this.orderDate = this.editData.orderDate
        ? this.$dayjs(this.editData.orderDate).format("YYYY-MM-DD")
        : null;

      this.selectedParts.forEach((partId) => {
        this.costPrices[partId] = this.editData.costPricePerUnit || 0;
        this.quantities[partId] = {};

        this.selectedStores.forEach((storeId) => {
          const deliveryLocation = this.editData.orderDeliveryLocation.find(
            (loc) => loc.storeLocation._id === storeId
          );
          this.quantities[partId][storeId] = deliveryLocation.orderedQuantity;
        });
      });
    }
  },

  methods: {
    getPartLabel(partId) {
      const part = this.inventoryItems.find((item) => item._id === partId);
      return part ? part.label : "Unknown Part";
    },
    getStoreName(storeId) {
      const store = this.stores.find((item) => item._id === storeId);
      return store ? store.storeName : "Unknown Store";
    },
    onQuantityChange(partId, storeId, event) {
      if (!this.quantities[partId]) {
        this.$set(this.quantities, partId, {});
      }
      this.$set(this.quantities[partId], storeId, event);
    },
    resetQuantities() {
      if (this.editData) return {};
      this.quantities = {};
      this.selectedParts.forEach((part) => {
        this.selectedStores.forEach((storeId) => {
          if (!this.quantities[part]) {
            this.$set(this.quantities, part, {});
          }
          this.$set(this.quantities[part], storeId, 0);
        });
      });
    },
    async addOrder() {
      let apiEndpoint;
      let method;
      let message;

      const isValid = this.$refs.form.validate();
      if (!isValid) {
        this.$toast.error("Please fill all the required fields");
        return;
      }

      await Promise.all(
        this.selectedParts.map(async (part) => {
          const orderDeliveryLocation = [];
          let totalQuantity = 0;
          const costPricePerUnit = this.costPrices[part] || 0;

          this.selectedStores?.forEach((storeId) => {
            const store = this.stores?.find((store) => store._id === storeId);
            const quantity = this.quantities[part]?.[storeId] || 0;
            if (quantity > 0) {
              orderDeliveryLocation.push({
                storeLocation: store._id,
                orderedQuantity: Number(quantity),
              });
              totalQuantity += Number(quantity);
            }
          });

          const totalOrderAmount = costPricePerUnit * totalQuantity;

          if (this.editData) {
            apiEndpoint = `/workforce/ecom/inventory/order/${this.editData._id}`;
            method = "put";
            message = "updated";
          } else {
            apiEndpoint = `/workforce/ecom/inventory/order`;
            method = "post";
            message = "created";
          }

          const data = {
            orderNumber: this.orderNumber,
            product: part,
            partner: this.selectedPartner,
            costPricePerUnit: costPricePerUnit,
            totalOrderAmount: totalOrderAmount,
            orderDate: this.orderDate,
            orderDeliveryLocation,
          };

          try {
            await this.$axios[method](apiEndpoint, data, {
            });
            this.$toast.success(
              `Order for part ${part} ${message} successfully !!`
            );
          } catch (error) {
            const errorMessage = error.response.data.message || "Order failed.";
            this.$toast.error(errorMessage);
          }
        })
      );

      this.cancel();
    },
    fetchPartners: debounce(async function (text) {
      if (!text) return;

      const { data } = await this.$axios.get("/workforce/ecom/partners", {
        params: {
          search: this.searchPartner,
        },
      });

      if (data.success) {
        this.partners = data.partners;
      }
    }, 1000),
    async fetchStores() {
      const { data } = await this.$axios.get("/workforce/ecom/stores");

      if (data.success) {
        this.stores = data.stores;
      }
    },
    debounceMethod: debounce(function (text) {
      if (!text && !this.editProductData) {
        return;
      }
      const selectedData = this.inventoryItems.find((el) => el.label === text);
      if (selectedData) {
        this.selectedParts = [selectedData._id];
      } else {
        this.fetchInventory();
      }
    }, 800),
    async fetchInventory() {
      try {
        const params = {};
        if (this.searchPart) {
          params.search = this.searchPart;
        }
        const response = await this.$axios.get("/workforce/ecom/products", {
          params,
        });
        const products = response.data?.products || [];

        const items = sortDropdownOptions(products, "productName");

        this.inventoryItems = items.map((item) => {
          return {
            ...item,
            label: `${item.productName} - ${item.modelNumber}`,
          };
        });
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>
