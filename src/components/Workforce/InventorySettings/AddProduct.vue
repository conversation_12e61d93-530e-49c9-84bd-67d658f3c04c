<template>
  <v-form
    ref="form"
    v-model="valid"
    lazy-validation
    class="p-12 bg-white dark-bg-custom rounded-lg mt-4"
  >
    <p class="text-[#6B6D78] dark-text-color text-2xl">
      {{ editProductData ? `Update Product` : `Add Product` }}
    </p>
    <v-container>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0" >Product Name:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="productName"
            label="Product Name"
            outlined
            dense
            required
            :rules="[(v) => !!v || `Product Name is required`]"
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Product Description:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="productDescription"
            label="Product Description"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Brand:</p>
        </v-col>
        <v-col cols="">
          <v-combobox
            flat
            :menu-props="{ offsetY: true }"
            v-model="selectedMaker"
            :items="partners"
            label="Search Brand"
            placeholder="Search Brand"
            @update:search-input="debounceMethod"
            :search-input.sync="searchPartner"
            item-text="partnerName"
            item-value="_id"
            outlined
            multiple
            dense
            hide-details
          ></v-combobox>
        </v-col>
      </v-row>
    </v-container>

    <v-row>
      <v-col cols="12" class="text-right">
        <v-btn outlined class="w-52 h-14 mr-8 rounded-md" @click="cancel">
          Cancel
        </v-btn>
        <v-btn
          class="bg-[#2A83FF] white--text w-52 h-14 rounded-md mr-3"
          :color="$vuetify.theme.currentTheme.primary"
          @click="addProduct"
        >
          {{ editProductData ? "Update" : "Add" }}
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>
<script>
import { sortDropdownOptions, debounce } from "@/utils/common";

export default {
  data() {
    return {
      productName: "",
      productDescription: "",
      searchPartner: "",
      selectedMaker: [],
      partners: [],
      valid: true,
    };
  },
  props: {
    editProductData: {
      type: Object,
      default: null,
    },
  },
  created() {
    if (this.editProductData) {
      this.productName = this.editProductData.productName || "";
      this.productDescription = this.editProductData.productDescription || "";
      this.selectedMaker = this.editProductData.productMaker.map(
        (el) => el.maker
      );
    }
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    debounceMethod: debounce(function (text) {
      if (!text && !this.editProductData) {
        return;
      }
      this.fetchPartners();
    }, 800),
    async fetchPartners() {
      try {
        const response = await this.$axios.get("/workforce/ecom/partners", {
          params: {
            search: this.searchPartner,
          },
        });
        const partners = response.data.partners;

        this.partners = sortDropdownOptions(partners, "partnerName");
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    async addProduct() {
      const isValid = this.$refs.form.validate();
      if (!isValid) return;
      try {
        let apiEndpoint;
        let method;
        let message;
        if (this.editProductData) {
          apiEndpoint = `/workforce/ecom/productMap/${this.editProductData._id}`;
          method = "put";
          message = "updated";
        } else {
          apiEndpoint = `/workforce/ecom/productMap`;
          method = "post";
          message = "created";
        }

        const data = this.selectedMaker.map((el) => {
          return {
            maker: el,
            model: [],
          };
        });

        const payload = {
          productName: this.productName,
          productDescription: this.productDescription || undefined,
          productMaker: data,
        };
        await this.$axios[method](apiEndpoint, payload);
        this.$toast.success(`Product ${message} successfully !!`);
        this.cancel();
      } catch (error) {
        console.log("An error occurred:", error);
        this.$toast.error(`Product ${error?.message} failed !!`);
      }
    },
  },
};
</script>
