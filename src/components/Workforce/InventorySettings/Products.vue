<template>
  <div class="p-2">
    <!-- Search section -->
    <div
      v-if="!showAddProduct"
      class="justify-end items-center"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
    >
      <div class="justify-end mr-4" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterProduct"
            label="Product Name"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </div>
    </div>

    <!-- Add Product Form -->
    <div v-if="showAddProduct">
      <AddProduct @cancel="toggleshowAddProduct" :editProductData="selectedProductData" />
    </div>

    <!-- Products Table -->
    <div v-else class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="products"
        item-key="_id"
        class="pl-4 pr-4 pt-4"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :options.sync="options"
        :server-items-length="totalItems"
        :loading="loadTable"
        fixed-header
      >
        <template v-slot:item.productName="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.productName }}</td>
        </template>
        <template v-slot:item.productMaker="{ item }">
          <td class="px-4 font-semibold">
            {{ getPartnerNames(item.productMaker) }}
          </td>
        </template>
        <template v-slot:item.actions="{ item }">
          <ActionButton
            :item="item"
            :canEdit="canEdit"
            :canDelete="canDelete"
            :handleEdit="() => editProduct(item)"
            :handleDelete="() => deleteProduct(item)"
            :showEditButton="canEdit"
            :showDeleteButton="canDelete"
          />
        </template>
      </v-data-table>
    </div>

    <!-- Bulk Upload Dialog -->
    <v-dialog v-model="openBulkCreate" max-width="600" persistent>
      <UploadProducts @close="closeBulkUpload" />
    </v-dialog>
  </div>
</template>

<script>
import AddProduct from '~/components/Workforce/InventorySettings/AddProduct.vue'
import ActionButton from '../../ActionButton.vue'
import UploadProducts from './uploadProducts.vue'
import { debounce, hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'

export default {
  name: 'ProductsComponent',
  components: {
    AddProduct,
    ActionButton,
    UploadProducts
  },

  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      products: [],
      options: { itemsPerPage: 5, page: 1 },
      totalItems: 0,
      filterProduct: '',
      loadTable: true,
      showAddProduct: false,
      openBulkCreate: false,
      selectedProductData: null
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchProducts()
      },
      deep: true
    }
  },
  computed: {
    headers() {
      return [
        { text: 'Product Name', value: 'productName', sortable: false, width: '35%' },
        { text: 'Brand', value: 'productMaker', sortable: false, width: '35%' },
        { text: 'Actions', value: 'actions', sortable: false, width: '30%' }
      ]
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    }
  },
  methods: {
    async fetchProducts() {
      try {
        const { page, itemsPerPage } = this.options
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems
        }
        if (this.filterProduct) {
          params.search = this.filterProduct
        }
        this.loadTable = true
        const response = await this.$axios.get('/workforce/ecom/productMaps', {
          params
        })
        this.loadTable = false
        this.products = response.data.products
        this.totalItems = response.data.pagination?.totalCount
      } catch (error) {
        console.error('An error occurred:', error)
      }
    },
    getPartnerNames(productMaker) {
      return productMaker.map((partner) => partner?.maker?.partnerName).join(', ')
    },
    handleInput: debounce(function () {
      this.options.page = 1
      this.fetchProducts()
    }, 1000),
    toggleshowAddProduct() {
      this.showAddProduct = !this.showAddProduct
      this.selectedProductData = null
    },
    uploadBrands() {
      this.openBulkCreate = true
    },
    closeBulkUpload() {
      this.openBulkCreate = false
      this.fetchProducts() // Refresh the list after bulk upload
    },
    editProduct(item) {
      this.selectedProductData = item
      this.showAddProduct = true
    },
    deleteProduct(item) {
      // Add delete confirmation logic here
      console.log('Delete product:', item)
    }
  }
}
</script>

<style scoped></style>
