<template>
  <v-card>
    <v-card-text class="flex flex-col gap-2">
      <div
        v-for="(branch, i) in customerAddresses"
        :key="i"
        class="text-gray-600 border rounded-md p-4"
      >
        <div class="flex justify-between">
          <p>
            Full Address:
            <span>{{ fullAddress(branch.address) }} </span>
          </p>
          <div class="flex">
            <v-icon
              size="22"
              color="blue"
              class="mr-4 mb-2 cursor-pointer h-8"
              @click="editBranch(branch, i)"
              >mdi-pencil</v-icon
            >
            <v-icon
              size="22"
              color="red"
              class="mr-4 mb-2 cursor-pointer h-8"
              @click="deleteBranch(branch, i)"
              >mdi-delete</v-icon
            >
          </div>
        </div>
        <p>
          Alias: <span>{{ branch?.alias }}</span>
        </p>
        <p>
          City: <span>{{ branch.address?.city }}</span>
        </p>
        <p>
          State: <span>{{ branch.address?.state }}</span>
        </p>
        <p class="mb-0">
          Pincode: <span>{{ branch.address?.pinCode }}</span>
        </p>
      </div>
    </v-card-text>
    <v-dialog v-model="confirmDeleteDialog" max-width="600">
      <v-card>
        <v-card-title class="text-h5 justify-center"
          >Delete Customer Address</v-card-title
        >
        <v-card-text class="text-center">
          Are you sure you want to delete this customer address?
        </v-card-text>
        <v-card-actions class="flex justify-center mb-2">
          <v-btn
            outlined
            color="grey"
            class="rounded-lg mr-4 px-6"
            @click="cancelDelete"
            >Cancel</v-btn
          >
          <v-btn
            class="white--text rounded-lg px-6"
            color="red"
            @click="confirmDelete"
            >Delete</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>
<script>
import { getFullAddress } from "@/utils/common";
export default {
  data() {
    return {
      confirmDeleteDialog: false,
      itemToDelete: null,
      branchIndex: null,
    };
  },
  props: {
    customerAddresses: Array,
  },
  methods: {
    fullAddress(address) {
      return getFullAddress(address);
    },
    deleteBranch(item, index) {
      this.itemToDelete = item;
      this.branchIndex = index;
      console.log(item, this.branchIndex,"check");
      this.confirmDeleteDialog = true;
    },
    confirmDelete() {
      const companyId = this.itemToDelete?.companyId;
      const branchId = this.itemToDelete?._id;
      try {
        this.$axios
          .delete(`/workforce/company/${companyId}/branches/${branchId}`)
          .then(() => {
            this.$toast.success("Branch deleted successfully !!");
            this.$props.customerAddresses.splice(this.branchIndex, 1);
          })
          .catch((error) => {
            console.error(error);
          });
      } catch (error) {
        console.error("Error in deleteLead:", error);
      }
      this.cancelDelete();
    },
    cancelDelete() {
      this.confirmDeleteDialog = false;
    },
    editBranch(item) {
      this.$emit("switchTab", item);
    },
  },
};
</script>
