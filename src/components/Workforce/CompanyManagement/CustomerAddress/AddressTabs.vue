<template>
  <div class="bg-gray-100 dark-bg-custom">
    <div>
      <v-main style="padding: 0px;" class="bg-[#f7fafb] dark-bg-custom">
        <div>
          <v-col>
            <v-tabs v-model="tabs" align-with-title :color="$vuetify.theme.currentTheme.primary">
              <v-tab
                :ripple="false"
                v-for="(tab, index) in tabItems"
                :key="index"
                class="mr-12 ml-2"
                :color="$vuetify.theme.currentTheme.primary"
              >
                {{ tab.name }}
              </v-tab>
              <v-tabs-slider color="#32D583"></v-tabs-slider>
            </v-tabs>
            <v-btn
              style="
                position: absolute;
                right: 3%;
                top: 25%;
                padding: 0 8px;
                font-size: 1.5rem;
              "
              @click="closeDialog"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-col>
        </div>
      </v-main>
      <div>
        <component
          :is="currentTab"
          :customerAddresses="branches"
          :editBranchData="editBranchData"
          @save="addCustomerAddress"
          @cancel="cancelTab"
          @switchTab="handleTabSwitch"
        ></component>
      </div>
    </div>
  </div>
</template>

<script>
import ViewAddress from "./ViewAddress";
import CompanyAddress from "~/components/Workforce/CompanyAddress.vue";

export default {
  name: "ContactTabs",
  components: {
    ViewAddress,
    CompanyAddress,
  },
  data() {
    return {
      tabs: 0,
      editBranchData: null,
    };
  },
  watch: {
    branches: {
      handler(newVal) {
        this.$props.branches = newVal;
      },
      deep: true,
    },
    tabs(newVal, oldVal) {
      if (newVal === 0 && oldVal === 1) {
        this.cancelTab();
      }
    },
  },
  props: {
    branches: Array,
  },
  computed: {
    currentTab() {
      return this.tabItems[this.tabs]?.component;
    },
    tabItems() {
      return [
        {
          key: "ViewAddress",
          name: "Existing Addresses",
          component: "ViewAddress",
        },
        {
          key: "CompanyAddress",
          name: "Add/Edit Address",
          component: "CompanyAddress",
        },
      ];
    },
  },
  methods: {
    closeDialog() {
      this.$emit("closeDialog");
      this.tabs = 0;
    },
    cancelTab() {
      this.tabs = 0;
      this.editBranchData = null;
    },
    handleTabSwitch(item) {
      this.tabs = 1;
      this.editBranchData = item;
    },
    async addCustomerAddress(data) {
      const companyId = this.branches[0]?.companyId;
      const branch = {
        alias: data.alias,
        address: {
          addressLine1: data.addLine1,
          addressLine2: data.addLine2,
          city: data.city,
          state: data.state,
          country: data.country,
          pinCode: data.pinCode,
          coordinates: {
            latitude: data.latitude,
            longitude: data.longitude,
          },
        },
      };
      const company = {
        branch,
      };
      try {
        if (data.companyId && data.branchId) {
          const editResponse = await this.$axios.put(
            `/workforce/company/${data.companyId}/branches/${data.branchId}`,
            branch,
          );
          const updatedBranch = editResponse.data?.branch;
          updatedBranch.companyId = data.companyId;
          updatedBranch.name = data.companyName;
          const index = this.branches.findIndex(
            (b) => b._id === updatedBranch._id
          );
          if (index !== -1) {
            this.$set(this.branches, index, updatedBranch);
          }
          this.$emit("updateBranch", updatedBranch);
        } else {
          const addResponse = await this.$axios.post(
            `/workforce/addBranch/${companyId}`,
            company,
          );
          this.$emit("updateBranch", addResponse.data?.company);
        }
        this.$toast.success("Company Branch Added Successfully !!!");
        this.cancelTab();
      } catch (error) {
        console.log("Error in company addition", error);
        const errorMessage = error?.response?.data?.message || error.message;
        this.$toast.error(errorMessage);
      }
    },
  },
};
</script>
<style scoped>
.v-tab:before {
  background-color: transparent;
}

.theme--light.v-tabs .v-tab:hover::before {
  opacity: 0;
}

.theme--light.v-tabs .v-tab--active:hover::before,
.theme--light.v-tabs .v-tab--active::before {
  opacity: 0;
}
.v-tab {
  font-size: 16px;
}
</style>
