<template>
  <v-card>
    <v-card-text class="flex flex-col gap-2" v-if="viewContacts.length">
      <div v-for="(contact, i) in viewContacts" :key="i" class="text-gray-600 border rounded-md p-4">
        <div class="flex justify-between">
          <p>
            Name:
            <span>{{ contact.firstName }} {{ contact.lastName }}</span>
          </p>
          <v-icon size="22" color="red" class="mr-4 mb-2 cursor-pointer"
            @click="deleteContact(contact, i)">mdi-delete</v-icon>
        </div>
        <p>
          Phone: <span>{{ contact.phoneNumber }}</span>
        </p>
        <p class="mb-0">
          Email: <span>{{ contact.email }}</span>
        </p>
      </div>
    </v-card-text>
    <NoData title="Nothing to Display" subTitle="You need to add contacts" v-else />
    <!-- <v-card-text > </v-card-text> -->
  </v-card>
</template>
<script>
import NoData from "@/components/NoData.vue";
export default {
  data() {
    return {
    };
  },
  components: {
    NoData,
  },
  props: {
    viewContacts: Array,
    actionContact: Object,
  },
  mounted() {
    // console.log("mounted", this.actionContact);
  },
  watch: {
    actionContact: {
      handler(newVal) {
        this.$props.actionContact = newVal;
      },
      deep: true,
    },
  },
  methods: {
    async deleteContact(contact, index) {
      const item = this.actionContact;
      const branchIndx = contact?.index ?? this.actionContact?.index;
      try {
        const response = await this.$axios.delete(
          `/workforce/companies/${item.customerId}/branches/${branchIndx}/contacts/${contact._id}`
        );
        this.$props.viewContacts.splice(index, 1);
        const successMessage = response?.data?.message || "Contact Deleted Successfully";
        this.$toast.success(successMessage);
      } catch (error) {
        console.log(error);
        const errorMessage = error?.response?.data?.message || error.message;
        this.$toast.error(errorMessage);
      }
    },
  },
};
</script>
