<template>
  <div class="bg-gray-100 dark-bg-custom">
    <div>
      <v-main style="padding: 0px" class="bg-[#f7fafb] dark-bg-custom">
        <div>
          <v-col class="flex">
            <v-tabs
              v-model="tabs"
              align-with-title
              :color="$vuetify.theme.currentTheme.primary"
            >
              <v-tab
                :ripple="false"
                v-for="(tab, index) in tabItems"
                :key="index"
                class="mr-12 ml-2"
                :color="$vuetify.theme.currentTheme.primary"
              >
                {{ tab.name }}
              </v-tab>
              <v-tabs-slider color="#32D583"></v-tabs-slider>
            </v-tabs>
            <v-icon @click="closeDialog" class="p-3">mdi-close</v-icon>
          </v-col>
        </div>
      </v-main>
      <div>
        <component
          :is="currentTab"
          @closeDialog="closeDialog"
          @close="handleTabSwitch"
          @contact-added="handleContactAdded"
          :viewContacts="viewContacts"
          :selectedBranchId="actionContact.locationId"
          :actionContact="actionContact"
          :selectedBranches="selectedBranches"
        ></component>
      </div>
    </div>
  </div>
</template>

<script>
import ViewContact from "./ViewContact";
import AddContact from "../AddContactForm.vue";

export default {
  name: "ContactTabs",
  components: {
    ViewContact,
    AddContact,
  },
  data() {
    return {
      tabs: 0,
    };
  },
  watch: {
    actionContact: {
      handler(newVal) {
        this.$props.actionContact = newVal;
      },
      deep: true,
    },
  },
  props: {
    viewContacts: Array,
    actionContact: Object,
    selectedBranches: Array,
  },
  computed: {
    currentTab() {
      return this.tabItems[this.tabs]?.component;
    },
    tabItems() {
      return [
        {
          key: "ViewContact",
          name: "Existing Contacts",
          component: "ViewContact",
        },
        {
          key: "AddContact",
          name: "Add Contact",
          component: "AddContact",
        },
      ];
    },
  },
  methods: {
    closeDialog() {
      this.$emit("closeDialog");
      this.tabs = 0;
    },
    handleTabSwitch() {
      this.tabs = 0;
    },
    handleContactAdded(contactResponse) {
      let updatedContacts = [];
      if (this.selectedBranches) {
        updatedContacts = contactResponse?.companyBranches.flatMap(
          (branch) => branch.contacts
        );
      } else {
        const branchIndex = this.actionContact?.index;
        updatedContacts =
          contactResponse?.companyBranches[branchIndex]?.contacts;
      }
      this.$emit("updateContacts", updatedContacts);
    },
  },
  mounted() {},
};
</script>
<style scoped>
.v-tab:before {
  background-color: transparent;
}

.theme--light.v-tabs .v-tab:hover::before {
  opacity: 0;
}

.theme--light.v-tabs .v-tab--active:hover::before,
.theme--light.v-tabs .v-tab--active::before {
  opacity: 0;
}

.v-tab {
  font-size: 16px;
}
</style>
