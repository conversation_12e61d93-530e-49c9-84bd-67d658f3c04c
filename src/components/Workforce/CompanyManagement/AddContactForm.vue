<template>
  <!-- <v-dialog v-model="showContactDialog" max-width="600" persistent> -->
  <v-card>
    <v-card-title v-if="!selectedBranches" class="flex w-full justify-between items-center bg-white dark-bg-custom">
      Add Contact
      <v-icon class="cursor-pointer" @click="cancelForm">mdi-close</v-icon>
    </v-card-title>
    <v-card-text>
      <!-- Contact form fields -->
      <div class="flex items-center w-4/5 ml-5 mt-4" v-if="selectedBranches">
        <label class="block font-medium w-32 text-[#464A53] dark-text-color">Customer Address:
          <!-- <span class="text-red-500">*</span> -->
        </label>
        <div class="flex flex-1 border border-gray-300 rounded-lg overflow-hidden">
          <select v-model="branchId" class="form-input flex-1 p-2 bg-transparent focus:outline-none w-48">
            <!-- <option value="">Select Branch Address</option> -->
            <option v-for="(branch, index) in selectedBranches" :key="index" :value="branch._id">
              {{ getFullAddress(branch?.address) }}
            </option>
          </select>
        </div>
      </div>
      <span class="text-red-500 pl-[9.3rem] text-sm">
        <!-- {{ contactLabels.lastName }} is Required. -->
      </span>
      <div class="flex items-center w-4/5 ml-5">
        <label class="block font-medium w-32 text-[#464A53] dark-text-color">{{ contactLabels.firstName }}:
          <span class="text-red-500">*</span>
        </label>
        <div class="flex flex-1 border border-gray-300 rounded-lg overflow-hidden" :style="{
      border:
        checkForm && !newContact.firstName.trim() ? '1px solid red' : '',
    }">
          <input type="text" v-model="newContact.firstName"
            class="form-input flex-1 p-2 bg-transparent focus:outline-none w-48" />
        </div>
      </div>
      <span :style="{
      visibility:
        checkForm && !newContact.firstName.trim() ? 'visible' : 'hidden',
    }" class="text-red-500 pl-[9.3rem] text-sm">
        {{ contactLabels.firstName }} is Required.
      </span>
      <div class="flex items-center w-4/5 ml-5 mt-4">
        <label for="lastName" class="block font-medium w-32 text-[#464A53] dark-text-color">{{ contactLabels.lastName }}:
          <!-- <span class="text-red-500">*</span> -->
        </label>
        <div class="flex flex-1 border border-gray-300 rounded-lg overflow-hidden">
          <input type="text" v-model="newContact.lastName"
            class="form-input flex-1 p-2 bg-transparent focus:outline-none w-48" />
        </div>
      </div>
      <span class="text-red-500 pl-[9.3rem] text-sm">
        <!-- {{ contactLabels.lastName }} is Required. -->
      </span>
      <div class="flex items-center w-4/5 ml-5 mt-4">
        <label class="block font-medium w-32 text-[#464A53] dark-text-color">{{ contactLabels.contactDesignation }}:
          <!-- <span class="text-red-500">*</span> -->
        </label>
        <div class="flex flex-1 border border-gray-300 rounded-lg overflow-hidden">
          <input type="text" v-model="newContact.designation"
            class="form-input flex-1 p-2 bg-transparent focus:outline-none w-48" />
        </div>
      </div>
      <span class="text-red-500 pl-[9.3rem] text-sm">
        <!-- {{ contactLabels.contactDesignation }} is Required. -->
      </span>
      <div class="flex items-center w-4/5 ml-5 mt-4">
        <label class="block font-medium w-32 text-[#464A53] dark-text-color">{{ contactLabels.contactNumber }}:
          <span class="text-red-500">*</span>
        </label>
        <div class="flex flex-1 border border-gray-300 rounded-lg overflow-hidden" :style="{
      border:
        checkForm && !newContact.phoneNumber.trim()
          ? '1px solid red'
          : '',
    }">
          <input type="text" v-model="newContact.phoneNumber"
            class="form-input flex-1 p-2 bg-transparent focus:outline-none w-48" />
        </div>
      </div>
      <span :style="{
      visibility:
        checkForm && !newContact.phoneNumber.trim() ? 'visible' : 'hidden',
    }" class="text-red-500 pl-[9.3rem] text-sm">
        {{ contactLabels.contactNumber }} is Required.
      </span>
      <div class="flex items-center w-4/5 ml-5 mt-4">
        <label class="block font-medium w-32 text-[#464A53] dark-text-color">{{ contactLabels.contactEmail }}:
          <!-- <span class="text-red-500">*</span> -->
        </label>
        <div class="flex flex-1 border border-gray-300 rounded-lg overflow-hidden">
          <input type="text" v-model="newContact.email"
            class="form-input flex-1 p-2 bg-transparent focus:outline-none w-48" />
        </div>
      </div>
      <span class="text-red-500 pl-[9.3rem] text-sm">
        <!-- {{ contactLabels.contactEmail }} is Required. -->
      </span>
    </v-card-text>
    <v-card-actions class="mb-4">
      <v-spacer></v-spacer>
      <v-btn outlined height="mr-4" @click="cancelForm">Cancel</v-btn>
      <v-btn class="white--text" :color="$vuetify.theme.currentTheme.primary" @click="addContact">Add</v-btn>
    </v-card-actions>
  </v-card>
  <!-- </v-dialog> -->
</template>
<script>
import { contactLabels } from "@/utils/workforce/formLabels";
export default {
  name: "AddContact",
  props: {
    selectedBranchId: String,
    showContactDialog: Boolean,
    editContact: Object,
    selectedBranches: { type: Array, default: null },
  },
  data() {
    return {
      contactLabels: contactLabels,
      checkForm: false,
      branchId: this.selectedBranches ? this.selectedBranches[0]?._id : null,
      newContact: {
        firstName: "",
        lastName: "",
        designation: "",
        phoneNumber: "",
        email: "",
      },
    };
  },
  mounted() {
    this.populateContact();
  },
  methods: {
    addContact() {
      this.checkForm = true;
      if (this.validateContactData()) {
        const contactData = {
          contact: {
            firstName: this.newContact.firstName,
            lastName: this.newContact.lastName,
            designation: this.newContact.designation,
            phoneNumber: this.newContact.phoneNumber,
            email: this.newContact.email || undefined,
          },
        };

        const branchId = this.selectedBranchId || this.branchId;
        this.$axios
          .post(`/workforce/addContact/${branchId}`, contactData)
          .then((response) => {
            const updatedCompany = response.data.company;
            this.$emit("contact-added", updatedCompany);

            const addedBranch = updatedCompany.companyBranches.find(
              (branch) => branch._id === branchId
            );
            const lastContact =
              addedBranch.contacts[addedBranch.contacts.length - 1];
            this.$emit("contactAdded", lastContact);
            this.newContact = {
              firstName: "",
              lastName: "",
              designation: "",
              phoneNumber: "",
              email: "",
            };
            this.cancelForm();
            this.showContactDialog = false;
            this.$toast.success(
              "New contact added into the company branch !!!"
            );
          })
          .catch((error) => {
            console.error("Error adding new contact:", error);
            this.$toast.error("Error in contact addition !!!");
          });
      }
    },
    validateContactData() {
      this.checkForm = true;
      if (
        this.newContact.firstName.trim() === "" ||
        this.newContact.phoneNumber.trim() === ""
      ) {
        return false;
      }

      return true;
    },
    cancelForm() {
      this.$emit("close");
      this.checkForm = false;
    },
    populateContact() {
      if (this.editContact) {
        this.newContact.firstName = this.editContact?.firstName;
        this.newContact.lastName = this.editContact?.lastName;
        this.newContact.email = this.editContact?.email;
        this.newContact.designation = this.editContact?.designation;
        this.newContact.phoneNumber = this.editContact?.phoneNumber;
      }
    },
    getFullAddress(address) {
      if (address) {
        const { addressLine1, addressLine2, city, country, pinCode, state } =
          address;

        return (
          [addressLine1, addressLine2, city, country, pinCode, state].join(
            ", "
          ) || ""
        );
      }
      return "—";
    },
  },
};
</script>
