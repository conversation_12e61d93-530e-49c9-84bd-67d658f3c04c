<template>
  <div>
    <v-col>
      <v-row>
        <div
          class="flex mb-2 justify-between items-center"
          v-show="sectionVisibility.progressStats"
        >
          <p class="mb-0 font-semibold text-gray-700 dark-text-white text-base">
            Overall Progress
          </p>
          <div class="flex mb-2 justify-end items-center">
            <p class="mb-0 text-base">Today is {{ currentDate }}</p>
            <div
              class="cursor-pointer"
              v-show="
                isFeatureVisible('SHOW_CASE_ANALYTICS') ||
                isFeatureVisible('SHOW_COMPLAINTS')
              "
            >
              <v-menu offset-y>
                <template v-slot:activator="{ on: menu, attrs }">
                  <v-tooltip top>
                    <template v-slot:activator="{ on: tooltip }">
                      <v-icon v-bind="attrs" v-on="{ ...tooltip, ...menu }"
                        >mdi-dots-vertical</v-icon
                      >
                    </template>
                    <span>Statictics</span>
                  </v-tooltip>
                </template>
                <v-list>
                  <v-list-group
                    v-for="(useCase, i) in useCases"
                    :key="i"
                    no-action
                    @click.stop="emitAll(useCase)"
                    class="w-44"
                  >
                    <template v-slot:activator>
                      <v-list-item-title>{{ useCase }}</v-list-item-title>
                    </template>
                    <v-list-item
                      v-for="(option, j) in getOptions(useCase)"
                      :key="j"
                      @click="emitEvent(option.value, useCase)"
                      class="text-sm px-5"
                    >
                      {{ option.text }}
                    </v-list-item>
                  </v-list-group>
                </v-list>
              </v-menu>
            </div>
          </div>
        </div>
        <div
          class="w-full grid grid-rows-2 gap-4 grid-cols-5"
          v-if="data"
          v-show="
            (isFeatureVisible('SHOW_CASE_ANALYTICS') ||
              isFeatureVisible('SHOW_COMPLAINTS')) &&
            sectionVisibility.progressStats
          "
        >
          <div
            class="row-span-2 col-span-3 rounded-lg p-4 shadow bg-white dark-bg-custom flex items-center justify-center flex-col"
          >
            <p class="text-xs mb-2 text-gray-500">
              {{ isItemTypeCase ? "Case" : taskLable?.tabs?.tasks }} Status
            </p>
            <div class="mb-2 relative">
              <div class="transform -rotate-90">
                <v-progress-circular
                  :color="$vuetify.theme.currentTheme.primary"
                  :value="taskCompletionRate"
                  :size="58"
                  :width="12"
                ></v-progress-circular>
              </div>
              <span
                class="absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 text-xs font-semibold text-gray-500"
                >{{ taskCompletionRate }}%</span
              >
            </div>
            <div v-if="isItemTypeCase">
              <p class="text-gray-600 mb-1">
                Total {{ data.caseStats?.TOTAL_CASES }} Cases
              </p>
              <p class="text-xs mb-0 text-gray-600 text-center">
                open {{ data?.caseStats?.OPEN }} cases
              </p>
            </div>
            <div v-else>
              <p class="text-gray-600 mb-1">
                Total {{ data.taskStats?.TOTAL_TASKS }}
                {{ taskLable?.tabs?.tasks }}
              </p>
              <p class="text-xs mb-0 text-gray-600">
                missed {{ data?.taskStats?.MISSED }}
                {{ taskLable?.tabs?.tasks.toLowerCase() }}
              </p>
            </div>
          </div>
          <div
            class="relative rounded-lg col-span-2 p-4 shadow bg-white dark-bg-custom"
            v-if="isItemTypeCase"
          >
            <p class="font-semibold text-gray-600 mb-1">
              {{ data.caseStats?.CLOSED }}
            </p>
            <div
              class="p-0.5 absolute top-3 right-3 rounded-full flex items-center justify-center bg-teal-500"
            >
              <v-icon color="white" size="15">mdi-check</v-icon>
            </div>
            <p class="mb-0 text-xs text-gray-500">Cases Closed</p>
          </div>
          <div
            class="relative rounded-lg col-span-2 p-4 shadow bg-white dark-bg-custom"
            v-else
          >
            <p class="font-semibold text-gray-600 mb-1">
              {{ data.taskStats?.COMPLETED }}
            </p>
            <div
              class="p-0.5 absolute top-3 right-3 rounded-full flex items-center justify-center bg-teal-500"
            >
              <v-icon color="white" size="15">mdi-check</v-icon>
            </div>
            <p class="mb-0 text-xs text-gray-500">
              {{ taskLable?.tabs?.tasks }} Completed
            </p>
          </div>
          <div
            class="relative rounded-lg col-span-2 p-4 shadow bg-white dark-bg-custom"
          >
            <p class="font-semibold text-gray-600 mb-1">
              {{
                isItemTypeCase
                  ? data.caseStats?.IN_PROGRESS
                  : data.taskStats?.IN_PROGRESS
              }}
            </p>
            <div
              class="p-0.5 absolute top-3 right-3 rounded-full flex items-center justify-center bg-sky-600"
            >
              <v-icon color="white" size="15"
                >mdi-clock-time-five-outline</v-icon
              >
            </div>
            <p class="mb-0 text-xs text-gray-500">In Progress</p>
          </div>
        </div>
        <v-skeleton-loader
          v-else
          class="mx-auto"
          max-width="350"
          type="image"
        ></v-skeleton-loader>
      </v-row>
      <v-row>
        <div class="ml-2 flex flex-col mx-auto" v-if="!isItemTypeCase">
          <div class="mt-3" v-if="series.length && !series.includes(0)">
            <p class="font-semibold text-gray-700 text-base">
              {{ taskLable?.tabs?.tasks }} Statistics
            </p>
            <apexchart
              class="flex justify-center"
              type="pie"
              :options="chartOptions"
              :series="series"
              width="320"
            >
            </apexchart>
          </div>
          <div class="mt-3" v-if="leadSeries.length && !leadSeries.includes(0)">
            <p class="mt-3 font-semibold text-gray-700 text-base">
              {{ leadLable?.tabs?.lead }} Statistics
            </p>
            <apexchart
              class="flex justify-center"
              type="pie"
              :options="leadChartOptions"
              :series="leadSeries"
              width="320"
            >
            </apexchart>
          </div>
        </div>
        <div
          class="slide-container mt-3"
          v-if="
            productsStats.length &&
            isFeatureVisible('SHOW_PRODUCTS_OUT_OF_STOCK')
          "
        >
          <p class="font-semibold text-gray-700 text-base">
            Out Of Stock Products
          </p>
          <v-slide-group show-arrows>
            <v-slide-item
              v-for="(product, index) in productsStats"
              :key="index"
            >
              <v-card
                class="bg-white dark-bg-custom rm-shadow p-2 mr-2 rounded-md"
              >
                <v-card-text>
                  <div class="text-sm font-semibold mb-2">
                    {{ product.productName }}
                  </div>
                  <div class="text-sm">
                    Availability: {{ product.availabilityStatus }}
                  </div>
                  <div class="text-sm">
                    Quantity in Stock: {{ product.quantityInStock }}
                  </div>
                </v-card-text>
              </v-card>
            </v-slide-item>
          </v-slide-group>
        </div>

        <div
          v-show="
            isItemTypeCase &&
            isFeatureVisible('SHOW_CASE_ANALYTICS') &&
            sectionVisibility.visitCollectionStats
          "
        >
          <CollectionWidget :collectionVisits="collectionVisits" />
        </div>
      </v-row>
    </v-col>
  </div>
</template>

<script>
import {
  convertToTitleCase,
  isModuleFeatureAllowed,
  isModuleVisible,
} from "@/utils/common";
import CollectionWidget from "./CollectionWidget";
export default {
  name: "ProgressComponent",
  components: {
    ...(typeof window !== "undefined" && {
      apexchart: () => import("vue-apexcharts"),
    }),
    CollectionWidget,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
    itemType: String,
    subCasesOptions: Array,
    collectionVisits: { type: Array, default: () => [] },
    sectionVisibility: Object,
  },
  data() {
    return {
      orgLang: this.$store.state.orgLanguage,
      enableUseCase: this.$store.state.useCase,
      currentIndex: 0,
      currentDate: this.getCurrentDate(),
      selectedUseCase: "",
      chartWidth: window.innerWidth * 0.28,
      productsStats: [],
      topVisits: [],
    };
  },
  async mounted() {
    window.addEventListener("resize", this.updateChartWidth);
    await this.fetchOutOfStockProducts();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateChartWidth);
  },
  computed: {
    taskCompletionRate() {
      let rate;
      if (this.isItemTypeCase) {
        rate = this.data?.caseStats?.caseCompletionRate;
        return rate ? rate.toFixed() : 0;
      } else {
        rate = this.data?.taskStats?.taskCompletionRate;
        return rate ? rate.toFixed() : 0;
      }
    },
    isItemTypeCase() {
      return this.itemType === "case";
    },
    taskLable() {
      let currentUsecase;
      if (!this.selectedUseCase) {
        currentUsecase = this.enableUseCase[0];
      } else {
        currentUsecase = this.selectedUseCase.toLowerCase();
      }
      const orgConfig = this.orgLang.data || {};

      const taskModules = orgConfig.taskModules || [];

      const currentConfig = taskModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      return currentConfig;
    },
    leadLable() {
      let currentUsecase;
      if (!this.selectedUseCase) {
        currentUsecase = this.enableUseCase[0];
      } else {
        currentUsecase = this.selectedUseCase;
      }
      const orgConfig = this.orgLang.data || {};
      const leadModules = orgConfig.leadModules || [];
      const currentConfig = leadModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      return currentConfig;
    },
    useCases() {
      let useCases;
      if (this.enableUseCase.length > 1) {
        useCases = [
          "All",
          ...this.enableUseCase.map((element) => convertToTitleCase(element)),
        ];
      } else {
        useCases = this.enableUseCase.map((element) => {
          return convertToTitleCase(element);
        });
      }
      return useCases;
    },
    chartOptions() {
      if (!this.data || !this.data.taskStats) {
        return {
          labels: [],
          colors: ["#3399FF", "#14B8A6", "#FFA600"],
          legend: {
            show: true,
            position: "bottom",
          },
        };
      }

      const labels = [];
      if (this.data.taskStats.IN_PROGRESS > 0) {
        labels.push(`In Progress`);
      }
      if (this.data.taskStats.COMPLETED > 0) {
        labels.push(`Completed`);
      }
      if (this.data.taskStats.PENDING > 0) {
        labels.push(`Pending`);
      }
      return {
        labels,
        colors: ["#3399FF", "#14B8A6", "#FFA600"],
        legend: {
          show: false,
          position: "bottom",
        },
      };
    },
    series() {
      if (!this.data || !this.data.taskStats) {
        return [];
      }

      const series = [];
      if (this.data.taskStats.IN_PROGRESS > 0) {
        series.push(this.data.taskStats.IN_PROGRESS);
      }
      if (this.data.taskStats.COMPLETED > 0) {
        series.push(this.data.taskStats.COMPLETED);
      }
      if (this.data.taskStats.PENDING > 0) {
        series.push(this.data.taskStats.PENDING);
      }
      return series;
    },
    leadChartOptions() {
      if (!this.data || !this.data.leadStats) {
        return {
          labels: [],
          colors: ["#3399FF", "#BE61CA"],
          legend: {
            show: false,
            position: "bottom",
          },
        };
      }

      const labels = [];
      labels.push(`Open`);
      labels.push(`Close`);

      return {
        labels,
        colors: ["#3399FF", "#BE61CA"],
        legend: {
          show: true,
          position: "bottom",
        },
      };
    },
    leadSeries() {
      if (!this.data || !this.data.leadStats) {
        return [];
      }

      const series = [];
      series.push(this.data.leadStats.OPEN);
      series.push(this.data.leadStats.CLOSE);

      return series;
    },
    isComplaintEnable() {
      return isModuleVisible("SELLS");
    },
  },
  methods: {
    updateChartWidth() {
      this.chartWidth = window.innerWidth * 0.28;
    },
    getCurrentDate() {
      const dateObj = new Date();
      const options = {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      };
      return dateObj.toLocaleDateString("en-US", options);
    },
    getOptions(useCase) {
      if (useCase === "All") {
        return;
      }
      return [
        { text: "Today's Stats", value: "CURRENT_DAY" },
        { text: "Past 7 Days' Stats", value: "WEEKLY" },
        { text: "Past 30 Days' Stats", value: "MONTHLY" },
      ];
    },
    emitEvent(period, useCase) {
      this.selectedUseCase = useCase.toLowerCase();
      const payload = { period, useCase };
      this.$emit("selectedStat", payload);
    },
    emitAll(useCase) {
      if (useCase !== "All") {
        return;
      }
      const payload = {};
      this.$emit("selectedStat", payload);
    },
    async fetchOutOfStockProducts() {
      try {
        if (!this.isComplaintEnable) {
          return;
        }
        const response = await this.$axios.get("/workforce/org/stats/product");
        this.productsStats = response.data.productsStats;
      } catch (error) {
        console.error(error);
      }
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed("DASHBOARD", feature);
    },
  },
};
</script>
<style scoped>
.slide-container {
  margin: 0;
  overflow: hidden;
}

.rm-shadow {
  box-shadow: none !important;
  min-width: 250px !important;
}
</style>
