<template>
  <v-container class="p-6 rounded-lg w-full">
    <v-row class="mt-4">
      <v-col>
        <h2 class="font-semibold text-gray-700 text-base">
          Latest {{ tasks.length }}/5 Visits
        </h2>
      </v-col>
    </v-row>

    <!-- <v-row class="mb-4 bg-blue-50 dark-bg-custom p-4 rounded-lg">
      <p class="text-md font-semibold text-gray-700 mb-0">
        Total Funds Collected:
        <span class="text-green-600">₹{{ totalFunds }}</span>
      </p>
    </v-row> -->

    <v-row class="flex-wrap gap-2">
      <v-col
        v-for="(task, i) in tasks"
        :key="task._id"
        :class="getBackgroundColor(i)"
        class="task-card p-4 rounded-lg mb-2 dark-bg-custom"
      >
        <div>
          <div class="flex justify-between">
            <p class="text-lg font-semibold text-gray-800">
              {{ task.taskTitle }}
            </p>
            <v-chip small label :color="$vuetify.theme.currentTheme.success">
              {{ convertTitle(task.taskStatus) }}
            </v-chip>
          </div>
          <p class="text-gray-600">Case No.: {{ task.caseNo }}</p>
          <p class="text-gray-600">
            {{ task.meetingOutCome }} Amount:
            <span class="font-semibold text-green-700">
              ₹{{ task.fundCollected }}
            </span>
          </p>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { convertToTitleCase } from "@/utils/common";
export default {
  data() {
    return {};
  },
  props: {
    collectionVisits: Array,
  },
  computed: {
    totalTasks() {
      return this.tasks.length;
    },
    totalFunds() {
      return this.tasks.reduce((total, task) => total + task.fundCollected, 0);
    },
    tasks() {
      return this.$props.collectionVisits;
    },
  },
  methods: {
    convertTitle(str) {
      return convertToTitleCase(str);
    },
    getBackgroundColor(index) {
      const colors = [
        "bg-red-100",
        "bg-green-100",
        "bg-blue-100",
        "bg-yellow-100",
        "bg-purple-100",
      ];
      return colors[index % colors.length];
    },
  },
};
</script>

<style scoped>
.v-container {
  max-width: 600px;
  margin: auto;
}
.task-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
