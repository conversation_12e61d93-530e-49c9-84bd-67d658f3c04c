<template>
  <div class="bg-gray-100">
    <main>
      <!-- Stats Cards - show based on admin settings -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6"
        v-if="sectionVisibility.loanStats"
      >
        <v-card class="p-6" flat>
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">Total Loans</p>
              <h3 class="text-2xl font-bold text-gray-800">{{ loanStatsData?.totalLoans }}</h3>
            </div>
            <div class="p-3 rounded-full bg-indigo-100">
              <v-icon color="indigo" size="24">mdi-cash-multiple</v-icon>
            </div>
          </div>
          <div class="mt-4">
            <span
              :class="getPercentageColor(loanStatsData?.totalLoans, lastMonthStatsData?.totalLoans)"
              class="text-sm font-medium"
            >
              {{
                calculatePercentageChange(
                  loanStatsData?.totalLoans || 0,
                  lastMonthStatsData?.totalLoans || 0
                )
              }}% from last month
            </span>
          </div>
        </v-card>

        <v-card class="p-6" flat>
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">Active Loans</p>
              <h3 class="text-2xl font-bold text-gray-800">{{ loanStatsData?.activeLoans }}</h3>
            </div>
            <div class="p-3 rounded-full bg-green-100">
              <v-icon color="green" size="24">mdi-chart-line</v-icon>
            </div>
          </div>
          <div class="mt-4">
            <span
              :class="
                getPercentageColor(loanStatsData?.activeLoans, lastMonthStatsData?.activeLoans)
              "
              class="text-sm font-medium"
            >
              {{
                calculatePercentageChange(
                  loanStatsData?.activeLoans || 0,
                  lastMonthStatsData?.activeLoans || 0
                )
              }}% from last month
            </span>
          </div>
        </v-card>

        <v-card class="p-6" flat>
          <div class="flex items-center justify-between mb-3">
            <div class="flex-1">
              <p class="text-gray-500 text-sm mb-1">Pending EMIs</p>
              <div class="flex items-baseline gap-2">
                <h3 class="text-2xl font-bold text-gray-800">
                  {{ loanStatsData?.pendingEmis || 0 }}
                </h3>
              </div>
            </div>
            <div class="p-3 rounded-full bg-yellow-100">
              <v-icon color="orange" size="24">mdi-alert-circle</v-icon>
            </div>
          </div>

          <!-- Amount Section -->
          <div class="bg-orange-50 rounded-lg p-3 mb-3">
            <div class="flex items-center justify-between">
              <span class="text-orange-700 text-sm font-medium mr-4">Total Amount</span>
              <div class="flex items-baseline gap-1">
                <span class="text-orange-800 font-bold text-lg"
                  >₹{{ formatAmount(loanStatsData?.pendingEmiAmount || '1892818') }}</span
                >
              </div>
            </div>
          </div>

          <div class="flex justify-between items-center">
            <span
              :class="
                getPercentageColor(loanStatsData?.pendingEmis, lastMonthStatsData?.pendingEmis)
              "
              class="text-sm font-medium"
            >
              {{
                calculatePercentageChange(
                  loanStatsData?.pendingEmis || 0,
                  lastMonthStatsData?.pendingEmis || 0
                )
              }}% from last month
            </span>
            <v-chip color="orange" text-color="white" x-small>
              {{ loanStatsData?.pendingEmis || 0 }} Pending
            </v-chip>
          </div>
        </v-card>

        <v-card class="p-6" flat>
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">Pending PDD Actions</p>
              <h3 class="text-2xl font-bold text-gray-800">
                {{ loanStatsData?.pendingPddActions }}
              </h3>
            </div>
            <div class="p-3 rounded-full bg-red-100">
              <v-icon color="red" size="24">mdi-alert</v-icon>
            </div>
          </div>
          <div class="mt-4">
            <span
              :class="
                getPercentageColor(
                  loanStatsData?.pendingPddActions,
                  lastMonthStatsData?.pendingPddActions
                )
              "
              class="text-sm font-medium"
            >
              {{
                calculatePercentageChange(
                  loanStatsData?.pendingPddActions || 0,
                  lastMonthStatsData?.pendingPddActions || 0
                )
              }}% from last month
            </span>
          </div>
        </v-card>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- EMI Status Chart -->
        <v-card v-if="sectionVisibility.emiStats" class="p-6" flat>
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">EMI Payment Status (Last 6 Months)</h3>
            <v-select
              v-model="emiTimeframe"
              :items="timeframeOptions"
              dense
              outlined
              hide-details
              class="max-w-xs"
            ></v-select>
          </div>
          <apexchart
            type="bar"
            height="350"
            :options="emiChartOptions"
            :series="emiChartSeries"
          ></apexchart>
        </v-card>

        <!-- PDD Actions Chart -->
        <v-card v-if="sectionVisibility.emiActions" class="p-6" flat>
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">PDD Action Status</h3>
            <v-select
              v-model="pddTimeframe"
              :items="pddTimeframeOptions"
              dense
              outlined
              hide-details
              class="max-w-xs"
            ></v-select>
          </div>
          <apexchart
            type="line"
            height="350"
            :options="pddChartOptions"
            :series="pddChartSeries"
          ></apexchart>
        </v-card>
      </div>

      <!-- EMI Payments Table -->
      <v-card class="mb-6" v-if="sectionVisibility.recentEmis" flat>
        <v-card-title class="flex justify-between items-center">
          <span class="text-lg font-semibold">EMI Payments</span>
          <div class="flex justify-end items-center gap-3">
            <v-text-field
              v-model="searchQuery"
              placeholder="Search EMI Payments"
              label="Search EMI Payments"
              prepend-inner-icon="mdi-magnify"
              hide-details
              outlined
              dense
              style="max-width: 200px"
            ></v-text-field>
            <!-- <div class="flex items-center gap-4"> -->
            <v-menu
              v-model="dateRangeMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  :value="dateRangeText"
                  label="Date Range"
                  prepend-inner-icon="mdi-calendar-range"
                  readonly
                  outlined
                  dense
                  hide-details
                  v-bind="attrs"
                  v-on="on"
                  style="max-width: 280px"
                ></v-text-field>
              </template>
              <v-date-picker v-model="dateRange" range @input="onDateRangeChange"></v-date-picker>
            </v-menu>

            <v-btn color="primary" outlined small @click="resetDateFilter"> Reset </v-btn>
            <!-- </div> -->
          </div>
        </v-card-title>

        <v-data-table
          :headers="emiHeaders"
          :items="emiData"
          :loading="loadTable"
          :server-items-length="totalItems"
          :items-per-page="options.itemsPerPage"
          :options.sync="options"
          :footer-props="{
            'items-per-page-options': [5, 10, 15, 20],
            'items-per-page-text': 'Rows per page:',
            'show-first-last-page': true
          }"
          class="pl-4"
          @update:options="handlePageChange"
        >
          <template v-slot:[`item.loanId`]="{ item }">
            <td class="px-4 py-6 font-semibold">{{ item.loan?.applicationNumber || '-' }}</td>
          </template>
          <template v-slot:[`item.customer`]="{ item }">
            <td
              class="px-4 py-6 font-semibold cursor-pointer hover:text-blue-700 dark-hover-text"
              @click="routeToCustomer(item)"
            >
              {{ item.loan?.borrower?.firstName }}
              {{ item.customer?.customerFullName || '-' }}
            </td>
          </template>
          <template v-slot:[`item.dueDate`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              {{ item.dueDate ? $dayjs(item.dueDate).format('DD/MM/YYYY') : '-' }}
            </td>
          </template>
          <template v-slot:[`item.amount`]="{ item }">
            <td class="px-4 py-6 font-semibold">₹{{ item.emiAmount }}</td>
          </template>
          <template v-slot:[`item.outstanding`]="{ item }">
            <td class="pl-8 font-semibold">₹{{ item.outstandingAmount }}</td>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            <v-chip :color="getStatusColor(item.emiStatus)" text-color="white" class="ml-2" small>
              {{ item.emiStatus }}
            </v-chip>
          </template>
          <!-- <template v-slot:item.action="{ item }">
            <v-btn text color="indigo" small @click="handleAction(item)">
              {{ getActionText(item.status) }}
            </v-btn>
          </template> -->
        </v-data-table>
      </v-card>

      <!-- Loan Disbursements Table -->
      <v-card class="mb-6" v-if="disbursementData && disbursementData.length" flat>
        <v-card-title class="flex justify-between items-center">
          <span class="text-lg font-semibold">Loan Disbursements</span>
          <div class="flex justify-end items-center gap-3">
            <v-select
              v-model="selectedPartner"
              :items="partnerOptions"
              item-text="partnerName"
              item-value="_id"
              label="Select Partner"
              prepend-inner-icon="mdi-account-group"
              hide-details
              outlined
              dense
              clearable
              style="max-width: 200px"
            ></v-select>
            <v-menu
              v-model="disbursementDateRangeMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  :value="disbursementDateRangeText"
                  label="Date Range"
                  prepend-inner-icon="mdi-calendar-range"
                  readonly
                  outlined
                  dense
                  hide-details
                  v-bind="attrs"
                  v-on="on"
                  style="max-width: 280px"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="disbursementDateRange"
                range
                @input="onDisbursementDateRangeChange"
              ></v-date-picker>
            </v-menu>
            <v-btn color="primary" outlined small @click="resetDisbursementDateFilter">
              Reset
            </v-btn>
          </div>
        </v-card-title>

        <v-data-table
          :headers="disbursementHeaders"
          :items="disbursementData"
          :loading="loadDisbursementTable"
          :server-items-length="totalDisbursementItems"
          :items-per-page="disbursementOptions.itemsPerPage"
          :options.sync="disbursementOptions"
          :footer-props="{
            'items-per-page-options': [5, 10, 15, 20],
            'items-per-page-text': 'Rows per page:',
            'show-first-last-page': true
          }"
          class="pl-4"
          @update:options="handleDisbursementPageChange"
        >
          <template v-slot:[`item.applicationNumber`]="{ item }">
            <td class="px-4 py-6 font-semibold">{{ item.applicationNumber || '-' }}</td>
          </template>
          <template v-slot:[`item.partnerName`]="{ item }">
            <td
              class="px-4 py-6 font-semibold cursor-pointer hover:text-blue-700 dark-hover-text"
              @click="routeToPartner(item.partner?.partnerName)"
            >
              {{ item.partner?.partnerName || '-' }}
            </td>
          </template>
          <template v-slot:[`item.loanAmountSanctioned`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              ₹{{ item.loanAmountSanctioned?.toLocaleString() }}
            </td>
          </template>
          <template v-slot:[`item.totalDisbursedAmount`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              ₹{{ item.totalDisbursedAmount?.toLocaleString() }}
            </td>
          </template>
          <template v-slot:[`item.disbursementDate`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              {{
                item.loanDisbursements?.[0]?.date
                  ? $dayjs(item.loanDisbursements[0].date).format('DD/MM/YYYY')
                  : '-'
              }}
            </td>
          </template>
          <template v-slot:[`item.disbursementStatus`]="{ item }">
            <v-chip
              :color="getDisbursementStatusColor(item.loanDisbursements?.[0]?.status)"
              text-color="white"
              class="ml-2"
              small
            >
              {{ item.loanDisbursements?.[0]?.status || 'N/A' }}
            </v-chip>
          </template>
          <template v-slot:[`item.referenceNo`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              {{ item.loanDisbursements?.[0]?.referenceNo || '-' }}
            </td>
          </template>
        </v-data-table>
      </v-card>

      <!-- Lender Summary Table -->
      <v-card class="mb-6" flat>
        <v-card-title class="flex justify-between items-center">
          <span class="text-lg font-semibold">Lender Summary</span>
          <div class="flex justify-end items-center gap-3">
            <v-text-field
              v-model="lenderSearchQuery"
              placeholder="Search Lenders"
              prepend-inner-icon="mdi-magnify"
              hide-details
              outlined
              dense
              style="max-width: 200px"
            ></v-text-field>
            <v-menu
              v-model="lenderDateRangeMenu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  :value="lenderDateRangeText"
                  label="Date Range"
                  prepend-inner-icon="mdi-calendar-range"
                  readonly
                  outlined
                  dense
                  hide-details
                  v-bind="attrs"
                  v-on="on"
                  style="max-width: 280px"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="lenderDateRange"
                range
                @input="onLenderDateRangeChange"
              ></v-date-picker>
            </v-menu>
            <v-btn color="primary" outlined small @click="resetLenderDateFilter"> Reset </v-btn>
          </div>
        </v-card-title>
        <v-data-table
          :headers="lenderHeaders"
          :items="filteredLenderData"
          :items-per-page="lenderOptions.itemsPerPage"
          :options.sync="lenderOptions"
          :footer-props="{
            'items-per-page-options': [5, 10, 15, 20],
            'items-per-page-text': 'Rows per page:',
            'show-first-last-page': true
          }"
          show-expand
          :single-expand="singleExpand"
          :expanded.sync="expandedLenders"
          class="elevation-0 pl-4"
          item-key="_id"
        >
          <template v-slot:[`item.partnerName`]="{ item }">
            <td
              class="px-4 py-6 font-semibold cursor-pointer hover:text-blue-700 dark-hover-text"
              @click="routeToPartner(item?.partnerName)"
            >
              {{ item?.partnerName || 'N/A' }}
            </td>
          </template>
          <template v-slot:[`item.totalLoans`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              <v-chip color="blue" text-color="white" small>
                {{ item.totalLoans || 0 }}
              </v-chip>
            </td>
          </template>
          <template v-slot:[`item.totalDisbursed`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              ₹{{ item.totalAmountDisbursed?.toLocaleString() }}
            </td>
          </template>
          <template v-slot:[`item.amountRecovered`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              ₹{{ item.totalAmountRecovered?.toLocaleString() }}
            </td>
          </template>
          <template v-slot:[`item.recoveryRate`]="{ item }">
            <td class="px-4 py-6 font-semibold">
              <v-chip :color="getRecoveryRateColor(item.recoveryRate)" text-color="white" small>
                {{ item.recoveryRate }}%
              </v-chip>
            </td>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            <v-chip
              :color="getLenderStatusColor(item.partnerStatus)"
              text-color="white"
              class="ml-2"
              small
            >
              {{ item.partnerStatus || 'N/A' }}
            </v-chip>
          </template>

          <!-- Expanded content showing loan details -->
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length" class="pa-0">
              <div class="pa-4 bg-gray-50">
                <h4 class="text-md font-semibold mb-4 text-gray-800">
                  Loan Details for {{ item.partnerName }}
                </h4>

                <v-data-table
                  :headers="loanDetailHeaders"
                  :items="item.loans || []"
                  :items-per-page="5"
                  hide-default-footer
                  class="elevation-1 rounded-lg"
                  dense
                >
                  <template v-slot:[`item.applicationNumber`]="{ item: loan }">
                    <td class="px-3 py-2 font-medium">{{ loan.applicationNumber }}</td>
                  </template>
                  <template v-slot:[`item.loanAmount`]="{ item: loan }">
                    <td class="px-3 py-2 font-medium">₹{{ loan.loanAmount?.toLocaleString() }}</td>
                  </template>
                  <template v-slot:[`item.disbursedAmount`]="{ item: loan }">
                    <td class="px-3 py-2 font-medium">
                      ₹{{ loan.disbursedAmount?.toLocaleString() }}
                    </td>
                  </template>
                  <template v-slot:[`item.recoveredAmount`]="{ item: loan }">
                    <td class="px-3 py-2 font-medium">
                      ₹{{ loan.recoveredAmount?.toLocaleString() }}
                    </td>
                  </template>
                  <template v-slot:[`item.recoveryPercentage`]="{ item: loan }">
                    <td class="px-3 py-2 font-medium">
                      <v-chip
                        :color="getRecoveryRateColor(calculateLoanRecoveryRate(loan))"
                        text-color="white"
                        x-small
                      >
                        {{ calculateLoanRecoveryRate(loan) }}%
                      </v-chip>
                    </td>
                  </template>
                  <template v-slot:[`item.outstandingAmount`]="{ item: loan }">
                    <td class="px-3 py-2 font-medium">
                      <span class="text-red-600 font-semibold">
                        ₹{{ (loan.disbursedAmount - loan.recoveredAmount)?.toLocaleString() }}
                      </span>
                    </td>
                  </template>
                </v-data-table>

                <!-- Summary for this lender -->
                <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div class="bg-white p-3 rounded-lg shadow-sm">
                    <div class="text-sm text-gray-600">Total Loans</div>
                    <div class="text-lg font-bold text-blue-600">{{ item.totalLoans }}</div>
                  </div>
                  <div class="bg-white p-3 rounded-lg shadow-sm">
                    <div class="text-sm text-gray-600">Total Disbursed</div>
                    <div class="text-lg font-bold text-green-600">
                      ₹{{ item.totalAmountDisbursed?.toLocaleString() }}
                    </div>
                  </div>
                  <div class="bg-white p-3 rounded-lg shadow-sm">
                    <div class="text-sm text-gray-600">Total Recovered</div>
                    <div class="text-lg font-bold text-purple-600">
                      ₹{{ item.totalAmountRecovered?.toLocaleString() }}
                    </div>
                  </div>
                  <div class="bg-white p-3 rounded-lg shadow-sm">
                    <div class="text-sm text-gray-600">Outstanding</div>
                    <div class="text-lg font-bold text-red-600">
                      ₹{{
                        (item.totalAmountDisbursed - item.totalAmountRecovered)?.toLocaleString()
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </template>
        </v-data-table>
      </v-card>
    </main>
  </div>
</template>

<script>
export default {
  name: 'LoanManagementDashboard',
  props: {
    sectionVisibility: {
      type: Object
    }
  },
  components: {
    apexchart: () => (typeof window !== 'undefined' ? import('vue-apexcharts') : null)
  },
  data() {
    return {
      searchQuery: '',
      lenderSearchQuery: '',
      selectedPartner: null,
      lenderDateRange: [],
      lenderDateRangeMenu: false,
      expandedLenders: [],
      singleExpand: true,
      partnerOptions: [],
      dateRange: [],
      dateRangeMenu: false,
      disbursementDateRange: [],
      disbursementDateRangeMenu: false,
      loadTable: false,
      loadDisbursementTable: false,
      totalItems: 0,
      totalDisbursementItems: 0,
      options: {
        page: 1,
        itemsPerPage: 5
      },
      lenderOptions: {
        page: 1,
        itemsPerPage: 5
      },
      disbursementOptions: {
        page: 1,
        itemsPerPage: 5
      },
      sidebarOpen: false,
      loanStatsData: null,
      lastMonthStatsData: null,
      emiTimeframe: 'Last 6 Months',
      pddTimeframe: 'This Month',
      timeframeOptions: [
        { text: 'Last 6 Months', value: 'Last 6 Months' },
        { text: 'This Year', value: 'This Year' },
        { text: 'Last Year', value: 'Last Year' }
      ],
      pddTimeframeOptions: [
        { text: 'This Month', value: 'This Month' },
        { text: 'Last Month', value: 'Last Month' },
        { text: 'Last Quarter', value: 'Last Quarter' }
      ],
      emiHeaders: [
        { text: 'Loan ID', value: 'loanId', sortable: false },
        { text: 'Customer', value: 'customer', sortable: false },
        { text: 'Due Date', value: 'dueDate', sortable: false },
        // { text: 'payment Date', value: 'paymentDate', sortable: false },
        { text: ' Emi Amount', value: 'amount', sortable: false },
        { text: 'OutStanding Amount', value: 'outstanding', sortable: false },
        { text: 'Status', value: 'status', sortable: false }
        // { text: 'Action', value: 'action', sortable: false }
      ],
      emiData: [],
      disbursementHeaders: [
        { text: 'Application Number', value: 'applicationNumber', sortable: false },
        { text: 'Partner Name', value: 'partnerName', sortable: false },
        { text: 'Sanctioned Amount', value: 'loanAmountSanctioned', sortable: false },
        { text: 'Disbursed Amount', value: 'totalDisbursedAmount', sortable: false },
        { text: 'Disbursement Date', value: 'disbursementDate', sortable: false },
        { text: 'Status', value: 'disbursementStatus', sortable: false },
        { text: 'Reference No', value: 'referenceNo', sortable: false }
      ],
      disbursementData: [],
      lenderHeaders: [
        { text: '', value: 'data-table-expand' },
        { text: 'Lender Name', value: 'partnerName', sortable: true },
        { text: 'Total Loans', value: 'totalLoans', sortable: true },
        { text: 'Total Disbursed', value: 'totalDisbursed', sortable: true },
        { text: 'Amount Recovered', value: 'amountRecovered', sortable: true },
        { text: 'Recovery Rate', value: 'recoveryRate', sortable: true },
        { text: 'Status', value: 'status', sortable: false }
      ],
      lenderData: [],
      loanDetailHeaders: [
        { text: 'Application No', value: 'applicationNumber', sortable: false },
        { text: 'Loan Amount', value: 'loanAmount', sortable: false },
        { text: 'Disbursed', value: 'disbursedAmount', sortable: false },
        { text: 'Recovered', value: 'recoveredAmount', sortable: false },
        { text: 'Recovery %', value: 'recoveryPercentage', sortable: false },
        { text: 'Outstanding', value: 'outstandingAmount', sortable: false }
      ],
      loanDisbursementStatsData: [],
      emiStatusChart: null,
      pddActionsChart: null,
      emiChartOptions: {
        chart: {
          type: 'bar',
          stacked: true,
          toolbar: { show: true },
          zoom: { enabled: true }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            borderRadius: 4,
            dataLabels: {
              total: {
                enabled: true,
                style: { fontSize: '13px', fontWeight: 900 }
              }
            }
          }
        },
        xaxis: {
          type: 'category',
          categories: ['May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct']
        },
        legend: {
          position: 'right',
          offsetY: 40
        },
        fill: {
          opacity: 1,
          colors: ['#10B981', '#F59E0B', '#EF4444']
        },
        colors: ['#10B981', '#F59E0B', '#EF4444'],
        tooltip: {
          y: { formatter: (val) => `${val} EMIs` }
        }
      },
      emiChartSeries: [
        { name: 'Paid', data: [120, 135, 142, 155, 148, 165] },
        { name: 'Pending', data: [35, 28, 32, 25, 30, 22] },
        { name: 'Overdue', data: [18, 15, 20, 12, 10, 8] }
      ],

      pddChartOptions: {
        chart: {
          height: 350,
          type: 'line',
          zoom: { enabled: false }
        },
        dataLabels: { enabled: false },
        stroke: {
          curve: 'smooth',
          width: [3, 3]
        },
        colors: ['#10B981', '#EF4444'],
        xaxis: {
          categories: [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec'
          ]
        },
        tooltip: {
          y: { formatter: (val) => `${val} actions` }
        },
        legend: {
          position: 'top'
        }
      },
      pddChartSeries: [
        { name: 'Completed', data: [45, 52, 38, 24, 33, 26, 21, 20, 6, 8, 15, 10] },
        { name: 'Pending', data: [35, 41, 62, 42, 13, 18, 29, 37, 36, 51, 32, 35] }
      ]
    }
  },
  computed: {
    currentMonthStart() {
      return this.$dayjs().startOf('month').format('YYYY-MM-DD')
    },
    currentDate() {
      return this.$dayjs().format('YYYY-MM-DD')
    },
    lastMonthStart() {
      return this.$dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD')
    },
    lastMonthEnd() {
      return this.$dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')
    },
    startDate() {
      return this.dateRange.length > 0 ? this.dateRange[0] : this.currentMonthStart
    },
    endDate() {
      return this.dateRange.length > 1
        ? this.dateRange[1]
        : this.dateRange.length === 1
        ? this.dateRange[0]
        : this.currentDate
    },
    dateRangeText() {
      if (this.dateRange.length === 0) {
        return `${this.$dayjs(this.currentMonthStart).format('DD/MM/YYYY')} - ${this.$dayjs(
          this.currentDate
        ).format('DD/MM/YYYY')}`
      } else if (this.dateRange.length === 1) {
        return this.$dayjs(this.dateRange[0]).format('DD/MM/YYYY')
      } else {
        const sortedDates = [...this.dateRange].sort()
        return `${this.$dayjs(sortedDates[0]).format('DD/MM/YYYY')} - ${this.$dayjs(
          sortedDates[1]
        ).format('DD/MM/YYYY')}`
      }
    },
    disbursementStartDate() {
      return this.disbursementDateRange.length > 0
        ? this.disbursementDateRange[0]
        : this.currentMonthStart
    },
    disbursementEndDate() {
      return this.disbursementDateRange.length > 1
        ? this.disbursementDateRange[1]
        : this.disbursementDateRange.length === 1
        ? this.disbursementDateRange[0]
        : this.currentDate
    },
    disbursementDateRangeText() {
      if (this.disbursementDateRange.length === 0) {
        return `${this.$dayjs(this.currentMonthStart).format('DD/MM/YYYY')} - ${this.$dayjs(
          this.currentDate
        ).format('DD/MM/YYYY')}`
      } else if (this.disbursementDateRange.length === 1) {
        return this.$dayjs(this.disbursementDateRange[0]).format('DD/MM/YYYY')
      } else {
        const sortedDates = [...this.disbursementDateRange].sort()
        return `${this.$dayjs(sortedDates[0]).format('DD/MM/YYYY')} - ${this.$dayjs(
          sortedDates[1]
        ).format('DD/MM/YYYY')}`
      }
    },
    disbursementApiParams() {
      const params = {
        page: this.disbursementOptions.page,
        limit: this.disbursementOptions.itemsPerPage,
        disbursedStartDate: this.disbursementStartDate,
        disbursedEndDate: this.disbursementEndDate
      }

      if (this.selectedPartner) {
        params.partner = this.selectedPartner
      }

      return params
    },
    apiParams() {
      return {
        page: this.options.page,
        limit: this.options.itemsPerPage,
        start: this.startDate,
        end: this.endDate
      }
    },
    lenderStartDate() {
      return this.lenderDateRange.length > 0 ? this.lenderDateRange[0] : this.currentMonthStart
    },
    lenderEndDate() {
      return this.lenderDateRange.length > 1
        ? this.lenderDateRange[1]
        : this.lenderDateRange.length === 1
        ? this.lenderDateRange[0]
        : this.currentDate
    },
    lenderDateRangeText() {
      if (this.lenderDateRange.length === 0) {
        return `${this.$dayjs(this.currentMonthStart).format('DD/MM/YYYY')} - ${this.$dayjs(
          this.currentDate
        ).format('DD/MM/YYYY')}`
      } else if (this.lenderDateRange.length === 1) {
        return this.$dayjs(this.lenderDateRange[0]).format('DD/MM/YYYY')
      } else {
        const sortedDates = [...this.lenderDateRange].sort()
        return `${this.$dayjs(sortedDates[0]).format('DD/MM/YYYY')} - ${this.$dayjs(
          sortedDates[1]
        ).format('DD/MM/YYYY')}`
      }
    },
    filteredLenderData() {
      if (!this.lenderSearchQuery) {
        return this.lenderData
      }
      return this.lenderData.filter((lender) =>
        (lender.partnerName || '').toLowerCase().includes(this.lenderSearchQuery.toLowerCase())
      )
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchEmiDetails()
      },
      deep: true
    },
    dateRange: {
      handler() {
        this.options.page = 1
        this.fetchEmiDetails()
      },
      deep: true
    },
    disbursementOptions: {
      handler() {
        this.fetchDisbursementDetails()
      },
      deep: true
    },
    disbursementDateRange: {
      handler() {
        this.disbursementOptions.page = 1
        this.fetchDisbursementDetails()
      },
      deep: true
    },
    selectedPartner: {
      handler() {
        this.disbursementOptions.page = 1
        this.fetchDisbursementDetails()
      }
    },
    lenderDateRange: {
      handler() {
        this.lenderStats()
      },
      deep: true
    }
  },
  async mounted() {
    this.dateRange = [this.currentMonthStart, this.currentDate]
    this.disbursementDateRange = [this.currentMonthStart, this.currentDate]
    this.lenderDateRange = [this.currentMonthStart, this.currentDate]

    // this.initCharts()
    await this.loanStats()
    await this.fetchLastMonthStats()
    await this.lenderStats()
    await this.fetchPartnerOptions()
    await this.loanDisbursementStats()
    await this.fetchEmiDetails()
    await this.fetchDisbursementDetails()
  },
  methods: {
    routeToCustomer(item) {
      const customerName = item.customer?.customerFullName || ''
      if (customerName) {
        const url = this.$router.resolve(`/workforce/customers?search=${customerName}`).href
        window.open(url, '_blank')
      }
    },
    routeToPartner(name) {
      const partnerName = name || ''
      if (partnerName) {
        const url = this.$router.resolve(`/workforce/partners?search=${partnerName}`).href
        window.open(url, '_blank')
      }
    },
    formatAmount(amount) {
      if (!amount) return '0'
      const num = typeof amount === 'string' ? parseFloat(amount) : amount

      // Format for Indian currency system
      if (num >= 10000000) {
        return (num / 10000000).toFixed(1) + 'Cr'
      } else if (num >= 100000) {
        return (num / 100000).toFixed(1) + 'L'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      } else {
        return num?.toLocaleString('en-IN')
      }
    },
    calculatePercentageChange(current, previous) {
      if (!previous || previous === 0) {
        return current > 0 ? '+100.0' : '0.0'
      }
      const change = ((current - previous) / previous) * 100
      return change >= 0 ? `+${change.toFixed(1)}` : change.toFixed(1)
    },
    getPercentageColor(current, previous) {
      if (!previous || previous === 0) {
        return current > 0 ? 'text-green-500' : 'text-gray-500'
      }
      return current >= previous ? 'text-green-500' : 'text-red-500'
    },
    async lenderStats() {
      try {
        const params = {
          disbursedStartDate: this.lenderStartDate,
          disbursedEndDate: this.lenderEndDate
        }
        const response = await this.$axios.get('/workforce/collection/partner/stats', { params })
        this.lenderData = response.data.stats?.partnerStats
      } catch (error) {
        console.log(error)
      }
    },
    async fetchPartnerOptions() {
      try {
        const response = await this.$axios.get('/workforce/collection/partners')
        this.partnerOptions = response.data.partners || []
      } catch (error) {
        console.log('Error fetching partner options:', error)
        // Fallback: use partners from lender data if available
        if (this.lenderData && this.lenderData.length > 0) {
          this.partnerOptions = this.lenderData.map((lender) => ({
            _id: lender._id,
            partnerName: lender.partnerName
          }))
        }
      }
    },
    async loanStats() {
      try {
        const params = { start: this.startDate, end: this.endDate }
        const response = await this.$axios.get('/workforce/collection/loan/stats', { params })
        this.loanStatsData = response.data.stats
      } catch (error) {
        console.log('Error fetching loan stats:', error)
      }
    },
    async loanDisbursementStats() {
      try {
        const params = { start: this.disbursedStartDate, end: this.disbursedEndDate }
        const response = await this.$axios.get('/workforce/collection/loan/disbursements', {
          params
        })
        this.loanDisbursementStatsData = response.data.disbursements
      } catch (error) {
        console.log('Error fetching loan stats:', error)
      }
    },
    async fetchLastMonthStats() {
      try {
        const params = { start: this.lastMonthStart, end: this.lastMonthEnd }
        const response = await this.$axios.get('/workforce/collection/loan/stats', { params })
        this.lastMonthStatsData = response.data.stats
      } catch (error) {
        console.log('Error fetching last month stats:', error)
      }
    },
    async fetchEmiDetails() {
      try {
        this.loadTable = true
        const response = await this.$axios.get('/workforce/collection/emi-details', {
          params: this.apiParams
        })

        this.emiData = response.data.emiDetails || []
        this.totalItems = response.data?.pagination?.totalCount || this.emiData.length || 0
        this.loadTable = false
      } catch (error) {
        console.log(error)
        this.loadTable = false
      }
    },
    async fetchDisbursementDetails() {
      try {
        this.loadDisbursementTable = true
        const response = await this.$axios.get('/workforce/collection/loan/disbursements', {
          params: this.disbursementApiParams
        })

        this.disbursementData = response.data.disbursements || []
        this.totalDisbursementItems =
          response.data?.pagination?.totalCount || this.disbursementData.length || 0
        this.loadDisbursementTable = false
      } catch (error) {
        console.log('Error fetching disbursement details:', error)
        this.loadDisbursementTable = false
      }
    },
    initCharts() {
      this.initEmiStatusChart()
      this.initPddActionsChart()
    },
    initEmiStatusChart() {
      const options = {
        series: [
          {
            name: 'Paid',
            data: [120, 135, 142, 155, 148, 165]
          },
          {
            name: 'Pending',
            data: [35, 28, 32, 25, 30, 22]
          },
          {
            name: 'Overdue',
            data: [18, 15, 20, 12, 10, 8]
          }
        ],
        chart: {
          type: 'bar',
          height: 350,
          stacked: true,
          toolbar: {
            show: true
          },
          zoom: {
            enabled: true
          }
        },
        responsive: [
          {
            breakpoint: 480,
            options: {
              legend: {
                position: 'bottom',
                offsetX: -10,
                offsetY: 0
              }
            }
          }
        ],
        plotOptions: {
          bar: {
            horizontal: false,
            borderRadius: 4,
            dataLabels: {
              total: {
                enabled: true,
                style: {
                  fontSize: '13px',
                  fontWeight: 900
                }
              }
            }
          }
        },
        xaxis: {
          type: 'category',
          categories: ['May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct']
        },
        legend: {
          position: 'right',
          offsetY: 40
        },
        fill: {
          opacity: 1,
          colors: ['#10B981', '#F59E0B', '#EF4444']
        },
        colors: ['#10B981', '#F59E0B', '#EF4444'],
        tooltip: {
          y: {
            formatter: function (val) {
              return val + ' EMIs'
            }
          }
        }
      }

      //   this.emiStatusChart = new ApexCharts(document.querySelector("#emiStatusChart"), options)
      this.emiStatusChart.render()
    },
    initPddActionsChart() {
      const options = {
        series: [
          {
            name: 'Completed',
            data: [45, 52, 38, 24, 33, 26, 21, 20, 6, 8, 15, 10]
          },
          {
            name: 'Pending',
            data: [35, 41, 62, 42, 13, 18, 29, 37, 36, 51, 32, 35]
          }
        ],
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
          width: [3, 3]
        },
        colors: ['#10B981', '#EF4444'],
        xaxis: {
          categories: [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec'
          ]
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val + ' actions'
            }
          }
        },
        legend: {
          position: 'top'
        }
      }

      //   this.pddActionsChart = new ApexCharts(document.querySelector("#pddActionsChart"), options)
      this.pddActionsChart.render()
    },

    getStatusColor(status) {
      const statusColors = {
        Pending: 'orange',
        Paid: 'green',
        Overdue: 'red',
        Partial: 'blue'
      }
      return statusColors[status] || 'grey'
    },
    getLenderStatusColor(status) {
      return status === 'ACTIVE' ? 'green' : 'red'
    },
    getDisbursementStatusColor(status) {
      const statusColors = {
        Pending: 'orange',
        Completed: 'green',
        Failed: 'red',
        Processing: 'blue'
      }
      return statusColors[status] || 'grey'
    },
    getStatusTextColor(status) {
      switch (status) {
        case 'Paid':
          return 'white'
        case 'Pending':
          return 'white'
        case 'Overdue':
          return 'white'
        default:
          return 'white'
      }
    },
    getPriorityColor(priority) {
      switch (priority) {
        case 'High':
          return 'red'
        case 'Medium':
          return 'orange'
        case 'Low':
          return 'blue'
        default:
          return 'grey'
      }
    },
    getPriorityTextColor(priority) {
      return 'white'
    },
    getActionText(status) {
      switch (status) {
        case 'Paid':
          return 'View'
        case 'Pending':
          return 'Remind'
        case 'Overdue':
          return 'Take Action'
        default:
          return 'View'
      }
    },
    handleAction(item) {
      // console.log('Action clicked for:', item.loanId)
      // Implement action logic here
    },
    handlePageChange() {
      this.fetchEmiDetails()
    },
    handleDisbursementPageChange() {
      this.fetchDisbursementDetails()
    },
    resetDateFilter() {
      this.dateRange = [this.currentMonthStart, this.currentDate]
      this.fetchEmiDetails()
    },
    resetDisbursementDateFilter() {
      this.disbursementDateRange = [this.currentMonthStart, this.currentDate]
      this.fetchDisbursementDetails()
    },
    onDateRangeChange() {
      if (this.dateRange.length === 2) {
        this.dateRangeMenu = false
      }
    },
    onDisbursementDateRangeChange() {
      if (this.disbursementDateRange.length === 2) {
        this.disbursementDateRangeMenu = false
      }
    },
    onLenderDateRangeChange() {
      if (this.lenderDateRange.length === 2) {
        this.lenderDateRangeMenu = false
      }
    },
    resetLenderDateFilter() {
      this.lenderDateRange = [this.currentMonthStart, this.currentDate]
      this.lenderStats()
    },
    getRecoveryRateColor(rate) {
      const numRate = parseFloat(rate) || 0
      if (numRate >= 80) return 'green'
      if (numRate >= 60) return 'orange'
      if (numRate >= 40) return 'amber'
      return 'red'
    },
    calculateLoanRecoveryRate(loan) {
      if (!loan.disbursedAmount || loan.disbursedAmount === 0) return '0.0'
      const rate = (loan.recoveredAmount / loan.disbursedAmount) * 100
      return rate.toFixed(1)
    }
  }
}
</script>

<style scoped>
.max-w-xs {
  max-width: 20rem;
}

/* Custom scrollbar styling */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
