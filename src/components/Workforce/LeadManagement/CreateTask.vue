<template>
  <v-card class="w-[54rem]" flat>
    <div
      v-if="!minimal"
      class="flex justify-between align-center sticky top-0 bg-white dark-bg-custom z-50"
    >
      <v-card-title style="min-width: 200px">{{
        editedTask ? `Update ${taskLable.task}` : `Create ${taskLable.task}`
      }}</v-card-title>
      <div>
        <v-chip
          class="rounded-xl white--text font-semibold"
          small
          flat
          v-if="isComplaintsEnabled && localSelectedLead"
          :color="warrantyChipColor"
        >
          {{ warrantyChipText }}
        </v-chip>
        <v-chip
          class="rounded-xl white--text font-semibold"
          small
          flat
          v-if="isComplaintsEnabled && localSelectedLead"
          :color="amcChipColor"
        >
          {{ amcChipText }}
        </v-chip>
        <v-chip
          class="rounded-xl white--text font-semibold"
          small
          flat
          v-if="
            isComplaintsEnabled && editedTask?.complaintDetails?.paymentStatus
          "
          :color="paymentChipColor"
        >
          {{ paymentChipText }}
        </v-chip>
        <v-btn
          class="mr-2"
          :color="$vuetify.theme.currentTheme.primary"
          text
          v-show="
            editedTask && editedTask?.complaintDetails?.requirePartsReplacement
          "
          @click.stop.prevent="requestInventory(editedTask)"
          >Part Request</v-btn
        >
        <!-- <v-btn
          class="mr-2"
          color="primary"
          text
          v-show="
            editedTask && editedTask?.complaintDetails?.requirePartsReplacement
          "
          @click.stop.prevent="requestInventory(editedTask)"
          >Shop Now</v-btn
        > -->
        <v-btn plain class="crossBtn" @click="closeDialog">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </div>
    </div>

    <div class="p-4">
      <v-form
        :disabled="minimal ? !editMode : false"
        ref="form"
        v-model="valid"
        lazy-validation
      >
        <div class="flex items-center" v-show="!isProjectEnable">
          <v-autocomplete
            class="w-1/2"
            v-model="lead"
            item-text="leadTitle"
            :placeholder="taskLable.taskFormObj.leadTitle"
            @update:search-input="fetchLeads"
            item-value="_id"
            :disabled="!!editedTask || !!localSelectedLead"
            :label="taskLable.taskFormObj.leadTitle"
            :items="leads"
            :rules="!localSelectedLead ? rules.lead : []"
            hide-no-data
            required
            clearable
            outlined
            dense
          ></v-autocomplete>
          <div class="p-1" />
          <v-text-field
            class="w-1/2"
            v-model="company.name"
            disabled
            :label="taskLable.taskFormObj.companyName"
            required
            outlined
            dense
          ></v-text-field>
        </div>
        <div class="flex items-center">
          <v-text-field
            class="w-1/2"
            v-if="editedTask && isComplaintsEnabled"
            v-model="bdNumber"
            label="BD. Number"
            disabled
            outlined
            dense
          ></v-text-field>
          <div class="p-1" />
          <v-text-field
            class="w-1/2"
            v-if="editedTask && isComplaintsEnabled"
            v-model="CpNumber"
            label="Complaint Number"
            disabled
            outlined
            dense
          ></v-text-field>
        </div>
        <v-select
          v-model="companyBranch"
          :label="taskLable.taskFormObj.companyBranch"
          :items="company.branches"
          v-show="!isProjectEnable"
          item-text="label"
          item-value="value"
          required
          outlined
          dense
        ></v-select>
        <v-select
          v-model="title"
          :label="taskLable.taskFormObj.taskTitle"
          :rules="rules.task"
          :items="complaintOptions"
          v-if="complaintOptions.length"
          hint="Required *"
          persistent-hint
          required
          outlined
          dense
        ></v-select>
        <v-text-field
          v-model="title"
          :label="taskLable.taskFormObj.taskTitle"
          :rules="rules.task"
          v-else
          required
          outlined
          dense
        ></v-text-field>
        <v-select
          v-model="linkedRequestPart"
          :items="linkedParts"
          item-text="label"
          item-value="_id"
          label="Part Linked"
          small-chips
          multiple
          readonly
          v-if="editedTask && isProjectEnable"
          outlined
          dense
        >
        </v-select>
        <v-text-field
          v-model="tad"
          :label="taskLable.taskFormObj.tad"
          :rules="rules.tad"
          hint="Required *"
          persistent-hint
          v-if="isCollectionsEnabled"
          outlined
          dense
        ></v-text-field>
        <v-text-field
          v-model="currentBalance"
          :label="taskLable.taskFormObj.currentBalance"
          :rules="rules.currentBalance"
          hint="Required *"
          persistent-hint
          v-if="isCollectionsEnabled"
          outlined
          dense
        ></v-text-field>
        <div v-if="isLink(desc)">
          <span class="text-gray-300 text-xs">{{
            taskLable.taskFormObj.description
          }}</span>
          <img
            class="h-48 mt-1 mb-5 rounded-lg overflow-hidden object-fit object-center"
            :src="desc"
          />
        </div>
        <v-text-field
          v-else
          v-model="desc"
          :label="taskLable.taskFormObj.description"
          :rules="rules.description"
          outlined
          dense
        ></v-text-field>
        <v-select
          v-if="taskLable.taskFormObj.natureOfComplaint"
          v-model="complaintNature"
          :items="natureOfComplaint"
          item-value="value"
          :label="taskLable.taskFormObj.natureOfComplaint"
          outlined
          dense
        ></v-select>
        <v-select
          v-model="isReplacement"
          v-if="isComplaintsEnabled"
          label="Part Replacement Required"
          :items="replacementOptions"
          required
          outlined
          dense
        ></v-select>
        <v-select
          v-model="isChargeRequired"
          v-if="isComplaintsEnabled"
          label="Need To Charge Customer"
          :items="chargeCustomerOptions"
          required
          outlined
          dense
        ></v-select>
        <v-select
          v-model="paymentStatus"
          v-if="isChargeRequired === 'Yes'"
          label="Payment Status"
          outlined
          dense
          :items="paymentOptions"
        ></v-select>
        <div class="flex items-center" v-if="paymentStatus === 'COMPLETED'">
          <v-text-field
            class="w-1/2"
            v-model="transactionNumber"
            label="Transaction/Receipt Number"
            :rules="rules.transaction"
            hint="Required *"
            outlined
            dense
            persistent-hint
          ></v-text-field>
          <div class="p-1" />
          <v-text-field
            class="w-1/2"
            v-model="modeOfPayment"
            outlined
            dense
            label="Payment Mode"
          ></v-text-field>
        </div>
        <div class="flex items-center" v-if="paymentStatus === 'COMPLETED'">
          <v-text-field
            class="w-1/3"
            v-model="paymentDate"
            type="datetime-local"
            label="Payment Date"
            outlined
            dense
          ></v-text-field>
          <div class="p-1" />
          <v-text-field
            class="w-1/3"
            v-model="challanNumber"
            outlined
            dense
            label="Bill Number"
          ></v-text-field>
          <div class="p-1" />
        </div>
        <v-text-field
          v-if="paymentStatus === 'PO'"
          v-model="poNumber"
          label="PO Number"
          :rules="rules.PO"
          hint="Required *"
          persistent-hint
          outlined
          dense
        ></v-text-field>
        <v-text-field
          v-if="paymentStatus === 'PO'"
          v-model="msrNumber"
          label="MSR Number"
          outlined
          dense
        ></v-text-field>
        <v-text-field
          v-if="isChargeRequired === 'Yes'"
          v-model="paymentReceived"
          label="Payment Amount"
          :rules="rules.payment"
          hint="Required *"
          persistent-hint
          outlined
          dense
        ></v-text-field>
        <v-select
          v-model="isApprovalRequired"
          label="Approval Required"
          v-if="
            isComplaintsEnabled &&
            isChargeRequired === 'Yes' &&
            paymentStatus !== 'COMPLETED'
          "
          :items="approvalOptions"
          required
          outlined
          dense
        ></v-select>
        <v-select
          v-model="approvalFrom"
          label="Approved From"
          v-if="
            isApprovalRequired === 'Yes' &&
            isChargeRequired === 'Yes' &&
            paymentStatus !== 'COMPLETED'
          "
          :items="users"
          item-text="full_name"
          :rules="rules.approval"
          item-value="id"
          hint="Required *"
          persistent-hint
          required
          outlined
          dense
        ></v-select>
        <v-text-field
          v-model="approvalComment"
          label="Approval Comment"
          v-if="
            isApprovalRequired === 'Yes' &&
            isChargeRequired === 'Yes' &&
            paymentStatus !== 'COMPLETED'
          "
          :rules="rules.comment"
          hint="Required *"
          outlined
          dense
          persistent-hint
        ></v-text-field>
        <div class="flex items-center">
          <v-autocomplete
            class="w-1/2"
            v-model="assignedTo"
            :label="taskLable.taskFormObj.assignedTo"
            :items="users"
            item-text="full_name"
            item-value="id"
            clearable
            required
            outlined
            dense
          ></v-autocomplete>
          <div class="p-1" />
          <v-select
            class="w-1/2"
            v-model="contactTo"
            :label="taskLable.taskFormObj.contactTo"
            :items="contacts"
            item-text="name"
            item-value="value"
            required
            outlined
            dense
          ></v-select>
        </div>

        <div
          class="flex items-center"
          v-show="!isProjectEnable && !allowTaskAutoEstimation"
        >
          <v-text-field
            class="w-1/2"
            type="datetime-local"
            v-model="startTime"
            :label="taskLable.taskFormObj.startDate"
            :min="minStartDateTime"
            :rules="rules.startDate"
            hint="Required *"
            persistent-hint
            required
            outlined
            dense
          ></v-text-field>
          <div class="p-1" />
          <v-text-field
            v-show="orgCode !== 'MITTAL'"
            class="w-1/2"
            v-model="endTime"
            type="datetime-local"
            :label="taskLable.taskFormObj.endDate"
            :min="minEndDateTime"
            :disabled="minimal ? !editMode : !startTime"
            :rules="rules.endDate"
            hint="Required *"
            persistent-hint
            required
            outlined
            dense
          ></v-text-field>
        </div>
        <v-select
          v-if="taskConfig?.directComplete"
          v-model="isDirectCompletionAllowed"
          label="Task Direct Completion"
          :items="completionOptions"
          item-text="name"
          item-value="value"
          outlined
          dense
        ></v-select>
        <v-select
          v-model="status"
          :label="taskLable.taskFormObj.taskStatus"
          :items="statuses"
          item-text="label"
          item-value="value"
          :rules="rules.status"
          required
          outlined
          dense
        ></v-select>
        <v-autocomplete
          v-model="supportingStaff"
          label="Supporting Staff"
          :items="users"
          item-text="full_name"
          item-value="_id"
          clearable
          multiple
          outlined
          dense
          required
        ></v-autocomplete>

        <!-- Frequency code -->
        <day-frequency
          class="mb-4"
          @selection-updated="handleSelectedDaysUpdated"
          ref="dayFrequencyComponent"
          :selectedDaysDisabled="selectedDaysDisabled"
          v-if="canSetFrequency && taskConfig?.recurAllow"
        ></day-frequency>
        <div class="flex items-center" v-if="isComplaintsEnabled && editedTask">
          <v-text-field
            v-model="serviceReportNumber"
            label="Service Report No."
            outlined
            dense
          ></v-text-field>
        </div>
        <div v-if="isComplaintsEnabled && editedTask">
          <v-textarea
            @input="validateRemarks"
            auto-grow
            label="Remarks"
            v-model="managerRemarks"
            outlined
            dense
          ></v-textarea>

          <div class="flex justify-end">
            <p class="text-sm text-gray-500 mb-0">
              {{ remainingChar }} character remaining
            </p>
          </div>
        </div>
        <InventoryRequest
          v-show="isProjectEnable"
          @cancel="closeRequestDialog"
          :selectedComplaintId="selectedComplaintId"
          @addedProduct="handleAddedPart"
        />

        <div
          v-if="minimal ? minimal && editMode : true"
          class="flex items-center justify-between"
        >
          <div class="w-full">
            <v-btn
              class="font-bold rounded-md"
              outlined
              block
              @click="closeDialog"
              >Cancel</v-btn
            >
          </div>
          <div class="p-2" />
          <div class="w-full">
            <v-btn
              @click="handleButtonClick"
              block
              class="white--text rounded-md"
              :color="$vuetify.theme.currentTheme.primary"
            >
              {{ editedTask ? "Update" : "Create" }}
            </v-btn>
          </div>
        </div>
      </v-form>
    </div>
    <v-dialog v-model="requestDialog" max-width="63rem">
      <div>
        <InventoryRequest
          @cancel="closeRequestDialog"
          :selectedComplaintId="selectedComplaintId"
        />
      </div>
    </v-dialog>
  </v-card>
</template>

<script>
import { convertToTitleCase } from "@/utils/common";
import { taskCreationSuccess } from "@/utils/toastMessages";
import { isValidHttpUrl } from "@/utils/common";
import InventoryRequest from "../InventoryRequest";
import DayFrequency from "@/components/DayFrequency.vue";
export default {
  name: "CreateTask",
  components: {
    DayFrequency,
    InventoryRequest,
  },
  data() {
    return {
      orgCode: this.$storage.getUniversal("organization_code"),
      taskConfig: this.$storage.getUniversal("task"),
      taskDirectCompletion: this.$storage.getUniversal("task"),
      orgLang: this.$store.state.orgLanguage,
      title: "",
      desc: "",
      tad: "",
      currentBalance: "",
      assignedTo: null,
      status: "",
      bdNumber: "",
      paymentReceived: "",
      localSelectedLead: null,
      CpNumber: "",
      isReplacement: "No",
      poNumber: "",
      msrNumber: "",
      serviceReportNumber: "",
      isDirectCompletionAllowed: false,
      isChargeRequired: "No",
      valid: true,
      // widthSet: false,
      requestDialog: false,
      isApprovalRequired: this.approvalRequired(),
      approvalFrom: null,
      approvalComment: "",
      users: [],
      leads: [],
      statuses: [],
      linkedRequestPart: [],
      supportingStaff: [],
      complaintOptions: [],
      replacementOptions: ["Yes", "No"],
      completionOptions: [
        { name: "Yes", value: true },
        { name: "No", value: false },
      ],
      chargeCustomerOptions: ["Yes", "No"],
      approvalOptions: ["Yes", "No"],
      paymentOptions: ["COMPLETED", "PENDING", "PO"],
      paymentStatus: "PENDING",
      transactionNumber: "",
      modeOfPayment: "",
      paymentDate: "",
      challanNumber: null,
      lead: "",
      companyBranch: "",
      contactTo: "",
      startTime: this.$dayjs().format("YYYY-MM-DDTHH:mm"),
      endTime: this.$dayjs().endOf("day").format("YYYY-MM-DDTHH:mm"),
      selectedComplaintId: null,
      linkedTaskId: null,
      linkedCustomerId: null,
      addedProducts: null,
      managerRemarks: "",
      allowTaskAutoEstimation: true,
      taskFrequency: {
        isRepeat: false,
        selectedDays: [],
      },
      natureOfComplaint: ["CALL", "EMAIL", "BY SALES PERSON"],
      complaintNature: "CALL",
      rules: {
        lead: [
          (val) =>
            (val || "").length > 0 ||
            `${this.taskLable.taskFormObj.leadTitle} is required`,
        ],
        task: [
          (val) =>
            (val || "").length > 0 ||
            `${this.taskLable.taskFormObj.taskTitle} is required`,
        ],
        description: [
          (val) => {
            if (this.isComplaintsEnabled) {
              return (
                (val || "").length > 0 ||
                `${this.taskLable.taskFormObj.description} is required`
              );
            }
            return true;
          },
        ],
        startDate: [
          (val) =>
            (val || "").length > 0 ||
            `${this.taskLable.taskFormObj.startDate} is required`,
        ],
        endDate: [
          (val) =>
            (val || "").length > 0 ||
            `${this.taskLable.taskFormObj.endDate} is required`,
          () => {
            if (this.$dayjs(this.startTime).diff(this.endTime) > 0) {
              return `${this.taskLable.taskFormObj.endDate} should be greater than start time`;
            }
            return true;
          },
        ],
        status: [
          (val) =>
            (val || "").length > 0 ||
            `${this.taskLable.taskFormObj.taskStatus} is required`,
        ],
        approval: [
          (val) => {
            if (
              this.isChargeRequired === "Yes" ||
              this.paymentStatus === "PENDING" ||
              this.selectedLead?.productWarrantyStatus === "OUT_OF_WARRANTY"
            ) {
              return (val || "").length > 0 || `Approval From is required`;
            }
            return true;
          },
        ],
        comment: [
          (val) => {
            if (
              this.isChargeRequired === "Yes" ||
              this.paymentStatus === "PENDING" ||
              this.selectedLead?.productWarrantyStatus === "OUT_OF_WARRANTY"
            ) {
              return (val || "").length > 0 || `Approval Comment is required`;
            }
            return true;
          },
        ],
        tad: [
          (val) =>
            (val || "").length > 0 ||
            `${this.taskLable.taskFormObj.tad} is required`,
        ],
        currentBalance: [
          (val) =>
            (val || "").length > 0 ||
            `${this.taskLable.taskFormObj.currentBalance} is required`,
        ],
        PO: [(val) => (val || "").length > 0 || `PO Number is required`],
        transaction: [
          (val) => (val || "").length > 0 || `Transaction Number is required`,
        ],
        payment: [
          (val) => (val || "").length > 0 || `Payment Amount is required`,
        ],
      },
    };
  },
  computed: {
    company() {
      let lead =
        this.localSelectedLead ||
        this.leads.find((lead) => lead._id === this.lead);

      if (typeof lead === "string") lead = this.leadProp;

      const name = lead?.company?.name || "";
      const branches =
        lead?.company?.companyBranches.map((branch) => ({
          label: `${branch.address.addressLine1} ${branch.address.city} ${branch.address.state} ${branch.address.pinCode}`,
          value: branch._id,
        })) || [];

      return {
        name,
        branches,
      };
    },
    contacts() {
      let lead =
        this.localSelectedLead ||
        this.leads.find((lead) => lead._id === this.lead);

      if (typeof lead === "string") lead = this.leadProp;

      const branch = lead?.company?.companyBranches.find(
        (branch) => branch._id === lead.selectedBranch
      );

      const contacts = branch?.contacts?.map((contact) => ({
        name: `${contact.firstName} ${contact.lastName}`,
        value: contact._id,
      }));

      return contacts || [];
    },
    minStartDateTime() {
      const now = new Date();
      return now.toISOString().substr(0, 16);
    },
    minEndDateTime() {
      const newStartTime = this.startTime || this.minStartDateTime;

      return this.$dayjs(newStartTime)
        .set("hour", 0)
        .set("minute", 0)
        .set("second", 0)
        .format("YYYY-MM-DDTHH:mm");
    },
    useCaseTaskConfig() {
      const currentUsecase = this.$route.params.usecase;
      const orgConfig = this.orgLang.data || {};
      const leadModules = orgConfig.taskModules || [];
      const currentConfig = leadModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      return currentConfig;
    },
    taskLable() {
      return {
        taskFormObj: this.useCaseTaskConfig?.addTaskForm || {},
        task: this.useCaseTaskConfig?.taskTable?.columns?.taskTitle || "",
      };
    },
    isComplaintsEnabled() {
      if (this.useCaseTaskConfig?.useCaseType === "inventory") return true;
      return false;
    },
    isCollectionsEnabled() {
      if (this.$route.params.usecase === "collection") {
        return true;
      }
      return false;
    },
    requirePartsReplacement() {
      if (this.isReplacement === "Yes") {
        return true;
      }
      return false;
    },
    needToChargeCustomer() {
      if (this.localSelectedLead?.productWarrantyStatus === "IN_WARRANTY")
        return true;
      if (this.isChargeRequired === "Yes") {
        return true;
      }
      return false;
    },
    warrantyChipColor() {
      return this.localSelectedLead?.productWarrantyStatus === "IN_WARRANTY"
        ? "green"
        : "red";
    },
    amcChipColor() {
      return this.localSelectedLead?.productHaveAmcPlan ? "green" : "red";
    },
    warrantyChipText() {
      return this.localSelectedLead?.productWarrantyStatus === "IN_WARRANTY"
        ? "Under Warranty"
        : "Out of Warranty";
    },
    amcChipText() {
      return this.localSelectedLead?.productHaveAmcPlan
        ? "AMC Plan Active"
        : "AMC Inactive";
    },
    paymentChipText() {
      return this.editedTask?.complaintDetails?.paymentStatus === "COMPLETED"
        ? "Payment Completed"
        : "Payment Pending";
    },
    paymentChipColor() {
      return this.editedTask?.complaintDetails?.paymentStatus === "COMPLETED"
        ? "green"
        : "red";
    },
    selectedDaysDisabled() {
      return this.editedTask;
    },
    isInventoryEnable() {
      return this.useCaseTaskConfig.useCaseType === "inventory";
    },
    isProjectEnable() {
      return this.useCaseTaskConfig.useCaseType === "project";
    },
    linkedParts() {
      const products =
        this.editedTask?.complaintDetails?.linkedRequestId?.products.map(
          (el) => {
            return {
              ...el,
              label: `${el.product} - (Qty-${el.quantityRequested})`,
            };
          }
        );
      return products;
    },
    remainingChar() {
      const character = this.managerRemarks
        .trim()
        .split("")
        .filter((character) => character !== "");
      return 1000 - character.length;
    },
  },
  props: {
    onUpdate: Function,
    minimal: Boolean,
    editMode: Boolean,
    closeModal: Function,
    selectedLead: Object,
    onCreate: Function,
    leadProp: Object,
    canSetFrequency: Boolean,
    taskTableSwitch: Function,
    editedTask: Object,
  },
  async mounted() {
    this.fetchUsers();
    this.fetchTaskStatuses();
    this.getConfig();
    this.status = "Pending";
    if (this.lead) {
      this.widthSet = true;
    }
    if (this.$props.selectedLead) {
      this.lead = this.$props.selectedLead;
    }
  },
  watch: {
    startTime(newValue) {
      this.endTime = this.$dayjs(newValue)
        .endOf("day")
        .format("YYYY-MM-DDTHH:mm");
    },

    paymentStatus: {
      immediate: true,
      handler(val) {
        if (val === "PENDING") this.isApprovalRequired = "Yes";
      },
    },
    leadProp: {
      immediate: true,
      handler(val) {
        if (val) {
          this.localSelectedLead = val;
          this.lead = val._id;
          if (!this.leads.length) this.leads = [val];
          this.isChargeRequired =
            val.productWarrantyStatus === "OUT_OF_WARRANTY" ? "Yes" : "No";
        }
      },
    },
    // editedTask: {
    //   immediate: true,
    //   handler(val) {
    //     console.log(val);
    //     this.editedTask = val;
    //     this.lead = this.editedTask?.leadDetails
    //     this.initializeFormFields();
    //   },
    // },
    selectedLead: {
      immediate: true,
      handler(val) {
        if (val) {
          this.localSelectedLead = val;
          this.isChargeRequired =
            val.productWarrantyStatus === "OUT_OF_WARRANTY" ? "Yes" : "No";
        }
      },
    },
    lead: {
      immediate: true,
      handler(newLead) {
        if (newLead) {
          this.fetchLeads(newLead);
        }
      },
    },
  },
  methods: {
    isLink(str) {
      return isValidHttpUrl(str);
    },
    handleSelectedDaysUpdated(taskFrequency) {
      this.taskFrequency = taskFrequency;
    },
    async getConfig() {
      try {
        const response = await this.$axios.get("/workforce/org/config");

        const data = response.data?.config;
        this.complaintOptions = data.predefinedComplaintTitles;
        this.allowTaskAutoEstimation = data.allowTaskAutoEstimation;
      } catch (error) {
        console.error("Error fetching config:", error);
      }
    },
    async handleSubmit() {
      let selectedLeadId = this.localSelectedLead
        ? this.localSelectedLead._id
        : this.lead;

      const currentUsecase = this.$route.params.usecase.toLowerCase();

      const data = {
        useCase: currentUsecase,
        taskTitle: this.title,
        taskDescription: this.desc,
        assignedTo: this.assignedTo || undefined,
        taskStartDateTime: this.startTime,
        taskEndDateTime: this.endTime,
        taskStatus: this.status.toUpperCase().replace(/[\s-]/g, "_"),
        isRepeated: this.taskFrequency.isRepeat,
        selectedWeekDays: this.taskFrequency.selectedDays,
        meetingDetails: {},
      };
      if (this.companyBranch) {
        data.selectedBranch = this.companyBranch;
      }
      if (this.supportingStaff.length) {
        data.supportingStaff = this.supportingStaff;
      }
      if (this.isDirectCompletionAllowed) {
        data.isDirectCompletionAllowed = this.isDirectCompletionAllowed;
      }

      if (this.contactTo) {
        data.meetingDetails = {
          meetingWith: this.contactTo,
        };
      } else {
        data.meetingDetails = {};
      }

      if (this.isComplaintsEnabled || this.isProjectEnable) {
        selectedLeadId += "?action=complaint";
        data.complaintDetails = {
          natureOfComplaint: this.complaintNature,
          requirePartsReplacement: this.requirePartsReplacement,
          needToChargeCustomer: this.needToChargeCustomer,
        };
        if (this.needToChargeCustomer) {
          data.complaintDetails.paymentStatus = this.paymentStatus;
        }
        if (this.needToChargeCustomer) {
          data.complaintDetails.paymentDetails = {
            transactionNumber: this.transactionNumber,
            modeOfPayment: this.modeOfPayment,
            paymentMadeOn: this.paymentDate,
            challanNumber: this.challanNumber,
            paymentReceived: this.paymentReceived,
          };
          if (this.poNumber) {
            data.complaintDetails.poNumber = this.poNumber;
            data.complaintDetails.msrNumber = this.msrNumber;
          }
        }
      }
      if (this.isCollectionsEnabled) {
        data.tda = this.tad;
        data.currentBalance = this.currentBalance;
      }

      const isValid = this.$refs.form.validate();
      if (!isValid) return;
      try {
        const response = await this.$axios.post(
          `/workforce/task/${selectedLeadId}`,
          data
        );
        this.$emit("task-created", response.data);
        const newTaskId = response.data.lead?.tasks[0]?._id;
        const newComplaintId =
          response.data.lead?.tasks[0]?.complaintDetails?._id;
        if (this.approvalFrom || this.addedProducts) {
          this.approvalSubmit(newTaskId, newComplaintId);
        }
        this.$toast.success(taskCreationSuccess(this.taskLable.task));
        this.closeDialog();
      } catch (error) {
        console.log(error);
        const errorMessage = error?.response?.data?.message || "Order failed.";
        this.$toast.error(errorMessage);
      }
    },
    async approvalSubmit(id, complaintId) {
      try {
        let approvalPayload = {};

        if (this.approvalFrom || this.addedProducts) {
          if (this.addedProducts && this.isProjectEnable) {
            approvalPayload = {
              requestApprovalsfrom: this.addedProducts?.requestApprovalsfrom,
              products: this.addedProducts?.products,
              task: id,
              complaint: complaintId,
              request_type: "INVENTORY_REQUEST",
            };
          } else {
            approvalPayload = {
              requestApprovalsfrom: [
                {
                  approvalFrom: this.approvalFrom,
                },
              ],
              task: id,
              complaint: complaintId,
              comment: this.approvalComment,
            };
          }

          const response = await this.$axios.post(
            `/workforce/ecom/inventory/request`,
            approvalPayload
          );

          const successMessage =
            response?.data?.message || "Request has been raised !!";
          this.$toast.success(successMessage);
        } else {
          return;
        }
      } catch (error) {
        console.log(error);
      }
    },

    closeDialog() {
      if (this.minimal) {
        this.closeModal();
        return;
      }
      this.title = "";
      this.desc = "";
      this.assignedTo = null;
      this.isApprovalRequired = "No";
      this.$refs.form.resetValidation();
      if (this.canSetFrequency && this.taskConfig?.recurAllow) {
        this.$refs.dayFrequencyComponent.reinitialise();
      }
      this.closeModal();
    },
    async fetchTaskStatuses() {
      try {
        const { data } = await this.$axios.get(`/workforce/task/steps`);

        const statuses = Object.keys(data.taskSteps).map((key) => ({
          label: convertToTitleCase(key),
          value: data.taskSteps[key],
        }));

        this.statuses = statuses;
      } catch (e) {
        console.error(e);
      }
    },
    async fetchLeads(text) {
      if (!text && !this.selectedLead) return;

      try {
        const currentUsecase = this.$route.params.usecase.toLowerCase();
        const { data } = await this.$axios.get(`/workforce/lead/org`, {
          params: {
            search: this.localSelectedLead.leadTitle || text,
            useCase: currentUsecase,
          },
        });

        const { leads } = data;
        this.leads = leads;
        this.initializeFormFields();
      } catch (error) {
        console.error("Error fetching leads:", error);
      }
    },

    fetchUsers() {
      this.$axios
        .get(`/workforce/users`, {
          params: {
            limit: 200,
          },
        })
        .then((res) => {
          this.users = res.data.users.map((user) => ({
            ...user,
            id: user._id,
            full_name:
              `${user.firstName || ""} ${user.lastName || ""}` +
              (user.wfmRole ? ` (${user.wfmRole.role})` : ""),
          }));
        })
        .catch((error) => {
          console.log(error.message);
        });
    },

    initializeFormFields() {
      if (this.editedTask) {
        this.localSelectedLead = this.editedTask?.leadDetails;
        this.title = this.editedTask?.taskTitle || "";
        this.desc = this.editedTask?.taskDescription || "";
        this.assignedTo = this.editedTask?.assignedTo?._id || null;
        this.companyBranch = this.editedTask?.selectedBranch || null;
        this.startTime =
          this.$dayjs(this.editedTask?.taskStartDateTime).format(
            "YYYY-MM-DDTHH:mm"
          ) || "";
        this.endTime =
          this.$dayjs(this.editedTask?.taskEndDateTime).format(
            "YYYY-MM-DDTHH:mm"
          ) || "";
        this.status = convertToTitleCase(this.editedTask?.taskStatus);
        this.contactTo = this.editedTask?.meetingDetails?.meetingWith || "";
        this.managerRemarks =
          this.editedTask?.meetingDetails?.managerRemarks || "";
        this.isReplacement = this.editedTask?.complaintDetails
          ?.requirePartsReplacement
          ? "Yes"
          : "No";
        this.isChargeRequired = this.editedTask?.complaintDetails
          ?.needToChargeCustomer
          ? "Yes"
          : "No";
        this.complaintNature =
          this.editedTask?.complaintDetails?.natureOfComplaint || "";
        this.transactionNumber =
          this.editedTask?.complaintDetails?.paymentDetails
            ?.transactionNumber || "";
        this.paymentStatus = this.editedTask?.complaintDetails?.paymentStatus;
        this.modeOfPayment =
          this.editedTask?.complaintDetails?.paymentDetails?.modeOfPayment ||
          "";
        this.paymentDate =
          this.$dayjs(
            this.editedTask?.complaintDetails?.paymentDetails?.paymentMadeOn
          ).format("YYYY-MM-DDTHH:mm") || "";
        this.challanNumber =
          this.editedTask?.complaintDetails?.paymentDetails?.challanNumber ||
          "";
        this.paymentReceived =
          this.editedTask?.complaintDetails?.paymentDetails?.paymentReceived ||
          "";
        if (
          this.editedTask?.linkedRequestId?.requestType ===
          "COMPLAINT_APPROVAL_REQUEST"
        ) {
          this.isApprovalRequired = this.editedTask?.linkedRequestId
            ?.requestApprovalsfrom?.[0]?.approvalFrom._id
            ? "Yes"
            : "No";
          this.approvalFrom =
            this.editedTask?.linkedRequestId?.requestApprovalsfrom?.[0]?.approvalFrom._id;
        } else {
          this.isApprovalRequired = "No";
        }
        this.bdNumber = this.editedTask?.complaintDetails?.bdNo;
        this.CpNumber = this.editedTask?.complaintDetails?.complainSrNo;
        this.isDirectCompletionAllowed =
          this.editedTask?.isDirectCompletionAllowed;
        this.serviceReportNumber =
          this.editedTask?.complaintDetails?.serviceReportNumber;
        this.msrNumber = this.editedTask?.complaintDetails?.msrNumber;
        this.poNumber = this.editedTask?.complaintDetails?.poNumber;
        this.tad = this.editedTask?.tda;
        this.currentBalance = this.editedTask?.currentBalance;
      } else {
        this.bdNumber = this.localSelectedLead?.complaintDetails?.bdNo;
        this.CpNumber = this.localSelectedLead?.complaintDetails?.complainSrNo;

        if (this.company.branches.length > 0) {
          this.companyBranch = this.localSelectedLead?.selectedBranch;
        }

        if (this.contacts.length > 0) {
          this.contactTo = this.contacts[0].value;
        }
      }
    },
    async handleButtonClick() {
      if (this.editedTask) {
        await this.handleUpdate();
      } else {
        await this.handleSubmit();
      }
    },
    async handleUpdate() {
      try {
        const approvalTaskId = this.editedTask?._id;
        const currentUsecase = this.$route.params.usecase.toLowerCase();

        const data = {
          taskTitle: this.title,
          taskDescription: this.desc,
          assignedTo: this.assignedTo,
          taskStartDateTime: this.startTime,
          taskEndDateTime: this.endTime,
          taskStatus: this.status.toUpperCase().replace(/[\s-]/g, "_"),
          isRepeated: this.taskFrequency.isRepeat,
          selectedWeekDays: this.taskFrequency.selectedDays,
          meetingDetails: {},
          useCase: currentUsecase,
        };
        if (this.companyBranch) {
          data.selectedBranch = this.companyBranch;
        }
        if (this.isDirectCompletionAllowed) {
          data.isDirectCompletionAllowed = this.isDirectCompletionAllowed;
        }

        data.meetingDetails = {
          _id: this.editedTask?.meetingDetails._id,
          managerRemarks: this.managerRemarks,
        };
        if (this.contactTo) {
          data.meetingDetails.meetingWith = this.contactTo;
        }

        if (this.isComplaintsEnabled) {
          this.editedTask._id += "?action=complaint";
          data.complaintDetails = {
            _id: this.editedTask?.complaintDetails._id,
            natureOfComplaint: this.complaintNature,
            requirePartsReplacement: this.requirePartsReplacement,
            needToChargeCustomer: this.needToChargeCustomer,
            serviceReportNumber: this.serviceReportNumber,
          };
          if (this.needToChargeCustomer) {
            data.complaintDetails.paymentStatus = this.paymentStatus;
          }
        }
        if (this.transactionNumber) {
          data.complaintDetails.paymentDetails = {
            transactionNumber: this.transactionNumber,
            modeOfPayment: this.modeOfPayment,
            paymentMadeOn: this.paymentDate,
            challanNumber: this.challanNumber,
            paymentReceived: this.paymentReceived,
          };
        }
        if (this.poNumber) {
          data.complaintDetails.poNumber = this.poNumber;
          data.complaintDetails.msrNumber = this.msrNumber;
        }

        const response = await this.$axios.put(
          `/workforce/task/${this.editedTask?._id}`,
          data
        );
        if (this.approvalFrom) {
          this.approvalSubmit(approvalTaskId);
        }

        if (this.minimal && this.onUpdate)
          this.onUpdate(response.data.task, this.requirePartsReplacement);
        this.onCreate();
        const successMessage =
          response?.data?.message || "Task Edited Successfully !!";
        this.$toast.success(successMessage);
      } catch (error) {
        console.error(error);
        const errorMessage = error?.response?.data?.message || "Order failed.";
        this.$toast.error(errorMessage);
      }
    },
    closeRequestDialog() {
      this.requestDialog = false;
    },
    requestInventory(item) {
      this.selectedComplaintId = item.complaintDetails._id;
      this.requestDialog = true;
    },
    approvalRequired() {
      if (this.localSelectedLead?.productWarrantyStatus) {
        const warrantyStatus = this.localSelectedLead?.productWarrantyStatus;
        return warrantyStatus !== "IN_WARRANTY" ? "Yes" : "No";
      }
      return "No";
    },
    validateRemarks() {
      const characterCount = this.managerRemarks.trim().length;
      if (characterCount > 1000) {
        this.$toast.error("Character Limit exceeded");
        this.managerRemarks = this.managerRemarks.substring(0, 1000);
      }
    },
    handleAddedPart(item) {
      this.addedProducts = item;
    },
  },
};
</script>

<style scoped>
.crossBtn {
  padding-right: 1.5rem;
  font-size: 1.5rem;
}
</style>
