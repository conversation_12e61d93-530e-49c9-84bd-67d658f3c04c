<template>
  <div
    class="flex flex-wrap justify-between bg-gray-100 dark-bg-custom p-8"
    id="print-content"
    v-if="task"
  >
    <div class="w-full md:w-2/3 p-6 bg-white dark-bg-custom rounded-md">
      <div class="flex justify-between">
        <p class="text-[#54A7E2] text-sm font-semibold">
          Appointment -
          {{ formatDateTime(task?.taskStartDateTime, task?.taskEndDateTime) }}
        </p>
        <p class="text-[#54A7E2] text-sm font-semibold">
          {{ taskLable.task }} Step - {{ convertCapitalize(task.status) }}
        </p>
      </div>
      <div>
        <p class="text-md font-semibold my-0 text-green-500">
          {{ task?.isOtpVerified ? "Otp Successfully verified! ✅" : "" }}
        </p>
      </div>
      <v-expansion-panels v-model="panel" flat multiple>
        <v-expansion-panel>
          <v-expansion-panel-header class="table_header px-4">
            <h3 class="text-lg font-semibold">
              {{ leadLable.leadFormObj.heading }}
            </h3>
          </v-expansion-panel-header>

          <v-expansion-panel-content class="text-[#464A53] dark-text-color">
            <ul class="ulLine">
              <li>
                <strong>{{ leadLable.leadFormObj.lead }} Title:</strong>
                {{ task.leadDetails.leadTitle }}
              </li>
              <li v-if="task.leadDetails.leadDescription">
                <strong>{{ leadLable.leadFormObj.description }}:</strong>
                {{ task.leadDetails.leadDescription.split(",")[0] }}
              </li>
              <li v-if="task.leadDetails.leadDescription">
                <strong>EMI AMT:</strong>
                {{ task.leadDetails.leadDescription.split(",")[1] }}
              </li>
              <li v-if="task.leadDetails.leadDescription">
                <strong>Bucket:</strong>
                {{ task.leadDetails.leadDescription.split(",")[2] }}
              </li>
              <li v-if="isCollectionsEnabled">
                <strong>{{ leadLable.leadFormObj.companyName }}:</strong>
                {{ task.leadDetails?.company?.companyBranches[0]?.alias }}
                ({{ task.leadDetails?.company?.name }})
              </li>
              <li v-else>
                <strong>{{ leadLable.leadFormObj.companyName }}:</strong>
                {{ task.leadDetails?.company?.name || "N/A" }}
              </li>
              <li>
                <strong>{{ leadLable.leadFormObj.companyBranch }}:</strong>
                {{
                  getCompanyBranch(
                    task.leadDetails?.selectedBranch,
                    task.leadDetails
                  )
                }}
              </li>
              <li>
                <strong>{{ leadLable.leadStatus }}:</strong>
                {{ convertCapitalize(this.task.leadDetails.leadStatus) }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Product Manufacturer:</strong>
                {{
                  this.task.leadDetails.productManufacturer?.partnerName ||
                  "N/A"
                }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Product Model Number:</strong>
                {{ this.task.leadDetails.productModelNumber || "N/A" }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Product Bill Number:</strong>
                {{ this.task.leadDetails.productBillNumber || "N/A" }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Product Warranty Status:</strong>
                {{
                  convertCapitalize(
                    this.task.leadDetails.productWarrantyStatus
                  ) || "—"
                }}
              </li>
              <li
                v-show="task.complaintDetails && task.leadDetails.productSoldOn"
              >
                <strong>Product Sell Date:</strong>
                {{
                  $dayjs(task.leadDetails.productSoldOn).format("DD/MM/YYYY")
                }}
                /
                {{ $dayjs(task.leadDetails.productSoldOn).format("hh:mm A") }}
              </li>
            </ul>
          </v-expansion-panel-content>
        </v-expansion-panel>

        <v-expansion-panel>
          <v-expansion-panel-header class="table_header px-4">
            <h3 class="text-lg font-semibold">{{ taskLable.task }} Details</h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="text-[#464A53] dark-text-color">
            <ul class="ulLine">
              <li>
                <strong>{{ taskLable.taskFormObj.taskTitle }}:</strong>
                {{ task.taskTitle }}
              </li>
              <li v-if="task.taskDescription">
                <strong>{{ taskLable.taskFormObj.description }}:</strong>
                <br />
                {{ task.taskDescription }}
              </li>
              <li>
                <strong>{{ taskLable.taskFormObj.assignedTo }}:</strong>
                {{ task.assignedTo?.firstName }}
                {{ task.assignedTo?.lastName }}
              </li>
              <li>
                <strong>Created By:</strong>
                {{ task.createdBy?.firstName }}
                {{ task.createdBy?.lastName }}
              </li>
              <li v-if="isCollectionsEnabled">
                <strong>{{ taskLable.taskFormObj.companyName }}:</strong>
                {{ task.leadDetails?.company?.companyBranches[0]?.alias }}
                ({{ task.leadDetails?.company?.name }})
              </li>
              <li v-else>
                <strong>{{ taskLable.taskFormObj.companyName }}:</strong>
                {{ task.leadDetails?.company?.name || "N/A" }}
              </li>
              <li>
                <strong>{{ taskLable.taskFormObj.companyBranch }}:</strong>
                {{ getCompanyBranch(task.selectedBranch, task.leadDetails) }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Complaint No:</strong>
                {{ task.complaintDetails?.complainSrNo }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>BD No:</strong>
                {{ task.complaintDetails?.bdNo }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Nature of complaint:</strong>
                {{ task.complaintDetails?.natureOfComplaint }}
              </li>
              <li v-show="task.supportingStaff?.length">
                <strong>Supporting Staff:</strong>
                {{ supportingStaffs(task?.supportingStaff) }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Complaint Date:</strong>
                {{
                  $dayjs(task.complaintDetails?.createdAt).format("DD/MM/YYYY")
                }}
                /
                {{ $dayjs(task.complaintDetails?.createdAt).format("hh:mm A") }}
              </li>
              <li v-show="task.complaintDetails">
                <strong>Complaint Status:</strong>
                {{ task.complaintDetails?.status }}
              </li>
              <li v-show="task.complaintDetails" v-if="this.getRequestId(task)">
                <strong>Request Id:</strong>
                {{ getRequestId(task) }}
              </li>
              <li
                v-show="task.complaintDetails"
                v-if="this.getRequestStatus(task)"
              >
                <strong>Request Status:</strong>
                {{ getRequestStatus(task) }}
              </li>
              <li
                v-show="task.complaintDetails"
                v-if="this.getRequestComment(task)"
              >
                <strong>Recent Comment:</strong>
                {{ getRequestComment(task) }}
              </li>
              <li v-show="task.complaintDetails?.linkedRequestId">
                <strong>Request Raised Date:</strong>
                {{
                  $dayjs(
                    task.complaintDetails?.linkedRequestId?.raisedOn
                  ).format("DD/MM/YYYY")
                }}
                /
                {{
                  $dayjs(
                    task.complaintDetails?.linkedRequestId?.raisedOn
                  ).format("hh:mm A")
                }}
              </li>
              <li
                v-show="task.complaintDetails"
                v-if="task?.complaintDetails?.paymentStatus"
              >
                <strong>Payment Status:</strong>
                {{ task?.complaintDetails?.paymentStatus }}
              </li>
              <li
                v-show="task.complaintDetails"
                v-if="task?.complaintDetails?.paymentDetails?.paymentReceived"
              >
                <strong>Payment Amount:</strong>
                {{ task?.complaintDetails?.paymentDetails?.paymentReceived }}
              </li>
              <li
                v-show="task.complaintDetails"
                v-if="task?.complaintDetails?.poNumber"
              >
                <strong>PO No. :</strong>
                {{ task?.complaintDetails?.poNumber }}
              </li>
              <li
                v-show="task.complaintDetails"
                v-if="task?.complaintDetails?.msrNumber"
              >
                <strong>MSR No. :</strong>
                {{ task?.complaintDetails?.msrNumber }}
              </li>
            </ul>
          </v-expansion-panel-content>
        </v-expansion-panel>

        <v-expansion-panel v-if="task?.complaintDetails?.linkedRequestId">
          <v-expansion-panel-header class="table_header px-4">
            <h3 class="text-lg font-semibold">
              Part Held By Employee/Engineer
            </h3>
          </v-expansion-panel-header>

          <v-expansion-panel-content class="text-[#464A53] dark-text-color">
            <div
              v-for="(el, elIndex) in task?.complaintDetails?.linkedRequestId
                ?.linkedUserInventory?.products"
              :key="elIndex"
            >
              <div class="ulLine">
                <p>
                  <strong>Employee Name:</strong>
                  {{ el?.collectedBy?.firstName }}
                  {{ el?.collectedBy?.lastName }}
                </p>
                <div>
                  <table class="w-4/5 border border-collapse mb-4">
                    <thead>
                      <tr>
                        <th class="border p-2">Part Name</th>
                        <th class="border p-2">Quantity Took</th>
                        <th class="border p-2">Quantity Used</th>
                        <th class="border p-2">Quantity Returned</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="border pl-4">
                          {{ el?.productId?.productName }}
                        </td>
                        <td class="border pl-4">
                          {{ el?.quantityTook }}
                        </td>
                        <td class="border pl-4">
                          {{ el?.quantityUsed }}
                        </td>
                        <td class="border pl-4">
                          {{ el?.quantityReturned }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <v-divider></v-divider>
            </div>
          </v-expansion-panel-content>
        </v-expansion-panel>

        <v-expansion-panel v-if="task?.complaintDetails?.orderDetails.length">
          <v-expansion-panel-header class="table_header px-4">
            <h3 class="text-lg font-semibold">Order Details</h3>
          </v-expansion-panel-header>

          <v-expansion-panel-content class="text-[#464A53] dark-text-color">
            <div
              v-for="(order, orderIndex) in task?.complaintDetails
                ?.orderDetails"
              :key="orderIndex"
            >
              <ul class="ulLine">
                <li><strong>Order Number:</strong> {{ order.orderSrNo }}</li>
                <li>
                  <strong>Order Amount:</strong> {{ order.orderAmount }} Rs
                </li>
                <li><strong>Order Status:</strong> {{ order.orderStatus }}</li>
                <li>
                  <table class="w-4/5 border border-collapse mb-4">
                    <thead>
                      <tr>
                        <th class="border p-2">Product Name</th>
                        <th class="border p-2">Quantity</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(product, productIndex) in order.products"
                        :key="productIndex"
                      >
                        <td class="border pl-4">
                          {{ product.product.productName }}
                        </td>
                        <td class="border pl-4">
                          {{ product.orderedQuantity }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </li>
              </ul>
              <v-divider></v-divider>
            </div>
          </v-expansion-panel-content>
        </v-expansion-panel>

        <v-expansion-panel>
          <v-expansion-panel-header class="table_header px-4">
            <h3 class="text-lg font-semibold">Meeting Details</h3>
          </v-expansion-panel-header>

          <v-expansion-panel-content
            class="text-[#464A53] dark-text-color"
            v-if="isMeetingDetailsAvailable"
          >
            <ul class="ulLine">
              <li>
                <strong
                  v-if="
                    this.task.meetingDetails
                      ? getFullName(
                          findMeetingWithContact(this.task.selectedBranch)
                        )
                      : ''
                  "
                  >Contact Person:</strong
                >
                {{
                  this.task.meetingDetails
                    ? getFullName(
                        findMeetingWithContact(this.task.selectedBranch)
                      )
                    : "N/A"
                }}
              </li>
              <li
                v-if="
                  findMeetingWithContact(this.task.selectedBranch)?.phoneNumber
                "
              >
                <strong>Contact Number:</strong>
                {{
                  findMeetingWithContact(this.task.selectedBranch).phoneNumber
                }}
              </li>
              <li
                v-if="findMeetingWithContact(this.task.selectedBranch)?.email"
              >
                <strong>Contact Email:</strong>
                {{ findMeetingWithContact(this.task.selectedBranch).email }}
              </li>
              <li
                v-if="
                  findMeetingWithContact(this.task.selectedBranch)?.designation
                "
              >
                <strong>Contact Designation:</strong>
                {{
                  findMeetingWithContact(this.task.selectedBranch).designation
                }}
              </li>
              <li v-if="task.meetingDetails?.meetingNotes">
                <strong>Meeting Notes:</strong>
                <br />
                {{ task.meetingDetails?.meetingNotes }}
              </li>
              <li v-if="task.meetingDetails?.managerRemarks">
                <strong>Remarks:</strong>
                <br />
                {{ task.meetingDetails?.managerRemarks }}
              </li>
              <li v-if="task.meetingDetails?.meetingOutCome">
                <strong>Meeting Outcome:</strong>
                <br />
                {{ task.meetingDetails?.meetingOutCome }}
              </li>
              <li v-if="task.meetingDetails?.fundCollected">
                <strong>Fund Collected:</strong>
                <br />
                {{ task.meetingDetails?.fundCollected }}
              </li>
              <li v-if="task.meetingDetails?.nextTaskScheduledOn">
                <strong>Next Scheduled Visit:</strong>
                <br />
                {{ task.meetingDetails?.nextTaskScheduledOn }}
              </li>
            </ul>
          </v-expansion-panel-content>
          <v-expansion-panel-content v-else>
            <p
              class="text-center font-normal rounded-lg bg-gray-200 dark-bg-custom text-gray-600 dark-text-color p-1"
            >
              Meeting details not available.
            </p>
          </v-expansion-panel-content>
        </v-expansion-panel>

        <!-- questionnaire -->

        <v-expansion-panel v-if="task?.meetingDetails?.questionnaire">
          <v-expansion-panel-header class="table_header px-4">
            <h3 class="text-lg font-semibold">Customer Survey/Questions</h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="text-[#464A53] dark-text-color">
            <v-container>
              <v-row
                v-for="(question, index) in task?.meetingDetails?.questionnaire"
                :key="index"
              >
                <v-col cols="12" class="question-title">
                  <div>Q{{ index + 1 }}: {{ question.question }}</div>
                </v-col>

                <v-col v-if="question.questionType !== 'CHECKBOX'">
                  <p class="text-sm text-black mb-0 pl-4">
                    {{ question.userResponse }}
                  </p>
                </v-col>

                <v-col v-else>
                  <p
                    class="text-sm text-black mb-0 pl-4"
                    v-for="(option, index) in question.userResponse"
                    :key="index"
                  >
                    {{ option }}
                    <br />
                  </p>
                </v-col>
              </v-row>
            </v-container>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </div>

    <!-- Right Side - Image Section -->
    <div class="w-full md:w-1/3 p-4 bg-gray-100 dark-bg-custom">
      <div class="flex justify-end">
        <v-menu
          v-model="menu"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
        >
          <template v-slot:activator="{ on }">
            <v-text-field
              label="Select field to print"
              outlined
              dense
              class="mr-2"
              readonly
              v-on="on"
              v-model="selectedItemsText"
              hide-details
            ></v-text-field>
          </template>

          <v-card style="max-height: 300px; overflow-y: auto">
            <v-treeview
              :items="treeViewData"
              item-text="name"
              v-model="selection"
              class="text-sm"
              selectable
              return-object
            ></v-treeview>
          </v-card>
        </v-menu>
        <v-btn
          @click="printContent"
          :color="$vuetify.theme.currentTheme.primary"
          class="white--text"
          :disabled="!selection.length"
          >Print</v-btn
        >
      </div>
      <div class="mt-10">
        <div class="mb-5 bg-gray-100 dark-bg-default">
          <h3 class="text-xl flex justify-center bg-gray-100 dark-bg-custom font-semibold">
            Captured Image
          </h3>
        </div>
        <div>
          <img
            :src="getImageUrl"
            alt="Task Image"
            class="w-full h-full object-cover rounded-md print-image"
          />
        </div>
        <div class="mt-10 hide-on-print">
          <h3 class="text-lg mt-5 font-semibold flex justify-center">
            Meeting Recording
          </h3>
          <div v-if="getAudioUrl">
            <audio controls class="mt-5 mx-auto">
              <source :src="getAudioUrl" type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
          <div v-else class="mt-2">
            <p
              class="text-center font-normal rounded-lg bg-gray-200 dark-bg-default text-gray-600 dark-text-color p-1"
            >
              Audio recording not available.
            </p>
          </div>
          <div v-if="imagesData?.length">
            <h3 class="text-lg mt-5 font-semibold flex justify-center">
              Attached Documents
            </h3>
            <div class="mt-4">
              <LightBox :imagesData="imagesData" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LightBox from "@/components/LightBox.vue";
import { fullName } from "@/utils/common";
export default {
  components: {
    LightBox,
  },
  data() {
    return {
      noImage: require("@/assets/img/notfound.jpg"),
      orgLang: this.$store.state.orgLanguage,
      panel: [0, 1, 2, 3],
      selection: [],
      menu: false,
    };
  },
  props: {
    task: Object,
    imagesData: Array,
  },
  methods: {
    printContent() {
      if (!this.selection.length) {
        this.$toast.error("Select the fields that you want to print");
        return;
      }
      const selectedItems = this.selection;
      const task = this.task;

      let tableHtml = "<table>";
      selectedItems.forEach((item) => {
        if (item.value !== undefined) {
          tableHtml += `
                <tr>
                    <td><strong>${item.name}</strong></td>
                    <td>${item.value}</td>
                </tr>
            `;
        }
      });
      tableHtml += "</table>";

      const itemNames = selectedItems.map((item) => item?.name);
      if (
        task?.complaintDetails?.linkedRequestId?.linkedUserInventory
          ?.products &&
        itemNames.includes("Part Held By Employee/Engineer")
      ) {
        tableHtml += `
        <h3>Part Held By Employee/Engineer</h3>
        <table>
          <thead>
            <tr>
              <th>Employee Name</th>
              <th>Part Name</th>
              <th>Quantity Took</th>
              <th>Quantity Used</th>
              <th>Quantity Returned</th>
            </tr>
          </thead>
          <tbody>
            ${task?.complaintDetails?.linkedRequestId?.linkedUserInventory?.products
              .map(
                (el) => `
              <tr>
                <td>${el?.collectedBy?.firstName} ${el?.collectedBy?.lastName}</td>
                <td>${el?.productId?.productName}</td>
                <td>${el?.quantityTook}</td>
                <td>${el?.quantityUsed}</td>
                <td>${el?.quantityReturned}</td>
              </tr>
            `
              )
              .join("")}
          </tbody>
        </table>
      `;
      }

      // Order Details section
      if (
        task?.complaintDetails?.orderDetails &&
        itemNames.includes("Order Details")
      ) {
        tableHtml += `
            <h3>Order Details</h3>
            <table>
              ${task?.complaintDetails?.orderDetails
                .map(
                  (order) => `
                <tr>
                  <td><strong>Order Number</strong></td>
                  <td>${order?.orderSrNo}</td>
                </tr>
                <tr>
                  <td><strong>Order Amount</strong></td>
                  <td>${order?.orderAmount} Rs</td>
                </tr>
                <tr>
                  <td><strong>Order Status</strong></td>
                  <td>${order?.orderStatus}</td>
                </tr>
                <tr>
                  <td colspan="2">
                    <table>
                      <thead>
                        <tr>
                          <th>Product Name</th>
                          <th>Quantity</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${order?.products
                          .map(
                            (product) => `
                          <tr>
                            <td>${product?.product?.productName}</td>
                            <td>${product?.orderedQuantity}</td>
                          </tr>
                        `
                          )
                          .join("")}
                      </tbody>
                    </table>
                  </td>
                </tr>
              `
                )
                .join("")}
            </table>
          `;
      }

      // Meeting Details section
      if (itemNames.includes("Meeting Details")) {
        tableHtml += `
        <h3>Meeting Details</h3>
        <table>
          ${
            this.isMeetingDetailsAvailable
              ? `
            <tr>
              <td><strong>Contact Person</strong></td>
              <td>${
                this.task?.meetingDetails
                  ? this.getFullName(
                      this.findMeetingWithContact(this.task?.selectedBranch)
                    )
                  : "N/A"
              }</td>
            </tr>
            <tr>
              <td>Contact Number</td>
              <td>${
                this.findMeetingWithContact(this.task?.selectedBranch)
                  ?.phoneNumber || "N/A"
              }</td>
            </tr>
            <tr>
              <td>Contact Email</td>
              <td>${
                this.findMeetingWithContact(this.task?.selectedBranch)?.email ||
                "N/A"
              }</td>
            </tr>
            <tr>
              <td>Contact Designation</td>
              <td>${
                this.findMeetingWithContact(this.task?.selectedBranch)
                  ?.designation || "N/A"
              }</td>
            </tr>
            <tr>
              <td>Meeting Notes</td>
              <td>${this.task.meetingDetails?.meetingNotes || "N/A"}</td>
            </tr>
            <tr>
              <td>Remarks</td>
              <td>${this.task.meetingDetails?.managerRemarks || "N/A"}</td>
            </tr>
          `
              : `
            <tr>
              <td colspan="2" class="text-center">Meeting details not available.</td>
            </tr>
          `
          }
        </table>
      `;
      }

      if (
        task?.meetingDetails?.questionnaire &&
        itemNames.includes("Customer Survey/Questions")
      ) {
        tableHtml += `
        <h3>Customer Survey/Questions</h3>
        <table>
          ${task?.meetingDetails?.questionnaire
            ?.map(
              (question, index) => `
            <tr>
              <td><strong>Q${index + 1}</strong>: ${question?.question}</td>
              <td>${
                question?.questionType !== "CHECKBOX"
                  ? question?.userResponse
                  : question?.userResponse.join("<br />")
              }</td>
                </tr>
                `
            )
            .join("")}
                </table>
                `;
      }
      if (itemNames.includes("Meeting Recording")) {
        const audioRecordingHTML = `
        <div class="mt-5">
          <h3 class="text-lg mt-5 font-semibold flex justify-center">
            Meeting Recording
          </h3>
          <div v-if="getAudioUrl">
            <p class="text-center">
              <strong>Audio Recording:</strong> <a :href="getAudioUrl">${
                this.getAudioUrl || "No Link Available"
              }</a>
            </p>
          </div>
        </div>
      `;
        tableHtml += audioRecordingHTML;
      }

      if (itemNames.includes("Selfie/Live Image")) {
        const capturedImageHTML = `
          <div class="mt-5">
            <h3 class="text-lg font-semibold">
              Selfie/Live Image
            </h3>
          </div>
          <div class="flex flex-wrap">
            <img
            src="${this.getImageUrl}"
            alt="Task Image"
            class="print-image"
            />
          </div>
         `;
        tableHtml += capturedImageHTML;
      }

      if (itemNames.includes("Attached Image")) {
        const attchmentHTML = `
          <div class="mt-5">
            <h3 class="text-lg font-semibold">Attached Images</h3>
              <div class="flex flex-wrap">
                ${this.imagesData
                  .map(
                    (image) =>
                      `<img src="${image.src}" alt="Attached Image" class="attached-image" />`
                  )
                  .join("")}
              </div>
          </div>
          `;
        tableHtml += attchmentHTML;
      }

      const printWindow = window.open("", "_blank");
      printWindow.document.open();
      printWindow.document.write(`
        <html>
          <head>
            <title>Print</title>
            <style>
                body { font-family: Arial, sans-serif;font-size: 12px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; table-layout: fixed; }
                table, th, td { border: 1px solid #ddd; padding: 8px;font-size: 12px;  }
                th { background-color: #f2f2f2; }
                strong { font-weight: bold; }
                .print-image {
                  width: 200px;
                  height: auto;
                  margin-top: 10px;
                  border-radius: 5%;
                }
                .attached-image {
                  width: 200px;
                  height: auto;
                  margin-top: 10px;
                  border-radius: 5%;
                }
                .hide-on-print {
                  display: none;
                }
              </style>
          </head>
          <body>
            ${tableHtml}
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.onload = () => {
        printWindow.print();
      };
    },
    getCompanyBranch(branchId, details) {
      const selectedBranch = details.company?.companyBranches.find(
        (branch) => branch._id === branchId
      );
      if (selectedBranch) {
        const { addressLine1, addressLine2, city, state, country, pinCode } =
          selectedBranch.address;
        const addressDetails = [
          addressLine1,
          addressLine2,
          city,
          state,
          country,
          pinCode,
        ].filter(Boolean);
        return addressDetails.join(", ");
      }
      return "N/A";
    },
    convertCapitalize(string) {
      return string
        ?.toLowerCase()
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    },
    formatDateTime(startDateTime, endDateTime) {
      const startDay = this.$dayjs(startDateTime);
      const endDay = this.$dayjs(endDateTime);

      if (startDay.isSame(endDay, "day")) {
        const formattedDate = startDay.format("DD-MM-YYYY");
        const formattedStartTime = startDay.format("hh:mm A");
        const formattedEndTime = endDay.format("hh:mm A");
        return `${formattedStartTime} to ${formattedEndTime} / ${formattedDate}`;
      } else {
        const formattedStartDate = startDay.format("DD-MM-YYYY");
        const formattedStartTime = startDay.format("hh:mm A");

        const formattedEndDate = endDay.format("DD-MM-YYYY");
        const formattedEndTime = endDay.format("hh:mm A");
        return `${formattedStartDate} / ${formattedStartTime} to ${formattedEndDate} / ${formattedEndTime}`;
      }
    },
    findMeetingWithContact(selectedBranchId) {
      const selectedBranch =
        this.task.leadDetails?.company?.companyBranches.find(
          (branch) => branch._id === selectedBranchId
        );
      if (selectedBranch) {
        for (const contact of selectedBranch.contacts) {
          if (contact._id === this.task.meetingDetails?.meetingWith) {
            return contact;
          }
        }
      }

      return "N/A";
    },

    getFullName(contact) {
      return fullName(contact);
    },
    getRequestStatus(item) {
      return (
        item.complaintDetails?.linkedRequestId?.requestStatus ||
        item.linkedRequestId?.requestStatus ||
        ""
      );
    },
    getRequestId(item) {
      return (
        item.complaintDetails?.linkedRequestId?.requestId ||
        item.linkedRequestId?.requestId ||
        ""
      );
    },

    getRequestComment(item) {
      return (
        item.complaintDetails?.linkedRequestId?.comments?.slice(-1)?.[0]
          ?.comment ||
        item.linkedRequestId?.comments?.slice(-1)?.[0]?.comment ||
        ""
      );
    },
    userFullName(item) {
      return fullName(item);
    },
    supportingStaffs(item) {
      const users = item?.map((el) => {
        return this.userFullName(el);
      });
      if (users?.length) {
        return users.join(", ");
      }
      return;
    },
  },
  computed: {
    getImageUrl() {
      const baseUrl = process.env.VUE_APP_S3_BASE_URL;
      const selfie = this.task.additionalInfo?.selfie;

      if (selfie && selfie.doc_physical_path) {
        const docPhysicalPath = selfie.doc_physical_path;
        return baseUrl + docPhysicalPath;
      } else {
        return this.noImage;
      }
    },
    getAudioUrl() {
      const baseUrl = process.env.VUE_APP_S3_BASE_URL;
      const audio = this.task.additionalInfo?.audio;

      if (audio && audio.doc_physical_path) {
        const docPhysicalPath = audio.doc_physical_path;
        return baseUrl + docPhysicalPath;
      } else {
        return "";
      }
    },
    isCollectionsEnabled() {
      return this.$route.params.usecase === "collection";
    },
    useCaseTaskConfig() {
      const currentUsecase = this.$route.params.usecase;
      const orgConfig = this.orgLang.data || {};
      const taskModules = orgConfig.taskModules || [];
      const currentConfig = taskModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      return currentConfig;
    },
    useCaseLeadConfig() {
      const currentUsecase = this.$route.params.usecase;
      const orgConfig = this.orgLang.data || {};
      const leadModules = orgConfig.leadModules || [];
      const currentConfig = leadModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      return currentConfig;
    },
    taskLable() {
      return {
        taskFormObj: this.useCaseTaskConfig.addTaskForm,
        task: this.useCaseTaskConfig.taskTable.columns?.taskTitle,
      };
    },
    leadLable() {
      return {
        leadTitle: this.useCaseLeadConfig.addLeadForm.lead,
        leadFormObj: this.useCaseLeadConfig.addLeadForm,
        description: this.useCaseLeadConfig.addLeadForm.description,
        leadStatus: this.useCaseLeadConfig.leadTable.columns?.status,
      };
    },
    isMeetingDetailsAvailable() {
      if (
        this.task.meetingDetails?.meetingWith ||
        this.task.meetingDetails?.meetingNotes ||
        this.task.meetingDetails?.managerRemarks
      ) {
        return true;
      } else {
        return false;
      }
    },
    selectedItemsText() {
      return this.selection.map((item) => item.name).join(", ");
    },
    isComplaintsEnabled() {
      return this.$route.params.usecase === "inventory";
    },
    treeViewData() {
      const task = this.task;
      let data = [
        {
          id: 1,
          name: this.leadLable.leadFormObj.heading,
          children: [
            {
              id: 2,
              name: this.leadLable.leadFormObj.lead,
              value: task.leadDetails.leadTitle,
            },
            {
              id: 3,
              name: this.leadLable.leadFormObj.description,
              value: task.leadDetails.leadDescription,
            },
            {
              id: 4,
              name: this.leadLable.leadFormObj.companyName,
              value: task.leadDetails?.company?.name,
            },
            {
              id: 5,
              name: this.leadLable.leadFormObj.companyBranch,
              value: this.getCompanyBranch(
                task.leadDetails?.selectedBranch,
                task.leadDetails
              ),
            },
            {
              id: 6,
              name: this.leadLable.leadStatus,
              value: this.convertCapitalize(task.leadDetails.leadStatus),
            },
            {
              id: 7,
              name: "Product Manufacturer",
              value: task.leadDetails.productManufacturer?.partnerName,
            },
            {
              id: 8,
              name: "Product Model Number",
              value: task.leadDetails.productModelNumber,
            },
            {
              id: 9,
              name: "Product Bill Number",
              value: task.leadDetails.productBillNumber,
            },
            {
              id: 10,
              name: "Product Warranty Status",
              value: this.convertCapitalize(
                task.leadDetails?.productWarrantyStatus
              ),
            },
            {
              id: 11,
              name: "Product Sell Date",
              value: this.$dayjs(task.leadDetails?.productSoldOn).isValid()
                ? this.$dayjs(task.leadDetails?.productSoldOn).format("DD/MM/YYYY hh:mm A")
                : ''
            },
          ],
        },
        {
          id: 12,
          name: `${this.taskLable.task} Details`,
          children: [
            {
              id: 13,
              name: this.taskLable.taskFormObj?.taskTitle,
              value: task?.taskTitle,
            },
            {
              id: 14,
              name: this.taskLable.taskFormObj?.description,
              value: task?.taskDescription,
            },
            {
              id: 15,
              name: this.taskLable.taskFormObj?.assignedTo,
              value:
                task.assignedTo?.firstName + " " + task.assignedTo?.lastName,
            },
            {
              id: 16,
              name: "Created By",
              value: task.createdBy?.firstName + " " + task.createdBy?.lastName,
            },
            {
              id: 17,
              name: this.taskLable.taskFormObj?.companyName,
              value: task.leadDetails?.company?.name,
            },
            {
              id: 18,
              name: this.taskLable.taskFormObj?.companyBranch,
              value: this.getCompanyBranch(
                task.selectedBranch,
                task.leadDetails
              ),
            },
            {
              id: 19,
              name: "Complaint No",
              value: task.complaintDetails?.complainSrNo,
            },
            {
              id: 20,
              name: "BD No",
              value: task.complaintDetails?.bdNo,
            },
            {
              id: 21,
              name: "Nature of complaint",
              value: task.complaintDetails?.natureOfComplaint,
            },
            {
              id: 22,
              name: "Complaint Date",
              value: this.$dayjs(task.complaintDetails?.createdAt).format(
                "DD/MM/YYYY hh:mm A"
              ),
            },
            {
              id: 23,
              name: "Complaint Status",
              value: task.complaintDetails?.status,
            },
            {
              id: 24,
              name: "Request Id",
              value: this.getRequestId(task),
            },
            {
              id: 25,
              name: "Request Status",
              value: this.getRequestStatus(task),
            },
            {
              id: 26,
              name: "Recent Comment",
              value: this.getRequestComment(task),
            },
            {
              id: 27,
              name: "Request Raised Date",
              value: this.$dayjs(
                task.complaintDetails?.linkedRequestId?.raisedOn
              ).format("DD/MM/YYYY"),
            },
            {
              id: 28,
              name: "Payment Status",
              value: task?.complaintDetails?.paymentStatus,
            },
            {
              id: 29,
              name: "Payment Amount",
              value: task?.complaintDetails?.paymentDetails?.paymentReceived,
            },
            {
              id: 30,
              name: "PO No.",
              value: task?.complaintDetails?.poNumber,
            },
            {
              id: 31,
              name: "MSR No.",
              value: task?.complaintDetails?.msrNumber,
            },
            {
              id: 34,
              name: "Supporting Staffs",
              value: this.supportingStaffs(task?.supportingStaff),
            },
          ],
        },

        {
          id: 35,
          name: `Meeting Details`,
        },
        {
          id: 36,
          name: `Customer Survey/Questions`,
        },
        {
          id: 37,
          name: `Meeting Recording`,
        },
        {
          id: 38,
          name: `Selfie/Live Image`,
        },
        {
          id: 39,
          name: `Attached Image`,
        },
      ];
      if (this.isComplaintsEnabled) {
        const complaintData = [
          {
            id: 32,
            name: `Part Held By Employee/Engineer`,
          },
          {
            id: 33,
            name: `Order Details`,
          },
        ];
        data = data.concat(complaintData);
      }
      return data;
    },
  },
};
</script>

<style scoped>
.ulLine {
  padding-left: 0px;
  line-height: 30px;
}

.table_header {
  padding-left: 0px;
}

.question-title {
  font-weight: bold;
  padding-bottom: 0px;
  padding-left: 0px;
}
.chip-width {
  width: 80px;
  height: 30px;
  overflow: hidden;
}
.chip-text {
  display: inline-block;
  max-width: 100%;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
