<template>
  <div>
    <v-menu offset-y>
      <template v-slot:activator="{ on }">
        <v-btn icon v-on="on" fab small v-show="canAdd">
          <v-icon>mdi-dots-vertical</v-icon>
        </v-btn>
      </template>
      <v-list>
        <v-list-item @click="openUploadDialog">
          <v-list-item-content>
            <v-list-item-title
              >Create Bulk Product(s)
              <v-icon right>mdi-upload</v-icon></v-list-item-title
            >
          </v-list-item-content>
        </v-list-item>
        <v-list-item @click="downloadFile">
          <v-list-item-content>
            <v-list-item-title
              >Download Sample File
              <v-icon right>mdi-download</v-icon>
            </v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-menu>
    <!-- Upload Dialog -->
    <v-dialog v-model="uploadDialog" max-width="600" persistent>
      <v-card>
        <v-card-title>Upload File</v-card-title>
        <v-card-text>
          <v-file-input
            label="Choose a file"
            v-model="selectedFile"
            multiple
          ></v-file-input>
          <v-progress-linear
            v-if="uploadProgress !== null"
            :value="uploadProgress"
            absolute
            bottom
          ></v-progress-linear>
          <div v-if="uploadStatus">
            <p>Success : {{ created }}</p>
            <div class="flex align-center justify-between">
              <p>Failed : {{ failed }}</p>
              <v-btn small v-if="failed" @click="downloadFile">
                Download Failed Status<v-icon color="red">mdi-download</v-icon>
              </v-btn>
            </div>
          </div>
          <v-alert v-if="uploadStatus" :type="uploadStatusType" class="mt-4">{{
            uploadStatus
          }}</v-alert>
        </v-card-text>
        <v-card-actions class="flex flex-1 items-center w-full mb-4">
          <v-btn
            outlined
            @click="closeUploadDialog"
            class="w-[50%]"
            >Close</v-btn
          >
          <v-btn
            @click="uploadFile"
            :loading="uploading"
            :disabled="uploading"
            class="white--text w-[50%]"
            :color="$vuetify.theme.currentTheme.primary"
            >Upload</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { hasPermission } from "@/utils/common";
import permissionData from "@/utils/permissions";

export default {
  props: {
    uploadUrl: {
      type: String,
      required: true,
    },
    downloadData: {
      type: Array,
      required: true,
    },
    fileName: {
      type: String,
      default: "example",
    },
    useCase: {
      type: Object,
    },
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      uploadDialog: false,
      selectedFile: [],
      uploadProgress: null,
      uploading: false,
      created: "",
      failed: "",
      invalidCases: [],
      invalidLeads: [],
      uploadStatus: null,
    };
  },
  computed: {
    uploadStatusType() {
      return this.uploadStatus === "File uploaded successfully"
        ? "success"
        : "error";
    },
    canAdd() {
      return hasPermission(this.userData, permissionData.leadWrite);
    },
  },
  methods: {
    openUploadDialog() {
      this.uploadDialog = true;
      this.selectedFile = [];
      this.uploadProgress = null;
      this.uploadStatus = null;
    },

    closeUploadDialog() {
      this.uploadDialog = false;
      this.created = "";
      this.failed = "";
    },
    uploadFile() {
      if (!this.selectedFile) {
        return;
      }
      const formData = new FormData();
      
      this.selectedFile.forEach((file) => {
        formData.append("attachments", file);
      });

      formData.append("useCase", this.useCase.useCaseType);
      const config = {
        onUploadProgress: (progressEvent) => {
          this.uploadProgress = Math.round(
            (progressEvent.loaded / progressEvent.total) * 100
          );
        },
      };
      this.uploading = true;
      this.$axios
        .post(this.uploadUrl, formData, config)
        .then((response) => {
          this.created =
            response.data?.data?.productCreatedSuccessfully ||
            response.data?.data?.leadCreated;
          this.failed =
            response.data?.data.productCreationFailed ||
            response.data?.data?.leadFailed;
          this.invalidCases = response.data?.data.invalidCases;
          this.invalidLeads = response.data?.data?.invalidLeads;
          if (this.created) {
            this.uploadStatus = "File uploaded successfully";
            this.$emit("updateUploadData");
          } else {
            this.uploadStatus = "File uploading failed";
          }
        })
        .catch((error) => {
          this.uploadStatus = "Error uploading file";
          console.error("Error uploading file:", error);
        })
        .finally(() => {
          this.uploading = false;
        });
    },

    downloadFile() {
      const XLSX = require("xlsx");
      let restructureInvalid;
      if (this.invalidCases?.length) {
        restructureInvalid = this.invalidCases?.map((el) => {
          return {
            ...el.entry,
            Error: el.error,
          };
        });
      } else {
        restructureInvalid = this.invalidLeads;
      }
      const data = restructureInvalid.length
        ? restructureInvalid
        : this.downloadData;
      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Data");
      const filename = `${this.fileName}.xlsx`;
      XLSX.writeFile(wb, filename);
    },
  },
};
</script>
