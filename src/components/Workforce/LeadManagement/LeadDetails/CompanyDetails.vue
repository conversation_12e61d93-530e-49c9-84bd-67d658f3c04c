<template>
  <div class="">
    <div class="bg-gray-100 dark-bg-custom px-6 py-8">
      <div class="bg-white dark-bg-custom p-4 rounded-lg">
        <div
          v-for="field in editingFields"
          :key="field.id"
          class="flex mb-8 items-center"
        >
          <p class="mr-15 mb-0">{{ field.title }}</p>
          <p
            v-if="!field.editing"
            class="text-gray-500 dark-text-color font-roboto mb-0 text-14 font-normal leading-18 tracking-px-05"
          >
            {{ company[field.value] }}
          </p>
          <div v-if="field.editing" class="flex items-center">
            <v-text-field
              class="mr-3"
              :placeholder="field.placeholder"
              hide-details="auto"
              v-model="editValues[field.value]"
            ></v-text-field>
            <v-btn
              class="mr-2 white--text"
              :color="$vuetify.theme.currentTheme.primary"
              @click="updateCompanyDetails(field)"
            >
              Save
            </v-btn>
            <v-btn outlined @click="cancelEdit(field)"> Cancel </v-btn>
          </div>
          <!-- <v-icon
            v-else-if="field.value === 'name' && canEdit "
            style="color: #4fa6ff"
            class="ml-4"
            @click="field.editing = true"
          >
            mdi-pencil-outline
          </v-icon> -->
        </div>

        <!-- <iframe
          width="650"
          height="250"
          loading="lazy"
          :src="
            'https://maps.google.com/maps?q=' +
            encodeURIComponent(companyDataDisplay?.location) +
            '&t=m&z=10&output=embed&iwloc=near'
          "
          :title="companyDataDisplay?.location"
          :aria-label="companyDataDisplay?.location"
        ></iframe> -->
        <div id="google-map" style="width: 100%; height: 400px"></div>

        <div class="flex mb-5 mt-7">
          <h1 class="text-xl font-bold">Contact details</h1>
          <!-- <v-icon style="color: #4fa6ff" class="ml-4" v-if="canEdit">
            mdi-pencil-outline
          </v-icon> -->
        </div>
        <div>
          <div
            v-for="(contact, index) in companyDataDisplay?.contacts"
            :key="index"
          >
            <template v-if="companyDataDisplay?.primaryContact">
              <div class="flex">
                <div class="mr-16">
                  <p class="mb-8">Contact Person</p>
                  <p class="mb-8">Mobile Number</p>
                  <p class="mb-8">Designation</p>
                  <p class="mb-8">Email</p>
                  <p class="mb-8">Decision Maker</p>
                </div>

                <div>
                  <p class="mb-8 text-gray-500">
                    {{ contact.firstName }} {{ contact.lastName }}
                  </p>
                  <p class="mb-8 text-gray-500">
                    {{ contact.phoneNumber }}
                  </p>
                  <p class="mb-8 text-gray-500">
                    {{ contact.designation }}
                  </p>
                  <p class="mb-8 text-gray-500">
                    {{ contact.email }}
                  </p>
                  <p class="mb-8 text-gray-500">
                    {{ contact.isDecisionMaker ? "Yes" : "No" }}
                  </p>
                </div>
              </div>
              <hr class="mb-4 border-gray-300 w-3/5" />
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { hasPermission } from "@/utils/common";
import permissionData from "@/utils/permissions";
import { companyUpdateSuccess } from "@/utils/toastMessages";
export default {
  name: "CompanyDetails",
  props: {
    id: {
      type: String,
    },
    leadId: String,
    onUpdate: Function,
    leadProp: Object,
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      attendance: {},
      company: [],
      selectedBranchId: "",
      editValues: { name: "", location: "" },
      map: null,
      coordinates: [],
      infoWindows: [],
      editingFields: [
        {
          value: "name",
          title: "Company",
          editing: false,
          placeholder: "Company Name",
        },
        {
          value: "location",
          title: "Location",
          editing: false,
          placeholder: "Company Location",
        },
      ],
    };
  },
  watch: {
    leadId: {
      handler(id) {
        if (id) this.fetchData();
      },
      immediate: true,
    },
  },
  computed: {
    companyDataDisplay() {
      try {
        const selectedBranchId = this.selectedBranchId;
        const companyBranches = this.company?.companyBranches || [];
        const selectedBranch = companyBranches.filter(
          (branch) => branch._id === selectedBranchId
        )[0];
        if (selectedBranch?.address) {
          const { addressLine1, addressLine2, city, country } =
            selectedBranch.address;
          let locationString = `${addressLine1}`;
          if (addressLine2) {
            locationString += `, ${addressLine2}`;
          }
          locationString += `, ${city}, ${country}`;
          return {
            location: locationString,
            primaryContact: selectedBranch?.contacts[0],
            contacts: selectedBranch?.contacts || [],
          };
        }
      } catch (error) {
        console.error("Error in computed property companyDataDisplay:", error);
        return {
          location: "Location data unavailable",
          primaryContact: null,
        };
      }
      return {};
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.companyEdit);
    },
  },
  mounted() {
    // Initialize the Google Map
    this.loadGoogleMapsScript();
  },
  methods: {
    loadGoogleMapsScript() {
      if (!window.google) {
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyCelkvj4fMod8CkhjPDQegdfuL72QEvzK8&callback=initializeGoogleMap`;
        script.defer = true;
        script.async = true;

        window.initializeGoogleMap = this.initMap;

        script.onload = () => {
          this.initMap();
          delete window.initializeGoogleMap;
        };

        document.head.appendChild(script);
      } else {
        this.initMap();
      }
    },

    initMap() {
      const center = { ...this.coordinates[0] };

      this.map = new window.google.maps.Map(
        document.getElementById("google-map"),
        {
          center,
          zoom: 10,
        }
      );

      this.coordinates.forEach((coordinate) => {
        const marker = new window.google.maps.Marker({
          position: coordinate,
          map: this.map,
        });

        const infoWindow = new window.google.maps.InfoWindow({
          content: `<div style="max-width: 200px;">${coordinate.display_address}</div>`,
        });

        infoWindow.open(this.map, marker);

        this.infoWindows.push(infoWindow);
      });
    },

    async fetchData() {
      try {
        this.company = this.leadProp.company;
        this.editValues.name = this.company?.name;
        this.selectedBranchId = this.leadProp.selectedBranch;
        this.company.location = this.getCompanyLocation(this.company);
        this.coordinates = this.company.companyBranches.map((branch) => {
          const {
            coordinates,
            addressLine1,
            addressLine2,
            city,
            state,
            country,
            pinCode,
          } = branch.address;

          return {
            lat: coordinates?.latitude,
            lng: coordinates?.longitude,
            display_address: Object.values({
              addressLine1,
              addressLine2,
              city,
              state,
              country,
              pinCode,
            }).join(", "),
          };
        });
        this.editValues.location = this.company?.location;
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    },
    async updateCompanyDetails(field) {
      try {
        await this.$axios.put(
          `/workforce/company/${this.company._id}`,
          {
            [field.value]: this.editValues[field.value],
          },
        );
        this.company[field.value] = this.editValues[field.value];
        this.onUpdate(this.company);
        this.$toast.success(companyUpdateSuccess);
        field.editing = false;
      } catch (e) {
        console.error(e);
      }
    },
    cancelEdit(field) {
      field.editing = false;
      this.editValues[field.value] = this.company[field.value];
    },
    getCompanyLocation(company) {
      const companyBranches = company?.companyBranches || [];
      const selectedBranch = companyBranches?.filter(
        (branch) => branch._id === this.selectedBranchId
      )[0];
      if (selectedBranch?.address) {
        const { addressLine1, addressLine2, city, country } =
          selectedBranch.address;
        let locationString = `${addressLine1}`;
        if (addressLine2) {
          locationString += `, ${addressLine2}`;
        }
        locationString += `, ${city}, ${country}`;
        return locationString;
      }
    },
    navToTask() {
      this.$router.push("/workforce/leadtask");
    },
  },
};
</script>
