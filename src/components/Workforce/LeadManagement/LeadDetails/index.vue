<template>
  <div class="lead-details-container bg-gray-100 dark-bg-custom">
    <div>
      <v-main style="padding: 0px" class="bg-[#f7fafb] dark-bg-custom">
        <div class="sticky-tabs">
          <v-col class="custom-tabs">
            <v-tabs
              v-model="tabs"
              align-with-title
              :color="$vuetify.theme.currentTheme.primary"
            >
              <v-tab
                :ripple="false"
                v-for="(tab, index) in tabItems"
                :key="index"
                class="mr-12 ml-2"
                :color="$vuetify.theme.currentTheme.primary"
              >
                {{ tab.name }}
              </v-tab>
              <v-tabs-slider color="#32D583"></v-tabs-slider>
            </v-tabs>
            <v-btn
              plain
              style="
                position: absolute;
                right: 3%;
                top: 25%;
                padding: 0 8px;
                font-size: 1.5rem;
              "
              :color="$vuetify.theme.currentTheme.primary"
              @click="toggleLeadDetails"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-col>
        </div>
      </v-main>
      <div class="component-container">
        <component
          :is="currentTab"
          :leadProp="leadProp"
          :leadId="leadId"
          :onUpdate="onUpdate"
        ></component>
      </div>
    </div>
  </div>
</template>

<script>
import TaskDetails from "@/components/Workforce/LeadManagement/TaskDetails.vue";
import CompanyDetails from "./CompanyDetails.vue";

export default {
  name: "SchedulerPage",
  layout: "Workforce/default",
  components: {
    TaskDetails,
    CompanyDetails,
  },
  props: {
    leadProp: Object,
    leadId: String,
    onUpdate: Function,
    toggleLeadDetails: Function,
  },
  watch: {
    leadId(id) {
      if (id) {
        this.fetchLead(id);
      }
    },
  },
  data() {
    return {
      orgLang: this.$store.state.orgLanguage,
      tabs: 0,
      leadName: "",
      leadSteps: [],
      selectedLead: null,
      companyData: null,
    };
  },
  methods: {
    fetchLead(id) {
      this.$axios
        .get(`/workforce/lead/${id}`)
        .then((res) => {
          this.leadName = res.data.lead.leadTitle;
          this.selectedLead = res.data.lead.leadStatus;
          this.companyData = res.data.lead.company;
        })
        .catch((error) => console.log(error));
    },
    updateSelectedLead() {
      const id = this.leadId;

      if (this.selectedLead) {
        const newData = {
          leadStatus: this.selectedLead.toUpperCase().replace(/\s+/g, "_"),
        };

        this.$axios
          .patch(`/workforce/lead/${id}`, newData)
          .then((response) => {
            const successMessage = response?.data?.message || "Lead Steps Updated!!!"
            this.$toast.success(successMessage);
          })
          .catch((error) => {
            console.error("Error updating lead:", error);
            const errorMessage = error.response.data.message || "Lead Steps Update Failed!!!"
            this.$toast.error(errorMessage);
          });
      }
    },
  },
  computed: {
    currentTab() {
      return this.tabItems[this.tabs]?.component;
    },
    tabItems() {
      if (this.companyData) {
        return [
          {
            key: "tasks",
            name: this.taskLable.taskTab,
            component: "TaskDetails",
          },
          {
            key: "companyDetails",
            name: "Company Details",
            component: "CompanyDetails",
          },
        ];
      } else {
        return [
          {
            key: "tasks",
            name: this.taskLable.taskTab,
            component: "TaskDetails",
          },
        ];
      }
    },
    useCaseTaskConfig() {
      const currentUsecase = this.$route.params.usecase;
      const orgConfig = this.orgLang.data || {};
      const leadModules = orgConfig.taskModules || [];
      const currentConfig = leadModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      return currentConfig;
    },
    taskLable() {
      return {
        taskTab: this.useCaseTaskConfig.tabs.tasks,
        addTask: this.useCaseTaskConfig.tabs.addANewTask,
      };
    },
  },
  mounted() {
    // this.fetchLead(this.leadId);
  },
};
</script>
<style scoped>
.v-tab:before {
  background-color: transparent;
}

.theme--light.v-tabs .v-tab:hover::before {
  opacity: 0;
}

.theme--light.v-tabs .v-tab--active:hover::before,
.theme--light.v-tabs .v-tab--active::before {
  opacity: 0;
}

.custom-tabs {
  padding-left: 0px;
}

.v-tab {
  font-size: 16px;
}

.lead-details-container {
  height: 80vh;
}
.sticky-tabs {
  position: sticky;
  top: 0%;
  z-index: 999;
  background-color: #f7fafb;
}
.dark .sticky-tabs {
  position: sticky;
  top: 0%;
  z-index: 999;
  background-color: var(--bg-custom);
}
.component-container {
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}
/* .sticky-tabs::-webkit-scrollbar {
  display: none;
}
.component-container::-webkit-scrollbar {
  display: none;
} */
</style>
