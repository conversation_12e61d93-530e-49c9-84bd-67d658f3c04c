<template>
  <div class="bg-gray-100 dark-bg-custom pb-8 pt-4">
    <div class="mx-10" v-if="tasks.length">
      <div class="flex justify-between">
        <p class="text-sm" v-if="orgCode === 'FSALES'">
          Select the options to assign the outlets to your employee.
        </p>
        <div class="flex justify-end">
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            small
            @click="selectAllTasks"
            v-if="orgCode === 'FSALES'"
            >{{ selectAll ? "Deselect All" : "Select All" }}</v-btn
          >
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            small
            class="ml-2 white--text"
            v-if="orgCode === 'FSALES'"
            @click="assignTasks"
            :disabled="!anyCheckboxChecked"
            >Assign</v-btn
          >
          <v-icon
            size="60"
            @click="showCreateTaskDialog = true"
            class="floating-btn"
          >
            mdi-plus-circle
          </v-icon>
        </div>
      </div>
      <v-row class="text-center px-8" v-if="isCollectionsEnabled">
        <v-col class="mt-4 font-semibold px-0">Visit/Calls</v-col>
        <v-col class="mt-4 font-semibold px-0">Visit Details</v-col>
        <v-col class="mt-4 font-semibold px-0">Outcome</v-col>
        <v-col class="mt-4 font-semibold px-0">Next Scheduled</v-col>
      </v-row>
      <v-expansion-panels v-model="panel" flat>
        <v-expansion-panel
          v-for="(task, index) in tasks"
          :key="task.id"
          class="mt-5"
        >
          <v-expansion-panel-header class="pt-0 pb-0 penel_header px-2">
            <div class="flex justify-between align-center w-full">
              <div v-if="!isCollectionsEnabled">
                <p class="text-base font-bold mt-4 truncate">
                  {{ taskLable.task }}: {{ tasks.length - index }} -
                  {{ task.taskTitle }}
                </p>
              </div>
              <div v-if="isComplaintsEnabled">
                <v-chip
                  class="rounded-xl white--text font-semibold mr-4"
                  small
                  flat
                  :color="requestChipColor(task)"
                >
                  {{ requestChipText(task) }}
                </v-chip>
                <v-chip
                  class="rounded-xl white--text font-semibold mr-4"
                  small
                  v-show="task.complaintDetails?.needToChargeCustomer"
                  flat
                  :color="paymentChipColor(task)"
                >
                  {{ paymentChipText(task) }}
                </v-chip>

                <!-- <v-btn
                    class="mr-2"
                    color="primary"
                    text
                    v-show="task.complaintDetails?.requirePartsReplacement"
                    @click.stop.prevent="openShop(task)"
                    >Shop Now</v-btn
                  > -->
              </div>
              <v-row v-if="isCollectionsEnabled" class="text-center">
                <v-col>
                  <p class="text-base font-semibold mt-5">
                    {{ tasks.length - index }}
                  </p>
                </v-col>
                <v-col>
                  <p
                    class="mt-5 font-semibold text-blue-600"
                    @click.stop.prevent="taskDetails(task)"
                  >
                    {{ leadProp.leadTitle }}
                  </p>
                </v-col>
                <v-col>
                  <p class="mt-5 font-semibold">
                    {{ task.meetingDetails?.meetingOutCome }}
                  </p>
                </v-col>
                <v-col>
                  <p class="mt-5 font-semibold">
                    {{
                      task.meetingDetails?.nextTaskScheduledOn
                        ? $dayjs(
                            task.meetingDetails?.nextTaskScheduledOn
                          ).format("DD/MM/YYYY")
                        : ""
                    }}
                  </p>
                </v-col>
              </v-row>
              <v-checkbox
                :color="$vuetify.theme.currentTheme.primary"
                v-model="task.checked"
                @click.stop.prevent="taskChecked(task)"
                v-if="!isComplaintsEnabled && orgCode === 'FSALES'"
              ></v-checkbox>
            </div>
          </v-expansion-panel-header>
          <v-expansion-panel-content class="text-sm">
            <div class="flex justify-end mt-2">
              <v-icon color="red" class="mt-2 mr-2" v-if="statusAlert"
                >mdi-alert</v-icon
              >
              <v-btn
                class="mr-2 mt-2"
                :color="$vuetify.theme.currentTheme.primary"
                v-show="task.complaintDetails?.requirePartsReplacement"
                text
                :disabled="task.taskStatus === 'COMPLETED'"
                @click.stop.prevent="requestInventory(task)"
                >Part Request</v-btn
              >
              <!-- Pencil icon for editing task -->
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-icon
                    v-on="on"
                    v-bind="attrs"
                    v-if="canEdit"
                    class="mr-5 mt-2"
                    @click.stop.prevent="toggleEditMode(index)"
                    style="color: #4fa6ff"
                  >
                    {{ task.editMode ? "mdi-close" : "mdi-pencil-outline" }}
                  </v-icon>
                </template>
                <span>{{
                  task.editMode ? "Close Edit" : `Edit ${taskLable.task}`
                }}</span>
              </v-tooltip>
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-icon
                    v-on="on"
                    v-bind="attrs"
                    v-if="canDelete"
                    @click.stop.prevent="deleteTaskButton(task._id)"
                    class="mt-2 mr-3"
                    style="color: rgb(219, 77, 77)"
                  >
                    mdi-delete-outline
                  </v-icon>
                  <v-dialog v-model="deleteTaskDialog" max-width="600">
                    <v-card>
                      <v-card-title
                        class="headline text-h5 justify-center text-[#6B6D78]"
                        >Confirm Deletion</v-card-title
                      >
                      <v-card-text class="text-center mt-2">
                        Are you sure you want to delete this task?
                      </v-card-text>
                      <v-card-actions class="flex justify-center mb-2">
                        <!-- <v-spacer></v-spacer> -->
                        <v-btn
                          outlined
                          color="grey"
                          class="rounded-lg mr-8 px-6"
                          @click.stop.prevent="deleteTaskDialog = false"
                          ><span class="text-black">Cancel</span></v-btn
                        >
                        <v-btn
                          color="#2A83FF"
                          class="white--text rounded-lg px-6"
                          @click="deleteTask"
                          >Confirm</v-btn
                        >
                      </v-card-actions>
                    </v-card>
                  </v-dialog>
                </template>
                <span>Delete Task</span>
              </v-tooltip>
            </div>
            <div>
              <create-task
                v-if="true"
                :minimal="true"
                :onCreate="onCreateTask"
                :closeModal="() => toggleEditMode(index)"
                :editedTask="task"
                :leadProp="leadProp"
                :editMode="task.editMode"
                :canSetFrequency="task.editMode"
                :onUpdate="
                  (task, partReplacement) =>
                    onTaskUpdate(task, index, partReplacement)
                "
              />
            </div>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </div>
    <no-data
      title="Nothing to Display"
      :subTitle="`Start by adding ${taskLable.task}`"
      :btnText="`Add ${taskLable.task}`"
      :btnAction="() => (showCreateTaskDialog = true)"
      v-else
    >
    </no-data>
    <v-dialog v-model="showCreateTaskDialog" width="auto" persistent>
      <div>
        <create-task
          :onCreate="onCreateTask"
          :closeModal="() => (showCreateTaskDialog = false)"
          :selectedLead="lead"
          :canSetFrequency="true"
          @task-created="handleTaskCreated"
        />
      </div>
    </v-dialog>
    <v-dialog v-model="shopDialog" max-width="63rem" persistent>
      <div>
        <ShopComponent
          :selectedComplaintId="selectedComplaintId"
          :linkedTaskId="linkedTaskId"
          :linkedCustomerId="linkedCustomerId"
          :assignedToId="assignedToId"
          @cancel="closeShopDialog"
        />
      </div>
    </v-dialog>
    <v-dialog v-model="requestDialog" max-width="63rem">
      <div>
        <InventoryRequest
          @requestRaised="onRaiseRequest"
          @cancel="closeRequestDialog"
          :selectedComplaintId="selectedComplaintId"
          :linkedTaskId="linkedTaskId"
        />
      </div>
    </v-dialog>
    <v-dialog v-model="openTaskDetails" max-width="63rem">
      <TaskData :task="selectedTask" :key="selectedTask?._id" />
    </v-dialog>
  </div>
</template>

<script>
import { hasPermission } from "@/utils/common";
import permissionData from "@/utils/permissions";
import NoData from "@/components/NoData.vue";
import ShopComponent from "../ShopComponent";
import InventoryRequest from "../InventoryRequest";
import TaskData from "./TaskData.vue";
import CreateTask from "@/components/Workforce/LeadManagement/CreateTask";
import {
  taskUpdateSuccess,
  taskUpdateError,
  taskDeleteSuccess,
  taskDeleteError,
} from "@/utils/toastMessages";
export default {
  name: "LeadDetails",
  components: {
    CreateTask,
    NoData,
    ShopComponent,
    InventoryRequest,
    TaskData,
  },
  props: {
    leadProp: Object,
    leadId: String,
  },
  data() {
    return {
      orgCode: this.$storage.getUniversal("organization_code"),
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      tasks: [],
      editModeData: [],
      showCreateTaskDialog: false,
      deleteTaskDialog: false,
      shopDialog: false,
      requestDialog: false,
      statusAlert: false,
      selectAll: false,
      panel: 0,
      taskToDeleteId: "",
      assignedToId: null,
      selectedComplaintId: null,
      linkedTaskId: null,
      linkedCustomerId: null,
      users: [],
      selectedAssignedTo: null,
      complaintDetails: null,
      openTaskDetails: false,
      selectedTask: null,
      lead: null,
      meetingNotes: "",
      managerRemarks: "",
      taskSteps: [],
      replacementOptions: [
        { text: "Yes", value: true },
        { text: "No", value: false },
      ],
      chargeCustomerOptions: [
        { text: "Yes", value: true },
        { text: "No", value: false },
      ],
      paymentStatusOptions: ["COMPLETED", "PENDING"],
      meetingTypeOptions: ["IN_PERSON"],
      leadName: "",
      selectedLead: "Introduce product",
    };
  },
  watch: {
    leadProp: {
      handler(lead) {
        if (lead) this.fetchData(lead);
      },
      immediate: true,
    },
  },
  methods: {
    onRaiseRequest(data) {
      const taskIndx = this.tasks.findIndex(
        (task) => task._id === data.request.task
      );
      const task = this.tasks[taskIndx];

      if (task && task.complaintDetails?.linkedRequestId?.requestStatus) {
        task.complaintDetails.linkedRequestId.requestStatus =
          data.request.requestStatus;
      }
    },
    onTaskUpdate(task, indx, partReplacement) {
      this.toggleEditMode(indx);
      if (this.tasks[indx].complaintDetails) {
        this.tasks[indx].complaintDetails.requirePartsReplacement =
          partReplacement;
      }
      this.tasks[indx].taskTitle = task.taskTitle;
    },
    fetchData(lead) {
      this.lead = lead;
      this.tasks = lead.tasks;
      this.editModeData = this.tasks.map((task) => ({ ...task }));
    },
    isPaymentDetails(task) {
      return (
        task.complaintDetails &&
        this.isComplaintsEnabled &&
        task.complaintDetails.paymentStatus === "COMPLETED"
      );
    },
    deepClone(obj) {
      return JSON.parse(JSON.stringify(obj));
    },
    toggleEditMode(index) {
      const task = this.tasks[index];
      if (task.editMode) {
        this.$set(this.tasks, index, this.deepClone(this.editModeData[index]));
      } else {
        this.$set(this.editModeData, index, this.deepClone(task));
      }
      this.$set(this.tasks[index], "editMode", !task.editMode);
    },
    deleteTaskButton(deleteId) {
      this.deleteTaskDialog = true;
      this.taskToDeleteId = deleteId;
    },
    handleTaskCreated(newData) {
      const newAddedTaskArray = newData.lead.tasks;
      this.tasks = newAddedTaskArray.map((el) => ({
        ...el,
        assignedToFullName: `${el.assignedTo?.firstName} ${el.assignedTo?.lastName}`,
      }));
    },
    onCreateTask() {
      this.showCreateTaskDialog = false;
    },
    async saveChanges(index) {
      try {
        const task = this.tasks[index];
        const taskId = task._id;

        const updatedTask = {
          taskTitle: task.taskTitle,
          taskDescription: task.taskDescription,
          assignedTo: task.selectedAssignedTo,
          meetingType: task.meetingType,
          taskStartDateTime: task.taskStartDateTime,
          taskEndDateTime: task.taskEndDateTime,
          taskStatus: task.taskStatus,
          complaintDetails: {
            ...task.complaintDetails,
            natureOfComplaint: task.complaintDetails.natureOfComplaint,
            requirePartsReplacement:
              task.complaintDetails.requirePartsReplacement,
            needToChargeCustomer: task.complaintDetails.needToChargeCustomer,
          },
        };

        if (task.complaintDetails.needToChargeCustomer) {
          updatedTask.complaintDetails.paymentStatus =
            task.complaintDetails.paymentStatus;
        }
        if (task.meetingDetails) {
          updatedTask.meetingDetails = {
            _id: task.meetingDetails._id,
            meetingWith: task.contactTo,
            meetingNotes: task.meetingDetails?.meetingNotes,
            managerRemarks: task.meetingDetails?.managerRemarks,
          };
        }
        if (task.complaintDetails.paymentStatus === "COMPLETED") {
          updatedTask.complaintDetails.paymentDetails = {
            transactionNumber:
              task.complaintDetails.paymentDetails.transactionNumber,
            modeOfPayment: task.complaintDetails.paymentDetails.modeOfPayment,
            paymentMadeOn: task.complaintDetails.paymentDetails.paymentMadeOn,
          };
        } else {
          updatedTask.complaintDetails.paymentDetails = {};
        }

        await this.$axios.put(`/workforce/task/${taskId}`, updatedTask);
        task.editMode = false;
        this.$toast.success(taskUpdateSuccess(this.taskLable.task));
      } catch (error) {
        console.error(taskUpdateError(this.taskLable.task), error);
        // this.$toast.error(taskUpdateError(this.taskLable.task));
        this.$toast.error(error.response.data?.message);
      }
    },
    deleteTask() {
      try {
        this.$axios
          .delete(`/workforce/task/${this.taskToDeleteId}`)
          .then(() => {
            this.tasks = this.tasks.filter(
              (task) => task._id !== this.taskToDeleteId
            );
            this.deleteTaskDialog = false;
            this.$toast.success(taskDeleteSuccess(this.taskLable.task));
          })
          .catch((error) => {
            console.error(taskDeleteError(this.taskLable.task), error);
            this.$toast.error(taskDeleteError(this.taskLable.task));
          });
      } catch (error) {
        console.error("Error in deleteTask:", error);
      }
    },

    fetchUser() {
      this.$axios
        .get(`/workforce/users?action=MY_REPORTEES`)
        .then((res) => {
          this.users = res.data.users.map((users) => ({
            ...users,
            id: users._id,
            full_name: `${users.firstName} ${users.lastName}`,
          }));
        })
        .catch((error) => {
          console.log(error.message);
        });
    },
    fetchTaskSteps() {
      this.$axios
        .get(`/workforce/task/steps`)
        .then((response) => {
          const taskStepsArray = [];

          for (const key in response.data?.taskSteps) {
            if (
              response.data.taskSteps.hasOwnProperty.call(
                response.data?.taskSteps,
                key
              )
            ) {
              taskStepsArray.push({
                key: key,
                value: response.data.taskSteps[key],
              });
            }
          }

          this.taskSteps = taskStepsArray;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    check(index, id) {
      const task = this.tasks[index];
      const user = this.users.find((el) => el._id === id);
      task.assignedToFullName = `${user?.firstName} ${user?.lastName}`;
      for (const task of this.tasks) {
        task.selectedAssignedTo = task.assignedTo?._id || null;
      }
    },
    openShop(item) {
      this.selectedComplaintId = item.complaintDetails._id;
      this.linkedTaskId = item._id;
      this.linkedCustomerId = this.leadProp.company._id;
      this.assignedToId = item.assignedTo._id;
      this.shopDialog = true;
    },
    requestInventory(task) {
      const status = task.complaintDetails?.linkedRequestId?.requestStatus;
      if (status === "RAISED" || status === "HOLD") {
        setTimeout(() => {
          this.statusAlert = false;
        }, 5000);
        this.statusAlert = true;
        this.$toast.error(
          "Your previous request is pending, clear previous requests"
        );
        return;
      }
      this.selectedComplaintId = task.complaintDetails._id;
      this.linkedTaskId = task._id;
      this.requestDialog = true;
    },
    closeShopDialog() {
      this.shopDialog = false;
    },
    closeRequestDialog() {
      this.requestDialog = false;
    },
    paymentChipColor(task) {
      return task.complaintDetails?.paymentStatus === "COMPLETED"
        ? "green"
        : "red";
    },
    paymentChipText(task) {
      return task.complaintDetails?.paymentStatus === "COMPLETED"
        ? "Payment Completed"
        : "Payment Pending";
    },
    requestChipColor(task) {
      if (task) {
        const status = task.complaintDetails?.linkedRequestId?.requestStatus;
        if (status === "RAISED") {
          return "blue";
        } else if (status === "APPROVED") {
          return "green";
        } else if (status === "WITHDRAW") {
          return "grey";
        } else if (status === "HOLD") {
          return "#ffcc00";
        } else if (status === "REJECTED") {
          return "red";
        } else {
          return "teal";
        }
      }
    },
    requestChipText(task) {
      const status = task.complaintDetails?.linkedRequestId?.requestStatus;
      if (status === "RAISED") {
        return "Part Request Raised";
      } else if (status === "APPROVED") {
        return "Part Request Apporved";
      } else if (status === "WITHDRAW") {
        return "Part Request Withdraw";
      } else if (status === "HOLD") {
        return "Part Request Hold";
      } else if (status === "REJECTED") {
        return "Part Request Rejected";
      } else {
        return "No Part Requested";
      }
    },
    async assignTasks() {
      try {
        const selectedTasks = this.tasks.filter((task) => task.checked);
        const apiCalls = selectedTasks.map(async (task) => {
          const taskId = task._id;
          const params = {
            assignedTo: this.leadProp?.assignedTo._id,
          };

          await this.$axios.put(`/workforce/task/${taskId}`, params);
        });

        await Promise.all(apiCalls);

        this.panel = 0;

        this.$toast.success("Outlet has been assigned !!");
      } catch (error) {
        console.log(error);
      }
    },

    taskChecked(task) {
      task.checked = !task.checked;
      this.$set(task, "checked", !task.checked);
    },
    selectAllTasks() {
      this.selectAll = !this.selectAll;
      this.tasks.forEach((task) => {
        this.$set(task, "checked", !task.checked);
      });
    },
    autoCheckAssignedTo() {
      this.tasks.forEach((task) => {
        if (task.assignedTo) {
          this.$set(task, "checked", !task.checked);
        }
      });
    },
    taskDetails(task) {
      this.selectedTask = task;
      this.openTaskDetails = true;
    },
  },
  mounted() {
    this.fetchUser();
    this.fetchTaskSteps();
    if (this.orgCode === "FSALES") {
      this.autoCheckAssignedTo();
    }
    for (const task of this.tasks) {
      task.selectedAssignedTo = task.assignedTo?._id || null;
    }
  },
  computed: {
    useCaseTaskConfig() {
      const currentUsecase = this.$route.params.usecase;
      const orgConfig = this.orgLang.data || {};
      const taskModules = orgConfig.taskModules || [];
      const currentConfig = taskModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      return currentConfig;
    },
    isComplaintsEnabled() {
      if (this.useCaseTaskConfig?.useCaseType === "inventory") return true;
      return false;
    },
    isCollectionsEnabled() {
      if (this.useCaseTaskConfig?.useCaseType === "collection") return true;
      return false;
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.taskEdit);
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.taskDelete);
    },
    taskLable() {
      return {
        taskFormObj: this.orgLang?.data?.allocateTaskModule.addTaskForm,
        task: this.useCaseTaskConfig?.taskTable?.columns?.taskTitle || "",
      };
    },
    anyCheckboxChecked() {
      return this.tasks.some((task) => task.checked);
    },
  },
};
</script>

<style scoped>
td {
  word-wrap: break-word;
}

.lead-step {
  border: 1px solid grey;
  border-radius: 2px;
  width: 200px;
}

tbody > tr:hover {
  background-color: transparent !important;
}

.v-text-field {
  padding: 0px;
  margin: 0px;
}

.v-data-table td {
  border-bottom: none !important;
}

.penel_header {
  border-bottom: 1px solid rgb(192, 189, 189);
}

.cancel {
  border: 1px solid #2a83ff;
  background: white;
}

.submit {
  background: #2a83ff;
}

.floating-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  color: #2a83ff;
  position: fixed;
  z-index: 20;
  bottom: 15%;
}
</style>
