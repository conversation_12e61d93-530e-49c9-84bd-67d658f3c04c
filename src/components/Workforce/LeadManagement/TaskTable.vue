<template>
  <div>
    <div>
      <div class="overflow-x-auto" v-show="!hideFilters">
        <div class="justify-end items-center rounded-lg mt-2 mb-5 cursor-pointer">
          <div
            class="justify-end mb-0 items-center"
            :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']"
          >
            <v-col
              :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
              :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
              v-if="isComplaintsEnabled"
            >
              <v-text-field
                v-model="modelNo"
                label="Product Name/Serial/Model No"
                placeholder="Product Name/Serial/Model No"
                prepend-inner-icon="mdi-magnify"
                persistent-hint
                solo
                flat
                clearable
                @input="handleInput"
                class="custom-text-field"
                outlined
                dense
              ></v-text-field>
            </v-col>
            <v-col
              :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
              :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
              v-if="isComplaintsEnabled"
            >
              <v-text-field
                v-model="complaintNo"
                label="Complaint/BD/PO/MSR/Service No"
                placeholder="Complaint/BD/PO No"
                prepend-inner-icon="mdi-magnify"
                persistent-hint
                solo
                flat
                clearable
                @input="handleInput"
                class="custom-text-field"
                outlined
                dense
              ></v-text-field>
            </v-col>

            <!-- <div
              class="justify-end mb-0 items-center"
              :class="[$vuetify.breakpoint.lgAndUp ? 'inline-flex' : 'inline-flex']"
            >
              <v-tooltip top>
                <template #activator="{ on, attrs }">
                  <v-btn
                    :color="$vuetify.theme.currentTheme.primary"
                    :loading="exportLoading"
                    :disabled="exportLoading"
                    class="ml-4 white--text mr-4 mb-2"
                    @click="exportDataToXLSX"
                    v-bind="attrs"
                    v-on="on"
                  >
                    Export
                    <v-icon right dark> mdi-download </v-icon>
                  </v-btn>
                </template>
                <span>Export Tasks</span>
              </v-tooltip>
              <FilterPopOver :selectedCount="selectedCount">
                <v-card outlined class="max-h-96 w-72">
                  <v-list>
                    <v-list-item class="px-0">
                      <v-col class="pb-0 pt-0">
                        <v-menu
                          v-model="menu"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                        >
                          <template v-slot:activator="{ on }">
                            <v-text-field
                              hide-details
                              outlined
                              dense
                              label="Select Date Range"
                              placeholder="Select Date Range"
                              v-model="formattedDate"
                              prepend-inner-icon="mdi-calendar"
                              readonly
                              clearable
                              @click:clear="onDateCleared"
                              v-on="on"
                            ></v-text-field>
                          </template>
                          <v-date-picker
                            v-model="dates"
                            @input="onDateSelected"
                            no-title
                            @change="menu = false"
                            range
                            scrollable
                          />
                        </v-menu>
                      </v-col>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-col class="pb-0">
                        <v-autocomplete
                          v-model="filterAssignedTo"
                          label="Select Assigned To"
                          placeholder="Select Assigned To"
                          :items="populateUser"
                          item-value="_id"
                          item-text="full_name"
                          hide-details
                          outlined
                          dense
                          clearable
                          @change="handleChange"
                        ></v-autocomplete>
                      </v-col>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-col class="pb-0">
                        <v-select
                          v-model="filterPayment"
                          label="Payment Status"
                          :items="paymentOptions"
                          hide-details
                          outlined
                          dense
                          clearable
                          @change="handleChange"
                        ></v-select>
                      </v-col>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-col class="pb-0">
                        <v-combobox
                          v-model="selectedCompany"
                          label="Select Customer"
                          placeholder="Select Customer"
                          :items="companyNames"
                          :search-input.sync="searchCompany"
                          @update:search-input="handleCompany"
                          hide-details
                          outlined
                          dense
                          clearable
                          @click:clear="handleInput"
                          @change="updateSelectedCount"
                        ></v-combobox>
                      </v-col>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-col class="pb-0">
                        <v-autocomplete
                          v-model="selectedCompanyBranch"
                          :items="selectedCompanyBranches"
                          label="Customer Address"
                          placeholder="Customer Address"
                          :disabled="!selectedCompany?.value"
                          hide-details
                          outlined
                          dense
                          clearable
                          multiple
                          class="custom-input"
                          @change="handleChange"
                        >
                          <v-list-item
                            class="pl-4 select-all-item"
                            slot="prepend-item"
                            @click="toggleSelectAllAddress"
                          >
                            <v-list-item-action>
                              <v-icon
                                :color="selectedCompanyBranch.length > 0 ? 'blue darken-2' : ''"
                                >{{ branchSelectIcon }}</v-icon
                              >
                            </v-list-item-action>
                            <v-list-item-title>Select All</v-list-item-title>
                          </v-list-item>
                          <v-divider slot="prepend-item" />
                          <template v-slot:selection="{ item, index }">
                            <v-chip small class="chip-width" v-if="index < 1">
                              <span class="chip-text">{{ item.text }}</span>
                            </v-chip>
                            <span v-if="index === 1" class="grey--text text-caption">
                              (+{{ selectedCompanyBranch.length - 1 }} more)
                            </span>
                          </template>
                        </v-autocomplete>
                      </v-col>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-col class="pb-0">
                        <v-select
                          label="Task Status"
                          placeholder="Task Status"
                          v-model="selectedTaskStatus"
                          :items="taskStepsSelect"
                          item-text="value"
                          item-value="key"
                          hide-details
                          outlined
                          dense
                          multiple
                          small-chips
                          clearable
                          @change="handleTaskChange"
                          @click:clear="onStatusCleared"
                        >
                          <v-list-item
                            class="pl-4 select-all-item"
                            slot="prepend-item"
                            @click="toggleSelectAll"
                          >
                            <v-list-item-action>
                              <v-icon
                                :color="selectedTaskStatus.length > 0 ? 'blue darken-2' : ''"
                                >{{ icon }}</v-icon
                              >
                            </v-list-item-action>
                            <v-list-item-title>Select All</v-list-item-title>
                          </v-list-item>
                          <v-divider slot="prepend-item" />

                          <template v-slot:selection="{ item, index }">
                            <v-chip small v-if="index < 1">
                              <span>{{ item.value }}</span>
                            </v-chip>
                            <span v-if="index === 1" class="grey--text text-caption">
                              (+{{ selectedTaskStatus.length - 1 }} more)
                            </span>
                          </template>
                        </v-select>
                      </v-col>
                    </v-list-item>
                  </v-list>
                </v-card>
              </FilterPopOver>
            </div> -->
          </div>
        </div>
      </div>

      <v-data-table
        :headers="tableHeaders"
        :items="alltask"
        class="pl-7 pr-7 pt-4 rounded-lg"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :options.sync="options"
        :server-items-length="totalTasks"
        :loading="loadTable"
        @click:row="selectTask"
        fixed-header
      >
        <template v-slot:item.companyName="{ item }">
          <td
            class="px-2 cursor-pointer hover:text-blue-600 td_font td-ellipsis font-semibold"
            @click="selectTask(item)"
          >
            {{ item.leadDetails.company?.name || '—' }}
            <br />
            {{ getCompanyBranch(item.selectedBranch, item.leadDetails) }}
          </td>
        </template>
        <template v-slot:item.taskTitle="{ item }">
          <td class="px-2">
            <div class="flex flex-col">
              <div
                class="td_font font-semibold cursor-pointer hover:text-blue-600 hover:dark-hover-text td-ellipsis"
                @click="selectTask(item)"
              >
                <v-tooltip top>
                  <template v-slot:activator="{ on }">
                    <span @contextmenu="(e) => copyToClipboard(item.taskTitle, e)" v-on="on">{{
                      item.taskTitle
                    }}</span>
                  </template>
                  <span>{{ item.taskTitle }}</span>
                </v-tooltip>
              </div>
              <span class="font-semibold text-gray-600">
                {{ item.complaintDetails?.complainSrNo }}
              </span>
            </div>
          </td>
        </template>
        <template v-slot:item.complaintLogDate="{ item }">
          <td class="px-2 td_font">
            {{ ChangeDate(item.complaintDetails?.createdAt, 'DD-MM-YYYY') }}
            <br />
            {{ ChangeDate(item.complaintDetails?.createdAt, 'hh:mm A') }}
          </td>
        </template>
        <template v-slot:item.complaintCloseDate="{ item }">
          <td class="px-2 td_font">
            {{ ChangeDate(item.actualEndDateTime, 'DD-MM-YYYY') }}
            <br />
            {{ ChangeDate(item.actualEndDateTime, 'hh:mm A') || '—' }}
          </td>
        </template>
        <template v-slot:item.complaintStatus="{ item }">
          <td class="px-2 td_font">
            {{ item.complaintDetails?.status }}
          </td>
        </template>
        <template v-slot:item.productName="{ item }">
          <td class="px-2">
            <div class="flex flex-col">
              <span class="td_font">
                {{ item.leadDetails?.productName?.productName }}
              </span>
              <span class="font-semibold text-gray-600">
                {{ item.leadDetails?.productModelNumber }}
              </span>
            </div>
          </td>
        </template>
        <template v-slot:item.productWarranty="{ item }">
          <td class="px-2 td_font font-semibold">
            <span
              :class="[
                item.leadDetails?.productWarrantyStatus === 'IN_WARRANTY'
                  ? 'text-green-500'
                  : 'text-yellow-500'
              ]"
            >
              {{ item.leadDetails?.productWarrantyStatus?.split('_').join(' ') || '—' }}
            </span>
          </td>
        </template>
        <template v-slot:item.productAmc="{ item }">
          <td class="px-2 td_font font-semibold">
            <span
              :class="[item.leadDetails?.productHaveAmcPlan ? 'text-green-500' : 'text-red-500']"
            >
              {{ item.leadDetails?.productHaveAmcPlan ? 'AMC ACTIVE' : 'AMC INACTIVE' }}
            </span>
          </td>
        </template>
        <template v-slot:item.partReplacement="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ item.complaintDetails?.requirePartsReplacement ? 'Yes' : 'No' }}
          </td>
        </template>
        <template v-slot:item.requestId="{ item }">
          <td class="px-2">
            <div class="flex flex-col">
              <span class="td_font font-semibold">
                {{ getRequestId(item) }}
              </span>
              <span class="font-semibold text-gray-600">
                {{ getRequestStatus(item) }}
              </span>
            </div>
          </td>
        </template>
        <template v-slot:item.requestComment="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ getRequestComment(item) }}
          </td>
        </template>
        <template v-slot:item.allOrders="{ item }">
          <td
            @contextmenu="
              (e) => copyToClipboard(extractOrderNumbers(item.complaintDetails?.orderDetails), e)
            "
            class="px-2 td_font font-semibold"
          >
            {{ extractOrderNumbers(item.complaintDetails?.orderDetails) || '—' }}
          </td>
        </template>
        <template v-slot:item.serviceReportNumber="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ item.complaintDetails?.serviceReportNumber || '—' }}
          </td>
        </template>
        <template v-slot:item.paymentStatus="{ item }">
          <td class="px-2 td_font font-semibold">
            <span :class="getPaymentStatusClassNames(item.complaintDetails?.paymentStatus)">
              {{ item.complaintDetails?.paymentStatus || '—' }}
            </span>
            <br />
            {{ formattedPaymentDetails(item.complaintDetails) }}
          </td>
        </template>

        <template v-slot:item.managerRemarks="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ item.meetingDetails?.managerRemarks || '—' }}
          </td>
        </template>
        <template v-slot:item.leadTitle="{ item }">
          <td class="px-2 td_font td-ellipsis">
            <v-tooltip top>
              <template v-slot:activator="{ on }">
                <span v-on="on">{{ item.leadDetails?.leadTitle }}</span>
              </template>
              <span>{{ item.leadDetails?.leadTitle }}</span>
            </v-tooltip>
          </td>
        </template>

        <template v-slot:item.tda="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ item.tda || '—' }}
          </td>
        </template>
        <template v-slot:item.currentBalance="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ item.currentBalance || '—' }}
          </td>
        </template>
        <template v-slot:item.taskStartDateTime="{ item }">
          <td class="px-2 td_font">
            {{ ChangeDate(item.taskStartDateTime, 'DD-MM-YYYY') }}
            <br />
            {{ ChangeDate(item.taskStartDateTime, 'hh:mm A') }}
          </td>
        </template>

        <template v-slot:item.taskEndDateTime="{ item }">
          <td class="px-2 td_font">
            {{ ChangeDate(item.taskEndDateTime, 'DD-MM-YYYY') }}
            <br />
            {{ ChangeDate(item.taskEndDateTime, 'hh:mm A') }}
          </td>
        </template>

        <template v-slot:item.assignedTo="{ item }">
          <td class="px-2 td_font">
            <v-select
              solo
              flat
              dense
              hide-selected
              :menu-props="{ offsetY: true }"
              class="fixed-width-column mt-5"
              v-model="item.assignedTo"
              :readonly="!canEdit"
              :items="users"
              item-value="_id"
              @click.stop.prevent="previousAssignedTo = item.assignedTo"
              @change="onChangeEmployee(item)"
            >
              <template v-slot:selection="{ item }">
                <employee-info :item="item"></employee-info>
              </template>
              <template v-slot:item="{ item }">
                <employee-info :item="item"></employee-info>
              </template>
            </v-select>
          </td>
        </template>
        <template v-slot:item.acknowledged="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ item.acknowledged || '—' }}
          </td>
        </template>
        <template v-slot:item.tat="{ item }">
          <td class="px-2 td_font font-semibold">
            {{ calculateTAT(item) || '—' }}
          </td>
        </template>

        <template v-slot:item.taskStatus="{ item }">
          <td class="w-48 td_font">
            <v-menu offset-y :disabled="!canEdit">
              <template v-slot:activator="{ on }">
                <v-btn v-on="on" :style="getStatusColor(item.taskStatus)" class="text-capitalize">
                  {{ taskSteps.find((status) => status.key === item.taskStatus)?.value }}
                  <v-icon right dark> mdi-chevron-down </v-icon>
                </v-btn>
              </template>
              <v-list
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  width: 250px;
                  border-radius: 20px;
                  text-align: center;
                "
              >
                <v-list-item
                  v-for="status in taskSteps"
                  :key="status.key"
                  @click="handleStatusChange(item, status.key)"
                >
                  <v-list-item-content :style="getStatusColor(status.key)">
                    <span style="white-space: nowrap; font-size: 14px">{{ status.value }}</span>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-menu>
          </td>
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4 py-5 td_font" @click.stop>
            <action-button
              :item="item"
              :canEdit="canEdit"
              :showDeleteButton="canDelete"
              :canDelete="canDelete"
              :handleEdit="() => taskEdit(item)"
              :handleDelete="() => selectTaskDelete(item)"
            />
          </td>
        </template>
      </v-data-table>
      <v-dialog v-model="showConfirmationDialog" max-width="600">
        <v-card>
          <v-card-title class="text-h5 justify-center">
            {{ confirmationTitle }}
          </v-card-title>
          <v-card-text class="text-center">
            {{ confirmationMessage }}
          </v-card-text>
          <v-card-actions class="flex justify-center mb-2">
            <v-btn outlined color="grey" class="rounded-lg mr-4 px-6" @click="cancelUpdate">
              <span class="text-black">Cancel</span>
            </v-btn>
            <v-btn
              :color="confirmationActionColor"
              class="white--text rounded-lg px-6"
              @click="performAction"
            >
              {{ confirmationActionText }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="showTaskDataDialog" width="70rem">
        <div class="sticky flex top-0 justify-end z-50 bg-[#f3f4f6] dark-bg-custom">
          <v-btn plain @click="closeDialog">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
        <TaskData :task="selectedTask" :key="selectedTask?._id" :imagesData="imagesData" />
      </v-dialog>
      <v-dialog v-model="showEditDialog" width="54rem" persistent>
        <!-- <div v-if="showEditDialog"> -->
        <create-task
          :onCreate="onCreateTask"
          :closeModal="() => (showEditDialog = false)"
          :editedTask="editedTask"
          :canSetFrequency="false"
          :leadProp="editedTask?.leadDetails"
        />
        <!-- </div> -->
      </v-dialog>

      <v-dialog v-model="showEmployeeChange" max-width="600">
        <v-card>
          <v-card-title class="text-h5 justify-center">Confirm Change</v-card-title>
          <v-card-text class="text-center">
            Are you sure you want to assign {{ employeeChangePopupContent }}?
          </v-card-text>
          <v-card-actions class="flex justify-center mb-2">
            <v-btn
              outlined
              color="grey"
              class="rounded-lg mr-8 px-6"
              @click="onCancelChangeEmployee"
              ><span class="text-black">Cancel</span></v-btn
            >
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text rounded-lg px-6"
              @click="onConfirmChangeEmployee()"
              >Confirm</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import TaskData from './TaskData.vue'
import CreateTask from './CreateTask'
import EmployeeInfo from '@/components/EmployeeInfo.vue'
import { hasPermission, dayHourFormat } from '@/utils/common'
import ActionButton from '@/components/ActionButton'
import { debounce, sortDropdownOptions } from '@/utils/common'
import permissionData from '@/utils/permissions'
import {
  taskDeleteSuccess,
  taskDeleteError,
  taskStatusSuccess,
  taskStatusError,
  taskAssignSuccess
} from '@/utils/toastMessages'
import { taskStatusColors } from '@/utils/workforce/statusColors'
import FilterPopOver from '@/components/FilterPopOver.vue'
export default {
  layout: 'Workforce/default',
  name: 'TeamTaskComponent',
  components: {
    TaskData,
    EmployeeInfo,
    ActionButton,
    CreateTask
    // FilterPopOver
  },
  props: {
    taskSteps: Array,
    useCaseTaskConfig: Object,
    users: Array,
    companies: Array
  },
  data() {
    return {
      orgCode: this.$storage.getUniversal('organization_code'),
      orgLang: this.$store.state.orgLanguage,
      userData: this.$store.state.wfmPermissions,
      totalTasks: 0,
      options: { sortBy: [], sortDesc: [], itemsPerPage: 5, page: 1 },
      showFilters: !!this.$route.query.complaint_no,
      direction: 'left',
      alltask: [],
      showTaskDataDialog: false,
      exportLoading: false,
      showEditDialog: false,
      leads: [],
      imagesData: [],
      selectedTask: null,
      editedTask: null,
      searchCompany: '',
      loadTable: true,
      selectedStartDate: null,
      selectedEndDate: null,
      paymentOptions: ['COMPLETED', 'PENDING', 'PO'],
      filterPayment: null,
      filterAssignedTo: '',
      selectedTaskStatus: [
        'PENDING',
        'MISSED',
        'IN_PROGRESS',
        'ENDED',
        'DEFERRED',
        'COMPLETED',
        'CANCELLED',
        'REJECTED',
        'APPROVED'
      ],
      taskStepColors: taskStatusColors,
      menu: false,
      dates: [],
      TaskStatus: '',
      complaintNo: this.$route.query.complaint_no || '',
      modelNo: '',
      leadName: '',
      company: '',
      selectedCompanyBranch: [],
      selectedCompany: null,
      showConfirmationDialog: false,
      showEmployeeChange: false,
      selectedEmployee: null,
      previousAssignedTo: null,
      isUpdateAction: false,
      shouldFetchData: true,
      confirmationTitle: '',
      confirmationMessage: '',
      confirmationActionText: '',
      confirmationActionColor: '',
      awsBaseUrl: process.env.VUE_APP_S3_BASE_URL,
      selectedCount: 1
    }
  },

  watch: {
    options: {
      handler() {
        if (this.shouldFetchData) {
          this.fetchData()
        }
      },
      deep: true
    },

    formattedDate() {
      this.updateSelectedCount()
    },
    filterAssignedTo() {
      this.updateSelectedCount()
    },
    filterPayment() {
      this.updateSelectedCount()
    },
    selectedCompany() {
      this.updateSelectedCount()
    },
    selectedCompanyBranch() {
      this.updateSelectedCount()
    },
    selectedTaskStatus() {
      this.updateSelectedCount()
    }
  },
  mounted() {
    const complaintNoParam = this.$route.query.complaintNo
    if (complaintNoParam) {
      this.showFilters = true
      this.complaintNo = complaintNoParam
    }
  },

  methods: {
    copyToClipboard(text, e) {
      e.preventDefault()
      navigator.clipboard.writeText(text)
      this.$toast.success('Copied to clipboard!')
      return false
    },
    getPaymentStatusClassNames(status) {
      const statuses = {
        PENDING: 'text-yellow-500',
        COMPLETED: 'text-green-500',
        PO: 'text-blue-500'
      }

      return statuses[status] || 'text-gray-600'
    },
    onChangeEmployee(item) {
      this.selectedEmployee = item
      this.showEmployeeChange = true
    },

    onCancelChangeEmployee() {
      this.selectedEmployee.assignedTo = this.previousAssignedTo
      this.showEmployeeChange = false
    },

    handleInput: debounce(function () {
      this.options.page = 1
      this.fetchData()
    }, 1000),

    // Func for count total filtered count
    updateSelectedCount() {
      this.selectedCount = [
        this.formattedDate,
        this.filterAssignedTo,
        this.filterPayment,
        this.selectedCompany,
        this.selectedCompanyBranch,
        this.selectedTaskStatus
      ].filter((item) => item !== null && item !== '' && item.length > 0).length
    },
    handleChange() {
      this.handleInput()
      this.updateSelectedCount()
    },
    handleTaskChange() {
      this.handleTaskStatusChange()
      this.updateSelectedCount()
    },
    handleCompany: debounce(function () {
      this.options.page = 1
      this.$emit('search', this.searchCompany)
    }, 1000),
    async onConfirmChangeEmployee() {
      const taskId = this.selectedEmployee._id
      const currentUsecase = this.$route.params.usecase.toLowerCase()
      const newEmployeeId = this.selectedEmployee.assignedTo
      const payload = {
        assignedTo: newEmployeeId,
        acknowledged: null,
        useCase: currentUsecase
      }

      try {
        const response = await this.$axios.put(`workforce/task/${taskId}`, payload)

        if (response.data && response.data.success) {
          this.$toast.success(
            taskAssignSuccess(this.orgLang?.data.allocateTaskModule.taskTable.columns?.taskTitle)
          )
          this.showEmployeeChange = false

          const selectedTaskIndex = this.alltask.findIndex((task) => task._id === taskId)
          if (selectedTaskIndex !== -1) {
            this.$set(this.alltask[selectedTaskIndex], 'acknowledged', null)
          }
        } else {
          console.error('Error updating task:', response.data.message)
        }
      } catch (error) {
        console.error('Error updating task:', error)
        this.selectedEmployee.assignedTo = this.previousAssignedTo
      }
    },
    showUpdateConfirmationDialog() {
      this.isUpdateAction = true
      this.confirmationTitle = 'Confirm Update'
      this.confirmationMessage = `Are you sure you want to update the ${this.taskLable.task} status?`
      this.confirmationActionText = 'Update'
      this.confirmationActionColor = '#2A83FF'
      this.showConfirmationDialog = true
    },

    showDeleteConfirmationDialog() {
      this.isUpdateAction = false
      this.confirmationTitle = 'Confirm Delete'
      this.confirmationMessage = `Are you sure you want to delete the ${this.taskLable.task}?`
      this.confirmationActionText = 'Delete'
      this.confirmationActionColor = 'red'
      this.showConfirmationDialog = true
    },
    performAction() {
      if (this.isUpdateAction) {
        this.updateTaskStatus()
      } else {
        this.deleteTask()
      }
      this.showConfirmationDialog = false
    },
    selectTaskDelete(item) {
      this.selectedTask = item
      this.showDeleteConfirmationDialog()
    },

    deleteTask() {
      try {
        this.$axios
          .delete(`/workforce/task/${this.selectedTask._id}`)
          .then(() => {
            this.selectedTask = null
            this.$toast.success(
              taskDeleteSuccess(this.orgLang?.data.allocateTaskModule.taskTable.columns?.taskTitle)
            )
            this.fetchData()
          })
          .catch((error) => {
            console.error('Error deleting task:', error)
            this.$toast.error(
              taskDeleteError(this.orgLang?.data.allocateTaskModule.taskTable.columns?.taskTitle)
            )
          })
      } catch (error) {
        console.error('Error in deleteTask:', error)
      }
    },
    taskEdit(item) {
      // this.$emit("edit-task", item);
      this.editedTask = item
      this.showEditDialog = true
      // this.addTaskSwitch();
    },
    async updateTaskStatus() {
      if (this.selectedTask) {
        const id = this.selectedTask._id

        const newData = {
          taskStatus: this.selectedTask.newStatus
        }

        try {
          await this.$axios.put(`/workforce/task/${id}`, newData)
          this.$toast.success(
            taskStatusSuccess(this.orgLang?.data.allocateTaskModule.taskTable.columns?.taskTitle)
          )

          const updatedTaskIndex = this.alltask.findIndex(
            (task) => task._id === this.selectedTask._id
          )
          if (updatedTaskIndex !== -1) {
            this.$set(this.alltask[updatedTaskIndex], 'taskStatus', this.selectedTask.newStatus)
          }
        } catch (error) {
          console.error('Error updating task:', error)
          this.$toast.error(
            taskStatusError(this.orgLang?.data.allocateTaskModule.taskTable.columns?.taskTitle)
          )
        } finally {
          this.showConfirmationDialog = false
        }
      }
    },
    cancelUpdate() {
      if (this.selectedTask) {
        this.selectedTask.newStatus = null
      }
      this.showConfirmationDialog = false
    },

    ChangeDate(date, format) {
      if (!date) return ''
      return dayjs(date).format(format)
    },
    filterParams() {
      const params = {}
      const { sortBy, sortDesc } = this.options
      const currentUsecase = this.$route.params.usecase.toLowerCase()
      if (!this.complaintNo) {
        params.useCase = currentUsecase
        if (this.selectedTaskStatus && this.selectedTaskStatus.length > 0) {
          params.taskStatus = this.selectedTaskStatus.join(',')
        }
      }

      if (this.isComplaintsEnabled) {
        params.show = '*'
      }
      if (sortBy.length > 0) {
        params.sortBy = sortBy[0]
      }
      if (sortDesc.length > 0) {
        params.sortOrder = sortDesc[0] ? 'desc' : 'asc'
      }

      if (this.selectedStartDate && this.selectedEndDate) {
        params.start = this.selectedStartDate
        params.end = this.selectedEndDate
      }

      if (this.filterPayment) {
        params.paymentStatus = this.filterPayment
        params.complaintNo = true
      }
      if (this.filterAssignedTo) {
        params.assignedTo = this.filterAssignedTo
      }
      if (this.complaintNo) {
        params.complaintNo = this.complaintNo
      }

      if (this.modelNo) {
        params.productSearch = this.modelNo
      }
      if (this.selectedCompanyBranch.length && this.selectedCompany) {
        params.selectedBranch = this.selectedCompanyBranch.join(',')
      }
      return params
    },
    async fetchData() {
      try {
        const { page, itemsPerPage } = this.options
        const filterParams = this.filterParams()
        const params = {
          page: page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalTasks,
          ...filterParams
        }
        this.loadTable = true
        const response = await this.$axios.get('/workforce/task/org', {
          params: params
        })

        this.alltask = response.data.tasks
        this.totalTasks = response.data.pagination.totalCount
        this.loadTable = false
      } catch (error) {
        console.error('Error fetching tasks:', error)
        this.loadTable = false
      }
    },
    prepareCheckInData(dataCopy) {
      return dataCopy.map((item, index) => {
        const imageLink = item.additionalInfo?.selfie
          ? `${this.awsBaseUrl}${item.additionalInfo.selfie.doc_physical_path}`
          : ''
        const audioLink = item.additionalInfo?.audio
          ? `${this.awsBaseUrl}${item.additionalInfo.audio.doc_physical_path}`
          : ''
        let rowData
        if (this.$route.params.usecase === 'default' || this.$route.params.usecase === 'project') {
          rowData = {
            'S.No': index + 1,
            [`${this.$props.useCaseTaskConfig.taskTable.columns.taskTitle}`]: `${
              item?.taskTitle || ''
            }`,
            [`${this.$props.useCaseTaskConfig.taskTable.columns.leadTitle}`]: `${item?.leadDetails?.leadTitle}`,
            'Appointment Start Date/Time': this.$dayjs(item.taskStartDateTime).format(
              'DD/MM/YYYY hh:mm A'
            ),
            'Appointment End Date/Time': this.$dayjs(item.taskEndDateTime).format(
              'DD/MM/YYYY hh:mm A'
            ),
            'Assigned To': `${item?.assignedTo?.firstName || ''} ${
              item?.assignedTo?.lastName || ''
            }`,
            'Task Status': `${item?.taskStatus || ''}`,
            'Meeting Notes': `${item.meetingDetails?.meetingNotes || ''}`,
            'Manager Remarks': `${item.meetingDetails?.managerRemarks || ''}`,
            'Image Link': imageLink,
            'Audio Link': audioLink
          }
        } else if (this.$route.params.usecase === 'inventory') {
          const customerAddress =
            item.leadDetails.company?.companyBranches.find(
              (cb) => cb._id === item.selectedBranch
            ) || ''
          rowData = {
            'S.No': index + 1,
            'Product Serial Number': item.leadDetails.leadTitle,
            Customer: `${item?.leadDetails.company?.name || ''}`,
            'Customer Address': `${customerAddress?.address?.addressLine1 || ''}`,
            'Complaint Type': `${item?.taskTitle || ''}`,
            'Complaint / Complaint No.': `${item?.complaintDetails?.complainSrNo}`,
            'Complaint Date': this.ChangeDate(item.complaintDetails?.createdAt, 'DD-MM-YYYY'),
            'Complaint Status': `${item?.complaintDetails?.status || ''}`,
            TAT: this.calculateTAT(item),
            'Product / Model No.': `${item.leadDetails?.productName?.productName || ''} ${
              item.leadDetails?.productModelNumber || ''
            }`,
            'Visit Date': `${
              this.$dayjs(item.meetingDetails?.createdAt).format('DD/MM/YYYY') || ''
            }`,
            Warranty: `${item.leadDetails?.productWarrantyStatus?.split('_').join(' ') || ''}`,
            'AMC Status': `${item.leadDetails?.productHaveAmcPlan ? 'AMC Active' : 'AMC InActive'}`,
            'Part Replacement': `${item.complaintDetails?.requirePartsReplacement ? 'Yes' : 'No'}`,
            'Request Id / Request Status': this.getRequestId(item),
            'Request Comment': this.getRequestComment(item),
            'Order No(s).': this.extractOrderNumbers(item.complaintDetails?.orderDetails),
            Payment: `${item.complaintDetails?.paymentStatus || ''}`,
            Remarks: `${item.meetingDetails?.managerRemarks || ''}`,
            'Assigned To': `${item?.assignedTo?.firstName || ''} ${
              item?.assignedTo?.lastName || ''
            }`,
            'Complaint Action': `${item?.taskStatus || ''}`,
            'Meeting Notes': `${item.meetingDetails?.meetingNotes || ''}`,
            'Image Link': imageLink,
            'Audio Link': audioLink
          }
        } else if (this.$route.params.usecase === 'collection') {
          rowData = {
            'S.No': index + 1,
            [`${this.$props.useCaseTaskConfig.taskTable.columns.taskTitle}`]: `${
              item?.taskTitle || ''
            }`,
            Bucket: item.leadDetails?.leadDescription.split(',')[2] || '',
            [`${this.$props.useCaseTaskConfig.taskTable.columns.leadTitle}`]: `${item?.leadDetails?.leadTitle}`,
            [`${this.$props.useCaseTaskConfig.taskTable.columns.tad}`]: item.tda,
            [`${this.$props.useCaseTaskConfig.taskTable.columns.currentBalance}`]:
              item.currentBalance,
            'Appointment Start Date/Time': this.$dayjs(item.taskStartDateTime).format(
              'DD/MM/YYYY hh:mm A'
            ),
            'Appointment End Date/Time': this.$dayjs(item.taskEndDateTime).format(
              'DD/MM/YYYY hh:mm A'
            ),
            'Assigned To': `${item?.assignedTo?.firstName || ''} ${
              item?.assignedTo?.lastName || ''
            }`,
            [`${this.$props.useCaseTaskConfig.taskTable.columns.taskTitle} Status`]: `${
              item?.taskStatus || ''
            }`,
            'Meeting Outcome/Status': `${item.meetingDetails?.meetingOutCome || ''}`,
            'Fund Collected': `${item.meetingDetails?.fundCollected || ''}`,
            'Next Schedule Date': `${
              item.meetingDetails?.nextTaskScheduledOn
                ? this.$dayjs(item.meetingDetails?.nextTaskScheduledOn).format('DD/MM/YYYY')
                : ''
            }`,
            'Meeting Notes': `${item.meetingDetails?.meetingNotes || ''}`,
            'Manager Remarks': `${item.meetingDetails?.managerRemarks || ''}`,
            'Image Link': imageLink,
            'Audio Link': audioLink
          }
        }

        item.meetingDetails?.questionnaire?.forEach((question) => {
          if (Array.isArray(question.userResponse)) {
            rowData[question.question] = question.userResponse.join(', ')
          } else {
            rowData[question.question] = question.userResponse
          }
        })

        return rowData
      })
    },
    async exportDataToXLSX() {
      try {
        const startDate = this.selectedStartDate
        const endDate = this.selectedEndDate
        const filterParams = this.filterParams()
        const chunkSize = 100
        this.exportLoading = true
        const totalPages = this.totalTasks / chunkSize

        const promises = []

        for (let index = 0; index < totalPages; index++) {
          const params = {
            start: startDate,
            end: endDate,
            limit: chunkSize,
            page: index + 1,
            ...filterParams
          }
          const promise = this.$axios.get('/workforce/task/org', {
            params
          })
          promises.push(promise)
        }

        const responses = await Promise.all(promises)

        const data = responses.reduce((acc, response) => {
          return acc.concat(response.data.tasks)
        }, [])
        const XLSX = require('xlsx')
        const dataCopy = [...data]
        const restructuredData = this.prepareCheckInData(dataCopy)
        const ws = XLSX.utils.json_to_sheet(restructuredData)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Task Data')
        const file_date = startDate || new Date().toISOString()
        const filename = `${this.useCaseTaskConfig?.tabs?.tasks}_${file_date}.xlsx`
        XLSX.writeFile(wb, filename)
      } catch (error) {
        console.error('Error exporting data:', error)
      } finally {
        this.exportLoading = false
      }
    },
    calculateTAT(item) {
      const taskStart = item.taskStartDateTime
      const taskEnd = item.actualEndDateTime
      if (taskEnd) {
        return dayHourFormat(taskStart, taskEnd)
      }
      return ''
    },
    handleStatusChange(item, status) {
      this.showUpdateConfirmationDialog()
      this.selectedTask = item
      this.selectedTask.newStatus = status
      this.showConfirmationDialog = true
    },

    updateselectedTask(item) {
      this.selectedTask = item
      this.previousLeadStatus = item.leadStatus
      this.showConfirmationDialog = true
    },
    onDateSelected() {
      if (this.dates.length === 2) {
        this.selectedStartDate = this.formatDate(this.dates[0])
        this.selectedEndDate = this.formatDate(this.dates[1])
      } else if (this.dates.length === 0) {
        this.selectedStartDate = null
        this.selectedEndDate = null
      }
      this.options.page = 1
      this.fetchData()
    },
    onDateCleared() {
      this.dates = []
      this.selectedStartDate = null
      this.selectedEndDate = null
      this.fetchData()
    },
    formatDate(dateString) {
      return this.$dayjs(dateString).format('YYYY-MM-DD')
    },
    visibleDate(date) {
      return this.$dayjs(date).format('DD-MM-YYYY')
    },
    handleTaskStatusChange() {
      if (this.selectedTaskStatus.length > 0) {
        this.shouldFetchData = true
      }
      if (this.shouldFetchData) {
        this.options.page = 1
        this.fetchData()
      }
    },
    toggleSelectAll() {
      this.$nextTick(() => {
        if (this.allTaskStatusSelected) {
          this.options.page = 1
          this.selectedTaskStatus = []
          this.alltask = []
          this.totalTasks = 0
          this.shouldFetchData = false
        } else {
          this.selectedTaskStatus = this.taskSteps.map((step) => step.key)
        }
        this.handleTaskStatusChange()
      })
    },
    toggleSelectAllAddress() {
      this.$nextTick(() => {
        if (this.allBranchSelected) {
          this.options.page = 1
          this.selectedCompanyBranch = []
        } else {
          this.selectedCompanyBranch = this.selectedCompanyBranches.map((branch) => branch.value)
        }
      })
      this.handleInput()
    },

    selectTask(task) {
      this.selectedTask = task
      const baseUrl = process.env.VUE_APP_S3_BASE_URL
      this.imagesData = task.meetingDetails?.attachments?.map((el) => ({
        src: baseUrl + el.filePath,
        crossorigin: 'anonymous'
      }))
      this.showTaskDataDialog = true
    },
    onStatusCleared() {
      this.options.page = 1
      this.alltask = []
      this.totalTasks = 0
      this.shouldFetchData = false
    },
    extractOrderNumbers(item) {
      return item?.map((element) => element?.orderSrNo).join(', ')
    },
    getRequestStatus(item) {
      return (
        item.complaintDetails?.linkedRequestId?.requestStatus ||
        item.linkedRequestId?.requestStatus ||
        '—'
      )
    },

    getRequestComment(item) {
      return (
        item.complaintDetails?.linkedRequestId?.comments?.slice(-1)?.[0]?.comment ||
        item.linkedRequestId?.comments?.slice(-1)?.[0]?.comment ||
        '—'
      )
    },
    getRequestId(item) {
      return (
        item.complaintDetails?.linkedRequestId?.requestId || item.linkedRequestId?.requestId || '—'
      )
    },
    onCreateTask() {
      this.fetchData()
      this.showEditDialog = false
    },
    getCompanyBranch(branchId, details) {
      const selectedBranch = details.company?.companyBranches.find(
        (branch) => branch._id === branchId
      )
      if (selectedBranch) {
        const { city, state } = selectedBranch.address
        const addressDetails = [city, state].filter(Boolean)
        return addressDetails.join(', ')
      }
      return ''
    },
    closeDialog() {
      this.showTaskDataDialog = false
    }
  },
  computed: {
    tableHeaders() {
      if (!this.$props.useCaseTaskConfig) return []

      const taskTitle = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.taskTitle,
        value: 'taskTitle'
      }
      const ComplaintTitle = {
        text: `${this.$props.useCaseTaskConfig.taskTable.columns?.taskTitle} / Complaint No.`,
        value: 'taskTitle',
        sortable: false,
        width: '200px'
      }
      const leadTitle = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.leadTitle,
        value: 'leadTitle'
      }
      const tad = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.tad,
        value: 'tda'
      }
      const curr_bal = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.currentBalance,
        value: 'currentBalance'
      }
      const taskStartTime = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.startDate,
        value: 'taskStartDateTime'
      }
      const taskEndTime = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.endDate,
        value: 'taskEndDateTime'
      }
      const taskAssignedTo = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.assignedTo,
        value: 'assignedTo',
        sortable: false
      }
      const taskCompanyName = {
        text: 'Customer',
        value: 'companyName',
        sortable: false,
        width: '200px'
      }
      const productName = {
        text: 'Product / Model No.',
        value: 'productName',
        sortable: false,
        width: '200px'
      }
      const complaintLogDate = {
        text: 'Complaint Logged On',
        value: 'complaintLogDate',
        sortable: false,
        width: '150px'
      }
      const complaintCloseDate = {
        text: 'Complaint Closed On',
        value: 'complaintCloseDate',
        sortable: false,
        width: '150px'
      }
      const productWarranty = {
        text: 'Warranty',
        value: 'productWarranty',
        sortable: false,
        width: '150px'
      }
      const productAmc = {
        text: 'AMC Status',
        value: 'productAmc',
        sortable: false,
        width: '150px'
      }
      const partReplacement = {
        text: 'Part Replacement',
        value: 'partReplacement',
        sortable: false
      }
      const requestId = {
        text: 'Request Id / Request Status',
        value: 'requestId',
        sortable: false,
        width: '150px'
      }
      const requestComment = {
        text: 'Request Comment',
        value: 'requestComment',
        sortable: false,
        width: '150px'
      }
      const allOrders = {
        text: 'Order No(s).',
        value: 'allOrders',
        sortable: false,
        width: '150px'
      }
      const paymentStatus = {
        text: 'Payment',
        value: 'paymentStatus',
        sortable: false,
        width: '200px'
      }
      const complaintStatus = {
        text: 'Complaint Status',
        value: 'complaintStatus',
        sortable: false,
        width: '150px'
      }
      const taskStatus = {
        text: this.$props.useCaseTaskConfig.taskTable.columns?.taskStatus,
        value: 'taskStatus',
        sortable: false
      }
      const complaintAction = {
        text: 'Complaint Action',
        value: 'taskStatus',
        sortable: false
      }
      const managerRemarks = {
        text: 'Remarks',
        value: 'managerRemarks',
        sortable: false,
        width: '200px'
      }
      const serviceReportNumber = {
        text: 'Report No.',
        value: 'serviceReportNumber',
        sortable: false,
        width: '150px'
      }
      const acknowledged = {
        text: 'Acknowledged',
        value: 'acknowledged',
        sortable: false,
        width: '150px'
      }
      const tat = {
        text: 'TAT',
        value: 'tat',
        sortable: false,
        width: '120px'
      }
      const tableHeaders = [
        ...(this.isComplaintsEnabled ? [taskCompanyName] : []),
        ...(this.isComplaintsEnabled ? [ComplaintTitle] : [taskTitle]),
        ...(this.isComplaintsEnabled ? [complaintLogDate] : []),
        ...(this.isComplaintsEnabled ? [complaintCloseDate] : []),
        ...(this.isComplaintsEnabled ? [tat] : []),
        ...(this.isComplaintsEnabled ? [complaintStatus] : []),
        ...(this.isComplaintsEnabled ? [productName] : [leadTitle]),
        ...(this.isComplaintsEnabled ? [productWarranty] : []),
        ...(this.isComplaintsEnabled ? [productAmc] : []),
        ...(this.isComplaintsEnabled ? [partReplacement] : []),
        ...(this.isComplaintsEnabled ? [requestId] : []),
        ...(this.isComplaintsEnabled ? [requestComment] : []),
        ...(this.isComplaintsEnabled ? [allOrders] : []),
        ...(this.isComplaintsEnabled ? [serviceReportNumber] : []),
        ...(this.isComplaintsEnabled ? [paymentStatus] : []),
        ...(this.isComplaintsEnabled ? [managerRemarks] : []),
        ...(this.isCollectionsEnabled ? [tad] : []),
        ...(this.isCollectionsEnabled ? [curr_bal] : []),
        ...(this.isComplaintsEnabled ? [] : [taskStartTime]),

        ...(this.isComplaintsEnabled ? [] : [taskEndTime]),
        ...(this.isComplaintsEnabled ? [taskAssignedTo] : [taskAssignedTo]),
        ...(this.isComplaintsEnabled ? [acknowledged] : [acknowledged]),
        ...(this.isComplaintsEnabled ? [complaintAction] : [taskStatus]),

        this.canDelete || this.canEdit
          ? {
              text: this.$props.useCaseTaskConfig.taskTable.columns?.actions,
              value: 'action',
              sortable: false
            }
          : []
      ]

      return tableHeaders
    },
    tasks() {
      return this.alltask
    },
    getStatusColor() {
      return (taskStatus) => {
        const colorInfo = this.taskStepColors[taskStatus]
        return {
          color: colorInfo ? colorInfo.textColor : 'black',
          backgroundColor: colorInfo ? colorInfo.bgColor : 'white',
          height: '39px',
          borderRadius: '20px',
          padding: '5px 10px',
          fontSize: '14px',
          boxShadow: 'none',
          border: '1px solid transparent'
        }
      }
    },
    formattedDate() {
      if (this.dates.length === 2) {
        const startDate = this.visibleDate(this.dates[0])
        const endDate = this.visibleDate(this.dates[1])
        return `${startDate} to ${endDate}`
      } else if (this.dates.length === 1) {
        return this.visibleDate(this.dates[0])
      }
      return ''
    },
    companyNames() {
      const options = []

      this.companies.forEach((company) => {
        options.push({
          text: `${company.name}`,
          value: company._id
        })
      })

      return sortDropdownOptions(options)
    },
    selectedCompanyBranches() {
      const selectedCompany = this.companies.find(
        (company) => company._id === this.selectedCompany?.value
      )
      const options = selectedCompany?.companyBranches.map((branch) => {
        return {
          text: `[ Alias - ${branch.alias || ''} ] - ${branch.address.addressLine1}`,
          value: branch._id
        }
      })
      return options
    },
    taskStepsSelect() {
      const taskStepsCopy = [...this.taskSteps]
      return taskStepsCopy
    },

    allTaskStatusSelected() {
      return this.selectedTaskStatus.length === this.taskSteps.length
    },
    someTaskStatusSelected() {
      return this.selectedTaskStatus.length > 0 && !this.allTaskStatusSelected
    },
    icon() {
      if (this.allTaskStatusSelected) return 'mdi-close-box'
      if (this.someTaskStatusSelected) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },
    allBranchSelected() {
      return this.selectedCompanyBranches?.length === this.selectedCompanyBranch?.length
    },
    someBranchSelected() {
      return this.selectedCompanyBranch.length > 0 && !this.allBranchSelected
    },
    branchSelectIcon() {
      if (this.allBranchSelected) return 'mdi-close-box'
      if (this.someBranchSelected) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },
    employeeChangePopupContent() {
      return `${this.selectedEmployee?.taskTitle} to ${
        this.users.find((item) => item._id === this.selectedEmployee?.assignedTo)?.full_name
      }`
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.taskEdit)
    },
    haveInventoryAccess() {
      return hasPermission(this.userData, permissionData.inventoryRead)
    },
    hideFilters() {
      if (
        this.$route.query?.complaint_no &&
        !hasPermission(this.userData, permissionData.leadRead)
      ) {
        return this.canEdit && this.haveInventoryAccess && this.$route.query?.complaint_no
      }
      return false
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.taskDelete)
    },
    taskLable() {
      return {
        task: this.useCaseTaskConfig?.taskTable.columns?.taskTitle,
        taskTitle: this.useCaseTaskConfig?.addTaskForm.taskTitle,
        description: this.useCaseTaskConfig?.addTaskForm.description
      }
    },
    isComplaintsEnabled() {
      if (this.$route.params.usecase === 'inventory') {
        return true
      }
      return false
    },
    formattedPaymentDetails() {
      return (complaintDetails) => {
        let prefix = ''
        if (complaintDetails?.poNumber) {
          prefix += 'PO No: ' + complaintDetails.poNumber
        }
        if (complaintDetails?.paymentDetails?.paymentReceived) {
          if (prefix) {
            prefix += ' | '
          }
          prefix += 'Amount: ' + complaintDetails.paymentDetails.paymentReceived
        }
        if (complaintDetails?.msrNumber) {
          if (prefix) {
            prefix += ' | '
          }
          prefix += 'MSR No: ' + complaintDetails.msrNumber
        }
        return prefix
      }
    },
    populateUser() {
      return this.users.filter((el) => el._id)
    }
  }
}
</script>
<style scoped>
.v-data-table-header th span {
  font-size: 14px;
  color: #262626;
  font-weight: 400;
}

.td_font {
  font-weight: 700;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.chip-width {
  width: 80px;
  overflow: hidden;
}

.branch-chip-width {
  width: 180px;
  overflow: hidden;
}

.chip-text {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fixed-width-column {
  width: 190px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.select-all-item {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}

.td-ellipsis {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* .v-menu__content {
  box-shadow: none !important;
} */

.v-menu__content {
  border-radius: 10px;
}

.crossBtn {
  background: #f3f4f6;
  display: flex;
  flex-wrap: wrap;
  padding-right: 1.5rem;
  font-size: 1.5rem;
}

.custom-text-field >>> .v-text-field__details {
  display: none !important;
}

.custom-text-field >>> .v-input__slot {
  margin-bottom: 0 !important;
}
</style>
