<template>
  <div>
    <div
      class="justify-end items-center rounded-lg mt-2 mb-5 cursor-pointer"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
    >
      <div
        class="items-center justify-end mb-0"
        :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']"
      >
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="leadTitleFilter"
            :label="`${leadLable.leadTitle}/Bill No.`"
            :placeholder="`${leadLable.leadTitle}`"
            prepend-inner-icon="mdi-magnify"
            persistent-hint
            solo
            flat
            clearable
            @input="handleInput"
            class="custom-text-field"
            outlined
            dense
          ></v-text-field>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
          v-if="!isComplaintsEnabled"
        >
          <v-select
            :label="`${leadLable.leadTitle} Status`"
            placeholder="`${leadLable.leadTitle} Status`"
            v-model="selectedLeadStatus"
            :items="leadStepsSelect"
            item-text="value"
            item-value="key"
            prepend-inner-icon="mdi-magnify"
            hide-details
            solo
            flat
            multiple
            small-chips
            clearable
            @click:clear="onStatusCleared"
            @change="handleLeadStatusChange"
            outlined
            dense
          >
            <!-- Add a tile with Select All as Label -->
            <v-list-item class="pl-4 select-all-item" slot="prepend-item" @click="toggleSelectAll">
              <v-list-item-action>
                <v-icon :color="selectedLeadStatus.length > 0 ? 'blue darken-2' : ''">{{
                  icon
                }}</v-icon>
              </v-list-item-action>
              <v-list-item-title>Select All</v-list-item-title>
            </v-list-item>
            <v-divider slot="prepend-item" />

            <template v-slot:selection="{ item, index }">
              <v-chip small class="chip-width" v-if="index === 0">
                <span class="chip-text">{{ item.value }}</span>
              </v-chip>
              <span v-if="index === 1" class="grey--text text-caption">
                (+{{ selectedLeadStatus.length - 1 }}
                more)
              </span>
            </template>
          </v-select>
        </v-col>

        <!-- <div
          class="justify-end mb-0 items-center"
          :class="[$vuetify.breakpoint.lgAndUp ? 'inline-flex' : 'inline-flex']"
        >
          <v-tooltip top>
            <template #activator="{ on, attrs }">
              <v-btn
                :color="$vuetify.theme.currentTheme.primary"
                :loading="exportLoading"
                :disabled="exportLoading"
                v-if="isComplaintsEnabled"
                class="white--text mr-4 ml-4"
                @click="exportDataToXLSX"
                v-bind="attrs"
                v-on="on"
              >
                Export
                <v-icon right dark> mdi-download </v-icon>
              </v-btn>
            </template>
            <span>Export Tasks</span>
          </v-tooltip>
          <FilterPopOver :selectedCount="selectedCount">
            <v-card outlined class="w-72 max-h-96">
              <v-list>
                <v-list-item class="px-0">
                  <v-col class="pb-0 pt-0">
                    <v-combobox
                      v-model="selectedCompany"
                      :items="companyNames"
                      :label="leadLable.companyName"
                      :placeholder="leadLable.companyName"
                      :search-input.sync="searchCompany"
                      hide-details
                      outlined
                      dense
                      clearable
                      class="custom-input .custom-text-field"
                      @input="handleInput"
                      @update:search-input="handleCompany"
                      @change="updateSelectedCount"
                    >
                    </v-combobox>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0">
                  <v-col class="pb-0">
                    <v-autocomplete
                      v-model="selectedCompanyBranch"
                      :items="selectedCompanyBranches"
                      label="Customer Address"
                      placeholder="Customer Address"
                      :disabled="!selectedCompany?.value"
                      hide-details
                      outlined
                      dense
                      clearable
                      class="custom-input .custom-text-field"
                      @change="handleChange"
                    >
                    </v-autocomplete>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="isComplaintsEnabled">
                  <v-col class="pb-0">
                    <v-select
                      v-model="productWarrantyStatus"
                      label="Warranty Status"
                      placeholder="Warranty Status"
                      :items="warrantyOptions"
                      item-text="warranty"
                      item-value="warranty"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="handleChange"
                      class=".custom-text-field"
                    ></v-select>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="isComplaintsEnabled">
                  <v-col class="pb-0">
                    <v-select
                      v-model="haveAmcPlan"
                      label="AMC Status"
                      placeholder="AMC Status"
                      :items="[
                        { text: 'AMC Active', value: true },
                        { text: 'AMC Inactive', value: false },
                      ]"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="handleChange"
                    ></v-select>
                  </v-col>
                </v-list-item>
              </v-list>
            </v-card>
          </FilterPopOver>
          <UploadFiles
            :upload-url="uploadUrl"
            :download-data="leadsDataFormat"
            :file-name="'leadDummyData'"
            :useCase="useCaseLeadConfig"
            @updateUploadData="bulkFileUpload"
          />
        </div> -->
      </div>
    </div>

    <div>
      <div></div>

      <div class="overflow-x-auto overflow-y-auto">
        <v-data-table
          class="pl-7 pr-7 pt-4 rounded-lg"
          :headers="tableHeaders"
          :items="leads"
          item-key="_id"
          :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
          :options.sync="options"
          :server-items-length="totalLeads"
          :loading="loadTable"
          fixed-header
        >
          <template v-slot:item.leadTitle="{ item }">
            <td
              @contextmenu="(e) => copyToClipboard(item.leadTitle, e)"
              class="px-4 py-5 hover:text-blue-700 hover:dark-hover-text cursor-pointer td_font"
              @click="toggleLeadDetails(item)"
            >
              <div class="flex">
                <div class="ellipsis_class">
                  <v-tooltip top>
                    <template v-slot:activator="{ on }">
                      <span v-on="on">{{ item.leadTitle }}</span>

                      <p
                        :class="{ highlighted: isSystemGenLeadTitle(item) }"
                        class="text-xs mb-0"
                        v-if="isSystemGenLeadTitle(item)"
                      >
                        (System Generated)
                      </p>
                    </template>
                    <span>{{ item.leadTitle }}</span>
                  </v-tooltip>
                </div>
                <span class="text-gray-500 ml-1"> ({{ item.tasks?.length }}) </span>
              </div>
            </td>
          </template>
          <template v-slot:item.productName="{ item }">
            <td class="px-4 py-5 td_font">
              {{ item.productName?.productName }}
            </td>
          </template>
          <template v-slot:item.productMaker="{ item }">
            <td class="px-4 py-5 td_font">
              {{ item.productManufacturer?.partnerName }}
            </td>
          </template>
          <template v-slot:item.modelNumber="{ item }">
            <td class="px-4 py-5 td_font">
              {{ item.productModelNumber }}
            </td>
          </template>
          <template v-slot:item.primaryContact="{ item }">
            <td class="px-4 py-5 td_font">
              {{ findPrimaryContact(item).firstName }}
              {{ findPrimaryContact(item).lastName }}
              <br />
              {{ findPrimaryContact(item).phoneNumber }}
            </td>
          </template>
          <template v-slot:item.warrantyStatus="{ item }">
            <td
              :class="
                item.productWarrantyStatus === 'IN_WARRANTY' ? 'text-green-500' : 'text-yellow-500'
              "
              class="px-4 py-5 font-semibold"
            >
              {{
                item.productWarrantyStatus ? item.productWarrantyStatus?.split('_').join(' ') : '—'
              }}
            </td>
          </template>
          <template v-slot:item.amcStatus="{ item }">
            <td
              :class="item.productHaveAmcPlan ? 'text-green-500' : 'text-red-500'"
              class="px-4 py-5 font-semibold"
            >
              {{ getAmcStatusText(item) }}
            </td>
          </template>
          <template v-slot:item.amcPlan="{ item }">
            <td
              class="px-4 py-5 hover:text-blue-700 dark-hover-text cursor-pointer td_font"
              @click="navToAmc"
            >
              {{ item.activeAmc?.amcPlan?.planName }}
            </td>
          </template>
          <template v-slot:item.sourceOfLead="{ item }">
            <td class="px-4 py-5 td_font">
              {{ item.sourceOfLead }}
            </td>
          </template>
          <template v-slot:item.tags="{ item }">
            <td class="px-1 py-5 td_font">
              <v-chip
                label
                small
                class="mx-1 my-1"
                :color="colors(item, i)"
                v-for="(el, i) in item.tags"
                :key="i"
              >
                {{ el }}
              </v-chip>
            </td>
          </template>
          <template v-slot:item.company="{ item }" v-if="isCollectionsEnabled">
            <td class="px-4 py-5 td_font">
              {{ item.company?.companyBranches[0]?.alias }}
              <br />
              ({{ item.company?.name }})
            </td>
          </template>
          <template v-slot:item.company="{ item }" v-else>
            <td class="px-4 py-5 td_font">
              {{ item.company?.name }}
            </td>
          </template>
          <template v-slot:item.bktCollect="{ item }" v-if="isCollectionsEnabled">
            <td class="px-4 py-5 td_font">
              {{ item.leadDescription?.split(',')[2] || '-' }}
            </td>
          </template>
          <template v-slot:item.posAmt="{ item }" v-if="isCollectionsEnabled">
            <td class="px-4 py-5 td_font">
              {{ item.tasks[0]?.tda }}
            </td>
          </template>
          <template v-slot:item.emiAmt="{ item }" v-if="isCollectionsEnabled">
            <td class="px-4 py-5 td_font">
              {{ item.leadDescription?.split(',')[1] || '-' }}
            </td>
          </template>
          <template v-slot:item.location="{ item }">
            <td class="px-4 py-5 td_font">
              {{ getBranchName(item) }}
            </td>
          </template>
          <template v-slot:item.description="{ item }">
            <td class="px-4 py-5 td_font">
              <div class="ellipsis_class">
                <v-tooltip top>
                  <template v-slot:activator="{ on }">
                    <span v-on="on">
                      {{ item.leadDescription ? item.leadDescription?.split(',')[0] : '-' }}</span
                    >
                  </template>
                  <span>
                    {{ item.leadDescription ? item.leadDescription?.split(',')[0] : '' }}</span
                  >
                </v-tooltip>
              </div>
            </td>
          </template>
          <!-- <template v-slot:item.assignedTo="{ item }">
            <td class="px-4 py-5 td_font">
              <employee-info :item="item.assignedTo" />
            </td>
          </template> -->

          <template v-slot:item.assignedTo="{ item }">
            <td class="px-2 td_font">
              <v-select
                solo
                flat
                dense
                hide-selected
                :menu-props="{ offsetY: true }"
                class="fixed-width-column mt-5"
                v-model="item.assignedTo"
                :items="users"
                item-value="_id"
                @click="previousAssignedTo = item.assignedTo"
                @change="updateSelectedLead(item)"
                :readonly="!canEdit"
              >
                <template v-slot:selection="{ item }">
                  <employee-info :item="item"></employee-info>
                </template>
                <template v-slot:item="{ item }">
                  <employee-info :item="item"></employee-info>
                </template>
              </v-select>
            </td>
          </template>
          <template v-slot:item.status="{ item }">
            <td class="w-48 td_font">
              <v-menu offset-y :disabled="!canEdit">
                <template v-slot:activator="{ on }">
                  <div>
                    <v-btn
                      v-on="on"
                      :style="getStatusColor(item.leadStatus)"
                      class="text-capitalize"
                    >
                      <v-tooltip top>
                        <template v-slot:activator="{ on }">
                          <span class="ellipsis_class" v-on="on">
                            {{ leadSteps.find((step) => step.key === item.leadStatus)?.value }}
                          </span>
                        </template>
                        <span>{{
                          leadSteps.find((step) => step.key === item.leadStatus)?.value
                        }}</span>
                      </v-tooltip>

                      <v-icon right dark> mdi-chevron-down </v-icon>
                    </v-btn>
                  </div>
                </template>
                <v-list
                  style="
                    max-height: 200px;
                    overflow-y: auto;
                    width: 250px;
                    border-radius: 20px;
                    text-align: center;
                  "
                >
                  <v-list-item
                    v-for="status in leadSteps"
                    :key="status.key"
                    @click="handleStatusChange(item, status.key)"
                  >
                    <v-list-item-content :style="getStatusColor(status.key)">
                      <span style="white-space: nowrap; font-size: 14px">{{ status?.value }}</span>
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-menu>
            </td>
          </template>
          <template v-slot:item.actions="{ item }">
            <td class="px-4 py-5 td_font">
              <action-button
                :item="item"
                :canEdit="canEdit"
                :canWrite="canEdit"
                :showEditButton="canEdit"
                :showDeleteButton="canDelete"
                :canDelete="canDelete"
                :addTaskAction="() => addTaskAction(item)"
                :handleEdit="() => handleEdit(item)"
                :handleDelete="() => handleDelete(item)"
                :showAddTaskButton="canEdit"
              />
            </td>
          </template>
        </v-data-table>
      </div>
    </div>
    <v-dialog v-model="showConfirmationDialog" max-width="600">
      <v-card>
        <v-card-title class="text-h5 justify-center">
          {{ confirmationTitle }}
        </v-card-title>
        <v-card-text class="text-center">
          {{ confirmationMessage }}
        </v-card-text>
        <v-card-actions class="flex justify-center mb-2">
          <v-btn outlined color="grey" class="rounded-lg mr-4 px-6" @click="cancelUpdate">
            <span class="text-black">Cancel</span>
          </v-btn>
          <v-btn
            :color="confirmationActionColor"
            class="white--text rounded-lg px-6"
            @click="performAction"
          >
            {{ confirmationActionText }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog
      v-model="showLeadDetails"
      width="54rem"
      content-class="custom-dialog"
      @click:outside="toggleLeadDetails"
    >
      <LeadDetails
        v-if="showLeadDetails"
        :leadProp="leadProp"
        :leadId="leadId"
        :onUpdate="onUpdate"
        :toggleLeadDetails="toggleLeadDetails"
      />
    </v-dialog>
    <v-dialog v-model="showCreateTaskDialog" width="auto" persistent>
      <div>
        <create-task
          :onCreate="onCreateTask"
          :closeModal="() => (showCreateTaskDialog = false)"
          :selectedLead="selectedLead"
          :canSetFrequency="false"
        />
      </div>
    </v-dialog>
  </div>
</template>
<script>
import EmployeeInfo from '@/components/EmployeeInfo.vue'
import LeadDetails from '@/components/Workforce/LeadManagement/LeadDetails'
import CreateTask from './CreateTask'
import UploadFiles from './uploadFile'
import ActionButton from '@/components/ActionButton'
import { debounce, hasPermission, isModuleVisible } from '@/utils/common'
import { demoLeadsData } from '@/utils/demoFormat'
import permissionData from '@/utils/permissions'
import { sortDropdownOptions, downloadExcel, getFullAddress } from '@/utils/common'
import {
  leadStatusSuccess,
  leadStatusError,
  leadDeleteSuccess,
  leadDeleteError
} from '@/utils/toastMessages'
import { leadStatusColors } from '@/utils/workforce/statusColors'
import FilterPopOver from '@/components/FilterPopOver.vue'
export default {
  layout: 'Workforce/default',
  name: 'LeadsTable',
  components: {
    LeadDetails,
    EmployeeInfo,
    CreateTask,
    // UploadFiles,
    ActionButton
    // FilterPopOver
  },
  props: {
    useCaseLeadConfig: Object,
    useCaseTaskConfig: Object,
    leadSteps: Array,
    companies: Array,
    users: Array
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      orgCode: this.$storage.getUniversal('organization_code'),
      leadProp: {},
      leadId: '',
      totalLeads: 0,
      direction: 'left',
      options: { sortBy: [], sortDesc: [], itemsPerPage: 5, page: 1 },
      showLeadDetails: false,
      leads: [],
      selectedLeadId: null,
      selectedCompanyBranch: null,
      exportLoading: false,
      loadTable: true,
      selectedLeadStatus: [
        'INTRODUCE_PRODUCT',
        'QUALIFYING_LEAD',
        'NURTURING',
        'DELIVERING_SALES_PRESENTATION',
        'OFFER_A_FREE_TRIAL',
        'SEND_PROPOSAL',
        'NEGOTIATE_TERMS',
        'CLOSING_DEAL',
        'ONBOARDING',
        'LOST',
        'ON_HOLD'
      ],
      warrantyOptions: ['IN_WARRANTY', 'OUT_OF_WARRANTY'],
      productWarrantyStatus: null,
      haveAmcPlan: null,
      showConfirmationDialog: false,
      selectedLead: null,
      previousLeadStatus: null,
      previousAssignedTo: null,
      searchCompany: null,
      leadTitleFilter: '',
      // selectedCompany: '',
      showCreateTaskDialog: false,
      shouldFetchData: true,
      showForm: false,
      isUpdateAction: false,
      confirmationTitle: '',
      confirmationMessage: '',
      confirmationActionText: '',
      confirmationActionColor: '',
      leadsDataFormat: demoLeadsData,
      leadStepColors: leadStatusColors,
      selectedCount: 0
    }
  },
  watch: {
    '$route.params.usecase': async function (newQuery) {
      if (newQuery) {
        await this.fetchData()
      }
    },
    options: {
      handler() {
        if (this.shouldFetchData) {
          this.fetchData()
        }
      },
      deep: true
    }
    // selectedCompany() {
    //   this.updateSelectedCount()
    // },
    // selectedCompanyBranch() {
    //   this.updateSelectedCount()
    // },
    // productWarrantyStatus() {
    //   this.updateSelectedCount()
    // },
    // haveAmcPlan() {
    //   this.updateSelectedCount()
    // }
  },
  methods: {
    copyToClipboard(text, e) {
      e.preventDefault()
      navigator.clipboard.writeText(text)
      this.$toast.success('Copied to clipboard!')
      return false
    },
    toggleLeadDetails(lead) {
      this.leadId = lead._id || ''
      this.leadProp = lead || {}
      this.showLeadDetails = !this.showLeadDetails
      if (!this.showLeadDetails) {
        this.fetchData()
      }
    },

    handleInput: debounce(function () {
      this.options.page = 1
      this.fetchData()
    }, 1000),

    // Func for count total filtered count
    // updateSelectedCount() {
    //   this.selectedCount = [
    //     this.selectedCompany,
    //     this.selectedCompanyBranch,
    //     this.productWarrantyStatus,
    //     this.haveAmcPlan
    //   ].filter((item) => item !== null && item !== '').length
    // },
    // handleChange() {
    //   this.handleInput()
    //   this.updateSelectedCount()
    // },
    handleCompany: debounce(function () {
      this.options.page = 1
      this.$emit('search', this.searchCompany)
      this.fetchData()
    }, 1000),

    handleStatusChange(item, status) {
      this.showUpdateConfirmationDialog()
      this.selectedLead = item
      this.selectedLead.newStatus = status
      this.showConfirmationDialog = true
    },
    async fetchData() {
      try {
        const { sortBy, sortDesc, page, itemsPerPage } = this.options
        const currentUsecase = this.$route.params.usecase?.toLowerCase()

        const params = {
          page: page,
          limit: itemsPerPage,
          useCase: currentUsecase
        }

        if (this.isComplaintsEnabled) {
          params.skipHierarchy = true
        }
        if (this.orgCode === 'FSALES') {
          params.taskPerDay = this.$dayjs().format('YYYY-MM-DD')
        }
        if (sortBy.length > 0) {
          params.sortBy = sortBy[0]
        }
        if (sortDesc.length > 0) {
          params.sortOrder = sortDesc[0] ? 'desc' : 'asc'
        }

        if (this.leadTitleFilter) {
          params.search = this.leadTitleFilter
        }
        if (this.productWarrantyStatus) {
          params.productWarrantyStatus = this.productWarrantyStatus
        }
        params.productHaveAmcPlan = this.haveAmcPlan
        if (this.selectedCompany) {
          params.company = this.selectedCompany?.value
        }

        if (this.selectedCompanyBranch && this.selectedCompany) {
          params.selectedBranch = this.selectedCompanyBranch
        }
        // if (this.selectedLeadStatus && this.selectedLeadStatus.length > 0) {
        //   params.leadStatus = this.selectedLeadStatus.join(",");
        // }

        this.loadTable = true
        const response = await this.$axios.get('/workforce/lead/org', {
          params: params
        })

        this.leads = response.data.leads
        this.totalLeads = response.data.pagination.totalCount
        this.leads.forEach(async (lead) => {
          await this.getBranchName(lead.selectedBranch)
        })
        this.loadTable = false
      } catch (error) {
        console.error(error)
        this.loadTable = false
      }
    },
    getBranchName(lead) {
      const companyBranch = lead?.company?.companyBranches?.find(
        (el) => el._id === lead.selectedBranch
      )
      return getFullAddress(companyBranch?.address) || ''
    },

    colors(item, index) {
      const colors = ['green', 'pink', 'blue', 'cyan', 'teal', 'orange']
      const randomIndex = Math.round(index % colors.length)
      return colors[randomIndex]
    },
    async updateSelectedLead(item) {
      try {
        const id = item._id
        const currentUsecase = this.$route.params.usecase.toLowerCase()

        const params = { assignedTo: item.assignedTo, useCase: currentUsecase }

        const response = await this.$axios.put(`/workforce/lead/${id}`, params)
        this.fetchData()
        const successMessage = response?.data?.message || 'Beat has been assigned !! '
        this.$toast.success(successMessage)
      } catch (error) {
        console.log(error)
      }
    },

    async updateLeadStatus() {
      if (this.selectedLead) {
        const id = this.selectedLead._id

        const newData = {
          leadStatus: this.selectedLead.newStatus.toUpperCase().replace(/\s+/g, '_')
        }
        try {
          await this.$axios.patch(`/workforce/lead/${id}`, newData)
          this.$toast.success(leadStatusSuccess(this.leadLable.leadTitle))

          const updatedLeadIndex = this.leads.findIndex(
            (lead) => lead._id === this.selectedLead._id
          )
          if (updatedLeadIndex !== -1) {
            this.$set(this.leads[updatedLeadIndex], 'leadStatus', this.selectedLead.newStatus)
          }
        } catch (error) {
          console.error('Error updating lead:', error)
          this.$toast.error(leadStatusError(this.leadLable.leadTitle))
        } finally {
          this.showConfirmationDialog = false
          this.selectedLead.newStatus = null
        }
      }
    },
    cancelUpdate() {
      if (this.selectedLead) {
        this.selectedLead.newStatus = null
      }
      this.showConfirmationDialog = false
    },
    handleLeadStatusChange() {
      if (this.selectedLeadStatus.length > 0) {
        this.shouldFetchData = true
      }
      if (this.shouldFetchData) {
        this.options.page = 1
        this.fetchData()
      }
    },

    toggleSelectAll() {
      this.$nextTick(() => {
        if (this.allLeadStatusSelected) {
          this.options.page = 1
          this.selectedLeadStatus = []
          this.leads = []
          this.totalLeads = 0
          this.shouldFetchData = false
        } else {
          this.selectedLeadStatus = this.leadSteps.map((step) => step.key)
        }
        this.handleLeadStatusChange()
      })
    },

    onStatusCleared() {
      this.leads = []
      this.options.page = 1
      this.totalLeads = 0
      this.shouldFetchData = false
    },
    onUpdate(updatedCompany) {
      const updatedCompanyIndex = this.companies.findIndex(
        (company) => company._id === updatedCompany._id
      )

      if (updatedCompanyIndex !== -1) {
        this.$set(this.companies, updatedCompanyIndex, updatedCompany)
        this.leads.forEach((lead) => {
          if (lead.company?._id === updatedCompany._id) {
            this.$set(lead, 'company', updatedCompany)
          }
        })
      }
    },
    showUpdateConfirmationDialog() {
      this.isUpdateAction = true
      this.confirmationTitle = 'Confirm Update'
      this.confirmationMessage = `Are you sure you want to update the ${this.leadLable.leadTitle} status?`
      this.confirmationActionText = 'Update'
      this.confirmationActionColor = '#2A83FF'
      this.showConfirmationDialog = true
    },

    showDeleteConfirmationDialog() {
      this.isUpdateAction = false
      this.confirmationTitle = 'Confirm Delete'
      this.confirmationMessage = `Are you sure you want to delete the ${this.leadLable.leadTitle}?`
      this.confirmationActionText = 'Delete'
      this.confirmationActionColor = 'red'
      this.showConfirmationDialog = true
    },
    performAction() {
      if (this.isUpdateAction) {
        this.updateLeadStatus()
      } else {
        this.deleteLead()
      }
      this.showConfirmationDialog = false
    },
    handleDelete(item) {
      this.selectedLead = item
      this.showDeleteConfirmationDialog()
    },
    deleteLead() {
      try {
        this.$axios
          .delete(`/workforce/lead/${this.selectedLead._id}`)
          .then(() => {
            this.selectedLead = null
            this.$toast.success(leadDeleteSuccess(this.leadLable.leadTitle))
            this.fetchData()
          })
          .catch((error) => {
            console.error(leadDeleteError(this.leadLable.leadTitle), error)
            this.$toast.error(leadDeleteError(this.leadLable.leadTitle))
          })
      } catch (error) {
        console.error('Error in deleteLead:', error)
      }
    },
    handleEdit(item) {
      this.$emit('handleEditAndChangeTab', item)
    },
    onCreateTask() {
      this.fetchData()
      this.showCreateTaskDialog = false
    },
    addTaskAction(item) {
      this.showCreateTaskDialog = true
      this.selectedLead = item
    },
    bulkFileUpload() {
      this.fetchData()
    },
    findPrimaryContact(item) {
      const selectedBranch = item.company?.companyBranches.find(
        (branch) => branch._id === item.selectedBranch
      )
      if (selectedBranch) {
        for (const contact of selectedBranch.contacts) {
          if (contact._id === item.selectedContact) {
            return contact
          }
        }
      }

      return 'N/A'
    },
    getAmcStatusText(item) {
      if (item.productHaveAmcPlan === false && item.productAmcEndDate !== null) {
        return 'AMC InActive'
      } else if (item.productHaveAmcPlan === true && item.productAmcEndDate !== null) {
        return 'AMC Active'
      } else {
        return 'AMC N/A'
      }
    },
    prepareData(dataCopy) {
      return dataCopy.map((item, index) => {
        return {
          'S.No': index + 1,
          'Product Serial Number': item.leadTitle,
          'Product Name': item.productName?.productName,
          'Product Manufacturer': item.productManufacturer?.partnerName,
          'Product Model Number': item.productModelNumber,
          'Customer Name': item.company?.name,
          'Customer Address': this.getBranchName(item),
          'Contact Number': this.findPrimaryContact(item).phoneNumber,
          'Warranty Status': item.productWarrantyStatus.split('_').join(' '),
          'AMC Status': this.getAmcStatusText(item)
        }
      })
    },
    exportDataToXLSX() {
      const dataCopy = [...this.leads]
      const restructuredData = this.prepareData(dataCopy)
      const currentDate = this.$dayjs().format('DD_MM_YYYY')
      const fileName = `Product_${currentDate}.xlsx`
      downloadExcel(restructuredData, 'Sheet1', fileName)
    },
    isSystemGenLeadTitle(item) {
      return item.leadTitle.startsWith('SR_NO_')
    },
    navToAmc() {
      this.$router.push({
        path: '/workforce/warrantyManagement',
        query: { active_tab: 'amcList' }
      })
    }
  },
  computed: {
    uploadUrl() {
      const productUrl = '/workforce/product/bulk'
      const leadUrl = '/workforce/lead/bulk'
      return this.isComplaintsEnabled ? productUrl : leadUrl
    },
    tableHeaders() {
      if (!this.$props.useCaseLeadConfig?.leadTable) return []
      const productMaker = {
        text: this.$props.useCaseLeadConfig.leadTable.columns?.productMaker,
        value: 'productMaker',
        sortable: false,
        width: '150px'
      }

      const modelNumber = {
        text: this.$props.useCaseLeadConfig.leadTable.columns?.modelNumber,
        value: 'modelNumber',
        sortable: false,
        width: '120px'
      }
      const description = {
        text: this.$props.useCaseLeadConfig.leadTable.columns?.description,
        value: 'description',
        sortable: false
      }
      const primaryContact = {
        text: 'Primary Contact',
        value: 'primaryContact',
        sortable: false,
        width: '150px'
      }
      const warrantyStatus = {
        text: 'Warranty Status',
        value: 'warrantyStatus',
        sortable: false,
        width: '150px'
      }
      const amcStatus = {
        text: 'AMC Status',
        value: 'amcStatus',
        sortable: false,
        width: '100px'
      }

      const assignedTo = {
        text: this.$props.useCaseLeadConfig.leadTable.columns?.assignedTo,
        value: 'assignedTo',
        sortable: false,
        width: '200px'
      }
      const leadStatus = {
        text: this.$props.useCaseLeadConfig.leadTable.columns?.status,
        value: 'status',
        sortable: false,
        width: '100px'
      }
      const location = {
        text: this.$props.useCaseLeadConfig.leadTable.columns?.location,
        value: 'location',
        sortable: false,
        width: '230px'
      }
      const productName = {
        text: 'Product Name',
        value: 'productName',
        sortable: false,
        width: '150px'
      }
      const amcPlan = {
        text: 'Amc Plan',
        value: 'amcPlan',
        sortable: false,
        width: '150px'
      }
      const posAmt = {
        text: this.$props.useCaseTaskConfig?.taskTable.columns.tad,
        value: 'posAmt',
        sortable: false,
        width: '150px'
      }
      const emiAmt = {
        // text: this.$props.useCaseTaskConfig?.taskTable.columns.taskTitle,
        text: 'EMI Amt',
        value: 'emiAmt',
        sortable: false,
        width: '150px'
      }
      const bktCollect = {
        text: 'Bucket',
        value: 'bktCollect',
        sortable: false,
        width: '100px'
      }
      const sourceOfLead = {
        text: 'Lead Source',
        value: 'sourceOfLead',
        sortable: false,
        width: '100px'
      }
      const tags = {
        text: 'Tags',
        value: 'tags',
        sortable: false,
        width: '150px'
      }

      const tableHeaders = [
        {
          text: this.$props.useCaseLeadConfig.leadTable.columns?.lead,
          value: 'leadTitle'
        },
        ...(this.isComplaintsEnabled ? [productName] : []),
        ...(this.isComplaintsEnabled ? [productMaker] : []),
        ...(this.isComplaintsEnabled ? [modelNumber] : []),
        {
          text: `${this.$props.useCaseLeadConfig.leadTable.columns?.company}`,
          value: 'company'
        },
        ...(this.isCollectionsEnabled ? [bktCollect] : []),
        ...(this.isCollectionsEnabled ? [posAmt] : []),
        ...(this.isCollectionsEnabled ? [emiAmt] : []),
        ...(this.isComplaintsEnabled ? [location] : []),
        ...(this.isComplaintsEnabled ? [primaryContact] : []),
        ...(this.isComplaintsEnabled ? [warrantyStatus] : [location]),
        ...(this.isComplaintsEnabled && isModuleVisible('AMC') ? [amcStatus] : []),
        ...(this.isComplaintsEnabled && isModuleVisible('AMC') ? [amcPlan] : []),
        ...(this.isBasicUseCase ? [sourceOfLead] : []),
        ...(this.isBasicUseCase ? [tags] : []),
        ...(!this.isComplaintsEnabled ? [description] : []),
        ...(this.isBasicUseCase ? [assignedTo] : []),
        ...(this.isBasicUseCase ? [leadStatus] : []),
        this.canEdit || this.canDelete
          ? {
              text: `${this.$props.useCaseLeadConfig.leadTable.columns?.actions}`,
              value: 'actions',
              sortable: false
            }
          : []
      ]

      return tableHeaders
    },
    isComplaintsEnabled() {
      if (this.$route.params.usecase === 'inventory') return true
      return false
    },
    isCollectionsEnabled() {
      if (this.$route.params.usecase === 'collection') return true
      return false
    },
    isBasicUseCase() {
      if (
        this.$route.params.usecase !== 'collection' &&
        this.$route.params.usecase !== 'inventory'
      ) {
        return true
      }
      return false
    },

    allLeadStatusSelected() {
      return this.selectedLeadStatus.length === this.leadSteps.length
    },
    someLeadStatusSelected() {
      return this.selectedLeadStatus.length > 0 && !this.allLeadStatusSelected
    },
    icon() {
      if (this.allLeadStatusSelected) return 'mdi-close-box'
      if (this.someLeadStatusSelected) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },

    companyNames() {
      const options = []

      this.companies.forEach((company) => {
        options.push({
          text: `${company.name}`,
          value: company._id
        })
      })

      return sortDropdownOptions(options)
    },
    selectedCompanyBranches() {
      const selectedCompany = this.companies.find(
        (company) => company._id === this.selectedCompany?.value
      )
      const options = selectedCompany?.companyBranches.map((branch) => {
        return {
          text: `[ Alias - ${branch.alias || ''} ] - ${branch.address.addressLine1}`,
          value: branch._id
        }
      })
      return options
    },

    getStatusColor() {
      return (leadStatus) => {
        const colorInfo = this.leadStepColors[leadStatus]
        return {
          color: colorInfo ? colorInfo.textColor : 'black',
          backgroundColor: colorInfo ? colorInfo.bgColor : 'white',
          height: '39px',
          borderRadius: '20px',
          padding: '5px 10px',
          fontSize: '14px',
          boxShadow: 'none',
          border: '1px solid transparent'
        }
      }
    },
    leadStepsSelect() {
      const leadStepsCopy = [...this.leadSteps]
      return leadStepsCopy
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.leadEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.leadDelete)
    },
    leadLable() {
      return {
        leadTitle: this.orgLang?.data?.leadModule.addLeadForm.lead,
        description: this.orgLang?.data?.leadModule.addLeadForm.description,
        companyName: this.orgLang?.data?.leadModule.addLeadForm.companyName
      }
    }
  }
}
</script>

<style scoped>
.v-data-table-header th span {
  font-size: 14px;
  color: #262626;
  font-weight: 400;
}

.td_font {
  font-weight: 700;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

/* 
.v-menu__content {
  box-shadow: none !important;
} */

.v-menu__content {
  border-radius: 10px;
}

.v-menu__content {
  border: 1px solid rgb(192, 188, 188);
}

.chip-width {
  width: auto;
  overflow: hidden;
}

.chip-text {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.select-all-item {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}

.ellipsis_class {
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-dialog.v-dialog {
  transition: none;
}

.highlighted {
  color: rgb(233, 95, 95);
}

.tagsClass {
  width: 150px;
  display: flex;
  flex-wrap: wrap;
}

.custom-text-field >>> .v-text-field__details {
  display: none !important;
}

.custom-text-field >>> .v-input__slot {
  margin-bottom: 0 !important;
}

.shadowFilter {
  box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px,
    rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px,
    rgba(0, 0, 0, 0.09) 0px -3px 5px !important;
}
</style>
