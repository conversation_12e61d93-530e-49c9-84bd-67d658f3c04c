<template>
  <div>
    <div class="p-12 bg-white dark-bg-custom rounded-lg mt-4">
      <div class="flex justify-between">
        <h2 class="text-xl mb-8 text-[#464A53]">
          {{ leadLable.leadFormObj.heading }}
        </h2>
      </div>
      <form @submit.prevent="addLead" class="space-y-4">
        <v-text-field
          v-model="leadName"
          dense
          required
          color="red"
          :label="leadLable.leadFormObj.lead + ' *'"
          class="w-5/6"
          hint="Required *"
          persistent-hint
          hide-details
          outlined
        ></v-text-field>
        <v-autocomplete
          v-if="isComplaintsEnabled"
          v-model="productName"
          :items="products"
          item-text="productName"
          item-value="_id"
          dense
          label="Product Name"
          placeholder="Start typing to Search"
          :loading="productLoading"
          @update:search-input="debounceProduct"
          :search-input.sync="searchProduct"
          class="w-5/6"
          cache-items
          hide-no-data
          hide-details
          outlined
        ></v-autocomplete>

        <v-select
          v-if="leadLable.leadFormObj.productMaker"
          v-model="productMaker"
          dense
          :items="filteredProductMakers"
          item-text="maker.partnerName"
          item-value="maker._id"
          :label="leadLable.leadFormObj.productMaker"
          class="w-5/6"
          hide-details
          outlined
        ></v-select>

        <v-text-field
          v-if="leadLable.leadFormObj.modelNumber"
          v-model="modelNumber"
          dense
          :label="leadLable.leadFormObj.modelNumber"
          class="w-5/6"
          hide-details
          outlined
        ></v-text-field>

        <v-text-field
          v-if="isComplaintsEnabled"
          v-model="productBillNumber"
          dense
          label="Bill Number"
          class="w-5/6"
          hide-details
          outlined
        ></v-text-field>
        <v-text-field
          v-if="isComplaintsEnabled"
          class="w-5/6"
          type="number"
          v-model="productPrice"
          label="Product Price"
          hide-details
          outlined
          dense
        ></v-text-field>

        <div class="flex w-5/6 space-x-5" v-if="isComplaintsEnabled">
          <v-text-field
            class="w-1/2"
            type="date"
            v-model="productMakeDate"
            label="Product Make Date"
            hide-details
            outlined
            dense
          ></v-text-field>

          <v-text-field
            class="w-1/2"
            type="date"
            v-model="productSoldOn"
            label="Product Sale Date"
            @input="calculateWarrantyEndDate"
            hide-details
            outlined
            dense
          ></v-text-field>
        </div>

        <v-select
          v-if="isComplaintsEnabled"
          class="w-5/6"
          :items="warrantyPeriods"
          item-text="text"
          item-value="value"
          v-model="defaultWarrantyPeriod"
          label="Warranty Period"
          @input="calculateWarrantyEndDate"
          hide-details
          outlined
          dense
        ></v-select>

        <v-text-field
          v-if="isComplaintsEnabled && !!productSoldOn"
          class="w-5/6"
          type="date"
          v-model="productWarrantyEndDate"
          label="Warranty End Date"
          disabled
          hide-details
          outlined
          dense
        ></v-text-field>
        <v-select
          v-if="isComplaintsEnabled && !!productSoldOn"
          class="w-5/6"
          v-model="warrantyStatus"
          label="Warranty Status:"
          :items="warrantyOptions"
          item-text="warranty"
          item-value="warranty"
          disabled
          hide-details
          outlined
          dense
        ></v-select>

        <v-select
          v-if="isComplaintsEnabled"
          v-model="haveExtendedWarranty"
          label="Extended Warranty"
          :items="[
            { text: 'Yes', value: true },
            { text: 'No', value: false },
          ]"
          item-value="value"
          item-text="text"
          class="w-5/6"
          hide-details
          outlined
          dense
        ></v-select>

        <v-text-field
          v-if="haveExtendedWarranty"
          class="w-5/6"
          type="number"
          label="Extended Warranty Period"
          v-model="extendedWarrantyPeriod"
          hide-details
          outlined
          dense
        ></v-text-field>

        <!-- AMC Plan Active -->

        <v-select
          v-if="isComplaintsEnabled"
          v-model="haveAmcPlan"
          label="AMC Plan Active"
          :items="[
            { text: 'AMC Active', value: true },
            { text: 'AMC Inactive', value: false },
            { text: 'N/A', value: null },
          ]"
          class="w-5/6"
          hide-details
          outlined
          dense
        ></v-select>

        <v-text-field
          v-if="haveAmcPlan !== null"
          class="w-5/6"
          type="date"
          v-model="productAmcEndDate"
          label="AMC End Date"
          required
          :rules="rules.amcEndDate"
          hide-details
          outlined
          dense
        ></v-text-field>

        <!-- Lead Description -->
        <div class="w-5/6" v-if="!isComplaintsEnabled">
          <v-textarea
            @input="validateLeadDescription"
            auto-grow
            :label="leadLable.leadFormObj.description"
            v-model="leadDescription"
            hide-details
            outlined
            dense
          ></v-textarea>

          <div class="flex justify-end">
            <p class="text-sm text-gray-500 mb-0">
              {{ remainingChar }} character remaining
            </p>
          </div>
        </div>
        <v-text-field
          v-if="isDefaultEnabled"
          class="w-5/6"
          v-model="sourceOfLead"
          label="Source of lead"
          hide-details
          outlined
        ></v-text-field>

        <v-combobox
          v-if="isDefaultEnabled"
          v-model="leadTags"
          :items="tags"
          class="w-5/6"
          hide-selected
          hint="Press Enter to add new tag"
          label="Add Lead Tags"
          persistent-hint
          multiple
          small-chips
          hide-details
          outlined
        >
          <template v-slot:selection="{ item }">
            <v-chip @click="removeChip(item, leadTags)">
              {{ item }}
              <v-icon small class="ml-2">mdi-close-circle</v-icon>
            </v-chip>
          </template>
        </v-combobox>

        <!-- Company Name -->
        <v-autocomplete
          class="w-5/6"
          v-model="selectedCompanyId"
          :label="leadLable.leadFormObj.companyName"
          :search-input.sync="searchCompany"
          :items="companies"
          item-text="name"
          item-value="_id"
          append-icon="mdi-plus"
          @click:append="showAddCompany = true"
          @update:search-input="handleCompany"
          hide-details
          outlined
          dense
        >
        </v-autocomplete>
        <!-- Company Branch -->
        <v-autocomplete
          :label="leadLable.leadFormObj.companyBranch"
          v-model="selectedBranchId"
          :items="selectedCompanyBranches"
          :disabled="!selectedCompanyId"
          item-value="_id"
          :item-text="
            (item) =>
              `${item.alias ? '[ ALIAS - ' + item.alias + ' ] - ' : ''}${
                item?.address?.addressLine1 || ''
              } ${item?.address?.addressLine2 || ''} ${
                item?.address?.city || ''
              }, ${item?.address?.state || ''}, ${
                item?.address?.country || ''
              }, ${item?.address?.pinCode || ''}`
          "
          class="w-5/6"
          :append-icon="selectedCompanyId ? 'mdi-plus' : ''"
          @click:append="showAddBranch = true"
          hide-details
          outlined
          dense
        >
        </v-autocomplete>

        <v-divider></v-divider>
        <div class="flex justify-between items-center">
          <h2 class="text-2xl mt-16 mb-8" v-if="selectedBranchId !== null">
            Contact Details
          </h2>
          <a
            v-if="selectedCompanyId !== '' && selectedBranchId !== null"
            class="mr-44 dark-hover-text"
            @click="openAddContactDialog"
          >
            + Add Contact
          </a>
        </div>

        <!-- Auto Populate Contact -->

        <div
          v-for="contact in selectedCompanyContacts"
          class="space-y-4"
          :key="contact"
        >
          <v-text-field
            :value="contact.firstName + ' ' + contact.lastName"
            dense
            readonly
            :label="contactLabels.contactLabels"
            class="w-5/6"
            hide-details
            outlined
          >
          </v-text-field>

          <v-text-field
            v-model="contact.designation"
            dense
            readonly
            :label="contactLabels.contactDesignation"
            class="w-5/6"
            hide-details
            outlined
          >
          </v-text-field>

          <v-text-field
            v-model="contact.phoneNumber"
            dense
            readonly
            :label="contactLabels.contactNumber"
            class="w-5/6"
            hide-details
            outlined
          >
          </v-text-field>

          <v-text-field
            v-model="contact.email"
            dense
            readonly
            :label="contactLabels.contactEmail"
            class="w-5/6"
            hide-details
            outlined
          >
          </v-text-field>

          <v-text-field
            :value="contact.isDecisionMaker ? 'Yes' : 'No'"
            dense
            readonly
            :label="contactLabels.decision"
            class="w-5/6"
            hide-details
            outlined
          >
          </v-text-field>

          <div class="flex items-center w-5/6 mt-4 space-x-5">
            <label
              for="selectContact"
              class="block font-medium w-32 text-[#464A53]"
              >Select as primary:</label
            >
            <div class="flex items-center">
              <input
                type="checkbox"
                :id="'contactCheckbox_' + contact._id"
                :value="contact._id"
                :checked="selectedContactId === contact._id"
                @change="updateSelectedContact(contact._id)"
                class="form-checkbox h-5 w-5 text-[#2A83FF]"
              />
            </div>
          </div>
          <hr class="mt-4 border-gray-300" />
        </div>

        <div class="flex justify-end mt-4 mr-48">
          <v-btn @click="cancelForm" outlined class="mr-4"> Cancel </v-btn>
          <v-btn
            type="submit"
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
          >
            Submit
          </v-btn>
        </div>
      </form>
    </div>
    <v-dialog v-model="showAddCompany" width="auto">
      <CompanyAddress
        :isOpen="showAddCompany"
        @cancel="showAddCompany = false"
        @save="addCompany"
        title="Add Company"
        :companies="companies"
      />
    </v-dialog>
    <v-dialog v-model="showAddBranch" width="auto">
      <CompanyAddress
        :company="selectedCompanyName"
        @cancel="showAddBranch = false"
        :isOpen="showAddBranch"
        @save="addBranch"
        title="Add Branch"
      />
    </v-dialog>
    <v-dialog v-model="showContactDialog" max-width="600">
      <AddContact
        :selectedBranchId="selectedBranchId"
        @close="showContactDialog = false"
        @contact-added="handleContactAdded"
      />
    </v-dialog>
  </div>
</template>

<script>
import CompanyAddress from "@/components/Workforce/CompanyAddress.vue";
import AddContact from "../CompanyManagement/AddContactForm.vue";
import {
  leadCreationSuccess,
  leadCreationError,
  companyCreationSuccess,
  companyCreationError,
  descriptionLimit,
} from "@/utils/toastMessages";
import { sortDropdownOptions } from "@/utils/common";
import { leadLabels, contactLabels } from "@/utils/workforce/formLabels";
import { debounce } from "@/utils/common";

export default {
  layout: "Workforce/default",
  name: "AddLead",
  components: {
    AddContact,
    CompanyAddress,
  },
  data() {
    const formatDate = (date) =>
      date ? this.$dayjs(date).format("YYYY-MM-DD") : null;
    return {
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      orgCode: this.$storage.getUniversal("organization_code"),
      leadLabels: leadLabels,
      contactLabels: contactLabels,
      isLoading: true,
      productLoading: false,
      showModal: false,
      leads: [],
      contacts: [],
      products: [],
      leadTags: [],
      sourceOfLead: "",
      tags: [],
      warrantyOptions: ["IN_WARRANTY", "OUT_OF_WARRANTY"],
      tasks: {},
      selectedLeadId: null,
      selectedBranchId: this.editItems?.selectedBranch || null,
      isModalOpen: false,
      selectedCompanyId: this.editItems?.company?._id || "",
      selectedContactId: this.editItems?.selectedContact || null,
      showForm: false,
      checkBranch: false,
      leadTitle: "",
      company: "",
      ContactName: "",
      productName: this.editItems?.productName?._id || null,
      nextContactIndex: 0,
      allContactsAdded: false,
      leadNameFilter: "",
      leadName: "",
      formSubmitted: false,
      searchCompany: null,
      productMaker: this.editItems?.productManufacturer?._id || null,
      modelNumber: this.editItems?.productModelNumber || "",
      leadDescription: this.editItems?.leadDescription || "",
      ContactFirstName: "",
      ContactDesignation: "",
      ContactNumber: "",
      ContactEmail: "",
      decisionMaker: "",
      searchProduct: null,
      productBillNumber: this.editItems?.productBillNumber || "",
      showContactDialog: false,
      showAddCompany: false,
      showAddBranch: false,
      warrantyPeriods: [
        { text: "6 Months", value: 0.5 },
        { text: "1 Years", value: 1 },
        { text: "2 Years", value: 2 },
        { text: "3 Years", value: 3 },
        { text: "5 Years", value: 5 },
      ],
      productPrice: this.editItems?.productPrice || null,
      haveExtendedWarranty:
        this.editItems?.productHaveExtendedWarranty || false,
      extendedWarrantyPeriod:
        this.editItems?.productExtendedWarrantyPeriod || null,
      defaultWarrantyPeriod: this.editItems?.productDefaultWarrantyPeriod || 1,
      haveAmcPlan: this.editItems?.productHaveAmcPlan || null,
      warrantyStatus: this.editItems?.productWarrantyStatus || undefined,
      productSoldOn: formatDate(this.editItems?.productSoldOn) || null,
      productWarrantyEndDate: formatDate(
        this.editItems?.productWarrantyEndDate
      ),
      productMakeDate: formatDate(this.editItems?.productMakeDate) || null,
      productAmcEndDate: formatDate(this.editItems?.productAmcDate) || null,
      isEditing: !!this.editItems?._id,
      rules: {
        amcEndDate: [
          (val) => {
            if (this.haveAmcPlan !== null) {
              return (val || "").length > 0 || `AMC end date is required`;
            }
            return true;
          },
        ],
      },
    };
  },
  props: {
    tabSwitch: Function,
    fetchCompanies: Function,
    companies: Array,
    editItems: Object,
    useCaseLeadConfig: Object,
  },
  async mounted() {
    this.populateLeadName();
    if (this.editItems) {
      this.$emit("search", this.editItems?.company?.name);
      this.searchProduct = this.editItems?.productName?.productName;
      await this.fetchProducts();
    }
  },
  computed: {
    selectedCompanyName() {
      if (this.selectedCompanyId) {
        const selectedCompany = this.companies.find(
          (company) => company._id === this.selectedCompanyId
        );
        return selectedCompany ? selectedCompany.name : "";
      }
      return "";
    },
    selectedCompanyContacts() {
      if (this.selectedBranchId !== null) {
        const selectedBranch = this.selectedCompanyBranches.find(
          (branch) => branch._id === this.selectedBranchId
        );
        return selectedBranch ? selectedBranch.contacts : [];
      }
      return [];
    },
    selectedCompanyBranches() {
      const selectedCompany = this.companies.find(
        (company) => company._id === this.selectedCompanyId
      );

      return selectedCompany
        ? sortDropdownOptions(
            selectedCompany.companyBranches,
            "address.addressLine1"
          )
        : [];
    },
    filteredProductMakers() {
      if (!this.productName) return [];
      const selectedProduct = this.products.find(
        (product) => product._id === this.productName
      );
      return selectedProduct
        ? sortDropdownOptions(selectedProduct.productMaker, "partnerName")
        : [];
    },
    remainingChar() {
      const character = this.leadDescription
        .trim()
        .split("")
        .filter((character) => character !== "");
      return 300 - character.length;
    },
    leadLable() {
      return {
        leadTitle: this.$props.useCaseLeadConfig?.addLeadForm.lead,
        leadFormObj: this.$props.useCaseLeadConfig?.addLeadForm,
        description: this.$props.useCaseLeadConfig?.addLeadForm.description,
      };
    },
    isComplaintsEnabled() {
      if (this.$route.params.usecase === "inventory") return true;
      return false;
    },
    isDefaultEnabled() {
      return this.$route.params.usecase === "default";
    },
  },
  methods: {
    async addLead() {
      this.formSubmitted = true;
      if (!this.leadName.trim()) {
        return;
      }
      try {
        const currentUsecase = this.$route.params.usecase.toLowerCase();
        const leadNames = this.leadName.split(",").map((name) => name.trim());

        const leadCreationPromises = leadNames.map(async (leadTitle) => {
          const data = {
            leadTitle: leadTitle,
            leadDescription: this.leadDescription,
            useCase: currentUsecase,
          };
          if (this.selectedCompanyId) {
            data.company = this.selectedCompanyId;
          }
          if (this.selectedBranchId) {
            data.selectedBranch = this.selectedBranchId;
          }
          if (this.selectedContactId) {
            data.selectedContact = this.selectedContactId;
          }
          if (this.isComplaintsEnabled) {
            data.productManufacturer = this.productMaker || undefined;
            data.productModelNumber = this.modelNumber || "";
            data.productMakeDate = this.productMakeDate || null;
            data.productSoldOn = this.productSoldOn || null;
            data.productPrice = this.productPrice || null;
            data.productWarrantyEndDate = this.productWarrantyEndDate || null;
            data.productHaveExtendedWarranty = this.haveExtendedWarranty;
            data.productExtendedWarrantyPeriod =
              this.extendedWarrantyPeriod || null;
            data.productDefaultWarrantyPeriod =
              this.defaultWarrantyPeriod || null;
            data.productHaveAmcPlan = this.haveAmcPlan;
            data.productAmcEndDate = this.productAmcEndDate;
            data.productWarrantyStatus = this.warrantyStatus;
            data.productBillNumber = this.productBillNumber;
            data.productName = this.productName || undefined;
          }
          if (this.sourceOfLead || this.leadTags) {
            data.sourceOfLead = this.sourceOfLead;
            data.tags = this.leadTags;
          }
          let response;
          if (this.isEditing) {
            response = await this.$axios.put(
              `/workforce/lead/${this.editItems?._id}`,
              data
            );
          } else {
            response = await this.$axios.post(`/workforce/lead`, data);
          }
          return response;
        });
        await Promise.all(leadCreationPromises);
        this.leadName = "";
        this.leadDescription = "";
        this.productMaker = "";
        this.modelNumber = "";
        this.selectedCompanyId = "";
        this.selectedBranchId = null;
        this.selectedContactId = null;
        this.$toast.success(leadCreationSuccess(this.leadLable.leadTitle));
        this.showForm = false;
        this.editItems = {};

        this.tabSwitch();
      } catch (error) {
        this.$toast.error(leadCreationError(this.leadLable.leadTitle));
        console.error("Error creating new lead:", error);
      }
    },
    decisionMakerDisplay() {
      if (this.contact.isDecisionMaker === true) {
        return "Yes";
      } else if (this.contact.isDecisionMaker === false) {
        return "No";
      } else {
        return "";
      }
    },
    async addCompany(data) {
      const company = {
        name: data.companyName,
        companyDetails: data.companyDetails,
        companyBranches: [
          {
            alias: data.companyName,
            address: {
              addressLine1: data.addLine1,
              addressLine2: data.addLine2,
              city: data.city,
              state: data.state,
              country: data.country,
              pinCode: data.pinCode,
              coordinates: {
                latitude: data.latitude,
                longitude: data.longitude,
              },
            },
          },
        ],
      };

      try {
        const response = await this.$axios.post("/workforce/company", company);

        this.companies.push(response.data.company);
        this.showAddCompany = false;
        this.$toast.success(companyCreationSuccess);
      } catch (error) {
        console.error(companyCreationError, error);
        this.$toast.error(companyCreationError);
      }
    },
    validateLeadDescription() {
      const characterCount = this.leadDescription.trim().length;
      if (characterCount > 300) {
        this.$toast.error(descriptionLimit);
        this.leadDescription = this.leadDescription.substring(0, 300);
      }
    },
    openAddContactDialog() {
      this.showContactDialog = true;
    },
    removeChip(item, options) {
      const index = options.indexOf(item);
      if (index !== -1) {
        options.splice(index, 1);
      }
      if (this.leadTags.length > 0) {
        this.tags = this.leadTags;
      }
    },
    handleContactAdded(updatedCompany) {
      const existingCompanyIndex = this.companies.findIndex(
        (company) => company._id === updatedCompany._id
      );
      if (existingCompanyIndex !== -1) {
        this.$set(this.companies, existingCompanyIndex, updatedCompany);
      }
      this.showContactDialog = false;
    },
    async addBranch(data) {
      this.checkBranch = true;
      const companyId = this.selectedCompanyId;
      const company = {
        branch: {
          alias: data.alias,
          address: {
            addressLine1: data.addLine1,
            addressLine2: data.addLine2,
            city: data.city,
            state: data.state,
            country: data.country,
            pinCode: data.pinCode,
            coordinates: {
              latitude: data.latitude,
              longitude: data.longitude,
            },
          },
        },
      };

      try {
        const response = await this.$axios.post(
          `/workforce/addBranch/${companyId}`,
          company
        );
        const newBranch = response.data.company.companyBranches;
        this.selectedCompanyBranches.push(newBranch[newBranch.length - 1]);
        this.checkBranch = false;
        this.showAddBranch = false;
        const successMessage =
          response?.data?.message || "Company Branch Added Successfully !!!";
        this.$toast.success(successMessage);
      } catch (error) {
        console.error("Error in company addition", error);
        const errorMessage =
          error.response.data.message || "Error in company branch addition !!!";
        this.$toast.error(errorMessage);
      }
    },
    cancelForm() {
      this.tabSwitch();
    },
    updateSelectedContact(contactId) {
      this.selectedContactId = contactId;
    },
    calculateWarrantyEndDate() {
      if (this.productSoldOn) {
        const saleDate = this.$dayjs(this.productSoldOn);
        const years = Math.floor(this.defaultWarrantyPeriod);
        const months = Math.round((this.defaultWarrantyPeriod % 1) * 12);

        let warrantyEnd = saleDate.add(years, "year").add(months, "month");

        this.productWarrantyEndDate = warrantyEnd.format("YYYY-MM-DD");

        const currentDate = this.$dayjs();
        if (warrantyEnd.isAfter(currentDate)) {
          this.warrantyStatus = "IN_WARRANTY";
        } else {
          this.warrantyStatus = "OUT_OF_WARRANTY";
        }
      }
    },
    debounceProduct: debounce(function (text) {
      if (!text && this.editItems) {
        this.products = [];
        return;
      }
      this.fetchProducts();
    }, 1000),
    async fetchProducts() {
      try {
        this.productLoading = true;
        const params = {};
        if (this.searchProduct) {
          params.search = this.searchProduct;
        }
        const response = await this.$axios.get(`/workforce/ecom/productMaps`, {
          params,
        });
        const allProducts = response.data?.products;

        this.products = sortDropdownOptions(allProducts, "productName");
      } catch (error) {
        console.error("An error occurred:", error);
      } finally {
        this.productLoading = false;
      }
    },
    populateLeadName() {
      let leadTitle = this.editItems?.leadTitle;

      if (this.isComplaintsEnabled && !leadTitle) {
        this.leadName = this.generateLeadTitle();
      } else if (leadTitle) {
        this.leadName = leadTitle;
      }
    },
    generateLeadTitle() {
      const prefix = "SR_NO_";
      const dateTime = this.$dayjs().format("DD/MM/YY/hhmmss");
      return `${prefix}${dateTime}`;
    },
    handleCompany: debounce(function () {
      if (this.selectedCompanyId) return;
      this.$emit("search", this.searchCompany);
    }, 1000),
  },
};
</script>
<style>
.cancel {
  border: 1px solid #2a83ff;
  background: white;
}
</style>
