<template>
  <div class="p-3">
    <div
      class="justify-end items-center my-3"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
      v-if="showTable"
    >
      <div
        class="justify-end mr-4"
        :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']"
      >
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="searchPlan"
            label="Plans"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </div>
      <v-tooltip top>
        <template #activator="{ on, attrs }">
          <v-btn
            @click="toggleShowAddAmcPlan()"
            :color="$vuetify.theme.currentTheme.primary"
            :class="[
              $vuetify.breakpoint.lgAndUp
                ? ' white--text'
                : 'ml-3 flex-col white--text mt-2',
            ]"
            v-bind="attrs"
            v-on="on"
            v-show="canWrite"
          >
            Add Plans
            <v-icon right dark>mdi-plus</v-icon>
          </v-btn>
        </template>
        <span>Add Plans</span>
      </v-tooltip>
    </div>

    <div v-if="showTable" class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="amcPlans"
        class="pl-7 pr-7 pt-4"
        :options.sync="options"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :server-items-length="totalPlans"
        fixed-header
        :loading="loadTable"
      >
        <template v-slot:item.planName="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.planName }}</td>
        </template>
        <template v-slot:item.planDuration="{ item }">
          <td class="px-4 font-semibold">{{ item.planDuration }} days</td>
        </template>
        <template v-slot:item.coverage="{ item }">
          <td class="px-4 font-semibold">{{ item.coverage[0] }}</td>
        </template>
        <template v-slot:item.serviceSchedule="{ item }">
          <td class="px-4 font-semibold">{{ item.serviceSchedule }}</td>
        </template>
        <template v-slot:item.planAdded="{ item }">
          <td class="px-4 font-semibold">{{ item.planAdded }}</td>
        </template>
        <template v-slot:item.payment="{ item }">
          <td class="px-4 font-semibold">{{ item.finalPrice }}</td>
        </template>
        <template v-slot:item.paymentSchedule="{ item }">
          <td class="px-4 font-semibold">{{ item.paymentSchedule }}</td>
        </template>
        <template v-slot:item.status="{ item }">
          <td class="px-4 pt-6 font-semibold">
            <v-select
              solo
              flat
              dense
              :menu-props="{ offsetY: true }"
              v-model="item.status"
              :items="statusOptions"
              item-text="text"
              item-value="value"
            ></v-select>
          </td>
        </template>
        <template v-slot:item.actions="{ item }">
          <td class="px-4">
            <action-button
              :item="item"
              :canEdit="canEdit"
              :canDelete="canDelete"
              :showDeleteButton="canDelete"
              :showEditButton="canEdit"
              :handleEdit="() => editItem(item)"
              :handleDelete="() => deleteItem(item)"
            />
          </td>
        </template>
      </v-data-table>
    </div>
    <div v-else-if="showAddAmcList">
      <AddAmcList @cancel="toggleShowTable" />
    </div>

    <div v-else-if="showAddAmcPlan">
      <AddAmcPlan @cancel="toggleShowTable" :editedItem="editedItem" />
    </div>
  </div>
</template>

<script>
import ActionButton from "../../ActionButton.vue";
import AddAmcList from "./AddAmcList.vue";
import AddAmcPlan from "./AddAmcPlan.vue";
import { debounce, hasPermission } from "@/utils/common";
import permissionData from "@/utils/permissions";

export default {
  name: "AmcList",
  components: {
    AddAmcList,
    AddAmcPlan,
    ActionButton,
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      loadTable: true,
      searchPlan: "",
      statusOptions: [
        { text: "Active", value: "ACTIVE" },
        { text: "In Active", value: "INACTIVE" },
      ],
      options: { sortBy: [], sortDesc: [], itemsPerPage: 5, page: 1 },
      amcPlans: [],
      totalPlans: 0,
      editedItem: null,
      filterName: "",
      showTable: true,
      showAddAmcList: false,
      showAddAmcPlan: false,
    };
  },
  watch: {
    options: {
      handler() {
        this.getAmcPlans();
      },
      deep: true,
    },
  },
  async mounted() {
    this.getAmcPlans();
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.amcEdit);
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.amcDelete);
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.amcWrite);
    },
    headers() {
      const action = {
        text: "Action",
        value: "actions",
        width: "16%",
        sortable: false,
      };

      const headers = [
        { text: "Plan Name", value: "planName", width: "15%", sortable: false },
        {
          text: "Plan Duration",
          value: "planDuration",
          width: "10%",
          sortable: false,
        },
        { text: "Coverage", value: "coverage", width: "25%", sortable: false },
        {
          text: "Servicing Schedule",
          value: "serviceSchedule",
          width: "13%",
          sortable: false,
        },
        { text: "Plan Price", value: "payment", width: "10%", sortable: false },
        {
          text: "Payment Scheduled",
          value: "paymentSchedule",
          width: "14%",
          sortable: false,
        },
        { text: "Plan Status", value: "status", width: "16%", sortable: false },
        ...(this.canEdit || this.canDelete ? [action] : []),
      ];
      return headers;
    },
  },
  methods: {
    toggleShowTable() {
      this.showAddAmcPlan = false;
      this.showAddAmcList = false;
      this.showTable = true;
      this.editedItem = {};
      this.getAmcPlans();
    },
    toggleShowAddAmcList() {
      this.showTable = false;
      this.showAddAmcPlan = false;
      this.showAddAmcList = true;
    },
    toggleShowAddAmcPlan() {
      this.showTable = false;
      this.showAddAmcList = false;
      this.showAddAmcPlan = true;
    },
    async getAmcPlans() {
      try {
        const { page, itemsPerPage } = this.options;
        const params = {
          page: page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalPlans,
        };
        if (this.searchPlan) {
          params.search = this.searchPlan;
        }
        this.loadTable = true;
        const response = await this.$axios.get("/workforce/ecom/amcplans", {
          params: params,
        });
        this.loadTable = false;
        this.amcPlans = response.data?.plans;
        this.totalPlans = response.data.pagination.totalCount;
      } catch (error) {
        console.log(error);
        this.loadTable = false;
      }
    },
    editItem(item) {
      this.editedItem = item;
      this.showTable = false;
      this.showAddAmcPlan = true;
    },
    async deleteItem(item) {
      try {
        const response = await this.$axios.delete(`/workforce/ecom/amcplan/${item._id}`);
        this.getAmcPlans();
        const successMessage = response?.data?.message || "Plans deleted successfully"
        this.$toast.success(successMessage);
      } catch (error) {
        console.error("Error deleting item:", error);
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage);
      }
    },
    handleInput: debounce(function () {
      this.options.page = 1;
      this.getAmcPlans();
    }, 1000),
  },
};
</script>
