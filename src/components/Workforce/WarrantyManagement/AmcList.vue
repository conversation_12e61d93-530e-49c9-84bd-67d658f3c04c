<template>
  <div class="p-3">
    <div v-if="showTable">
      <div
        class="grid grid-rows-2 grid-cols-9 gap-4 mb-8"
        v-show="isFeatureVisible('SHOW_AMC_STATS')"
      >
        <div
          class="row-start-1 row-span-2 col-span-2"
          :class="{
            'p-1 border border-[#717EEE] rounded-3xl': filterValue == 'active',
          }"
          @click="showAmcList('active')"
        >
          <ReusableBox
            :number="amcStats?.activeAmc"
            content="Active AMC"
            color="bg-[#717EEE]"
            class="py-4 cursor-pointer"
          />
        </div>
        <div
          class="row-start-1 row-span-2 col-span-2"
          @click="showAmcList('expired')"
          :class="{
            'p-1 border border-[#58B49D] rounded-3xl': filterValue == 'expired',
          }"
        >
          <ReusableBox
            :number="amcStats?.amcExpired"
            content="AMC Expiring"
            color="bg-[#58B49D]"
            class="py-4 cursor-pointer"
          />
        </div>
        <div
          class="row-start-1 row-span-2 col-span-2"
          @click="showAmcList('scheduledService')"
          :class="{
            'p-1 border border-[#F2605F] rounded-3xl':
              filterValue == 'scheduledService',
          }"
        >
          <ReusableBox
            :number="amcStats?.upcomingServices"
            content="Scheduled Services"
            color="bg-[#F2605F]"
            class="py-4 cursor-pointer"
          />
        </div>
        <div
          class="row-start-1 row-span-2 col-span-2"
          @click="showAmcList('scheduledPayment')"
          :class="{
            'p-1 border border-[#205d68] rounded-3xl':
              filterValue == 'scheduledPayment',
          }"
        >
          <ReusableBox
            :number="amcStats?.upcomingPayments"
            content="Scheduled Payment"
            color="bg-[#205d68]"
            class="py-4 cursor-pointer"
          />
        </div>
      </div>
    </div>
    <div
      class="flex justify-end items-center"
      v-if="showTable"
      v-show="canWrite"
    >
      <v-tooltip top>
        <template #activator="{ on, attrs }">
          <v-btn
            @click="toggleShowAddAmcList()"
            :color="$vuetify.theme.currentTheme.primary"
            class="-mt-4 ml-4 mb-4 white--text"
            v-bind="attrs"
            v-on="on"
          >
            Add AMC List
            <v-icon right dark>mdi-plus</v-icon>
          </v-btn>
        </template>
        <span>Add AMC List</span>
      </v-tooltip>
    </div>
    <div v-if="showTable" class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="amcSellsData"
        class="pl-7 pr-7 pt-4"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :options.sync="options"
        :server-items-length="totalSells"
        :loading="loadTable"
        fixed-header
      >
        <template v-slot:item.planName="{ item }">
          <td
            class="px-4 py-6 hover:text-blue-700 dark-hover-text cursor-pointer font-semibold"
            @click="navToAmcPlan"
          >
            {{ item.amcPlan?.planName }}
          </td>
        </template>
        <template v-slot:item.company="{ item }">
          <td class="px-4 font-semibold">
            {{ item.productSell?.company?.name }}
          </td>
        </template>
        <template v-slot:item.serialNumber="{ item }">
          <td class="px-4 font-semibold">{{ item.productSell?.leadTitle }}</td>
        </template>
        <template v-slot:item.productName="{ item }">
          <td class="px-4 font-semibold">
            {{ item.productSell?.productName?.productName }}
          </td>
        </template>
        <template v-slot:item.coverage="{ item }">
          <td class="px-4 font-semibold">
            {{ item.amcPlan?.coverage?.join(", ") }}
          </td>
        </template>
        <template v-slot:item.planDescription="{ item }">
          <td class="px-4 font-semibold">
            {{ item.amcPlan?.planDescription }}
          </td>
        </template>
        <template v-slot:item.transaction="{ item }">
          <td class="px-4 font-semibold">
            {{ item.lastPayment?.transactionNumber }}
          </td>
        </template>
        <template v-slot:item.amountPaid="{ item }">
          <td class="px-4 font-semibold">
            {{ item.lastPayment?.amountPaid }}
          </td>
        </template>
        <template v-slot:item.paidOn="{ item }">
          <td class="px-4 font-semibold">
            {{
              item.lastPayment?.paidOn
                ? $dayjs(item.lastPayment?.paidOn).format("DD/MM/YYYY")
                : "-"
            }}
          </td>
        </template>
        <template v-slot:item.amountPending="{ item }">
          <td class="px-4 font-semibold">
            {{ item.lastPayment?.amountPending }}
          </td>
        </template>
        <template v-slot:item.startDate="{ item }">
          <td class="px-4 font-semibold">
            {{
              item.amcEffectivefrom
                ? $dayjs(item.amcEffectivefrom).format("DD/MM/YYYY")
                : ""
            }}
          </td>
        </template>
        <template v-slot:item.expiryDate="{ item }">
          <td class="px-4 font-semibold">
            {{ $dayjs(item.amcExpiresOn).format("DD/MM/YYYY") }}
          </td>
        </template>
        <template v-slot:item.serviceBy="{ item }">
          <td class="px-4 font-semibold">
            {{ getFullName(item) }}
            {{
              item.lastService?.serviceDoneOn
                ? $dayjs(item.lastService?.serviceDoneOn).format("DD/MM/YYYY")
                : ""
            }}
          </td>
        </template>
        <template v-slot:item.nextService="{ item }">
          <td class="px-4 font-semibold">
            {{
              item.nextServiceDueOn
                ? $dayjs(item.nextServiceDueOn).format("DD/MM/YYYY")
                : ""
            }}
          </td>
        </template>
        <template v-slot:item.serviceStatus="{ item }">
          <td class="px-4 font-semibold">
            {{ item.lastService?.serviceStatus }}
          </td>
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4">
            <action-button
              :item="item"
              :canWrite="canWrite"
              :canDelete="canDelete"
              :showAddTaskButton="canWrite"
              :showDeleteButton="canDelete"
              :showEditButton="false"
              :plusIconText="'Update Amc Services'"
              :addTaskAction="() => showPopUp(item)"
              :handleDelete="() => deleteAmcSells(item)"
            />
          </td>
        </template>
      </v-data-table>
    </div>
    <div v-else-if="showAddAmcList">
      <AddAmcList @cancel="toggleShowTable" />
    </div>

    <div v-else-if="showAddAmcPlan">
      <AddAmcPlan @cancel="toggleShowTable" />
    </div>
    <div class="h-44">
      <v-dialog
        v-model="showPaymentDialog"
        width="43rem"
        class="dark-bg-custom"
      >
        <ServicePaymentTab
          :selectedAmcSell="selectedAmcSell"
          @closeDialog="closeDialog"
        />
      </v-dialog>
    </div>
    <AlertPopUp
      :showConfirmationDialog="showConfirmationDialog"
      :confirmationTitle="'Confirm Delete'"
      :confirmationMessage="'Are you sure you want to delete the AMC entry?'"
      :confirmationActionText="'DELETE'"
      :confirmationActionColor="'red'"
      :performAction="performDelete"
      :cancel="cancel"
    />
  </div>
</template>

<script>
import AddAmcList from "~/components/Workforce/WarrantyManagement/AddAmcList.vue";
import AddAmcPlan from "~/components/Workforce/WarrantyManagement/AddAmcPlan.vue";
import ServicePaymentTab from "./ServicePaymentTab";
import ReusableBox from "@/components/ReusableBox.vue";
import { isModuleFeatureAllowed, hasPermission } from "@/utils/common";
import permissionData from "@/utils/permissions";
import AlertPopUp from "@/components/AlertPopUp";
import ActionButton from "../../ActionButton.vue";

export default {
  name: "AmcList",
  components: {
    AddAmcList,
    AddAmcPlan,
    ActionButton,
    ServicePaymentTab,
    ReusableBox,
    AlertPopUp,
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      loadTable: true,
      options: { sortBy: [], sortDesc: [], itemsPerPage: 5, page: 1 },
      amcSellsData: [],
      amcStats: null,
      totalSells: 0,
      filterName: "",
      showTable: true,
      showAddAmcList: false,
      showAddAmcPlan: false,
      showPaymentDialog: false,
      filterValue: "",
      showConfirmationDialog: false,
      selectedAmcSell: null,
    };
  },
  watch: {
    options: {
      handler() {
        this.getAmcSells();
      },
      deep: true,
    },
  },
  async mounted() {
    await this.getAmcStats();
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.amcEdit);
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.amcDelete);
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.amcWrite);
    },
    headers() {
      const action = {
        text: "Action",
        value: "action",
        width: "100px",
        sortable: false,
      };
      const headers = [
        {
          text: "Plan Name",
          value: "planName",
          width: "150px",
          sortable: false,
        },
        {
          text: "Customer Name",
          value: "company",
          width: "120px",
          sortable: false,
        },
        {
          text: "Product Sr. No.",
          value: "serialNumber",
          width: "150px",
          sortable: false,
        },
        {
          text: "Product Name",
          value: "productName",
          width: "150px",
          sortable: false,
        },
        {
          text: "Coverage",
          value: "coverage",
          width: "150px",
          sortable: false,
        },
        {
          text: "Plan Description",
          value: "planDescription",
          width: "150px",
          sortable: false,
        },
        {
          text: "Transaction Number",
          value: "transaction",
          width: "120px",
          sortable: false,
        },
        {
          text: "Amount Paid",
          value: "amountPaid",
          width: "100px",
          sortable: false,
        },
        {
          text: "Amount Pending",
          value: "amountPending",
          width: "100px",
          sortable: false,
        },
        {
          text: "Payment Date",
          value: "paidOn",
          width: "100px",
          sortable: false,
        },
        {
          text: "Start Date",
          value: "startDate",
          width: "100px",
          sortable: false,
        },
        {
          text: "Expiry Date",
          value: "expiryDate",
          width: "100px",
          sortable: false,
        },
        {
          text: "Last Service By",
          value: "serviceBy",
          width: "120px",
          sortable: false,
        },
        {
          text: "Next Service",
          value: "nextService",
          width: "100px",
          sortable: false,
        },
        {
          text: "Service Status",
          value: "serviceStatus",
          width: "100px",
          sortable: false,
        },
        ...(this.canEdit || this.canDelete || this.canWrite ? [action] : []),
      ];
      return headers;
    },
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed("AMC", feature);
    },
    toggleShowTable() {
      this.showAddAmcPlan = false;
      this.showAddAmcList = false;
      this.showTable = true;
      this.getAmcSells();
    },
    toggleShowAddAmcList() {
      this.showTable = false;
      this.showAddAmcPlan = false;
      this.showAddAmcList = true;
    },
    showPopUp(item) {
      this.showPaymentDialog = true;
      this.selectedAmcSell = item;
    },
    showAmcList(filterValue) {
      if(this.filterValue == filterValue ) {
        this.filterValue = ""
      }else {
        this.filterValue = filterValue;
      }
      this.getAmcSells();
    },
    async getAmcSells() {
      try {
        const { page, itemsPerPage } = this.options;
        const params = {
          page: page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalSells,
          filter: this.filterValue,
        };
        this.loadTable = true;
        const response = await this.$axios.get("/workforce/ecom/amc/sells", {
          params: params,
        });
        this.loadTable = false;
        this.amcSellsData = response.data?.amcSells;
        this.totalSells = response.data.pagination.totalCount;
      } catch (error) {
        console.error(error);
        this.loadTable = false;
      }
    },
    async getAmcStats() {
      try {
        const response = await this.$axios.get("/workforce/ecom/amc/stats");
        this.amcStats = response.data.amcStats;
      } catch (error) {
        console.error(error);
      }
    },
    getFullName(item) {
      return [
        item.lastService?.serviceDoneBy?.firstName,
        item.lastService?.serviceDoneBy?.lastName || "",
      ].join(" ");
    },
    navToAmcPlan() {
      this.$emit("navToAmcPlan");
    },
    closeDialog() {
      this.showPaymentDialog = false;
      this.selectedAmcSell = null;
      this.getAmcSells();
    },
    deleteAmcSells(item) {
      this.selectedAmcSell = item;
      this.showConfirmationDialog = true;
    },
    async performDelete() {
      try {
        const response = await this.$axios.delete(
          `/workforce/ecom/amc/sell/${this.selectedAmcSell?._id}`
        );
        this.getAmcSells();
        const successMessage = response?.data?.message || "AMC entry deleted successfully!"
        this.$toast.success(successMessage);
      } catch (error) {
        console.error(error);
        const errorMessage = error.response.data.message || "Failed to delete the AMC entry."
        this.$toast.error(errorMessage);
      } finally {
        this.showConfirmationDialog = false;
        this.selectedAmcSell = null;
      }
    },
    cancel() {
      this.showConfirmationDialog = false;
      this.selectedAmcSell = null;
    },
  },
};
</script>
