<template>
  <v-form
    ref="form"
    v-model="valid"
    lazy-validation
    class="p-12 bg-white dark-bg-custom rounded-lg mt-4"
  >
    <v-container>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Name:</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="amcPlan"
            :items="amcPlans"
            item-text="planName"
            label="Amc Plan Name"
            item-value="_id"
            outlined
            dense
            hide-details
            @change="handleChange"
            :rules="[(v) => !!v || `Plan name is required`]"
          ></v-select>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Customer/Product Sr.No:</p>
        </v-col>
        <v-col cols="">
          <v-autocomplete
            v-model="productSell"
            :items="leads"
            label="Customer/Product Sr.No"
            item-text="label"
            item-value="_id"
            :search-input.sync="searchLead"
            @update:search-input="debounceLead"
            dense
            outlined
            hide-details
            :rules="[(v) => !!v || `Customer/Product Sr.No is required`]"
          ></v-autocomplete>
        </v-col>
      </v-row>

      <v-row v-if="amcPlan">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Coverage:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="coverage"
            label="Coverage"
            dense
            outlined
            hide-details
            readonly
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row v-if="amcPlan">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Description:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="planDescription"
            label="Plan Description"
            dense
            outlined
            readonly
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Effective Date:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            type="datetime-local"
            v-model="amcEffectivefrom"
            label="Effective Date"
            outlined
            dense
            hide-details
            :rules="[(v) => !!v || `Effective date is required`]"
          ></v-text-field>
        </v-col>
      </v-row>
    </v-container>

    <v-row>
      <v-col cols="12" class="text-right">
        <v-btn outlined class="px-12 mr-5" @click="cancel">Cancel</v-btn>
        <v-btn
          class="white--text px-12 mr-3"
          :color="$vuetify.theme.currentTheme.primary"
          @click="addAmc"
          >Add</v-btn
        >
      </v-col>
    </v-row>
  </v-form>
</template>

<script>
import { debounce } from "@/utils/common";
export default {
  data() {
    return {
      amcPlan: null,
      location: "",
      amcEffectivefrom: null,
      productSell: null,
      searchLead: null,
      amcPlans: [],
      leads: [],
      valid: true,
    };
  },
  async mounted() {
    await this.getAmcPlans();
    // await this.getLeads();
  },
  methods: {
    addAmc() {
      try {
        const isValid = this.$refs.form.validate();
        if (!isValid) return;

        const payload = {
          productSell: this.productSell,
          amcPlan: this.amcPlan,
          amcEffectivefrom: this.amcEffectivefrom,
        };
        this.$axios.post("/workforce/ecom/amc/sell", payload);
        this.$toast.success("AMC added successfully");
        this.cancel();
      } catch (error) {
        this.$toast.error(error);
        console.log(error);
      }
    },
    cancel() {
      this.$emit("cancel");
    },
    async getAmcPlans() {
      try {
        const response = await this.$axios.get("/workforce/ecom/amcplans");
        this.amcPlans = response.data?.plans;
      } catch (error) {
        console.log(error);
      }
    },
    debounceLead: debounce(async function (text) {
      if (!text) {
        return;
      }
      const selectedItem = this.leads.find((lead) => lead.label === text);
      if (selectedItem) {
        this.productSell = selectedItem;
      } else {
        await this.getLeads();
      }
    }, 800),

    async getLeads() {
      try {
        const params = {};

        if (this.searchLead) {
          params.search = this.searchLead;
          params.useCase = "inventory";
        }
        const response = await this.$axios.get("/workforce/lead/org", {
          params,
        });
        this.leads = response.data?.leads.map((el) => {
          return {
            ...el,
            label: `${el.leadTitle || ""} - ${el.company?.name || ""}`,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    handleChange() {
      const selectedPlan = this.amcPlans.find((el) => el._id === this.amcPlan);
      this.coverage = selectedPlan?.coverage;
      this.planDescription = selectedPlan?.planDescription;
    },
  },
};
</script>
