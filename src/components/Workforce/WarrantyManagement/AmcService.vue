<template>
  <v-card class="p-4">
    <div>
      <v-form v-model="valid">
        <v-text-field
          label="Service Date"
          type="datetime-local"
          v-model="serviceDoneOn"
          outlined 
          dense
          hide-details
          class="mb-3"
        ></v-text-field>
        <v-select
          label="Service Done By"
          :items="users"
          item-text="fullName"
          item-value="_id"
          v-model="serviceDoneBy"
          outlined 
          dense
          hide-details
          class="mb-3"
        >
        </v-select>

        <v-select
          label="Service Status"
          :items="serviceOptions"
          v-model="serviceStatus"
          outlined 
          dense
          hide-details
          class="mb-3"
        >
        </v-select>
        <v-text-field
          v-if="serviceStatus === 'COMPLETED'"
          label="Next Service Date"
          type="datetime-local"
          outlined 
          dense
          hide-details
          v-model="nextServiceDueOn"
          class="mb-3"
        ></v-text-field>
        <div class="flex items-center justify-between">
          <div class="w-full">
            <v-btn
              class="font-bold rounded-md"
              outlined
              block
              @click="closeDialog"
              >Cancel</v-btn
            >
          </div>
          <div class="p-2" />
          <div class="w-full">
            <v-btn
              @click="handleClick"
              block
              class="white--text rounded-md"
              :color="$vuetify.theme.currentTheme.primary"
            >
              Update
            </v-btn>
          </div>
        </div>
      </v-form>
    </div>
  </v-card>
</template>
<script>
export default {
  data() {
    return {
      valid: true,
      serviceDoneBy: null,
      serviceStatus: "Pending",
      serviceDoneOn: this.$dayjs().format("YYYY-MM-DDTHH:mm"),
      users: [],
      nextServiceDueOn: null,
      serviceOptions: [
        {
          text: "Pending",
          value: "PENDING",
        },
        {
          text: "Completed",
          value: "COMPLETED",
        },
      ],
    };
  },
  watch: {
    selectedAmcSell: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.updateFormFields();
        }
      },
    },
  },
  props: {
    selectedAmcSell: Object,
  },
  mounted() {
    this.getUserList();
  },
  methods: {
    async getUserList() {
      try {
        const res = await this.$axios.get("/workforce/users");
        this.users = res.data?.users.map((el) => {
          return {
            ...el,
            fullName: `${el.firstName || ""} ${el.lastName || ""}`,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    closeDialog() {
      this.$emit("closeDialog");
    },
    handleClick() {
      const item = this.$props.selectedAmcSell;
      try {
        const payload = {
          amcSellId: item._id,
          serviceDoneOn: this.serviceDoneOn,
          serviceDoneBy: this.serviceDoneBy,
          serviceStatus: this.serviceStatus,
          nextServiceDueOn: this.nextServiceDueOn,
        };
        
        this.$axios.post("/workforce/ecom/amc/service", payload);
        this.closeDialog();
      } catch (error) {
        console.log(error);
      }
    },
    updateFormFields() {
      if (this.selectedAmcSell?.lastService) {
        this.serviceDoneOn = this.$dayjs(
          this.selectedAmcSell?.lastService?.serviceDoneOn
        ).format("YYYY-MM-DDTHH:mm");
        this.serviceDoneBy =
          this.selectedAmcSell?.lastService?.serviceDoneBy?._id;
        this.serviceStatus = this.selectedAmcSell?.lastService?.serviceStatus;
      }
    },
  },
};
</script>
