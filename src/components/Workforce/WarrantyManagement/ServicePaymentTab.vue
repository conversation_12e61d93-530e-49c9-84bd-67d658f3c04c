<template>
  <div class="bg-gray-100 dark-bg-custom">
    <div>
      <v-main style="padding: 0px; background-color: #f7fafb" class="bg-[#f7fafb] dark-bg-custom">
        <div>
          <v-col class="dark-bg-custom">
            <v-tabs v-model="tabs" align-with-title :color="$vuetify.theme.currentTheme.primary" class="bg-[#F7FAFB] dark-bg-custom">
              <v-tab
                :ripple="false"
                v-for="(tab, index) in tabItems"
                :key="index"
                class="mr-12 ml-2"
                :color="$vuetify.theme.currentTheme.primary"
              >
                {{ tab.name }}
              </v-tab>
              <v-tabs-slider color="#32D583"></v-tabs-slider>
            </v-tabs>
          </v-col>
        </div>
      </v-main>
      <div>
        <component
          :is="currentTab"
          :selectedAmcSell="$props.selectedAmcSell"
          @closeDialog="closeDialog"
        ></component>
      </div>
    </div>
  </div>
</template>

<script>
import AmcPayment from "./AmcPayment";
import AmcService from "./AmcService";
export default {
  name: "ServicePaymentTab",
  components: {
    AmcPayment,
    AmcService,
  },
  props: {
    selectedAmcSell: Object,
  },
  data() {
    return {
      tabs: 0,
    };
  },
  computed: {
    currentTab() {
      return this.tabItems[this.tabs]?.component;
    },
    tabItems() {
      return [
        {
          key: "AmcService",
          name: "Update Service Details",
          component: "AmcService",
        },
        {
          key: "AmcPayment",
          name: "Update Payment Details",
          component: "AmcPayment",
        },
      ];
    },
  },
  methods: {
    closeDialog() {
      this.$emit("closeDialog");
      this.tabs = 0;
    },
  },
  mounted() {},
};
</script>
<style scoped>
.v-tab:before {
  background-color: transparent;
}

.theme--light.v-tabs .v-tab:hover::before {
  opacity: 0;
}

.theme--light.v-tabs .v-tab--active:hover::before,
.theme--light.v-tabs .v-tab--active::before {
  opacity: 0;
}
.v-tab {
  font-size: 16px;
}
</style>
