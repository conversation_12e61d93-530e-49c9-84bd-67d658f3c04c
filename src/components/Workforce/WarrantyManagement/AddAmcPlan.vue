<template>
  <v-form
    ref="form"
    v-model="valid"
    lazy-validation
    class="p-12 bg-white dark-bg-custom rounded-lg mt-4"
  >
    <v-container>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Name:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="planName"
            outlined
            dense
            label="Plan Name"
            :rules="[(v) => !!v || `Plan name is required`]"
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Description:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="description"
            outlined
            dense
            label="Plan Description"
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Duration:</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="planDuration"
            label="Plan Duration"
            outlined
            :items="durationOptions"
            item-text="text"
            item-value="value"
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Payment Cycle:</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="paymentSchedule"
            label="Payment Cycle"
            outlined
            :items="paymentOptions"
            item-text="text"
            item-value="value"
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Schedule:</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="serviceSchedule"
            label="Paln Schedule"
            outlined
            :items="scheduleOptions"
            item-text="text"
            item-value="value"
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Coverage:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="coverage"
            outlined
            dense
            label="Coverage"
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Price:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="mrp"
            outlined
            dense
            label="Plan price"
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">GST:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="gst"
            outlined
            dense
            label="GST"
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Discount:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="discount"
            outlined
            dense
            label="Plan discount"
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Plan Status:</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="status"
            label="Plan Status"
            outlined
            :items="statusOptions"
            item-text="text"
            item-value="value"
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Total Price:</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="finalPrice"
            outlined
            dense
            readonly
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
    </v-container>

    <v-row>
      <v-col cols="12" class="text-right">
        <v-btn outlined class="px-12 mr-5" @click="cancel">Cancel</v-btn>
        <v-btn
          class="px-16 mr-3 white--text"
          :color="$vuetify.theme.currentTheme.primary"
          @click="savePlan"
          >{{ editedItem ? "Update" : "Add" }}</v-btn
        >
      </v-col>
    </v-row>
  </v-form>
</template>

<script>
export default {
  data() {
    return {
      planName: "",
      description: "",
      planDuration: 30,
      mrp: "",
      discount: "",
      gst: "",
      coverage: "",
      serviceSchedule: 7,
      paymentSchedule: 30,
      status: "ACTIVE",
      valid: true,
      statusOptions: [
        { text: "Active", value: "ACTIVE" },
        { text: "In Active", value: "INACTIVE" },
      ],
      durationOptions: [
        { text: "1 Month", value: 30 },
        { text: "3 Months", value: 90 },
        { text: "6 Months", value: 180 },
        { text: "1 Year", value: 365 },
        { text: "2 Years", value: 730 },
        { text: "3 Years", value: 1095 },
        { text: "4 Years", value: 1460 },
        { text: "5 Years", value: 1825 },
      ],
      scheduleOptions: [
        { text: "Weekly", value: 7 },
        { text: "Monthly", value: 30 },
        { text: "Quarterly", value: 90 },
        { text: "Half Yearly", value: 180 },
        { text: "Yearly", value: 365 },
      ],
      paymentOptions: [
        { text: "Monthly", value: 30 },
        { text: "Quarterly", value: 90 },
        { text: "Half Yearly", value: 180 },
        { text: "Yearly", value: 365 },
      ],
    };
  },
  props: {
    editedItem: {
      type: Object,
    },
  },
  computed: {
    finalPrice() {
      const mrp = parseFloat(this.mrp) || 0;
      const gst = parseFloat(this.gst) || 0;
      const discount = parseFloat(this.discount) || 0;

      return mrp + gst - discount;
    },
  },
  created() {
    if (this.editedItem) {
      this.planName = this.editedItem?.planName;
      this.description = this.editedItem?.planDescription;
      this.planDuration = this.editedItem?.planDuration;
      this.paymentSchedule = this.editedItem?.paymentSchedule;
      this.serviceSchedule = this.editedItem?.serviceSchedule;
      this.status = this.editedItem?.status;
      this.mrp = this.editedItem?.priceBreakup?.mrp;
      this.gst = this.editedItem?.priceBreakup?.gst;
      this.discount = this.editedItem?.priceBreakup?.discount;
      this.coverage = this.editedItem?.coverage[0];
    }
  },
  methods: {
    async savePlan() {
      try {
        const isValid = this.$refs.form.validate();
        if (!isValid) return;
        const method = this.editedItem ? "put" : "post";
        const endpoint = this.editedItem
          ? `/workforce/ecom/amcplan/${this.editedItem._id}`
          : "/workforce/ecom/amcplan";
        const payload = {
          planName: this.planName,
          planDescription: this.description,
          planDuration: this.planDuration,
          serviceSchedule: this.serviceSchedule,
          paymentSchedule: this.paymentSchedule,
          coverage: this.coverage,
          status: this.status,
          priceBreakup: {
            mrp: this.mrp,
            gst: this.gst,
            discount: this.discount,
          },
          finalPrice: this.finalPrice,
        };
        await this.$axios({
          method: method,
          url: endpoint,
          data: payload,
        });
        this.cancel();
      } catch (error) {
        console.error("Error:", error);
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage);
      }
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>
