<template>
  <v-card class="p-4">
    <div>
      <v-form v-model="valid">
        <v-text-field
          label="Transaction Number"
          v-model="transactionNumber"
          outlined 
          dense
          hide-details
          class="mb-3"
        ></v-text-field>
        <v-select
          label="Payment Mode"
          :items="paymentOptions"
          v-model="paymentStatus"
          outlined 
          dense
          hide-details
          class="mb-3"
        >
        </v-select>
        <v-text-field
          label="Payment Date"
          type="datetime-local"
          v-model="paidOn"
          outlined 
          dense
          hide-details
          class="mb-3"
        ></v-text-field>
        <v-text-field label="Amount Paid" v-model="amountPaid"   
          outlined 
          dense
          hide-details
          class="mb-3">
        </v-text-field>
        <v-text-field
          label="Payment Source"
          v-model="paymentSourceOrg"
          outlined 
          dense
          hide-details
          class="mb-3"
        ></v-text-field>
        <div class="flex items-center justify-between">
          <div class="w-full">
            <v-btn
              class="font-bold rounded-md"
              outlined
              block
              @click="closeDialog"
              >Cancel</v-btn
            >
          </div>
          <div class="p-2" />
          <div class="w-full">
            <v-btn
              @click="handleClick"
              block
              class="white--text rounded-md"
              :color="$vuetify.theme.currentTheme.primary"
            >
              Update
            </v-btn>
          </div>
        </div>
      </v-form>
    </div>
  </v-card>
</template>
<script>
export default {
  data() {
    return {
      valid: true,
      transactionNumber: null,
      paymentStatus: "Cash",
      amountPaid: null,
      paidOn: this.$dayjs().format("YYYY-MM-DDTHH:mm"),
      paymentSourceOrg: "",
      paymentOptions: [
        {
          text: "Cash",
          value: "CASH",
        },
        {
          text: "Card",
          value: "CARD",
        },
        {
          text: "Cheque",
          value: "CHEQUE",
        },
        {
          text: "Bank Transfer",
          value: "BANK_TRANSFER",
        },
        {
          text: "UPI",
          value: "UPI",
        },
      ],
    };
  },
  watch: {
    selectedAmcSell: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.updateFormFields();
        }
      },
    },
  },
  props: {
    selectedAmcSell: Object,
  },
  methods: {
    closeDialog() {
      this.$emit("closeDialog");
    },
    handleClick() {
      const item = this.$props.selectedAmcSell;
      try {
        const payload = {
          amcSellId: item._id,
          transactionNumber: this.transactionNumber,
          paymentMode: this.paymentStatus,
          paymentSourceOrg: this.paymentSourceOrg,
          amountPaid: this.amountPaid,
          paidOn: this.paidOn,
        };
        this.$axios.post("/workforce/ecom/amc/payment", payload);
        this.closeDialog();
      } catch (error) {
        console.log(error);
      }
    },
    updateFormFields() {
      if (this.selectedAmcSell?.lastPayment) {
        this.transactionNumber =
          this.selectedAmcSell?.lastPayment?.transactionNumber;
        this.amountPaid = this.selectedAmcSell?.lastPayment?.amountPaid;
        this.paymentSourceOrg =
          this.selectedAmcSell?.lastPayment?.paymentSourceOrg;
        this.paidOn = this.$dayjs(
          this.selectedAmcSell?.lastPayment?.paidOn
        ).format("YYYY-MM-DDTHH:mm");
        this.paymentStatus = this.selectedAmcSell?.lastPayment?.paymentMode;
      }
    },
  },
};
</script>
