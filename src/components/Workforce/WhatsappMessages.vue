<template>
  <div class="bg-white">
    <div class="flex gap-2 items-center justify-end absolute z-20 -top-14 pb-2 right-0">
      <div class="w-72">
        <v-col class="p-0">
          <v-autocomplete
            v-model="messageStatus"
            label="Select Status"
            placeholder="Select Status"
            :items="messageStatusOptions"
            item-text="label"
            item-value="value"
            hide-details
            outlined
            dense
            clearable
            @change="fetchMessages"
          ></v-autocomplete>
        </v-col>
      </div>
      <div class="w-72">
        <v-col class="py-0">
          <v-menu
            v-model="menu"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
          >
            <template v-slot:activator="{ on }">
              <v-text-field
                label="Select Date"
                v-model="date"
                prepend-inner-icon="mdi-calendar"
                readonly
                hide-details
                outlined
                dense
                clearable
                v-on="on"
                @click:clear="onDateCleared"
              ></v-text-field>
            </template>
            <v-date-picker v-model="date" no-title scrollable @change="menu = false" />
          </v-menu>
        </v-col>
      </div>
    </div>
    <div v-if="!messages.length" class="flex flex-col items-center justify-center py-16">
      <no-data title="Nothing to Display" :subTitle="`No messages log found for selected date`" />
    </div>
    <div v-else class="mt-4">
      <v-data-table
        :headers="messageTableHeaders"
        :items="messages"
        :loading="fetchingData"
        class="pl-7 pr-7 pt-4 rounded-lg"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        item-key="_id"
        fixed-header
      >
        <template v-slot:item.triggeredTo="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.triggeredTo }}
          </td>
        </template>
        <template v-slot:item.status="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.status }}</td>
        </template>
        <template v-slot:item.triggerType="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.triggerType }}</td>
        </template>
        <template v-slot:item.message="{ item }">
          <td class="px-4 py-6 font-semibold">{{ item.message }}</td>
        </template>
        <template v-slot:item.createdAt="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ formatDate(item.createdAt) }}
          </td>
        </template>
      </v-data-table>
    </div>
  </div>
</template>

<script>
import { callStatusMap } from '@/utils/workforce/utils.js'
import dayjs from 'dayjs'
import NoData from '@/components/NoData.vue'

export default {
  name: 'CampaignsPage',
  layout: 'Workforce/default',
  components: {
    NoData
  },
  data() {
    return {
      authData: this.$storage.getUniversal('token'),
      isAddCampaignVisible: false,
      messages: [],
      activeSection: 0,
      name: '',
      menu: false,
      messageStatus: '',
      singleExpand: true,
      expanded: [],
      startDate: null,
      endDate: null,
      dates: [],
      date: dayjs().format('YYYY-MM-DD'),
      description: '',
      campaigns: [],
      fetchingData: false,
      updatingCampaignStatus: false,
      deletingCampaign: false,
      orgConfig: null,
      currentCampaignCases: [],
      expandedHeaders: [{ text: 'Case No', value: 'caseNo', sortable: false }]
    }
  },
  watch: {
    date(date) {
      this.fetchMessages(null, date)
    },
    expanded: {
      handler(ids) {
        if (!ids.length) return
        this.currentCampaignCases = []
        this.expandedHeaders = []
        const campaignId = ids[0]._id
        const campaignIndx = this.campaigns.findIndex((c) => c._id === campaignId)
        this.fetchCampaign(campaignId, campaignIndx)
      }
    },
    activeSection: {
      handler() {
        this.fetchCurrentCampaigns()
      },
      immediate: false
    }
  },
  methods: {
    async fetchMessages(status, date) {
      this.fetchingData = true

      const response = await this.$axios.get('/workforce/whatsapp/templates/messages', {
        params: {
          status: status || this.messageStatus || undefined,
          date: date || this.date ? dayjs(date || this.date).toDate() : undefined
        },
        headers: {
          Authorization: this.authData.access_token
        }
      })

      this.messages = response.data.messages
      this.fetchingData = false
    },
    redirectToCase(caseData) {
      const subCases = this.orgConfig.enabledSubCases[0].subCase
      const subCase = subCases.find((s) => s.name === caseData.caseType)

      const redirectUrl = `/workforce/collection-management/${subCase._id}?caseNo=${caseData.caseNo}`
      window.open(redirectUrl, '_blank')
    },
    onDateCleared() {
      this.dates = []
      this.startDate = null
      this.endDate = null
      this.fetchCurrentCampaigns()
    },
    onDateSelected() {
      if (this.dates.length === 2) {
        this.startDate = this.$dayjs(this.dates[0]).format('YYYY-MM-DD')
        this.endDate = this.$dayjs(this.dates[1]).format('YYYY-MM-DD')
        this.fetchCurrentCampaigns()
      } else {
        this.startDate = this.$dayjs(this.dates[0]).format('YYYY-MM-DD')
        this.endDate = null
      }
    },
    callDialStatus(response) {
      const { EVENT_TYPE = '', A_DIAL_STATUS = '', B_DIAL_STATUS = '' } = response

      const key = `${EVENT_TYPE.trim()}|${A_DIAL_STATUS.trim()}|${B_DIAL_STATUS.trim()}`

      return callStatusMap.get(key)
    },
    populateExpandedHeaders(casesData) {
      const subCases = this.orgConfig.enabledSubCases[0].subCase
      const subCase = subCases.find((s) => s.name === casesData[0].case.caseType)
      const fieldMappings = subCase.fieldMappings
      this.expandedHeaders = [
        {
          text: fieldMappings.caseNo.displayName,
          value: 'caseNo',
          sortable: false
        },
        {
          text: fieldMappings.assignedTo.displayName,
          value: 'assignedTo',
          sortable: false
        },
        {
          text: fieldMappings.customerFullName.displayName,
          value: 'customer',
          sortable: false
        },
        {
          text: 'Status',
          value: 'status',
          sortable: false
        }
      ]
    },
    formatDate(date) {
      return dayjs(date).format('DD MMM YYYY, hh:mm a')
    },
    onCancelAddCampaign() {
      this.isAddCampaignVisible = false
    },
    toggleShowAddCampaign() {
      this.isAddCampaignVisible = !this.isAddCampaignVisible
    },
    onCreateCampaign() {
      this.isAddCampaignVisible = false
      this.fetchCurrentCampaigns()
    },
    async fetchCampaign(campaignId, campaignIndx) {
      try {
        if (!this.orgConfig.enabledSubCases.length) return
        const cachedCases = this.campaigns[campaignIndx].cases || []

        if (cachedCases[0]?.caseNo) {
          return this.populateExpandedHeaders(cachedCases)
        }

        const response = await this.$axios.get(`/workforce/campaign/${campaignId}`)
        const campaign = response.data.data
        this.campaigns[campaignIndx] = campaign
        this.currentCampaignCases = campaign.cases
        this.populateExpandedHeaders(campaign.cases)
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async fetchConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config

        this.orgConfig = data
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async startCampaign({ campaignId, indx }) {
      const newCampaigns = [...this.campaigns]
      newCampaigns[indx].cancelling = true
      this.campaigns = newCampaigns

      const response = await this.$axios.post(
        '/workforce/campaign',
        {
          campaignId,
          populate: true
        },
        {
          headers: {
            Authorization: this.authData.access_token
          }
        }
      )

      if (response.data.success) {
        newCampaigns[indx].starting = false
        newCampaigns[indx].status = 'INITIALIZED'
        this.campaigns = newCampaigns
        this.$toast.success('Campaign started successfully')
      }
    },
    async cancelCampaign({ campaignId, indx }) {
      const newCampaigns = [...this.campaigns]
      newCampaigns[indx].cancelling = true
      this.campaigns = newCampaigns

      await this.$axios.put(
        '/workforce/campaign/status',
        {
          campaignId,
          status: '2',
          channelType: 'call'
        },
        {
          headers: {
            Authorization: this.authData.access_token
          }
        }
      )
      newCampaigns[indx].cancelling = false
      newCampaigns[indx].status = 'CANCELLED'
      this.campaigns = newCampaigns
      this.$toast.success('Campaign cancelled successfully')
    },
    async stopCampaign({ campaignId }) {
      const response = await this.$axios.post(
        '/workforce/campaign/status',
        {
          campaignId,
          status: 'CANCELLED'
        },
        {
          headers: {
            Authorization: this.authData.access_token
          }
        }
      )

      if (response.data.success) {
        this.$toast.success('Campaign stopped successfully')
      }
    },
    async updateCampaignStatus({ campaignId, status, indx }) {
      const newCampaigns = [...this.campaigns]
      newCampaigns[indx].updatingStatus = true
      this.campaigns = newCampaigns

      await this.$axios.put(
        '/workforce/campaign/status',
        {
          campaignId,
          status,
          channelType: 'call'
        },
        {
          headers: {
            Authorization: this.authData.access_token
          }
        }
      )
      newCampaigns[indx].updatingStatus = false
      newCampaigns[indx].status = status === '1' ? 'PAUSED' : 'IN_PROGRESS'
      this.campaigns = newCampaigns
      this.$toast.success(
        status === '1' ? 'Campaign paused successfully' : 'Campaign resumed successfully'
      )
    },
    async fetchCampaignsHistory() {
      this.fetchingData = true
      const response = await this.$axios.get('/workforce/campaign/campaigns?history=true', {
        headers: {
          Authorization: this.authData.access_token
        }
      })
      this.fetchingData = false

      this.campaigns = response.data.data.map((c, i) => ({
        ...c,
        indx: i,
        id: c.channelType === 'call' ? c.channelOptions?.campaignId : '-'
      }))
    },
    async fetchCurrentCampaigns() {
      this.fetchingData = true
      const response = await this.$axios.get('/workforce/campaign/campaigns', {
        params: {
          status: this.messageStatus || undefined,

          start: this.startDate || undefined,
          end: this.endDate || undefined
        },
        headers: {
          Authorization: this.authData.access_token
        }
      })
      this.fetchingData = false

      this.campaigns = response.data.data.map((c, i) => ({
        ...c,
        indx: i,
        id: c.channelType === 'call' ? c.channelOptions?.campaignId : '-'
      }))
    }
  },
  computed: {
    messageStatusOptions() {
      return [
        { label: 'Read', value: 'read' },
        { label: 'Delivered', value: 'delivered' }
      ]
    },
    formattedDate() {
      if (!this.startDate || !this.endDate) return 'Select Date Range'
      return `${this.startDate} to ${this.endDate}`
    },
    messageTableHeaders() {
      const headers = [
        { text: 'Sent to', value: 'triggeredTo', sortable: false },
        { text: 'Trigger type', value: 'triggerType', sortable: false },
        { text: 'Status', value: 'status', sortable: false },
        { text: 'Message', value: 'message', sortable: false },
        { text: 'Triggered At', value: 'createdAt', sortable: false }
      ]

      return headers
    }
  },
  mounted() {
    this.fetchMessages()

    this.fetchCurrentCampaigns()
    this.fetchConfig()
  }
}
</script>

<style scoped></style>
