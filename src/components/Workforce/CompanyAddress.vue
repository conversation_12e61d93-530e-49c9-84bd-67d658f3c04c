<template>
  <div>
    <div ref="map"></div>
    <div>
      <v-card class="p-2 w-[36rem]">
        <div
          class="flex justify-between sticky top-0 bg-white dark-bg-custom z-50 align-center"
          v-if="title"
        >
          <v-card-title
            class="flex justify-between items-center"
            v-if="!editBranchData"
          >
            {{ title }}
            <v-icon @click="onCancel">mdi-close</v-icon></v-card-title
          >
        </div>
        <div class="p-4">
          <v-form ref="form" lazy-validation>
            <div>
              <v-select
                :items="companyTypeAdd"
                v-model="selectedType"
                label="Select Addition Type"
                outlined
                dense
                hide-details
                class="mb-3"
              ></v-select>
            </div>
            <v-text-field
              v-if="!company"
              v-model="companyName"
              :label="useCaseLeadConfig?.addCompanyForm?.companyName + ' *'"
              :rules="rules.companyName"
              hint="Required *"
              persistent-hint
              required
              clearable
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-text-field
              v-if="company"
              v-model="alias"
              label="Alias (DBA)"
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-autocomplete
              v-model="companyBranch"
              @change="onChange"
              @update:search-input="getLocationSuggestions"
              label="Search Address"
              :items="branches"
              :filter="customFilter"
              item-text="label"
              :rules="rules.companyBranch"
              item-value="value"
              v-if="selectedType === companyTypeAdd[0]"
              hide-no-data
              required
              clearable
              outlined
              dense
              hide-details
              class="mb-3"
            >
              <template v-slot:item="{ item }">
                <v-list-item-content class="w-[600px]">
                  <v-list-item-title>{{ item.label }}</v-list-item-title>
                </v-list-item-content>
              </template>
            </v-autocomplete>
            <v-row v-if="showLatLon">
              <v-col>
                <v-text-field
                  v-model="latitude"
                  label="Latitude"
                  outlined
                  dense
                  hide-details
                  :rules="rules.latitude"
                  class="mb-3"
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  v-model="longitude"
                  label="Longitude"
                  :rules="rules.latitude"
                  outlined
                  dense
                  hide-details
                  class="mb-3"
                ></v-text-field>
              </v-col>
              <v-btn
                :color="$vuetify.theme.currentTheme.primary"
                class="mt-3 white--text"
                @click="fetchAddress"
                >Add</v-btn
              >
            </v-row>
            <v-text-field
              v-if="shouldShowFormFields && !company"
              v-model="companyDetails"
              v-bind:label="
                this.useCaseLeadConfig?.addCompanyForm?.companyDetails
              "
              required
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-text-field
              v-model="addLine1"
              :rules="rules.addLine1"
              label="Full Address"
              v-if="shouldShowFormFields"
              required
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-text-field
              v-model="addLine2"
              :label="this.useCaseLeadConfig?.addCompanyForm?.addressLine1"
              v-if="shouldShowFormFields"
              required
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-text-field
              v-model="city"
              v-if="shouldShowFormFields"
              v-bind:label="this.useCaseLeadConfig?.addCompanyForm?.cityName"
              required
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-text-field
              v-model="state"
              v-bind:label="this.useCaseLeadConfig?.addCompanyForm?.stateName"
              v-if="shouldShowFormFields"
              required
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-text-field
              v-model="country"
              v-if="shouldShowFormFields"
              v-bind:label="this.useCaseLeadConfig?.addCompanyForm?.country"
              required
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <v-text-field
              v-model="pinCode"
              v-if="shouldShowFormFields"
              v-bind:label="this.useCaseLeadConfig?.addCompanyForm?.pinCode"
              required
              outlined
              dense
              hide-details
              class="mb-3"
            ></v-text-field>
            <div class="flex mt-4 gap-2 items-center justify-between">
              <div class="w-full">
                <v-btn outlined block @click="onCancel">Cancel</v-btn>
              </div>
              <div class="w-full">
                <v-btn
                  @click="onSave"
                  block
                  class="white--text rounded-md"
                  :color="$vuetify.theme.currentTheme.primary"
                  >{{ editBranchData ? "Update" : "Create" }}</v-btn
                >
              </div>
            </div>
          </v-form>
        </div>
      </v-card>
    </div>
  </div>
</template>

<script>
/* global google */
import { debounce } from "@/utils/common";
export default {
  props: {
    title: String,
    company: {
      type: String,
      default: "",
    },
    companies: Array,
    editBranchData: Object,
    customerAddresses: Array,
    isOpen: Boolean,
  },
  data() {
    return {
      orgLang: this.$store.state.orgLanguage,
      useCase: this.$store.state.useCase,
      companyName: "",
      companyDetails: "",
      showAdditionalFields: false,
      branches: [],
      companyTypeAdd: ["Auto Fill", "Latitude and Longitude", "Manual"],
      selectedType: this.editBranchData ? "Manual" : "Auto Fill",
      latitude: "",
      longitude: "",
      latLonAddress: "",
      alias: "",
      companyBranch: "",
      addLine1: "",
      addLine2: "",
      city: "",
      state: "",
      country: "",
      pinCode: "",
      map: null,
    };
  },
  computed: {
    rules() {
      return {
        companyName: [
          (val) => (val || "").length > 0 || "Company name is required",
          (val) =>
            !this.validateCompanyName(val) || "Company is already present",
        ],
        companyBranch: [
          (val) => (val || "").length > 0 || "Address is required",
        ],
        addLine1: [
          (val) => (val || "").length > 0 || "Address line 1 is required",
        ],
        latitude: [(val) => (val || "").length > 0 || "Latitude is required"],
        longitude: [(val) => (val || "").length > 0 || "Longitude is required"],
      };
    },
    showLatLon() {
      return (
        this.showAdditionalFields ||
        this.selectedType === this.companyTypeAdd[1]
      );
    },
    showBranchField() {
      return this.selectedType === this.companyTypeAdd[0];
    },
    shouldShowFormFields() {
      return (
        this.companyBranch ||
        this.latLonAddress ||
        this.selectedType === this.companyTypeAdd[2]
      );
    },
    useCaseLeadConfig() {
      const currentUsecase = this.useCase[0];
      const orgConfig = this.orgLang.data || {};
      const leadModules = orgConfig.leadModules || [];
      const currentConfig = leadModules.find(
        (m) => m.useCaseType === currentUsecase
      );
      if (currentConfig) {
        return currentConfig;
      }
      return leadModules[0];
    },
  },
  mounted() {
    this.addGoogleMapsScript();
  },
  created() {
    if (this.editBranchData) {
      this.$porp.company = this.editBranchData?.name;
      this.alias = this.editBranchData?.alias;
      this.companyDetails = this.editBranchData?.description;
      this.addLine1 = this.editBranchData?.address?.addressLine1;
      this.addLine2 = this.editBranchData?.address?.addressLine2;
      this.city = this.editBranchData?.address?.city;
      this.state = this.editBranchData?.address?.state;
      this.country = this.editBranchData?.address?.country;
      this.pinCode = this.editBranchData?.address?.pinCode;
    }
    if (this.$props.customerAddresses) {
      this.$props.company = this.$props.customerAddresses[0]?.companyId || "";
    }
  },
  methods: {
    async fetchAddress() {
      try {
        const params = {
          latitude: this.latitude,
          longitude: this.longitude,
        };

        const response = await this.$axios.post(
          "/workforce/location/ondemand",
          params
        );

        const addressComponents = response.data.data.address_components;

        if (addressComponents && addressComponents.length > 0) {
          this.addLine1 = response.data.data.formatted_address;
          addressComponents.forEach((component) => {
            switch (component.types[0]) {
              case "neighborhood":
                this.addLine2 = component.long_name;
                break;
              case "locality":
                this.city = component.long_name;
                break;
              case "administrative_area_level_1":
                this.state = component.long_name;
                break;
              case "postal_code":
                this.pinCode = component.long_name;
                break;
              case "country":
                this.country = component.long_name;
                break;
              default:
                break;
            }
            this.selectedType = "Manual";
          });
        } else {
          this.$toast.error("Please recheck the latitude and longitude");
        }
      } catch (error) {
        console.error(error);
      }
    },
    onChange(val) {
      if (val === "Add") {
        this.showAdditionalFields = true;
      } else {
        this.showAdditionalFields = false;
      }

      const request = {
        placeId: val,
        fields: ["address_component", "formatted_address", "name"],
      };
      const service = new google.maps.places.PlacesService(this.map);
      service.getDetails(request, (place, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK) {
          const addressComponents = place.address_components;
          const branchData = {};

          for (const component of addressComponents) {
            for (const type of component.types) {
              if (type === "locality") {
                branchData.city = component.long_name;
              } else if (type === "administrative_area_level_1") {
                branchData.state = component.long_name;
              } else if (type === "route") {
                branchData.address = component.long_name;
              } else if (type === "postal_code") {
                branchData.pinCode = component.long_name;
              } else if (type === "country") {
                branchData.country = component.long_name;
              } else if (type === "street_number") {
                branchData.streetNumber = component.long_name;
              } else if (type === "sublocality") {
                branchData.sublocality = component.long_name;
              }
            }
          }

          this.addLine1 = branchData.address || place.formatted_address;
          this.addLine2 = branchData.sublocality;
          this.city = branchData.city;
          this.state = branchData.state;
          this.country = branchData.country;
          this.pinCode = branchData.pinCode;
        }
      });
    },
    async resetForm() {
      this.companyName = "";
      this.companyDetails = "";
      this.alias = "";
      this.companyBranch = "";
      this.addLine1 = "";
      this.branches = [];
      this.addLine2 = "";
      this.latitude = "";
      this.longitude = "";
      this.city = "";
      this.state = "";
      this.country = "";
      this.pinCode = "";
      this.selectedType = "Auto Fill";
      await this.$nextTick();
      this.$refs.form?.resetValidation();
    },
    onSave() {
      const isValid = this.$refs.form.validate();
      if (!isValid) return;
      const address = `${this.addLine1}, ${this.addLine2}, ${this.city}, ${this.state}, ${this.country}, ${this.pinCode}`;

      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ address: address }, (results, status) => {
        if (status === "OK") {
          const location = results[0].geometry.location;
          this.latitude = location.lat();
          this.longitude = location.lng();

          const emitData = {
            companyName: this.companyName,
            alias: this.alias,
            companyDetails: this.companyDetails,
            addLine1: this.addLine1,
            addLine2: this.addLine2,
            city: this.city,
            state: this.state,
            country: this.country,
            pinCode: this.pinCode,
            latitude: this.latitude,
            longitude: this.longitude,
          };
          if (this.editBranchData) {
            emitData.companyId = this.editBranchData?.companyId;
            emitData.branchId = this.editBranchData?._id;
          }
          this.$emit("save", emitData);

          this.resetForm();
        } else {
          console.error(
            "Geocode was not successful for the following reason: " + status
          );
        }
      });
    },

    onCancel() {
      this.resetForm();
      this.$emit("cancel");
    },
    validateCompanyName(val) {
      const formattedInput = val?.trim().toLowerCase().replace(/\s/g, "");
      return this.companies?.some(
        (company) =>
          company.name.toLowerCase().replace(/\s/g, "") === formattedInput
      );
    },

    async addGoogleMapsScript() {
      const scriptTag = document.querySelector(
        'script[src*="maps.googleapis.com"]'
      );
      if (!scriptTag) {
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.VUE_APP_MAP_KEY}&libraries=places&callback=Function.prototype`;
        script.defer = true;
        script.async = true;
        script.onload = this.initMap; // Initialize the map after the script is loaded
        document.head.appendChild(script);
      } else {
        setTimeout(() => {
          this.initMap();
        }, 1000);
      }
    },
    async initMap() {
      try {
        const mapOptions = {
          center: { lat: 37.7749, lng: -122.4194 },
          zoom: 15,
        };
        this.map = new window.google.maps.Map(this.$refs.map, mapOptions);
      } catch (error) {
        console.error("An error occurred while initializing the map:", error);
      }
    },
    customFilter() {
      return true;
    },

    getLocationSuggestions: debounce(function (searchTxt) {
      if (!searchTxt) {
        this.branches = [];
        return;
      }
      const placesService = new google.maps.places.PlacesService(
        document.createElement("div")
      );

      placesService.textSearch(
        {
          query: searchTxt + " in India",
          componentRestrictions: { country: "IN" },
        },
        (results, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK) {
            this.branches = results.map((result) => ({
              label: result.formatted_address,
              value: result.place_id,
            }));
          } else {
            this.branches = [];
          }
        }
      );
    }, 1000),
  },
};
</script>
