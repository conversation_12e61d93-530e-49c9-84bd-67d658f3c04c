<template>
  <div>
    <v-card>
      <div class="flex justify-between align-center">
        <v-card-title class="text-md">Select File(XLSX)</v-card-title>
        <!-- <v-btn
          outlined
          small
          @click="openOrgMapping"
          :color="$vuetify.theme.currentTheme.primary"
          class="mr-2"
          >Verify columns before create/update</v-btn
        > -->
        <v-btn
          outlined
          small
          @click="downloadSample"
          :color="$vuetify.theme.currentTheme.primary"
          class="mr-2"
          >Sample Download</v-btn
        >
      </div>
      <div class="px-5 text-sm">
        <v-alert type="info" color="blue" outlined v-if="!caseConfig"
          ><strong> Address Types: </strong>
          For adding multiple addresses use below prefix for diffrent types of addresses.
          <br />
          <strong>PA_</strong> - PERMANENT ADDRESS
          <br />
          <strong>MA_</strong> - MAILING ADDRESS
          <br />

          <strong>CA_</strong> - CURRENT ADDRESS
          <br />

          <strong>BA_</strong> - BILLING ADDRESS
          <br />

          <strong>OA_</strong> - OFFICE ADDRESS
          <br />
          <strong>RA_</strong> - RESIDENTIAL ADDRESS
        </v-alert>
        <v-alert type="info" color="blue" outlined
          ><strong> Gender Types: </strong>
          For adding gender use below options
          <br />
          <strong>MALE</strong> or <strong>M</strong> - for Male
          <br />
          <strong>FEMALE</strong> or <strong>F</strong> - for Female
          <br />
          <strong>OTHERS</strong> - for Others
          <br />
          <strong>NOT_APPLICABLE</strong> - for NA
        </v-alert>
        <v-alert type="info" color="blue" outlined
          ><strong> Mobile Number: </strong>
          For adding multiple number use columns name like
          <strong> "Customer Number 1" </strong>
          ,
          <strong> "Customer Number 2" </strong>
          , etc.
        </v-alert>
      </div>
      <v-card-text>
        <v-file-input
          label="Choose a file"
          v-model="selectedFile"
          multiple
          accept=".xlsx"
        ></v-file-input>
        <v-progress-linear
          v-if="uploadProgress !== null"
          :value="uploadProgress"
          absolute
          bottom
        ></v-progress-linear>
        <div v-if="uploadStatus">
          <p>Success : {{ created }}</p>
          <div class="flex align-center justify-between">
            <p>Failed : {{ failed }}</p>
            <!-- <v-btn small v-if="failed" @click="downloadFile">
              Download Failed Status<v-icon color="red">mdi-download</v-icon>
            </v-btn> -->
          </div>
        </div>
        <div v-if="invalid && invalid.length > 0" class="mt-4">
          <v-alert type="error" text outlined class="mt-2" :icon="false">
            <strong>Failed Upload Details:</strong>
            <div style="max-height: 300px; overflow-y: auto">
              <v-list dense>
                <v-list-item v-for="(item, index) in invalid" :key="index">
                  <v-list-item-content>
                    <v-list-item-title>
                      {{ index + 1 }} - {{ caseConfig.fieldMappings?.caseNo?.displayName }}:
                      {{ getDynamicField(item) }} - Reason:
                      <span class="multiline-text">{{ item.failedReason }}</span>
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </div>
          </v-alert>
        </div>
        <v-alert
          v-else-if="validationErrors.length > 0 || validationPassed"
          :type="validationPassed ? 'success' : 'error'"
          text
          outlined
          class="mt-2"
          :icon="false"
        >
          <strong v-if="!validationPassed"
            >Validation failed: Please ensure the columns in your uploaded file match the required
            headers listed below. The file may have missing or mismatched headers.</strong
          >
          <strong v-else>Validation passed: Column mapping matched successfully!</strong>
          <v-divider class="my-2" v-if="!validationPassed"></v-divider>
          <ul v-if="!validationPassed">
            <li v-for="(error, index) in validationErrors" :key="index">
              {{ error }}
            </li>
          </ul>
          <p v-else>
            Please click on <strong>{{ caseAction }}</strong> to proceed.
          </p>
        </v-alert>
        <v-alert v-if="uploadStatus" :type="uploadStatusType" class="mt-4">{{
          uploadStatus
        }}</v-alert>

        <!-- matched header alert -->

        <v-alert
          v-if="matchedHeaders.length && !validationPassed"
          type="success"
          text
          outlined
          class="mt-2"
          :icon="false"
        >
          <strong>Validation passed: Below Column matched successfully!</strong>
          <v-divider class="my-2" v-if="!validationPassed"></v-divider>
          <ul v-if="!validationPassed">
            <li v-for="(passedCol, index) in matchedHeaders" :key="index">
              {{ passedCol }}
              <v-icon> mdi-check </v-icon>
            </li>
          </ul>
        </v-alert>
      </v-card-text>
      <v-card-actions class="flex items-center justify-evenly mb-4">
        <v-btn @click="closeUploadDialog" class="rounded-md w-40" outlined>Close</v-btn>
        <v-btn
          @click="handleAction"
          :loading="uploading"
          :disabled="!selectedFile || uploading"
          class="white--text rounded-md w-40"
          :color="$vuetify.theme.currentTheme.primary"
        >
          {{ validationPassed ? 'Create' : 'Validate' }}
        </v-btn>
      </v-card-actions>
    </v-card>
    <v-dialog v-model="showTableMapping" max-width="53rem">
      <OrgMapping :editSubCase="subCases" :isUploadCase="isUploadCase" @cancel="closeMapping" />
    </v-dialog>
  </div>
</template>
<script>
import OrgMapping from '@/components/Workforce/Settings/OrgMapping'
import { extractExcelHeaders } from '@/utils/common'
export default {
  data() {
    return {
      selectedFile: null,
      uploading: false,
      created: null,
      failed: null,
      invalid: null,
      uploadProgress: null,
      uploadStatus: '',
      showTableMapping: false,
      isUploadCase: false,
      subCases: {},
      headers: [],
      validationErrors: [],
      matchedHeaders: [],
      validationPassed: false
    }
  },
  components: {
    OrgMapping
  },
  props: {
    caseConfig: Object,
    caseAction: String
  },
  computed: {
    uploadStatusType() {
      return this.uploadStatus === 'File uploaded successfully' ? 'success' : 'error'
    }
  },
  watch: {
    selectedFile: {
      handler(newFile) {
        if (newFile) {
          this.clearData()
        }
      }
    }
  },
  methods: {
    uploadFile() {
      if (!this.selectedFile) {
        return
      }
      const formData = new FormData()

      this.selectedFile.forEach((file) => {
        formData.append('attachments', file)
      })

      if (this.caseConfig?.branch) {
        formData.append('branch', this.caseConfig?.branch)
      }
      formData.append('caseType', this.caseConfig?.name)

      const config = {
        onUploadProgress: (progressEvent) => {
          this.uploadProgress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
        }
      }

      this.uploading = true
      this.$axios
        .post('/workforce/customer/bulk', formData, config)
        .then((response) => {
          this.created = response.data?.data?.customerCreatedSuccessfully
          this.failed = response.data?.data?.customerCreationFailed
          this.invalid = response.data?.data?.invalidCustomers
          if (this.created) {
            this.uploadStatus = 'File uploaded successfully'
            this.$emit('updateUploadData')
          } else {
            this.uploadStatus = 'File uploading failed'
          }
        })
        .catch((error) => {
          this.uploadStatus = 'Error uploading file'
          console.error('Error uploading file:', error)
        })
        .finally(() => {
          this.uploading = false
        })
    },
    closeUploadDialog() {
      this.clearData()
      this.selectedFile = null
      this.$emit('close')
    },
    clearData() {
      this.created = ''
      this.failed = ''
      this.invalid = []
      this.uploadStatus = ''
      this.validationErrors = []
      this.matchedHeaders = []
      this.validationPassed = false
    },
    openOrgMapping() {
      this.showTableMapping = true
      this.isUploadCase = true
      this.subCases = { ...this.$props.caseConfig }
    },
    closeMapping() {
      this.showTableMapping = false
    },
    downloadSample() {
      const XLSX = require('xlsx')
      const restructuredData = this.prepareData()
      const caseType = this.caseConfig?.name
      const workbook = XLSX.utils.book_new()
      const worksheet = XLSX.utils.aoa_to_sheet(restructuredData)
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      XLSX.writeFile(workbook, `${caseType}_Case.xlsx`)
    },

    prepareData() {
      const tableData = []
      const uniqueHeaders = [
        'crn',
        'First Name',
        'Last Name',
        'Middle Name',
        'Client Name',
        'Date of birth',
        'Gender',
        'Customer Email',
        'Customer Number 1',
        'Customer Number 2',
        'Occupation',
        'Company',
        'Jobtype',
        'Salary',
        'Father Name',
        'Mother Name',
        'Entity'
      ]

      tableData.push(uniqueHeaders)

      if (this.caseConfig) {
        const subCase = this.caseConfig

        if (subCase?.fieldMappings?.addresses) {
          subCase.fieldMappings.addresses.forEach((address) => {
            Object.values(address).forEach((field) => {
              if (
                field.displayName &&
                field.displayName.trim() &&
                !uniqueHeaders.includes(field.displayName)
              ) {
                uniqueHeaders.push(field.displayName)
              }
            })
          })
        }
      } else {
        const defaultAddressFields = [
          'PA_addressLine1',
          'PA_addressLine2',
          'PA_landMark',
          'PA_city',
          'PA_state',
          'PA_country',
          'PA_pinCode'
        ]

        defaultAddressFields.forEach((field) => {
          if (!uniqueHeaders.includes(field)) {
            uniqueHeaders.push(field)
          }
        })
      }

      tableData[0] = uniqueHeaders
      return tableData
    },

    getDynamicField(item) {
      const dynamicField = this.caseConfig.fieldMappings?.caseNo?.displayName
        ?.toLowerCase()
        .replace(/\s+/g, '_')

      return item[dynamicField]
    },

    async handleAction() {
      if (!this.validationPassed) {
        await this.validateFile()
      } else {
        this.uploadFile()
      }
    },
    async validateFile() {
      try {
        this.headers = await extractExcelHeaders(this.selectedFile)
        const requiredColumns = ['crn', 'First Name', 'Last Name', 'Customer Full Name']
        const genderEnums = ['MALE', 'M', 'FEMALE', 'F', 'OTHERS', 'NOT_APPLICABLE']
        const missingFields = []
        const matchedFields = []
        let missedCount = 0
        let matchedCount = 0

        requiredColumns.forEach((column) => {
          if (!this.headers.includes(column)) {
            missingFields.push(`${++missedCount}: Missing required column "${column}"`)
          } else {
            matchedFields.push(`${++matchedCount}: ${column}`)
          }
        })

        const genderColumn = this.headers.find((header) => header.toLowerCase() === 'gender')
        if (genderColumn) {
          const XLSX = require('xlsx')
          const file = this.selectedFile[0]
          const buffer = await file.arrayBuffer()
          const workbook = XLSX.read(buffer, { type: 'array' })
          const worksheet = workbook.Sheets[workbook.SheetNames[0]]
          const excelData = XLSX.utils.sheet_to_json(worksheet)

          const invalidGenders = excelData.filter((row) => {
            const gender = row[genderColumn]
            return gender && !genderEnums.includes(gender.toUpperCase())
          })

          if (invalidGenders.length > 0) {
            missingFields.push(
              `Invalid gender values found in column "${genderColumn}". Allowed values are: ${genderEnums.join(
                ', '
              )}`
            )
          }
        }

        const mobileColumns = this.headers.filter((header) =>
          header.toLowerCase().startsWith('customer number')
        )
        if (mobileColumns.length > 0) {
          const XLSX = require('xlsx')
          const file = this.selectedFile[0]
          const buffer = await file.arrayBuffer()
          const workbook = XLSX.read(buffer, { type: 'array' })
          const worksheet = workbook.Sheets[workbook.SheetNames[0]]
          const excelData = XLSX.utils.sheet_to_json(worksheet)

          mobileColumns.forEach((column) => {
            const invalidNumbers = excelData.filter((row) => {
              const mobile = row[column]
              if (!mobile) return false

              const mobileString = String(mobile).trim()
              const numbers = mobileString.includes(',')
                ? mobileString.split(',').map((num) => num.trim())
                : [mobileString]

              return numbers.some((num) => !/^\d{10}$/.test(num))
            })

            if (invalidNumbers.length > 0) {
              missingFields.push(
                `Invalid mobile numbers found in column "${column}". Ensure all numbers are exactly 10 digits and properly formatted.`
              )
            }
          })
        }

        this.validationErrors = [...this.validationErrors, ...missingFields]
        this.matchedHeaders = matchedFields
        if (this.validationErrors.length === 0) {
          this.validationPassed = true
        }
      } catch (error) {
        console.error('Error during validation:', error)
        this.validationErrors = ['Error processing the file.']
        this.validationPassed = false
      }
    }
  }
}
</script>
<style scoped>
.multiline-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
  font-weight: 400;
}
</style>
