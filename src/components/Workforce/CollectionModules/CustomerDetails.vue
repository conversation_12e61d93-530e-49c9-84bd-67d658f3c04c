<template>
  <v-card class="customer-details-card" elevation="2">
    <v-card-title
      class="bg-gray-200 dark-bg-custom flex justify-between w-full px-3"
      >Customer Details
      <v-icon @click="closeCustomerPopUp">mdi-close</v-icon></v-card-title
    >
    <!-- Basic customer information -->
    <v-card class="info-section" outlined>
      <v-card-title>Basic Information</v-card-title>
      <v-card-text>
        <v-simple-table>
          <template v-slot:default>
            <tbody>
              <tr v-if="customer.customerFullName">
                <td class="label-column"><strong>Full Name</strong></td>
                <td>{{ customer.customerFullName }}</td>
              </tr>
              <tr v-if="customer.crn">
                <td class="label-column"><strong>CRN</strong></td>
                <td>{{ customer.crn }}</td>
              </tr>
              <tr v-if="customer.dateOfBirth">
                <td class="label-column"><strong>Date of Birth</strong></td>
                <td>
                  {{
                    $dayjs(customer.dateOfBirth).format("DD/MM/YYYY hh:mm A")
                  }}
                </td>
              </tr>
              <tr v-if="customer.gender">
                <td class="label-column"><strong>Gender</strong></td>
                <td>{{ customer.gender }}</td>
              </tr>
              <tr v-if="customer.customerEmail">
                <td class="label-column"><strong>Email</strong></td>
                <td>{{ customer.customerEmail }}</td>
              </tr>
              <tr v-if="customer.customerMobile.length">
                <td class="label-column"><strong>Mobile Numbers</strong></td>
                <td>
                  {{
                    customer.customerMobile
                      .map((number) => mask(number))
                      .join(", ")
                  }}
                </td>
              </tr>
              <tr v-if="customer.jobType">
                <td class="label-column"><strong>Job Type</strong></td>
                <td>{{ customer.jobType }}</td>
              </tr>
              <tr v-if="customer.occupation">
                <td class="label-column"><strong>Occupation</strong></td>
                <td>{{ customer.occupation }}</td>
              </tr>
              <tr v-if="customer.company">
                <td class="label-column"><strong>Company</strong></td>
                <td>{{ customer.company }}</td>
              </tr>
              <tr v-if="customer.salary">
                <td class="label-column"><strong>Salary</strong></td>
                <td>{{ customer.salary }}</td>
              </tr>
            </tbody>
          </template>
        </v-simple-table>
      </v-card-text>
    </v-card>

    <!-- Customer Addresses -->
    <v-divider></v-divider>
    <v-card
      class="address-section"
      outlined
      v-if="customer?.customerAddress?.length"
    >
      <v-card-title> Addresses</v-card-title>
      <v-card-text>
        <v-simple-table>
          <template v-slot:default>
            <tbody>
              <tr
                v-for="address in customer?.customerAddress"
                :key="address._id"
              >
                <td class="label-column">
                  <strong>{{ convertTitle(address?.typeOfAddress) }}</strong>
                </td>
                <td class="py-3">
                  <span>
                    <v-icon color="blue" @click="openGoogleMaps(address)"
                      >mdi-map</v-icon
                    >
                  </span>
                  <span class="ml-2">
                    {{ extractAddress(address) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </template>
        </v-simple-table>
      </v-card-text>
    </v-card>

    <!-- Customer References -->
    <v-divider></v-divider>
    <v-card
      class="reference-section"
      outlined
      v-if="customer?.references?.length"
    >
      <v-card-title>References</v-card-title>

      <v-card-text>
        <v-simple-table>
          <template v-slot:default>
            <tbody
              v-for="(reference, index) in customer.references"
              :key="reference._id"
            >
              <tr>
                <p class="font-semibold mb-0 mt-4">Reference {{ index + 1 }}</p>
              </tr>
              <tr>
                <td class="label-column">
                  <strong>Name</strong>
                </td>
                <td>{{ reference.name }}</td>
              </tr>
              <tr>
                <td class="label-column">
                  <strong>Relation</strong>
                </td>
                <td>{{ reference.relation }}</td>
              </tr>
              <tr>
                <td class="label-column">
                  <strong>Mobile Number</strong>
                </td>
                <td>
                  <ul>
                    <li v-for="mobile in reference.mobile" :key="mobile">
                      {{ mobile }}
                    </li>
                  </ul>
                </td>
              </tr>
              <tr>
                <td>
                  <strong>Address</strong>
                </td>
                <td>
                  <ul>
                    <li
                      v-for="address in reference.addresses"
                      :key="address._id"
                    >
                      {{ extractAddress(address) }}
                    </li>
                  </ul>
                </td>
              </tr>
            </tbody>
          </template>
        </v-simple-table>
      </v-card-text>
    </v-card>
  </v-card>
</template>

<script>
import {
  getFullAddress,
  convertToTitleCase,
  maskPhoneNumber,
} from "@/utils/common";

export default {
  name: "CustomerDetailsCard",
  props: {
    customer: Object,
    closeCustomerPopUp: Function,
  },
  methods: {
    extractAddress(str) {
      return getFullAddress(str);
    },
    convertTitle(str) {
      if (str) {
        return convertToTitleCase(str);
      }
      return;
    },
    mask(num) {
      return maskPhoneNumber(num);
    },
    openGoogleMaps(address) {
      const fullAddress = getFullAddress(address);
      const encodedAddress = encodeURIComponent(fullAddress);
      const googleMapsURL = `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
      window.open(googleMapsURL, "_blank");
    },
  },
};
</script>

<style scoped>
.customer-details-card {
  padding: 20px;
}
td {
  width: 50%;
}
td ul {
  padding-left: 0px;
}
.info-section,
.address-section,
.reference-section {
  margin-bottom: 20px;
}
.label-column {
  width: 15%;
  white-space: nowrap;
  padding-right: 8px;
}
</style>
