<template>
  <v-card class="w-full" flat :color="$vuetify.theme.currentTheme.navigationColor">
    <div class="sticky flex top-0 justify-between z-50 p-4 bg-white dark-bg-custom">
      <h4 class="text-xl">
        {{
          editCustomer
            ? `Edit ${configTabMapping?.customerTable?.displayName || 'Customer'}`
            : `Add ${configTabMapping?.customerTable?.displayName || 'Customer'}`
        }}
      </h4>
      <v-btn plain @click="cancel" class="p-4">
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </div>

    <!-- Show form only when checkbox is checked -->
    <v-form ref="form" v-model="valid" lazy-validation>
      <v-stepper v-model="step" vertical flat color="navigationColor">
        <v-stepper-items>
          <!-- Step 1: Personal Information -->
          <v-stepper-step :complete="step > 1" step="1" editable>
            Basic Info <small>(Mandatory)</small>
          </v-stepper-step>
          <v-stepper-content step="1">
            <div class="grid grid-cols-2 gap-2">
              <v-text-field
                v-model="crn"
                label="CRN"
                :rules="rules.crn"
                required
                hide-details
                outlined
                dense
                placeholder="ex : **********"
              ></v-text-field>
              <v-autocomplete
                v-model="selectedEntity"
                :label="`Search Entity Name`"
                :items="entities"
                item-text="entityName"
                :search-input.sync="searchEntity"
                item-value="_id"
                @update:search-input="debounceEntity"
                hide-no-data
                hide-details
                outlined
                placeholder="ex : ABC Corporation"
                dense
              ></v-autocomplete>
              <div class="flex justify-between">
                <v-text-field
                  v-model="firstName"
                  label="First Name"
                  hide-details
                  outlined
                  dense
                  placeholder="ex : John"
                ></v-text-field>
                <div class="p-1" />
                <v-text-field
                  v-model="middleName"
                  label="Middle Name"
                  hide-details
                  outlined
                  placeholder="ex : Doe"
                  dense
                ></v-text-field>
                <div class="p-1" />
                <v-text-field
                  v-model="lastName"
                  label="Last Name"
                  hide-details
                  outlined
                  dense
                  placeholder="ex : Smith"
                ></v-text-field>
              </div>
              <div class="flex justify-between">
                <v-text-field
                  v-model="fatherName"
                  label="Father's Name"
                  hide-details
                  outlined
                  dense
                  placeholder="ex : James Smith"
                ></v-text-field>
                <div class="p-1" />
                <v-text-field
                  v-model="motherName"
                  label="Mother's Name"
                  hide-details
                  outlined
                  placeholder="ex : Mary Smith"
                  dense
                ></v-text-field>
              </div>
              <v-menu
                v-model="menu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                class="checkBorder_a"
              >
                <template v-slot:activator="{ on }">
                  <v-text-field
                    label="Date of birth"
                    placeholder="ex : 02-01-2023"
                    v-model="dateOfBirthFormat"
                    prepend-inner-icon="mdi-calendar"
                    readonly
                    clearable
                    v-on="on"
                    hide-details
                    outlined
                    dense
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="dateOfBirth"
                  :max="$dayjs().format('YYYY-MM-DD')"
                  no-title
                  scrollable
                />
              </v-menu>
              <v-select
                v-model="gender"
                :items="genderTypes"
                item-text="text"
                label="Select Gender"
                item-value="value"
                hide-details
                outlined
                placeholder="ex : Male"
                dense
              ></v-select>
              <v-select
                v-model="maritalStatus"
                :items="['Single', 'Married', 'Divorced', 'Widowed']"
                label="Marital Status"
                hide-details
                outlined
                placeholder="ex : Single"
                dense
              ></v-select>
              <v-text-field
                v-model="nationality"
                label="Nationality/Citizenship"
                hide-details
                outlined
                dense
                placeholder="ex : Indian"
              ></v-text-field>
              <div class="flex justify-between">
                <v-select
                  v-model="idProofType"
                  :items="['Passport', 'Driver License', 'SSN', 'Aadhar']"
                  label="ID Proof Type"
                  hide-details
                  outlined
                  dense
                ></v-select>
                <div class="p-1" />
                <v-text-field
                  v-model="idProofNumber"
                  label="ID Proof Number"
                  hide-details
                  outlined
                  dense
                ></v-text-field>
              </div>
            </div>
            <div class="flex items-center justify-end">
              <v-btn outlined @click="cancel">Cancel</v-btn>
              <v-btn
                @click="step++"
                class="white--text ml-3"
                :color="$vuetify.theme.currentTheme.primary"
                >Next</v-btn
              >
            </div>
          </v-stepper-content>
          <!-- Step 2: Contact Details -->
          <v-stepper-step :complete="step > 2" step="2" editable>
            Contact Details <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="2">
            <v-checkbox
              v-model="fillContactInfo"
              label="Do you want to add contact details (Optional)"
              hide-details
              class="mt-3 mb-3"
            ></v-checkbox>

            <div v-if="fillContactInfo" class="grid grid-cols-2 gap-2">
              <v-select
                v-model="jobType"
                :items="jobTypes"
                item-text="text"
                item-value="value"
                label="Job Type"
                hide-details
                outlined
                dense
              ></v-select>
              <v-text-field
                v-model="customerEmail"
                label="Email"
                hide-details
                outlined
                dense
                placeholder="ex : <EMAIL>"
              ></v-text-field>
              <v-text-field
                v-model="customerMobile"
                label="Phone Number"
                hide-details
                outlined
                dense
                placeholder="ex : 9876543210"
              ></v-text-field>
              <v-text-field
                v-model="customerMobile"
                label="Alternate Phone Number"
                hide-details
                outlined
                dense
                placeholder="ex : 9876543210"
              ></v-text-field>
              <v-text-field
                v-model="emergencyName"
                label="Emergency Contact Name"
                hide-details
                outlined
                dense
                placeholder="ex : John Doe"
              ></v-text-field>
              <v-text-field
                v-model="emergencyPhone"
                label="Emergency Contact Number"
                hide-details
                outlined
                dense
                placeholder="ex : 9876543210"
              ></v-text-field>
              <v-text-field
                v-model="mailingAddress"
                label="Mailing Address"
                hide-details
                outlined
                dense
                placeholder="ex : 123 Main St, City, State"
              ></v-text-field>
              <v-select
                v-model="preferredContactMethod"
                :items="['Email', 'Phone', 'SMS', 'Mail']"
                label="Preferred Contact Method"
                hint="How would you like to be reached?"
                persistent-hint
                hide-details
                outlined
                dense
                class=""
              ></v-select>
            </div>

            <div class="flex items-center pt-2 justify-end">
              <v-btn @click="step--" outlined>Back</v-btn>
              <v-btn
                @click="step++"
                class="white--text ml-3"
                :color="$vuetify.theme.currentTheme.primary"
              >
                Next
              </v-btn>
            </div>
          </v-stepper-content>
          <!-- Step 3: Employment Details -->
          <v-stepper-step :complete="step > 3" step="3" editable>
            Occupation Details
            <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="3">
            <v-checkbox
              v-model="fillOccupationDetails"
              label="Do you want to add occupation details (Optional)"
              hide-details
              class="mt-3 mb-3"
            ></v-checkbox>

            <div v-if="fillOccupationDetails" class="grid grid-cols-2 gap-2">
              <v-select
                v-model="employmentStatus"
                :items="['Full-time', 'Part-time', 'Self-employed', 'Unemployed', 'Retired']"
                label="Employment Status"
                placeholder="Current employment status"
                hide-details
                outlined
                dense
              ></v-select>
              <v-text-field
                v-model="employmentDuration"
                label="Employment Duration"
                placeholder="How long at current job ex: 2 years 3 months"
                hide-details
                outlined
                dense
              ></v-text-field>
              <v-text-field
                v-model="previousEmployment"
                label="Previous Employment"
                hint="Required if current employment is recent"
                placeholder="ex: Software Engineer at XYZ Corp"
                hide-details
                outlined
                dense
              ></v-text-field>
              <v-text-field
                v-model="industrySector"
                label="Industry Sector"
                hint="More specific than company name"
                placeholder="ex: Information Technology"
                hide-details
                outlined
                dense
              ></v-text-field>
              <v-text-field
                v-model="occupation"
                label="Occupation"
                placeholder="ex: Software Developer"
                hide-details
                outlined
                dense
              ></v-text-field>
              <v-text-field
                v-model="designation"
                label="Designation"
                placeholder="ex: Senior Developer"
                hide-details
                outlined
                dense
              ></v-text-field>
              <v-text-field
                v-model="company"
                label="Company"
                placeholder="ex: ABC Technologies"
                hide-details
                outlined
                dense
              ></v-text-field>
              <v-text-field
                v-model="monthlyIncome"
                label="Monthly Income/Salary"
                type="number"
                prefix="₹"
                placeholder="ex: 50000"
                hide-details
                outlined
                dense
                @input="calculateYearlyIncome"
              ></v-text-field>
              <v-text-field
                v-model="yearlyIncome"
                label="Yearly Income"
                type="number"
                prefix="₹"
                readonly
                hide-details
                outlined
                dense
              ></v-text-field>
              <v-text-field
                v-model="taxPerYear"
                label="Tax Paying Per Year"
                type="number"
                prefix="₹"
                placeholder="ex: 120000"
                hide-details
                outlined
                dense
              ></v-text-field>
            </div>

            <div class="flex items-center pt-2 justify-end">
              <v-btn @click="step--" outlined>Back</v-btn>
              <v-btn
                @click="step++"
                class="white--text ml-3"
                :color="$vuetify.theme.currentTheme.primary"
              >
                Next
              </v-btn>
            </div>
          </v-stepper-content>

          <!-- Step 4: Address Details -->
          <v-stepper-step :complete="step > 4" step="4" editable>
            Financial Details
            <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="4">
            <v-checkbox
              v-model="fillFinancialDetails"
              label="Do you want to add financial details (Optional)"
              hide-details
              class="mt-3 mb-3"
            ></v-checkbox>

            <div v-if="fillFinancialDetails">
              <v-form>
                <v-text-field
                  v-model="bankAccountNumber"
                  label="Bank Account Number"
                  hide-details
                  outlined
                  dense
                  placeholder="ex: **********"
                  class="mt-3 mb-3"
                ></v-text-field>
                <v-text-field
                  v-model="bankName"
                  label="Bank Name"
                  hide-details
                  outlined
                  dense
                  placeholder="ex: HDFC Bank"
                  class="mt-3 mb-3"
                ></v-text-field>
                <v-text-field
                  v-model="ifscCode"
                  label="IFSC Code"
                  hide-details
                  outlined
                  dense
                  placeholder="ex: HDFC0001234"
                  class="mt-3 mb-3"
                ></v-text-field>
              </v-form>
            </div>

            <div class="flex items-center justify-end">
              <v-btn @click="step--" outlined>Back</v-btn>
              <v-btn
                @click="step++"
                class="white--text ml-3"
                :color="$vuetify.theme.currentTheme.primary"
              >
                Next
              </v-btn>
            </div>
          </v-stepper-content>
          <!-- Step 5: Additional Information -->
          <!-- <v-stepper-step :complete="step > 5" step="5" editable>
            Additional Information
          </v-stepper-step>
          <v-stepper-content step="5">
            <div v-for="(reference, index) in references" :key="index">
              <div class="flex justify-between items-start">
                <p>{{ `Reference ${index + 1}` }}</p>
                <v-icon
                  size="20"
                  color="red"
                  @click="removeReference(index)"
                  v-show="references.length > 1"
                  >mdi-delete</v-icon
                >
              </div>
              <v-text-field
                v-model="reference.name"
                :label="`Name`"
                hide-details
                outlined
                placeholder="ex : John Doe"
                dense
                class="mt-3 mb-3"
              ></v-text-field>
              <v-text-field
                v-model="reference.relation"
                label="Relation"
                hide-details
                outlined
                placeholder=" ex : Brother"
                dense
                class="mt-3 mb-3"
              ></v-text-field>
              <v-text-field
                v-model="reference.mobile"
                :label="`Mobile`"
                hide-details
                outlined
                placeholder="ex : 9876543210"
                dense
                class="mt-3 mb-3"
              ></v-text-field>
              <v-text-field
                v-model="reference.addresses[0].addressLine1"
                @click="showAddRefAddress(index)"
                :label="`Address`"
                hide-details
                outlined
                placeholder="ex : 123 Main Street, Apt 4B, Bangalore, Karnataka, 560001"
                dense
                class="mt-3 mb-3"
              ></v-text-field>
            </div>
            <div class="flex items-center justify-end">
              <v-btn @click="step--" outlined>Back</v-btn>
              <v-btn
                @click="step++"
                class="white--text ml-3"
                :color="$vuetify.theme.currentTheme.primary"
                >Next</v-btn
              >
            </div>
          </v-stepper-content> -->

          <!-- Step 6: References -->
          <!-- <v-stepper-step :complete="step > 6" step="6" editable> References </v-stepper-step>
          <v-stepper-content step="6">
            <div v-for="(details, index) in customerDetails" :key="index">
              <div class="flex justify-between items-start">
                <p>{{ `Address ${index + 1}` }}</p>
                <v-icon
                  size="20"
                  color="red"
                  @click="removeDetails(index)"
                  v-show="customerDetails.length > 1"
                  >mdi-delete</v-icon
                >
              </div>
              <v-select
                v-model="details.typeOfAddress"
                :items="filterAddressTypes(index)"
                item-text="text"
                :label="`Address Type`"
                item-value="value"
                hide-details
                outlined
                placeholder="ex : Current Address"
                dense
                class="mt-3 mb-3"
              >
              </v-select>
              <v-text-field
                v-model="details.addressLine1"
                @click="showAddAddress(index)"
                :label="`${configTabMapping.customerTable?.displayName || 'Customer'} Address`"
                hide-details
                outlined
                dense
                placeholder="ex : 123 Main Street, Apt 4B, Bangalore, Karnataka, 560001"
                class="mt-3 mb-3"
              ></v-text-field>
            </div>
            <div class="flex justify-end mb-4">
              <v-btn @click="addCustomerDetails" small outlined v-if="showAddressTypeButton"
                >Add Address</v-btn
              >
            </div>
            <div class="flex items-center justify-end">
              <v-btn @click="step--" outlined>Back</v-btn>
              <v-btn
                @click="step++"
                class="white--text ml-3"
                :color="$vuetify.theme.currentTheme.primary"
                >Next</v-btn
              >
            </div>
          </v-stepper-content> -->

          <!-- Step 8: Preferences -->
          <v-stepper-step :complete="step > 5" step="5" editable>
            Preference <small>(Optional)</small>
          </v-stepper-step>
          <v-stepper-content step="5">
            <v-checkbox
              v-model="fillPreferences"
              label="Do you want to add preferences (Optional)"
              hide-details
              class="mt-3 mb-3"
            ></v-checkbox>
            <div v-if="fillPreferences">
              <v-form>
                <v-checkbox
                  v-model="marketingOptIn"
                  label="Opt-in for Marketing Communications"
                  hide-details
                  class="mt-3 mb-3"
                ></v-checkbox>
                <v-select
                  v-model="preferredLanguage"
                  :items="['English', 'Hindi', 'Spanish', 'French']"
                  label="Preferred Language"
                  hide-details
                  outlined
                  dense
                  class="mt-3 mb-3"
                ></v-select>
                <v-select
                  v-model="servicePreferences"
                  :items="[
                    'SMS Notifications',
                    'Email Updates',
                    'Phone Calls',
                    'Mobile App Notifications'
                  ]"
                  label="Service Preferences"
                  multiple
                  chips
                  hide-details
                  outlined
                  dense
                  class="mt-3 mb-3"
                ></v-select>
              </v-form>
            </div>
          </v-stepper-content>
        </v-stepper-items>
      </v-stepper>
    </v-form>
    <!-- Form bottom buttons - inside the card -->
    <div class="flex sticky bottom-0 z-50 bg-white p-2 items-center justify-end">
      <v-btn outlined @click="cancel">Cancel</v-btn>
      <v-btn
        @click="addCustomer"
        class="white--text ml-3"
        :color="$vuetify.theme.currentTheme.primary"
        >Save
      </v-btn>
    </div>

    <v-dialog v-model="showAddAddressDialog" width="auto">
      <CompanyAddress
        :company="this.crn"
        :isOpen="showAddAddressDialog"
        @cancel="closeAddressDialogs"
        @save="addLocation"
        title="Add Address"
      />
    </v-dialog>
    <v-dialog v-model="showAddRefDialog" width="auto">
      <CompanyAddress
        :company="this.crn"
        :isOpen="showAddRefDialog"
        @cancel="closeAddressDialogs"
        @save="addRefLocation"
        title="Add Address"
      />
    </v-dialog>
  </v-card>
</template>
<script>
import CompanyAddress from '../CompanyAddress.vue'
export default {
  name: 'AddEditCaseTask',
  components: {
    CompanyAddress
  },
  data() {
    return {
      step: 1,
      fillContactInfo: false,
      fillOccupationDetails: false,
      fillFinancialDetails: false,
      fillPreferences: false,
      valid: true,
      genderTypes: [
        { text: 'Male', value: 'MALE' },
        { text: 'Female', value: 'FEMALE' },
        { text: 'Others', value: 'OTHERS' },
        { text: 'Not Applicable', value: 'NOT_APPLICABLE' }
      ],
      jobTypes: [
        { text: 'Salaried', value: 'SALARIED' },
        { text: 'Self Employed', value: 'SELF_EMPLOYED' },
        { text: 'Business', value: 'BUSINESS' },
        { text: 'Student', value: 'STUDENT' }
      ],
      addressTypes: [
        { text: 'Current Address', value: 'CURRENT_ADDRESS' },
        { text: 'Mailing Address', value: 'MAILING_ADDRESS' },
        { text: 'Billing Address', value: 'BILLING_ADDRESS' },
        { text: 'Office Address', value: 'OFFICE_ADDRESS' },
        { text: 'Residential Address', value: 'RESIDENTIAL_ADDRESS' }
      ],
      anyReferences: false,
      relation: null,
      showAddAddressDialog: false,
      showAddRefDialog: false,
      customerAddressFormat: '',
      addressTypeToAdd: null,
      selectedAddress: null,
      crn: '',
      firstName: '',
      middleName: '',
      lastName: '',
      dateOfBirth: null,
      gender: null,
      customerEmail: '',
      menu: false,
      customerMobile: [],
      customerDetails: [
        {
          typeOfAddress: null
        }
      ],
      references: [
        {
          name: '',
          relation: null,
          mobile: '',
          addresses: [{ addressLine1: '' }]
        }
      ],
      selectedIndex: null,
      jobType: null,
      occupation: '',
      company: '',
      salary: null,
      typeOfAddress: null,
      fatherName: '',
      motherName: '',
      stepperRule: true,
      searchEntity: '',
      selectedEntity: null,
      rules: {
        crn: [(val) => (val || '').length > 0 || `CRN is required`]
      },
      // Add saved form data
      savedFormData: {
        crn: '',
        selectedEntity: null,
        customerEmail: '',
        customerMobile: '',
        fatherName: '',
        motherName: '',
        customerDetails: [],
        references: [],
        linkWithSubLicense: false,
        selectedSubcase: null
        // Add other form fields as needed
      },
      showForm: false
    }
  },
  props: {
    editCustomer: Object,
    caseType: String,
    entities: Array,
    configTabMapping: Object
  },
  watch: {
    editCustomer: {
      handler(newVal) {
        this.$props.editCustomer = newVal
        this.populateFormData()
      },
      immediate: true
    }
  },
  computed: {
    mobileRules() {
      return [(v) => (v && v.length === 10) || 'Phone Number should have 10 digits']
    },
    showAddressTypeButton() {
      return this.customerDetails?.length < this.addressTypes.length
    },
    dateOfBirthFormat() {
      if (this.dateOfBirth) {
        return this.$dayjs(this.dateOfBirth).format('DD-MM-YYYY')
      }
      return this.$dayjs().format('DD-MM-YYYY')
    },
    isPersonalInfoCompleted() {
      return (
        this.crn &&
        this.firstName &&
        this.lastName &&
        this.gender &&
        this.customerEmail &&
        this.customerMobile.length
      )
    },
    isAdditionalInfoCompleted() {
      return (
        !this.fillContactInfo ||
        (this.jobType && this.occupation && this.fatherName && this.motherName)
      )
    },
    jumpToStep() {
      return !this.isPersonalInfoCompleted ? 1 : this.step
    }
  },
  methods: {
    async addCustomer() {
      try {
        const isValid = this.$refs.form.validate()
        if (!isValid) {
          this.stepperRule = false
          return
        }
        const payload = {
          crn: this.crn,
          relatedSubCategories: this.caseType || undefined,
          relatedCategory: 'collection',
          firstName: this.firstName || undefined,
          middleName: this.middleName || undefined,
          lastName: this.lastName || undefined,
          customerFullName: `${this.firstName} ${this.middleName} ${this.lastName}`,
          dateOfBirth: this.dateOfBirth || undefined,
          gender: this.gender || undefined,
          customerEmail: this.customerEmail || undefined,
          customerMobile: this.customerMobile || undefined,
          jobType: this.jobType || undefined,
          occupation: this.occupation || undefined,
          company: this.company || undefined,
          salary: this.salary || undefined,
          fatherName: this.fatherName || undefined,
          motherName: this.motherName || undefined
        }
        if (this.selectedEntity) {
          payload.entity = this.selectedEntity
        }
        if (this.customerDetails.length && this.customerDetails[0].addressLine1) {
          payload.customerAddress = this.customerDetails
        }
        if (this.references.length && this.anyReferences) {
          payload.references = this.references
        }

        let url = '/workforce/customer'
        let method = 'POST'

        if (this.editCustomer) {
          url += `/${this.editCustomer._id}`
          method = 'PUT'
        }

        await this.$axios({
          method: method,
          url: url,
          data: payload
        })

        this.cancel()
        this.$toast.success(`Customer ${this.editCustomer ? 'updated' : 'added'} successfully`)
      } catch (error) {
        this.$toast.error(error.response?.data?.message)
        console.log(error)
      }
    },
    async addLocation(data) {
      const index = this.selectedIndex
      if (index !== null && index !== undefined) {
        const newAddress = {
          addressLine1: `${data.addLine1}, ${data.addLine2}, ${data.city}, ${data.state}, ${data.pinCode}`,
          addressLine2: data.addLine2,
          city: data.city,
          state: data.state,
          country: data.country,
          pinCode: data.pinCode
        }
        this.$set(this.customerDetails, index, {
          typeOfAddress: this.customerDetails[index].typeOfAddress,
          ...newAddress
        })
        this.showAddAddressDialog = false
      }
    },
    async addRefLocation(data) {
      const index = this.selectedRefIndex
      const addresses = []
      if (index !== null && index !== undefined) {
        const newAddress = {
          addressLine1: `${data.addLine1}, ${data.addLine2}, ${data.city}, ${data.state}, ${data.pinCode}`,
          addressLine2: data.addLine2,
          city: data.city,
          state: data.state,
          country: data.country,
          pinCode: data.pinCode
        }
        addresses.push(newAddress)
        this.$set(this.references, index, {
          name: this.references[index].name,
          relation: this.references[index].relation,
          mobile: this.references[index].mobile,
          addresses: addresses
        })
        this.showAddRefDialog = false
      }
    },
    debounceEntity() {
      if (!this.searchEntity || this.selectedManager) return
      this.$emit('emittedEntity', this.searchEntity)
    },
    addReference() {
      this.references.push({
        name: '',
        relation: null,
        mobile: '',
        addresses: [{ addressLine1: '' }]
      })
    },
    removeChip(item, options) {
      const index = options.indexOf(item)
      if (index !== -1) {
        options.splice(index, 1)
      }
    },
    filterAddressTypes(index) {
      const selectedTypes = this.customerDetails.map((item) => item.typeOfAddress)
      return this.addressTypes.filter(
        (type) =>
          !selectedTypes.includes(type.value) ||
          type.value === this.customerDetails[index].typeOfAddress
      )
    },
    addCustomerDetails() {
      this.customerDetails.push({
        typeOfAddress: null
      })
    },
    closeAddressDialogs() {
      this.showAddAddressDialog = false
      this.showAddRefDialog = false
    },
    removeReference(index) {
      this.references.splice(index, 1)
    },
    removeDetails(index) {
      this.customerDetails.splice(index, 1)
    },
    showAddAddress(index) {
      this.selectedIndex = index
      this.showAddAddressDialog = true
    },
    showAddRefAddress(index) {
      this.selectedRefIndex = index
      this.showAddRefDialog = true
    },
    cancel() {
      this.$emit('customerAdded')
      this.step = 1
      this.clearForm()
    },

    cancelForm() {
      this.clearForm()
      this.cancel()
    },

    populateFormData() {
      if (this.editCustomer) {
        this.crn = this.editCustomer.crn || ''
        this.firstName = this.editCustomer.firstName || this.editCustomer?.customerFullName
        this.middleName = this.editCustomer.middleName || ''
        this.lastName = this.editCustomer.lastName || ''
        this.dateOfBirth = this.editCustomer.dateOfBirth || null
        this.gender = this.editCustomer.gender || null
        this.customerEmail = this.editCustomer.customerEmail || ''
        this.customerMobile = this.editCustomer.customerMobile || ''
        this.jobType = this.editCustomer.jobType || null
        this.occupation = this.editCustomer.occupation || ''
        this.company = this.editCustomer.company || ''
        this.salary = this.editCustomer.salary || null
        this.fatherName = this.editCustomer.fatherName || ''
        this.motherName = this.editCustomer.motherName || ''

        // Populate customer addresses
        if (this.editCustomer.customerAddress && this.editCustomer.customerAddress.length > 0) {
          this.customerDetails = this.editCustomer.customerAddress.map((address) => ({
            typeOfAddress: address.typeOfAddress || null,
            addressLine1: address.addressLine1 || '',
            addressLine2: address.addressLine2 || '',
            city: address.city || '',
            state: address.state || '',
            country: address.country || '',
            pinCode: address.pinCode || ''
          }))
        }

        // Populate references
        if (this.editCustomer.references && this.editCustomer.references.length > 0) {
          this.anyReferences = true // Set to true to show reference fields
          this.references = this.editCustomer.references.map((reference) => ({
            name: reference.name || '',
            relation: reference.relation || null,
            mobile: reference.mobile || '',
            addresses: reference.addresses.map((address) => ({
              addressLine1: address.addressLine1 || '',
              addressLine2: address.addressLine2 || '',
              city: address.city || '',
              state: address.state || '',
              country: address.country || '',
              pinCode: address.pinCode || ''
            }))
          }))
        }
      }
    }
  }
}
</script>
<style scoped>
/* Custom styling for stepper completed steps */
::v-deep .v-stepper__step--complete .v-stepper__step__step {
  background-color: #4caf50 !important; /* Green color for completed steps */
}

::v-deep .v-stepper__step--complete .v-stepper__label {
  color: #4caf50 !important; /* Green color for completed step labels */
}

::v-deep .v-stepper__step--active .v-stepper__step__step {
  background-color: #062783 !important; /* Keep active step blue */
}
</style>
