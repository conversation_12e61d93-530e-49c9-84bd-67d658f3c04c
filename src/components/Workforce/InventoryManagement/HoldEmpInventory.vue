<template>
  <div>
    <v-data-table
      :items="employeeInventory"
      :headers="headers"
      class="pl-7 pr-7 pt-4 dark-bg-custom"
    >
      <template v-slot:item.holdQty="{ item }">
        {{ remainingQtyHoldByUser(item) }}
      </template>
    </v-data-table>
  </div>
</template>
<script>
export default {
  data() {
    return {
      headers: [
        { text: "Request Id", value: "requestId", sortable: false },
        {
          text: "Product Name",
          value: "productData.productName",
          sortable: false,
        },
        { text: "Collected Qty", value: "quantityTook", sortable: false },
        {
          text: "Hold Qty",
          value: "holdQty",
          sortable: false,
        },
      ],
    };
  },
  props: {
    employeeInventory: Array,
  },
  methods: {
    remainingQtyHoldByUser(item) {
      return item.quantityTook - (item.quantityReturned + item.quantityUsed);
    },
  },
};
</script>
