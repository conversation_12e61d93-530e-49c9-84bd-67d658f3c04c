<template>
  <div class="w-full p-4">
    <ol class="flex bg-blue-100 dark-bg-custom rounded-lg items-start py-1 w-full">
      <li v-for="(item, i) in timelineData" :key="item.title" class="relative">
        <div class="flex items-center">
          <div
            class="z-10 flex items-center justify-center w-6 h-6 bg-blue-100 dark-bg-custom rounded-full ring-0 ring-white shrink-0"
          >
            <svg
              class="w-2.5 h-2.5 text-blue-800"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"
              />
            </svg>
          </div>
          <div :class="[i === timelineData.length - 1 ? 'bg-transparent' : 'bg-gray-400']" class="hidden sm:flex w-full h-0.5"></div>
        </div>
        <div class="mt-3 sm:pe-8">
          <h3 class="text-sm whitespace-nowrap font-semibold text-gray-900">
            {{ item.title }}
          </h3>
          <p class="text-sm whitespace-nowrap font-normal mb-0 text-gray-500">
            {{ item.desc || "-" }}
          </p>
          <p
            class="text-sm font-semibold text-xs whitespace-nowrap text-gray-500"
          >
            {{ item.quantity ? `Quantity: ${item.quantity}` : "-" }}
          </p>
          <time
            class="block mb-2 text-xs whitespace-nowrap font-normal leading-none text-gray-400"
            >{{ item.time }}</time
          >
        </div>
      </li>
    </ol>
  </div>
</template>

<script>
export default {
  props: ["data"],
  data() {
    return {
      tableHeaders: [
        {
          text: "Quantity Assigned",
          value: "quantityAssigned",
          sortable: false,
        },
        { text: "Used Quantity", value: "usedQuantity", sortable: false },
        { text: "Balance", value: "balance", sortable: false },
        { text: "Returned", value: "returned", sortable: false },
      ],
    };
  },
  computed: {
    tableItems() {
      const data = this.$props.data;

      const store = data.inStoreAvailability?.find(
        (s) => s.storeLocation._id === data.userInventoryHistory.storeId
      );
      const storeName = store?.storeLocation.storeName;

      return [
        {
          productName: data.productName,
          storeName,
          quantityAssigned: data.userInventoryHistory.quantityAssigned,
          usedQuantity: data.userInventoryHistory.quantityUsed,
          balance:
            data.userInventoryHistory.quantityAssigned -
            data.userInventoryHistory.quantityUsed,
          returned: data.userInventoryHistory.quantityReturned,
        },
      ];
    },
    quantity() {
      const inventory = this.$props.data.userInventoryHistory;
      return {
        assigned: inventory.quantityAssigned,
        used: inventory.quantityUsed,
        returned: inventory.quantityReturned,
        balance: inventory.quantityAssigned - inventory.quantityUsed,
      };
    },
    timelineData() {
      const data = this.$props.data;
      const h = data.userInventoryHistory;

      const getName = (data) =>
        data ? `${data.firstName} ${data.lastName}` : "";

      const items = [
        {
          time: this.getFormattedTime(h.assignedDateTime),
          title: "Assigned By",
          desc: getName(h.productAssignedBy),
          quantity: h.quantityAssigned,
          color: "pink",
        },
        {
          time: this.getFormattedTime(h.assignedDateTime),
          title: "Assigned To",
          desc: getName(h.productAssignedTo),
          quantity: h.quantityAssigned,
          color: "teal lighten-3",
        },
        {
          time: this.getFormattedTime(h.collectedDateTime),
          title: "Collected By",
          quantity: h.quantityTook,
          desc: getName(h.collectedBy),
          color: "purple lighten-3",
        },
        {
          time: this.getFormattedTime(h.collectedDateTime),
          title: "Inventory Handover By",
          desc: getName(h.productGivenBy),
          quantity: h.quantityTook,
          color: "deep-purple lighten-3",
        },
        {
          time: this.getFormattedTime(h.collectedDateTime),
          title: "Inventory Used",
          desc: getName(h.collectedBy),
          quantity: h.quantityUsed,
          color: "deep-purple lighten-3",
        },
        {
          time: this.getFormattedTime(h.returnedDateTime),
          title: "Remaining Returned By",
          desc: getName(h.collectedBy),
          quantity: h.quantityReturned,
          color: "blue lighten-3",
        },
        {
          time: this.getFormattedTime(h.returnedDateTime),
          title: "Remaining Collected By",
          desc: getName(h.productRemainingCollectedBy),
          quantity: h.quantityReturned,
          color: "amber lighten-3",
        },
      ];

      return items;
    },
  },
  methods: {
    getFormattedTime(time) {
      if (!time) return "-";
      return this.$dayjs(time).format("HH:mm a, DD MMM");
    },
  },
};
</script>

<style scoped>
:deep(.v-data-table__wrapper) td {
  padding-left: 1rem !important;
}
</style>
