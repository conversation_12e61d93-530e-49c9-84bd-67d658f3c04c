<template>
  <v-form
    class="p-12 bg-white dark-bg-custom rounded-lg mt-4"
    ref="form"
    v-model="valid"
    lazy-validation
  >
    <p class="text-[#6B6D78] dark-text-color text-2xl">
      {{ isInnerEditAvailable ? "Update" : "Add" }} Inventory
    </p>
    <v-container>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Product Name</p>
        </v-col>
        <v-col cols="">
          <v-select
            flat
            :menu-props="{ offsetY: true }"
            v-model="selectedProduct"
            :items="filteredProducts"
            item-text="productName"
            :disabled="isInnerEditAvailable"
            item-value="_id"
            label="Select Products"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2"  class="my-auto">
          <p class="mt-4">Stores Name</p>
        </v-col>
        <v-col cols="">
          <v-select
            flat
            :menu-props="{ offsetY: true }"
            v-model="selectedStores"
            :items="activeStores"
            item-text="storeName"
            item-value="_id"
            label="Select Stores"
            :disabled="isStatusReturned || isInnerEditAvailable"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Quantity Assigned</p>
        </v-col>
        <v-col cols="" class="pb-0">
          <v-text-field
            flat
            v-model="quantityInStore"
            :disabled="isStatusReturned || isInnerEditAvailable"
            label="Quantity Assigned"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row v-if="isInnerEditAvailable">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Inventory Status</p>
        </v-col>
        <v-col cols="">
          <v-select
            flat
            :menu-props="{ offsetY: true }"
            v-model="selectedStatus"
            :items="inventoryStatus"
            item-text="text"
            :disabled="isOtpSent"
            item-value="value"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row v-if="!isStatusAssigned">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Collected By</p>
        </v-col>
        <v-col cols="" class="w-[80%] flex">
          <v-autocomplete
            v-model="selectedEmployee"
            :items="employees"
            :item-text="getFullName"
            item-value="_id"
            :disabled="(isInnerEditAvailable && isStatusReturned) || isOtpSent"
            label="Search Employee"
            :search-input.sync="searchUser"
            @update:search-input="debounceUser"
            hide-no-data
            outlined
            dense
            hide-details
          ></v-autocomplete>
          <v-btn
            @click="checkEmployeeHistory"
            :color="$vuetify.theme.currentTheme.primary"
            text
            class="ml-2"
          >
            Check History
          </v-btn>
        </v-col>
      </v-row>
      <v-row v-if="!isStatusAssigned">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Chalan Number</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="challanNumber"
            label="Chalan Number"
            outlined
            dense
            :disabled="isChallanAvailable"
            :rules="rules.challanNumber"
            required
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row v-if="!isStatusAssigned">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Out Station Delivery</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="outStationDelivery"
            label="Select Option"
            :items="outStationOptions"
            outlined
            dense
            required
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row v-if="outStationDelivery">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Mode of Delivery</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="modeOfDelivery"
            label="Delivery Option"
            :items="deliveryOptions"
            outlined
            dense
            required
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row v-if="modeOfDelivery == 'BY_COURIER'">
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Courier Number</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="trackingNo"
            label="Courier Number"
            outlined
            dense
            required
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row
        v-if="
          !isStatusReturned &&
          selectedEmployee &&
          !innerEdit?.item?.collectedBy?._id &&
          otpRequired
        "
      >
        <v-col cols="2" class="my-auto">
          <p class="mb-0">Confirm Otp</p>
        </v-col>
        <v-col cols="" class="flex">
          <v-text-field
            v-model="employeeOtp"
            label="Enter OTP"
            outlined
            dense
            hide-details
          ></v-text-field>
          <v-btn
            @click="sendOtp"
            :color="$vuetify.theme.currentTheme.primary"
            text
            class="ml-2"
            :disabled="resendOtpCounter > 0"
            hide-details
          >
            Send OTP
          </v-btn>
        </v-col>
      </v-row>
      <p v-if="resendOtpCounter > 0" class="text-sm text-red-600">
        {{ `Resend Otp in ${resendOtpCounter}s` }}
      </p>
      <v-row v-if="isInnerEditAvailable && isStatusReturned">
        <v-col cols="2" class="my-auto">
          <p class="">Quantity Used</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="usedQuantity"
            label="Quantity Used"
            outlined
            disabled
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <v-row v-if="isInnerEditAvailable && isStatusReturned">
        <v-col cols="2" class="my-auto">
          <p class="">Quantity Returned</p>
        </v-col>
        <v-col cols="">
          <v-text-field
            v-model="returnedQuantity"
            label="Quantity Returned"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>
      <v-row v-if="isInnerEditAvailable && isStatusReturned">
        <v-col cols="2" class="my-auto">
          <p class="">Expected Faulty Product Return</p>
        </v-col>
        <v-col cols="">
          <v-select
            v-model="isProductFaulty"
            label="Expected Faulty Product"
            :items="faultyProductItem"
            item-text="text"
            item-value="value"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-row v-if="isProductFaulty">
        <v-col cols="2" class="my-auto">
          <p class="">Faulty Product Quantity Return</p>
        </v-col>
        <v-col cols="" >
          <v-text-field
            v-model="faultyQuantity"
            label="Faulty Product Quantity"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </v-row>

      <div class="flex justify-end py-0">
        <v-btn
          class="w-42 h-14 mr-32 mb-4 rounded-md"
          outlined
          v-if="!isInnerEditAvailable"
          @click="addEntry"
        >
          Add
        </v-btn>
      </div>
      <v-simple-table v-if="entries.length" class="m-auto">
        <template v-slot:default>
          <thead>
            <tr>
              <th>Product</th>
              <th>Store</th>
              <th>Quantity</th>
              <th>Remove</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(entry, index) in entries" :key="index">
              <td class="px-4">{{ entry.product.name }}</td>
              <td class="px-4">{{ entry.store.name }}</td>
              <td class="px-4">{{ entry.quantity }}</td>
              <td class="px-4">
                <v-icon color="red" @click="clickRemove(index)"
                  >mdi-close</v-icon
                >
              </td>
            </tr>
          </tbody>
        </template>
      </v-simple-table>
    </v-container>

    <v-row>
      <v-col cols="12" class="text-right mt-4">
        <v-btn outlined class="w-52 h-14 mr-8 rounded-md" @click="cancel">
          Cancel
        </v-btn>
        <v-btn
          class="bg-[#2A83FF] white--text w-52 h-14 mr-3 rounded-md"
          :color="$vuetify.theme.currentTheme.primary"
          @click="handleInventoryBtn"
        >
          {{ isInnerEditAvailable ? "Update" : "Submit" }}
        </v-btn>
      </v-col>
    </v-row>

    <v-dialog v-model="openEmpHistory" max-width="800">
      <HoldEmpInventory :employeeInventory="employeeInventory" />
    </v-dialog>
  </v-form>
</template>
<script>
import { debounce } from "@/utils/common";
import HoldEmpInventory from "./HoldEmpInventory";
export default {
  components: {
    HoldEmpInventory,
  },
  data() {
    return {
      products: [],
      activeStores: [],
      employees: [],
      entries: [],
      quantityInStore: null,
      selectedProduct: null,
      selectedStores: null,
      selectedEmployee: null,
      isOtpSent: false,
      openEmpHistory: false,
      selectedStatus: "ASSIGNED",
      usedQuantity: "",
      challanNumber: "",
      employeeOtp: "",
      valid: true,
      resendOtpCounter: 0,
      returnedQuantity: "",
      faultyQuantity: null,
      isProductFaulty: false,
      otpRequired: false,
      employeeInventory: [],
      searchUser: null,
      rules: {
        challanNumber: [
          (val) => (val || "").length > 0 || `Challan Number is required`,
        ],
      },
      faultyProductItem: [
        { text: "Yes", value: true },
        { text: "No", value: false },
      ],
      outStationOptions: [
        { text: "Yes", value: true },
        { text: "No", value: false },
      ],
      deliveryOptions: [
        { text: "By Hand", value: "BY_HAND" },
        { text: "By Courier", value: "BY_COURIER" },
      ],
      inventoryStatus: [
        { text: "Assigned", value: "ASSIGNED" },
        { text: "Collected", value: "COLLECTED" },
        { text: "Returned", value: "RETURNED" },
      ],
      trackingNo: "",
      modeOfDelivery: "",
      outStationDelivery: false,
    };
  },
  props: { innerEdit: Object },
  async mounted() {
    if (this.innerEdit?.item?.collectedBy) {
      this.searchUser = this.innerEdit.item?.collectedBy?.firstName;
      await this.fetchEmployees();
    }
    await this.fetchInventory();
    await this.fetchStores();
    await this.populateFields();
  },
  computed: {
    isInnerEditAvailable() {
      return this.innerEdit ? true : false;
    },
    isStatusReturned() {
      return this.selectedStatus === "RETURNED" ? true : false;
    },
    isStatusAssigned() {
      return this.selectedStatus === "ASSIGNED" ? true : false;
    },

    filteredProducts() {
      return this.products.filter(
        (product) => !this.productExistsInEntries(product._id)
      );
    },
    selectedEmployeeMobile() {
      const Employee = this.employees.find(
        (employee) => employee._id === this.selectedEmployee
      );
      return Employee ? Employee.mobile : null;
    },
    isChallanAvailable() {
      return this.innerEdit.challanNumber ? true : false;
    },
  },

  methods: {
    cancel() {
      this.$emit("cancel");
    },
    getFullName(employee) {
      return (
        `${employee.firstName} ${employee.lastName}` +
        (employee.wfmRole ? ` (${employee.wfmRole.role})` : "")
      );
    },
    debounceUser: debounce(async function (text) {
      if (!text || this.selectedEmployee) return true;
      this.fetchEmployees();
    }, 1000),
    async fetchEmployees() {
      try {
        const params = {};
        if (this.searchUser) {
          params.search = this.searchUser;
        }
        const { data } = await this.$axios.get("workforce/users", {
          params,
        });
        this.employees = data.users;
      } catch (error) {
        console.log(error?.response?.data?.message);
        if (error.response.data) {
          this.$toast.error(error?.response?.data?.message);
        }
      }
    },
    async fetchStores() {
      const { data } = await this.$axios.get("/workforce/ecom/stores", {
      });

      if (data.success) {
        this.activeStores = data.stores;
      }
    },
    async fetchInventory() {
      try {
        const params = {};
        if (this.innerEdit) {
          params.search = this.innerEdit.item?.productId?.productName;
        }
        const { data } = await this.$axios.get("/workforce/ecom/products", {
          params,
        });
        this.products = data?.products;
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    addEntry() {
      if (this.selectedProduct && this.selectedStores && this.quantityInStore) {
        const selectedProduct = this.products.find(
          (product) => product._id === this.selectedProduct
        );
        const selectedStore = this.activeStores.find(
          (store) => store._id === this.selectedStores
        );

        const entry = {
          product: {
            id: selectedProduct._id,
            name: selectedProduct.productName,
          },
          store: {
            id: selectedStore._id,
            name: selectedStore.storeName,
          },
          quantity: this.quantityInStore,
        };

        this.entries.push(entry);

        this.selectedProduct = null;
        this.selectedStores = null;
        this.quantityInStore = null;
      } else {
        console.error("Please fill in all fields");
      }
    },
    extractPayload() {
      const products = this.entries.map((el) => ({
        productId: el.product.id,
        storeId: el.store.id,
        quantityAssigned: el.quantity,
      }));

      const payloadData = {
        userId: this.selectedEmployee,
        products,
      };
      return payloadData;
    },
    async handleInventoryBtn() {
      try {
        let method, url, payload, quantityAssigned;

        if (this.innerEdit) {
          const requestId = this.innerEdit?.requestId?._id;
          const editProductId = this.innerEdit.item?.productId?._id;
          const isValid = this.$refs.form.validate();
          if (!isValid) return;

          method = "put";
          url = `workforce/ecom/inventory/emp/${requestId}`;
          payload = {
            collectedBy: this.selectedEmployee,
            product: {
              productId: editProductId,
              productStatus: this.selectedStatus,
            },
          };
          if (this.challanNumber) {
            payload.challanNumber = this.challanNumber;
          }
          if (this.isProductFaulty) {
            payload.product.expectedFaultyProduct = this.isProductFaulty;
            payload.product.qtyFaultyProduct = this.faultyQuantity;
          }
          if (this.outStationDelivery) {
            payload.product.outStationDelivery = this.outStationDelivery;
            payload.product.modeOfDelivery = this.modeOfDelivery;
            payload.product.trackingNo = this.trackingNo;
          }
          if (this.selectedStatus === "ASSIGNED") {
            payload.product.quantityAssigned = quantityAssigned;
          } else if (this.selectedStatus === "RETURNED") {
            payload.product.quantityUsed = Number(this.usedQuantity);
            payload.product.quantityReturned = Number(this.returnedQuantity);
          } else {
            if (this.otpRequired) {
              const isOtpConfirmed = await this.confirmOtp();

              if (!isOtpConfirmed) {
                return;
              }
            }
          }
        } else {
          method = "post";
          url = `/workforce/ecom/inventory/user`;
          payload = await this.extractPayload();
        }

        await this.$axios[method](url, payload);
        this.cancel();
      } catch (error) {
        console.log(error);
        const errorMessage = error?.response?.data?.message || "Order failed.";
        this.$toast.error(errorMessage);
      }
    },
    async sendOtp() {
      try {
        const payload = {
          phoneNumber: Number(this.selectedEmployeeMobile),
        };
        const response = await this.$axios.post(
          "/workforce/otp/generate",
          payload,
        );
        this.startResendOtpTimer();
        this.isOtpSent = true;
        const successMessage = response?.data?.message || "OTP sent!"
        this.$toast.success(successMessage);
      } catch (error) {
        const errorMessage = error?.response?.data?.message || "Failed to send OTP"
        this.$toast.error(errorMessage);
      }
    },

    startResendOtpTimer() {
      this.resendOtpCounter = 60;
      const timerInterval = setInterval(() => {
        this.resendOtpCounter -= 1;
        if (this.resendOtpCounter <= 0) {
          clearInterval(timerInterval);
        }
      }, 1000);
    },

    async confirmOtp() {
      try {
        const payload = {
          phoneNumber: Number(this.selectedEmployeeMobile),
          otpCode: this.employeeOtp,
          skipTaskValidation: true,
        };
        const response = await this.$axios.post(
          "/workforce/otp/verify",
          payload,
        );

        if (response.data.success) {
          const successMessage = response?.data?.message || "Successfully Submitted !!!"
          this.$toast.success(successMessage);
          return true;
        } else {
          this.$toast.error("Incorrect OTP. Please try again.");
          this.employeeOtp = "";
          return false;
        }
      } catch (err) {
        const errorMessage = err?.response?.data?.message || "Error confirming OTP. Please try again."
        this.$toast.error(errorMessage);
        this.employeeOtp = "";
        return false;
      }
    },
    productExistsInEntries(productId) {
      return this.entries.some((entry) => entry.product.id === productId);
    },
    clickRemove(index) {
      const removedEntry = this.entries.splice(index, 1)[0];

      const removedProduct = {
        _id: removedEntry.product.id,
        productName: removedEntry.product.name,
      };
      this.products.push(removedProduct);
    },
    async checkEmployeeHistory() {
      try {
        if (!this.selectedEmployee) {
          this.$toast.error("Select the employee");
          return;
        }
        const payload = {
          collectedById: this.selectedEmployee,
        };
        const response = await this.$axios.post(
          "/workforce/ecom/inventory/user/check",
          payload,
        );
        this.employeeInventory = response.data.products;
        this.openEmpHistory = true;
      } catch (error) {
        console.log(error);
      }
    },
    async getConfig() {
      try {
        const response = await this.$axios.get("/workforce/org/config", {
        });

        const data = response.data?.config;
        this.otpRequired = data.isCollectionOtpRequired;
      } catch (error) {
        console.error("Error fetching config:", error);
      }
    },
    async populateFields() {
      if (this.innerEdit) {
        this.selectedEmployee = this.innerEdit.item?.collectedBy?._id;
        this.challanNumber = this.innerEdit?.challanNumber;
        this.selectedProduct = this.innerEdit.item?.productId?._id;
        this.selectedStores = this.innerEdit.item?.storeId?._id;
        this.quantityInStore = this.innerEdit.item?.quantityAssigned;
        this.selectedStatus = this.innerEdit.item?.productStatus;
        this.usedQuantity = this.innerEdit.item?.quantityUsed;
        this.returnedQuantity = this.innerEdit.item?.quantityReturned;
        await this.getConfig();
      }
    },
  },
};
</script>
