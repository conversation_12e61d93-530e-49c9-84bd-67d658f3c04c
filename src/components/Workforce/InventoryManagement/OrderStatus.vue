<template>
  <div class="p-3">
    <div
      class="justify-end items-center my-3"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
    >
      <div class="justify-end mr-4" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="orderNumber"
            label="Order Number"
            placeholder="Order Number"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-select
            v-model="filterOrderStatus"
            label="Order Status"
            placeholder="Order Status"
            solo
            :items="orderStatusOptions"
            item-text="value"
            flat
            clearable
            @change="handleInput"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </div>
    </div>
    <div v-if="showTable" class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="orders"
        class="pl-7 pr-7 pt-4"
        :options.sync="options"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :server-items-length="totalItems"
        :loading="loading"
        :single-expand="singleExpand"
        :expanded.sync="expanded"
        show-expand
        item-key="_id"
        fixed-header
      >
        <template v-slot:item.employeeName="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.updatedBy?.firstName }} {{ item.updatedBy?.lastName }}
          </td>
        </template>
        <template v-slot:item.customerName="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.linkedCustomerId?.name }}
          </td>
        </template>
        <template v-slot:item.orderSrNo="{ item }">
          <td class="px-4 font-semibold">{{ item.orderSrNo }}</td>
        </template>
        <template v-slot:item.updatedAt="{ item }">
          <td class="px-4 font-semibold">
            {{ $dayjs(item.updatedAt).format('DD/MM/YYYY') }}
            <br />
            {{ $dayjs(item.updatedAt).format('hh:mm A') }}
          </td>
        </template>
        <template v-slot:item.updatedBy="{ item }">
          <td class="px-4 py-4 font-semibold">
            {{ item.updatedBy?.firstName }} {{ item.updatedBy?.lastName }}
          </td>
        </template>
        <template v-slot:item.orderStatus="{ item }">
          <OrderStatuses
            :orderStatus="item.orderStatus"
            :statuses="orderStatusOptions"
            @statusChange="(status) => openCommentDialog(status, item._id)"
          />
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4">
            <action-button
              :item="item"
              :canEdit="canEdit"
              :canDelete="canDelete"
              :showEditButton="canEdit"
              :showDeleteButton="canDelete"
              :handleDelete="() => orderDelete(item)"
            />
          </td>
        </template>
        <template v-slot:expanded-item="{ headers, item }">
          <td :colspan="headers.length">
            <v-data-table
              class="expanded-table bg-gray-100 dark-bg-default p-7"
              :headers="expandedHeaders"
              :items="item.products"
              :items-per-page="5"
              hide-default-footer
            >
              <template v-slot:item.productName="{ item }">
                <td class="px-4 font-semibold">
                  {{ item.product?.productName }}
                </td>
              </template>
              <template v-slot:item.orderedQuantity="{ item }">
                <td class="px-4 font-semibold">
                  {{ item.orderedQuantity }}
                </td>
              </template>
              <template v-slot:item.orderAmt="{ item }">
                <td class="px-4 font-semibold">
                  {{ item.product?.productPrice }}
                </td>
              </template>
            </v-data-table>
          </td>
        </template>
      </v-data-table>
      <v-dialog v-model="openDialog" max-width="500" persistent>
        <v-card>
          <v-card-title class="text-h5 justify-center"> Add Comment </v-card-title>
          <v-card-text class="text-center">
            <v-text-field label="Comment" v-model="comment"> </v-text-field>
          </v-card-text>
          <v-card-actions class="flex justify-end mb-2">
            <v-btn @click="handleCancel" class="rounded-md w-24" outlined>Cancel</v-btn>
            <v-btn
              @click="handleAddComment"
              class="w-24 rounded-md"
              outlined
              :disabled="isButtonEnable"
              :class="{
                'bg-[#2A83FF] white--text': !isButtonEnable,
                gray: isButtonEnable
              }"
              >Add</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>
    <div v-else-if="showAddInventory">
      <AddInventory @cancel="toggleShowTable" />
    </div>
  </div>
</template>

<script>
import AddInventory from '~/components/Workforce/InventoryManagement/AddInventory.vue'
import ActionButton from '../../ActionButton.vue'
import OrderStatuses from './OrderStatuses.vue'
import permissionData from '@/utils/permissions'

import { debounce, hasPermission, downloadExcel } from '@/utils/common'
export default {
  name: 'PartStatus',
  components: {
    AddInventory,
    ActionButton,
    OrderStatuses
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      showFilters: false,
      options: { itemsPerPage: 5, page: 1 },
      totalItems: 0,
      loading: true,
      openDialog: false,
      comment: '',
      headers: [
        { text: 'Customer', value: 'customerName', sortable: false },
        { text: 'Order No.', value: 'orderSrNo', sortable: false },
        { text: 'Order Status', value: 'orderStatus', sortable: false },
        { text: 'Assigned To', value: 'employeeName', sortable: false },
        { text: 'Updated On', value: 'updatedAt', sortable: false },
        { text: 'Updated By', value: 'updatedBy', sortable: false },
        { text: 'Action', value: 'action', sortable: false }
      ],
      expandedHeaders: [
        { text: 'Spare Part', value: 'productName', sortable: false },
        { text: 'Qty.', value: 'orderedQuantity', sortable: false },
        { text: 'Order Amt.', value: 'orderAmt', sortable: false }
      ],
      orders: [],
      expanded: [],
      singleExpand: true,
      showTable: true,
      orderNumber: '',
      selectedOrder: null,
      selectedOrderStatus: '',
      filterOrderStatus: '',
      orderStatusOptions: [
        { label: 'Placed', value: 'PLACED' },
        { label: 'Dispatched', value: 'DISPATCHED' },
        { label: 'Installed', value: 'INSTALLED' },
        { label: 'Delivered', value: 'DELIVERED' },
        { label: 'Cancelled', value: 'CANCELLED' },
        { label: 'Returned', value: 'RETURNED' }
      ],
      showAddInventory: false
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchOrder()
      },
      deep: true
    }
  },
  async mounted() {
    await this.fetchOrderStatus()
  },
  computed: {
    isButtonEnable() {
      return (
        (this.selectedOrderStatus === 'CANCELLED' || this.selectedOrderStatus === 'RETURNED') &&
        !this.comment
      )
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.inventoryEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.inventoryDelete)
    }
  },
  methods: {
    toggleFilters() {
      this.showFilters = !this.showFilters
    },
    toggleShowTable() {
      this.showAddInventory = false
      this.showTable = true
    },
    toggleshowAddInventory() {
      this.showTable = false
      this.showAddInventory = true
    },
    openCommentDialog(status, orderId) {
      this.comment = ''
      this.selectedOrder = orderId
      this.selectedOrderStatus = status
      this.openDialog = true
    },
    handleCancel() {
      this.comment = ''
      this.selectedOrder = null
      this.selectedOrderStatus = ''
      this.openDialog = false
    },

    async handleAddComment() {
      try {
        const payload = {
          orderStatus: this.selectedOrderStatus,
          comment: this.comment
        }
        const response = await this.$axios.put(
          `/workforce/ecom/order/${this.selectedOrder}`,
          payload
        )

        this.fetchOrder()

        this.handleCancel()
        const successMessage = response?.data?.message || `Order status updated !!`
        this.$toast.success(successMessage)
      } catch (error) {
        const errorMessage = error.response?.data?.message || 'Order failed.'
        this.$toast.error(errorMessage)
      }
    },
    async fetchOrder() {
      try {
        this.loading = true
        const { page, itemsPerPage } = this.options
        const params = { page, limit: itemsPerPage }
        if (this.orderNumber) {
          params.search = this.orderNumber
        }
        if (this.filterOrderStatus) {
          params.orderStatus = this.filterOrderStatus
        }

        const response = await this.$axios.get('/workforce/ecom/orders', {
          params: params
        })

        const allOrders = response.data.orders
        this.orders = allOrders
        this.totalItems = response.data.pagination.totalCount
        this.loading = false
      } catch (error) {
        console.log(error)
      }
    },
    async orderDelete(item) {
      try {
        this.selectedOrder = item._id
        const response = await this.$axios.delete(`/workforce/ecom/order/${this.selectedOrder}`)
        await this.fetchOrder()
        const successMessage = response?.data?.message || 'Order deleted successfully!'
        this.$toast.success(successMessage)
      } catch (error) {
        console.error('An error occurred:', error)
        const errorMessage = error.response.data.message || 'Error deleting Order.'
        this.$toast.error(errorMessage)
      }
    },
    async fetchOrderStatus() {
      await this.$axios.get('/workforce/ecom/order/listofstatus')
    },
    handleInput: debounce(function () {
      this.options.page = 1
      this.fetchOrder()
    }, 1000),
    async fetchAllOrders() {
      const itemsPerPage = 20
      const totalPages = Math.ceil(this.totalItems / itemsPerPage)

      const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1)

      const apiCalls = pageNumbers.map(async (page) => {
        return await this.$axios.get('/workforce/ecom/orders', {
          params: {
            page,
            limit: itemsPerPage,
            search: this.orderNumber || undefined,
            orderStatus: this.filterOrderStatus || undefined
          }
        })
      })

      return Promise.all(apiCalls).then((responses) => {
        return responses.reduce((acc, res) => {
          return acc.concat(res.data.orders)
        }, [])
      })
    },
    prepareData(orders) {
      const data = orders.map((order) => {
        const row = {
          'Order Number': order.orderSrNo,
          'Customer Name': order.linkedCustomerId?.name || '',
          'Order Status': order.orderStatus || '',
          'Assigned To': order.updatedBy?.fullName || ''
        }

        order.products?.forEach((product, index) => {
          const partIndex = index + 1
          row[`Spare Part ${partIndex}`] = product.product?.productName || ''
          row[`Quantity ${partIndex}`] = product?.orderedQuantity || 0
          row[`Amount ${partIndex}`] = product.product?.productPrice || 0
        })

        return row
      })

      downloadExcel(data, 'Orders Data', 'OrderData.xlsx')
    },
    exportData() {
      if (this.totalItems === 0) {
        this.$toast.error('No data available for export')
        return
      }

      this.fetchAllOrders()
        .then((orders) => {
          this.prepareData(orders)
          this.$toast.success('Data exported successfully!')
        })
        .catch((error) => {
          console.error('Error exporting data:', error)
          this.$toast.error('Error exporting data.')
        })
    },
    getTabButtons() {
      return [
        {
          key: 'export-consumed',
          label: 'Export',
          icon: 'mdi-download',
          color: 'primary',
          class: 'white--text',
          action: 'exportData',
          visible: this.canEdit
        }
      ]
    }
  }
}
</script>

<style scoped>
.v-data-table >>> .v-data-table__wrapper tbody tr.v-data-table__expanded__content {
  box-shadow: none;
}
</style>
