<template>
  <div class="p-2">
    <div
      class="justify-end items-center"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
      v-if="!showRefillInventory"
    >
      <div
        class="justify-end mr-4"
        :class="[$vuetify.breakpoint.lgAndUp ? 'flex items-center' : 'flex-col']"
      >
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterPartName"
            label="Part Name/Part Number"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterOrder"
            label="Order Number"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-select
            v-model="selectedOrderStatus"
            label="Select Status"
            :items="inventoryOrderOptions"
            item-value="value"
            item-text="label"
            solo
            flat
            clearable
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </div>
      <!-- Button removed from here - now managed by parent -->
    </div>
    <div v-if="showTable" class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="inventoryOrders"
        class="pl-2 pr-2 pt-2 rounded-lg"
        :options.sync="options"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        :server-items-length="totalItems"
        :loading="loading"
        :single-expand="singleExpand"
        :expanded.sync="expanded"
        show-expand
        item-key="_id"
        fixed-header
      >
        <template v-slot:item.partName="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.product?.productName }}
          </td>
        </template>
        <template v-slot:item.modelNumber="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.product?.modelNumber }}
          </td>
        </template>
        <template v-slot:item.partnerName="{ item }">
          <td class="px-4 font-semibold">{{ item.partner?.partnerName }}</td>
        </template>
        <template v-slot:item.orderNumber="{ item }">
          <td class="px-4 font-semibold">{{ item.orderNumber }}</td>
        </template>
        <template v-slot:item.costPrice="{ item }">
          <td class="px-4 font-semibold">{{ item.costPricePerUnit }}</td>
        </template>
        <template v-slot:item.totalAmount="{ item }">
          <td class="px-4 font-semibold">{{ item.totalOrderAmount }}</td>
        </template>
        <template v-slot:item.orderStatus="{ item }">
          <OrderStatuses
            :orderStatus="item.orderStatus"
            :statuses="inventoryOrderOptions"
            @statusChange="(status) => onStatusChange(status, item._id)"
          />
        </template>
        <template v-slot:item.updatedBy="{ item }">
          <td class="px-4 font-semibold">
            {{ item.updatedBy.firstName }} {{ item.updatedBy.lastName }}
          </td>
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4">
            <action-button
              :item="item"
              :canEdit="canEdit"
              :canDelete="false"
              :handleEdit="() => orderEdit(item)"
              :showDeleteButton="false"
              :showEditButton="canEdit"
              :isButtonDisabled="disableActionBtn(item)"
            />
          </td>
        </template>

        <template v-slot:expanded-item="{ headers, item }">
          <td :colspan="headers.length">
            <v-data-table
              class="expanded-table bg-gray-100 dark-bg-default p-7"
              :headers="expandedHeaders"
              :items="item.orderDeliveryLocation"
              :items-per-page="5"
              hide-default-footer
            >
              <template v-slot:item.storeLocation.storeName="{ item }">
                <td class="px-4">{{ item.storeLocation.storeName }}</td>
              </template>
              <template v-slot:item.orderedQuantity="{ item }">
                <td class="px-4">{{ item.orderedQuantity }}</td>
              </template>
              <template v-slot:item.inStoreOrderStatus="{ item }">
                <td class="px-4" :class="getStoreOrderStatusClr(item.inStoreOrderStatus)">
                  {{ item.inStoreOrderStatus }}
                </td>
              </template>
              <template v-slot:item.storeLocation.storeLocation="{ item }">
                <td class="px-4">
                  {{ Object.values(item.storeLocation.storeLocation).join(', ') }}
                </td>
              </template>
            </v-data-table>
          </td>
        </template>
      </v-data-table>
    </div>
    <div v-else-if="showRefillInventory">
      <AddOrder @cancel="toggleShowTable" :editData="selectedOrderData" />
    </div>
  </div>
</template>

<script>
import AddOrder from '~/components/Workforce/InventorySettings/AddOrder.vue'
import ActionButton from '@/components/ActionButton.vue'
import { debounce } from '@/utils/common'
import OrderStatuses from './OrderStatuses.vue'
import { hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
export default {
  name: 'InventoryIn',
  components: {
    AddOrder,
    ActionButton,
    OrderStatuses
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      options: { itemsPerPage: 5, page: 1 },
      totalItems: 0,
      loading: true,
      inventoryOrders: [],
      showTable: true,
      singleExpand: true,
      inventoryOrderOptions: [
        { label: 'Placed', value: 'PLACED' },
        { label: 'Completed', value: 'COMPLETED' },
        { label: 'In-Progress', value: 'IN_PROGRESS' },
        { label: 'Return', value: 'RETURN' }
      ],
      expandedHeaders: [
        {
          text: 'Store Name',
          value: 'storeLocation.storeName',
          sortable: false
        },
        {
          text: 'Store Address',
          value: 'storeLocation.storeLocation',
          sortable: false
        },
        { text: 'Quantity', value: 'orderedQuantity', sortable: false },
        { text: 'Store Status', value: 'inStoreOrderStatus', sortable: false }
      ],
      filterPartName: '',
      filterOrder: '',
      selectedOrderData: null,
      showRefillInventory: false,
      selectedOrderStatus: '',
      expanded: []
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchInventoryOrder()
      },
      deep: true
    }
  },
  computed: {
    headers() {
      const headers = [
        { text: 'Spare Part', value: 'partName', sortable: false },
        { text: 'Part/Model No.', value: 'modelNumber', sortable: false },
        { text: 'Brand Name', value: 'partnerName', sortable: false },
        { text: 'Order No.', value: 'orderNumber', sortable: false },
        { text: 'Cost Price(Per Unit)', value: 'costPrice', sortable: false },
        { text: 'Total Amt.', value: 'totalAmount', sortable: false },
        { text: 'Status', value: 'orderStatus', sortable: false },
        { text: 'Updated By', value: 'updatedBy', sortable: false },
        this.isActionEnable ? { text: 'Action', value: 'action', sortable: false } : []
      ]
      return headers
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.inventoryDelete)
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.inventoryEdit)
    },
    canAdd() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    },
    isActionEnable() {
      return this.canEdit || this.canDelete
    }
  },
  methods: {
    getStoreOrderStatusClr(status) {
      const statuses = {
        PLACED: 'text-sky-500',
        CANCELLED: 'text-red-500',
        IN_PROGRESS: 'text-blue-500',
        COMPLETED: 'text-green-500',
        RETURNED: 'text-yellow-500'
      }

      return statuses[status]
    },
    disableActionBtn(item) {
      return item.orderStatus === 'COMPLETED' || (!this.canEdit && !this.canDelete)
    },
    toggleShowTable() {
      this.showRefillInventory = false
      this.showTable = true
      this.selectedOrderData = null
      this.fetchInventoryOrder()
    },
    toggleshowRefillInventory() {
      this.showTable = false
      this.showRefillInventory = true
    },
    async fetchInventoryOrder() {
      this.loading = true
      const { page, itemsPerPage } = this.options
      const params = { page, limit: itemsPerPage }
      if (this.filterPartName) {
        params.productSearch = this.filterPartName
      }
      if (this.filterOrder) {
        params.search = this.filterOrder
      }
      if (this.selectedOrderStatus) {
        params.orderStatus = this.selectedOrderStatus
      }
      const { data } = await this.$axios.get('/workforce/ecom/inventory/orders', {
        params: params
      })

      if (data.success) {
        this.inventoryOrders = data.inventoryOrders
        this.totalItems = data.pagination.totalCount
        this.loading = false
      }
    },
    async onStatusChange(status, id) {
      try {
        const selectedItem = this.inventoryOrders.find((item) => item._id === id)

        const partnerId = selectedItem.partner?._id
        const productId = selectedItem.product?._id
        const orderDeliveryLocation = selectedItem.orderDeliveryLocation
        const orderNumber = selectedItem.orderNumber

        const dataToSend = {
          orderStatus: status,
          partnerId: partnerId,
          productId: productId,
          orderDeliveryLocation: orderDeliveryLocation,
          orderNumber: orderNumber
        }
        const response = await this.$axios.put(`/workforce/ecom/inventory/order/${id}`, dataToSend)
        this.fetchInventoryOrder()
        const successMessage = response?.data?.message || 'Order status updated successfully !!'
        this.$toast.success(successMessage)
      } catch (error) {
        const errorMessage = error.response.data.message || 'Failed to update order status.'
        this.$toast.error(errorMessage)
      }
    },
    orderEdit(item) {
      this.selectedOrderData = item
      this.toggleshowRefillInventory()
    },
    handleInput: debounce(function () {
      this.options.page = 1
      this.fetchInventoryOrder()
    }, 1000),

    // Method to provide buttons to parent component
    getTabButtons() {
      return [
        {
          key: 'refill-inventory',
          label: 'Refill Inventory',
          icon: 'mdi-plus',
          color: 'primary',
          class: 'white--text',
          action: 'toggleshowRefillInventory',
          visible: this.canAdd
        }
      ]
    }
  }
}
</script>

<style scoped>
.v-data-table >>> .v-data-table__wrapper tbody tr.v-data-table__expanded__content {
  box-shadow: none;
}
</style>
