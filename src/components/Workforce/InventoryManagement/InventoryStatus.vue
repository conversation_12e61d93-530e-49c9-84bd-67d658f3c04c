<template>
  <div class="p-2">
    <div
      class="justify-end items-center"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
      v-if="!editInventoryRow && !showAddInventory"
    >
      <div class="justify-end" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterPartNumber"
            label="Part Name/Model Number"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-select
            v-model="filterPartStatus"
            label="Part Status"
            :items="productStatusItems"
            solo
            flat
            clearable
            @change="handleInput"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </div>
      <!-- Buttons removed from here - now managed by parent -->
    </div>
    <div v-if="!!editInventoryRow">
      <AddInventory @cancelUpdate="cancelUpdateInventory" :inventory="editInventoryRow" />
    </div>
    <div v-else-if="showTable" class="overflow-x-auto overflow-y-auto">
      <InventoryTable
        ref="inventoryTable"
        :headers="headers"
        :handler="fetchInventory"
        :expandedHeaders="expandedHeaders"
      >
        <template v-slot="{ item }">
          <td class="px-4">
            <action-button
              :item="item"
              :canEdit="canEdit"
              :canDelete="canDelete"
              :showDeleteButton="canDelete"
              :showEditButton="canEdit"
              :handleEdit="() => inventoryEdit(item)"
              :handleDelete="() => inventoryDelete(item)"
              :isButtonDisabled="disableActionBtn(item)"
            />
          </td>
        </template>
      </InventoryTable>
    </div>
    <div v-else-if="showAddInventory">
      <AddInventory @cancel="toggleShowTable" />
    </div>

    <v-dialog v-model="showConfirmationDialog" max-width="600">
      <v-card>
        <v-card-title class="text-h5 justify-center"> Confirm Delete </v-card-title>
        <v-card-text class="text-center">
          Are you sure you want to delete the product?
        </v-card-text>
        <v-card-actions class="flex justify-center mb-2">
          <v-btn outlined color="grey" class="rounded-lg mr-4 px-6" @click="cancelUpdate">
            <span class="text-black">Cancel</span>
          </v-btn>
          <v-btn color="red" class="white--text rounded-lg px-6" @click="handleDeleteInventory">
            Confirm
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import AddInventory from '~/components/Workforce/InventoryManagement/AddInventory.vue'
import ActionButton from '../../ActionButton.vue'
import { convertToTitleCase, debounce } from '@/utils/common'
import InventoryTable from './Table.vue'
import { hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
export default {
  name: 'InventoryIn',
  components: {
    AddInventory,
    ActionButton,
    InventoryTable
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      expandedHeaders: [
        {
          text: 'Store Name',
          value: 'storeLocation.storeName',
          sortable: false
        },
        { text: 'Quantity', value: 'quantityInStock', sortable: false },
        {
          text: 'Store Status',
          value: 'storeLocation.storeStatus',
          sortable: false
        },
        {
          text: 'Availability Status',
          value: 'availabilityStatus',
          sortable: false
        }
      ],
      showTable: true,
      productName: '',
      modelNumber: '',
      availableNumber: '',
      filterPartNumber: '',
      filterPartStatus: '',
      editInventoryRow: null,
      productPrice: null,
      discount: null,
      tax: null,
      splDiscount: null,
      selectedProduct: null,
      productStatusItems: ['IN STOCK', 'OUT OF STOCK'],
      showAddInventory: false,
      showConfirmationDialog: false
    }
  },
  computed: {
    headers() {
      const headers = [
        { text: 'Spare Part', value: 'productName', sortable: false },
        { text: 'Part/Model no.', value: 'modelNumber', sortable: false },
        {
          text: 'Brand Name',
          value: 'partnerDetails.partnerName',
          sortable: false
        },
        { text: 'Cost Price(per Unit)', value: 'costPrice', sortable: false },

        {
          text: 'Selling Price(per Unit)',
          value: 'productPrice',
          sortable: false
        },
        { text: 'Taxes', value: 'tax', sortable: false },
        { text: 'Discount', value: 'discount', sortable: false },
        { text: 'Spl. Discount', value: 'splDiscount', sortable: false },
        { text: 'Available Qty.', value: 'availableNumber', sortable: false },
        { text: 'Status', value: 'availabilityStatus', sortable: false },
        this.isActionEnable ? { text: 'Action', value: 'action', sortable: false } : []
      ]
      return headers
    },
    canAdd() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.inventoryEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.inventoryDelete)
    },
    isActionEnable() {
      return this.canAdd || this.canEdit || this.canDelete
    }
  },
  methods: {
    convertStatus(item) {
      return convertToTitleCase(item)
    },
    cancelUpdateInventory() {
      this.editInventoryRow = null
    },
    toggleShowTable() {
      this.showAddInventory = false
      this.showTable = true
    },
    toggleshowAddInventory() {
      this.showTable = false
      this.showAddInventory = true
    },
    async fetchInventory({ page = '1', limit = '10' }) {
      try {
        const params = {
          page,
          limit
        }
        if (this.filterPartNumber) {
          params.search = this.filterPartNumber
        }
        if (this.filterPartStatus) {
          params.availabilityStatus = this.filterPartStatus.split(' ').join('_')
        }
        const response = await this.$axios.get('/workforce/ecom/products', {
          params: params
        })

        const data = response.data
        data.items = data.products
        return response.data
      } catch (error) {
        console.error('An error occurred:', error)
      }
    },
    inventoryEdit(item) {
      this.editInventoryRow = item
    },
    inventoryDelete(item) {
      this.selectedProduct = item._id
      this.showConfirmationDialog = true
    },
    disableActionBtn() {
      return !this.canEdit && !this.canDelete
    },
    async handleDeleteInventory() {
      try {
        const response = await this.$axios.delete(`/workforce/ecom/product/${this.selectedProduct}`)
        await this.$refs.inventoryTable.getDataFromApi()
        const successMessage = response?.data?.message || 'Product deleted successfully!'
        this.$toast.success(successMessage)
      } catch (error) {
        const errorMessage = error.response.data.message || 'Order failed.'
        this.$toast.error(errorMessage)
      }

      this.showConfirmationDialog = false
    },
    async exportProductDataToExcel() {
      try {
        const data = await this.fetchInventory({ page: 1, limit: 100 })

        const dataForExcel = this.prepareDataForExcel(data.products)

        const ws = XLSX.utils.json_to_sheet(dataForExcel)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Product Data')
        XLSX.writeFile(wb, 'product_inventory_data.xlsx')
      } catch (error) {
        console.error('Error exporting data:', error)
      }
    },
    prepareDataForExcel(products) {
      const formattedData = []
      let count = 0
      products.forEach((product) => {
        count++
        product.inStoreAvailability.forEach((store, index) => {
          formattedData.push({
            'Sr.No': index == 0 ? count : '',
            'Product Name': index == 0 ? product.productName : '',
            'Model Number': index == 0 ? product.modelNumber : '',
            'Brand Name': index == 0 ? product.partnerDetails.partnerName : '',
            'Cost Price': product.costPricePerUnit,
            'Selling Price': product.productPrice,
            'Product Availability Status': product.availabilityStatus,
            'Store Name': store.storeLocation.storeName,
            'Store Address (City)': store.storeLocation.storeLocation.city,
            'Store Address (State)': store.storeLocation.storeLocation.state,
            'Store Address (Country)': store.storeLocation.storeLocation.country,
            'Quantity in Stock': store.quantityInStock,
            'Store Availability Status': store.availabilityStatus
          })
        })
      })

      return formattedData
    },
    cancelUpdate() {
      this.showConfirmationDialog = false
      this.selectedProduct = null
    },
    handleInput: debounce(function () {
      this.$refs.inventoryTable.getDataFromApi()
      this.$refs.inventoryTable.resetPage()
    }, 1000),

    // Method to provide buttons to parent component
    getTabButtons() {
      return [
        {
          key: 'add-inventory',
          label: 'Add Inventory',
          icon: 'mdi-plus',
          color: 'primary',
          class: 'white--text',
          action: 'toggleshowAddInventory',
          visible: this.canAdd
        },
        {
          key: 'export-status',
          label: 'Export',
          icon: 'mdi-download',
          color: 'primary',
          class: 'white--text',
          action: 'exportProductDataToExcel',
          visible: this.canAdd || this.canEdit || this.canDelete
        }
      ]
    }
  }
}
</script>

<style scoped></style>
