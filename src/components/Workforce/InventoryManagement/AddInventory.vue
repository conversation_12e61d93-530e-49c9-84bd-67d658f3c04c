<template>
  <v-form class="p-12 bg-white dark-bg-custom rounded-lg mt-4">
    <p v-if="inventory" class="text-[#6B6D78] dark-text-color text-2xl">Update Inventory</p>
    <p v-else class="text-[#6B6D78] dark-text-color text-2xl">Add Inventory</p>
    <v-form ref="form" v-model="valid" lazy-validation>
      <v-container>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Order Number</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="orderNumber"
              :disabled="isEditModeEnable"
              outlined
              dense
              hide-details
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Spare Part</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="partName"
              :disabled="isEditModeEnable"
              label="Spare part name"
              outlined
              dense
              hide-details
              :rules="[(v) => !!v || 'Spare part name is required']"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Brands:</p>
          </v-col>
          <v-col cols="">
            <v-autocomplete
              flat
              :menu-props="{ offsetY: true }"
              v-model="partner"
              label="Search Brand"
              :items="activePartners"
              @update:search-input="fetchPartners"
              :search-input.sync="searchPartner"
              item-text="partnerName"
              item-value="_id"
              outlined
              dense
              hide-details
              :rules="[(v) => !!v || 'Brand/Partner name is required']"
            ></v-autocomplete>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Part/Model No.</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="partNumber"
              :disabled="isEditModeEnable"
              label="Spare part model number"
              outlined
              dense
              hide-details
              :rules="[(v) => !!v || 'Spare part model number is required']"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Cost Price(per Unit)</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="costPricePerUnit"
              outlined
              dense
              hide-details
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Selling Price(per Unit)</p>
          </v-col>
          <v-col cols="">
            <v-text-field v-model="productPrice" outlined dense hide-details></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Taxes</p>
          </v-col>
          <v-col cols="">
            <v-text-field v-model="tax" outlined dense hide-details></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Discount:</p>
          </v-col>
          <v-col cols="">
            <v-text-field v-model="discount" outlined dense hide-details></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Special Discount</p>
          </v-col>
          <v-col cols="">
            <v-text-field v-model="splDiscount" outlined dense hide-details></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Installation Charges</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="installationCharges"
              outlined
              hide-details
              dense
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Minimum Stock Limit</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              v-model="minStockThreshold"
              outlined
              dense
              hide-details
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Delivery Stores</p>
          </v-col>
          <v-col cols="">
            <v-select
              flat
              :menu-props="{ offsetY: true }"
              v-model="selectedStores"
              :items="activeStores"
              item-text="storeName"
              item-value="_id"
              multiple
              outlined
              dense
              hide-details
              :rules="[(v) => !!v.length || 'Store name is required']"
            ></v-select>
          </v-col>
        </v-row>
        <v-row v-for="(item, i) in quantities" :key="item.name">
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Qty delivered to ({{ item.name }})</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              type="number"
              v-model="quantityInStore[i]"
              @change="(value) => onQuantityChange(i, value)"
              outlined
              dense
              hide-details
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Total Quantity</p>
          </v-col>
          <v-col cols="">
            <v-text-field
              type="Total Quantity"
              v-model="calculateTotalQuantity"
              outlined
              disabled
              dense
              hide-details
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row v-if="!!inventory">
          <v-col cols="2" class="my-auto">
            <p class="mb-0">Product Image:</p>
          </v-col>
          <v-col cols="">
            <v-file-input
              v-model="productImages"
              prepend-icon="mdi-camera"
              multiple
              hide-details
            ></v-file-input>
          </v-col>
        </v-row>
      </v-container>
    </v-form>

    <v-row class="mt-1">
      <v-col v-if="!!inventory" cols="12" class="text-right">
        <v-btn outlined class="w-52 h-14 mr-8 rounded-md" @click="cancelUpdate">
          Cancel
        </v-btn>
        <v-btn
          class="bg-[#2A83FF] white--text w-52 h-14 rounded-md mr-3"
          :color="$vuetify.theme.currentTheme.primary"
          @click="handleEditInventory"
        >
          Update
        </v-btn>
      </v-col>
      <v-col v-else cols="12" class="text-right">
        <v-btn outlined class="w-52 h-14 mr-8 rounded-md" @click="cancel">
          Cancel
        </v-btn>
        <v-btn
          class="bg-[#2A83FF] white--text w-52 h-14 rounded-md mr-3"
          :color="$vuetify.theme.currentTheme.primary"
          @click="addInventory"
        >
          Add
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<script>
import { sortDropdownOptions } from "@/utils/common";

export default {
  props: ["inventory"],
  data() {
    return {
      partName: "",
      orderNumber: "",
      partner: "",
      partNumber: "",
      costPricePerUnit: "",
      productPrice: null,
      minStockThreshold: null,
      tax: null,
      discount: null,
      splDiscount: null,
      installationCharges: null,
      partners: [],
      productImages: [],
      stores: [],
      selectedStores: [],
      quantityInStore: [],
      valid: true,
    };
  },
  computed: {
    quantities() {
      return this.selectedStores.map((storeId) => {
        const store = this.stores.find((store) => storeId === store._id) || {};

        return { name: store.storeName, quantity: 0 };
      });
    },
    generateRandomNumber(min, max) {
      return Math.floor(Math.random() * (max - min + 1) + min);
    },
    isEditModeEnable() {
      if (this.$props.inventory) {
        return true;
      }
      return false;
    },
    activePartners() {
      const partners = this.partners.filter(
        (partner) => partner.partnerStatus !== "IN_ACTIVE"
      );

      return sortDropdownOptions(partners, "partnerName");
    },
    activeStores() {
      return this.stores.filter(
        (store) => store.storeStatus !== "NON_OPERATIONAL"
      );
    },
    calculateTotalQuantity() {
      return this.quantityInStore.reduce(
        (sum, quantity) => sum + Number(quantity),
        0
      );
    },
  },
  mounted() {
    this.fetchPartners();
    this.fetchStores();
    this.populateFields();
  },
  methods: {
    async handleEditInventory() {
      const updatedInventory = this.getInventoryData();
      const isValid = this.$refs.form.validate();
      if (!isValid) {
        this.$toast.error("Please fill all the required fields");
        return;
      }
      try {
        const response = await this.$axios.put(
          `/workforce/ecom/product/${this.$props.inventory._id}`,
          updatedInventory,
        );
        if (this.productImages.length) {
          this.uploadProductImages();
        }
        this.cancelUpdate();
        const errorMessage = response?.data?.message || "Product updated successfully!!"
        this.$toast.success(errorMessage);
      } catch (error) {
        const errorMessage = error.response.data.message || "Order failed.";
        this.$toast.error(errorMessage);
      }
    },
    async uploadProductImages() {
      const formData = new FormData();
      formData.append("productId", this.$props.inventory._id);
      for (const image of this.productImages) {
        formData.append("attachments", image);
      }

      try {
        await this.$axios.post(`/workforce/ecom/product/images`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      } catch (error) {
        console.error("An error occurred:", error);
        const errorMessage = error?.response?.data?.message || "Product Image Upload Failed !!"
        this.$toast.error(errorMessage);
      }
    },
    populateFields() {
      if (this.$props.inventory) {
        const inventoryData = this.$props.inventory;
        this.partName = inventoryData.productName;
        this.partNumber = inventoryData.modelNumber;
        this.orderNumber = inventoryData.lastOrderNo;
        this.productPrice = inventoryData.productPrice;
        this.tax = inventoryData.tax;
        this.minStockThreshold = inventoryData.minStockThreshold;
        this.discount = inventoryData.discount;
        this.splDiscount = inventoryData.splDiscount;
        this.installationCharges = inventoryData.installationCharges;
        this.partner = inventoryData.partnerDetails?._id;
        this.selectedStores =
          inventoryData.inStoreAvailability?.map(
            (item) => item.storeLocation?._id
          ) || [];
        this.quantityInStore =
          inventoryData.inStoreAvailability?.map(
            (item) => item.quantityInStock
          ) || [];
        this.costPricePerUnit = inventoryData.costPricePerUnit;
      }
    },
    onQuantityChange(storeIndx, quantity) {
      this.quantityInStore[storeIndx] = quantity;
    },
    async fetchPartners() {
      const { data } = await this.$axios.get("/workforce/ecom/partners", {
        params: {
          limit: 100,
        },
      });

      if (data.success) {
        this.partners = data.partners;
      }
    },
    async fetchStores() {
      const { data } = await this.$axios.get("/workforce/ecom/stores", {
      });

      if (data.success) {
        this.stores = data.stores;
      }
    },
    getInventoryData() {
      const inStoreAvailability = [];

      this.selectedStores.forEach((storeId, i) => {
        const store = this.stores.find((store) => store._id === storeId);
        const data = {
          storeLocation: store?._id,
          quantityInStock: Number(this.quantityInStore[i]),
        };

        inStoreAvailability.push(data);
      });

      const inventoryData = {
        inStoreAvailability,
        productName: this.partName,
        modelNumber: this.partNumber,
        lastOrderNo: this.orderNumber,
        costPricePerUnit: this.costPricePerUnit,
        productPrice: this.productPrice,
        tax: this.tax,
        discount: this.discount,
        splDiscount: this.splDiscount,
        installationCharges: this.installationCharges,
        minStockThreshold: Number(this.minStockThreshold),
        quantityInStock: this.calculateTotalQuantity,
        partnerDetails: this.partner,
      };

      return inventoryData;
    },
    async addInventory() {
      const inventoryData = this.getInventoryData();
      const isValid = this.$refs.form.validate();
      if (!isValid) {
        this.$toast.error("Please fill all the required fields");
        return;
      }
      try {
        const response = await this.$axios.post(
          "/workforce/ecom/product",
          inventoryData,
        );
        const successMessage = response?.data?.message || "Product added successfully !!"
        this.$toast.success(successMessage);
        this.cancel();
      } catch (error) {
        const errorMessage = error.response.data.message || "Order failed.";
        this.$toast.error(errorMessage);
      }
    },
    cancelUpdate() {
      this.$emit("cancelUpdate");
    },

    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>
