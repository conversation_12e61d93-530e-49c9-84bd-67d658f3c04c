<template>
  <v-menu offset-y>
    <template v-slot:activator="{ on }">
      <v-btn
        v-on="on"
        :style="getStatusColor(orderStatus)"
        class="text-capitalize"
      >
        {{ statuses.find((status) => status.value === orderStatus)?.value }}
        <v-icon right dark> mdi-chevron-down </v-icon>
      </v-btn>
    </template>
    <v-list
      style="
        max-height: 200px;
        overflow-y: auto;
        width: 250px;
        border-radius: 20px;
        text-align: center;
      "
    >
      <v-list-item
        v-for="status in statuses"
        :key="status.value"
        @click="handleStatusChange(status.value)"
      >
        <v-list-item-content :style="getStatusColor(status.value)">
          <span style="white-space: nowrap; font-size: 14px">{{
            status.label
          }}</span>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
import { orderStatusColors } from "@/utils/workforce/statusColors";

export default {
  props: ["orderStatus","statuses"],
  methods: {
    handleStatusChange(status) {
      this.$emit("statusChange", status);
    },
    getStatusColor(status) {
      const colorInfo = orderStatusColors[status];
      return {
        color: colorInfo ? colorInfo.textColor : "black",
        backgroundColor: colorInfo ? colorInfo.bgColor : "white",
        height: "39px",
        borderRadius: "20px",
        padding: "5px 10px",
        fontSize: "14px",
        boxShadow: "none",
        border: "1px solid transparent",
      };
    },
  },
};
</script>
