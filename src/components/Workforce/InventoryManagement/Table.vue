<template>
  <div>
    <v-data-table
      :headers="headers"
      :items="items"
      class="pl-2 pr-2 pt-2 rounded-lg"
      :options.sync="options"
      :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
      :server-items-length="totalItems"
      :single-expand="singleExpand"
      :expanded.sync="expanded"
      :loading="loading"
      show-expand
      item-key="_id"
      fixed-header
    >
      <template v-slot:item.productName="{ item }">
        <td class="px-4 py-6 font-semibold">{{ item.productName }}</td>
      </template>
      <template v-slot:item.partnerDetails.partnerName="{ item }">
        <td class="px-4 font-semibold">
          {{ item.partnerDetails.partnerName }}
        </td>
      </template>
      <template v-slot:item.partnerDetails.partnerStatus="{ item }">
        <td class="px-4 font-semibold">
          {{ item.partnerDetails.partnerStatus }}
        </td>
      </template>

      <template v-slot:item.splDiscount="{ item }">
        <td class="px-4 font-semibold">{{ item.splDiscount }}</td>
      </template>

      <template v-slot:item.discount="{ item }">
        <td class="px-4 font-semibold">{{ item.discount }}</td>
      </template>

      <template v-slot:item.tax="{ item }">
        <td class="px-4 font-semibold">{{ item.tax }}</td>
      </template>

      <template v-slot:item.modelNumber="{ item }">
        <td class="px-4 font-semibold">{{ item.modelNumber }}</td>
      </template>
      <template v-slot:item.updatedAt="{ item }">
        <td class="px-4 font-semibold">
          {{ $dayjs(item.updatedAt).format('DD/MM/YYYY hh:mm A') }}
        </td>
      </template>
      <template v-slot:item.storeName="{ item }">
        <td class="px-4 font-semibold">
          {{ getStoreName(item) }}
        </td>
      </template>
      <template v-slot:item.quantityInStock="{ item }">
        <td class="px-4 font-semibold">{{ getAvailableQuantity(item) }}</td>
      </template>
      <template v-slot:item.availableNumber="{ item }">
        <td class="px-4 font-semibold">{{ item.quantityInStock }}</td>
      </template>
      <template v-slot:item.quantityAssigned="{ item }">
        <td class="px-4 font-semibold">
          {{ item.userInventoryHistory.quantityAssigned || '-' }}
        </td>
      </template>
      <template v-slot:item.quantityTook="{ item }">
        <td class="px-4 font-semibold">
          {{ item.userInventoryHistory.quantityTook || '-' }}
        </td>
      </template>
      <template v-slot:item.customerName="{ item }">
        <td class="px-4 font-semibold">
          {{ item.customerOrderId?.linkedCustomerId?.name || '-' }}
        </td>
      </template>
      <template v-slot:item.orderSerialNumber="{ item }">
        <td class="px-4 font-semibold">
          {{ item.customerOrderId?.orderSrNo || '-' }}
        </td>
      </template>
      <template v-slot:item.quantitySold="{ item }">
        <td class="px-4 font-semibold">{{ item.quantitySold || '-' }}</td>
      </template>
      <template v-slot:item.activity="{ item }">
        <td class="px-4 font-semibold">{{ activityText(item) }}</td>
      </template>
      <template v-slot:item.productPrice="{ item }">
        <td class="px-4 font-semibold">{{ item.productPrice }}</td>
      </template>
      <template v-slot:item.costPrice="{ item }">
        <td class="px-4 font-semibold">{{ item.costPricePerUnit }}</td>
      </template>
      <template v-slot:item.reqNumber="{ item }">
        <td class="px-4 py-6 font-semibold">
          {{ item.requestId?.requestId }}
        </td>
      </template>
      <template v-slot:item.challanNumber="{ item }">
        <td class="px-4 py-6 font-semibold">
          {{ item.challanNumber }}
        </td>
      </template>
      <template v-slot:item.reqStatus="{ item }">
        <td :class="getRequestStatusClr(item.requestId?.requestStatus)" class="px-4 font-semibold">
          {{ item.requestId?.requestStatus }}
        </td>
      </template>
      <template v-slot:item.complaintNumber="{ item }">
        <td
          class="px-4 font-semibold cursor-pointer hover:text-blue-600"
          @click="() => navToComplaint(item.requestId?.complaint?.complainSrNo)"
        >
          {{ item.requestId?.complaint?.complainSrNo }}
        </td>
      </template>

      <template v-slot:item.complaintStatus="{ item }">
        <td
          :class="getComplaintStatusClr(item.requestId?.complaint?.status)"
          class="px-4 font-semibold"
        >
          {{ item.requestId?.complaint?.status }}
        </td>
      </template>

      <template v-slot:item.availabilityStatus="{ item }">
        <td class="px-4 font-semibold">
          <v-chip
            small
            class="rounded-xl white--text font-semibold"
            flat
            :color="item.availabilityStatus === 'IN_STOCK' ? 'success' : 'error'"
          >
            {{ item.availabilityStatus === 'IN_STOCK' ? 'In Stock' : 'Out Of Stock' }}
          </v-chip>
        </td>
      </template>
      <template v-slot:item.action="{ item }">
        <slot :item="item"></slot>
      </template>
      <template v-slot:item.updatedBy="{ item }">
        <td class="px-4 font-semibold">
          {{ updatedByFullName(item) }}
        </td>
      </template>
      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length">
          <div v-if="hasFaultyProducts(item.products)">
            <div class="expanded-table bg-gray-100 dark-bg-custom p-7">
              <p class="text-md font-semibold">Faulty Products Received</p>
              <v-data-table
                :headers="faultyHeaders"
                :items="getFaultyProducts(item.products)"
                hide-default-footer
              >
                <template v-slot:item.faultyProductName="{ item }">
                  <td class="px-4">{{ item.productId.productName }}</td>
                </template>
                <template v-slot:item.faultyQuantity="{ item }">
                  <td class="px-4">{{ item.qtyFaultyProduct }}</td>
                </template>
                <template v-slot:item.faultyReturnBy="{ item }">
                  <td class="px-4">
                    {{ extractName(item.collectedBy) }}
                  </td>
                </template>
                <template v-slot:item.faultyCollectedBy="{ item }">
                  <td class="px-4">
                    {{ extractName(item.productRemainingCollectedBy) }}
                  </td>
                </template>
              </v-data-table>
            </div>
          </div>
          <div class="p-7">
            <p class="text-md font-semibold" v-if="hasFaultyProducts(item.products)">
              Product Assignment
            </p>
            <ExpandedTableRow v-if="hasSmStatus(item)" :data="item" />
            <v-data-table
              v-else
              class="expanded-table bg-gray-100 dark-bg-custom"
              :headers="getExpandedHeaders(item)"
              :items="getExpandedRowItems(item)"
              :items-per-page="5"
              hide-default-footer
            >
              <template v-slot:item.storeLocation.storeName="{ item }">
                <td class="px-4">{{ item.storeLocation.storeName }}</td>
              </template>
              <template v-slot:item.quantityInStock="{ item }">
                <td class="px-4">{{ item.quantityInStock }}</td>
              </template>
              <template v-slot:item.collectedBy="{ item }">
                <td class="px-4">
                  {{ extractName(item.collectedBy) }}
                  <br />
                  {{
                    item.collectedDateTime
                      ? $dayjs(item.collectedDateTime).format('DD/MM/YYYY')
                      : '-'
                  }}
                </td>
              </template>
              <template v-slot:item.quantityTook="{ item }">
                <td class="px-4">
                  {{ item.userInventoryHistory?.quantityTook }}
                </td>
              </template>
              <template v-slot:item.quantityReturned="{ item }">
                <td class="px-4">
                  {{ item.userInventoryHistory?.quantityReturned }}
                </td>
              </template>
              <template v-slot:item.quantityAssigned="{ item }">
                <td class="px-4">
                  {{ item.userInventoryHistory?.quantityAssigned }}
                </td>
              </template>
              <template v-slot:item.storeLocation.storeStatus="{ item }">
                <td class="px-4">
                  <v-chip
                    small
                    class="rounded-xl white--text font-semibold"
                    flat
                    :color="item.storeLocation.storeStatus === 'OPERATIONAL' ? 'success' : 'error'"
                  >
                    {{
                      item.storeLocation.storeStatus === 'OPERATIONAL'
                        ? 'Operational'
                        : 'Non-Operational'
                    }}
                  </v-chip>
                </td>
              </template>
              <template v-slot:item.availabilityStatus="{ item }">
                <td class="px-4">
                  <v-chip
                    small
                    class="rounded-xl white--text font-semibold"
                    flat
                    :color="item.availabilityStatus === 'IN_STOCK' ? 'success' : 'error'"
                  >
                    {{ item.availabilityStatus === 'IN_STOCK' ? 'In Stock' : 'Out Of Stock' }}
                  </v-chip>
                </td>
              </template>
              <template v-slot:item.productName="{ item }">
                <td class="px-4">{{ item.productId.productName }}</td>
              </template>
              <template v-slot:item.storeName="{ item }">
                <td class="px-4">{{ item.storeId.storeName }}</td>
              </template>
              <template v-slot:item.qtyAssigned="{ item }">
                <td class="px-4">{{ item.quantityAssigned }}</td>
              </template>
              <template v-slot:item.qtyUsed="{ item }">
                <td class="px-4">{{ item.quantityUsed }}</td>
              </template>
              <template v-slot:item.qtyReturned="{ item }">
                <td class="px-4">{{ item.quantityReturned }}</td>
              </template>
              <template v-slot:item.assignedBy="{ item }">
                <td class="px-4">
                  {{ extractName(item.productAssignedBy) }}
                  <br />
                  {{ $dayjs(item.assignedDateTime).format('DD/MM/YYYY') }}
                </td>
              </template>
              <template v-slot:item.givenBy="{ item }">
                <td class="px-4">
                  {{ extractName(item.productGivenBy) }}
                </td>
              </template>
              <template v-slot:item.returnedTo="{ item }">
                <td class="px-4">
                  {{ extractName(item.productRemainingCollectedBy) }}
                  <br />
                  {{
                    item.returnedDateTime ? $dayjs(item.returnedDateTime).format('DD/MM/YYYY') : '-'
                  }}
                </td>
              </template>
              <template v-slot:item.deliveryMode="{ item }">
                <td class="px-4">
                  {{ item.modeOfDelivery }}
                </td>
              </template>
              <template v-slot:item.tracking="{ item }">
                <td class="px-4">
                  {{ item.trackingNo }}
                </td>
              </template>
              <template v-slot:item.expandStatus="{ item }">
                <td :class="getProductStatusClr(item.productStatus)" class="px-4">
                  {{ item.productStatus }}
                </td>
              </template>
              <template v-slot:item.expandAction="{ item }">
                <td class="px-4">
                  <v-icon @click="expandEdit(item)" style="color: #4fa6ff" v-show="canEdit"
                    >mdi-pencil</v-icon
                  >
                </td>
              </template>
            </v-data-table>
          </div>
        </td>
      </template>
    </v-data-table>
  </div>
</template>

<script>
import { hasPermission, fullName } from '@/utils/common'
import permissionData from '@/utils/permissions'
import ExpandedTableRow from './ExpandedRow.vue'
import * as XLSX from 'xlsx'
export default {
  name: 'TableComponent',
  props: ['handler', 'headers', 'expandedHeaders'],
  components: { ExpandedTableRow },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      totalItems: 0,
      loading: true,
      items: [],
      options: { itemsPerPage: 5, page: 1 },
      singleExpand: true,
      faultyHeaders: [
        { text: 'Product Name', value: 'faultyProductName', sortable: false },
        { text: 'Qty.', value: 'faultyQuantity', sortable: false },
        { text: 'Return By', value: 'faultyReturnBy', sortable: false },
        { text: 'Collected By', value: 'faultyCollectedBy', sortable: false }
      ],
      expanded: []
    }
  },
  watch: {
    options: {
      handler() {
        this.getDataFromApi()
      },
      deep: true
    }
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.inventoryEdit)
    }
  },
  methods: {
    getProductStatusClr(status) {
      const statuses = {
        ASSIGNED: 'text-blue-500',
        COLLECTED: 'text-green-500',
        CONSUMED: 'text-sky-500',
        RETURNED: 'text-yellow-500'
      }

      return statuses[status] || 'text-gray-500'
    },
    getComplaintStatusClr(status) {
      const statuses = {
        RAISED: 'text-indigo-500',
        ON_HOLD: 'text-yellow-500',
        APPROVAL_REQUEST_RAISED: 'text-sky-500',
        PRE_APPROVED: 'text-emerald-500', // by default can be auto approved // for specific case need to handle
        APPROVED: 'text-lime-500', // by default can be auto approved // for specific case need to handle
        IN_PROGRESS: 'text-blue-500',
        COMPLETED: 'text-green-500',
        REJECTED: 'text-red-500',
        RE_OPEN: 'text-orange-500'
      }

      return statuses[status] || 'text-gray-500'
    },
    getRequestStatusClr(status) {
      const statuses = {
        RAISED: 'text-blue-500',
        HOLD: 'text-yellow-500',
        WITHDRAW: 'text-orange-500',
        APPROVED: 'text-green-500',
        REJECTED: 'text-red-500'
      }

      return statuses[status] || 'text-gray-500'
    },
    activityText(item) {
      const activity = {
        ASSIGN_TO_SM: 'Assign to Service Engineer',
        RETURN_BY_SM: 'Return by Service Engineer',
        GIVEN_TO_SM: 'Given to Service Engineer',
        SOLD_BY_SM: 'Sold by Service Engineer'
      }
      return activity[item.activity] || item.activity
    },
    resetPage() {
      this.options.page = 1
    },
    async getDataFromApi() {
      this.loading = true
      const { page, itemsPerPage } = this.options
      const data = await this.$props.handler({ page, limit: itemsPerPage })
      this.items = data.items
      this.totalItems = data.pagination?.totalCount
      this.loading = false
    },
    getExpandedRowItems(item) {
      const products = item.inStoreAvailability || item.products
      const filteredItems = products.filter((product) => {
        const storeId = item.userInventoryHistory?.storeId || item.customerOrderId?.storeId?._id
        if (storeId && product.storeLocation._id !== storeId) return false
        return true
      })

      if (this.hasSmStatus(item)) {
        const rows = filteredItems.map((row) => {
          const collectedBy = item.userInventoryHistory.collectedBy
          return {
            ...row,
            userInventoryHistory: item.userInventoryHistory,
            collectedBy: collectedBy ? `${collectedBy.firstName} ${collectedBy.lastName}` : '-'
          }
        })

        return rows
      }

      return filteredItems
    },
    updatedByFullName(item) {
      return fullName(item.updatedBy)
    },
    hasSmStatus(item) {
      return ['GIVEN_TO_SM', 'RETURN_BY_SM', 'ASSIGN_TO_SM'].includes(item.activity)
    },
    getExpandedHeaders(item) {
      if (this.hasSmStatus(item)) {
        return [
          { text: 'Collected By', value: 'collectedBy', sortable: false },
          {
            text: 'Quantity Assigned',
            value: 'quantityAssigned',
            sortable: false
          },
          { text: 'Quantity Took', value: 'quantityTook', sortable: false },
          {
            text: 'Quantity Returned',
            value: 'quantityReturned',
            sortable: false
          }
        ]
      }

      return this.$props.expandedHeaders
    },
    getStoreName(item) {
      const products = item.inStoreAvailability || item.products
      const store =
        products.find((p) => {
          return p.storeLocation?._id === item.userInventoryHistory?.storeId
        }) || {}

      return store.storeLocation?.storeName
    },
    getAvailableQuantity(item) {
      const products = item.inStoreAvailability || item.products
      const filteredItems = products.filter((product) => {
        const storeId = item.userInventoryHistory?.storeId || item.customerOrderId?.storeId?._id
        if (storeId && product.storeLocation._id !== storeId) return false
        return true
      })

      const qty = filteredItems.reduce((qty, item) => {
        return qty + item.quantityInStock
      }, 0)

      return qty
    },
    async expandEdit(item) {
      const request = this.findRequestId(item)
      this.$emit('expand-edit', {
        item,
        requestId: request.requestId,
        challanNumber: request.challanNumber
      })
    },
    findRequestId(item) {
      const selectedProduct = this.items.find((inventory) => {
        return inventory.products.some((product) => product._id === item._id)
      })
      return selectedProduct ? selectedProduct : null
    },
    hasFaultyProducts(products) {
      return products && products.some((product) => product.expectedFaultyProduct === true)
    },
    getFaultyProducts(products) {
      return products.filter((product) => product.expectedFaultyProduct === true)
    },
    navToComplaint(id) {
      const routeData = this.$router.resolve({
        path: '/workforce/leadspage/inventory',
        query: { active_tab: 'complaint', complaint_no: id }
      })
      window.open(routeData.href, '_blank')
    },
    extractName(str) {
      return fullName(str)
    },
    exportToExcel() {
      // Get the keys from the first item to determine which columns to include
      // Dynamically build the data array
      const data = this.items.map((item) => {
        const row = {}
        if (item.productName) row.ProductName = item.productName
        if (item.partnerDetails) {
          if (item.partnerDetails.partnerName) row.PartnerName = item.partnerDetails.partnerName
          if (item.partnerDetails.partnerStatus)
            row.PartnerStatus = item.partnerDetails.partnerStatus
        }
        if (item.splDiscount) row.SplDiscount = item.splDiscount
        if (item.discount) row.Discount = item.discount
        if (item.tax) row.Tax = item.tax
        if (item.modelNumber) row.ModelNumber = item.modelNumber
        if (item.updatedAt) row.UpdatedAt = this.$dayjs(item.updatedAt).format('DD/MM/YYYY hh:mm A')
        if (this.getStoreName(item)) row.StoreName = this.getStoreName(item)
        if (this.getAvailableQuantity(item)) row.QuantityInStock = this.getAvailableQuantity(item)
        if (item.quantityInStock) row.AvailableNumber = item.quantityInStock
        if (item.userInventoryHistory) {
          if (item.userInventoryHistory.quantityAssigned)
            row.QuantityAssigned = item.userInventoryHistory.quantityAssigned
          if (item.userInventoryHistory.quantityTook)
            row.QuantityTook = item.userInventoryHistory.quantityTook
        }
        if (item.customerOrderId) {
          if (item.customerOrderId.linkedCustomerId && item.customerOrderId.linkedCustomerId.name)
            row.CustomerName = item.customerOrderId.linkedCustomerId.name
          if (item.customerOrderId.orderSrNo) row.OrderSerialNumber = item.customerOrderId.orderSrNo
        }
        if (item.quantitySold) row.QuantitySold = item.quantitySold
        if (this.activityText(item)) row.Activity = this.activityText(item)
        if (item.productPrice) row.ProductPrice = item.productPrice
        if (item.costPricePerUnit) row.CostPrice = item.costPricePerUnit
        if (item.requestId) {
          if (item.requestId.requestId) row.ReqNumber = item.requestId.requestId
          if (item.requestId.requestStatus) row.ReqStatus = item.requestId.requestStatus
          if (item.requestId.complaint) {
            if (item.requestId.complaint.complainSrNo)
              row.ComplaintNumber = item.requestId.complaint.complainSrNo
            if (item.requestId.complaint.status)
              row.ComplaintStatus = item.requestId.complaint.status
          }
        }
        if (item.availabilityStatus)
          row.AvailabilityStatus =
            item.availabilityStatus === 'IN_STOCK' ? 'In Stock' : 'Out Of Stock'
        if (this.updatedByFullName(item)) row.UpdatedBy = this.updatedByFullName(item)

        return row
      })

      // Determine unique columns from the data
      const columns = [...new Set(data.flatMap((row) => Object.keys(row)))]

      // Convert the data array to a sheet with dynamically generated columns
      const ws = XLSX.utils.json_to_sheet(data, { header: columns })
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

      // Export the workbook to a file
      XLSX.writeFile(wb, 'exported-data.xlsx')
    }
  }
}
</script>

<style scoped>
.expanded-table tr {
  padding-left: 5px;
}

.v-data-table >>> .v-data-table__wrapper tbody tr.v-data-table__expanded__content {
  box-shadow: none;
}
</style>
