<template>
  <div class="p-3">
    <div
      class="justify-end items-center my-3"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
      v-if="!showEmpInventory"
    >
      <div class="justify-end mr-4" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterRequest"
            label="Request/Complaint Number"
            placeholder="Request/Complaint Number"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterChallan"
            label="Challan Number"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
      </div>
    </div>
    <div v-if="showTable" class="overflow-x-auto overflow-y-auto">
      <InventoryTable
        ref="inventoryTable"
        :headers="headers"
        :expandedHeaders="expandedHeaders"
        :handler="fetchInventory"
        @expand-edit="handleExpandEdit"
      >
      </InventoryTable>
    </div>
    <div v-else-if="showEmpInventory">
      <AddEmpInventory @cancel="toggleShowTable" :innerEdit="innerEdit" />
    </div>
  </div>
</template>

<script>
import AddEmpInventory from '~/components/Workforce/InventoryManagement/AddEmpInventory.vue'
import { debounce } from '@/utils/common'
import InventoryTable from './Table.vue'

export default {
  name: 'InventoryIn',
  components: {
    InventoryTable,
    AddEmpInventory
  },
  data() {
    return {
      headers: [
        { text: 'Request No', value: 'reqNumber', sortable: false },
        { text: 'Challan No', value: 'challanNumber', sortable: false },
        { text: 'Request Status', value: 'reqStatus', sortable: false },
        { text: 'Complaint No', value: 'complaintNumber', sortable: false },
        { text: 'Complaint Status', value: 'complaintStatus', sortable: false }
      ],
      expandedHeaders: [
        { text: 'Product Name', value: 'productName', sortable: false },
        { text: 'Store Name', value: 'storeName', sortable: false },
        { text: 'Assigned Qty.', value: 'qtyAssigned', sortable: false },
        { text: 'Used Qty.', value: 'qtyUsed', sortable: false },
        { text: 'Returned Qty.', value: 'qtyReturned', sortable: false },
        { text: 'Assigned To', value: 'assignedBy', sortable: false },
        { text: 'Given By', value: 'givenBy', sortable: false },
        { text: 'Collected By', value: 'collectedBy', sortable: false },
        { text: 'Returned To', value: 'returnedTo', sortable: false },
        { text: 'Delivery Mode', value: 'deliveryMode', sortable: false },
        { text: 'Tracking No.', value: 'tracking', sortable: false },
        { text: 'Status', value: 'expandStatus', sortable: false },
        { text: 'Action', value: 'expandAction', sortable: false }
      ],
      showTable: true,
      innerEdit: null,
      filterRequest: '',
      filterChallan: '',
      showEmpInventory: false
    }
  },
  methods: {
    toggleShowEmpInventory() {
      this.showTable = false
      this.showEmpInventory = true
    },
    toggleShowTable() {
      this.showEmpInventory = false
      this.innerEdit = null
      this.showTable = true
    },
    async fetchInventory({ page = '1', limit = '10' }) {
      try {
        const params = {
          page,
          limit
        }
        if (this.filterRequest) {
          params.search = this.filterRequest
        }
        if (this.filterChallan) {
          params.searchChallan = this.filterChallan
        }
        const response = await this.$axios.get('/workforce/ecom/inventory/user', {
          params: params
        })

        const data = response?.data
        data.items = data?.inventories
        return response?.data
      } catch (error) {
        console.error('An error occurred:', error)
      }
    },
    inventoryDelete(item) {
      this.selectedProduct = item._id
    },
    async handleDeleteInventory() {
      try {
        const response = await this.$axios.delete(`/workforce/ecom/product/${this.selectedProduct}`)
        await this.$refs.inventoryTable.getDataFromApi()
        const successMessage = response?.data?.message || 'Product deleted successfully!'
        this.$toast.success(successMessage)
      } catch (error) {
        const errorMessage = error.response.data.message || 'Order failed.'
        this.$toast.error(errorMessage)
      }

      this.showConfirmationDialog = false
    },
    handleInput: debounce(function () {
      this.$refs.inventoryTable.getDataFromApi()
    }, 1000),
    handleExpandEdit(item) {
      this.innerEdit = item
      this.toggleShowEmpInventory()
    },
    getTabButtons() {
      return [
        {
          key: 'assign-part',
          label: 'Assign Part',
          icon: 'mdi-account-plus',
          color: 'primary',
          class: 'white--text',
          action: 'showAssignPartDialog',
          visible: this.canAdd || this.canEdit
        }
      ]
    }
  }
}
</script>

<style scoped></style>
