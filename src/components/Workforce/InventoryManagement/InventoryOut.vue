<template>
  <div class="p-3">
    <div class="justify-end items-center my-3" :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']">
      <div
        class="justify-end mr-4"
        :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']"
      >
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-text-field
            v-model="filterPartName"
            label="Spare Part/Model Number"
            solo
            flat
            clearable
            prepend-inner-icon="mdi-magnify"
            @input="handleInput"
            outlined
            dense
            hide-details
          ></v-text-field>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
        >
          <v-select
            v-model="filterActivity"
            label="Select Status"
            :items="activityOptions"
            item-text="text"
            item-value="value"
            solo
            flat
            clearable
            @change="handleInput"
            outlined
            dense
            hide-details
          ></v-select>
        </v-col>
      </div>
    </div>
    <div v-if="showTable" class="overflow-x-auto overflow-y-auto">
      <InventoryTable
        ref="inventoryTable"
        :headers="headers"
        :handler="fetchInventoryOut"
        :expandedHeaders="expandedHeaders"
      />
    </div>
    <div v-else-if="showAddInventory">
      <AddInventory @cancel="toggleShowTable" />
    </div>
  </div>
</template>

<script>
import AddInventory from "~/components/Workforce/InventoryManagement/AddInventory.vue";
import InventoryTable from "./Table.vue";
import { debounce } from "@/utils/common";
export default {
  name: "InventoryIn",
  components: {
    AddInventory,
    InventoryTable,
  },
  data() {
    return {
      filterActivity: "",
      activityOptions: [
        { value: "SOLD", text: "Sold" },
        { value: "GIVEN_TO_SM", text: "Given to Service Engineer" },
        { value: "SOLD_BY_SM", text: "Sold by Service Engineer" },
      ],
      headers: [
        { text: "Spare Part", value: "productName", sortable: false },
        { text: "Part/Model No.", value: "modelNumber", sortable: false },
        {
          text: "Brand Name",
          value: "partnerDetails.partnerName",
          sortable: false,
        },
        {
          text: "Order Number",
          value: "orderSerialNumber",
          sortable: false,
        },
        {
          text: "Store Name",
          value: "storeName",
          sortable: false,
        },
        {
          text: "Selling Price(Per Unit)",
          value: "productPrice",
          sortable: false,
        },
        { text: "Discount", value: "discount", sortable: false },
        { text: "Spl. Discount", value: "splDiscount", sortable: false },
        { text: "Available Qty.", value: "quantityInStock", sortable: false },
        { text: "Sold To", value: "customerName", sortable: false },
        { text: "Sold Qty", value: "quantitySold", sortable: false },
        { text: "Sold By", value: "updatedBy", sortable: false },
        { text: "Sold On", value: "updatedAt", sortable: false },
        { text: "Status", value: "activity", sortable: false },
      ],
      expandedHeaders: [
        {
          text: "Store Name",
          value: "storeLocation.storeName",
          sortable: false,
        },
        { text: "Quantity", value: "quantityInStock", sortable: false },
        {
          text: "Store Status",
          value: "storeLocation.storeStatus",
          sortable: false,
        },
        {
          text: "Availability Status",
          value: "availabilityStatus",
          sortable: false,
        },
      ],
      showTable: true,
      filterPartName: "",
      showAddInventory: false,
    };
  },
  computed: {},
  methods: {
    updatedByFullName(item) {
      return item.updatedBy.firstName + " " + item.updatedBy.lastName;
    },
    toggleShowTable() {
      this.showAddInventory = false;
      this.showTable = true;
    },
    toggleshowAddInventory() {
      this.showTable = false;
      this.showAddInventory = true;
    },
    async fetchInventoryOut({ page, limit }) {
      try {
        const params = { page, limit, activity: "SOLD,GIVEN_TO_SM,SOLD_BY_SM" };
        if (this.filterPartName) {
          params.search = this.filterPartName;
        }

        if (this.filterActivity) {
          params.activity = this.filterActivity;
        } else {
          params.activity = "SOLD,GIVEN_TO_SM,SOLD_BY_SM";
        }

        const response = await this.$axios.get(
          `/workforce/ecom/inventory/history`,
          {
            params: params,
          }
        );
        const data = response.data;
        data.items = data.history;
        return response.data;
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    handleInput: debounce(function () {
      this.$refs.inventoryTable.getDataFromApi();
      this.$refs.inventoryTable.resetPage();
    }, 1000),
  },
};
</script>

<style scoped></style>
