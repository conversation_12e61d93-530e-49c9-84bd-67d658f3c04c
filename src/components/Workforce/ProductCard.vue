<template>
  <v-card class="p-2" flat>
    <v-divider></v-divider>
    <v-row class="flex align-center">
      <v-col>
        <v-img
          :src="getProductImage(product)"
          width="100px"
          height="100px"
          class="mt-2 mb-2"
        ></v-img>
      </v-col>
      <v-col>
        <v-card-subtitle>{{ product.productName }}</v-card-subtitle>
      </v-col>
      <v-col>
        <v-card-subtitle>{{ product.modelNumber }}</v-card-subtitle>
      </v-col>
      <v-col>
        <v-card-subtitle class="p-0">{{
          product.availabilityStatus || "IN_STOCK"
        }}</v-card-subtitle>
      </v-col>
      <v-col>
        <v-card-subtitle>
          <v-btn icon @click="decreaseQuantity">
            <v-icon>mdi-minus</v-icon>
          </v-btn>
          {{ quantity }}
          <v-btn icon @click="increaseQuantity">
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </v-card-subtitle>
      </v-col>
      <v-col>
        <v-card-subtitle>
          {{ product.productPrice * quantity }}</v-card-subtitle
        >
      </v-col>
      <v-card-actions>
        <v-btn icon @click="deleteProduct">
          <v-icon color="red">mdi-delete</v-icon>
        </v-btn>
      </v-card-actions>
    </v-row>

    <v-divider></v-divider>
  </v-card>
</template>

<script>
import dummyProductImage from "@/assets/img/img-dummy-product.jpg";
export default {
  props: {
    product: {
      type: Object,
      required: true,
    },
    quantity: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_S3_BASE_URL,
    };
  },
  methods: {
    increaseQuantity() {
      this.$emit("increaseQuantity");
    },
    decreaseQuantity() {
      if (this.quantity > 1) {
        this.$emit("decreaseQuantity");
      }
    },
    deleteProduct() {
      this.$emit("deleteProduct");
    },
    getProductImage(product) {
      if (product.productImages && product.productImages.length) {
        return `${this.baseUrl}${product?.productImages[0]}`;
      } else {
        return dummyProductImage;
      }
    },
  },
};
</script>
