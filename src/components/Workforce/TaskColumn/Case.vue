<template>
  <div
    class="mx-0.5 mb-3 border border-gray-200 rounded-lg px-3 py-1.5 mr-1 bg-white dark-bg-custom"
    :class="{ 'cursor-pointer': $props.caseNo }"
  >
    <div v-if="item">
      <span class="inline-block ml-auto text-xs text-gray-500">{{
        $dayjs(date).format("DD/MM/YYYY hh:mm A")
      }}</span>
      <div class="flex items-start justify-between">
        <div class="flex flex-col mb-2">
          <div>
            <span
              :class="{ 'whitespace-nowrap': !viewMore }"
              class="text-gray-600 leading-5 w-full block font-semibold text-md text-ellipsis overflow-hidden"
              >{{ caseNo }}</span
            >
          </div>
          <span class="text-xs text-sky-900"
            >{{ bkt ? bkt : "--" }} - {{ pos }}</span
          >
          <span class="text-xs text-sky-900">{{
            item ? item.case.customer?.customerFullName : ""
          }}</span>
          <span class="text-xs text-sky-900">{{
            item.case.customer?.customerEmail ? item.case.customer?.customerEmail : ""
          }}</span>
          <div
            class="border border-red-400 rounded-lg p-1 mt-2"
            
          >
            <span class="text-xs text-sky-900">POS: {{ pos }}</span>
            <br />
            <span class="text-xs text-sky-900"
              >Case Start Date:
              {{ $dayjs(caseStartDate).format("DD/MM/YYYY hh:mm A") }}</span
            >
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between">
        <div
          :style="{
            backgroundColor: statusColor.bgColor,
            color: statusColor.textColor,
          }"
          class="text-gray-100 text-xs font-semibold px-2 py-0.5 rounded-xl"
        >
          {{ status }}
        </div>
        <div class="flex text-sky-900 items-center justify-end">
          <span class="text-sm">
            {{ userFullName(assignedTo) }}
          </span>
          <div class="ml-2">
            <v-icon size="30">mdi-account-circle</v-icon>
          </div>
        </div>
      </div>
    </div>
    <v-skeleton-loader
      v-else
      class="mx-auto"
      max-width="350"
      type="image"
    ></v-skeleton-loader>
  </div>
</template>

<script>
import { convertToTitleCase, fullName } from "@/utils/common";

const maxTitleLen = 40;

export default {
  name: "CaseComponent",
  data() {
    return {
      viewMore: false,
    };
  },
  props: {
    item: Object,
    date: {
      type: String,
    },
    bkt: String,
    pos: Number,
    title: { type: String },
    description: {
      type: String,
    },
    createdBy: {
      type: String,
    },
    status: {
      type: String,
      required: true,
    },
    caseStartDate: { type: String },
    caseNo: { type: String },
    assignedTo: { type: Object },
  },
  computed: {
    titleCasedStatus() {
      return convertToTitleCase(this.status);
    },
    statusColor() {
      const statusColors = {
        OPEN: {
          textColor: "#FFCC00",
          bgColor: "rgba(255, 204, 0, 0.12)",
        },
        CLOSED: {
          textColor: "#66CC66",
          bgColor: "rgb(102, 204, 102, 0.12)",
        },
        COMPLETED: {
          textColor: "#66CC66",
          bgColor: "rgb(102, 204, 102, 0.12)",
        },
        PENDING: {
          textColor: "#FFCC00",
          bgColor: "rgba(255, 204, 0, 0.12)",
        },
        REJECTED: {
          textColor: "#FF3366",
          bgColor: "rgb(255, 51, 102, 0.12)",
        },
        IN_PROGRESS: {
          textColor: "#3399FF",
          bgColor: "rgb(51, 153, 255, 0.12)",
        },
      };

      return statusColors[this.status] || {};
    },
    canViewMore() {
      return this.title.length > maxTitleLen;
    },
  },
  methods: {
    userFullName(emp) {
      return fullName(emp);
    },
  },
};
</script>

<style></style>
