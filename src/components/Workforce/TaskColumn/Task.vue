<template>
  <div
    class="mx-0.5 mb-3 border border-gray-200 rounded-lg px-3 py-1.5 mr-1 bg-white dark-bg-custom"
    :class="{ 'cursor-pointer': $props.complainSrNo }"
    @click="navToComplaint"
  >
    <div v-if="item">
      <span class="inline-block ml-auto text-xs text-gray-500 dark-text-color">{{ $dayjs(date).format("hh:mm A") }}</span>
      <div class="flex items-start justify-between">
        <div class="flex flex-col mb-2">
          <div>
            <span
              :class="{ 'whitespace-nowrap': !viewMore }"
              class="text-gray-600 leading-5 w-full block font-semibold text-md text-ellipsis overflow-hidden"
              >{{ title }}</span
            >
            <span
              v-if="canViewMore"
              @click="viewMore = !viewMore"
              class="text-xs cursor-pointer text-blue-400"
              >{{ viewMore ? "View less" : "View more" }}</span
            >
          </div>
          <span class="text-xs text-sky-900">{{
            description ? description : "--"
          }}</span>
          <span class="text-xs text-sky-900">{{
            item ? item.leadDetails?.company?.name : ""
          }}</span>
          <div
            class="border border-red-400 rounded-lg p-1 mt-2"
            v-if="complaintStatus"
          >
            <span class="text-xs text-sky-900"
              >Complaint Status: {{ complaintStatus }}</span
            >
            <br />
            <span class="text-xs text-sky-900" v-if="complaintComment">{{
              complaintComment
            }}</span>
          </div>
          <div
            class="border border-blue-400 rounded-lg p-1 mt-2"
            v-if="requestStatus"
          >
            <span class="text-xs text-sky-900"
              >Request Status: {{ requestStatus }}</span
            >
            <br />
            <span class="text-xs text-sky-900" v-if="requestComment">{{
              requestComment
            }}</span>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between">
        <div
          :style="{
            backgroundColor: statusColor.bgColor,
            color: statusColor.textColor,
          }"
          class="text-gray-100 text-xs font-semibold px-2 py-0.5 rounded-xl"
        >
          {{ titleCasedStatus }}
        </div>
        <div class="flex text-sky-900 items-center justify-end">
          <span class="text-sm">
            {{ meetingWith }}
          </span>
          <div class="ml-2" v-if="meetingWith.trim() !== ''">
            <v-icon size="30">mdi-account-circle</v-icon>
          </div>
        </div>
      </div>
    </div>
    <v-skeleton-loader
      v-else
      class="mx-auto"
      max-width="350"
      type="image"
    ></v-skeleton-loader>
  </div>
</template>

<script>
import { convertToTitleCase } from "@/utils/common";

const maxTitleLen = 40;

export default {
  name: "TaskComponent",
  data() {
    return {
      viewMore: false,
    };
  },
  props: {
    item: Object,
    date: {
      type: String,
    },
    title: { type: String },
    description: {
      type: String,
    },
    meetingWith: {
      type: String,
    },
    status: {
      type: String,
      required: true,
    },
    complaintComment: { type: String },
    complaintStatus: { type: String },
    requestComment: { type: String },
    requestStatus: { type: String },
    complainSrNo: { type: String },
  },
  computed: {
    titleCasedStatus() {
      return convertToTitleCase(this.status);
    },
    statusColor() {
      const statusColors = {
        PENDING: {
          textColor: "#FFCC00",
          bgColor: "rgba(255, 204, 0, 0.12)",
        },
        MISSED: {
          textColor: "#FF0000",
          bgColor: "rgb(255, 0, 0, 0.12)",
        },
        IN_PROGRESS: {
          textColor: "#3399FF",
          bgColor: "rgb(51, 153, 255, 0.12)",
        },
        ENDED: {
          textColor: "#66CC66",
          bgColor: "rgb(102, 204, 102, 0.12)",
        },
        DEFERRED: {
          textColor: "#FF9900",
          bgColor: "rgb(255, 153, 0, 0.12)",
        },
        COMPLETED: {
          textColor: "#00CC00",
          bgColor: "rgb(0, 204, 0, 0.12)",
        },
        CANCELLED: {
          textColor: "#999999",
          bgColor: "rgb(153, 153, 153, 0.12)",
        },
        REJECTED: {
          textColor: "#FF3366",
          bgColor: "rgb(255, 51, 102, 0.12)",
        },
        APPROVED: {
          textColor: "#33CC33",
          bgColor: "rgb(51, 204, 51, 0.12)",
        },
      };

      return statusColors[this.status];
    },
    canViewMore() {
      return this.title.length > maxTitleLen;
    },
  },
  methods: {
    navToComplaint() {
      if (this.$props.complainSrNo) {
        const useCase = this.$props.item?.useCase;
        const routeData = this.$router.resolve({
          path: `/workforce/leadspage/${useCase}`,
          query: {
            active_tab: "complaint",
            complaint_no: this.$props.complainSrNo,
          },
        });
        window.open(routeData.href, "_blank");
      }
    },
  },
};
</script>
<style></style>
