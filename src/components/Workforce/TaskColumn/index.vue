<template>
  <div>
    <no-data title="Nothing to Display" subTitle="" v-if="!categorizedItems.length"> </no-data>
    <ssr-carousel
      v-if="categorizedItems.length"
      :class="{ 'justify-start': categorizedItems.length < 2 }"
      :feather="false"
      :slides-per-page="2"
      show-arrows
    >
      <div
        class="px-2 bg-gray-100 dark-bg-custom shadow-sm rounded-lg pb-2"
        v-for="(category, index) in categorizedItems"
        :key="index"
      >
        <Title
          v-if="category.type === 'task'"
          :isViewAllVisible="category.showMinimizeOpt"
          :status="category.status"
          :number="category.items"
          :minimized="category.isMinimized"
          :toggleMinimized="toggleMinimized(category.type)"
          :dynamicTitle="taskLable?.tabs?.tasks"
        />
        <Title
          v-else
          :isViewAllVisible="category.showMinimizeOpt"
          :status="category.status"
          :number="category.items"
          :minimized="category.isMinimized"
          :toggleMinimized="toggleMinimized(category.type)"
          :dynamicTitle="taskLable?.tabs?.cases"
        />
        <div :style="{ height: itemsContainerHeight }" class="overflow-y-scroll colScroll">
          <template v-if="category.type === 'task'">
            <Task
              v-for="item in category.items"
              :key="item.id"
              :date="item.taskStartDateTime"
              :title="item.taskTitle"
              :description="item.taskDescription"
              :complainSrNo="item.complaintDetails?.complainSrNo"
              :complaintComment="item.linkedRequestId?.comments.at(-1)?.comment"
              :complaintStatus="item.linkedRequestId?.requestStatus"
              :requestComment="item.complaintDetails?.linkedRequestId?.comments.at(-1)?.comment"
              :requestStatus="item.complaintDetails?.linkedRequestId?.requestStatus"
              :meetingWith="`${item?.assignedTo?.firstName || ''} ${
                item?.assignedTo?.lastName || ''
              }`"
              :status="item.taskStatus"
              :item="item"
            />
          </template>
          <template v-if="category.type === 'case'">
            <Case
              v-for="item in category.items"
              :key="item.id"
              :item="item"
              :date="item.updatedAt"
              :caseNo="item.case.caseNo"
              :typeOfLoan="item.case.typeOfLoan"
              :bkt="item.case.bkt"
              :pos="item.case.pos"
              :emiAmount="item.case.emiAmount"
              :tenure="item.case.tenure"
              :customer="item.case.customer"
              :status="item.taskStatus"
              :caseStartDate="item.case.caseStartDate"
              :assignedTo="item.assignedTo"
            />
          </template>
        </div>
      </div>
    </ssr-carousel>
  </div>
</template>

<script>
import Task from './Task.vue'
import Case from './Case.vue'
import Title from './Title.vue'
import NoData from '@/components/NoData.vue'
import SsrCarousel from 'vue-ssr-carousel'
import ssrCarouselCss from 'vue-ssr-carousel/index.css'

export default {
  name: 'ItemColumnComponent',
  components: {
    Task,
    Case,
    Title,
    NoData,
    'ssr-carousel': SsrCarousel
  },
  props: {
    items: Array,
    itemType: String
  },
  data() {
    return {
      orgLang: this.$store.state.orgLanguage,
      enabledUseCase: this.$store.state.useCase,
      benched: 0,
      minimizedCategories: {}
    }
  },
  computed: {
    itemsContainerHeight() {
      return window.innerHeight - 285 + 'px'
    },
    categorizedItems() {
      const categories = {}

      this.items.forEach((item) => {
        const status = item.taskStatus
        const existingItems = categories[status] || []
        categories[status] = [...existingItems, item]
      })

      const categorizedItems = Object.keys(categories).map((key) => {
        const isMinimized =
          this.minimizedCategories[key] === undefined ? true : this.minimizedCategories[key]
        const items = isMinimized ? categories[key].slice(0, this.maxItemsPerCol) : categories[key]
        return {
          items,
          isMinimized,
          status: key,
          type: this.itemType,
          showMinimizeOpt: categories[key].length > this.maxItemsPerCol
        }
      })

      const sorted = categorizedItems.sort((a, b) => {
        const statusA = a.status.toUpperCase()
        const statusB = b.status.toUpperCase()

        if (statusA < statusB) {
          return -1
        }
        if (statusA > statusB) {
          return 1
        }
        return 0
      })

      return sorted
    },
    taskLable() {
      let currentUsecase
      if (!this.selectedUseCase) {
        currentUsecase = this.enabledUseCase[0]
      } else {
        currentUsecase = this.selectedUseCase
      }
      const orgConfig = this.orgLang.data || {}

      const taskModules = orgConfig.taskModules || []

      const currentConfig = taskModules.find((m) => m.useCaseType === currentUsecase)

      return currentConfig
    }
  },
  methods: {
    toggleMinimized() {
      return (status) => {
        const categoryStatus = this.minimizedCategories[status]
        const updatedCategories = {
          ...this.minimizedCategories,
          [status]: categoryStatus === undefined ? false : !categoryStatus
        }
        this.minimizedCategories = updatedCategories
      }
    }
  }
}
</script>

<style scoped>
.colScroll::-webkit-scrollbar {
  display: none;
}
</style>
