<template>
  <div class="flex items-center mb-3 pt-2 justify-between">
    <p class="text-sky-900 text-md font-semibold ml-1 mb-0">
      {{ title }} {{ dynamicTitle }} ({{ number.length }})
    </p>
  </div>
</template>

<script>
import { convertToTitleCase } from "@/utils/common";

export default {
  name: "TaskColumnTitle",
  data() {
    return {
      orgLang: this.$store.state.orgLanguage,
    };
  },
  props: {
    number: Array,
    status: String,
    minimized: Boolean,
    isViewAllVisible: Boolean,
    toggleMinimized: Function,
    dynamicTitle: String,
  },
  computed: {
    title() {
      return convertToTitleCase(this.status);
    },
    taskLable() {
      return {
        task: this.orgLang?.data?.allocateTaskModule.taskTable.columns
          ?.taskTitle,
      };
    },
  },
};
</script>
