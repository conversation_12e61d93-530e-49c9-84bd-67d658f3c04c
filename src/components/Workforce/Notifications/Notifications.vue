<template>
  <div v-if="notificationStats.length">
    <div
      class="items-center justify-start pr-2"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex mb-4' : 'flex-col w-full']"
    >
      <v-col
        v-if="filteredNotificationCategory.length > 1"
        :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
        :class="[$vuetify.breakpoint.lgAndUp ? 'py-0 pr-0' : 'ml-auto  py-2']"
      >
        <v-autocomplete
          v-model="selectedCategory"
          label="Select Category"
          placeholder="Select Category"
          :items="filteredNotificationCategory"
          item-text="category"
          item-value="_id"
          hide-details
          outlined
          dense
          clearable
          @change="handleCategory"
        >
        </v-autocomplete>
      </v-col>
      <v-col
        :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
        :class="[$vuetify.breakpoint.lgAndUp ? 'py-0 pr-0' : 'ml-auto  py-2']"
      >
        <v-autocomplete
          v-model="selectRead"
          :items="notificationRead"
          hide-details
          outlined
          dense
          clearable
          label="Select Read"
          placeholder="Select Read"
          item-text="label"
          item-value="value"
          @change="handleCategory"
        ></v-autocomplete>
      </v-col>
    </div>
    <v-app>
      <div class="mt-2 px-2">
        <v-row>
          <v-col cols="3" class="legend-div">
            <div>
              <v-list class="legendItemsStyle">
                <v-list-item
                  v-for="item in filteredLegendItems"
                  :key="item.color"
                  :class="{
                    'hover:bg-gray-300 cursor-pointer': true,
                    'selected-bg-color': selectedColor === item.label,
                    'mb-2': !$vuetify.breakpoint.lgAndUp
                  }"
                  @click="handleSelectedColor(item)"
                >
                  <v-list-item-content>
                    <v-list-item-title class="text-base">
                      <v-icon :color="item.color" class="mr-2">mdi-circle</v-icon>
                      {{ titleCase(item.label) }} ({{ getNotificationCount(item.label) }})
                      <v-icon :color="'#33CC33'" v-if="selectedColor === item.label"
                        >mdi-check</v-icon
                      >
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </div>
          </v-col>
          <v-col>
            <v-list>
              <v-list-item
                v-for="(notification, index) in filteredNotifications"
                class="px-0"
                :key="index"
              >
                <v-alert
                  :type="notification.type"
                  class="w-full p-2 text-sm"
                  :color="categoryColor(notification)?.bgColor"
                  text
                  outlined
                >
                  <v-row>
                    <v-col cols="11" class="text-base">
                      {{ notification.message }}
                    </v-col>
                    <v-col cols="1" class="cursor-pointer" @click="toggleReadStatus(index)">
                      <v-icon
                        class="vIconClass"
                        :color="categoryColor(notification)?.textColor"
                        v-if="notification.read"
                        >mdi-check-all</v-icon
                      >
                      <v-tooltip top v-else>
                        <template v-slot:activator="{ on }">
                          <v-icon v-on="on" :color="categoryColor(notification)?.bgColor"
                            >mdi-check</v-icon
                          >
                        </template>
                        <span>Mark as read</span>
                      </v-tooltip>
                    </v-col>
                  </v-row>
                  <p class="mb-0 text-sm">
                    (
                    {{ $dayjs(notification.date).format('DD/MM/YYYY / hh:mm A') }}
                    )
                  </p>
                </v-alert>
              </v-list-item>
            </v-list>
          </v-col>
        </v-row>
      </div>
      <v-pagination
        v-model="currentPage"
        :length="totalPages"
        :total-visible="5"
        v-if="totalPages > 1"
        :color="$vuetify.theme.currentTheme.primary"
      ></v-pagination>
    </v-app>
  </div>
  <NoData
    v-else
    title="Nothing to display"
    subTitle="There is no notifications for this organization"
  />
</template>

<script>
import { notificationColor } from '~/utils/workforce/statusColors'
import { convertToTitleCase, isAnyFeatureAllowed, isModuleVisible } from '@/utils/common'
import NoData from '@/components/NoData.vue'
export default {
  name: 'NotificationComponent',
  components: {
    NoData
  },
  data() {
    return {
      notifications: [],
      totalPages: null,
      currentPage: 1,
      notificationCategoryMap: {
        INVENTORY: 'STOCK_MANAGEMENT',
        AMC: 'AMC',
        COMPLAINT: 'SELLS',
        LEAVE: 'LEAVE_MANAGEMENT'
      },
      selectedCategory: null,

      notificationRead: [
        { label: 'Read', value: true },
        { label: 'Not Read', value: false }
      ],
      selectRead: false,
      selectedColor: null,
      notificationStats: []
    }
  },
  watch: {
    currentPage: {
      handler() {
        this.fetchNotifications()
      },
      immediate: true
    }
  },
  computed: {
    legendItemsFromStatusColor() {
      return Object.keys(notificationColor).map((key) => ({
        label: key,
        color: notificationColor[key].bgColor
      }))
    },
    filteredNotifications() {
      return this.notifications
    },
    filteredLegendItems() {
      return this.legendItemsFromStatusColor.filter(
        (item) => this.getNotificationCount(item.label) > 0
      )
    },
    filteredNotificationCategory() {
      return Object.entries(this.notificationCategoryMap)
        .filter(([_, moduleName]) => isModuleVisible(moduleName) || isAnyFeatureAllowed(moduleName))
        .map(([category]) => category)
    }
  },
  methods: {
    titleCase(str) {
      return convertToTitleCase(str)
    },
    async toggleReadStatus(index) {
      try {
        const notification = this.filteredNotifications[index]
        if (notification.read) {
          return
        }

        const notificationId = notification._id
        const payload = {
          isRead: true
        }
        await this.$axios.put(`/workforce/notification/${notificationId}`, payload)
        await this.fetchNotifications()
      } catch (error) {
        console.log(error)
      }
    },
    async fetchNotifications() {
      try {
        const params = {
          page: this.currentPage,
          limit: 10,
          category: this.selectedCategory,
          isRead: this.selectRead,
          notificationGenric: this.selectedColor
        }
        const response = await this.$axios.get('/workforce/notifications', {
          params
        })
        const data = response.data?.notifications
        this.totalPages = response.data?.pagination.totalPages
        const notificationData = data.map((item) => {
          let type = 'info'

          if (item.notificationType === 'COMPLAINT_APPROVAL_REQUEST') {
            type = 'warning'
          } else if (item.notificationType === 'INVENTORY_REQUEST') {
            type = 'success'
          }

          return {
            type,
            message: item.notificationMessage,
            read: item.isRead,
            date: item.updatedAt,
            _id: item._id,
            category: item.category,
            notificationGenric: item.notificationGenric
          }
        })
        this.notifications = notificationData
        this.notificationStats = response.data?.stats
      } catch (error) {
        console.log(error)
      }
    },
    getNotificationCount(label) {
      const stat = this.notificationStats.find(
        (item) => item.notificationGenric === label.toUpperCase()
      )
      return stat ? stat.count : 0
    },
    categoryColor(noti) {
      if (noti.read) {
        return {
          bgColor: '#888',
          textColor: '#555'
        }
      }
      const colorInfo = notificationColor[noti.notificationGenric]
      return colorInfo
    },
    handleCategory() {
      this.fetchNotifications()
    },
    handleSelectedColor(item) {
      if (this.selectedColor === item.label) {
        this.selectedColor = null
      } else {
        this.selectedColor = item.label
      }
      this.fetchNotifications()
    }
  }
}
</script>

<style scoped>
.v-list-item {
  transition: background-color 0.3s ease;
}

.legend-div {
  max-width: 270px;
  min-width: 180px;
}

.hover-highlight {
  background-color: rgba(0, 0, 0, 0.1);
}

.selected-bg-color {
  background-color: rgba(0, 0, 0, 0.1);
}
</style>
