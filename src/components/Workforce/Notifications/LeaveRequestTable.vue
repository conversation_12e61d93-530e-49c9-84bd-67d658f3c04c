<template>
  <div>
    <v-data-table
      :headers="headers"
      :items="leaveRequests"
      :loading="loading"
      :options.sync="options"
      :server-items-length="totalLeaveRequests"
      class="pl-7 pr-7 pt-4 rounded-lg"
    >
      <template v-slot:item.requestedBy="{ item }">
        <td class="px-4 py-4 font-semibold">
          {{ userFullName(item.userId) }}
        </td>
      </template>
      <template v-slot:item.leaveType="{ item }">
        <td class="px-4 py-4 font-semibold">
          {{ convertCase(item.typeOfLeaveApplied) }}
        </td>
      </template>
      <template v-slot:item.leaveDates="{ item }">
        <td class="px-4 py-4 font-semibold">
          <div class="flex flex-wrap gap-2">
            <v-chip
              v-for="(date, index) in formatLeave(item.leaveDates)"
              :key="index"
              small
              label
            >
              {{ date }}
            </v-chip>
          </div>
        </td>
      </template>
      <template v-slot:item.duration="{ item }">
        <td class="px-4 py-4 font-semibold">
          {{ item.duration }}
        </td>
      </template>
      <template v-slot:item.comment="{ item }">
        <td class="px-4 font-semibold">
          <div class="flex items-center gap-2">
            <p class="ellipsisClass w-32 mb-0">
              {{ item.comments[item.comments.length - 1]?.comment }}
            </p>
            <v-tooltip top>
              <template v-slot:activator="{ on: tooltipOn }">
                <v-icon
                  v-on="{ ...tooltipOn, ...on }"
                  @click.stop="toggleMenu(item._id, item.comments)"
                  v-if="item.comments.length"
                  style="font-size: 20px"
                  color="#fb8c00"
                >
                  mdi-information
                </v-icon>
              </template>
              <span>See Comments</span>
            </v-tooltip>
            <CommentsPopOver
              v-if="menuState[item._id]"
              :comments="selectedComments[item._id]"
              :menuState="menuState[item._id]"
              @close="closeMenu(item._id)"
            ></CommentsPopOver>
          </div>
        </td>
      </template>
      <template v-slot:item.raisedBy="{ item }">
        <td class="px-4 py-4 font-semibold">
          {{ userFullName(item.requestRaisedByUser) }}
        </td>
      </template>
      <template v-slot:item.raisedBy="{ item }">
        <td class="px-4 py-4 font-semibold">
          {{ userFullName(item.requestRaisedByUser) }}
        </td>
      </template>
      <template v-slot:item.approvalFrom="{ item }">
        <td class="px-4 py-4 font-semibold">
          {{ userFullName(item.requestApprovalsfrom[0]?.approvalFrom) }}
        </td>
      </template>
      <template v-slot:item.status="{ item }">
        <OrderStatuses
          :orderStatus="item.leaveStatus"
          :statuses="getRequestStatuses(item)"
          @statusChange="(status) => updateStatus(status, item._id)"
        />
      </template>
    </v-data-table>
  </div>
</template>

<script>
import { fullName, convertToTitleCase } from "@/utils/common";
import CommentsPopOver from "../CommentsPopOver.vue";
import OrderStatuses from "@/components/Workforce/InventoryManagement/OrderStatuses.vue";
export default {
  props: {
    leaveRequests: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    totalLeaveRequests: Number,
  },
  components: {
    OrderStatuses,
    CommentsPopOver,
  },
  data() {
    return {

      headers: [
        {
          text: "Requested By",
          value: "requestedBy",
          width: "150px",
          sortable: false,
        },
        {
          text: "Leave Type",
          value: "leaveType",
          width: "120px",
          sortable: false,
        },
        {
          text: "Leave Dates",
          value: "leaveDates",
          width: "200px",
          sortable: false,
        },
        {
          text: "Duration",
          value: "duration",
          width: "100px",
          sortable: false,
        },
        { text: "Comment", value: "comment", width: "200px", sortable: false },
        {
          text: "Raised By",
          value: "raisedBy",
          width: "150px",
          sortable: false,
        },
        {
          text: "Approval From",
          value: "approvalFrom",
          width: "150px",
          sortable: false,
        },
        { text: "Status", value: "status", width: "100px", sortable: false },
      ],
      menuState: {},
      selectedComments: {},
      options: { itemsPerPage: 5, page: 1 },
    };
  },
  watch: {
    options: {
      handler() {
        this.$emit("refetchPage", this.options);
      },
      deep: true,
    },
  },
  methods: {
    userFullName(item) {
      return fullName(item);
    },
    convertCase(str) {
      return convertToTitleCase(str);
    },
    formatLeave(dates) {
      return dates.map((date) => this.$dayjs(date).format("YYYY-MM-DD"));
    },
    toggleMenu(itemId, comments) {
      Object.keys(this.menuState).forEach((key) => {
        if (key !== itemId) {
          this.$set(this.menuState, key, false);
        }
      });
      this.$set(this.menuState, itemId, !this.menuState[itemId]);

      if (this.menuState[itemId]) {
        this.$set(this.selectedComments, itemId, comments);
      } else {
        this.$delete(this.selectedComments, itemId);
      }
    },
    closeMenu(id) {
      this.$set(this.menuState, id, false);
    },
    getRequestStatuses(item) {
      const loggedInUser = JSON.parse(localStorage.getItem("user"));
      const userId = loggedInUser._id;
      if (item.raisedBy?._id === userId) {
        return [{ label: "Withdraw", value: "WITHDRAW" }];
      }

      const statuses = [
        { label: "Pending", value: "PENDING" },
        { label: "Approve", value: "APPROVED" },
        { label: "Reject", value: "REJECTED" },
        { label: "Withdraw", value: "WITHDRAW" },
      ];

      return statuses;
    },
    async updateStatus(status, itemId) {
      try {
        await this.$axios.put(
          `/workforce/emp/leave/request/${itemId}`,
          {
            leaveStatus: status,
          },
        );
        this.$emit("statusUpdated", { itemId, status });
      } catch (error) {
        console.error("Error updating status:", error);
      }
    },
  },
};
</script>
