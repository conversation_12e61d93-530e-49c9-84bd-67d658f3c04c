<template>
  <div class="p-3">
    <div
      class="justify-end items-center my-3"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
    >
      <div>
        <div
          class="justify-end mr-4"
          :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']"
        >
          <v-col
            :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
            :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
            class="fixed-width-col"
          >
            <v-text-field
              v-model="filterComplaint"
              label="Complaint/Request No."
              v-show="selectedRequestType !== 'LEAVE_REQUEST'"
              solo
              flat
              clearable
              @input="handleInput"
              outlined
              dense
              hide-details
              class="fixed-width-input"
            ></v-text-field>
          </v-col>
          <v-col
            :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
            :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
            class="fixed-width-col"
          >
            <v-select
              v-model="selectedRequestType"
              label="Select Request Type"
              :items="requestTypeOptions"
              item-value="value"
              item-text="label"
              solo
              flat
              clearable
              @change="handleInput"
              outlined
              dense
              hide-details
              class="fixed-width-input"
            ></v-select>
          </v-col>
          <v-col
            :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
            :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
            class="fixed-width-col"
          >
            <v-select
              v-model="selectedStatus"
              label="Select Status"
              :items="requestStatusOptions"
              item-value="value"
              item-text="label"
              solo
              flat
              clearable
              @change="handleInput"
              outlined
              dense
              hide-details
              class="fixed-width-input"
            ></v-select>
          </v-col>
        </div>
      </div>
      <v-icon
        class=""
        :loading="loadingRefresh"
        :disabled="loadingRefresh"
        @click="handleInput"
        :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-3']"
      >
        mdi-refresh
      </v-icon>
    </div>
    <div class="overflow-x-auto overflow-y-auto">
      <v-data-table
        v-if="selectedRequestType !== 'LEAVE_REQUEST'"
        :headers="headers"
        :items="filteredRequests"
        class="pl-7 pr-7 pt-4 rounded-lg"
        :options.sync="options"
        :server-items-length="totalItems"
        :loading="loading"
        :single-expand="singleExpand"
        :expanded.sync="expanded"
        show-expand
        item-key="_id"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
      >
        <template #item.data-table-expand="{ item, expand, isExpanded }">
          <td
            v-if="item.requestType === 'INVENTORY_REQUEST'"
            class="text-start"
          >
            <v-btn
              icon
              @click="expand(!isExpanded)"
              class="v-data-table__expand-icon"
              :class="{ 'v-data-table__expand-icon--active': isExpanded }"
            >
              <v-icon>mdi-chevron-down</v-icon>
            </v-btn>
          </td>
        </template>
        <template v-slot:item.requestedBy="{ item }">
          <td class="px-4 py-4 font-semibold">
            {{ item.raisedBy?.firstName }} {{ item.raisedBy?.lastName }}
            <br />
            {{ $dayjs(item.raisedOn).format("DD/MM/YYYY") }}
          </td>
        </template>
        <template v-slot:item.requestType="{ item }">
          <td class="px-4 py-4 font-semibold">
            {{ item.requestType.split("_").join(" ") }}
          </td>
        </template>
        <template v-slot:item.requestedId="{ item }">
          <td class="px-4 py-4 font-semibold">
            {{ item.requestId || "-" }}
          </td>
        </template>
        <template v-slot:item.complaintNumber="{ item }">
          <td
            class="px-4 py-4 font-semibold cursor-pointer hover:text-blue-600"
            @click="() => navToComplaint(item.complaint?.complainSrNo)"
          >
            <span>
              {{ item.complaint?.complainSrNo || "-" }}
            </span>
          </td>
        </template>
        <template v-slot:item.comment="{ item }">
          <td class="px-4 font-semibold">
            <div class="flex items-center gap-2">
              <p class="ellipsisClass w-32 mb-0">
                {{ item.comments[item.comments.length - 1]?.comment }}
              </p>
              <v-tooltip top>
                <template v-slot:activator="{ on: tooltipOn }">
                  <v-icon
                    v-on="{ ...tooltipOn }"
                    @click.stop="toggleMenu(item._id, item.comments)"
                    v-if="item.comments.length"
                    style="font-size: 20px"
                    color="#fb8c00"
                  >
                    mdi-information
                  </v-icon>
                </template>
                <span>See Comments</span>
              </v-tooltip>

              <CommentsPopOver
                v-if="menuState[item._id]"
                :comments="selectedComments[item._id]"
                :menuState="menuState[item._id]"
                @close="closeMenu(item._id)"
              ></CommentsPopOver>
            </div>
          </td>
        </template>
        <template v-slot:item.approvedBy="{ item }">
          <td class="px-4 font-semibold">
            {{
              item.approvals[item.approvals.length - 1]?.approvedBy?.firstName
            }}
            {{
              item.approvals[item.approvals.length - 1]?.approvedBy?.lastName ||
              "-"
            }}
            <br />
            {{
              item.approvals && item.approvals.length > 0
                ? $dayjs(item.approvals[0]?.approvedOn).format("DD/MM/YYYY")
                : ""
            }}
          </td>
        </template>
        <template v-slot:item.rejectedBy="{ item }">
          <td class="px-4 font-semibold">
            {{ item.rejectedBy?.firstName }}
            {{ item.rejectedBy?.lastName || "-" }}
            <br />
            {{
              item.rejectedBy
                ? $dayjs(item.rejectedOn).format("DD/MM/YYYY")
                : ""
            }}
          </td>
        </template>

        <template v-slot:item.status="{ item }">
          <td class="w-48 td_font">
            <v-menu offset-y :disabled="isStatusDisabled(item.requestStatus)">
              <template v-slot:activator="{ on }">
                <v-btn
                  v-on="on"
                  :style="getStatusColor(item.requestStatus)"
                  class="text-capitalize"
                  >{{ item.requestStatus }}</v-btn
                >
              </template>
              <v-list
                style="
                  max-height: 200px;
                  overflow-y: auto;
                  width: 250px;
                  border-radius: 20px;
                  text-align: center;
                "
              >
                <v-list-item
                  v-for="status in getRequestStatuses(item)"
                  :key="status.value"
                  @click="addComment(item, status.value)"
                >
                  <v-list-item-content :style="getStatusColor(status.value)">
                    <span style="white-space: nowrap; font-size: 14px">{{
                      status.label
                    }}</span>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-menu>
          </td>
        </template>

        <template v-slot:expanded-item="{ headers, item }">
          <td :colspan="headers.length">
            <v-data-table
              v-if="item.products && item.products.length > 0"
              class="expanded-table bg-gray-100 dark-bg-default p-7"
              :headers="getExpandedHeaders(item)"
              :items="item.products"
              hide-default-footer
            >
              <template v-slot:item.productName="{ item }">
                <td class="px-4">{{ item.product?.productName }}</td>
              </template>
              <template v-slot:item.modelNumber="{ item }">
                <td class="px-4">{{ item.product?.modelNumber }}</td>
              </template>
              <template v-slot:item.storeName="{ item }">
                <td class="px-4">{{ item.store?.storeName }}</td>
              </template>
              <template v-slot:item.quantityRequested="{ item }">
                <td class="px-4">{{ item.quantityRequested }}</td>
              </template>
              <template v-slot:item.productPrice="{ item }">
                <td class="px-4">{{ item.product?.productPrice }}</td>
              </template>
            </v-data-table>
            <v-container v-else>
              <v-row class="flex justify-center my-2">
                <v-btn
                  @click="raiseRequest(item)"
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text"
                  v-if="addPartButton(item)"
                  >Add Parts</v-btn
                >
                <p v-else>
                  You can not add parts for {{ item.requestStatus }} request.
                </p>
              </v-row>
            </v-container>
          </td>
        </template>
      </v-data-table>
      <LeaveRequestTable
        v-else
        :leaveRequests="leaveRequests"
        :totalLeaveRequests="totalLeaveRequests"
        :loading="loading"
        @statusUpdated="handleInput"
        @refetchPage="refetchPage"
      />
      <v-dialog v-model="openCommentDialog" max-width="500" persistent>
        <v-card>
          <v-card-title class="text-h5 flex justify-between items-center">
            Add Comment <v-icon @click="handleChipCancel" class="cursor-pointer">mdi-close</v-icon>
          </v-card-title>
          <div class="px-4 py-2">
            <v-select
              dense
              outlined
              v-model="comment"
              :items="getCommentOptions()"
              label="Comment"
              hide-details
              class="mb-3"
            ></v-select>
          </div>
          <div class="px-4">
            <v-text-field
              dense
              outlined
              v-model="orderNumber"
              label="Order Number"
               hide-details
              class="mb-3"
            ></v-text-field>
          </div>
          <v-card-actions class="flex justify-end mb-2">
            <v-btn @click="handleChipCancel" class="rounded-md w-24" outlined
              >Cancel</v-btn
            >
            <v-btn
              @click="handleChipClick"
              class="white--text w-24 rounded-md"
              :color="$vuetify.theme.currentTheme.primary"
              >Add</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="requestDialog" max-width="63rem">
        <div>
          <InventoryRequest
            :editData="editData"
            @requestRaised="onRaiseRequest"
            @cancel="requestDialog = false"
          />
        </div>
      </v-dialog>
    </div>
  </div>
</template>

<script>
import { hasPermission, isModuleVisible,isAnyFeatureAllowed } from "@/utils/common";
import permissionData from "@/utils/permissions";
import InventoryRequest from "../InventoryRequest";
import LeaveRequestTable from "./LeaveRequestTable";
import { sparePartRequestStatusColors } from "@/utils/workforce/statusColors";
import CommentsPopOver from "../CommentsPopOver.vue";

const predefinedComments = {
  APPROVED: ["Inventory request Approved."],
  HOLD: [
    "Inventory request Hold, due to insufficient stock.",
    "Inventory request Hold, due to gathering approvals.",
    "Inventory request Hold, waiting for refill and notify back.",
  ],
  REJECTED: [
    "Inventory request Rejected, due to product model discontinue.",
    "Inventory request Rejected, due to lack of approvals.",
    "Inventory request Rejected, due to not available in requested store but available in another store.",
  ],
  WITHDRAW: [
    "Inventory request Withdraw, due to customer not required service any more.",
    " Inventory request Withdraw, request raised by mistake for this complaint.",
  ],
};

export default {
  name: "AlertComponent",
  layout: "Workforce/default",
  components: {
    InventoryRequest,
    LeaveRequestTable,
    CommentsPopOver,
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      headers: [
        { text: "Requested By", value: "requestedBy", sortable: false },
        { text: "Request Type", value: "requestType", sortable: false },
        { text: "Requested Id", value: "requestedId", sortable: false },
        {
          text: "Related Comaplaint",
          value: "complaintNumber",
          sortable: false,
        },
        { text: "Comment", value: "comment", sortable: false },
        { text: "Approved By", value: "approvedBy", sortable: false },
        { text: "Rejected By", value: "rejectedBy", sortable: false },
        { text: "Status", value: "status", sortable: false },
      ],
      requestStatusOptions: [
        { value: "", label: "All" },
        { value: "RAISED", label: "Raised" },
        { value: "APPROVED", label: "Approved" },
        { value: "REJECTED", label: "Rejected" },
        { value: "HOLD", label: "Hold" },
        { value: "WITHDRAW", label: "Withdraw" },
      ],
      selectedStatus: "",
      selectedRequestType: "",
      requests: [],
      leaveRequests: [],
      editData: null,
      filterComplaint: "",
      comment: "",
      orderNumber: "",
      options: { itemsPerPage: 5, page: 1 },
      totalItems: 0,
      totalLeaveRequests: 0,
      loading: false,
      loadingRefresh: false,
      menuState: {},
      selectedComments: {},
      singleExpand: true,
      expanded: [],
      selectedRequestId: null,
      selectedRequestStatus: "",
      openCommentDialog: false,
      requestDialog: false,
      requestStatusClrs: sparePartRequestStatusColors,
      comments: predefinedComments,
    };
  },
  watch: {
    options: {
      handler() {
        this.fetchAlerts();
        if (this.requestTypeOptions.length) {
          this.selectedRequestType = this.requestTypeOptions[0]?.value;
        }
      },
      deep: true,
    },
  },
  computed: {
    canEdit() {
      return hasPermission(this.userData, permissionData.requestWrite);
    },
    filteredRequests() {
      const filtered = this.requests.filter((req) => {
        if (!this.selectedStatus) return true;
        if (req.requestStatus === this.selectedStatus) return true;
        return false;
      });

      return filtered;
    },
    requestTypeOptions() {

      const requestTypes = [
        {
          value: "INVENTORY_REQUEST",
          label: "Inventory Request",
          visible: isModuleVisible("SELLS"),
        },
        {
          value: "COMPLAINT_APPROVAL_REQUEST",
          label: "Complaint Approval Request",
          visible: isModuleVisible("SELLS"),
        },
        {
          value: "LEAVE_REQUEST",
          label: "Leave Request",
          visible: isAnyFeatureAllowed("LEAVE_MANAGEMENT"),
        },
      ];
      return requestTypes.filter((request) => request.visible);
    },
  },
  methods: {
    toggleMenu(itemId, comments) {
      Object.keys(this.menuState).forEach((key) => {
        if (key !== itemId) {
          this.$set(this.menuState, key, false);
        }
      });
      this.$set(this.menuState, itemId, !this.menuState[itemId]);

      if (this.menuState[itemId]) {
        this.$set(this.selectedComments, itemId, comments);
      } else {
        this.$delete(this.selectedComments, itemId);
      }
    },
    closeMenu(id) {
      this.$set(this.menuState, id, false);
    },
    getExpandedHeaders() {

      return [
        { text: "Product Name", value: "productName", sortable: false },
        { text: "Model Number", value: "modelNumber", sortable: false },
        {
          text: "Quantity Requested",
          value: "quantityRequested",
          sortable: false,
        },
        { text: "Store Name", value: "storeName", sortable: false },
        { text: "Price", value: "productPrice", sortable: false },
      ];
    },
    getCommentOptions() {
      return predefinedComments[this.selectedRequestStatus];
    },
    navToComplaint(id) {
      const routeData = this.$router.resolve({
        path: "/workforce/leadspage/inventory",
        query: { active_tab: "complaint", complaint_no: id },
      });
      window.open(routeData.href, "_blank");
    },
    getRequestStatuses(item) {
      const loggedInUser = JSON.parse(localStorage.getItem("user"));
      const userId = loggedInUser._id;
      if (item.raisedBy?._id === userId) {
        return [
          {
            label: "Withdraw",
            value: "WITHDRAW",
          },
        ];
      }

      const statuses = [
        {
          label: "Approve",
          value: "APPROVED",
        },
        {
          label: "Reject",
          value: "REJECTED",
        },
        {
          label: "Hold",
          value: "HOLD",
        },
        {
          label: "Withdraw",
          value: "WITHDRAW",
        },
      ];

      return statuses;
    },
    async fetchAlerts() {
      try {
        this.loadingRefresh = true;
        this.loading = true;
        const { page, itemsPerPage } = this.options;
        const params = { page, limit: itemsPerPage };

        if (this.selectedStatus) {
          params.status = this.selectedStatus;
        }
        if (this.selectedRequestType) {
          params.requestType = this.selectedRequestType.trim();
        }
        if (this.filterComplaint) {
          params.search = this.filterComplaint.trim();
        }

        const response = await this.$axios.get(
          "/workforce/ecom/inventory/requests",
          {
            params,
          }
        );
        this.requests = response.data?.requests;
        this.totalItems = response.data?.pagination.totalCount;
        this.loadingRefresh = false;
        this.loading = false;
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    async fetchLeaveRequests(options) {
      this.loading = true;
      try {
        const loggedInUser = JSON.parse(localStorage.getItem("user"));
        const { page, itemsPerPage } = options;
        const userId = loggedInUser._id;
        const params = {
          leaveStatus: this.selectedStatus,
          page,
          limit: itemsPerPage,
        };
        const response = await this.$axios.get(
          `/workforce/emp/leave/request/${userId}`,
          {
            params,
          }
        );
        this.leaveRequests = response.data?.employeeLeaveRequests;
        this.totalLeaveRequests = response.data?.pagination.totalCount;
      } catch (error) {
        console.error("Error fetching leave requests:", error);
      } finally {
        this.loading = false;
      }
    },
    addComment(item, status) {
      this.openCommentDialog = true;
      this.selectedRequestId = item._id;
      this.selectedRequestStatus = status;
    },
    handleChipCancel() {
      this.openCommentDialog = false;
      this.selectedRequestId = null;
      this.selectedRequestStatus = "";
      this.comment = "";
    },
    async handleChipClick() {
      try {
        if (this.selectedRequestStatus === "REJECTED" && !this.orderNumber) {
          this.$toast.error("Please fill the order number");
          return;
        }
        const requestId = this.selectedRequestId;
        const payload = {
          status: this.selectedRequestStatus,
          comment: this.comment,
          orderNumber: this.orderNumber,
        };
        await this.$axios.put(
          `/workforce/ecom/inventory/request/${requestId}`,
          payload,
        );
        this.fetchAlerts();
        this.handleChipCancel();
      } catch (error) {
        console.log(error);
      }
    },
    isStatusDisabled(status) {
      const disabledStatuses = ["APPROVED", "REJECTED", "WITHDRAW"];

      return disabledStatuses.includes(status);
    },
    getStatusColor(taskStatus) {
      const colorInfo = this.requestStatusClrs[taskStatus];
      return {
        color: colorInfo ? colorInfo.textColor : "black",
        backgroundColor: colorInfo ? colorInfo.bgColor : "white",
        height: "39px",
        borderRadius: "20px",
        padding: "5px 10px",
        fontSize: "14px",
        boxShadow: "none",
        border: "1px solid transparent",
      };
    },
    raiseRequest(item) {
      this.requestDialog = true;
      this.editData = item;
    },
    async onRaiseRequest() {
      await this.fetchAlerts();
      this.requestDialog = false;
    },
    async handleInput() {
      this.options.page = 1;
      if (this.selectedRequestType === "LEAVE_REQUEST") {
        await this.fetchLeaveRequests({ itemsPerPage: 5, page: 1 });
      } else {
        await this.fetchAlerts();
      }
    },
    async refetchPage(options) {
      await this.fetchLeaveRequests(options);
    },
    addPartButton(item) {
      return item.requestStatus === "RAISED";
    },
  },
};
</script>

<style scoped>
.comment-cell {
  max-width: 150px;
}

.comment-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsisClass {
  max-width: 140px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.v-data-table
  >>> .v-data-table__wrapper
  tbody
  tr.v-data-table__expanded__content {
  box-shadow: none;
}

.v-menu__content {
  border-radius: 10px;
}
.fixed-width-col {
  max-width: 250px;
  min-width: 250px;
}

.fixed-width-input {
  width: 100%;
}
</style>
