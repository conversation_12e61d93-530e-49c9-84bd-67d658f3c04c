<template>
  <v-stepper v-model="e1">
    <v-stepper-header>
      <v-stepper-step :complete="e1 > 1" step="1"> Shop </v-stepper-step>

      <v-divider></v-divider>

      <v-stepper-step :complete="e1 > 2" step="2"> Cart </v-stepper-step>
    </v-stepper-header>

    <div class="flex align-center justify-end">
      <v-label>Employee Inventory</v-label>
      <v-switch
        :ripple="false"
        class="mr-10 ml-5 mb-1"
        inset
        :disabled="e1 !== 1"
        dense
        label="Global Inventory"
        v-model="globalInventory"
        @change="changeInventory"
      ></v-switch>
    </div>
    <v-stepper-items>
      <v-stepper-content step="1">
        <v-card class="mb-12" flat>
          <v-autocomplete
            v-model="selectedStore"
            label="Search Store"
            :items="activeStores"
            item-text="storeName"
            v-if="globalInventory"
            item-value="_id"
            class="mb-4"
            :disabled="isAutocompleteDisabled"
            hide-details
            clearable
            required
          ></v-autocomplete>
          <v-autocomplete
            v-model="selectedProduct"
            label="Search Products"
            @change="addProductCard"
            :items="inventoryItems"
            v-if="selectedStore || !globalInventory"
            item-text="productName"
            class="mb-4"
            item-value="_id"
            hide-details
            clearable
            required
          ></v-autocomplete>
        </v-card>

        <v-row v-if="productCards.length > 0" class="flex justify-evenly">
          <v-col
            v-for="(header, index) in Object.keys(headers)"
            :key="index"
            class="flex justify-around"
            :class="headers[header]"
          >
            <h4>{{ header }}</h4>
          </v-col>
        </v-row>

        <!-- Render ProductCards -->
        <div v-if="productCards.length > 0">
          <ProductCard
            v-for="(card, index) in productCards"
            :key="index"
            :product="card.product"
            :quantity="Number(card.quantity)"
            @deleteProduct="deleteProduct(index)"
            @increaseQuantity="increaseQuantity(index)"
            @decreaseQuantity="decreaseQuantity(index)"
          />
        </div>

        <v-row class="mt-4">
          <v-col cols="12" class="text-right">
            <v-btn
              outlined
              class="w-52 h-14 mr-8 rounded-md"
              @click="cancelDialog"
            >
              Cancel
            </v-btn>
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text w-52 h-14 rounded-md"
              @click="updateCalculations"
              :disabled="addToCartDisable || isAnyProductOutOfStock"
            >
              Add to cart ({{ numberOfProducts }})
            </v-btn>
          </v-col>
        </v-row>
      </v-stepper-content>

      <v-stepper-content step="2">
        <v-card class="mb-12" flat>
          <v-row v-if="productCards.length > 0" class="flex justify-evenly">
            <v-col
              v-for="(header, index) in Object.keys(headers)"
              :key="index"
              class="flex justify-around"
              :class="headers[header]"
            >
              <h4>{{ header }}</h4>
            </v-col>
          </v-row>
          <div v-if="productCards.length > 0">
            <ProductCard
              v-for="(card, index) in productCards"
              :key="index"
              :product="card.product"
              :quantity="card.quantity"
              @deleteProduct="deleteProduct(index)"
              @outOfStock="outOfStock(index)"
              @increaseQuantity="increaseQuantity(index)"
              @decreaseQuantity="decreaseQuantity(index)"
            />
          </div>

          <div
            v-if="productCards.length > 0"
            class="text-red-600 flex justify-center"
          >
            {{ warningMessage }}
          </div>

          <div class="flex">
            <div
              class="w-1/3 rounded-lg bg-gray-100 dark-bg-custom mt-4 ml-auto mr-3"
              v-if="productCards.length > 0"
            >
              <v-col
                v-for="item in calculations"
                :key="item.label"
                class="mt-2 flex justify-between"
              >
                <span class="font-weight-bold mr-10">{{ item.label }} </span>
                <span class="text-gray-600 dark-text-color">{{
                  item.value
                }}</span>
              </v-col>
            </div>
            <div class="mt-4 flex flex-row-reverse justify-center align-center">
              <v-btn
                v-if="productCards.length > 0"
                :color="$vuetify.theme.currentTheme.primary"
                class="white--text w-1/2 h-14 rounded-md"
                @click="confirmProduct"
                >Place Order
              </v-btn>
              <v-btn
                outlined
                class="w-1/2 h-14 mx-3 rounded-md"
                @click="handleBackButtonPress"
              >
                Back
              </v-btn>
            </div>
          </div>
        </v-card>
      </v-stepper-content>
    </v-stepper-items>
  </v-stepper>
</template>
<script>
import ProductCard from "./ProductCard";
export default {
  components: {
    ProductCard,
  },
  props: {
    selectedComplaintId: String,
    linkedTaskId: String,
    linkedCustomerId: String,
    assignedToId: String,
  },
  data() {
    return {
      e1: 1,
      inventoryItems: [],
      selectedStore: "",
      globalInventory: false,
      selectedProduct: null,
      numberOfParts: 1,
      productCards: [],
      calculations: [],
      empInventories: [],
      stores: [],
      isAnyProductOutOfStock: false,
      headers: {
        Image: "",
        Name: "ml-6",
        Model: "ml-7 pl-3",
        Status: "ml-5",
        Quantity: "ml-11 -mr-2",
        Amount: "-mr-4 pl-4",
        Remove: "flex justify-end",
      },
      warningMessage: null,
    };
  },
  watch: {
    productCards: {
      handler: function (newProductCards) {
        this.isAnyProductOutOfStock = newProductCards.some(
          (card) => card.product.availabilityStatus === "OUT_OF_STOCK"
        );
      },
      deep: true,
    },
  },
  mounted() {
    this.fetchInventory();
    this.fetchStores();
  },
  computed: {
    numberOfProducts() {
      return this.productCards.length;
    },
    isAutocompleteDisabled() {
      return this.productCards.length > 0;
    },
    addToCartDisable() {
      return !this.productCards.length;
    },
    activeStores() {
      return this.stores.filter(
        (store) => store.storeStatus !== "NON_OPERATIONAL"
      );
    },
  },

  methods: {
    increaseQty() {
      this.numberOfParts = this.numberOfParts + 1;
    },
    decreaseQty() {
      this.numberOfParts = this.numberOfParts - 1;
    },
    handleBackButtonPress() {
      this.e1 = 1;
      this.warningMessage = "";
    },
    changeInventory() {
      this.fetchInventory();
    },
    async fetchInventory() {
      try {
        let url;
        if (this.globalInventory) {
          url = `/workforce/ecom/products`;
        } else {
          url = `/workforce/ecom/inventory/user/${this.assignedToId}`;
        }
        const response = await this.$axios.get(url);
        if (this.globalInventory) {
          this.inventoryItems = response.data?.products;
        } else {
          this.empInventories = response.data?.inventories;
          this.inventoryItems = response.data?.inventories[0]?.products.map(
            (el) => {
              return el.productId;
            }
          );
        }
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    async fetchStores() {
      try {
        const response = await this.$axios.get("/workforce/ecom/stores");
        this.stores = response.data.stores;
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    addProductCard() {
      if (this.selectedProduct) {
        const selectedProductDetails = this.inventoryItems.find(
          (product) => product._id === this.selectedProduct
        );
        if (selectedProductDetails) {
          const existingProductIndex = this.productCards.findIndex(
            (card) => card.product._id === this.selectedProduct
          );

          existingProductIndex !== -1
            ? this.increaseQuantity(existingProductIndex)
            : this.productCards.push({
                product: selectedProductDetails,
                quantity: this.numberOfParts,
              });
          this.selectedProduct = "";
          this.numberOfParts = 1;
        }
      }
    },
    async confirmProduct() {
      try {
        const totalPrice = this.calculateValues();

        const products = this.productCards.map((product) => ({
          product: product.product._id,
          orderedQuantity: product.quantity,
        }));

        const data = {
          products,
          orderAmount: totalPrice[totalPrice.length - 1].value,
          linkedComplaintId: this.selectedComplaintId,
          linkedTaskId: this.linkedTaskId,
          linkedCustomerId: this.linkedCustomerId,
        };
        if (this.globalInventory) {
          data.storeId = this.selectedStore;
        }
        if (!this.globalInventory) {
          data.userInventory = this.empInventories[0]._id;
        }

        const response = await this.$axios.post("/workforce/ecom/order", data);

        if (response?.data?.success) {
          const successMessage = response?.data?.message || "Order created successfully !!"
          this.$toast.success(successMessage);
          this.warningMessage = "";
          this.cancelDialog();
        }
      } catch (error) {
        this.warningMessage = error.response.data.message;
      }
    },
    deleteProduct(index) {
      this.productCards.splice(index, 1);
      this.calculations = this.calculateValues();
    },
    increaseQuantity(index) {
      this.productCards[index].quantity++;
      this.calculations = this.calculateValues();
    },
    decreaseQuantity(index) {
      if (this.productCards[index].quantity > 1) {
        this.productCards[index].quantity--;
        this.calculations = this.calculateValues();
      }
    },

    calculateValues() {
      let subTotal = 0;
      let tax = 0;
      let discount = 0;
      let specialDiscount = 0;
      let installationCharges = 0;

      this.productCards.forEach((productCard) => {
        const { product, quantity } = productCard;
        const {
          productPrice,
          productTax,
          productDiscount,
          productInstallationCharges,
          productSpecialDiscount,
        } = product;

        subTotal += productPrice * quantity;
        tax += productTax * quantity;
        discount += productDiscount * quantity;
        specialDiscount += productSpecialDiscount * quantity;
        installationCharges += productInstallationCharges * quantity;
      });

      const totalAmountMRP =
        subTotal + tax - discount - specialDiscount + installationCharges;

      const calculations = [
        { label: "Sub Total", value: subTotal.toFixed(2) },
        { label: "Tax", value: tax.toFixed(2) },
        { label: "Discount", value: discount.toFixed(2) },
        { label: "Special Discount", value: specialDiscount.toFixed(2) },
        {
          label: "Installation Charges",
          value: installationCharges.toFixed(2),
        },
        { label: "Total Amount(MRP)", value: totalAmountMRP.toFixed(2) },
      ];

      return calculations;
    },

    updateCalculations() {
      this.e1 = 2;
      this.calculations = this.calculateValues();
    },
    cancelDialog() {
      this.selectedProduct = null;
      this.selectedStore = "";
      this.productCards = [];
      this.$emit("cancel");
      this.e1 = 1;
    },
  },
};
</script>
<style scoped>
.v-stepper__content {
  padding: 0px 24px 24px 24px;
}
</style>
