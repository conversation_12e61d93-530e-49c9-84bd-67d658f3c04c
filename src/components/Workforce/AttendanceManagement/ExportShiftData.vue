<template>
</template>

<script>
import * as XLSX from "xlsx";

export default {
    props: {
        dates: Array,
        employees: Array,
    },
    methods: {
        exportToExcel() {

            const dates = this.$props.dates
            const header = ['Employee', ...dates];

            const data = this.$props.employees.map(employee => {
                const row = new Array(dates.length).fill('');
                row[0] = employee.fullName;
                employee.dates.forEach(date => {
                    const dateObj = new Date(date.date);
                    const day = dateObj.getDate().toString().padStart(2, '0') + ' ' + dateObj.toLocaleString('default', { month: 'short' });
                    const dateIndex = header.indexOf(day);

                    if (dateIndex !== -1) {
                        row[dateIndex] = `${date.shift.shiftTime || ''}\n${date.shift.shiftStartTime || ''} - ${date.shift.shiftEndTime || ''}`;
                    }

                });
                return row;
            });

            const excelData = [header, ...data];


            const workbook = XLSX.utils.book_new();

            const worksheet = XLSX.utils.json_to_sheet(excelData);

            XLSX.utils.book_append_sheet(workbook, worksheet, 'Shifts');

            XLSX.writeFile(workbook, "Shifts.xlsx");
        }
    },
    mounted() {
        this.$root.$on('export-shift-excel', this.exportToExcel);
    },
    beforeDestroy() {
        this.$root.$off('export-shift-excel', this.exportToExcel);
    }
};
</script>
