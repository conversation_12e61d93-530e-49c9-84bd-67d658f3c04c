<template>
  <v-dialog v-model="dialog" max-width="500px" persistent>
    <v-card>
      <v-card-title>
        <span class="text-lg">Alter {{ column }} Timing</span>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="currentTime"
                class="pr-3"
                label="Time"
                type="time"
                required
                dense
                outlined
                hide-details
              ></v-text-field>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-title class="pt-0 pb-0">
        <span class="text-lg">Alter {{ column }} Address</span>
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12">
              <CommonAutocomplete
                class="pr-3"
                @selected-place="handleSelectedPlace"
                hide-details
              />
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn outlined class="mr-4" @click="closeDialog">Cancel</v-btn>
        <v-btn :color="$vuetify.theme.currentTheme.primary" class="white--text" @click="saveDialogData">Save</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import CommonAutocomplete from "~/components/CommonAutoComplete.vue";
dayjs.extend(customParseFormat);

export default {
  components: {
    CommonAutocomplete,
  },
  props: {
    dialog: Boolean,
    time: String,
    column: String,
  },
  data() {
    return {
      currentTime: this.convertTimeTo24HourFormat(this.time),
      address: "",
      latitude: "",
      longitude: "",
    };
  },
  watch: {
    time(newValue) {
      this.currentTime = this.convertTimeTo24HourFormat(newValue);
    },
  },
  methods: {
    convertTimeTo24HourFormat(time) {
      return time ? this.$dayjs(time, "hh:mm A").format("HH:mm") : "";
    },
    handleSelectedPlace(val) {
      this.latitude = val.location.lat();
      this.longitude = val.location.lng();
      this.address = val.formatted_address;
    },
    closeDialog() {
      this.$emit("update:dialog", false);
      this.currentAddress = "";
    },
    saveDialogData() {
      this.$emit("save", {
        time: this.currentTime,
        address: this.address,
        latitude: this.latitude,
        longitude: this.longitude,
      });
      this.closeDialog();
    },
  },
};
</script>
