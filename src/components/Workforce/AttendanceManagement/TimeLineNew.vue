<template>
  <div class="bg-gray-200 dark-bg-custom px-5">
    <div v-if="!isTimelineLoad" class="relative w-full flex justify-between">
      <div class="w-[30vw] rounded-lg p-4 pl-0 pt-0">
        <v-expansion-panels flat class="mt-6" v-model="panel">
          <v-expansion-panel
            v-for="(entry, index) in filteredData"
            :key="index"
            class="mb-2 rounded-lg shadow-sm"
            @click="displayMap(index)"
          >
            <v-expansion-panel-header
              class="flex justify-between items-center rounded-t-lg p-3 bg-gradient-to-r from-blue-50 to-blue-100 dark-bg-gradient"
              expand-icon=""
            >
              <div class="flex flex-col">
                <div class="flex items-center justify-between w-[100%] mb-2">
                  <div
                    class="w-[170px] h-[30px] bg-gray-200 dark-bg-default flex justify-center items-center"
                  >
                    <span class="text-blue-900 font-semibold"
                      >{{ entry.date }} ({{ dayOfMonth(entry.date) }})</span
                    >
                  </div>

                  <v-btn
                    color="primary"
                    small
                    class="text-sm"
                    icon
                    v-tooltip="{
                      text: 'Refresh Distance'
                    }"
                  >
                    <v-icon @click.stop="manualCalculateDistance(entry)">mdi-refresh</v-icon>
                  </v-btn>
                </div>
                <div class="flex items-center justify-between">
                  <p class="mb-0 text-gray-700 dark-text-color font-bold">
                    Total distance traveled:

                    <span class="font-semibold mx-2 text-red-600">{{
                      calculateTotalDistance(entry)
                    }}</span>
                    km (approx)
                  </p>
                </div>
                <div class="bg-gray-200 dark-bg-default mt-2 flex justify-between">
                  <div class="items-center flex flex-col p-2">
                    <p class="mb-0 text-blue-500 dark-text-color font-bold">Total</p>
                    <p class="mb-0 mt-1 text-blue-500">
                      {{ entry.stats.total }}
                    </p>
                  </div>
                  <div class="items-center flex flex-col p-2">
                    <p class="mb-0 text-orange-500 dark-text-color font-bold">Inprogress</p>
                    <p class="mb-0 mt-1 text-orange-500">
                      {{ entry.stats.total - entry.stats.completed }}
                    </p>
                  </div>
                  <div class="items-center flex flex-col p-2">
                    <p class="mb-0 text-green-500 dark-text-color font-bold">Completed</p>
                    <p class="mb-0 mt-1 text-green-500">
                      {{ entry.stats.completed }}
                    </p>
                  </div>
                </div>
              </div>
            </v-expansion-panel-header>

            <v-expansion-panel-content
              class="text-gray-700 bg-gray-50 dark-bg-expansion p-4 rounded-b-lg"
            >
              <div>
                <v-timeline align-top dense>
                  <v-timeline-item
                    v-for="(location, locIndex) in entry.location"
                    :key="locIndex"
                    :color="getTimelineItemColor(location?.activity?.action, location?.visitIndex)"
                    :icon="getIcon(location)"
                    class="custom_item p-1 flex"
                  >
                    <div
                      class="absolute h-1 mb-3 top-[-10%] -left-32 w-[26rem]"
                      v-if="location.activity?.action === 'taskStart'"
                    >
                      <div class="absolute mt-1 border-2 mb-0 px-2 py-0 rounded-full">
                        <p class="mb-0 font-bold">{{ location?.visitIndex }}</p>
                      </div>
                    </div>
                    <div class="flex flex-col items-start w-fit">
                      <div
                        v-if="location.activity?.action"
                        class="flex items-center mb-2 justify-between w-full"
                      >
                        <span
                          class="font-bold p-1 rounded text-white text-sm mr-2 cursor-pointer"
                          :class="{ 'disabled-span': !locationInfoAvailable(location) }"
                          @click="openInfoWindow(location)"
                          :style="{
                            backgroundColor: getTimelineItemColor(
                              location?.activity?.action,
                              location?.visitIndex
                            )
                          }"
                        >
                          {{ updateText(location.activity?.action) }}
                          <v-icon color="red" v-if="location.activity?.error?.state"
                            >mdi-alert</v-icon
                          >
                        </span>
                        <span
                          class="px-2 py-1 rounded border dark-text-color text-sm"
                          :style="{
                            border: getTimelineItemColor(
                              location?.activity?.action,
                              location?.visitIndex
                            ),
                            borderWidth: '2px',
                            borderStyle: 'solid'
                          }"
                        >
                          {{ convertTime(location?.activity?.time) }}
                        </span>
                      </div>
                      <div
                        v-if="location.activity?.image"
                        class="flex items-center gap-2 dark-text-color"
                      >
                        <span> Selfie:- </span>

                        <span
                          v-if="location.activity?.image"
                          @click.stop="openDialog(location.activity?.image)"
                          v-tooltip="{
                            text: 'Click to view'
                          }"
                          class="cursor-pointer"
                        >
                          <img
                            :src="location.activity?.image"
                            alt="Attendance Selfie"
                            class="w-[30px] h-[30px] rounded-full border-2 border-gray-200 shadow-md"
                            v-if="location.activity?.image"
                          />
                        </span>
                      </div>
                    </div>
                    <div
                      class="absolute mt-1 mb-3 -left-32 top-[-200%]"
                      v-if="location.activity?.action === 'taskEnded'"
                    >
                      <div class="mb-0 font-bold text-sm">Duration:</div>
                      <p class="mb-0 font-bold text-sm">
                        {{ location.totalVisitTime || '00:00:00' }}
                      </p>
                    </div>
                  </v-timeline-item>
                </v-timeline>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>

      <div
        v-if="filteredData.length !== 0"
        class="sticky right-0 rounded-lg overflow-hidden py-6"
        style="height: 70vh; top: 230px"
      >
        <div
          :id="'map-' + mapIndex"
          style="width: 63vw; height: inherit"
          class="map-container-timeline"
        ></div>
      </div>
    </div>
    <div v-else class="w-full flex flex-col justify-center items-center pt-4 pb-4">
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>

    <v-overlay :value="overlay">
      <v-progress-circular
        indeterminate
        size="64"
        :color="$vuetify.theme.currentTheme.primary"
      ></v-progress-circular>
    </v-overlay>
    <v-dialog v-model="isDialogOpen" max-width="460">
      <v-card>
        <v-img :src="dialogImage" max-height="600px" contain />
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
/* global google */

import { convertToTitleCase, getFullAddress } from '@/utils/common'
export default {
  name: 'TimelineModal',
  props: {
    selectedUserId: String,
    reporteesName: String,
    selectedReportee: Array,
    startDate: String,
    endDate: String,
    isTimelineLoad: Boolean
  },
  data() {
    return {
      userData: this.$storage.getUniversal('user'),
      overlay: true,
      timelineData: [],
      checkInData: [],
      initialZoom: 15,
      batteryLevel: 0,
      totalDistance: 0,
      employeeDeviceData: [],
      maps: [],
      google: null,
      polyline: '',
      directionsService: null,
      directionsRenderer: null,
      isDialogOpen: false,
      dialogImage: '',
      dates: [],
      mapIndex: 0,
      filteredData: [],
      panel: 0,
      infoWindows: [],
      markers: [],
      waypoints: [],
      waypointData: [],
      modal1: false,
      datesData: [],
      apiLoaded: false,
      dateFilter: false,
      locationHistory: [],
      loading: false,
      statsData: null
    }
  },
  watch: {
    selectedReportee: {
      async handler(newVal) {
        if (newVal) {
          this.datesData = [
            this.$dayjs(this.$props.startDate).format('YYYY-MM-DD'),
            this.$dayjs(this.$props.endDate).format('YYYY-MM-DD')
          ]
          await this.getUserLocationData()
          this.filteredData = this.filteredDatafunc()
          if (this.filteredData?.length > 0) {
            this.displayMap(this.mapIndex)
          }
        }
        this.isTimelineLoad = false
      }
    }
  },
  computed: {
    dateRangeText() {
      return this.datesData.join(' to ')
    }
  },

  beforeMount() {
    const scriptTag = document.querySelector('script[src*="maps.googleapis.com"]')
    if (!scriptTag) {
      const script = document.createElement('script')
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.VUE_APP_MAP_KEY}&libraries=places&callback=Function.prototype`
      script.async = true
      script.defer = true
      script.onerror = function (error) {
        console.error('Error loading Google Maps API:', error)
      }
      document.head.appendChild(script)
    }
    this.loadGoogleMapsAPI()
  },

  async mounted() {
    this.dateFilter = true
    await this.getUserLocationData()
    this.overlay = false

    this.datesData = [
      this.$dayjs().subtract(1, 'year').startOf('year').format('YYYY-MM-DD'),
      this.$dayjs().format('YYYY-MM-DD')
    ]
    if (this.datesData) {
      this.filteredData = this.filteredDatafunc()
    }

    if (this.filteredData.length > 0) {
      this.displayMap(0)
    }
  },
  methods: {
    updateText(text) {
      switch (text) {
        case 'checkIn':
          return 'Clock In'
        case 'taskStart':
          return 'Visit Start'
        case 'taskEnded':
          return 'Visit Ended'
        case 'taskComplete':
          return 'Visit Complete'
        case 'checkOut':
          return 'Clock Out'
        case 'uploadSelfie':
          return 'Upload Selfie'
        case 'questionnaire':
          return 'Questionnaire'
        case 'addMeetingNotes':
          return 'Add Meeting Notes'
        default:
          return text
      }
    },
    convertTime(date) {
      return this.$dayjs(date).format('h:mm:ss A')
    },
    dayOfMonth(date) {
      const dateD = this.$dayjs(date, 'DD-MM-YYYY')

      const dayOfWeek = dateD.format('ddd')

      return dayOfWeek
    },
    filteredDatafunc() {
      const [startDate, endDate] = this.datesData.map((date) => this.$dayjs(date))

      const filteredTimelineData = this.timelineData.filter((item) => {
        const itemDate = this.$dayjs(item.date, 'DD-MM-YYYY')
        return itemDate.isBetween(startDate, endDate, null, '[]')
      })

      let combinedStats = { total: 0, completed: 0 }

      const data = filteredTimelineData.map((item) => {
        let visitStartIndex = 1
        let visitEndIndex = 1
        let time1 = null
        let time2 = null
        const stats = item.location.reduce(
          (acc, loc) => {
            if (loc.activity.action === 'taskStart') {
              acc.total += 1
              combinedStats.total += 1
              loc.visitIndex = visitStartIndex++
              time1 = this.$dayjs(loc.activity.time)
            }
            if (loc.activity.action === 'taskEnded' && time1) {
              acc.completed += 1
              combinedStats.completed += 1
              loc.visitEndIndex = visitEndIndex++
              time2 = this.$dayjs(loc.activity.time)
              const diffHours = time2.diff(time1, 'hour')
              const diffMinutes = time2.diff(time1, 'minute') % 60
              const diffSeconds = time2.diff(time1, 'second') % 60
              loc.totalVisitTime = `${diffHours}:${diffMinutes}:${diffSeconds}`
            }
            loc.visitIndex = visitStartIndex
            loc.visitEndIndex = visitEndIndex
            return acc
          },
          { total: 0, completed: 0 }
        )

        return { ...item, stats }
      })

      this.$emit('statsData', combinedStats)
      return data
    },
    getColor(index) {
      const colors = ['blue', 'green', 'red', 'orange', 'purple']
      return colors[index % colors.length]
    },
    getHeaderColor(index) {
      const colors = [
        'linear-gradient(to right, #1E90FF, #87CEFA)',
        'linear-gradient(to right, #32CD32, #98FB98)',
        'linear-gradient(to right, #FF6347, #FF4500)',
        'linear-gradient(to right, #FFA07A, #FF8C00)',
        'linear-gradient(to right, #9370DB, #D8BFD8)'
      ]
      return colors[index % colors.length]
    },
    getIcon(location) {
      if (location) {
        return 'mdi-map-marker'
      }
    },
    locationInfoAvailable(location) {
      return !!(location.location?.geometry?.lat && location.location?.geometry?.lng)
    },

    async getUserLocationData() {
      try {
        this.overlay = true
        const data = this.$props.selectedReportee
        this.overlay = false
        if (data) {
          this.timelineData = this.processLocationData(data)
        } else {
          this.timelineData = []
        }
      } catch (error) {
        this.overlay = false
        this.timelineData = []
        console.error('Error fetching user location data:', error)
      } finally {
        if (window.google && window.google.maps) {
          this.initAllMaps()
        } else {
          setTimeout(() => {
            this.loadGoogleMapsAPI()
          }, 500)
        }
      }
    },

    processLocationData(data) {
      const sortedData = data
        ?.slice()
        .sort((a, b) => new Date(a.travelDate) - new Date(b.travelDate))
      const timelineData = sortedData?.map((el) => {
        const locationData = el.locationData
        const images = el.images
        const lastLocationWithDeviceData = locationData
          .slice()
          .reverse()
          .find((location) => location.deviceData)

        const lastDeviceData = lastLocationWithDeviceData
          ? lastLocationWithDeviceData.deviceData
          : null

        locationData.reduce((acc, location) => {
          const image = images?.find((img) => {
            const imgTime = new Date(img.time)
            const locTime = new Date(location.activity.time)
            return (
              imgTime.getHours() === locTime.getHours() &&
              imgTime.getMinutes() === locTime.getMinutes()
            )
          })
          const error = el.error?.find((err) => {
            const errTime = new Date(err.time)
            const locTime = new Date(location.activity.time)
            return (
              errTime.getHours() === locTime.getHours() &&
              errTime.getMinutes() === locTime.getMinutes()
            )
          })
          if (image) {
            location.activity.image = image.image
          }
          if (error) {
            location.activity.error = error.error
          }

          const address = location.location?.formatted_address
          if (!address) return acc

          if (!acc[address]) {
            acc[address] = {
              address,
              times: [],
              latitude: location.location.geometry?.lat || null,
              longitude: location.location.geometry?.lng || null,
              details: location.location?.formatted_address
                ? { display_name: location.location?.formatted_address }
                : {}
            }
          }

          acc[address].times.push({
            time: new Date(location.activity.time).toLocaleTimeString(),
            activity: location.activity,
            timestamp: new Date(location.activity.time).valueOf()
          })

          return acc
        }, {})
        return {
          date: this.$dayjs(el.travelDate).format('DD-MM-YYYY'),
          location: locationData,
          distance: el.dailyDistanceTraveled?.distance,
          pairDistanceArray: el.dailyDistanceTraveled.pairDistance,
          deviceData: lastDeviceData
        }
      })
      return timelineData.reverse()
    },

    loadGoogleMapsAPI() {
      if (window.google && window.google.maps) {
        this.apiLoaded = true
        return
      }
      const scriptTag = document.querySelector('script[src*="maps.googleapis.com"]')
      if (scriptTag) {
        scriptTag.parentNode.removeChild(scriptTag)
      }
      const script = document.createElement('script')
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.VUE_APP_MAP_KEY}&libraries=places&callback=Function.prototype`
      script.async = true
      script.defer = true
      document.head.appendChild(script)
      script.onload = () => {
        this.initAllMaps()
      }
      script.onerror = () => {
        this.loadGoogleMapsAPI()
      }
      window.initGoogleMaps = () => {
        this.apiLoaded = true
        this.checkAndInitMap(this.mapIndex)
      }
    },
    checkAndInitMap(index) {
      if (!this.apiLoaded) {
        setTimeout(() => this.checkAndInitMap(index), 500)
        return
      }
      this.initMap(index)
    },

    initAllMaps() {
      this.filteredData.forEach((entry, index) => {
        this.initMap(index)
      })
    },

    async initMap(index) {
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not loaded yet.')
        return
      }
      this.waypoints = []
      this.waypointData = []
      await this.$nextTick()

      const mapElement = document.getElementById(`map-${index}`)
      if (!mapElement) {
        console.error('Map element not found!')
        return
      }

      const mapOptions = {
        center: { lat: 28.6448, lng: 77.216721 },
        zoom: this.initialZoom
      }

      if (!mapElement && !mapOptions) {
        return
      }
      const map = new window.google.maps.Map(mapElement, mapOptions)
      this.maps[index] = map

      const bounds = new window.google.maps.LatLngBounds()

      this.waypoints = this.filteredData[this.mapIndex]?.location
        .filter((loc) => loc?.location?.geometry?.lat && loc?.location?.geometry?.lng)
        .map((loc) => {
          const position = {
            lat: loc.location.geometry.lat,
            lng: loc.location.geometry.lng
          }
          const activityData = { activity: loc?.activity }
          const deviceData = { deviceData: loc?.deviceData }
          bounds.extend(position)
          this.waypointData.push({
            location: position,
            formatted_address: loc.location?.formatted_address,
            activityData,
            deviceData,
            locationId: loc?._id,
            label: String.fromCharCode(65 + index)
          })
          return { location: position, stopover: true }
        })

      const colors = ['#FF0000', '#00FF00', '#0000FF', '#FF00FF', '#FFA500']
      if (this.waypoints.length > 0) {
        const origin = this.waypoints[0]?.location
        const destination = this.waypoints[this.waypoints.length - 1].location

        this.directionsService = new window.google.maps.DirectionsService()

        if (this.waypoints?.length > 20) {
          const chunksData = this.splitWaypoints(this.waypoints)
          const start = origin
          const end = destination
          for (let i = 0; i < chunksData.length; i++) {
            const chunk = chunksData[i]
            const middleWaypoints = chunk.slice(1, chunk.length - 1)

            const directionsRendererr = new window.google.maps.DirectionsRenderer({
              map,
              suppressMarkers: true,
              polylineOptions: {
                strokeColor: colors[index % colors.length],
                strokeOpacity: 0.8,
                strokeWeight: 5
              }
            })

            await this.directionWaypoints(start, end, middleWaypoints, map, directionsRendererr)
          }
        } else {
          this.directionsRenderer = new window.google.maps.DirectionsRenderer({
            map,
            suppressMarkers: true,
            polylineOptions: {
              strokeColor: colors[index % colors.length],
              strokeOpacity: 0.8,
              strokeWeight: 5
            }
          })
          this.directionsService.route(
            {
              origin,
              destination,
              waypoints: this.waypoints.slice(1, this.waypoints.length - 1),
              travelMode: window.google.maps.TravelMode.DRIVING
            },
            (response, status) => {
              if (status === 'OK') {
                this.addInfoWindowsToWaypoints(map, this.waypointData)
                this.directionsRenderer.setDirections(response)
              } else {
                console.error('Directions request failed due to ' + status)
                this.$toast.error('Directions request failed due to ' + status)
              }
            }
          )
        }
      }

      if (this.waypoints.length > 0) {
        map.fitBounds(bounds)
      }
    },
    splitWaypoints(waypoints) {
      const chunkSize = 23
      const chunks = []

      for (let i = 0; i < waypoints.length; i += chunkSize) {
        const chunk = waypoints.slice(i, i + chunkSize)
        chunks.push(chunk)
      }

      return chunks
    },
    directionWaypoints(origin, destination, waypoints, map, directionsRenderer) {
      return new Promise((resolve, reject) => {
        this.directionsService.route(
          {
            origin: origin,
            destination: destination,
            waypoints: waypoints,
            travelMode: google.maps.TravelMode.DRIVING
          },
          (response, status) => {
            if (status === google.maps.DirectionsStatus.OK) {
              directionsRenderer.setDirections(response)
              this.addInfoWindowsToWaypoints(map, this.waypointData)
              resolve(response)
            } else {
              console.error('Directions request failed due to ' + status)
              reject(status)
            }
          }
        )
      })
    },
    addInfoWindowsToWaypoints(map, waypointsdata) {
      this.infoWindows = []
      this.markers = []
      let visitStartIndex = 1
      let visitEndIndex = 1
      waypointsdata.forEach((waypoint, index) => {
        if (waypoint?.activityData?.activity?.action === 'taskStart') {
          waypoint.visitIndex = visitStartIndex++
        }
        if (waypoint?.activityData?.activity?.action === 'taskEnded') {
          waypoint.visitEndIndex = visitEndIndex++
        }
        waypoint.visitIndex = visitStartIndex
        waypoint.visitEndIndex = visitEndIndex

        const marker = new window.google.maps.Marker({
          position: waypoint.location,
          map: map,
          label: String.fromCharCode(65 + index),
          title: `Waypoint ${index + 1}`
        })
        let lastWaypoint
        if (index !== 0) {
          lastWaypoint = waypointsdata[index - 1]
        }
        let actionText = waypoint?.activityData?.activity?.action

        let customerAddress =
          waypoint?.activityData?.activity?.caseTaskDetails?.case?.customer?.customerAddress || []

        const customInfoWindowContent = `
            <div style="font-size: 14px; color: #333; width: 300px; border: 1px solid ${this.getTimelineItemColor(
              waypoint?.activityData?.activity?.action,
              waypoint.visitIndex
            )};;" class="mt-1 p-2 rounded dark-bg-custom">
              ${
                this.updateText(actionText) &&
                `  
                <span style="background-color: ${this.getTimelineItemColor(
                  waypoint?.activityData?.activity?.action,
                  waypoint.visitIndex
                )};" class="mb-2 font-bold p-1 rounded text-white text-sm mr-2">${this.updateText(
                  actionText
                )}</span>
              `
              }
              <span class="text-sm px-1 rounded mb-2" style="border: 1px solid ${this.getTimelineItemColor(
                waypoint?.activityData?.activity?.action,
                waypoint.visitIndex
              )};">
                <span class="font-bold text-gray-500"> 
                      ${this.convertTime(waypoint?.activityData?.activity?.time)}
                </span>
                </span>
                  ${
                    waypoint?.activityData?.activity?.caseTaskDetails?.case?.caseNo
                      ? `
                  <div class="mt-2">
                    <span class="font-bold text-sm mr-2">
                      Case info: 
                    </span>
                    <span class="mr-2 font-semibold text-gray-500">
                    ${waypoint?.activityData?.activity?.caseTaskDetails?.case?.caseNo}
                    </span>
                    ${
                      waypoint?.activityData?.activity?.caseTaskDetails?.case?.userDefinedCaseStatus
                        ? `
                      <span class="font-bold p-1 px-3 rounded text-white text-sm" style="background-color: ${this.getTimelineItemColor(
                        waypoint?.activityData?.activity?.action,
                        waypoint.visitIndex
                      )};">
                          ${
                            waypoint?.activityData?.activity?.caseTaskDetails?.case
                              ?.userDefinedCaseStatus
                          }
                      <span>
                    `
                        : ''
                    }
                  </div>
                `
                      : ''
                  }
                      ${
                        waypoint?.activityData?.activity?.caseTaskDetails?.case
                          ? `
                  <div class="mt-2">
                    <span class="font-bold text-sm mr-2">
                      Team/ Case Type: 
                    </span>
                    <span class="font-semibold text-gray-500" >
                    ${waypoint?.activityData?.activity?.caseTaskDetails?.case?.caseType}
                    </span>
                  </div>
                `
                          : ''
                      }
                ${
                  waypoint?.activityData?.activity?.taskDetails?.taskTitle ||
                  waypoint?.activityData?.activity?.caseTaskDetails?.taskTitle
                    ? `
                  <div class="mt-2">
                    <span class="font-bold text-sm mr-2">
                      Visit info: 
                    </span>
                    <span class="font-semibold text-gray-500">
                    ${
                      waypoint?.activityData?.activity?.taskDetails?.taskTitle ||
                      waypoint?.activityData?.activity?.caseTaskDetails?.taskTitle
                    }
                    </span>
                  </div>
                `
                    : ''
                }
                ${
                  waypoint?.activityData?.activity?.caseTaskDetails?.case?.customer
                    ? `
                  <div class="mt-2">
                    <span class="font-bold text-sm mr-2">
                      Customer Name: 
                    </span>
                    <span class="font-semibold text-gray-500" >
                    ${waypoint?.activityData?.activity?.caseTaskDetails?.case?.customer?.customerFullName}
                    </span>
                  </div>
                `
                    : ''
                }
                     ${
                       customerAddress && customerAddress?.length
                         ? `
                  <div class="mt-2">
                    <span class="font-bold text-sm mr-2">
                     Customer Address:
                    </span>
                    <span class="font-semibold text-gray-500">${getFullAddress(customerAddress[0])}
                    </span>
                  </div>
                `
                         : ''
                     }
                ${
                  waypoint.deviceData?.deviceData?.batteryHealth
                    ? `
                  <div class="mt-2">
                    <span class="font-bold text-sm mr-2">
                      Battery Health:
                    </span>
                    <span class="font-semibold text-gray-500">
                    ${waypoint.deviceData?.deviceData?.batteryHealth}%
                    </span>
                  </div>
                `
                    : ''
                }
                ${
                  waypoint.deviceData?.deviceData?.deviceName
                    ? `
                  <div class="mt-2">
                    <span class="font-bold text-sm mr-2">
                     Device Name:
                    </span>
                    <span class="font-semibold text-gray-500">
                    ${waypoint.deviceData?.deviceData?.deviceName}
                    </span>
                  </div>
                `
                    : ''
                }
               ${
                 lastWaypoint
                   ? `
                  <p class="mt-2 mb-0"><span class="font-bold">From location:</span>
                    <span class="font-semibold text-gray-500">
                     ${lastWaypoint?.formatted_address}
                    </span> 
                   </p>
                `
                   : ''
               }
                <p class="mt-2 mb-0"><span class="font-bold">Visit done on location:</span>
                  <span class="font-semibold text-blue-500">
                    ${waypoint?.formatted_address}
                    </span> 
                </p>
                ${
                  waypoint?.activityData?.activity?.error?.state
                    ? `
                  <p class=" text-sm text-red-500 mt-2 mb-0"><span class="font-bold">Error Reason:</span> ${waypoint?.activityData?.activity?.error?.messages?.join(
                    ' && '
                  )}</p>
                `
                    : ''
                }

              </div>
              `

        const infoWindow = new window.google.maps.InfoWindow({
          content: customInfoWindowContent
        })

        marker.addListener('click', () => {
          if (this.currentOpenInfoWindow) {
            this.currentOpenInfoWindow.close()
          }
          infoWindow.open(map, marker)
          this.currentOpenInfoWindow = infoWindow
        })

        this.infoWindows.push(infoWindow)
        this.markers.push(marker)
      })
    },

    openInfoWindow(location) {
      const index = this.waypointData.findIndex(
        (point) =>
          point.location?.lat === location.location?.geometry?.lat &&
          point.location?.lng === location.location?.geometry?.lng &&
          point?.activityData?.activity?.action === location?.activity?.action &&
          location?._id === point?.locationId
      )

      if (index !== -1 && this.infoWindows[index] && this.markers[index]) {
        if (this.currentOpenInfoWindow) {
          this.currentOpenInfoWindow.close()
        }
        this.infoWindows[index].open(this.maps[0], this.markers[index])
        this.currentOpenInfoWindow = this.infoWindows[index]
      } else {
        console.error('No matching waypoint found for the given location.')
        this.$toast.error('No matching waypoint found for the given location.')
      }
    },
    closeCurrentInfoWindow() {
      if (this.currentOpenInfoWindow) {
        this.currentOpenInfoWindow.close()
        this.currentOpenInfoWindow = null
      }
    },
    displayMap(index) {
      this.mapIndex = index
      this.$nextTick(() => {
        this.initMap(this.mapIndex)
      })
    },

    calculateTotalDistance(entry) {
      if (entry.distance) {
        return entry?.distance.toFixed(2)
      }
      return 0
    },

    getTimelineItemColor(status, index) {
      if (status === 'checkIn') return '#4CAF50'
      if (status === 'checkOut') return '#21427D'

      const colors = ['#FF9900', '#3357FF', '#FF33A1', '#A133FF']
      return colors[index % colors.length]
    },

    getBorderClass(status) {
      if (status === 'checkIn') return '3px solid #4CAF50'
      if (status === 'checkOut') return '3px solid #21427D'
      return '3px solid #067CFB'
    },

    getTextClass(status) {
      if (status === 'checkIn') return 'text-green-500'
      if (status === 'checkOut') return 'text-red-500'
      return 'text-blue-500'
    },
    openDialog(imageUrl) {
      this.dialogImage = imageUrl
      this.isDialogOpen = true
    },
    convertTitle(str) {
      return convertToTitleCase(str) || ''
    },
    async manualCalculateDistance(entry) {
      try {
        if (!this.selectedUserId || !entry.date) {
          this.$toast.error('Selected userId is not available')
          return
        }
        const payload = {
          date: this.$dayjs(entry.date, 'DD-MM-YYYY').format('YYYY-MM-DD'),
          userId: this.selectedUserId,
          orgId: this.userData?.organization_id
        }
        await this.$axios.post('/workforce/distance/calculate-distance', payload)
        this.$toast.success(`User distance updated for ${entry.date} date`)
        await this.getUserLocationData()
        this.calculateTotalDistance(entry)
      } catch (error) {
        console.log('Error:', error)
        const errorMessage = error?.response?.data?.message || 'Error in distance calculation'
        this.$toast.error(errorMessage)
      }
    },
    filteredLocations(data) {
      return data.location.filter((location) => location.activity?.action)
    }
  }
}
</script>

<style>
:root {
  --timeline-opposite-item-width: 120px;
  --timeline-line-width: 3px;
}

.v-timeline--dense .v-timeline-item__opposite {
  display: inline-block;
}

.v-timeline-item__opposite {
  flex: none;
  min-width: var(--timeline-opposite-item-width);
  padding-left: 10px;
}

.v-application--is-ltr .v-timeline--dense:not(.v-timeline--reverse):before {
  left: calc((105px - var(--timeline-line-width)) / 2);
  width: var(--timeline-line-width);
}

.v-timeline::before {
  height: auto;
}
</style>

<style scoped>
.custom-title {
  font-size: 16px;
}
</style>

<style>
.v-timeline-item__body {
  display: flex;
}
.gm-style-iw button[title='Close'] {
  display: none !important;
}
.disabled-span {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
