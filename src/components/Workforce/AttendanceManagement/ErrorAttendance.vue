<template>
  <v-dialog v-model="localDialogOpen" max-width="600" persistent>
    <v-card>
      <v-divider></v-divider>

      <div class="flex justify-between items-center">
        <v-card-title class="text-h6">Mark Attendance</v-card-title>
        <div class="mr-2">
          <v-btn
            :disabled="!isApplyEnabled"
            color="primary"
            small
            class="mr-4"
            @click="handleApply"
          >
            Apply
          </v-btn>
          <v-icon @click="closeDialog">mdi-close</v-icon>
        </div>
      </div>
      <p class="text-xs text-red-500 text-semibold px-4">
        * NOTE: Attendance not marked because of following reason(s).
      </p>
      <p class="text-sm px-4 optionSelect">Select option</p>
      <div class="flex flex-wrap gap-4 px-4 my-2">
        <v-btn
          v-for="text in attendanceOptions"
          :key="text"
          :outlined="selectedAttendanceOption !== text"
          :color="selectedAttendanceOption === text ? 'primary' : 'grey'"
          @click="selectAttendanceOption(text)"
          small
        >
          {{ text }}
        </v-btn>
      </div>
      <div class="flex justify-end px-4">
        <v-btn
          v-if="formattedAttendanceData.length > 1"
          @click="toggleViewMore"
          small
          text
          color="indigo"
        >
          {{ showMore ? "View Less" : "View More" }}
          <v-icon size="18">
            {{ showMore ? "mdi-arrow-up" : "mdi-arrow-down" }}
          </v-icon>
        </v-btn>
      </div>
      <v-card-text class="scrollable-content">
        <v-list dense>
          <v-list-item
            v-for="(item, index) in displayedAttendanceData"
            :key="index"
            class="mb-2 flex align-start"
          >
            <div class="mr-4">
              <p class="mt-2">{{ index + 1 }} :</p>
            </div>
            <v-list-item-content>
              <p><strong>Date:</strong> {{ item?.date }}</p>
              <p><strong>Time:</strong> {{ item?.time }}</p>
              <p>
                <strong>Status:</strong>
                <span :class="item?.statusClass"> {{ item?.status }} </span>
              </p>
              <p><strong>Address:</strong> {{ item?.address }}</p>
              <p>
                <strong>Location:</strong> ({{ item?.lat }}, {{ item?.lng }})
              </p>
              <p v-if="item?.errorMessages.length">
                <strong class="text-red--text">Error:</strong>
              </p>
              <ul class="error-list">
                <li
                  v-for="(error, i) in item?.errorMessages"
                  :key="i"
                  class="text-red--text"
                >
                  {{ error }}
                </li>
              </ul>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    isVerificationDialogOpen: {
      type: Boolean,
      default: false,
    },
    dialogAttendanceData: {
      type: Array,
      default: () => [],
    },
    options: Object,
  },
  data() {
    return {
      localDialogOpen: this.isVerificationDialogOpen,
      selectedAttendanceOption: null,
      showMore: false,
    };
  },
  watch: {
    isVerificationDialogOpen(val) {
      this.localDialogOpen = val;
      if (val) this.resetState();
    },
    localDialogOpen(val) {
      this.$emit("update:isVerificationDialogOpen", val);
      if (!val) this.resetState();
    },
  },
  computed: {
    formattedAttendanceData() {
      return this.dialogAttendanceData
        .map((item) => ({
          date: this.$dayjs(item.time).format("YYYY-MM-DD"),
          time: this.$dayjs(item.time).format("HH:mm:ss"),
          status: item.status,
          statusClass:
            item.status === "CHECKED_IN"
              ? "text-green--text"
              : "text-red--text",
          address: item.location?.formatted_address || "No address available",
          lat: item.location?.geometry.lat || "N/A",
          lng: item.location?.geometry.lng || "N/A",
          errorMessages: item.error?.state ? item.error.messages : [],
        }))
        .reverse(); // Reverse order for most recent first
    },
    displayedAttendanceData() {
      return this.showMore
        ? this.formattedAttendanceData
        : [this.formattedAttendanceData[0]];
    },
    attendanceOptions() {
      return Object.keys(this.options);
    },
    isApplyEnabled() {
      return this.selectedAttendanceOption !== null;
    },
  },
  methods: {
    selectAttendanceOption(option) {
      this.selectedAttendanceOption =
        this.selectedAttendanceOption === option ? null : option;
    },
    handleApply() {
      if (this.selectedAttendanceOption) {
        this.$emit("selectedAttendanceOption", this.selectedAttendanceOption);
      }
    },
    closeDialog() {
      this.localDialogOpen = false;
    },
    resetState() {
      this.selectedAttendanceOption = null;
      this.showMore = false;
    },
    toggleViewMore() {
      this.showMore = !this.showMore;
    },
  },
};
</script>

<style scoped>
.text-green--text {
  color: #4caf50;
}

.text-red--text {
  color: #f44336;
}

ul.error-list {
  list-style-type: disc;
  margin-left: 20px;
}

ul.error-list li {
  margin-bottom: 5px;
}

.scrollable-content {
  max-height: 70vh;
  overflow-y: auto;
}

.scrollable-content {
  padding-bottom: 16px;
}
.optionSelect {
  font-weight: 800;
}
</style>
