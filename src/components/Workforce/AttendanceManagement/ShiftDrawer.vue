<template>
  <div>
    <v-row no-gutters class="mb-2 flex justify-between">
      <h4 class="font-bold text-xl">Shift Templates</h4>
      <v-icon class="cursor-pointer" @click="handleClose">mdi-close</v-icon>
    </v-row>

    <v-toolbar flat>
      <span
        class="text-gray-400 dark-text-color dark-bg-custom font-bold text-sm"
        ><span class="text-red-500">*</span> Select, drag and drop shifts
        templates on calender table for an employee.</span
      >
    </v-toolbar>
    <v-list class="flex flex-col justify-center">
      <v-btn
        :color="$vuetify.theme.currentTheme.primary"
        class="my-3 white--text"
        @click="handleRoute"
        v-if="!getActiveShifts"
        >Add Shift</v-btn
      >
      <v-list-item-content
        v-for="shift in shiftTimesData"
        v-show="shift.status === 'Active'"
        :key="shift._id"
        draggable="true"
        @dragstart="
          onDragStart(
            $event,
            shift.shiftName,
            shift.shiftStartTime,
            shift.shiftEndTime,
            shift.roasterColor
          )
        "
        :style="{
          backgroundColor: shift.roasterColor.bgColor,
          borderColor: shift.roasterColor.borderColor,
        }"
        class="custom-box pt-0 pb-0"
      >
        <v-list-item-content class="">
          <v-list-item-content class="font-bold text-2xl pt-0"
            >{{ shift.shiftStartTime }} - {{ shift.shiftEndTime }}
          </v-list-item-content>
          <v-list-item-content class="text-xl pt-0 pb-0">{{
            shift.shiftName
          }}</v-list-item-content>
        </v-list-item-content>
      </v-list-item-content>
    </v-list>
  </div>
</template>

<script>
export default {
  data() {
    return {
      menu: false,
      drawer: false,
      shiftTimesData: [],
    };
  },
  props: {
    shiftTimes: Array,
  },
  mounted() {
    this.shiftTimesData = this.$props.shiftTimes;
  },
  computed: {
    getActiveShifts() {
      return this.shiftTimesData.filter((item) => item.status === "Active")
        .length;
    },
  },
  methods: {
    handleRoute() {
      this.$router.push({
        path: "/workforce/settings",
        query: {
          settings_tab: "configurations",
          config_tab: "attendanceSetting",
        },
      });
    },
    onDragStart(event, shiftTime, shiftStartTime, shiftEndTime, roasterColor) {
      event.dataTransfer.setData("format", shiftTime);
      event.dataTransfer.setData("startTime", shiftStartTime);
      event.dataTransfer.setData("endTime", shiftEndTime);
      event.dataTransfer.setData("roasterColorBg", roasterColor.bgColor);
      event.dataTransfer.setData(
        "roasterColorBorder",
        roasterColor.borderColor
      );
    },
    handleClose() {
      this.$emit("handleClose", false);
    },
  },
};
</script>

<style>
.sticky-position {
  position: sticky;
  left: 0;
  background: white;
  z-index: 1;
}

.v-data-table > .v-data-table__wrapper table th:nth-child(2),
.v-data-table > .v-data-table__wrapper table td:nth-child(2) {
  width: 15%;
}
.custom-box {
  border: 1px solid #ccc;
  border-left: 6px solid #dfb93d;
  width: 200px;
  /* padding: 10px; */
  background-color: #fff8e1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin: 10px;
  padding: 10px;
}
</style>
