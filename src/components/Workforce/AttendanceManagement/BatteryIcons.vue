<template>
  <div>
    <v-icon class="rotated-icon" v-if="batteryLevel >= 90" style="color: #06cf9c"
      >mdi-battery</v-icon
    >
    <v-icon class="rotated-icon" v-else-if="batteryLevel >= 80" style="color: #54a7e2"
      >mdi-battery-80</v-icon
    >
    <v-icon class="rotated-icon" v-else-if="batteryLevel >= 60" style="color: #ff8423"
      >mdi-battery-60</v-icon
    >
    <v-icon class="rotated-icon" v-else-if="batteryLevel >= 40" style="color: #f2605f"
      >mdi-battery-40</v-icon
    >
    <v-icon class="rotated-icon" v-else style="color: #b60505">mdi-battery-20</v-icon>
  </div>
</template>

<script>
export default {
  name: 'BatteryIcons',
  props: {
    batteryLevel: Number
  },
  mounted() {
    // console.log(this.batteryLevel)
  }
}
</script>

<style></style>
