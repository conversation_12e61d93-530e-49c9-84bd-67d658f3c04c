<template>
  <div class="p-2">
    <div v-if="isLoadTableData">
      <v-progress-linear color="black" indeterminate rounded height="6"></v-progress-linear>
    </div>
    <div
      v-if="isLoadTableData"
      class="flex absolute z-10 flex-col items-center h-[60vh] w-screen justify-center"
    >
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
    <div class="overflow-x-auto overflow-y-auto">
      <div class="table-wrapper">
        <div class="table-container" v-if="!loadTable">
          <no-data
            v-if="renderTable"
            title="Nothing to Display"
            :subTitle="`Please select the custom date range`"
          >
          </no-data>
          <v-data-table
            :headers="[]"
            :items="items"
            class="custom-data-table"
            :loading="loadTable"
            hide-default-footer
            hide-default-header
            ref="dataTable"
            v-else
          >
            <template v-slot:header>
              <thead>
                <tr>
                  <th class="fixed-column main-header" rowspan="2">Name</th>
                  <template v-for="date in currentMonthDates">
                    <th
                      :key="date"
                      :data-date="date"
                      :class="{
                        'non-working-day':
                          isNonWorkingDay(date).isWeekend || isNonWorkingDay(date).isHoliday,
                        today: today === date
                      }"
                      class="main-header"
                      :colspan="selectedColumns.length"
                    >
                      {{ date }} ({{ dayOfMonth(date) }})
                      <v-tooltip v-if="isNonWorkingDay(date).isHoliday" top>
                        <template v-slot:activator="{ on }">
                          <v-icon v-on="on" style="cursor: default">mdi-information</v-icon>
                        </template>
                        <span>{{ isNonWorkingDay(date).holidayName }}</span>
                      </v-tooltip>
                    </th>
                  </template>
                </tr>
                <tr>
                  <template v-for="date in currentMonthDates">
                    <template v-for="column in selectedColumns">
                      <th
                        class="sub-header"
                        v-bind:class="{
                          'non-working-day':
                            isNonWorkingDay(date).isWeekend || isNonWorkingDay(date).isHoliday,
                          leftBorder: leftBorder(today, date, selectedColumns, column),
                          rightBorder: rightBorder(today, date, selectedColumns, column)
                        }"
                      >
                        {{ column }}
                      </th>
                    </template>
                  </template>
                </tr>
              </thead>
            </template>

            <template v-slot:item="{ item }">
              <tr class="hover-effect">
                <td
                  :class="{
                    'cursor-not-allowed fixed-column font-semibold ': !hasAttendenceData(
                      item.attendance
                    ),
                    'fixed-column font-semibold hover:text-blue-500 ': hasAttendenceData(
                      item.attendance
                    )
                  }"
                  @click="hasAttendenceData(item?.attendance) && openReporteeDialog(item)"
                >
                  <employee-info :item="item" view="attendenceTable"></employee-info>
                </td>
                <template v-for="date in currentMonthDates">
                  <template v-for="column in selectedColumns">
                    <td
                      :key="`${item.employeeId}-${date}-${column}`"
                      :class="{
                        'non-working-day':
                          isNonWorkingDay(date).isWeekend || isNonWorkingDay(date).isHoliday,
                        leftBorder: leftBorder(today, date, selectedColumns, column),
                        rightBorder: rightBorder(today, date, selectedColumns, column)
                      }"
                      @click="openTimeAddressDialog(item, date, column)"
                    >
                      <v-btn
                        v-if="
                          column === 'Attendance' &&
                          isAnyErrorInAttendance(item.attendance[date]?.checkInOutData)
                        "
                        class="white--text"
                        @click="openAttendanceDialog(item, date)"
                        :color="$vuetify.theme.currentTheme.primary"
                        small
                      >
                        Take Action
                      </v-btn>
                      <v-menu
                        v-else-if="
                          column === 'Attendance' &&
                          !isAnyErrorInAttendance(item.attendance[date]?.checkInOutData)
                        "
                        :close-on-content-click="false"
                        :nudge-width="20"
                        offset-y
                        :value="openMenu === generateCellIdentifier(item, date, column)"
                        @input="(val) => handleMenuOpen(item, date, column, val)"
                      >
                        <template v-slot:activator="{ on, attrs }">
                          <span
                            v-bind="attrs"
                            v-on="on"
                            class="py-2 cursor-pointer"
                            style="display: block; width: 100%"
                            @click="openMenu = generateCellIdentifier(item, date, column)"
                          >
                            <template
                              v-if="
                                item &&
                                item.attendance &&
                                item.attendance[date] &&
                                column === 'Attendance'
                              "
                            >
                              <span class="text-xs">
                                {{ item.attendance[date][column] }}
                              </span>
                              <span>
                                <v-tooltip
                                  top
                                  v-if="isAttendanceStausLeave(item, date, column)"
                                  open-on-click
                                  :open-on-hover="false"
                                  :open-delay="1000"
                                >
                                  <template v-slot:activator="{ on }">
                                    <v-icon
                                      class="cursor-pointer"
                                      v-on="on"
                                      retain-focus-on-click
                                      @click.stop="
                                        fetchLeaveRequests(item.attendance[date]?.LeaveReqId)
                                      "
                                    >
                                      mdi-information
                                    </v-icon>
                                  </template>
                                  <span v-if="leaveRequestData.leaveType">
                                    Type of leave:
                                    {{ leaveRequestData.leaveType }}<br />
                                    Leave Status:
                                    {{ leaveRequestData.leaveStatus }}<br />
                                    Comment:
                                    {{ leaveRequestData?.comments[0]?.comment || '' }}<br />
                                    Approved By:
                                    {{ leaveRequestData.approvedBy }}
                                  </span>
                                </v-tooltip>
                              </span>
                            </template>
                            <template v-else>
                              {{
                                (item.attendance &&
                                  item.attendance[date] &&
                                  item.attendance[date][column]) ||
                                '-'
                              }}
                            </template>
                          </span>
                        </template>
                        <v-list>
                          <v-list-item
                            v-for="(option, key) in attendanceOptions"
                            :key="key"
                            class="text-sm"
                            :class="{
                              selected:
                                key ===
                                (item.attendance[date] ? item.attendance[date][column] : null)
                            }"
                            @click="saveAttendance(item, date, column, key)"
                          >
                            {{ key }}
                          </v-list-item>
                        </v-list>
                      </v-menu>
                      <!-- daily status -->
                      <template v-else-if="item.attendance[date]?.Status && column === 'Status'">
                        <span
                          class="px-0 text-sm"
                          :style="{
                            color: durationStatusColor(item.attendance[date]?.Status)?.textColor
                          }"
                        >
                          {{ item.attendance[date]?.Status }}
                        </span>
                      </template>
                      <template v-else>
                        {{ getAttendanceOrImage(item, date, column) }}
                      </template>
                      <template
                        v-if="item.attendance[date]?.clockInImage && column === 'Clock-In Image'"
                      >
                        <v-hover v-slot:default="{ props }">
                          <span v-bind="props" class="cursor-pointer">
                            <img
                              :src="item.attendance[date].clockInImage"
                              alt="Attendance Selfie"
                              style="width: 30px; height: 30px; margin: auto"
                              @click.stop="openDialog(item.attendance[date].clockInImage)"
                            />
                          </span>
                        </v-hover>
                      </template>
                      <template
                        v-if="item.attendance[date]?.clockOutImage && column === 'Clock-Out Image'"
                      >
                        <v-hover v-slot:default="{ props }">
                          <span v-bind="props" class="cursor-pointer">
                            <img
                              :src="item.attendance[date].clockOutImage"
                              alt="Attendance Selfie"
                              style="width: 30px; height: 30px; margin: auto"
                              @click.stop="openDialog(item.attendance[date].clockOutImage)"
                            />
                          </span>
                        </v-hover>
                      </template>
                    </td>
                  </template>
                </template>
              </tr>
            </template>
          </v-data-table>
        </div>
        <div v-else class="w-full flex flex-col items-center justify-center">
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </div>

      <v-dialog v-model="isDialogOpen" max-width="460">
        <v-card>
          <v-img :src="dialogImage" max-height="600px" contain />
        </v-card>
      </v-dialog>

      <ErrorAttendance
        :isVerificationDialogOpen="isVerificationOpen"
        :dialogAttendanceData="dialogAttendanceData"
        :options="attendanceOptions"
        @selectedAttendanceOption="
          (val) => saveAttendance(editData, selectedErrorDate, 'Attendance', val)
        "
        @update:isVerificationDialogOpen="isVerificationOpen = $event"
      />

      <v-dialog
        v-model="isReporteeDialogOpen"
        origin="top center"
        persistent
        class="dialogOverflow"
        v-if="isReporteeDialogOpen"
      >
        <div class="bg-white dark-bg-default">
          <v-card class="dark-bg-default">
            <div class="btnClass px-8 py-3">
              <div class="flex justify-between items-start">
                <div class="flex flex-col">
                  <p class="text-md mb-0">
                    <span class="font-semibold">{{ reporteesFullName }}'s</span>
                    activity timeline from
                    <span class="font-semibold">{{
                      this.$dayjs(startDate).format('D MMMM, YYYY')
                    }}</span>
                    to
                    <span class="font-semibold">{{
                      this.$dayjs(endDate).format('D MMMM, YYYY')
                    }}</span>
                  </p>
                  <p class="mb-0">
                    [ Total Distance Travelled -
                    <span class="font-semibold">
                      {{ totalDistance?.toFixed(2) || 0 }} km(approx) </span
                    >]
                  </p>
                  <div
                    class="bg-gray-200 dark-bg-custom mt-2 flex justify-between"
                    v-if="stats && !isTimelineLoad"
                  >
                    <div class="items-center flex flex-col p-2">
                      <p class="mb-0 text-blue-500 dark-text-color font-bold">Total</p>
                      <p class="mb-0 mt-1 text-blue-500">
                        {{ stats && stats.total }}
                      </p>
                    </div>
                    <div class="items-center flex flex-col p-2">
                      <p class="mb-0 text-orange-500 dark-text-color font-bold">Inprogress</p>
                      <p class="mb-0 mt-1 text-orange-500">
                        {{ stats.total - stats.completed }}
                      </p>
                    </div>
                    <div class="items-center flex flex-col p-2">
                      <p class="mb-0 text-green-500 dark-text-color font-bold">Completed</p>
                      <p class="mb-0 mt-1 text-green-500">
                        {{ stats.completed }}
                      </p>
                    </div>
                  </div>
                  <hr class="mt-2" />
                  <v-row class="flex justify-between mt-4 mb-4 gap-4 ml-1">
                    <v-select
                      v-model="localDateRangeType"
                      :items="dateRangeTypes"
                      label="Date Range Type"
                      hide-details
                      solo
                      flat
                      outlined
                      dense
                      :disabled="!selectedUserId"
                      @change="updateDateRangeType"
                    ></v-select>

                    <v-menu
                      v-model="menu"
                      :close-on-content-click="false"
                      transition="scale-transition"
                      offset-y
                      class="checkBorder_a"
                      elevation="0"
                    >
                      <template v-slot:activator="{ on }">
                        <v-text-field
                          label="Select Date Range"
                          placeholder="Select Date Range"
                          v-model="formattedMonth"
                          prepend-inner-icon="mdi-calendar"
                          readonly
                          hide-details
                          solo
                          flat
                          outlined
                          dense
                          class="checkBorder"
                          clearable
                          @click:clear="clearDate"
                          v-on="on"
                          :disabled="!selectedUserId"
                        ></v-text-field>
                      </template>

                      <v-date-picker
                        v-if="localDateRangeType === 'Monthly'"
                        v-model="selectedDate"
                        @input="emitDateSelected"
                        :min="minDate"
                        :max="maxDate"
                        type="month"
                        scrollable
                      />

                      <v-date-picker
                        v-show="localDateRangeType === 'Custom Date'"
                        v-model="customDates"
                        @input="emitCustomDateSelected"
                        :min="minDate"
                        :max="maxDate"
                        range
                        scrollable
                        type="date"
                      ></v-date-picker>
                    </v-menu>
                  </v-row>
                  <hr />
                </div>
                <v-btn
                  icon
                  @click="closeReporteeDialog"
                  :color="$vuetify.theme.currentTheme.primary"
                >
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>
            </div>
            <TimeLineNew
              :selectedUserId="selectedUserId"
              :selectedDate="selectedDate"
              :reporteesName="reporteesFullName"
              :selectedReportee="selectedReportee"
              :startDate="startDate"
              :endDate="endDate"
              :isTimelineLoad="isTimelineLoad"
              @statsData="handleStatsData"
              v-if="selectedUserId"
            />
            <v-container class="bg-white dark-bg-custom" style="height: 600px" v-else>
              <v-row class="fill-height" align-content="center" justify="center">
                <v-col class="text-subtitle-1 text-center" cols="12">
                  Gathering Information...
                </v-col>
                <v-col cols="6">
                  <v-progress-linear
                    color="black"
                    indeterminate
                    rounded
                    height="6"
                  ></v-progress-linear>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </div>
      </v-dialog>
      <EditAttendancePoint
        :dialog.sync="dialog"
        :time="currentTime"
        :column="currentColumn"
        @save="handleSave"
      />
      <ExportToExcel
        :items="items"
        :startDate="startDate"
        :endDate="endDate"
        :currentMonthDates="currentMonthDates"
        :selectedColumns="selectedColumns"
        :stateFetch="stateFetch"
        @cancel="refetch"
      />
    </div>
  </div>
</template>

<script>
import { fullName, isModuleFeatureAllowed } from '@/utils/common'
import ExportToExcel from './ExportToExcel'
import { attendanceColor } from '@/utils/workforce/statusColors'
import EditAttendancePoint from './EditAttendancePoint'
import EmployeeInfo from '@/components/EmployeeInfo.vue'
import TimeLineNew from './TimeLineNew.vue'
import ErrorAttendance from './ErrorAttendance.vue'

export default {
  name: 'AttendancePage',
  components: {
    TimeLineNew,
    ExportToExcel,
    EditAttendancePoint,
    EmployeeInfo,
    ErrorAttendance
  },
  data() {
    return {
      items: [],
      loadTable: true,
      editingCell: null,
      openMenu: null,
      selectedReportee: '',
      isReporteeDialogOpen: false,
      attendanceOptions: {
        PRESENT: 'P',
        ABSENT: 'A',
        PUBLIC_HOLIDAY: 'PH',
        WEEK_OFF: 'WO',
        LEAVE: 'L',
        MISS_PUNCH: 'MP'
      },
      isDialogOpen: false,
      dialogImage: '',
      startDate: null,
      endDate: null,
      locationHistory: [],
      loading: false,
      leaveRequestData: {},
      cachedData: [],
      dialog: false,
      currentTime: null,
      currentDate: '',
      currentColumn: '',
      selectedLocationData: [],
      locationFetchState: false,
      stateFetch: false,
      selectedItem: null,
      isMount: false,
      today: this.$dayjs().format('DD MMM'),
      locationRange: [],
      totalDistance: 0,
      localDateRangeType: this.dateRangeType,
      selecteEmployee: null,
      isTimelineLoad: false,
      isLoadTableData: false,
      stats: null,
      isVerificationOpen: false,
      dialogAttendanceData: [],
      editData: {},
      selectedErrorDate: null,
      parallelCallDateRange: 10
    }
  },
  props: {
    teamData: Array,
    selectedColumns: Array,
    orgConfig: Object,
    // locationHistory: Array,
    startOfMonth: String,
    endOfMonth: String,
    customDates: Array,
    dateRangeType: String,
    userIds: Array,
    dateRangeTypes: Array,
    formattedMonth: String,
    selectedDate: String,
    minDate: String,
    maxDate: String,
    menu: Boolean,
    apiLoading: Boolean
  },
  watch: {
    dateRangeType: {
      handler(newVal) {
        this.localDateRangeType = newVal
        this.updateDateRange()
      },
      immediate: true
    },
    customDates: {
      handler() {
        this.updateDateRange()
      },
      immediate: true
    },
    startOfMonth: {
      handler() {
        this.updateDateRange()
      },
      immediate: true
    },
    endOfMonth: {
      handler() {
        this.updateDateRange()
      },
      immediate: true
    },
    teamData: {
      async handler(newVal) {
        if (newVal) {
          this.isLoadTableData = true
          await this.fetchLocationByRange()
          this.items = this.formatTeamData(newVal)
          this.isLoadTableData = false
        }
      },
      deep: true
    },
    selectedColumns: {
      async handler(newVal) {
        if (newVal.includes('Distance (km)')) {
          this.isLoadTableData = true
          await this.fetchLocationByRange()
          this.items = this.formatTeamData(this.teamData)
          this.isLoadTableData = false
        }
      }
    },
    apiLoading: {
      handler(v) {
        this.isLoadTableData = v
      }
    }
  },
  computed: {
    reporteesFullName() {
      if (this.selecteEmployee && this.selecteEmployee?.name) {
        return this.selecteEmployee?.name
      }
      return ''
    },
    selectedUserId() {
      return this.selectedReportee?.length ? this.selectedReportee[0]?.userId._id : null
    },
    currentMonthDates() {
      if (this.dateRangeType === 'Monthly') {
        return this.getCurrentMonthDates()
      }
      if (this.dateRangeType === 'Custom Date' && this.customDates.length === 2) {
        return this.getCustomDates()
      }
      return []
    },
    renderTable() {
      return this.dateRangeType === 'Custom Date' && this.customDates.length !== 2
    }
  },
  mounted() {
    this.isMount = true
    if (this.$props?.teamData.length > 0) {
      this.items = this.formatTeamData(this.$props?.teamData)
    }
    //  else {
    //   this.fetchLocationByRange();
    // }
  },
  methods: {
    handleStatsData(data) {
      this.stats = data
    },
    updateDateRangeType() {
      this.$emit('update-date-range-type', this.localDateRangeType)
    },
    clearDate() {
      this.$emit('onDateCleared')
      this.$emit('filter')
    },
    async emitDateSelected(dates) {
      this.selectedDate = dates
      this.$emit('onDateSelects', dates)
      const selectedMonth = this.$dayjs(dates).month()
      const currentMonth = this.$dayjs().month()
      const selectedYear = this.$dayjs(dates).year()
      const currentYear = this.$dayjs().year()

      this.startDate = this.$dayjs(dates).startOf('month').format('YYYY-MM-DD')

      if (selectedMonth === currentMonth && selectedYear === currentYear) {
        this.endDate = this.$dayjs().format('YYYY-MM-DD')
      } else {
        this.endDate = this.$dayjs(dates).endOf('month').format('YYYY-MM-DD')
      }

      if (this.startDate && this.endDate) {
        const reportee = this.selecteEmployee
        this.isTimelineLoad = true
        if (reportee) {
          this.openReporteeDialog(reportee)
        }
      }
    },
    async emitCustomDateSelected(dates) {
      if (dates?.length === 2) {
        this.$emit('onCustomDateSelects', dates)
        this.customDates = dates
        this.startDate = dates[0]
        this.endDate = dates[1]
        const reportee = this.selecteEmployee
        this.isTimelineLoad = true
        if (reportee) {
          this.openReporteeDialog(reportee)
        }
      }
    },
    hasAttendenceData(attendance) {
      for (let date in attendance) {
        if (attendance[date]['First Clock-In'] !== '-') {
          return true
        }
      }
      return false
    },
    refetch() {
      this.$emit('cancel')
    },
    scrollToCurrentDate() {
      this.$nextTick(() => {
        const dataTable = this.$refs.dataTable
        let currentDate = this.$dayjs().format('DD MMM')
        if (dataTable) {
          const leftWidth = this.$el.querySelector(`th[data-date="${currentDate}"]`)
          const scrollableElement = this.$el.querySelector('.v-data-table__wrapper')
          if (!this.isMount && scrollableElement) {
            scrollableElement.scrollLeft = 0
          }
          if (scrollableElement && leftWidth) {
            scrollableElement.scrollLeft = leftWidth.offsetLeft - 200
          }
        }
        this.isMount = false
      })
    },
    isAttendanceStausLeave(item, date, column) {
      return item.attendance[date][column] === 'LEAVE'
    },
    generateCellIdentifier(item, date, column) {
      return `${item.employeeId}-${date}-${column}`
    },
    openAttendanceDialog(data, date) {
      this.editData = data
      this.selectedErrorDate = date
      this.dialogAttendanceData = data.attendance[date]?.checkInOutData
      this.isVerificationOpen = true
    },
    handleMenuOpen(item, date, column, val) {
      this.openMenu = val ? this.generateCellIdentifier(item, date, column) : null
    },
    getAttendanceOrImage(item, date, column) {
      if (item.attendance[date] && item.attendance[date][column]) {
        return item.attendance[date][column]
      } else if (item.attendance[date]?.clockInImage || item.attendance[date]?.clockOutImage) {
        return ''
      } else {
        return '-'
      }
    },
    getCurrentMonthDates() {
      const startOfMonth = this.$dayjs(this.startOfMonth)
      const endOfMonth = this.$dayjs(this.endOfMonth)
      const dates = []
      for (
        let date = startOfMonth;
        date.isBefore(endOfMonth) || date.isSame(endOfMonth);
        date = date.add(1, 'day')
      ) {
        dates.push(date.format('DD MMM'))
      }
      return dates
    },
    getCustomDates() {
      const startDate = this.$dayjs(this.customDates[0])
      const endDate = this.$dayjs(this.customDates[1])
      const dates = []
      for (
        let date = startDate;
        date.isBefore(endDate) || date.isSame(endDate);
        date = date.add(1, 'day')
      ) {
        dates.push(date.format('DD MMM'))
      }
      return dates
    },
    updateDateRange() {
      if (this.dateRangeType === 'Monthly') {
        this.startDate = this.startOfMonth
        this.endDate = this.endOfMonth
      } else if (this.dateRangeType === 'Custom Date' && this.customDates.length === 2) {
        this.startDate = this.customDates[0]
        this.endDate = this.customDates[1]
      }
    },
    formatTeamData(data) {
      const currentMonthDates = this.currentMonthDates
      const employeeMap = {}
      data.forEach(async (employee) => {
        const userId = employee?.userId?._id
        const locationData = this.locationHistory?.find((el) => el.userId?._id === userId)
        if (!employeeMap[userId]) {
          const attendance = {}
          currentMonthDates.forEach((date) => {
            attendance[date] = {
              Attendance: '-',
              'First Clock-In': '-',
              'Last Clock-In': '-',
              'Last Clock-Out': '-',
              'Working Hours': '-',
              Status: '-',
              Overtime: '-',
              'Clock-In Image': '',
              'Clock-Out Image': '',
              'Delay In Shift': '',
              'Breaks Count': ''
            }
          })
          employeeMap[userId] = {
            employeeId: employee.userId?._id,
            role: employee.userId?.wfmRole?.role || '',
            designation: employee.userId?.designation || '',
            city: employee.userId?.city || '',
            orgEmployeeId: employee.userId?.employeeId,
            name: `${fullName(employee.userId)}`,
            mobile: `${employee?.userId?.mobile}`,
            reporteeName: `${fullName(employee.userId?.reportesTo)}`,
            profileImage: employee.profileImage,
            attendance,
            locationData,
            images: []
          }
        }
        if (employee.checkInOutData.length > 0) {
          employee.checkInOutData.forEach(() => {
            const date = this.$dayjs(employee.attendanceDateUTC).format('DD MMM')
            const status = employee.attendanceStatus
            const attendanceId = employee?._id || null
            const {
              firstClockInTime,
              lastClockInTime,
              lastClockOutTime,
              images,
              clockInImage,
              clockOutImage,
              delayInShift,
              clockInCount,
              currentStatus
            } = this.calculateHoursWorked(employee.checkInOutData)

            const dailyStatus = this.calculateStatus(employee.actualWorkingHrs)
            const overTime = this.overTimeDuration(employee.actualWorkingHrs)
            const isWorking = this.isNonWorkingDay(date)?.isWeekend
            employeeMap[userId].attendance[date]['checkInOutData'] = employee.checkInOutData

            const [hours, minutes, seconds] = employee.actualWorkingHrs.split(':')
            const formattedWorkingHours = `${hours}h ${minutes}min ${seconds}sec`

            if (firstClockInTime) {
              employeeMap[userId].attendance[date]['First Clock-In'] =
                this.$dayjs(firstClockInTime).format('hh:mm A') || ''
            }
            if (lastClockInTime) {
              employeeMap[userId].attendance[date]['Last Clock-In'] =
                this.$dayjs(lastClockInTime).format('hh:mm A') || ''
            }
            if (lastClockOutTime) {
              employeeMap[userId].attendance[date]['Last Clock-Out'] =
                this.$dayjs(lastClockOutTime).format('hh:mm A') || ''
            }
            if (status) {
              employeeMap[userId].attendance[date]['Attendance'] = status
            }
            if (employee.actualWorkingHrs) {
              employeeMap[userId].attendance[date]['Working Hours'] = formattedWorkingHours
            }
            if (attendanceId) {
              employeeMap[userId].attendance[date]['attendanceId'] = attendanceId
            }
            if (clockInImage) {
              employeeMap[userId].attendance[date]['clockInImage'] = clockInImage
            }
            if (clockOutImage) {
              employeeMap[userId].attendance[date]['clockOutImage'] = clockOutImage
            }
            if (dailyStatus) {
              employeeMap[userId].attendance[date]['Status'] = dailyStatus
            }
            if (overTime) {
              employeeMap[userId].attendance[date]['Overtime'] = isWorking
                ? formattedWorkingHours
                : overTime
            }
            if (delayInShift) {
              employeeMap[userId].attendance[date]['Delay In Shift'] = delayInShift
            }
            if (clockInCount >= 0) {
              employeeMap[userId].attendance[date]['Breaks Count'] = clockInCount - 1 || 'No break'
            }
            if (currentStatus) {
              employeeMap[userId].attendance[date]['CurrentStatus'] = currentStatus || ''
            }
            employeeMap[userId].images.push(...images)
          })
        } else if (employee.attendanceStatus === 'LEAVE') {
          const date = this.$dayjs(employee.attendanceDate).format('DD MMM')
          employeeMap[userId].attendance[date]['Attendance'] = employee.attendanceStatus

          employeeMap[userId].attendance[date]['Leave'] = this.leaveRequestData
          employeeMap[userId].attendance[date]['LeaveReqId'] = employee.leaveRequest?._id
        } else {
          const date = this.$dayjs(employee.attendanceDateUTC).format('DD MMM')
          const attendanceId = employee?._id || null
          if (attendanceId) {
            employeeMap[userId].attendance[date]['attendanceId'] = attendanceId
          }
          if (employee?.attendanceStatus) {
            if (
              employeeMap[userId].attendance[date]['Attendance'] !== 'PRESENT' &&
              employeeMap[userId].attendance[date]['Attendance'] !== 'ABSENT'
            ) {
              employeeMap[userId].attendance[date]['Attendance'] = employee?.attendanceStatus
            }
          }
        }
      })
      // console.log(employeeMap);
      const formattedData = Object.values(employeeMap)
      this.loadTable = false
      this.scrollToCurrentDate()

      let locationHistoryMap = new Map()
      let locationDataMap = new Map()

      this.locationRange.forEach((history) => {
        const userId = history.userId?._id
        const travelDate = this.$dayjs(history.travelDate).format('DD MMM')

        if (!locationHistoryMap.has(userId)) {
          locationHistoryMap.set(userId, {})
        }
        if (!locationDataMap.has(userId)) {
          locationDataMap.set(userId, {})
        }

        locationHistoryMap.get(userId)[travelDate] = history.dailyDistanceTraveled
        locationDataMap.get(userId)[travelDate] = history.locationData
      })

      formattedData.forEach(({ employeeId, attendance }) => {
        Object.entries(attendance).forEach(([date, attendanceRecord]) => {
          const distanceData = locationHistoryMap.get(employeeId)?.[date]
          const locationData = locationDataMap.get(employeeId)?.[date] || []

          if (!distanceData?.distance) {
            attendanceRecord['Distance (km)'] = '-'
            return
          }

          attendanceRecord['Distance (km)'] = Number(distanceData.distance).toFixed(2)
          attendanceRecord['pairDistance'] = locationData.map((data, index) => {
            const nextLocation = locationData[index + 1]

            const originCaseNo = data.activity?.caseTaskDetails?.case?.caseNo
            const customerName =
              data.activity?.caseTaskDetails?.case?.customer?.customerFullName ||
              data.activity?.taskDetails?.leadDetails?.company?.name
            const originAction = `${data.activity?.action || 'N/A'} at (${this.$dayjs(
              data?.activity?.time
            ).format('hh:mm A')})${originCaseNo ? ` for (${originCaseNo})` : ''}`

            const destinationCaseNo = nextLocation?.activity?.caseTaskDetails?.case?.caseNo
            const destinationAction = nextLocation
              ? `${nextLocation.activity?.action || 'N/A'} at (${this.$dayjs(
                  nextLocation.activity?.time
                ).format('hh:mm A')})${destinationCaseNo ? ` for (${destinationCaseNo})` : ''}`
              : ''

            return {
              ...distanceData.pairDistance?.[index],
              customerName,
              originAction,
              destinationAction
            }
          })
        })
      })

      return formattedData
    },
    calculateHoursWorked(checkInOutData) {
      let firstClockIn,
        lastClockIn,
        lastClockOut,
        images = [],
        delayInShift
      let clockInCount = 0
      checkInOutData.forEach((record) => {
        if (record.status === 'CHECKED_IN') {
          clockInCount++
          if (!firstClockIn) {
            firstClockIn = record
          }
          lastClockIn = record
        } else if (record.status === 'CHECKED_OUT') {
          lastClockOut = record
        }

        if (record.selfieUrl) {
          images.push({
            image: `${process.env.VUE_APP_S3_BASE_URL}${record.selfieUrl}`,
            status: record.status,
            time: record?.time
          })
        }
      })

      const clockInImage = firstClockIn?.selfieUrl
        ? `${process.env.VUE_APP_S3_BASE_URL}${firstClockIn?.selfieUrl}`
        : ''
      const clockOutImage = lastClockOut?.selfieUrl
        ? `${process.env.VUE_APP_S3_BASE_URL}${lastClockOut?.selfieUrl}`
        : ''

      const clockInTimeFormat = this.$dayjs(firstClockIn?.time).format('HH:mm')
      const shiftStartTime = this.$dayjs(this.$props.orgConfig?.shiftStartTime, 'HH:mm')
      const firstClockInTime = this.$dayjs(clockInTimeFormat, 'HH:mm')
      const diffHours = firstClockInTime.diff(shiftStartTime, 'hour')
      const diffMinutes = firstClockInTime.diff(shiftStartTime, 'minute') % 60

      if (diffHours > 0 || diffMinutes > 0) {
        delayInShift = `${diffHours}h ${diffMinutes}min`
      } else {
        delayInShift = '-'
      }
      const currentStatus = checkInOutData[checkInOutData.length - 1]?.status
      return {
        firstClockInTime: firstClockIn?.time,
        lastClockInTime: lastClockIn?.time,
        lastClockOutTime: lastClockOut?.time,
        images,
        clockInImage,
        clockOutImage,
        delayInShift,
        clockInCount,
        currentStatus
      }
    },
    async openReporteeDialog(reportee) {
      console.log({ reportee })
      this.selecteEmployee = reportee
      if (
        !isModuleFeatureAllowed('ATTENDANCE', 'DISTANCE_CALCULATION') ||
        !this.$props.orgConfig?.isTrackEmployeeActions
      ) {
        return
      }
      const reporteeId = reportee?.employeeId
      this.isReporteeDialogOpen = true
      await this.fetchLocationData(reporteeId)
      this.selectedReportee = this.locationHistory.filter((team) => team.userId?._id === reporteeId)

      if (this.selectedReportee.length) {
        let totalDistance = 0
        this.selectedReportee.forEach((el) => {
          const travelDate = this.$dayjs(el.travelDate).format('DD-MM-YYYY')
          const matchedImages = reportee.images.filter(
            (image) => this.$dayjs(image.time).format('DD-MM-YYYY') === travelDate
          )
          const date = this.$dayjs(el.travelDate).format('DD MMM')
          const errorEntry = reportee.attendance[date]?.checkInOutData
          const isAnyError = this.isAnyErrorInAttendance(errorEntry)
          if (isAnyError) {
            el.error = errorEntry
          }
          el.images = matchedImages
          if (el.dailyDistanceTraveled?.distance) {
            totalDistance += Number(el.dailyDistanceTraveled?.distance)
            this.totalDistance = totalDistance
          }
        })
      } else {
        this.$toast.error('No clock-in records found')
      }
      console.log(this.selectedReportee)
      this.isTimelineLoad = false
    },
    async fetchLocationByRange() {
      try {
        if (!this.selectedColumns.includes('Distance (km)')) {
          return
        }

        if (!this.startDate || !this.endDate) {
          this.$toast.error('Please select a start date')
          return
        }

        if (!this.userIds) {
          return
        }

        let paramsArray = []
        const userIdChunks = []
        const batchSize = 5

        for (let i = 0; i < this.userIds.length; i += 10) {
          userIdChunks.push(this.userIds.slice(i, i + 10).join(','))
        }

        if (this.userIds.length <= 10) {
          paramsArray.push({
            start: this.startDate,
            end: this.endDate,
            userIds: this.userIds.join(',')
          })
        } else {
          const daysInMonth = this.$dayjs(this.endDate).format('DD')

          for (let start = 1; start <= daysInMonth; start += this.parallelCallDateRange) {
            const end = Math.min(start + (this.parallelCallDateRange - 1), daysInMonth)
            userIdChunks.forEach((userIds) => {
              paramsArray.push({
                start: this.$dayjs(this.startDate).date(start).format('YYYY-MM-DD'),
                end: this.$dayjs(this.endDate).date(end).format('YYYY-MM-DD'),
                userIds
              })
            })
          }
        }

        this.stateFetch = true
        const results = []

        for (let i = 0; i < paramsArray.length; i += batchSize) {
          const batch = paramsArray.slice(i, i + batchSize)
          const requests = batch.map((params) =>
            this.$axios.get(`/workforce/user/v2/location`, { params })
          )
          const responses = await Promise.all(requests)
          results.push(...responses.flatMap((response) => response.data?.locationHistory || []))
        }

        this.locationRange = results
        this.stateFetch = false
      } catch (error) {
        console.log(error)
        this.stateFetch = false
      }
    },
    async fetchLocationData(reporteeId) {
      try {
        let params = {}
        if (this.startDate && this.endDate) {
          params = {
            start: this.startDate,
            end: this.endDate
          }
        } else {
          this.$toast.error('Please select a start date')
        }
        const response = await this.$axios.get(`/workforce/user/v2/location/${reporteeId}`, {
          params
        })
        this.locationHistory = response.data?.locationHistory
      } catch (error) {
        console.log(error)
      }
    },
    closeReporteeDialog() {
      this.selectedReportee = null
      this.isReporteeDialogOpen = false
    },
    isEditing(name, date, column) {
      return (
        this.editingCell &&
        this.editingCell.name === name &&
        this.editingCell.date === date &&
        this.editingCell.column === column
      )
    },
    dayOfMonth(date) {
      const dateD = this.$dayjs(date, 'DD MMM')

      const dayOfWeek = dateD.format('ddd')

      return dayOfWeek
    },
    isNonWorkingDay(date) {
      const dayjs = require('dayjs')
      const isBetween = require('dayjs/plugin/isBetween')
      dayjs.extend(isBetween)
      date = date + this.$dayjs(this.startDate).format('YYYY')
      const holidays = this.$props.orgConfig?.listOfHolidays || []
      const isHoliday = (date) => {
        const dateToCheck = dayjs(date).startOf('day')
        const holiday = holidays.find((holiday) => {
          const startDate = dayjs(holiday.holidayStartDate).startOf('day')
          const endDate = dayjs(holiday.holidayEndDate).endOf('day')
          return (
            dateToCheck.isSame(startDate) || dateToCheck.isBetween(startDate, endDate, null, '[]')
          )
        })
        return {
          isHoliday: !!holiday,
          holidayName: holiday?.holidayName
        }
      }
      const isWeekend = (date) => {
        const dayOfWeek = dayjs(date).format('dddd').toUpperCase()
        return !this.$props.orgConfig?.workingDays.includes(dayOfWeek)
      }
      return {
        isHoliday: isHoliday(date).isHoliday,
        holidayName: isHoliday(date).holidayName,
        isWeekend: isWeekend(date)
      }
    },
    leftBorder(today, date, selectedColumns, column) {
      return today === date && selectedColumns[0] === column
    },
    rightBorder(today, date, selectedColumns, column) {
      return today === date && selectedColumns[selectedColumns.length - 1] === column
    },
    editAttendance(name, date, column) {
      this.editingCell = { name, date, column }
    },
    async saveAttendance(item, date, column, selectedOption) {
      const attendance = item.attendance[date]
      const attendanceId = attendance?.attendanceId
      const payload = {
        attendanceStatus: selectedOption
      }

      const attendanceDate = date + this.$dayjs(this.startDate).format('YYYY')

      const postPayload = {
        ...payload,
        attendanceDate: this.$dayjs(attendanceDate).format('YYYY-MM-DD'),
        userId: item.employeeId
      }

      try {
        await (attendanceId
          ? this.$axios.put(`/workforce/user/v2/attendance/${attendanceId}`, payload)
          : this.$axios.post(`/workforce/user/v2/attendance`, postPayload))

        this.$toast.success(
          `Attendance Status ${attendanceId ? 'updated' : 'created'} successfully`
        )
        this.openMenu = null
        this.editData = {}
        item.attendance[date][column] = selectedOption
        this.isVerificationOpen = false
      } catch (error) {
        console.error(`Failed to ${attendanceId ? 'update' : 'create'} attendance:`, error)
        const errorMessage = error?.response?.data?.message || error
        this.$toast.error(errorMessage)
      } finally {
        this.editingCell = null
      }
    },
    async fetchLeaveRequests(requestId) {
      if (!requestId) return

      const isDataCached = this.cachedData.filter((el) => el.leaveId === requestId)

      if (isDataCached.length) {
        this.leaveRequestId = isDataCached[0]
        return
      }

      try {
        const response = await this.$axios.get('/workforce/emp/leave/requests', {
          params: { _id: requestId }
        })

        const data = response.data?.allEmployeeLeaveRequests[0]
        this.leaveRequestData = {
          leaveType: data?.typeOfLeaveApplied || '',
          leaveStatus: data?.leaveStatus || '',
          comments: data?.comments || '',
          approvedBy: fullName(data.approvals[0]?.approvedBy) || '',
          leaveId: data._id
        }

        this.cachedData.push(this.leaveRequestData)
      } catch (error) {
        console.error('Error fetching leave requests:', error)
      } finally {
        this.loading = false
      }
    },
    openDialog(imageUrl) {
      this.dialogImage = imageUrl
      this.isDialogOpen = true
    },
    calculateStatus(item) {
      if (item) {
        const [hour, minute, second] = item.split(':').map(Number)
        const actualHours = (hour * 60 + minute + second / 60).toFixed(2)
        const workingHours = this.$props.orgConfig?.workingHours * 60
        const workHourThreshold = this.$props.orgConfig?.fullDayThreshold || 100
        const orgWorkingHours = workingHours * (workHourThreshold / 100)
        const halfActualHours = (orgWorkingHours * this.$props.orgConfig?.halfDayThreshold) / 100
        const overTimeThresholdTime =
          workingHours + (workingHours * this.$props.orgConfig?.overTimeThreshold) / 100
        if (actualHours >= overTimeThresholdTime) {
          return 'Over Time'
        } else if (actualHours >= halfActualHours && actualHours < orgWorkingHours) {
          return 'Half Day'
        } else if (actualHours >= orgWorkingHours && actualHours < overTimeThresholdTime) {
          return 'Full Day'
        }
        return ''
      }
    },
    overTimeDuration(item) {
      if (item) {
        const [hour, minute, second] = item.split(':').map(Number)
        const actualHours = (hour * 60 + minute + second / 60).toFixed(2)
        const orgWorkingHours = this.$props.orgConfig?.workingHours * 60
        const duration = actualHours - orgWorkingHours
        const hours = Math.floor(duration / 60)
        const minutes = Math.floor(duration % 60)
        const seconds = Math.floor(((duration % 60) % 1) * 60)
        if (duration > 0) {
          return `${Math.abs(hours)}h ${minutes}min ${seconds}sec`
        }
      }
      return '-'
    },
    durationStatusColor(status) {
      if (status !== '-') {
        const colorInfo = attendanceColor[status]
        return colorInfo
      }
      return
    },
    async openTimeAddressDialog(item, date, column) {
      if (column === 'First Clock-In' || column === 'Last Clock-Out') {
        this.currentTime = item.attendance[date][column]
        this.currentColumn = column
        this.dialog = true
        this.selectedItem = item

        await this.fetchLocationData(item.employeeId)
        this.currentDate = date + this.$dayjs(this.startDate).format('YYYY')
        this.selectedLocationData = this.locationHistory.filter((el) => {
          return (
            this.$dayjs(el.travelDate).format('YYYY-MM-DD') ===
            this.$dayjs(this.currentDate).format('YYYY-MM-DD')
          )
        })
      }
    },
    async handleSave({ time, address, latitude, longitude }) {
      const dayjs = require('dayjs')
      const utc = require('dayjs/plugin/utc')
      dayjs.extend(utc)

      const newDateTime = dayjs(this.currentDate + ' ' + time)
        .utc()
        .format()

      const payload = {
        location: {
          geometry: {
            lat: latitude,
            lng: longitude
          },
          formatted_address: address,
          source: 'Manual By Admin'
        },
        activity: {
          activityType: 'CHECK_IN_OUT',
          action: this.currentColumn === 'First Clock-In' ? 'checkIn' : 'checkOut',
          time: newDateTime
        },
        deviceData: {}
      }

      if (this.selectedLocationData.length) {
        if (this.currentColumn === 'First Clock-In') {
          this.selectedLocationData[0].locationData.unshift(payload)
        } else if (this.currentColumn === 'Last Clock-Out') {
          this.selectedLocationData[0].locationData.push(payload)
        }
      }

      await this.updateClockIn(payload, this.currentDate)
      const locationId = this.selectedLocationData[0]?._id

      try {
        if (locationId) {
          const response = await this.$axios.put(`/workforce/user/v2/location/${locationId}`, {
            userId: this.selectedItem?.employeeId,
            locationData: this.selectedLocationData[0]?.locationData
          })
          const successMessage = response?.data?.message || 'Time Updated successfully'
          this.$toast.success(successMessage)
        }
      } catch (error) {
        console.log(error)
      }
    },
    async updateClockIn(data, date) {
      try {
        date = this.$dayjs(date).format('DD MMM')
        const attendance = this.selectedItem.attendance[date]
        const attendanceId = attendance?.attendanceId
        const selectedAttendanceData = this.teamData?.find((el) => el._id === attendanceId)
        const newData = {
          status: data.activity?.action === 'checkIn' ? 'CHECKED_IN' : 'CHECKED_OUT',
          time: data.activity?.time,
          location: data.location,
          deviceData: data.deviceData
        }
        if (this.currentColumn === 'First Clock-In') {
          selectedAttendanceData.checkInOutData.unshift(newData)
        } else if (this.currentColumn === 'Last Clock-Out') {
          selectedAttendanceData.checkInOutData.push(newData)
        }
        const payload = {
          checkInOutData: selectedAttendanceData.checkInOutData
        }
        await this.$axios.put(`/workforce/user/v2/attendance/${attendanceId}`, payload)
        this.$emit('updateTeamData', selectedAttendanceData)
      } catch (error) {
        console.log(error)
      }
    },
    isAnyErrorInAttendance(data) {
      if (data && data.length) {
        const isErrorAvailable = data.some((el) => el.error?.state == true)
        return isErrorAvailable
      }
      return false
    }
  }
}
</script>

<style scoped>
.sortHandle {
  cursor: move;
}

.dialogOverflow {
  overflow-y: none !important;
}

.v-data-table >>> .v-data-table__wrapper > table > tbody > tr > td > .v-data-table__checkbox {
  text-align: center;
}

.table-wrapper {
  overflow-x: auto;
}

.table-container {
  display: block;
}

.custom-data-table {
  min-width: 800px; /* Adjust as needed for horizontal scroll */
  white-space: nowrap;
  overflow: auto; /* Enable horizontal scrolling */
}

.fixed-column {
  position: sticky;
  left: 0;
  background-color: #c8dae2 !important;
  z-index: 1;
  border-right: 1px solid #ddd;
  cursor: pointer;
}

.dark .fixed-column {
  background-color: var(--bg-custom) !important;
}

.custom-data-table thead th.fixed-column {
  top: 0;
  z-index: 2;
}

.custom-data-table thead th,
.custom-data-table tbody td {
  min-width: 120px; /* Adjust as needed */
  padding: 8px;
  text-align: center !important;
  cursor: pointer;
  border: 1px solid #ddd; /* Add border to each cell */
}

custom-data-table thead th,
.custom-data-table tbody td:nth-child(1) {
  min-width: 200px;
}

.custom-data-table thead th {
  font-weight: bold;
  text-align: center;
}

.main-header {
  /* Main header background color */
  font-weight: bold;
  text-align: center !important;
  border: 1px solid #000; /* Border for main headers */
}

.sub-header {
  /* Sub-header background color same as main header */
  border: 1px solid #000; /* Border for sub-headers */
}

/* Alternating background color for even columns */

.custom-data-table tbody td:nth-child(odd) {
  background-color: #f8fdff;
}

.dark .custom-data-table tbody td:nth-child(odd) {
  background-color: var(--bg-custom);
}

.custom-data-table thead th:nth-child(even):not(.main-header) {
  background-color: #f8fdff;
}

.dark .custom-data-table thead th:nth-child(even):not(.main-header) {
  background-color: var(--bg-custom);
}

.custom-data-table thead th:nth-child(odd):not(.sub-header) {
  background-color: #f8fdff;
}

.dark .custom-data-table thead th:nth-child(odd):not(.sub-header) {
  background-color: var(--bg-custom);
}

.custom-data-table thead th:nth-child(even):not(.sub-header) {
  background-color: #c8dae2;
}

.dark .custom-data-table thead th:nth-child(even):not(.sub-header) {
  background-color: var(--bg-default);
}

.table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.custom-data-table .non-working-day {
  background-color: #cac7c771 !important;
}
.btnClass {
  position: sticky;
  top: 0%;
  padding-top: 8px;
  z-index: 11;
  background-color: white;
}

.dark .btnClass {
  background-color: var(--bg-default);
}
.selected {
  background-color: #2a83ff;
  color: #ffffff !important;
  font-weight: bold;
}

.custom-data-table tbody tr:hover td:not(.fixed-column) {
  background-color: #eeeeee !important;
}

.dark .custom-data-table tbody tr:hover td:not(.fixed-column) {
  background-color: var(--bg-hover) !important;
}

.custom-data-table tbody td.fixed-column {
  background-color: #c8dae2 !important;
}

.dark .custom-data-table tbody td.fixed-column {
  background-color: #3b3b3b !important;
}

/* For current date border hightlight */

.custom-data-table thead th:not(.main-header).rightBorder {
  border-right: 3px solid #21427d;
}

.custom-data-table thead th:not(.main-header).leftBorder {
  border-left: 3px solid #21427d;
}
.custom-data-table thead th:not(.sub-header).today {
  border-top: 3px solid #21427d;
  border-left: 3px solid #21427d;
  border-right: 3px solid #21427d;
}

.custom-data-table tbody td:not(.sub-header).rightBorder {
  border-right: 3px solid #21427d;
}

.custom-data-table tbody td:not(.sub-header).leftBorder {
  border-left: 3px solid #21427d;
}
</style>

<style>
/* this is controlling the box shadow of global menus */
.cursor-not-allowed {
  cursor: not-allowed;
  color: grey;
}
.v-menu__content {
  box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;
  /* z-index: 5 !important; */
}
</style>
