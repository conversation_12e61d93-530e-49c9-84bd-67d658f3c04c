<template>
  <div class="py-2">
    <v-row v-if="employees.length > 0" no-gutters class="flex flex-col items-center">
      <v-col class="mb-4">
        <v-data-table
          :headers="[]"
          :items="employees"
          class="elevation-1 custom-roaster-table"
          fixed-header
          hide-default-footer
          hide-default-header
        >
          <template v-slot:header>
            <thead>
              <tr>
                <th
                  v-for="(date, index) in days"
                  class="text-center border-r-2 z-1"
                  :class="
                    index === 0 && 'sticky-position-name bg-[#C8DAE2] dark-bg-custom font-bold'
                  "
                  :style="{
                    zIndex: index === 0 ? '2' : '1',
                    backgroundColor: index === 0 ? '#C8DAE2 dark-bg-custom' : '#fff dark-bg-default'
                  }"
                >
                  {{ date.text }}
                </th>
              </tr>
            </thead>
          </template>
          <template v-slot:body="{ items }">
            <tbody>
              <tr v-for="item in items" :key="item?._id" class="border-2 text-center">
                <td class="sticky-position custom-td capitalize" style="width: 100px">
                  <div class="w-48 font-bold">
                    {{ item.fullName }}
                  </div>
                </td>
                <td
                  v-for="date in item?.dates"
                  :key="date.date"
                  @drop="onDrop($event, item?.fullName, date.date, item?._id, date?.roasterId)"
                  @dragover.prevent
                  class="custom-td border-2"
                >
                  <v-list class="tableList">
                    <v-list-item-content
                      :class="{
                        'shift-present': date.shift,
                        'shift-absent': !date.shift
                      }"
                      :style="{
                        backgroundColor: date.roasterColor?.bgColor,
                        borderColor: date.roasterColor?.borderColor
                      }"
                    >
                      <div v-if="date.shift" class="pt-0 pb-0 flex flex-row-reverse">
                        <div class="flex justify-end w-5 h-5">
                          <v-icon
                            color="red"
                            class="cursor-pointer text-lg"
                            @click="handleDelete(item._id, date.roasterId)"
                            >mdi-delete</v-icon
                          >
                        </div>
                        <div>
                          <v-list-item-content class="font-bold pt-0 pb-0 roasterText">
                            {{ date.shift.shiftStartTime }} -
                            {{ date.shift.shiftEndTime }}
                          </v-list-item-content>
                          <v-list-item-content class="pt-0 pb-0 text-sm roasterText">
                            {{ date.shift.shiftTime }}
                          </v-list-item-content>
                        </div>
                      </div>
                      <div v-else>
                        <v-icon>mdi-plus</v-icon>
                      </div>
                    </v-list-item-content>
                  </v-list>
                </td>
              </tr>
            </tbody>
          </template>
        </v-data-table>
      </v-col>
      <v-pagination
        v-model="currentPage"
        :length="totalPages"
        total-visible="5"
        :color="$vuetify.theme.currentTheme.primary"
      ></v-pagination>
    </v-row>
    <div v-else class="w-full flex flex-col items-center justify-center">
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
    <v-navigation-drawer
      width="18%"
      v-model="roasterDrawer"
      right
      fixed
      class="p-4"
      v-if="roasterDrawer"
    >
      <ShiftDrawer :shiftTimes="shiftTimes" @handleClose="handleClose" />
    </v-navigation-drawer>
    <ExportShiftData :dates="formattedDates" :employees="totalEmployeeData" />
  </div>
</template>

<script>
import ShiftDrawer from '@/components/Workforce/AttendanceManagement/ShiftDrawer.vue'
import ExportShiftData from './ExportShiftData.vue'
import { debounce } from '@/utils/common'
import { roasterColor } from '@/utils/workforce/statusColors'

export default {
  data() {
    return {
      userData: this.$storage.getUniversal('user'),
      menu: false,
      drawer: false,
      headers: [],
      employees: [],
      shiftTimes: [],
      draggedShift: null,
      orgShift: [],
      roasters: [],
      updatedEmployees: [],
      days: [],
      currentPage: 1,
      totalPages: null,
      formattedDates: [],
      totalEmployeeData: [],
      isTableLoad: false
    }
  },
  components: {
    ShiftDrawer,
    ExportShiftData
  },
  props: {
    orgConfig: Object,
    teamData: Array,
    startOfMonth: String,
    endOfMonth: String,
    customDates: Array,
    dateRangeType: String,
    roasterDrawer: Boolean,
    search: String
  },
  watch: {
    '$route.query.search': {
      handler(newVal) {
        try {
          this.handleInput(newVal)
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },
    currentPage: {
      handler(newPage) {
        if (newPage) {
          this.fetchAllData(this.$props.search)
        }
      },
      immediate: true
    },
    customDates: {
      async handler(newDate) {
        if (newDate.length === 2) {
          this.updateDateRange()
        }
      },
      immediate: true
    },
    endOfMonth: {
      async handler(newVal) {
        if (newVal) {
          this.updateDateRange()
        }
      },
      immediate: true
    },
    search: {
      handler(newVal) {
        this.handleInput(newVal)
      }
    }
  },
  async mounted() {
    this.isTableLoad = true
    this.shiftTimes = this.$props.orgConfig.orgShifts
    this.$root.$on('export-data', this.exportAllEmployee)
    this.shiftsWithColor()
  },
  beforeDestroy() {
    this.$root.$off('export-data', this.exportAllEmployee)
  },
  methods: {
    shiftsWithColor() {
      this.shiftTimes = this.shiftTimes.map((shift, index) => {
        const colorIndex = (index % Object.keys(roasterColor).length) + 1
        const colorKey = `color${colorIndex}`
        return {
          ...shift,
          roasterColor: roasterColor[colorKey]
        }
      })
    },

    async updateDateRange() {
      if (this.$props.dateRangeType === 'Monthly') {
        const roaster = await this.getRoasters(this.$props.startOfMonth, this.$props.endOfMonth)

        if (roaster) {
          this.updateTableHeaders(this.$props.startOfMonth, this.$props.endOfMonth, this.employees)
        }
      } else if (this.$props.dateRangeType === 'Custom Date' && this.customDates.length === 2) {
        const roaster = await this.getRoasters(
          this.$props.customDates[0],
          this.$props.customDates[1]
        )

        if (roaster) {
          this.updateTableHeaders(
            this.$props.customDates[0],
            this.$props.customDates[1],
            this.employees
          )
        }
      }
    },
    handleInput: debounce(async function (newVal) {
      await this.fetchAllData(newVal)
    }, 800),
    handleClose(data) {
      this.$props.roasterDrawer = data
      this.$emit('handleClose')
    },
    async fetchAllData(search) {
      let employeeLimit = 10
      await this.fetchEmployees(search, employeeLimit)
      if (this.employees) {
        let startDate = ''
        let endDate = ''
        if (this.$props.dateRangeType === 'Monthly') {
          startDate = this.$props.startOfMonth
          endDate = this.$props.endOfMonth
        } else if (this.$props.dateRangeType === 'Custom Date' && this.customDates.length === 2) {
          startDate = this.$props.customDates[0]
          endDate = this.$props.customDates[1]
        }
        const roasters = await this.getRoasters(startDate, endDate)
        if (roasters) {
          this.updateTableHeaders(startDate, endDate, this.employees)
        }
      }
    },
    async fetchEmployees(search, employeeLimit) {
      try {
        const params = {
          page: this.currentPage,
          limit: employeeLimit,
          search: search || null,
          sortBy: 'firstName',
          sortOrder: 'asc'
        }
        const allUser = {
          limit: '*',
          page: 1
        }
        if (this.userData?.branch && !this.userData?.role?.includes('WFM_ADMIN')) {
          params.branch = this.userData?.branch
        }

        const response = await this.$axios.get('workforce/v2/users', {
          params: employeeLimit ? params : allUser
        })

        if (response.data.success) {
          if (employeeLimit) {
            this.employees = response.data.users
            this.currentPage = response.data.pagination.currentPage
            this.totalPages = response.data.pagination.totalPages
          }
        }

        return response.data.users
      } catch (error) {
        console.error('Error fetching employees:', error)
      }
    },
    toggleDrawer() {
      this.$props.roasterDrawer = !this.$props.roasterDrawer
    },
    updateTableHeaders(startDate, endDate, employees, type = 'tableView') {
      const start = this.$dayjs(startDate)
      const end = this.$dayjs(endDate)
      const dates = []
      const shiftDates = []
      for (let date = start; date.isBefore(end) || date.isSame(end); date = date.add(1, 'day')) {
        dates.push(date.format('DD MMM'))
        shiftDates.push(date.format('YYYY-MM-DD'))
      }

      const headers = [{ text: 'Name', value: 'name', align: 'start' }]
      dates.forEach((day) => {
        const date = day
        headers.push({ text: date, value: `${day}`, class: 'custom-header' })
      })

      this.days = headers
      this.formattedDates = dates
      this.updateItemsWithShifts(shiftDates, employees, type)
    },
    updateItemsWithShifts(datesData, employees, type) {
      const employeeData = employees.map((employee) => {
        const dates = []
        datesData.forEach((day) => {
          let currentDate = day
          const shift = this.roasters.find(
            (shift) =>
              shift.userId === employee._id && shift.shiftDate.toString().startsWith(currentDate)
          )
          let roastColorData = null
          if (shift) {
            roastColorData = this.shiftTimes.find(
              (shiftData) => shiftData.shiftName === shift.shiftDetails.name
            )
          }
          dates.push({
            date: `${day}`,
            shift: shift
              ? {
                  shiftTime: shift.shiftDetails.name,
                  shiftStartTime: shift.shiftDetails.startTime,
                  shiftEndTime: shift.shiftDetails.endTime
                }
              : '',
            roasterId: shift ? shift._id : '',
            roasterColor: roastColorData?.roasterColor
          })
        })
        return { ...employee, dates }
      })

      if (type === 'export') {
        this.totalEmployeeData = employeeData
      } else if (type === 'tableView') {
        this.employees = employeeData
      }
      this.isTableLoad = false
    },
    async handleDelete(empId, roasterId) {
      try {
        const response = await this.$axios.delete(`/workforce/org/roster/shift/${roasterId}`)
        if (response.data.success) {
          this.$toast.success(response?.data?.message)
          this.employees = this.employees.map((employee) => {
            employee.dates = employee.dates.map((date) => {
              if (date.roasterId === roasterId) {
                date.roasterId = ''
                date.shift = ''
                date.roasterColor = {}
              }
              return date
            })
            return employee
          })
        }
      } catch (error) {
        console.log(error)
      }
    },
    onDragStart(event, shiftTime) {
      event.dataTransfer.setData('text', shiftTime)
    },
    async onDrop(event, employeeName, date, id, roasterDetailId) {
      const shiftTime = event.dataTransfer.getData('format')
      const shiftStartTime = event.dataTransfer.getData('startTime')
      const shiftEndTime = event.dataTransfer.getData('endTime')
      const roasterColorBg = event.dataTransfer.getData('roasterColorBg')
      const roasterColorBorder = event.dataTransfer.getData('roasterColorBorder')

      const employeeIndex = this.employees.findIndex((item) => item.fullName === employeeName)
      let dateIndex
      if (employeeIndex !== -1) {
        dateIndex = this.employees[employeeIndex].dates.findIndex((d) => d.date === date)
        if (dateIndex !== -1) {
          let roasterId = ''
          if (roasterDetailId) {
            roasterId = await this.editRoaster(
              shiftTime,
              shiftStartTime,
              shiftEndTime,
              date,
              roasterDetailId
            )
          } else {
            roasterId = await this.postRoaster(shiftTime, shiftStartTime, shiftEndTime, date, id)
          }

          let roasterColor = null
          if (roasterColorBg || roasterColorBorder) {
            roasterColor = {
              bgColor: roasterColorBg,
              borderColor: roasterColorBorder
            }
          }
          this.$set(this.employees[employeeIndex].dates, dateIndex, {
            ...this.employees[employeeIndex].dates[dateIndex],
            shift: { shiftTime, shiftStartTime, shiftEndTime },
            roasterId,
            roasterColor
          })
        }
      }
    },
    async getRoasters(startDate, endDate) {
      try {
        const response = await this.$axios.get(
          `/workforce/org/roster/shifts?start=${startDate}&end=${endDate}`
        )

        this.roasters = response.data.rosterShifts
        return response.data.success
      } catch (error) {
        console.log(error)
      }
    },
    async postRoaster(shiftTime, shiftStartTime, shiftEndTime, date, id) {
      try {
        const body = {
          userId: id,
          shiftDate: date,
          shiftDetails: {
            name: shiftTime,
            startTime: shiftStartTime,
            endTime: shiftEndTime
          }
        }
        const response = await this.$axios.post(`/workforce/org/roster/shift`, body)

        if (response.data.success) {
          const successMessage = response?.data?.message || 'Roaster created successfully.'
          this.$toast.success(successMessage)
          return response.data.team._id
        }
      } catch (error) {
        console.log(error)
        this.$toast.error(error?.response?.data?.message)
      }
    },
    async editRoaster(shiftTime, shiftStartTime, shiftEndTime, date, rosterShiftId) {
      try {
        const body = {
          shiftDate: date,
          shiftDetails: {
            name: shiftTime,
            startTime: shiftStartTime,
            endTime: shiftEndTime
          }
        }
        const response = await this.$axios.put(`/workforce/org/roster/shift/${rosterShiftId}`, body)
        if (response.data.success) {
          const successMessage = response?.data?.message || 'Roaster edited successfully.'
          this.$toast.success(successMessage)
          return response.data.rosterShift._id
        }
      } catch (error) {
        console.log(error)
        this.$toast.error(error?.response?.data?.message)
      }
    },
    async exportAllEmployee() {
      const employees = await this.fetchEmployees(null, null)
      if (this.$props.dateRangeType === 'Monthly') {
        await this.updateTableHeaders(
          this.$props.startOfMonth,
          this.$props.endOfMonth,
          employees,
          'export'
        )
      } else if (this.$props.dateRangeType === 'Custom Date' && this.customDates.length === 2) {
        await this.updateTableHeaders(
          this.$props.customDates[0],
          this.$props.customDates[1],
          employees,
          'export'
        )
      }
      this.$root.$emit('export-shift-excel')
    }
  }
}
</script>

<style>
.sticky-position {
  position: sticky;
  left: 0;
  background-color: #c8dae2;
  z-index: 1;
  width: 200px;
}

.dark .sticky-position {
  background-color: var(--bg-custom);
}

.sticky-position-name {
  position: sticky;
  left: 0;
  background-color: #c8dae2;
  z-index: 10;
  width: 200px;
}

.dark .sticky-position-name {
  background-color: var(--bg-custom);
}

.shift-present {
  border: 1px solid #ccc;
  border-left: 6px solid #dfb93d;
  width: 200px;
  /* padding: 10px; */
  background-color: #fff8e1;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin: 10px;
  padding: 5px;
}

.shift-absent {
  border: 1px dashed #ccc;
  width: 200px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin: 10px;
  padding: 12px;
  height: inherit;
}

.custom-td {
  width: 240px;
}

/*  */
</style>

<style scoped>
.custom-header {
  width: 100px !important;
}

.v-data-table >>> .v-data-table__wrapper > table {
  border: 2px solid rgb(232, 225, 225);
  border-radius: 10px;
  width: auto;
  overflow-x: scroll;
}

.custom-roaster-table tbody tr:hover {
  background-color: transparent !important;
}

.shift-details {
  display: flex;
  align-items: center;
}

.delete-icon {
  margin-right: 10px;
}

.shift-info {
  display: flex;
  flex-direction: column;
}

.shift-role {
  font-weight: bold;
  font-size: 16px;
}

.shift-time {
  font-size: 14px;
  color: #555;
}

.roasterText {
  font-size: 16px;
}
.tableList {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
</style>
