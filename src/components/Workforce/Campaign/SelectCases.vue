<template>
  <div class="p-3">
    <div class="relative">
      <input
        type="file"
        class="hidden"
        @change="onFileSelect"
        ref="excelFile"
      />
      <div
        @click="onExcelFileClick"
        class="flex items-center justify-center w-full h-16 bg-gray-100 cursor-pointer rounded-md"
      >
        <p class="mb-0 text-lg">Select Excel File</p>
      </div>
    </div>
    <v-divider class="my-4"></v-divider>
    <v-data-table
      v-if="cases.length"
      :headers="headers"
      :items="cases"
      fixed-header
      :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30, 50] }"
      v-model="selected"
      show-select
      item-key="caseNo"
    >
    </v-data-table>
    <div class="mt-2 flex items-center justify-between">
      <v-btn color="primary" text @click="onPrev">
        <v-icon left> mdi-arrow-left </v-icon>
        Go back
      </v-btn>
      <div>
        <v-btn text @click="onClose"> Cancel </v-btn>
        <v-btn color="primary" @click="onContinue" :loading="loading">
          Create Campaign
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script>
import readXlsxFile from "read-excel-file";
import { fullName } from "@/utils/common";

export default {
  name: "SelectCustomers",
  data() {
    return {
      authData: this.$storage.getUniversal("token"),
      fetchingCases: false,
      cases: [],
      headers: [],
      filterCustomer: "",
      options: { itemsPerPage: 5, page: 1 },
      totalItems: 0,
      selected: [],
    };
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    orgConfig: Object,
  },
  methods: {
    onFileSelect(e) {
      readXlsxFile(e.target.files[0]).then((rows) => {
        this.headers = rows[0].map((row) => {
          return {
            text: row,
            value: row,
            sortable: false,
          };
        });
        const selected = [];
        const subCases = this.orgConfig.enabledSubCases[0].subCase;
        const caseNoFieldNames = subCases.map(
          (s) => s.fieldMappings.caseNo.displayName
        );
        const caseNoIndx = this.headers.findIndex((h) =>
          caseNoFieldNames.includes(h.value)
        );

        this.cases = rows.slice(1).map((cols) => {
          const data = {};

          for (let i = 0; i < rows[0].length; i++) {
            data[rows[0][i]] = cols[i];
            data.caseNo = cols[caseNoIndx];
            if (!selected.find((s) => s.caseNo === data.caseNo)) {
              selected.push(data);
            }
          }

          return data;
        });
        this.selected = selected;
      });
    },
    onExcelFileClick() {
      this.$refs.excelFile.click();
    },
    userFullName(item) {
      if (item.customerFullName) {
        return item.customerFullName;
      } else if (item.firstName) {
        return fullName(item);
      } else {
        return "";
      }
    },
    onContinue() {
      this.$emit("continue", { selectedCases: this.selected });
    },
    onPrev() {
      this.$emit("prev");
    },
    onClose() {
      this.$emit("cancel");
    },
    async fetchCases() {
      try {
        this.fetchingCases = true;
        const { page, itemsPerPage } = this.options;
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems,
        };
        if (this.filterCustomer) {
          params.search = this.filterCustomer;
        }
        const response = await this.$axios.get("/workforce/collection/cases", {
          params,
          headers: {
            Authorization: this.authData?.access_token,
          },
        });
        this.cases = response.data.cases;
        this.fetchingCases = false;
        this.totalItems = response.data?.pagination?.totalCount;
      } catch (error) {
        this.$toast.error(error);
        this.fetchingCases = false;
      }
    },
    selectCustomer(customer) {
      this.$emit("selectCustomer", customer);
    },
  },
};
</script>

<style scoped>
.v-data-table >>> .v-data-table__wrapper > table > thead > tr > th span {
  white-space: nowrap;
}

.v-data-table
  >>> .v-data-table__wrapper
  > table
  > tbody
  > tr
  > td
  > .v-data-table__checkbox {
  text-align: center;
}
</style>
