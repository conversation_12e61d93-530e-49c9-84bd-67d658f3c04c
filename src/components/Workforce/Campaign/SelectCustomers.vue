<template>
  <div class="p-3">
    <v-text-field
      v-model="filterCustomer"
      label="Search by Customer"
      solo
      flat
      clearable
      outlined
      dense
      prepend-inner-icon="mdi-magnify"
      @input="handleInput"
    >
    </v-text-field>
    <v-data-table
      :headers="headers"
      :items="customers"
      fixed-header
      :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30, 50] }"
      :options.sync="options"
      :server-items-length="totalItems"
      :loading="gettingCustomers"
      v-model="selected"
      show-select
      item-key="_id"
    >
      <template v-slot:item.customerName="{ item }">
        <td class="whitespace-no-wrap px-4 py-6 font-semibold">
          {{ userFullName(item) }}
        </td>
      </template>
      <template v-slot:item.crn="{ item }">
        <td class="px-4 py-6 font-semibold">{{ item.crn }}</td>
      </template>
    </v-data-table>
    <div class="mt-2 flex items-center justify-between">
      <v-btn
        color="primary"
        text
        @click="onPrev"
        >
        <v-icon left>
          mdi-arrow-left
        </v-icon>
        Go back
      </v-btn>
      <div>
        <v-btn text @click="onClose">
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          @click="onContinue"
          :loading="loading"
        >
          Create Campaign
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script>
import { fullName, debounce } from "@/utils/common";

export default {
  name: 'SelectCustomers',
  data () {
    return {
      authData: this.$storage.getUniversal("token"),
      gettingCustomers: false,
      customers: [],
      filterCustomer: '',
      options: { itemsPerPage: 5, page: 1 },
      totalItems: 0,
      selected: [],
    }
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    headers() {
      return [
        { text: "Customer Name", value: "customerName", sortable: false },
        { text: "CRN", value: "crn", sortable: false },
      ]
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchCustomers();
      },
      deep: true,
    },
  },
  methods: {
    userFullName(item) {
      if (item.customerFullName) {
        return item.customerFullName;
      } else if (item.firstName) {
        return fullName(item);
      } else {
        return "";
      }
    },
    onContinue() {
      this.$emit("continue", { selectedCustomers: this.selected })
    },
    onPrev() {
      this.$emit('prev')
    },
    onClose() {
      this.$emit('cancel')
    },
    async fetchCustomers() {
      try {
        this.fetchingCustomers = true;
        const { page, itemsPerPage } = this.options;
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems,
        };
        if (this.filterCustomer) {
          params.search = this.filterCustomer;
        }
        const response = await this.$axios.get("/workforce/customers", {
          params,
          headers: {
            Authorization: this.authData?.access_token,
          },
        });
        this.customers = response.data.customers;
        this.fetchingCustomers = false;
        this.totalItems = response.data?.pagination?.totalCount;
      } catch (error) {
        this.$toast.error(error);
        this.fetchingCustomers = false;
      }
    },
    handleInput: debounce(async function () {
      this.options.page = 1;
      await this.fetchCustomers();
    }, 800),
    selectCustomer (customer) {
      this.$emit('selectCustomer', customer)
    }
  },
  mounted() {
    this.fetchCustomers();
  }
}
</script>

<style scoped>
.v-data-table
  >>> .v-data-table__wrapper
  > table
  > thead
  > tr
  > th
  span {
  white-space: nowrap;
}


.v-data-table
  >>> .v-data-table__wrapper
  > table
  > tbody
  > tr
  > td
  > .v-data-table__checkbox {
  text-align: center;
}
</style>
