<template>
  <v-stepper v-model="e1">
    <v-stepper-header>
      <v-stepper-step :complete="e1 > 1" step="1">
        Campaign Details
      </v-stepper-step>

      <v-divider></v-divider>

      <v-stepper-step :complete="e1 > 2" step="2">
        Create Campaign
      </v-stepper-step>

      <v-divider></v-divider>

      <v-stepper-step step="3"> Select Cases </v-stepper-step>
    </v-stepper-header>

    <v-stepper-items>
      <v-stepper-content step="1">
        <Channels @continue="onContinue" @cancel="onCancel" />
      </v-stepper-content>

      <v-stepper-content step="2">
        <WhatsappConfig
          v-if="campaignDetails.channel === 'whatsapp'"
          @cancel="onCancel"
          @continue="onWhatsappConfig"
        />
        <VoiceCallConfig
          v-if="campaignDetails.channel === 'call'"
          @selectVoiceFiles="onSelectCallVoiceFiles"
          @prev="onPrev"
          @cancel="onCancel"
          @continue="onVoiceCallConfig"
        />
      </v-stepper-content>

      <v-stepper-content step="3">
        <SelectCases
          :orgConfig="orgConfig"
          :loading="creatingCampaign"
          @prev="onPrev"
          @cancel="onCancel"
          @continue="onSelectCases"
        />
      </v-stepper-content>
    </v-stepper-items>
  </v-stepper>
</template>

<script>
import Channels from "./Channels.vue";
import SelectCases from "./SelectCases.vue";
import WhatsappConfig from "./WhatsappConfig.vue";
import VoiceCallConfig from "./VoiceCallConfig.vue";

export default {
  components: {
    Channels,
    SelectCases,
    WhatsappConfig,
    VoiceCallConfig,
  },
  props: {
    orgConfig: Object,
  },
  data() {
    return {
      authData: this.$storage.getUniversal("token"),
      e1: 1,
      creatingCampaign: false,
      campaignDetails: {
        campaignName: "",
        campaignDescription: "",
        channel: "",
        dateTimeEnd: "",
        dateTimeStart: "",
      },
      channelOptions: null,
      cases: [],
      voiceFiles: { customer: null, agent: null },
    };
  },
  methods: {
    onSelectCallVoiceFiles(voiceFiles) {
      this.voiceFiles = {
        customer: voiceFiles.customer,
        agent: voiceFiles.agent,
      };
    },
    async createCampaign() {
      const payload = {
        title: this.campaignDetails.campaignName,
        description: this.campaignDetails.campaignDescription,
        endDateTime: this.campaignDetails.dateTimeEnd,
        startDateTime: this.campaignDetails.dateTimeStart,
        virtualNumber: this.channelOptions.phoneNoId,
	cases: this.cases.map((c) => ({ case: c.caseNo }))
      }

      try {
        this.creatingCampaign = true;
        await this.$axios.post("/workforce/campaign", payload, {
          headers: {
            Authorization: this.authData.access_token,
          },
        });

        this.$toast.success("Successfully created campaign");
        this.$emit("create");
      } catch (err) {
        this.$toast.error(err.response.data.message);
      }
      this.creatingCampaign = false;
    },
    onSelectCases({ selectedCases }) {
      // console.log({ selectedCases });
      this.cases = selectedCases;
      this.createCampaign();
    },
    onPrev() {
      this.e1 = this.e1 - 1;
    },
    onCancel() {
      this.$emit("cancel");
    },
    onVoiceCallConfig(options) {
      this.e1 = 3;
      this.channelOptions = options;
    },
    onWhatsappConfig() {
      this.e1 = 3;
    },
    onContinue(campaignDetails) {
      this.campaignDetails = campaignDetails;
      this.e1 = 2;
    },
    onClose() {
      this.$emit("close");
    },
  },
};
</script>
