<template>
  <v-form class="pt-1">
    <v-select
      v-model="selectedPhoneNumber"
      :items="phoneNumbers"
      item-text="label"
      item-value="value"
      label="Select phone number"
      outlined
      dense
      hide-details
    ></v-select>
    <div class="flex mt-10 items-center justify-between">
      <v-btn
        color="primary"
        text
        @click="onPrev"
      >
        <v-icon left>
          mdi-arrow-left
        </v-icon>
         Go back
      </v-btn>
      <div>
        <v-btn text @click="onClose">
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          @click="onContinue"
        >
          Continue
        </v-btn>
      </div>
    </div>
  </v-form>
</template>

<script>
export default {
  name: "WhatsappConfig",
  layout: "Workforce/default",
  data() {
    return {
      authData: this.$storage.getUniversal("token"),
      selectedPhoneNumber: "",
      phoneNumbers: []
    };
  },
  watch: {
    search(agentName) {
      this.fetchAgents(agentName)
    }
  },
  methods: {
    async fetchPhoneNumbers() {
      const orgData = JSON.parse(localStorage.getItem("orgData"));
      const { data } = await this.$axios.get(`/workforce/tele-caller/credentials?orgId=${orgData?.id}`, {
        headers: {
          Authorization: this.authData.access_token,
        },
      });

      this.phoneNumbers = data.data.map(phoneNo => ({ label: phoneNo.phoneNumber, value: phoneNo._id }))
    },
    onPrev() {
      this.$emit('prev')
    },
    onContinue() {
      this.$emit('continue', { phoneNoId: this.selectedPhoneNumber })
    },
    onClose() {
      this.$emit('cancel')
    },
  },
  mounted() {
    this.fetchPhoneNumbers();
  }
};
</script>

<style scoped>
</style>
