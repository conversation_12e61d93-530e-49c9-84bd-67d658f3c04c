<template>
  <v-form class="p-3 pb-0 flex flex-col gap-4">
    <v-select
      v-model="selectedChannel"
      v-if="false"
      :items="channels"
      item-text="label"
      item-value="value"
      label="Select Channel"
      outlined
      dense
      hide-details
    ></v-select>
    <v-text-field
      v-model="name"
      label="Campaign Name"
      outlined
      dense
      hide-details
    ></v-text-field>
    <v-text-field
      v-model="description"
      label="Campaign Description"
      outlined
      dense
      hide-details
    ></v-text-field>
    <v-text-field
      outlined
      dense
      type="datetime-local"
      v-model="dateTimeStart"
      label="Select start date and time"
    ></v-text-field>
    <div class="-mt-6">
      <v-text-field
	outlined
	dense
	type="datetime-local"
	v-model="dateTimeEnd"
	label="Select end date and time"
      ></v-text-field>
    </div>
    <div class="flex gap-2 items-center justify-end">
      <v-btn text @click="onClose">
        Cancel
      </v-btn>
      <v-btn
        color="primary"
        @click="onContinue"
      >
        Continue
      </v-btn>
    </div>
  </v-form>
</template>

<script>
export default {
  name: "Chan-nels",
  layout: "Workforce/default",
  data() {
    return {
      authData: this.$storage.getUniversal("token"),
      isAddChannelVisible: false,
      name: "",
      description: "",
      selectedChannel: "call",
      dateTimeEnd: "",
      dateTimeStart: "",
      channels: [
        /*
        {
          label: "WhatsApp",
          value: "whatsapp",
        },
        */
        {
          label: "Call",
          value: "call",
        },
      ],
    };
  },
  methods: {
    onClose() {
      this.$emit('cancel')
    },
    onContinue() {
      if (!this.selectedChannel) return this.$toast.error("Channel is required");

      this.$emit('continue', {
        campaignName: this.name,
        campaignDescription: this.description,
        channel: this.selectedChannel,
	dateTimeEnd: this.dateTimeEnd,
	dateTimeStart: this.dateTimeStart
      })
    },
    toggleShowAddChannel() {
      this.isAddChannelVisible = !this.isAddChannelVisible;
    },
  },
};
</script>

<style scoped>
</style>
