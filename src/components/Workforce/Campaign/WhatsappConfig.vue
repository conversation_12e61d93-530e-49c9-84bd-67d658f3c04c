<template>
  <v-form class="pt-1">
    <v-select
      @change="handleTemplateChange"
      :items="whatsappTemplates"
      v-model="selectedTemplate"
      label="Select template"
      item-text="name"
      item-value="name"
      dense
      outlined
      :clearable="true"
    ></v-select>
    <div>
      <v-btn
        color="primary"
        @click="onContinue"
      >
        Continue
      </v-btn>

      <v-btn text @click="onClose">
        Cancel
      </v-btn>
    </div>
  </v-form>
</template>

<script>
export default {
  name: "WhatsappConfig",
  layout: "Workforce/default",
  data() {
    return {
      authData: this.$storage.getUniversal("token"),
      isAddChannelVisible: false,
      name: "",
      description: "",
      selectedChannel: "",
      selectedTemplate: "",
      whatsappTemplates: [],
      channels: [
        {
          channelName: "WhatsApp",
          channelDescription: "WhatsApp channel description",
        },
        {
          channelName: "Email",
          channelDescription: "Email channel description",
        },
        {
          channelName: "Call",
          channelDescription: "Call channel description",
        },
      ],
    };
  },
  methods: {
    handleTemplateChange() {
      this.$emit('continue', {
        campaignName: this.name,
        campaignDescription: this.description,
        channelId: this.selectedChannel,
        customers: [],
        template: this.selectedTemplate,
      })
    },
    async fetchTemplates() {
      const { data } = await this.$axios.get("/workforce/whatsapp/templates", {
        headers: {
          Authorization: this.authData.access_token,
        },
      });

      this.whatsappTemplates = data.templates;
    },
    onContinue() {
      this.$emit('continue', {
        campaignName: this.name,
        campaignDescription: this.description,
        channelId: this.selectedChannel,
        customers: [],
      })
    },
    onClose() {},
    toggleShowAddChannel() {
      this.isAddChannelVisible = !this.isAddChannelVisible;
    },
  },
  mounted() {
    this.fetchTemplates();
  },
};
</script>

<style scoped>
</style>
