<template>
  <div
    class="bg-white dark-bg-default p-4"
    :class="{ 'px-0 py-0': isProjectEnabled }"
  >
    <div class="flex justify-between mb-4">
      <p class="text-xl mb-0">
        {{ editData ? "Add Parts" : "Request Parts" }}
      </p>
      <v-icon @click="cancelDialog" class="cursor-pointer">mdi-close</v-icon>
    </div>
    <v-form ref="form" v-model="valid" lazy-validation>
      <v-card flat>
        <v-autocomplete
          v-model="selectedManager"
          label="Search Spare Part Manager"
          :items="employees"
          :item-text="getFullName"
          :search-input.sync="searchUser"
          item-value="_id"
          class="mb-4"
          v-show="!editData"
          @update:search-input="fetchUsers"
          :rules="[(v) => !!v || `Spare Part Manager is required`]"
          hide-details
          dense
          outlined
        >
        </v-autocomplete>
        <v-autocomplete
          v-model="selectedStore"
          label="Search Store"
          :items="activeStores"
          item-text="storeName"
          item-value="_id"
          class="mb-4"
          hide-details
          clearable
          dense
          outlined
        ></v-autocomplete>

        <v-autocomplete
          v-model="selectedProduct"
          label="Search Products"
          :items="inventoryItems"
          item-text="label"
          :search-input.sync="searchProduct"
          class="mb-4"
          item-value="_id"
          hide-details
          hide-no-data
          clearable
          required
          @update:search-input="handleInput"
          dense
          outlined
        >
          <template #item="{ item }">
            <v-list-item
              :disabled="isItemDisabled(item)"
              @click="addProductCard(item)"
            >
              <v-list-item-content>
                <v-list-item-title>{{ item.label }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </template>
        </v-autocomplete>
        <v-text-field
          v-model="comment"
          label="Add Comment"
          class="mb-4"
          v-show="!editData"
          hide-details
          dense
          outlined
        >
        </v-text-field>
      </v-card>
    </v-form>

    <v-row v-if="productCards.length > 0" class="flex justify-evenly">
      <v-col
        v-for="(header, index) in Object.keys(headers)"
        :key="index"
        class="flex justify-around"
        :class="headers[header]"
      >
        <h4>{{ header }}</h4>
      </v-col>
    </v-row>

    <div v-if="productCards.length > 0">
      <ProductCard
        v-for="(card, index) in productCards"
        :key="index"
        :product="card.product"
        :quantity="Number(card.quantity)"
        @deleteProduct="deleteProduct(index)"
        @increaseQuantity="increaseQuantity(index)"
        @decreaseQuantity="decreaseQuantity(index)"
      />
    </div>

    <v-row class="" v-if="!isProjectEnabled">
      <v-col cols="12" class="text-right">
        <v-btn outlined @click="cancelDialog" class="mr-3"> Cancel </v-btn>
        <v-btn
          :color="$vuetify.theme.currentTheme.primary"
          class="white--text"
          @click="raiseRequest"
        >
          {{ editData ? "Add Parts" : "Raise Request" }}
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>
<script>
import ProductCard from "./ProductCard";
import { debounce } from "@/utils/common";
export default {
  components: {
    ProductCard,
  },
  props: {
    selectedComplaintId: String,
    linkedTaskId: String,
    editData: Object,
  },
  data() {
    return {
      stores: [],
      inventoryItems: [],
      productCards: [],
      employees: [],
      numberOfParts: 1,
      selectedStore: null,
      selectedProduct: null,
      selectedManager: null,
      searchProduct: "",
      searchUser: "",
      comment: "",
      valid: true,
      headers: {
        Image: "",
        Name: "ml-6",
        Model: "ml-7 pl-3",
        Status: "ml-5",
        Quantity: "ml-11 -mr-2",
        Amount: "-mr-4 pl-4",
        Remove: "flex justify-end",
      },
    };
  },
  async mounted() {
    await this.fetchStores();
  },
  watch: {
    editData: {
      handler(newVal) {
        this.$props.editData = newVal;
      },
      deep: true,
    },
  },
  computed: {
    activeStores() {
      return this.stores.filter(
        (store) => store.storeStatus !== "NON_OPERATIONAL"
      );
    },
    partRequest() {
      const payload = {
        requestApprovalsfrom: this.selectedManager
          ? [{ approvalFrom: this.selectedManager }]
          : undefined,
        products: this.productCards.map((product) => ({
          product: product.product._id,
          store: this.selectedStore,
          quantityRequested: product.quantity,
        })),

      };
      return payload;
    },
    isProjectEnabled() {
      if (this.$route.params.usecase === "project") {
        return true
      }
      return false
    },
  },
  methods: {
    isItemDisabled(item) {
      return item.inStoreAvailability[0].quantityInStock == 0;
    },
    async fetchStores() {
      try {
        const response = await this.$axios.get("/workforce/ecom/stores", {
        });
        this.stores = response.data.stores;
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    async fetchInventory() {
      try {
        let url = `/workforce/ecom/products`;
        const response = await this.$axios.get(url, {
          params: {
            search: this.searchProduct,
          },
        });
        const data = response.data?.products;
        const filteredStore = data.filter((el) =>
          el.inStoreAvailability.some(
            (availability) =>
              availability.storeLocation._id === this.selectedStore
          )
        );
        this.inventoryItems = filteredStore.map((el) => {
          const inStoreAvailability = el.inStoreAvailability.find(
            (availability) =>
              availability.storeLocation._id === this.selectedStore
          );
          return {
            ...el,
            label: `${el.productName} - Model_${el.modelNumber} (Quantity - ${inStoreAvailability.quantityInStock})`,
          };
        });
      } catch (error) {
        console.error("An error occurred:", error);
      }
    },
    fetchUsers: debounce(async function (text) {
      try {
        if (!text || this.selectedManager) return;
        const params = {};
        if (this.searchUser) {
          params.search = this.searchUser;
        }
        const response = await this.$axios.get("/workforce/users", {
          params,
        });
        this.employees = response.data?.users;
      } catch (error) {
        console.log(error);
      }
    }, 500),
    addProductCard(item) {
      this.selectedProduct = item._id;
      if (this.selectedProduct) {
        const selectedProductDetails = this.inventoryItems.find(
          (product) => product._id === this.selectedProduct
        );
        if (selectedProductDetails) {
          const existingProductIndex = this.productCards.findIndex(
            (card) => card.product._id === this.selectedProduct
          );

          existingProductIndex !== -1
            ? this.increaseQuantity(existingProductIndex)
            : this.productCards.push({
                product: selectedProductDetails,
                quantity: this.numberOfParts,
              });
          this.selectedProduct = "";
          this.inventoryItems = [];
          this.numberOfParts = 1;
        }

        this.$emit("addedProduct", this.partRequest);
      }
    },
    async raiseRequest() {
      try {
        const isValid = this.$refs.form.validate();
        if (!isValid && !this.editData) {
          this.$toast.error("Please fill the required fields");
          return;
        }
        if (!this.productCards.length && this.editData) {
          this.$toast.error("Please add spare part in the list");
          return;
        }
        const payload = {
          requestApprovalsfrom: this.selectedManager
            ? [{ approvalFrom: this.selectedManager }]
            : undefined,
          products: this.productCards.map((product) => ({
            product: product.product._id,
            store: this.selectedStore,
            quantityRequested: product.quantity,
          })),
          complaint: this.selectedComplaintId || undefined,
          task: this.linkedTaskId || undefined,
          comment: this.comment || undefined,
          request_type: "INVENTORY_REQUEST",
        };

        const url = this.$props.editData
          ? `/workforce/ecom/inventory/request/${this.$props.editData._id}/product`
          : "/workforce/ecom/inventory/request";

        const method = this.$props.editData ? "put" : "post";

        const response = await this.$axios[method](url, payload);

        this.cancelDialog();
        const successMessage = response?.data?.message || "Request has been raised!!"

        this.$toast.success(successMessage);
        this.$emit("requestRaised", response.data);
      } catch (error) {
        console.log(error);
        this.$toast.error(error.response?.data?.message);
      }
    },
    getFullName(employee) {
      return (
        `${employee.firstName || ""} ${employee.lastName || ""}` +
        (employee.wfmRole ? ` (${employee.wfmRole.role})` : "")
      );
    },
    deleteProduct(index) {
      this.productCards.splice(index, 1);
    },
    increaseQuantity(index) {
      this.productCards[index].quantity++;
      this.$emit("addedProduct", this.partRequest);
    },
    decreaseQuantity(index) {
      if (this.productCards[index].quantity > 1) {
        this.productCards[index].quantity--;
        this.$emit("addedProduct", this.partRequest);
      }
    },
    cancelDialog() {
      this.selectedManager = null;
      this.selectedProduct = null;
      this.selectedStore = "";
      this.productCards = [];
      this.$emit("cancel");
    },
    handleInput: debounce(function (text) {
      if (!text) return;
      this.fetchInventory();
    }, 800),
  },
};
</script>
