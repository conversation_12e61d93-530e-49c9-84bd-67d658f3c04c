<template>
  <div class="bg-white dark-bg-custom rounded-lg shadow-lg p-4">
    <h2 class="text-xl text-gray-800 dark-text-color font-semibold mb-4">Send WhatsApp Message</h2>
    <v-select
      :items="templates"
      v-model="selectedTemplate"
      label="Select Template"
      dense
      outlined
      :clearable="true"
    ></v-select>
    <div class="flex justify-end gap-4 items-center">
      <v-btn :color="$vuetify.theme.currentTheme.primary">
        <v-icon left> mdi-send </v-icon>
        Send
      </v-btn>
      <v-btn outlined @click="$emit('update:modelValue', false)">
        Cancel
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  name: "WhatsAppMessage",
  props: {
    customers: Array,
  },
  data() {
    return {
      selectedUser: "",
      selectedTemplate: "",
      users: [],
    };
  },
  computed: {
    templates() {
      return [
        "template_1",
        "notify_customer"
      ];
    },
  },
  methods: {
  },
};
</script>

<style></style>
