<template>
  <div>
    <div class="flex items-center py-8 justify-center flex-col">
      <p class="mb-0 text-3xl font-semibold text-gray-700">
        {{ title }}
      </p>
      <p class="mb-0 text-gray-500 mt-2" v-if="!callTab">{{ subTitle }}</p>
      <div class="flex mt-3 items-center" v-if="btnText && !callTab">
        <slot>
          <v-btn :color="$vuetify.theme.currentTheme.primary" class="white--text" @click="btnAction">{{ btnText }}</v-btn>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NoData",
  props: {
    title: String,
    subTitle: String,
    btnText: String,
    btnAction: Function,
    callTab: Boolean
  },
};
</script>
