<template>
  <div class="transaction-data">
    <div class="header" @click="onToggleClaimData">
      <div class="flex justify-end">
        <p class="claim-status">
          <span :class="claimSummary?.status.toLowerCase()">{{
            claimSummary?.status
          }}</span>
        </p>
      </div>
      <div>
        <p class="claim-ref">
          <span>Ref ID:</span>
          <span>{{ claimSummary?.claim_ref_id }}</span>
        </p>
        <p class="claim-amt">
          <span>Claim Amount:</span>
          <span> &#8377; {{ claimSummary?.total_claim_amount }}</span>
        </p>
      </div>
      <div>
        <p class="claim-date-time">
          <span>Distance Travelled:</span>
          <span>{{ claimSummary?.total_distance || 0 }} Km</span>
        </p>
        <p class="claim-date-time" v-if="claimSummary?.travel_amt_max">
          <span>Max Claim Amt:</span>
          <span>&#8377; {{ claimSummary?.travel_amt_max }}</span>
        </p>
      </div>
      <div>
        <p class="claim-date-time">
          <span>Mode of Travel:</span>
          <span>{{ claimSummary?.mode_of_travel || "" }} </span>
          <span style="color: green" class="font-semibold">
            (&#8377; {{ claimSummary?.travel_amt_per_km || 0 }} /Km)
          </span>
        </p>
        <p class="claim-date-time" v-if="claimSummary?.travel_amt_min">
          <span>Min Claim Amt:</span>
          <span>&#8377; {{ claimSummary?.travel_amt_min }}</span>
        </p>
      </div>
      <div>
        <p class="claim-amt" v-if="claimSummary?.allowance"></p>
        <p class="claim-date-time" v-if="claimSummary?.allowance">
          <span>Allowance:</span>
          <span
            >{{ titleCase(claimSummary?.allowance).split("-")[0] || "" }}
          </span>
          <span style="color: green" class="font-semibold">
            (&#8377; {{ claimSummary?.allowance.split("-")[1] || 0 }})
          </span>
        </p>
      </div>
      <div>
        <p class="claim-date-time" v-if="claimSummary?.from">
          <span>From:</span>
          <span> {{ claimSummary?.from }}</span>
        </p>
        <p class="claim-date-time" v-if="claimSummary?.to">
          <span>To:</span>
          <span>{{ claimSummary?.to }}</span>
        </p>
      </div>
      <div>
        <p class="claim-date-time" v-if="claimSummary?.claim_from_date">
          <span>From Date:</span>
          <span>
            {{
              $dayjs(claimSummary?.claim_from_date).format("DD/MM/YYYY")
            }}</span
          >
        </p>
        <p class="claim-date-time" v-if="claimSummary?.claim_to_date">
          <span>To Date:</span>
          <span>{{
            $dayjs(claimSummary?.claim_from_date).format("DD/MM/YYYY")
          }}</span>
        </p>
      </div>
      <div>
        <p class="claim-date-time">
          <span>Created On:</span>
          <span>
            {{
              $dayjs(claimSummary?.created_at).format("DD-MM-YYYY hh:mm: A")
            }}</span
          >
        </p>
        <p class="claim-date-time">
          <span>Last Updated At:</span>
          <span>{{
            $dayjs(claimSummary?.updated_at).format("DD-MM-YYYY hh:mm: A")
          }}</span>
        </p>
      </div>
    </div>
    <div class="main" v-if="isDetailVisible">
      <div class="content">
        <div v-if="claimDetails?.transactions?.length">
          <header class="msger-header" @click="onToggleTransactionLog">
            <div class="msger-header-title">
              Claimed Transaction ({{ isTransactionLogVisible ? "-" : "+" }})
            </div>
          </header>
          <v-simple-table v-if="isTransactionLogVisible">
            <template v-slot:default>
              <thead>
                <th>Comment</th>
                <th>Amount &#8377;</th>
                <th>Date (dd-mm-yyyy)</th>
              </thead>
              <tbody>
                <tr v-for="transaction in claimDetails?.transactions">
                  <td>{{ transaction?.transaction_comments }}</td>
                  <td>{{ transaction?.transaction_amount }}</td>
                  <td>
                    {{
                      $dayjs(transaction?.transaction_time).format(
                        "DD-MM-YYYY hh:mm: A"
                      )
                    }}
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </div>
        <div class="conv-container" v-if="claimDetails?.supplement?.length">
          <section class="msger">
            <header class="msger-header" @click="onToggleConv">
              <div class="msger-header-title dark-text-color">
                Conversation ({{ isConvVisible ? "-" : "+" }})
              </div>
              <div class="msger-header-options">
                <span><i class="fas fa-cog"></i></span>
              </div>
            </header>
            <main class="msger-chat" v-if="isConvVisible">
              <div
                :class="
                  comment?.added_by_user_type === 'USER'
                    ? 'msg left-msg'
                    : 'msg right-msg'
                "
                v-for="comment in claimDetails?.supplement"
              >
                <div class="msg-bubble">
                  <div class="msg-info">
                    <div class="msg-info-name">
                      {{ comment?.added_by_user_name }}
                    </div>
                    <div class="msg-info-time">
                      {{
                        $dayjs(comment?.added_at).format("DD-MM-YYYY hh:mm: A")
                      }}
                    </div>
                  </div>

                  <div class="msg-text">
                    {{ comment?.comment }}
                  </div>

                  <div class="supportive-docs" v-if="comment?.document?.length">
                    <LightBox
                      :imagesData="
                        comment?.document.map((doc) => ({
                          src: doc.doc_url,
                          crossorigin: 'anonymous',
                        }))
                      "
                    />
                  </div>
                </div>
              </div>
            </main>
          </section>
        </div>

        <div v-show="isFeatureVisible('ENABLE_CLAIM_APPROVAL_PROCESS')">
          <header class="msger-header" @click="onToggleTakeAction">
            <div class="msger-header-title dark-text-color">
              Take action ({{ isTakeActionVisible ? "-" : "+" }})
            </div>
          </header>
          <v-form
            ref="form"
            v-model="valid"
            lazy-validation
            v-if="
              isTakeActionVisible &&
              claimSummary?.status !== 'APPROVED' &&
              claimSummary?.status !== 'RETURNED'
            "
          >
            <v-container>
              <v-row>
                <v-col cols="12" md="7">
                  <v-text-field
                    v-model="adminComment"
                    :rules="adminCommentRules"
                    :counter="100"
                    label="Enter Your Comment."
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="3">
                  <v-select
                    v-model="adminActionTaken"
                    :rules="adminStatusRules"
                    :items="reimbursementStatusType[claimSummary?.status]"
                    label="Select Status"
                  ></v-select>
                </v-col>
                <v-col cols="12" md="2">
                  <v-btn
                    type="button"
                    @click="update"
                    variant="flat"
                    :color="$vuetify.theme.currentTheme.primary"
                    class="white--text"
                  >
                    Submit
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
          <v-container
            v-if="isTakeActionVisible && claimSummary?.status === 'RETURNED'"
          >
            <div>
              <p>You can take action once employee resubmits the claim.</p>
            </div>
          </v-container>
        </div>
      </div>

      <v-alert color="green" type="success" dismissible :value="isAlert" light>
        Your Status have been changed successfully for Claim "{{
          claimSummary?.claim_ref_id
        }}" Id.<br />Next time you will find this claim in
        {{ claimSummary?.status }} filter status.</v-alert
      >
    </div>
  </div>
</template>

<script>
import { convertToTitleCase } from "@/utils/common";
import "vue-cool-lightbox/dist/vue-cool-lightbox.min.css";
import { isModuleFeatureAllowed } from "@/utils/common";
export default {
  name: "ClaimItem",
  props: {
    claimData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isAlert: false,
      isDetailVisible: false,
      user: this.$storage.getUniversal("user"),
      valid: true, //this key can only be used internally
      adminActionTaken: "",
      adminComment: "",
      adminCommentRules: [
        (value) => {
          if (value) return true;
          return "You must enter a comment.";
        },

        (v) =>
          (v && v.length >= 10 && v.length < 100) ||
          "Your comment should not be less than 10 character",
      ],
      adminStatusRules: [
        (value) => {
          if (value) return true;
          return "You must select a status.";
        },
      ],
      claimSummary: this.claimData,
      claimDetails: null,
      isClaimDetailsFetched: false,
      isConvVisible: false,
      isTakeActionVisible: false,
      isTransactionLogVisible: false,
      reimbursementStatusType: {
        NEW: ["RETURNED", "ON_HOLD", "APPROVED", "REJECTED"],
        RESUBMITTED: ["RETURNED", "ON_HOLD", "APPROVED", "REJECTED"],
        ON_HOLD: ["RETURNED", "APPROVED", "REJECTED"],
        REOPENED: ["RETURNED", "APPROVED", "REJECTED"],
        REJECTED: ["REOPENED"],
        CLOSED: ["REOPENED"],
      },
    };
  },
  updated() {
    if (
      this.isDetailVisible &&
      !this.claimDetails &&
      !this.isClaimDetailsFetched
    ) {
      this.fetchClaimDetails();
    }
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed("CLAIM_MANAGEMENT", feature);
    },
    titleCase(str) {
      return convertToTitleCase(str);
    },
    onToggleTakeAction() {
      this.isTakeActionVisible = !this.isTakeActionVisible;
    },
    onToggleClaimData() {
      this.isDetailVisible = !this.isDetailVisible;
    },
    onToggleConv() {
      this.isConvVisible = !this.isConvVisible;
    },
    onToggleTransactionLog() {
      this.isTransactionLogVisible = !this.isTransactionLogVisible;
    },
    async update() {
      const isValid = this.$refs.form?.validate();
      if (isValid) {
        const { claim_id } = this.claimData;
        const payload = {
          claim_status: this.adminActionTaken,
          user_comment: this.adminComment,
        };
        try {
          const res = await this.$axios.put(
            `/api/crpx/org/claim?claimid=${claim_id}`,
            payload
          );
          if (res.data.status === true) {
            this.isAlert = true;
            this.$refs.form.reset();
            this.fetchClaimDetails();
          } else {
            this.$toast.error(res.data.message);
          }
        } catch (error) {
          console.error(error);
          this.$toast.error(error.message);
        }
      }
    },
    async fetchClaimDetails() {
      try {
        const { claim_id } = this.claimData;
        const res = await this.$axios.get(
          `/api/crpx/org/claim?claimid=${claim_id}`
        );
        if (res.data.status === true) {
          this.claimDetails = res.data.claimDetails;
          this.claimSummary = res.data.claimDetails.summary;
          this.isClaimDetailsFetched = true;
        }
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>

<style>
.image {
  width: 300px;
  height: 300px;
  margin: 0 auto;
}
.transaction-data p {
  margin-bottom: 0;
}
.transaction-data > .header {
  background-color: #f0f0f0;
  padding: 10px;
  cursor: pointer;
}

.dark .transaction-data > .header {
  background-color: var(--bg-custom);
}
.transaction-data > .header > div {
  display: flex;
  justify-content: space-between;
}
.transaction-data > .header > div > p > span:nth-child(1) {
  font-weight: bold;
}
.rejected {
  color: red;
  font-weight: bold;
}
.approved {
  color: green;
  font-weight: bold;
}
.on_hold,
.returned {
  color: orange;
  font-weight: bold;
}
.resubmitted,
.new {
  color: #4d4de5;
  font-weight: bold;
}
.content {
  padding: 10px;
}
.content > div {
  border: 1px solid #ddd;
  margin-bottom: 10px;
}
.content h3,
.msger-header-title {
  margin-bottom: 10px;
  font-size: 14px;
  cursor: pointer;
  background: #eee;
  padding: 10px;
  margin: 0;
}

.dark .content h3,
.dark .msger-header-title {
  background: var(--bg-custom);
}
div.conv-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 400px;
  font-family: Helvetica, sans-serif;
}

.msger {
  display: flex;
  flex-flow: column wrap;
  justify-content: space-between;
  width: 100%;
  height: calc(100% - 50px);
  background: #fff;
}

.msger-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
  background: #eee;
  color: #666;
  cursor: pointer;
}

.dark .msger-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
  background: var(--bg-custom);
  color: #666;
  cursor: pointer;
}

.msger-chat {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}
.msger-chat::-webkit-scrollbar {
  width: 6px;
}
.msger-chat::-webkit-scrollbar-track {
  background: #ddd;
}
.msger-chat::-webkit-scrollbar-thumb {
  background: #bdbdbd;
}
.msg {
  display: flex;
  align-items: flex-end;
  margin-bottom: 10px;
}
.msg:last-of-type {
  margin: 0;
}
.msg-img {
  width: 50px;
  height: 50px;
  margin-right: 10px;
  background: #ddd;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 50%;
}
.msg-bubble {
  max-width: 450px;
  padding: 15px;
  border-radius: 15px;
  background: #ececec;
}

.dark .msg-bubble {
  max-width: 450px;
  padding: 15px;
  border-radius: 15px;
  background: var(--bg-default);
}
.msg-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.msg-info-name {
  margin-right: 10px;
  font-weight: bold;
}
.msg-info-time {
  font-size: 0.85em;
}

.left-msg .msg-bubble {
  border-bottom-left-radius: 0;
}

.right-msg {
  flex-direction: row-reverse;
}
.right-msg .msg-bubble {
  background: #579ffb;
  color: #fff;
  border-bottom-right-radius: 0;
}
.right-msg .msg-img {
  margin: 0 0 0 10px;
}

.msger-inputarea {
  display: flex;
  padding: 10px;
  border-top: 2px solid #ddd;
  background: #eee;
}
.msger-inputarea * {
  padding: 10px;
  border: none;
  border-radius: 3px;
  font-size: 1em;
}
.msger-input {
  flex: 1;
  background: #ddd;
}
.msger-send-btn {
  margin-left: 10px;
  background: rgb(0, 196, 65);
  color: #fff;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.23s;
}
.msger-send-btn:hover {
  background: rgb(0, 180, 50);
}

.msger-chat {
  background-color: #fcfcfe;
  max-height: 350px;
}

.dark .msger-chat {
  background-color: var(--bg-custom);
  max-height: 350px;
}

.supportive-docs {
  display: flex;
}
.supportive-docs img {
  height: 100px;
  width: 100px;
  margin: 5px;
  border: 1px solid;
}
table {
  text-align: left;
  padding: 5px;
}
tbody > tr > td {
  padding: 0 !important;
  margin: 0 !important;
}
</style>
