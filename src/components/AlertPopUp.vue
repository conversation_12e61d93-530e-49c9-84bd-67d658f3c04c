<template>
  <div>
    <v-dialog
      v-model="localShowConfirmationDialog"
      max-width="600"
      persistent
      @input="emitDialogChange"
    >
      <v-card :color="$vuetify.theme.currentTheme.navigationColor">
        <v-card-title class="text-h5 justify-center">
          {{ confirmationTitle }}
        </v-card-title>
        <div
          class="flex flex-col justify-center items-center p-2"
          v-if="dialogLable === 'delete'"
        >
          <v-checkbox
            :color="$vuetify.theme.currentTheme.primary"
            v-model="localDeleteCustomer"
            @change="emitCheckboxChange"
            :label="confirmationCustomerMessage"
            hide-details
            class="text-center"
          ></v-checkbox>
          <div class="flex" v-if="localDeleteCustomer">
            <v-icon color="warning" small>mdi-shield-alert-outline</v-icon>
            <p class="text-center flex items-center warning-text p-1 mb-0">
              {{ afterConfirmationMessage }}
            </p>
          </div>
        </div>
        <v-card-text class="text-center">
          {{ confirmationMessage }}
        </v-card-text>
        <v-card-actions class="flex mb-2 justify-end w-full">
          <v-btn
            outlined
            class="mr-2"
            @click="cancel"
            v-if="confirmationActionText !== 'Logout'"
          >
            <span class="text-black dark-text-color">{{
              cancelActionText
            }}</span>
          </v-btn>
          <v-btn
            :color="confirmationActionColor"
            class="white--text"
            @click="performAction"
            :loading="loading"
          >
            {{ confirmationActionText }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      localShowConfirmationDialog: this.showConfirmationDialog,
      localDeleteCustomer: this.deleteCustomer,
    };
  },
  watch: {
    showConfirmationDialog(val) {
      this.localShowConfirmationDialog = val;
    },
    deleteCustomer(val) {
      this.localDeleteCustomer = val;
    },
  },
  props: {
    confirmationTitle: String,
    showConfirmationDialog: Boolean,
    confirmationMessage: String,
    confirmationActionText: String,
    cancelActionText: { type: String, default: "Cancel" },
    confirmationActionColor: String,
    cancel: Function,
    performAction: Function,
    dialogLable: String,
    confirmationCustomerMessage: String,
    deleteCustomerFunc: Function,
    loading: Boolean,
    afterConfirmationMessage: String,
    deleteCustomer: Boolean,
  },
  methods: {
    emitCheckboxChange() {
      this.$emit("update:deleteCustomer", this.localDeleteCustomer);
    },
    emitDialogChange() {
      this.$emit(
        "update:showConfirmationDialog",
        this.localShowConfirmationDialog
      );
    },
  },
};
</script>

<style scoped>
.warning-text {
  color: #fb8c00;
  font-size: 12px;
}
</style>
