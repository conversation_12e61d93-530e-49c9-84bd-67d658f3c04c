<template>
  <div class="bg-gray-50">
    <!-- Header Tabs -->
    <div class="border-b border-gray-300">
      <div class="py-2">
        <div class="flex justify-between items-center">
          <div class="flex space-x-2">
            <v-btn
              v-for="(tab, index) in tabs"
              :key="index"
              :color="activeTab === tab.key ? 'primary' : 'gray'"
              @click="setActiveTab(tab.key)"
              depressed
              class="text-xs"
              :outlined="activeTab === tab.key ? false : true"
              :class="activeTab === tab.key ? 'text-white' : ''"
            >
              <v-icon left>{{ tab.icon }}</v-icon>
              {{ tab.label }}
            </v-btn>
          </div>
          <!-- Filter Buttons for Each Tab -->
          <div class="mr-4">
            <slot name="tab-buttons" :activeTab="activeTab"></slot>
          </div>
        </div>
      </div>
    </div>

    <!-- Dynamic Content Based on Active Tab -->
    <div class="tab-content">
      <slot :name="activeTab" :activeTab="activeTab"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActiveTabComponent',
  props: {
    tabs: {
      type: Array,
      required: true,
      validator: (tabs) => {
        return tabs.every(
          (tab) =>
            Object.prototype.hasOwnProperty.call(tab, 'key') &&
            Object.prototype.hasOwnProperty.call(tab, 'label') &&
            Object.prototype.hasOwnProperty.call(tab, 'icon') &&
            Object.prototype.hasOwnProperty.call(tab, 'component')
        )
      }
    },
    defaultTab: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      activeTab: this.defaultTab || (this.tabs.length > 0 ? this.tabs[0].key : null)
    }
  },
  methods: {
    setActiveTab(tabKey) {
      this.activeTab = tabKey
      this.$emit('tab-changed', tabKey)
    }
  },
  watch: {
    defaultTab(newVal) {
      if (newVal) {
        this.activeTab = newVal
      }
    }
  }
}
</script>

<style scoped>
.tab-content {
  min-height: calc(80vh - 80px);
}

/* Override Vuetify's default uppercase text transform */
.v-btn {
  text-transform: none !important;
}
.buttonClass {
  font-size: 12px;
}
</style>
