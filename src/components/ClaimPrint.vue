<template>
  <div></div>
</template>

<script>
import { convertToTitleCase } from "@/utils/common";
export default {
  name: "ClaimPrint",
  data() {
    return {
      claimDetailsArray: [],
    };
  },
  props: { printData: Array, index: Number },
  mounted() {
    this.fetchClaimDetails();
  },
  watch: {
    printData: {
      handler(newVal) {
        this.printData = newVal;
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    async fetchClaimDetails() {
      for (const item of this.printData) {
        try {
          const res = await this.$axios.get(
            `/api/crpx/org/claim?claimid=${item.claim_id}`
          );
          if (res.data.status === true) {
            this.claimDetailsArray.push(res.data.claimDetails);
          }
        } catch (error) {
          console.error(error);
        }
      }
    },
    async openClaimDetailsPage() {
      const newWindow = window.open("", "_blank");
      const content = this.printContent();
      newWindow.document.write(content);
      newWindow.document.close();
      newWindow.onload = () => {
        newWindow.print();
      };
    },
    printContent() {
      const additionalTable = `
            <table class="info-table">
            <thead>
                <p class="heading">Claim Report of ${this.printData[0]?.claimant_first_name} ${this.printData[0]?.claimant_last_name}</p>
            </thead>
            <tbody>
                <tr>
                <th>Full Name</th>
                <td>${this.printData[0]?.claimant_first_name} ${this.printData[0]?.claimant_last_name}</td>
                </tr>
                <tr>
                <th>Mobile</th>
                <td>${this.printData[0]?.claimant_mobile}</td>
                </tr>
                <tr>
                <th>User Id</th>
                <td>${this.printData[0]?.claimant_id}</td>
                </tr>
                <tr v-if="this.printData[0]?.claimant_email">
                <th>Email Id</th>
                <td>${this.printData[0]?.claimant_email}</td>
                </tr>
            </tbody>
            </table>
        `;
      let totalAmount = 0;
      const rows = this.claimDetailsArray
        .map((claimDetails) => {
          totalAmount += claimDetails.summary.total_claim_amount;
          return `
          <tr>
            <td>${this.$dayjs(claimDetails.summary.created_at).format(
              "DD/MM/YYYY hh:mm A"
            )}</td>
            <td>${claimDetails.summary.claim_ref_id}</td>
            <td class="description">
              ${
                claimDetails.summary.from
                  ? `From - ${claimDetails.summary.from}<br/>`
                  : ""
              }
              ${
                claimDetails.summary.to
                  ? `To - ${claimDetails.summary.to}<br/>`
                  : ""
              }
              ${
                claimDetails.supplement[0]?.comment
                  ? `Comment - ${claimDetails.supplement[0]?.comment}<br/>`
                  : ""
              }
              ${
                claimDetails.summary.total_distance
                  ? `Distance Travelled - ${claimDetails.summary.total_distance}<br/>`
                  : `Distance Travelled - 0 Km <br/>`
              }
              ${
                claimDetails.summary.mode_of_travel
                  ? `Travel Mode - ${
                      claimDetails.summary.mode_of_travel
                    } (&#8377; ${
                      claimDetails.summary.travel_amt_per_km || 0
                    })<br/>`
                  : ""
              }
              ${
                claimDetails.summary.allowance
                  ? `Allowance - ${
                      convertToTitleCase(claimDetails.summary.allowance)?.split(
                        "-"
                      )[0]
                    } (&#8377; ${
                      claimDetails.summary.allowance.split("-")[1] || 0
                    })`
                  : ""
              }
              ${
                claimDetails.summary.travel_amt_max
                  ? `Max Claim Amt - ${claimDetails.summary.travel_amt_max}<br/>`
                  : ""
              }
              ${
                claimDetails.summary.travel_amt_min
                  ? `Min Claim Amt - ${claimDetails.summary.travel_amt_min}<br/>`
                  : ""
              }
            </td>
            <td>${claimDetails.summary.status}</td>
            <td>&#8377; ${claimDetails.summary.total_claim_amount}</td>
            <td>${
              claimDetails.supplement[0]?.document[0]?.doc_url
                ? `<img src="${claimDetails.supplement[0]?.document[0]?.doc_url}" alt="image">`
                : ""
            }</td>
          </tr>
        `;
        })
        .join("");
      const table = `
            <table class="claim-table">
            <thead>
                <tr>
                <th>Date</th>
                <th>Ref ID</th>
                <th>Description</th>
                <th>Status</th>
                <th>Claim Amount</th>
                <th>Attachment</th>
                </tr>
            </thead>
            <tbody>
                ${rows}
            </tbody>
            </table>
        `;

      const totalClaimAmt = `
        <table class="total-claim-table">
            <thead>
                <tr>
                <th>Total Claim Amount raised by ${this.printData[0]?.claimant_first_name} ${this.printData[0]?.claimant_last_name} - ${totalAmount} Rs</th>
                </tr>
            </thead>
            </table>
        `;

      return `
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Claim Report</title>
        <style>
          .claim-table , .info-table {
            width: 100%;
            border-collapse: collapse;
          }
          .info-table th{
            width: 20%;
            border:1px solid black;
            padding: 10px;
          }
          .info-table td{
            width: 80%;
            padding: 10px;
            border:1px solid black;
          }
          .heading{
            text-align: center;
            font-size:18px;
            font-weight:bold
          }
          .claim-table th, .claim-table td {
            border: 1px solid black;
            font-size:10px;
            padding: 18px;
          }
          .claim-table img {
            max-width: 150px;
            max-height: 150px;
            border: 1px solid black;
          }
          .description{
            line-height:1.5
          }
          .total-claim-table{
            display:flex;
            justify-content:end;
            border: 1px solid black;
            font-size:12px;
            padding: 18px;
          }
          // @media print {
          //   @page {
          //     size: landscape;
          //   }
          // }
        </style>
      </head>
      <body>
        ${additionalTable}
        ${table}
        ${totalClaimAmt}
      </body>
    </html>
  `;
    },
  },
};
</script>

<style></style>
