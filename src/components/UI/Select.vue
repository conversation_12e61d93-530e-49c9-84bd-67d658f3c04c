<template>
  <div
    class="w-full flex items-center border-gray-300 shadow-sm border border-solid sm:text-sm py-2"
  >
    <p class="px-2 gradient-label text-sm font-semibold pl-3 mb-0" v-if="label">{{ label }}:</p>
    <div class="relative w-full flex items-center justify-between" v-click-outside="onBlur">
      <div
        ref="input"
        :class="label ? 'ml-0' : 'ml-3'"
        class="mr-2 flex items-center justify-between cursor-text flex-grow w-full"
      >
        <div>
          <span v-if="inputValue" class="text-gray-800">{{ titleCase(inputValue) }}</span>
          <span v-else class="text-gray-400">Select...</span>
        </div>
        <div>
          <v-icon
            v-if="clearable && inputValue"
            @click.stop="clearSelection"
            class="ml-2 text-gray-400 cursor-pointer"
            >mdi-close</v-icon
          >
          <v-icon :class="isOpen ? 'rotate-180' : ''" color="gray" class="ml-2 text-gray-400"
            >mdi-menu-down</v-icon
          >
        </div>
      </div>
      <div
        ref="list"
        id="select-list"
        class="absolute z-50 w-full my-4 overflow-hidden border border-solid border-gray-100 shadow-lg"
      >
        <ul class="bg-white max-h-56 overflow-y-auto pl-0">
          <li
            v-if="showAddNew"
            key="add-new"
            @click="onAddNew"
            class="cursor-pointer text-blue-500 flex items-center gap-2 font-bold whitespace-nowrap hover:bg-gray-100 p-2"
          >
            <v-icon color="blue">mdi-plus</v-icon>
            Add new
          </li>
          <li
            v-for="(item, i) in items"
            :key="i"
            @click="onSelect(item)"
            class="cursor-pointer whitespace-nowrap hover:bg-gray-100 p-2"
          >
            {{ titleCase(item[itemText]) }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { createPopperLite as createPopper, preventOverflow, flip } from '@popperjs/core'
import clickOutside from '@/utils/clickOutside'
import { convertToTitleCase } from '@/utils/common'

export default {
  name: 'SelectComponent',
  directives: {
    clickOutside
  },
  props: {
    items: {
      type: Array,
      default: () => []
    },
    showAddNew: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    itemText: {
      type: String,
      default: 'label'
    },
    itemValue: {
      type: String,
      default: 'value'
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isOpen: false
    }
  },
  computed: {
    inputValue() {
      const value = this.items.find((item) => item[this.itemValue] === this.value)

      if (value) {
        return value[this.itemText]
      }

      return ''
    }
  },
  methods: {
    onFocus() {
      this.isOpen = true
    },
    onAddNew() {
      this.onBlur()
      this.$emit('add')
    },
    onBlur() {
      this.$refs.list?.removeAttribute('data-show')
      this.isOpen = false
    },
    onSelect(item) {
      const value = item[this.$props.itemValue]
      this.$emit('input', value)
      this.$emit('select', { value, item })
      this.onBlur()
    },
    clearSelection() {
      this.$emit('input', '')
      this.$emit('select', { value: '', item: null })
    },
    titleCase(str) {
      return convertToTitleCase(str)
    }
  },
  mounted() {
    const popper = createPopper(this.$refs.input, this.$refs.list, {
      modifiers: [preventOverflow, flip]
    })

    this.$refs.input.addEventListener('click', () => {
      if (this.$refs.list.getAttribute('data-show')) {
        this.onBlur()
      } else {
        this.$refs.list.setAttribute('data-show', '')
        this.onFocus()
        popper.update()
      }
    })
  }
}
</script>

<style>
#select-list {
  display: none;
}

#select-list[data-show] {
  display: block;
}
</style>
