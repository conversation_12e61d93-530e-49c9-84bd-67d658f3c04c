<template>
    <v-text-field
        v-model="inputVal"
        :label="label"
        @click:append="togglePassword"
        :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
        :rules="passwordRule"
        required
        :type="showPassword ? 'text' : 'password'"
        :autocomplete="autoComplete"
    />
</template>

<script>
export default {
    props: {
        label: {
            type: String,
            required: true, 
        },
        value: {
            type: String,
            required: true,
        },
        passwordRule: {
            type: Array,
            required: true,
        },
        showPassword: {
            type: Boolean,
            required: true,
        },
    },
    computed: {
        inputVal: {
            get() {
                return this.value;
            },
            set(newVal) {
                this.$emit("input", newVal);
            },
        },
        autoComplete() {
            return this.inputVal.length > 0 ? "current-password" : "new-password";
        },
    },
    methods: {
        togglePassword() {
            this.showPassword = !this.showPassword;
        },
    },
};    
</script>