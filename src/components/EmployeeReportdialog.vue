<template>
  <div class="">
    <div class="sticky top-20 bg-white z-50">
      <v-tabs
        v-model="activeTab"
        class="bg-white dark-bg-default mb-6"
        :color="$vuetify.theme.currentTheme.primary"
      >
        <v-tab value="employees">Employees</v-tab>
        <v-tab value="teams">Teams</v-tab>
        <v-tabs-slider color="#32D583"></v-tabs-slider>
        <!-- Search -->
        <v-spacer></v-spacer>
        <v-text-field
          v-model="searchQuery"
          placeholder="Search Here..."
          prepend-inner-icon="mdi-magnify"
          dense
          outlined
          hide-details
          class="text-sm mt-2 mr-4"
          style="max-width: 250px"
          clearable
          @input="handleInput"
        ></v-text-field>
      </v-tabs>
    </div>
    <v-card class="report-dialog">
      <div class="p-6">
        <!-- Tabs -->

        <!-- Employee List -->
        <div class="space-y-2">
          <!-- Select All -->
          <div class="flex items-center p-3 bg-gray-50 rounded-lg">
            <v-checkbox
              v-model="selectAll"
              @change="toggleSelectAll"
              hide-details
              class="mr-3"
            ></v-checkbox>
            <span class="text-sm font-medium text-gray-700">Select All</span>
          </div>

          <!-- Employee Items -->
          <div class="h-[400px] overflow-auto" @scroll="handleScroll" ref="scrollContainer">
            <div
              v-for="employee in employees"
              :key="employee.id"
              class="flex items-center p-3 bg-white border border-gray-200"
            >
              <v-checkbox
                v-model="employee.selected"
                @change="updateSelectAll"
                hide-details
                class="mr-3"
              ></v-checkbox>
              <span class="text-sm text-gray-900">{{ employee.name }}</span>
            </div>

            <div v-if="loading" class="text-center py-2 text-gray-600">
              Loading more employees...
            </div>
          </div>
        </div>
      </div>
    </v-card>
  </div>
</template>
<script>
import { debounce } from '@/utils/common'
export default {
  name: 'EmployeeReportDialog',
  data() {
    return {
      reportDialog: false,
      searchQuery: '',
      loading: false,
      allLoaded: false,
      activeTab: 'employees',
      menu: false,
      page: 1,
      employees: [],
      selectAll: false
    }
  },
  props: {
    selectedReport: {
      type: Object,
      default: null
    }
  },
  methods: {
    async loadMoreEmployees() {
      if (this.loading || this.allLoaded) return
      this.loading = true
      console.log('loadMoreEmployees', this.page)
      const newEmployees = await this.fetchEmployees(this.page)

      if (!Array.isArray(newEmployees) || newEmployees.length === 0) {
        this.allLoaded = true
      } else {
        this.employees = [...this.employees, ...newEmployees]
        this.page++
      }

      this.loading = false
    },
    async handleScroll() {
      const container = this.$refs.scrollContainer
      const scrollBottom = container.scrollTop + container.clientHeight
      const threshold = container.scrollHeight - 50

      if (this.loading || this.allLoaded) return

      if (scrollBottom >= threshold) {
        console.log('handleScroll - Loading next page:', this.page)
        await this.loadMoreEmployees()
      }
    },
    handleInput: debounce(async function () {
      this.page = 1
      this.employees = []
      this.allLoaded = false
      await this.loadMoreEmployees()
    }, 300),
    async fetchEmployees(x) {
      try {
        console.log({ x })
        const params = {
          page: x,
          limit: 10
        }
        if (this.searchQuery) {
          params.search = this.searchQuery
        }
        const response = await this.$axios.get('workforce/v2/users', { params })
        const users = response.data.users
        const mappedUsers = users.map((el) => {
          return {
            id: el._id,
            name: el.fullName ? el.fullName : `${el.firstName || ''} ${el.lastName || ''}`,
            selected: false
          }
        })
        console.log('fetchEmployees:', mappedUsers)
        return mappedUsers
      } catch (error) {
        console.error('Error fetching employees:', error)
        return []
      }
    },
    toggleSelectAll() {
      if (Array.isArray(this.employees)) {
        this.employees.forEach((employee) => {
          employee.selected = this.selectAll
        })
      }
    },

    updateSelectAll() {
      if (!Array.isArray(this.employees)) return

      const filteredCount = this.employees.length
      const selectedCount = this.employees.filter((emp) => emp.selected).length
      this.selectAll = filteredCount > 0 && selectedCount === filteredCount
    }
  },
  mounted() {
    // this.fetchEmployees()
    this.loadMoreEmployees()
  }
}
</script>
<style scoped>
.v-btn {
  text-transform: none;
  font-weight: 500;
}

.v-dialog > .v-card > .v-card__text {
  padding: 0;
}

/* Custom checkbox styling */
.v-input--checkbox {
  margin-top: 0;
  padding-top: 0;
}

.v-input--checkbox .v-input__slot {
  margin-bottom: 0;
}
</style>
