<template>
  <div class="passcode-container">
    <div class="passcode-inputs">
      <input
        v-for="(digit, index) in otpField"
        :key="index"
        :type="isPasswordVisible ? 'text' : 'password'"
        maxlength="1"
        :value="digits[index]"
        @input="onInput($event, index)"
        @keydown.delete="onDelete(index)"
        @keydown.left="focusPrev(index)"
        @keydown.right="focusNext(index)"
        ref="inputs"
      />
      <span @click="togglePasswordVisibility" class="toggle-icon">
        <v-icon>{{ isPasswordVisible ? 'mdi-eye' : 'mdi-eye-off' }}</v-icon>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PasscodeInput',
  data() {
    return {
      digits: Array(this.otpField).fill(''),
      isPasswordVisible: false
    }
  },
  props: {
    otpField: { type: Number, default: 6 }
  },
  watch: {
    otpField(newVal) {
      this.digits = Array(newVal).fill('')
    }
  },
  methods: {
    onInput(event, index) {
      const value = event.target.value
      this.digits[index] = value

      if (value && index < this.otpField - 1) {
        this.$nextTick(() => {
          this.$refs.inputs[index + 1].focus()
        })
      }

      this.emitValue()
    },
    onDelete(index) {
      if (!this.digits[index] && index > 0) {
        this.$nextTick(() => {
          this.$refs.inputs[index - 1].focus()
        })
      }
      this.emitValue()
    },
    focusPrev(index) {
      if (index > 0) {
        this.$refs.inputs[index - 1].focus()
      }
    },
    focusNext(index) {
      if (index < this.otpField - 1) {
        this.$refs.inputs[index + 1].focus()
      }
    },
    emitValue() {
      this.$emit('input', this.digits.join(''))
    },
    togglePasswordVisibility() {
      this.isPasswordVisible = !this.isPasswordVisible
    }
  }
}
</script>

<style scoped>
.passcode-container {
  width: 100%;
  margin: 20px 0;
}
.passcode-inputs {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
}
input {
  width: 40px;
  height: 40px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 18px;
}
input:focus {
  outline: none;
  border-color: var(--v-primary-base);
}
.toggle-icon {
  cursor: pointer;
  font-size: 24px;
}
</style>
