<template>
  <div>
    <!-- later we will add this cross origin in both cool box and image tag -->
    <!-- crossorigin="anonymous" -->
    <CoolLightBox :items="images" :index="index" @close="index = null"> </CoolLightBox>
    <div class="images-wrapper">
      <img
        v-for="(image, imageIndex) in images"
        :src="getImageSrc(image)"
        alt="Document"
        @click="index = imageIndex"
        :key="imageIndex"
        :style="{ width: imageWidth, height: imageHeight }"
      />
    </div>
  </div>
</template>

<script>
import CoolLightBox from 'vue-cool-lightbox'
import 'vue-cool-lightbox/dist/vue-cool-lightbox.min.css'
export default {
  components: {
    CoolLightBox
  },
  name: 'LightBox',
  props: {
    imagesData: {
      type: Array,
      required: true
    },
    imageWidth: {
      type: String,
      default: '500px'
    },
    imageHeight: {
      type: String,
      default: '200px'
    }
  },
  mounted() {
    if (this.imagesData) {
      this.images = [...this.images, ...this.imagesData]
    }
    console.log(this.imagesData)
  },
  updated() {
    const imageContainer = document.querySelectorAll('.cool-lightbox__slide__img')
    const imageBtns = document.querySelectorAll('.cool-lightbox-thumbs__list>button')
    if (imageContainer) {
      imageContainer.forEach((el) => {
        el.children[0].setAttribute('crossorigin', 'anonymous')
      })
    }
    if (imageBtns) {
      imageBtns.forEach((el) => {
        el.children[0].setAttribute('crossorigin', 'anonymous')
      })
    }
  },
  data() {
    return {
      images: [],
      index: null
    }
  },
  methods: {
    getImageSrc(image) {
      if (this.isImage(image.src)) {
        return image.src
      } else if (this.isPDF(image.src)) {
        return require('@/assets/img/pdf.png')
      } else if (this.isWord(image.src)) {
        return require('@/assets/img/word.jpg')
      } else {
        return ''
      }
    },

    isImage(src) {
      return /\.(jpg|jpeg|png|gif)$/i.test(src)
    },
    isPDF(src) {
      return /\.pdf$/i.test(src)
    },
    isWord(src) {
      return /\.(doc|docx)$/i.test(src)
    }
  }
}
</script>

<style>
.cool-lightbox .cool-lightbox__slide img {
  height: initial;
  width: initial;
}
.cool-lightbox .cool-lightbox-thumbs .cool-lightbox-thumbs__list .cool-lightbox__thumb {
  background-color: transparent !important;
}
.cool-lightbox .cool-lightbox-thumbs .cool-lightbox-thumbs__list .cool-lightbox__thumb img {
  width: 88%;
  height: 88%;
  object-fit: cover;
}
.images-wrapper {
  max-width: 500px !important;
  border-radius: 8px;
  display: flex;
}
</style>
