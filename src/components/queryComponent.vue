<template>
  <div>
    <transition name="fade">
      <div
        class="absolute bottom-14 right-0 bg-white dark-bg-custom shadow-lg pt-0 rounded-md w-[500px] max-h-[600px] overflow-y-auto border-2 border-[#21427D]"
      >
        <div
          class="flex justify-between items-center mb-2 sticky top-0 z-10 bg-gray-300 dark-bg-default px-4 py-2"
        >
          <h3 class="text-lg font-medium">HELP DESK</h3>
          <v-btn icon @click="togglePopup">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
        <v-card v-if="step === 1" class="border-2 rounded-md dark-bg-custom flex flex-col" flat>
          <div class="p-2">How may I help you ?</div>
          <div class="flex flex-wrap p-2">
            <v-card-title
              v-for="type in types"
              :key="type"
              :class="[
                'm-1 border-2 rounded-md max-w-fit cursor-pointer card-title',
                !selectedType
                  ? 'border-blue-600'
                  : 'border-gray-400 text-gray-400 cursor-not-allowed'
              ]"
              :style="{ pointerEvents: !selectedType ? 'auto' : 'none' }"
              @click="handleSelectType(type)"
            >
              <span class="text-sm">{{ type }}</span>
            </v-card-title>
          </div>
        </v-card>
        <v-form class="flex flex-col gap-4 p-4" ref="form">
          <div v-if="step === 2" class="mb-2 flex flex-col gap-2">
            <v-text-field
              v-model="companyName"
              label="Company Name"
              outlined
              dense
              flat
              hide-details
              class="w-full"
              :rules="[rules.required]"
            ></v-text-field>

            <v-text-field
              v-model="contactPerson"
              label="Contact Person"
              outlined
              dense
              flat
              hide-details
              class="w-full"
              :rules="[rules.required]"
            ></v-text-field>

            <v-text-field
              v-model="contactEmail"
              label="Contact Email"
              outlined
              dense
              flat
              hide-details
              class="w-full"
              :rules="[rules.required, rules.email]"
            ></v-text-field>

            <v-text-field
              v-model="phoneNumber"
              label="Contact Phone Number"
              outlined
              dense
              flat
              hide-details
              type="number"
              class="w-full"
              :rules="[rules.required, rules.mobile]"
            ></v-text-field>
            <div class="w-full flex justify-end gap-2">
              <v-btn outlined @click="prevStep">Previous</v-btn>
              <v-btn
                :color="$vuetify.theme.currentTheme.primary"
                class="white--text"
                @click="nextStep"
                >Next</v-btn
              >
            </div>
          </div>

          <div v-else-if="step === 3" class="mb-2 flex flex-col gap-2">
            <div class="flex flex-col gap-2">
              <v-text-field
                v-model="query"
                label="Enter your query"
                dense
                outlined
                hide-details
                single-line
                class="w-full"
                :rules="[rules.required]"
              ></v-text-field>
              <v-textarea
                v-model="detailsTemplate"
                label="Query Details"
                outlined
                auto-grow
                hide-details
                class="w-full"
                :rules="[rules.required]"
              ></v-textarea>

              <div
                @dragover.prevent
                @dragenter.prevent
                @drop.prevent="handleDrop"
                :class="['drag-drop-area', { 'cursor-not-allowed': attachments.length >= 2 }]"
                class="drag-drop-area p-4 border-2 border-dashed border-[#21427D] bg-[#f8f9fa] dark-bg-custom rounded-md text-center cursor-pointer"
                @click="attachments.length < 2 && triggerFileInput()"
              >
                <v-icon size="48">mdi-upload</v-icon>
                <p v-if="!fileNames.length">Drag & Drop your files here or click to select</p>
                <p v-else>{{ fileNames.join(', ') }}</p>
                <input
                  ref="fileInput"
                  type="file"
                  class="hidden"
                  multiple
                  accept="image/*"
                  @change="handleFileChange"
                  :disabled="attachments.length >= 2"
                />
              </div>

              <div class="flex justify-end gap-2">
                <v-btn outlined @click="prevStep">Previous</v-btn>
                <v-btn
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text"
                  @click="handleSubmit"
                  >Submit</v-btn
                >
              </div>
            </div>
          </div>
          <div v-else-if="step === 4" class="flex flex-col items-center p-4">
            <v-icon color="green" size="64">mdi-check-circle</v-icon>
            <h3 class="text-lg font-semibold text-green-600">Submission Successful!</h3>
            <p class="text-sm">Your request has been successfully submitted.</p>
            <v-btn color="primary" @click="togglePopup" class="mt-4"> Close </v-btn>
          </div>
        </v-form>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  data() {
    return {
      step: 1,
      showPopup: false,
      query: '',
      selectedType: '',
      types: ['TECH_SUPPORT', 'COMPLAINT'],
      detailsTemplate: '',
      companyName: '',
      contactPerson: '',
      contactEmail: '',
      phoneNumber: '',
      attachments: [],
      rules: {
        required: (v) => !!v || 'This field is required',
        email: (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
        number: (v) => (!isNaN(parseFloat(v)) && isFinite(v)) || 'Must be a number',
        mobile: (v) =>
          (/^[0-9]{10}$/.test(v) && v.length === 10) || 'Please enter a valid Mobile number'
      },
      fileNames: [],
      orgId: '',
      tokenData: this.$storage.getUniversal('token')
    }
  },
  methods: {
    handleSelectType(type) {
      this.selectedType = type
      if (this.step === 1) {
        if (this.contactPerson && this.phoneNumber) {
          this.step = 3
        } else {
          this.step++
        }
      }
    },
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
    handleDrop(e) {
      const files = e.dataTransfer.files
      this.processFiles(files)
    },
    handleFileChange(e) {
      const files = e.target.files
      this.processFiles(files)
    },
    processFiles(files) {
      if (this.attachments.length + files.length > 2) {
        this.$toast.error('You can only upload up to 2 files.')
        return
      }
      for (let i = 0; i < files.length; i++) {
        if (files[i].type.startsWith('image/')) {
          this.attachments.push(files[i])
          this.fileNames.push(files[i].name)
        }
      }
      // console.log("this attatchment", this.attachments);
    },
    togglePopup() {
      this.$emit('handleCloseComplaint', false)
    },
    nextStep() {
      if (this.$refs.form.validate()) {
        this.step++
      } else {
        this.$toast.error('Please fill out all required fields')
      }
    },
    prevStep() {
      if (this.step === 3 && this.contactPerson && this.phoneNumber) {
        this.step = 1
      } else {
        this.step--
      }
      this.selectedType = ''
    },
    async handleSubmit() {
      if (this.$refs.form.validate()) {
        try {
          const formData = new FormData()
          this.attachments.forEach((file) => {
            formData.append('attachments', file)
          })
          formData.append('typeOfQuery', this.selectedType)
          formData.append('details', this.detailsTemplate)
          formData.append('companyName', this.companyName)
          formData.append('contactPerson', this.contactPerson)
          formData.append('contactEmail', this.contactEmail)
          formData.append('contactNumber', this.phoneNumber)
          formData.append('orgId', this.orgId)
          this.step = 4
          const response = await this.$axios.post('/workforce/cualitywork/query', formData)

          if (response.data.success) {
            this.resetForm()
            this.togglePopup()
            this.$toast.success(response?.data?.message)
          }
        } catch (error) {
          const errorMessage = error?.response?.data?.message || 'Something went wrong'
          this.$toast.error(errorMessage)
          console.log('error', error)
        }
      }
    },
    resetForm() {
      this.selectedType = ''
      this.detailsTemplate = ''
      this.companyName = ''
      this.contactPerson = ''
      this.contactEmail = ''
      this.phoneNumber = ''
      this.attachments = []
      this.fileNames = []
      this.step = 1
    }
  },
  mounted() {
    if (!this.tokenData) {
      this.types.push('REQUEST_GUEST_CREDENTIAL')
    }
    const orgData = JSON.parse(localStorage.getItem('orgData'))
    if (orgData) {
      this.companyName = orgData?.name
      this.orgId = orgData?.id
    }
    const userRole = JSON.parse(localStorage.getItem('user'))
    if (userRole) {
      this.phoneNumber = String(userRole?.user?.mobile)
      this.contactEmail = userRole?.user?.email
      this.contactPerson = `${userRole?.user?.first_name} ${userRole?.user?.last_name}`
    }
  }
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.drag-drop-area {
  /* background-color: #f8f9fa; */
  transition: background-color 0.2s;
}

.drag-drop-area:hover {
  background-color: #e9ecef;
}

.dark .drag-drop-area:hover {
  background-color: var(--bg-hover);
}

.hidden {
  display: none;
}

.card-title.v-card__title {
  padding: 8px;
}
</style>
