import Vue from "vue";
import App from "./App.vue";
import vuetify from "./plugins/vuetify";
import './assets/tailwind.css';
import router from "./router";
import "./plugins/axios";
import "./plugins/dayjs";
import storage from './plugins/storage';
import {store} from "./store/index"
import './plugins/toast';
import "@/assets/global.css";
import "./plugins/toolTipPlugin"

Vue.config.productionTip = false;
Vue.use(storage);


new Vue({
  store,
  router,
  vuetify,
  render: (h) => h(App),
}).$mount("#app");
