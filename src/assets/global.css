:root {
  --bg-color-light: #ffffff;
  --bg-color-dark: #2a2a2a;
  --bg-custom: #2a2a2a;
  --bg-default: #1f1f1f;
  --bg-hover: #333333;
}

.dark .dark-bg-custom {
  background-color: #2a2a2a;
}

.dark .dark-text-color {
  color: #ffffff;
}

.dark .dark-bg-default {
  background-color: #1f1f1f;
}

.dark .dark-bg-modal {
  background-color: #2a2a2a;
}

.dark .dark-icon-bg {
  background-color: #2a2a2a;
}

.dark .dark-icon-bg:hover {
  background-color: black;
}

.dark .dark-bg-expansion {
  background-color: #4a4747;
}

.dark .dark-bg-gradient {
  background: linear-gradient(to right, #333333, #333333);
}

.dark .dark-text-color {
  color: #f3f3f3;
}

.dark .dark-hover-text {
  color: #4a95da;
}
.close-toast-icon {
  color: white !important;
  font-size: 24px !important;
  margin: 0 !important;
  text-decoration: none !important;
  line-height: 0px !important;
  margin-bottom: 6px !important;
}

.dark p {
  color: #f3f3f3;
}

.dark h3 {
  color: #f3f3f3;
}

.dark span {
  color: #f3f3f3;
}

.dark a {
  color: #4a95da;
}

.dark h2 {
  color: #f3f3f3;
}

.theme--dark.v-list-item.v-list-item--highlighted::before {
  color: #4a95da !important;
  caret-color: #4a95da !important;
  opacity: 0.24;
}

.theme--dark.v-list--dense .v-list-item .v-list-item__content {
  color: white !important;
}

.theme--dark.theme--dark.v-list-item.select-all-item {
  background-color: #4a95da !important;
}

.theme--dark.v-list-item:not(.v-list-item--active):not(.v-list-item--disabled) {
  background-color: transparent !important;
}

.v-btn.v-date-picker-table__current.v-btn--rounded.v-btn--outlined.theme--dark.accent--text {
  color: white !important;
  caret-color: white !important;
}

.v-btn.v-size--default.v-date-picker-table__current.v-btn--outlined.theme--dark.accent--text {
  color: white !important;
  caret-color: white !important;
}
