<template>
  <v-app class="back" :class="{ dark: isDarkMode }">
    <!-- side nav -->
    <v-navigation-drawer
      class="wfm-navbar"
      v-model="drawer"
      :mini-variant="miniVariant"
      :clipped="clipped"
      fixed
      :color="$vuetify.theme.currentTheme.navigationColor"
      app
    >
      <div class="mt-5 mb-2 font-bold">
        <div class="flex justify-center items-start">
          <div class="image-container relative flex items-start justify-center">
            <v-icon class="edit-btn dark-icon-bg" @click="openImageUploadDialog">mdi-pen</v-icon>
            <input type="file" ref="fileInput" style="display: none" @change="handleImageUpload" />
            <template v-if="orgData">
              <div class="p-2">
                <img class="ml-0" width="150px" v-if="orgData.logo" :src="getLogoImage" />
                <img class="ml-0" width="150px" v-else src="@/assets/img/cualityWork.png" />
              </div>
            </template>
            <template v-else>
              <v-skeleton-loader height="80" type="card" width="150"></v-skeleton-loader>
            </template>
          </div>
        </div>
      </div>
      <v-list class="scrollable-list">
        <template v-for="(item, i) in menuItems">
          <v-list-item
            :class="{
              'selected-menu': isSelected(i)
            }"
            :key="i"
            :to="item.to"
            router
            exact
            v-if="item.visible && item.to"
            :color="$vuetify.theme.currentTheme.primary"
          >
            <v-list-item-action>
              <v-icon size="20" :color="isSelected(i) ? 'white' : 'grey'">{{ item.icon }}</v-icon>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title
                class=""
                :class="isSelected(i) ? 'text-white' : 'text-blue-950 dark-text-color'"
                v-text="item.title"
              />
            </v-list-item-content>
          </v-list-item>

          <v-list-group
            :value="Boolean($route.params.subCase)"
            :class="{
              'selected-menu': isSelected(i)
            }"
            :key="i"
            v-else-if="item.childRoutes"
            :prepend-icon="item.icon"
            :color="$vuetify.theme.currentTheme.primary"
          >
            <template v-slot:activator>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
            </template>

            <v-list-item
              v-for="({ title, icon, to }, j) in item.childRoutes"
              :key="j"
              :class="{
                'hover:bg-blue-100 hover:dark-icon-bg': !$route.path.startsWith(to),
                'selected-menu': $route.path.startsWith(to)
              }"
              :to="to"
              router
              exact
              :color="$vuetify.theme.currentTheme.primary"
            >
              <v-icon
                size="20"
                class="ml-6 mr-4"
                :color="$route.path.startsWith(to) ? 'white' : 'grey'"
                >{{ icon }}</v-icon
              >
              <v-list-item-title
                :class="isSelected(i) ? 'text-white' : 'text-blue-950 dark-text-color'"
                >{{ title }}</v-list-item-title
              >
            </v-list-item>
          </v-list-group>
        </template>
      </v-list>
      <!-- <v-btn
        class="fixed-bottom-btn"
        @click="downloadApp"
        style="background-color: #3b3b3b; color: white"
      >
        Download Employee App
        <v-icon class="empAppIcon" large>mdi-android</v-icon>
      </v-btn> -->
    </v-navigation-drawer>
    <!-- main header -->
    <v-app-bar
      :clipped-left="isClipped"
      fixed
      dense
      class="flex justify-between"
      :color="$vuetify.theme.currentTheme.appBarColor"
      app
    >
      <div class="flex items-center justify-between">
        <v-toolbar-title class="white--text" v-text="title" />
      </div>
      <div v-if="showGlobalSearch">
        <GlobalSearch :enableFetch="allowEnableFetch" />
      </div>

      <div class="flex justify-end items-center">
        <div class="transition-colors duration-500">
          <div class="flex justify-end mr-4">
            <button @click="toggleDarkMode" class="focus:outline-none text-white">
              <span v-if="isDarkMode" class="mdi mdi-weather-sunny text-2xl"></span>
              <span v-else class="mdi mdi-weather-night text-2xl"></span>
            </button>
          </div>
        </div>
        <div class="notification mr-4">
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <div v-bind="attrs" v-on="on" v-show="isbellIconEnabled('NOTIFICATIONS')">
                <v-badge color="red" overlap :content="alertsCount" v-if="alertsCount">
                  <v-icon class="ml-2 white--text" @click="toggleDropdown">mdi-bell</v-icon>
                </v-badge>
                <v-icon class="ml-2 white--text" @click="toggleDropdown" v-else>mdi-bell</v-icon>
              </div>
            </template>

            <v-card class="w-[28rem] max-h-[40rem]">
              <v-list class="mt-4 w-full">
                <v-list-item v-for="(notification, index) in alerts.slice(0, 4)" :key="index">
                  <v-alert
                    :type="notification.type"
                    class="w-full p-1 text-base"
                    :color="categoryColor(notification.notificationGenric)?.bgColor"
                    text
                    outlined
                  >
                    {{ notification.message }}
                  </v-alert>
                </v-list-item>
                <v-list-item v-if="!alerts.length">
                  <v-alert type="warning" color="blue" class="w-full p-1 text-base">
                    No alerts
                  </v-alert>
                </v-list-item>
                <v-list-item
                  @click="handleSeeAll"
                  class="cursor-pointer flex justify-center hover:bg-slate-200 hover:dark-icon-bg dark-text-color mx-4 rounded"
                >
                  See all &nbsp;
                  <span v-if="this.alertsCount > 4"> (+{{ this.alertsCount - 4 }} more)</span>
                </v-list-item>
              </v-list>
            </v-card>
          </v-menu>
        </div>

        <div class="user-menu">
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn icon v-bind="attrs" v-on="on" class="custom-btn">
                <div>
                  <img
                    v-if="user?.user?.profile_image"
                    :src="getEmployeeImage()"
                    alt="Employee Image"
                    class="border border-white-500 rounded-full h-7 w-7 inline -mt-1 mr-2"
                  />
                  <v-icon v-else class="border border-white-500 rounded-full white--text mr-2"
                    >mdi-account</v-icon
                  >
                  <span class="white--text capitalize">Hi, {{ user_name }} </span>
                </div>
              </v-btn>
            </template>
            <v-list>
              <v-list-item to="/workforce/profile">Profile</v-list-item>
              <v-list-item to="/workforce/org">Organization</v-list-item>
              <v-list-item @click="logout()" class="border-b">Logout</v-list-item>
              <v-list-item @click.prevent="openInNewTab"
                ><span class="colorText">About Product</span></v-list-item
              >
            </v-list>
          </v-menu>
        </div>
      </div>
    </v-app-bar>
    <!-- main content -->
    <v-main class="bg-[#F7FAFB] dark-bg-default">
      <div class="flex gap-2 my-1">
        <v-app-bar-nav-icon
          :color="$vuetify.theme.currentTheme.primary"
          @click.stop="drawer = !drawer"
          class="no-animation"
        />
        <div v-show="isCallingAllowed('CALL_A_CUSTOMER')" class="flex justify-between">
          <!-- <div class="flex items-center gap-2 cursor-pointer max-w-fit" @click="openDialerCard">
            <div
              class="cursor-pointer border-2 border-[#21427D] rounded-[50%] w-6 h-6"
              style="display: flex; justify-content: center"
            >
              <v-icon :color="$vuetify.theme.currentTheme.primary" style="font-size: 16px"
                >mdi-phone</v-icon
              >
            </div>
            <p class="mb-0 p-0 text-[#21427D]">Call a customer</p>
          </div> -->
          <RightDrawer
            @openCallCutomer="openDialerCard"
            @openHelpDesk="openComplaintCard"
            @downloadEmpApp="downloadApp"
          />
        </div>

        <div class="dialer-overlay" v-if="isDialerOpen">
          <div class="dialer-container">
            <DialerPopOver @handleCloseDialer="openDialerCard" />
          </div>
        </div>

        <!-- <div class="help-desk-container">
          <transition name="fade">
            <div v-show="isHelpTextVisible" class="help-text-container">
              <span class="help-text">How can I help you?</span>
            </div>
          </transition>
          <v-btn
            icon
            :color="$vuetify.theme.currentTheme.primary"
            @click="openComplaintCard"
            class="shadow-lg help-desk-btn"
          >
            <v-icon>mdi-comment-processing-outline</v-icon>
          </v-btn>
        </div> -->
        <div class="query-overlay" v-if="isComplaintOpen">
          <div class="query-container">
            <QueryComponent @handleCloseComplaint="openComplaintCard" />
          </div>
        </div>
      </div>
      <div class="px-2 py-2 h-5/6">
        <router-view />
      </div>
    </v-main>
    <v-dialog v-model="planStatus" persistent max-width="450">
      <v-card class="text-center pa-6">
        <v-img
          src="https://cdn-icons-png.flaticon.com/512/6179/6179025.png"
          alt="Plan Expired"
          class="mx-auto mb-4 bg-white"
          max-width="120"
        />
        <v-card-title class="headline ml-16">Your plan has expired</v-card-title>
        <v-card-text>
          Your access to this service has been temporarily suspended due to an expired plan. Please
          contact Cuality-Work admin ( <EMAIL> ) to renew your plan and regain
          full access.
          <br />
          Rest assured, all your data and settings are saved and secured.
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            small
            @click="logout"
            >Log Out</v-btn
          >
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-app>
</template>

<script>
import {
  hasPermission,
  convertToTitleCase,
  isModuleVisible,
  isModuleFeatureAllowed
} from '@/utils/common'
import permissionData from '@/utils/permissions'
import { reformatNotification } from '@/utils/workforce/utils'
import {
  NOTIFICATION_CHANNEL,
  PUSHER_CLIENT_LIB,
  routeConfig
  // USER_GUIDE_LINK,
} from '@/utils/workforce/constants'
import { notificationColor } from '@/utils/workforce/statusColors'
import DialerPopOver from '@/components/DialerPopOver.vue'
import QueryComponent from '@/components/queryComponent.vue'
import GlobalSearch from '@/components/GlobalSearch.vue'
import RightDrawer from '@/components/RightDrawer'
import Pusher from 'pusher-js'
// import "floating-vue/dist/style.cs s";

export default {
  name: 'DefaultLayout',
  data() {
    return {
      orgCode: this.$storage.getUniversal('organization_code'),
      userData: this.$store.state.wfmPermissions,
      userOrg: this.$storage.getUniversal('user'),
      orgLang: this.$store.state.orgLanguage,
      clipped: true,
      drawer: true,
      fixed: false,
      miniVariant: false,
      right: true,
      user_name: '',
      menuItems: [],
      orgInfo: null,
      currentDate: this.getCurrentDate(),
      searchQuery: '',
      user: {},
      baseUrlS3: process.env.VUE_APP_S3_BASE_URL,
      dropdownVisible: false,
      alerts: [],
      alertsCount: 0,
      useCases: [],
      orgData: null,
      isDialerOpen: false,
      isComplaintOpen: false,
      planStatus: false,
      isDarkMode: false,
      theme: 'light',
      isLoad: false,
      isHelpTextVisible: true,
      allSubCases: []
    }
  },
  components: {
    DialerPopOver,
    QueryComponent,
    GlobalSearch,
    RightDrawer
  },

  created() {
    if (typeof window !== 'undefined') {
      this.isDarkMode = localStorage.getItem('theme') === 'dark'
      this.applyTheme()
      this.isLoad = true
    }
  },

  watch: {
    // orgInfo(nextObj) {
    //   if (
    //     !nextObj?.organization_config?.is_corporate_expense_enabled &&
    //     this.$route.path === "/CorporateExpenses"
    //   ) {
    //     this.$router.push("/workforce/dashboard");
    //   }
    // },
  },
  computed: {
    title() {
      const orgName = this.orgData?.name || ''
      return orgName && `${orgName.charAt(0).toUpperCase()}${orgName.slice(1).toLowerCase()}`
    },
    isClipped() {
      return this.clipped
    },
    selectedItemIndex() {
      return this.menuItems.findIndex((item) => {
        if (item.to && this.$route.path.startsWith(item.to)) {
          return true
        }
        return false
      })
    },
    orgLanguage() {
      return this.orgLang.data.leftNav
    },
    sparePartEnabled() {
      return (
        hasPermission(this.userData, permissionData.inventoryRead) &&
        isModuleVisible('STOCK_MANAGEMENT')
      )
    },
    amcEnabled() {
      return hasPermission(this.userData, permissionData.amcRead) && isModuleVisible('AMC')
    },
    isCollectionEnabled() {
      return isModuleVisible('COLLECTION_MANAGEMENT')
    },
    checkRouteVisibility() {
      return (
        this.$route.path == '/workforce/leadspage/inventory' &&
        !hasPermission(this.userData, permissionData.leadRead) &&
        this.$route.query?.complaint_no
      )
    },
    getLogoImage() {
      if (this.orgData?.logo) {
        return `${process.env.VUE_APP_S3_BASE_URL + this.orgData?.logo}`
      }
      return null
    },
    isEveryTabsInModule() {
      return (
        isModuleFeatureAllowed('ATTENDANCE', 'MANAGE_ATTENDANCE') ||
        isModuleFeatureAllowed('ATTENDANCE', 'MANAGE_ROSTER')
      )
    },
    allowEnableFetch() {
      if (
        this.$route.path.startsWith('/workforce/collection-management') ||
        this.$route.path.startsWith('/workforce/loan_management')
      ) {
        return true
      } else {
        return false
      }
    },
    showGlobalSearch() {
      const currentRoute = Object.values(routeConfig).find((route) =>
        this.$route.path.includes(route.path)
      )
      return currentRoute?.visibility === true
    }
  },
  beforeDestroy() {
    // Clear the interval to avoid memory leaks
    clearInterval(this.helpTextInterval)
  },
  async mounted() {
    await this.startHelpTextLoop()
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      this.isDarkMode = savedTheme === 'dark'
      this.applyTheme()
    } else {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      this.isDarkMode = prefersDark
      this.applyTheme()
    }
    await this.getOrgData()
    this.loadExternalScripts()
    this.fetchNotifications()
    this.initFb()
    const _this = this
    setInterval(function () {
      const user = _this.$storage.getUniversal('user')
      if (user) {
        _this.user_name = user.user.first_name
      }
      if (user?.role[0] === 'SUPER_ADMIN') {
        _this.title = 'Super Admin Panel'
      }
    }, 100)
    const userData = this.$storage.getUniversal('user')
    this.user = userData
    const scope = userData?.scope
    const dynamicNavLinks = await this.getDynamicNavLinks()
    await this.fetchAllUseCases() // Make sure to fetch use cases before creating menu items

    this.menuItems = [
      {
        id: 'dashboard',
        icon: 'mdi-apps',
        title: this.orgLanguage.dashboard,
        to: '/workforce/dashboard',
        visible:
          hasPermission(this.userData, permissionData.userRead) && isModuleVisible('DASHBOARD')
      },
      {
        id: 'user',
        icon: 'mdi-account-group',
        title: 'Organization',
        to: '/workforce/organization',
        visible:
          hasPermission(this.userData, permissionData.userRead) && isModuleVisible('EMPLOYEES')
      },
      {
        id: 'organization',
        icon: 'mdi-clock-time-four',
        title: 'Attendance',
        to: '/workforce/team-review',
        visible:
          hasPermission(this.userData, permissionData.attendenceRead) &&
          isModuleVisible('ATTENDANCE') &&
          this.isEveryTabsInModule
      },
      ...dynamicNavLinks,
      ...this.getUseCaseMenuItems(),
      {
        id: 'customers',
        icon: 'mdi-account-tie',
        title: 'Customers',
        to: '/workforce/product-customers',
        visible:
          hasPermission(this.userData, permissionData.customerRead) &&
          !isModuleVisible('COLLECTION_MANAGEMENT') &&
          isModuleVisible('CUSTOMERS') &&
          this.orgCode !== 'S4UAMM'
      },
      {
        id: 'customers',
        icon: 'mdi-account-supervisor',
        title: 'Customers',
        to: '/workforce/customers',
        visible:
          hasPermission(this.userData, permissionData.customerRead) &&
          isModuleVisible('COLLECTION_MANAGEMENT') &&
          isModuleVisible('CUSTOMERS') &&
          this.orgCode !== 'S4UAMM'
      },
      {
        id: 'inventory-partners',
        icon: 'mdi-handshake',
        title: 'Lenders',
        to: '/workforce/partners',
        visible: true
      },
      {
        id: 'workflow-steps',
        icon: 'mdi-source-branch',
        title: 'Workflows',
        to: '/workforce/workflow-steps',
        visible: true
      },
      {
        id: 'campaigns',
        icon: 'mdi-bullhorn',
        title: 'Campaigns',
        to: '/workforce/campaigns',
        visible: this.isCallingAllowed('CALL_A_CUSTOMER') && this.orgCode !== 'S4UAMM'
      },
      {
        id: 'inventory-management',
        icon: 'mdi-package-variant',
        title: this.orgLanguage.partManagement,
        to: '/workforce/stock-management',
        visible: this.sparePartEnabled
      },
      {
        id: 'inventory-settings',
        icon: 'mdi-cog-box',
        title: this.orgLanguage.partSettings,
        to: '/workforce/inventory-settings',
        visible: this.sparePartEnabled
      },
      {
        id: 'amc-management',
        icon: 'mdi-shield-check',
        title: 'AMC',
        to: '/workforce/amc',
        visible: this.amcEnabled
      },
      {
        id: 'report-management',
        icon: 'mdi-file-chart',
        title: 'Reports',
        to: '/workforce/reports',
        visible: this.orgCode === 'KWALTY'
      },
      {
        id: 'report-management',
        icon: 'mdi-form-select',
        title: 'Dynamic Forms',
        to: '/workforce/dynamic-responses',
        visible: true
      },
      {
        id: 'notification',
        icon: 'mdi-bell-badge',
        title: this.orgLanguage.notifications,
        to: '/workforce/notifications',
        visible:
          hasPermission(this.userData, permissionData.notificationRead) &&
          isModuleVisible('NOTIFICATIONS')
      },
      {
        id: 'org-settings',
        icon: 'mdi-cog-outline',
        title: this.orgLanguage.settings,
        to: '/workforce/settings',
        visible:
          hasPermission(this.userData, permissionData.settingRead) && isModuleVisible('SETTINGS')
      },
      {
        id: 'corporate-expenses',
        icon: 'mdi-cash-multiple',
        title: 'Corporate Expenses',
        to: '/workforce/corporate-expenses',
        visible:
          scope.indexOf('ADMIN_ACCESS') > -1 &&
          userData.role.indexOf('ORG_ADMIN') > -1 &&
          isModuleVisible('CLAIMS')
      }
    ]

    await this.setupMenuItems()
    await this.fetchEmployees()
  },
  methods: {
    getRouteById(id) {
      const menuItem = this.menuItems.find((item) => item.id === id)
      return menuItem ? menuItem.to : null
    },
    toggleDarkMode() {
      this.isDarkMode = !this.isDarkMode
      this.applyTheme()
    },
    applyTheme() {
      const theme = this.isDarkMode ? 'dark' : 'light'
      document.documentElement.classList.toggle('dark', this.isDarkMode)
      this.$vuetify.theme.dark = this.isDarkMode
      localStorage.setItem('theme', theme)
      this.$root.$emit('modeChange')
      this.isLoad = true
    },
    initFb() {
      window.fbAsyncInit = function () {
        if (window.FB) {
          window.FB.init({
            appId: '1622165388539821',
            cookie: true,
            xfbml: true,
            version: 'v19.0'
          })
          window.FB.AppEvents.logPageView()
        }
      }
      ;(function (d, s, id) {
        var js,
          fjs = d.getElementsByTagName(s)[0]
        if (d.getElementById(id)) {
          return
        }
        js = d.createElement(s)
        js.id = id
        js.src = 'https://connect.facebook.net/en_US/sdk.js'
        js.nonce = 'kyHuC4JA'
        fjs.parentNode.insertBefore(js, fjs)
      })(document, 'script', 'facebook-jssdk')
    },
    async getDynamicNavLinks() {
      const config = await this.getConfig()
      const links = []

      if (config.enabledSubCases?.length && isModuleVisible('COLLECTION_MANAGEMENT')) {
        config.enabledSubCases.forEach((subCaseItem) => {
          const subCasesLink = {
            id: 'collection-management',
            icon: 'mdi-bank',
            title: 'Teams',
            visible: isModuleVisible('COLLECTION_MANAGEMENT'),
            childRoutes: []
          }

          if (this.userOrg?.branch && !this.userOrg?.role?.includes('WFM_ADMIN')) {
            const filteredSubCases = subCaseItem?.subCase?.filter(
              (sub) => sub?.branch === this.userOrg?.branch
            )
            filteredSubCases.forEach((subCase) => {
              subCasesLink.childRoutes.push({
                id: `subcase-${subCase.name}`,
                icon: 'mdi-cash-multiple',
                title: convertToTitleCase(subCase.name) || 'tet test',
                to: `/workforce/collection-management/${subCase._id}`
              })
            })
          } else {
            subCaseItem.subCase.forEach((subCase) => {
              subCasesLink.childRoutes.push({
                id: `subcase-${subCase.name}`,
                icon: 'mdi-cash-multiple',
                title: convertToTitleCase(subCase.name) || 'tet test',
                to: `/workforce/collection-management/${subCase._id}`
              })
            })
          }

          links.push(subCasesLink)
        })
      }

      const modules = [
        {
          key: 'PROJECT_MANAGEMENT',
          icon: 'mdi-bag-checked',
          usecase: 'project',
          title: 'Project',
          permission: permissionData.projectRead
        },
        {
          key: 'SELLS',
          icon: 'mdi-shopping',
          usecase: 'inventory',
          title: 'Products',
          permission: permissionData.inventoryRead
        },
        {
          key: 'LEAD_MANAGEMENT',
          icon: 'mdi-bag-checked',
          usecase: 'default',
          title: 'Lead',
          permission: permissionData.leadRead
        }
      ]

      modules.forEach((module) => {
        if (module.key) {
          const moduleVisible =
            hasPermission(this.userData, module.permission) && isModuleVisible(module.key)
          links.push({
            id: `${module.key.toLowerCase()}-${module.title}`,
            icon: module.icon,
            title: module.title,
            to: `/workforce/leadspage/${module.usecase.toLowerCase()}`,
            visible: moduleVisible
          })
        }
      })

      return links
    },
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config
        this.useCases = data.enabledUseCases
        this.$store.dispatch('setSubCases', data.enabledSubCases)
        this.$store.dispatch('setUseCase', this.useCases)
        const obj = {
          directComplete: data.isTaskDirectCompletionAllowedOrg,
          recurAllow: data.isTaskRecurrenceAllowedOrg,
          isCheckedInSelfie: data.isCheckedInSelfie,
          isCheckedOutSelfie: data.isCheckedOutSelfie
        }
        const jsonString = JSON.stringify(obj)
        localStorage.setItem('task', jsonString)
        return data || {}
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    toggleDropdown() {
      this.dropdownVisible = !this.dropdownVisible
    },
    handleSeeAll() {
      this.$router.push('/workforce/notifications')
    },
    async logout() {
      const exportColumnKey = localStorage.getItem('exportColumnSelectedKey')
      const oldUserData = localStorage.getItem('user')
      const oldOrganizationCode = localStorage.getItem('organization_code')
      localStorage.clear()
      if (exportColumnKey) {
        localStorage.setItem('exportColumnSelectedKey', exportColumnKey)
      }
      localStorage.setItem('user', oldUserData)
      localStorage.setItem('organization_code', oldOrganizationCode)
      this.$store.dispatch('resetWfmPermissions')
      this.$store.dispatch('resetOrgLanguage')
      this.$store.dispatch('resetSubCase')
      // this.$storage.removeCookie("token"); // Uncomment if token needs to be removed
      this.$router.push('/login')
    },
    async setupMenuItems() {
      let isAnyItemVisible = false

      for (const item of this.menuItems) {
        if (item.visible) {
          isAnyItemVisible = true
        }

        if (!item.visible && this.$route.path === item.to) {
          this.$router.push('/workforce/dashboard')
        }
      }

      if (!isAnyItemVisible) {
        this.$toast.error("You don't have permission to access the admin portal")
        await this.logout()
        return
      }

      if (this.checkRouteVisibility) {
        const complainRoute = `${this.$route.path}?complaint_no=${this.$route.query?.complaint_no}`
        this.$router.push(complainRoute)
        return
      }
    },
    getCurrentDate() {
      const dateObj = new Date()
      const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }
      return dateObj.toLocaleDateString('en-US', options)
    },
    isSelected(index) {
      const item = this.menuItems[index]

      if (item.to) {
        return this.$route.path.startsWith(item.to)
      }

      return false
    },
    getEmployeeImage() {
      return `${this.baseUrlS3 + this.user?.user.profile_image}`
    },
    openInNewTab() {
      window.open('https://www.cualitywork.com/', '_blank')
    },
    loadExternalScripts() {
      const pusherScript = document.createElement('script')
      pusherScript.src = PUSHER_CLIENT_LIB
      pusherScript.async = true
      pusherScript.onload = () => {
        this.initPusher()
      }
      document.head.appendChild(pusherScript)
    },

    initPusher() {
      const userId = this.userOrg._id

      Pusher.logToConsole = false
      const pusher = new Pusher(process.env.VUE_APP_PUSHER_ID, {
        cluster: process.env.VUE_APP_PUSHER_CLUSTER
      })
      const channel = pusher.subscribe(`${NOTIFICATION_CHANNEL}-${userId}`)
      channel.bind(NOTIFICATION_CHANNEL, (data) => {
        this.alertsCount = data?.pagination.totalCount
        this.alerts = [...reformatNotification(data?.notifications)]
      })
    },

    async fetchNotifications() {
      try {
        const response = await this.$axios.get('/workforce/notifications')
        this.alertsCount = response.data?.pagination?.totalCount
        this.alerts = [...reformatNotification(response.data?.notifications)]
      } catch (error) {
        console.error('Error fetching notifications:', error)
      }
    },
    categoryColor(generic) {
      const colorInfo = notificationColor[generic]
      return colorInfo
    },
    openImageUploadDialog() {
      this.$refs.fileInput.click()
    },
    async handleImageUpload(event) {
      const file = event.target.files[0]
      const formData = new FormData()
      formData.append('attachments', file)
      try {
        const response = await this.$axios.post('/workforce/org/logo', formData)

        if (response.data.success) {
          const isUpdate = await this.updateOrg(response.data.url)
          if (isUpdate) {
            this.getOrgData()
            this.$toast.success('Image uploaded successfully')
          }
        } else {
          this.$toast.error('Failed to upload image')
        }
      } catch (error) {
        console.error('Error uploading image:', error)
        const errorMessage = error.response.data.message || 'Failed to upload image'
        this.$toast.error(errorMessage)
      }
    },
    async updateOrg(url) {
      const payload = {
        orgCode: this.orgCode,
        name: this.orgData?.name,
        logo: url.doc_physical_path
      }
      try {
        const response = await this.$axios.put('/workforce/org/info', payload)

        if (response.data.success) {
          return true
        } else {
          return false
        }
      } catch (error) {
        console.error('Error updating org.', error)
      }
    },
    async getOrgData() {
      try {
        const response = await this.$axios.get('/organizations')
        this.orgData = response.data[0]
        if (this.orgData.plan_status && this.orgData.plan_details) {
          this.planStatus = this.orgData.plan_status === 'active' ? false : true
        }

        localStorage.setItem('orgData', JSON.stringify(this.orgData))
      } catch (error) {
        console.error('Error getting organization data.', error)
      }
    },
    openDialerCard() {
      this.isDialerOpen = !this.isDialerOpen
    },
    openComplaintCard() {
      this.isComplaintOpen = !this.isComplaintOpen

      // When complaint card is opened, stop the help text from showing
      if (this.isComplaintOpen) {
        this.isHelpTextVisible = false
        clearInterval(this.helpTextInterval) // Stop the interval when the dialog opens
      } else {
        // Restart the help text loop when the dialog closes
        this.startHelpTextLoop()
      }
    },
    startHelpTextLoop() {
      this.helpTextInterval = setInterval(() => {
        this.isHelpTextVisible = true
        setTimeout(() => {
          this.isHelpTextVisible = false
        }, 6000) // 4 seconds delay
      }, 10000) // Show text every 6 seconds (2 seconds after hiding)
    },
    downloadApp() {
      const appLink = process.env.VUE_APP_EMP_APP_LINK
      window.open(appLink, '_blank')
    },
    isCallingAllowed(moduleName) {
      return isModuleFeatureAllowed('ADDONS', moduleName)
    },
    isbellIconEnabled(moduleName) {
      return (
        hasPermission(this.userData, permissionData.notificationRead) && isModuleVisible(moduleName)
      )
    },
    toggleOptionsDrawer() {
      this.isOptionsDrawerOpen = !this.isOptionsDrawerOpen
    },
    async fetchEmployees() {
      try {
        const chain = JSON.parse(localStorage.getItem('userReporteeChain'))
        if (chain) {
          return
        }

        const allUser = {
          limit: '*',
          sortBy: 'firstName',
          sortOrder: 'asc'
        }
        const response = await this.$axios.get('workforce/v2/users', {
          params: allUser
        })
        if (response.data.success) {
          const userTree = this.constructReporteeTree(response.data.users)
          localStorage.setItem('userReporteeChain', JSON.stringify(userTree))
        }
      } catch (error) {
        console.error('Error fetching employees:', error)
      }
    },
    constructReporteeTree(data) {
      const map = {}
      data.forEach((employee) => {
        map[employee._id] = { ...employee }
      })

      const buildReportesToChain = (employee, visited = new Set()) => {
        if (!employee.reportesTo || !employee.reportesTo._id) return null

        const managerId = employee.reportesTo._id

        // Prevent infinite loop due to circular reference
        if (visited.has(managerId)) return null
        visited.add(managerId)

        const manager = map[managerId]

        if (!manager) return null // Ensure manager exists

        return {
          firstName: manager.firstName,
          lastName: manager.lastName,
          fullName: manager.fullName,
          reportesTo: buildReportesToChain(manager, visited),
          _id: manager._id
        }
      }

      const buildManagerChain = (employee) => {
        const chain = []
        let current = employee
        const visited = new Set()

        while (current.reportesTo && current.reportesTo._id) {
          const managerId = current.reportesTo._id

          if (visited.has(managerId)) break // Avoid infinite loops
          visited.add(managerId)

          const manager = map[managerId]
          if (!manager) break // Ensure manager exists
          chain.push(manager.fullName)
          current = manager
        }

        return chain.join(' -> ')
      }

      const tree = {}
      data.forEach((employee) => {
        tree[employee._id] = {
          firstName: employee.firstName,
          lastName: employee.lastName,
          fullName: employee.fullName,
          reportesTo: buildReportesToChain(employee),
          managerChain: buildManagerChain(employee),
          _id: employee._id
        }
      })
      return tree
    },
    async fetchAllUseCases() {
      try {
        const res = await this.$axios.get(`/workforce/orgSetup/extend-usecase`)
        this.allSubCases = res.data?.data
      } catch (error) {
        console.log(error)
      }
    },
    getUseCaseMenuItems() {
      if (!this.allSubCases || !this.allSubCases.length) return []

      // Group subcases by useCase
      const useCaseGroups = {}
      this.allSubCases.forEach((subCase) => {
        const useCase = subCase.useCase
        if (!useCaseGroups[useCase]) {
          useCaseGroups[useCase] = []
        }
        useCaseGroups[useCase].push(subCase)
      })

      return Object.entries(useCaseGroups).map(([useCase, subCases]) => {
        return {
          id: `usecase-${useCase}`,
          icon: 'mdi-folder-multiple',
          title: convertToTitleCase(useCase),
          visible: true,
          childRoutes: subCases.map((subCase) => ({
            title: convertToTitleCase(subCase.name),
            icon: 'mdi-file-document',
            to: `/workforce/${useCase}/${subCase._id}`
          }))
        }
      })
    }
  }
}
</script>

<style>
header > div.v-toolbar__content {
  width: 100%;
}

.user-menu > button {
  width: 100% !important;
}

.back {
  background-color: #f7fafb;
}

.selected-menu {
  background: #21427d;
}

.dark .selected-menu {
  background-color: #4a95da;
}

.selected-menu .text-blue-950 {
  color: white;
}

.selected-menu .white {
  color: white;
}

.justify-start .ssr-carousel-track {
  justify-content: flex-start !important;
}

.scroll-bar::-webkit-scrollbar {
  display: none;
}

.custom-btn {
  border-radius: 0px;
}

.v-application--is-ltr .wfm-navbar .v-list-item__action:first-child,
.v-application--is-ltr .wfm-navbar .v-list-item__icon:first-child {
  margin-right: 10px;
}

.wfm-navbar {
  max-width: 250px;
}

/* .wfm-navbar .v-icon {
    font-size: 20px !important;
  } */

.image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn {
  position: absolute !important;
  top: -17px;
  right: 24px;
  z-index: 1;
  padding: 2px;
  background-color: #ffffff;
  border-radius: 50%;
  border: 1px solid #2a83ff;
}

.edit-btn:hover {
  background: #21427d;
  color: #fff;
}

.colorText {
  color: #21427d;
}
.empAppIcon {
  font-size: 30px !important;
  margin-bottom: 5px;
  margin-left: 5px;
}

.no-animation.v-btn--icon.v-size--default {
  width: auto;
  height: auto;
}

.no-animation.theme--light.v-btn:hover::before {
  opacity: 0;
}
.no-animation.theme--light.v-btn:hover::after {
  opacity: 0;
}
.fixed-bottom-btn {
  position: fixed;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  font-size: 10px !important;
}
.scrollable-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.arrow {
  width: 8px;
  height: 8px;
  position: absolute;
  z-index: -1; /* Keeps it behind the tooltip text */
}

.arrow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  transform: rotate(-45deg);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.help-text-container {
  background-color: #f0f0f0;
  padding: 5px 10px;
  border-radius: 10px;
  cursor: pointer;
  max-width: 250px;
  /* margin-bottom: 10px; */
}

.help-text {
  font-size: 14px;
  color: #21427d;
  font-weight: bold;
  text-align: center;
}
.options-drawer {
  width: 250px;
  background-color: #f7f7f7;
}

.dialer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialer-container {
  background: transparent;
  max-width: 90%;
  width: auto;
}

.query-container {
  position: fixed;
  top: 60%;
  left: 60%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  width: 100%;
  display: flex;
  justify-content: center;
}

.help-desk-container {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 50;
  display: flex;
  align-items: center;
  background: white;
}

.help-desk-btn {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.query-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.query-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  width: auto;
}
</style>
