<template>
  <v-app>
    <component :is="layout">
      <router-view />
    </component>
  </v-app>
</template>

<script>
import DefaultLayout from '@/layout/DefaultLayout'
import NoLayout from '@/layout/NoLayout'
import { checkVersion } from '@/version'

export default {
  name: 'App',
  components: {
    DefaultLayout,
    NoLayout
  },
  computed: {
    layout() {
      const layoutName = this.$route.meta.layout || 'DefaultLayout'
      return layoutName
    }
  },
  mounted() {
    checkVersion()
  }
}
</script>
