<template>
  <div class="bg-gray-50 min-h-screen">
    <ActitiveTabcomponent
      :tabs="reportTabs"
      :defaultTab="'download'"
      @tab-changed="handleTabChange"
    >
      <!-- Main Content -->
      <template #download>
        <div class="px-6 py-8">
          <!-- Attendance Section -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Attendance</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Summary', 'attendance')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Summary</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Basic summary including distance, attendance, work completed and
                        location/app settings
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Attendance Details', 'attendance')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Attendance Details</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Attendance Overview; Working Days & Hours; Late comers/ Early Leavers;
                        Attendance Time & Location
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('VASA', 'attendance')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">VASA</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Data on Sites visited & Hours spent there (auto calculated)
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </div>
          </div>

          <!-- Location & Travel Section -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Location & Travel</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Location Details', 'location')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Location Details</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Location, Distance & Time details of Stoppages, Travel, Activities &
                        Attendance
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Timeline Details', 'location')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Timeline Details</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Daily Timeline Report of User Activities
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Preset Directions Report', 'location')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Preset Directions Report</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Distance travelled if Employee choses to lookup directions to a task
                        location
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Geofence In/Out Report', 'location')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Geofence In/Out Report</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Information about the users entry into and exit from the geofences
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </div>
          </div>

          <!-- Productivity Section -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Productivity</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Tasks/Photos/Forms', 'productivity')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Tasks/Photos/Forms</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Details of tasks/photos/forms
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Photos Archive', 'productivity')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Photos Archive</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">
                        Employee / Date -wise Zip File of Photos
                      </p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </div>
          </div>

          <!-- Sales Section -->
          <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Sales</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Order Reports', 'sales')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Order Reports</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">Details of Orders</p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Sales Report', 'sales')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Sales Report</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">Sales Report of Employees</p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>

              <v-card
                class="cursor-pointer"
                elevation="0"
                @click="openReportDialog('Targets Report', 'sales')"
              >
                <v-card-text class="p-6">
                  <div class="flex items-start space-x-4">
                    <div class="bg-blue-100 p-3 rounded-lg">
                      <v-icon color="blue darken-2" size="24">mdi-file-chart</v-icon>
                    </div>
                    <div class="flex-1">
                      <h3 class="font-semibold text-gray-900 mb-2">Targets Report</h3>
                      <p class="text-sm text-gray-600 leading-relaxed">Details of all Targets</p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </div>
          </div>
        </div>
      </template>
      <template #schedule>
        <div class="px-6 py-8">
          <div class="flex justify-end mb-4">
            <v-btn color="primary" @click="addDialog = true" class="text-white">
              <v-icon left>mdi-plus</v-icon>
              Add
            </v-btn>
          </div>
          <v-data-table :headers="reportHeaders" :items="reportItems" class="elevation-1">
            <template v-slot:item.name="{ item }">
              <div class="font-semibold pl-4 text-sm text-gray-800">
                {{ item.name }}
              </div>
            </template>
            <template v-slot:item.type="{ item }">
              <v-chip small color="blue lighten-3" text-color="blue darken-4">
                {{ item.type }}
              </v-chip>
            </template>
            <template v-slot:item.time="{ item }">
              <div class="text-sm pl-4 text-gray-600">
                {{ item.time }}
              </div>
            </template>
            <template v-slot:item.status="{ item }">
              <v-chip small :color="item.status === 'Active' ? 'green' : 'red'" text-color="white">
                {{ item.status }}
              </v-chip>
            </template>
            <template v-slot:item.actions="{ item }">
              <div class="flex pl-6">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn icon small v-bind="attrs" v-on="on" @click="deleteReport(item)">
                      <v-icon small color="red">mdi-delete</v-icon>
                    </v-btn>
                  </template>
                  <span>Delete</span>
                </v-tooltip>
              </div>
            </template>
          </v-data-table>
          <v-dialog v-model="addDialog" max-width="800px" persistent>
            <v-card>
              <!-- Add your dialog content here -->
              <v-card-title class="headline d-flex justify-space-between align-center">
                <div class="d-flex align-center">
                  <v-icon color="primary" class="mr-2">mdi-calendar-clock</v-icon>
                  Schedule Reports
                </div>
                <v-btn icon @click="addDialog = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </v-card-title>
              <v-divider></v-divider>
              <v-card-text>
                <v-form class="pa-4">
                  <v-text-field
                    label="Report Name"
                    hint="Add custom name for better identification"
                    persistent-hint
                    outlined
                    v-model="form.reportName"
                    class="mb-4"
                    :rules="[(v) => !!v || 'Report name is required']"
                    dense
                    @focus="$event.target.select()"
                  ></v-text-field>

                  <v-select
                    label="Select Reports"
                    :items="reportOptions"
                    v-model="form.selectedReport"
                    outlined
                    class="mb-4"
                    :rules="[(v) => !!v || 'Please select a report']"
                    dense
                    @click:append="form.selectedReport = ''"
                  ></v-select>

                  <v-select
                    label="Select When to Send"
                    :items="scheduleOptions"
                    v-model="form.scheduleTime"
                    outlined
                    class="mb-4"
                    :rules="[(v) => !!v || 'Schedule time is required']"
                    dense
                    @click:append="form.scheduleTime = ''"
                  ></v-select>

                  <v-select
                    label="Select Whose Data to Send"
                    :items="['All Users', 'specific Users', 'Selected Teams']"
                    v-model="form.dataOwner"
                    outlined
                    class="mb-4"
                    :rules="[(v) => !!v || 'Data owner selection is required']"
                    dense
                    @click:append="form.dataOwner = ''"
                  >
                  </v-select>
                  <EmployeeReportdialog
                    v-if="form.dataOwner === 'specific Users'"
                    v-model="isEmployeeReportDialogOpen"
                  />
                </v-form>
              </v-card-text>

              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" dark @click="saveChanges"> Save Changes </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </div>
      </template>
    </ActitiveTabcomponent>

    <!-- Report Dialog -->
    <v-dialog v-model="reportDialog" max-width="800px" persistent>
      <v-card class="report-dialog">
        <!-- Dialog Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-200 sticky top-0 bg-white z-50"
        >
          <div class="flex items-center space-x-3">
            <div class="bg-blue-100 p-2">
              <v-icon color="blue darken-2" size="20">mdi-file-chart</v-icon>
            </div>
            <h2 class="text-xl font-semibold text-gray-900">{{ selectedReport.title }}</h2>
          </div>
          <div class="flex items-center space-x-3">
            <v-menu
              v-model="menu"
              :close-on-content-click="false"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <div
                  class="flex items-center bg-gray-100 px-3 py-2 cursor-pointer"
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-icon left size="16" color="gray">mdi-calendar</v-icon>
                  <span class="text-sm text-gray-700">
                    {{ dateRange[0] }} - {{ dateRange[1] }}
                  </span>
                </div>
              </template>
              <v-date-picker
                v-model="dateRange"
                range
                no-title
                @input="menu = false"
              ></v-date-picker>
            </v-menu>
            <v-btn color="primary" depressed class="mr-4 text-white">
              <v-icon left size="16">mdi-download</v-icon>
              Download
            </v-btn>
            <div class="absolute top-0 right-0">
              <v-btn icon @click="reportDialog = false">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>

        <EmployeeReportdialog :selectedReport="selectedReport" @close="reportDialog = false" />
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import EmployeeReportdialog from '@/components/EmployeeReportdialog.vue'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'

export default {
  name: 'ReportsDashboard',
  data() {
    return {
      reportDialog: false,
      menu: false,
      activeTab: 'employees',
      addDialog: false,
      isLoading: false,
      searchQuery: '',
      selectAll: false,
      isEmployeeReportDialogOpen: false,
      form: {
        reportName: '',
        selectedReport: '',
        scheduleTime: '',
        dataOwner: ''
      },
      dateRange: ['2023-01-01', '2023-01-31'],
      selectedReport: {
        title: '',
        type: ''
      },
      reportTabs: [
        { key: 'download', label: 'Download Reports', icon: 'mdi-download' },
        { key: 'schedule', label: 'Schedule Reports', icon: 'mdi-calendar-clock' }
      ],
      reportHeaders: [
        { text: 'Report Name', value: 'name' },
        { text: 'Type', value: 'type' },
        { text: 'Time', value: 'time' },
        { text: 'Status', value: 'status' },
        { text: 'Actions', value: 'actions', sortable: false }
      ],
      reportItems: [
        {
          name: 'Monthly Sales',
          type: 'Attendance Details',
          time: 'Daily',
          status: 'Active'
        },
        {
          name: 'User Growth',
          type: 'Summary',
          time: 'Weekly',
          status: 'Inactive'
        },
        {
          name: 'Daily Summary',
          type: 'VASA',
          time: 'Monthly',
          status: 'Active'
        }
      ]
    }
  },
  components: {
    EmployeeReportdialog,
    ActitiveTabcomponent
  },
  methods: {
    handleTabChange(t) {
      console.log(t)
    },
    openReportDialog(title, type) {
      this.selectedReport = { title, type }
      this.reportDialog = true
      // Reset selections when opening dialog
      this.selectAll = false
      this.searchQuery = ''
      this.activeTab = 'employees' || 'teams'
    },
    watch: {
      'form.dataOwner': {
        handler(newVal) {
          if (newVal !== 'specific Users') {
            this.isEmployeeReportDialogOpen = false
          }
        },
        deep: true
      }
    }
  }
}
</script>

<style scoped>
.v-card {
  border: 1px solid #b3b8c0;
}
.v-btn {
  text-transform: none;
  font-weight: 500;
}

.v-dialog > .v-card > .v-card__text {
  padding: 0;
}

/* Custom checkbox styling */
.v-input--checkbox {
  margin-top: 0;
  padding-top: 0;
}

.v-input--checkbox .v-input__slot {
  margin-bottom: 0;
}
</style>
