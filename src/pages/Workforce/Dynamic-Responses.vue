<template>
  <div class="p-3">
    <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
      <template v-slot:Forms>
        <CategoryQuestionnaire :receivedData="receivedData" class="py-4" />
      </template>

      <template v-slot:Responses>
        <div class="flex justify-end items-center mb-4">
          <div
            class="justify-end mr-4"
            :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']"
          >
            <v-col
              :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
              :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
            >
              <v-select
                v-model="filterForm"
                label="Select Form"
                :items="formOptions"
                item-text="text"
                item-value="value"
                solo
                flat
                clearable
                outlined
                dense
                hide-details
                @change="handleInput"
              ></v-select>
            </v-col>
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text my-auto"
              @click="exportToExcel"
              :loading="exportLoading"
              :disabled="exportLoading"
            >
              Export
            </v-btn>
          </div>
        </div>
        <div class="overflow-x-auto overflow-y-auto">
          <v-data-table
            :headers="dynamicHeaders"
            :items="formattedResponses"
            item-key="_id"
            class="pl-7 pr-7 pt-4 dynamic-response-table"
            :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
            :options.sync="options"
            :server-items-length="totalItems"
            :loading="loading"
            fixed-header
          >
            <template v-slot:item.responseId="{ item }">
              <td class="px-4 py-6 font-semibold response-id-cell">
                {{ item.responseId }}
              </td>
            </template>
            <template v-slot:item.formName="{ item }">
              <td class="px-4 py-6 font-semibold form-name-cell">
                {{ item.formName }}
              </td>
            </template>
            <template v-slot:item.formType="{ item }">
              <td class="px-4 py-6 font-semibold">
                <v-chip label :color="getStatusColor(item.formType)" text-color="white" small>
                  {{ item.formType }}
                </v-chip>
              </td>
            </template>
            <template v-for="header in questionHeaders" v-slot:[`item.${header.value}`]="{ item }">
              <td :key="header.value" class="px-4 py-6 question-cell">
                {{ item[header.value] }}
              </td>
            </template>
          </v-data-table>
        </div>
      </template>
    </ActitiveTabcomponent>
  </div>
</template>

<script>
import { debounce } from '@/utils/common'
import * as XLSX from 'xlsx'
import CategoryQuestionnaire from '@/components/Workforce/Settings/OrgConfigComponents/CategeoryQuestionnaire.vue'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'

export default {
  name: 'DynamicResponses',
  layout: 'Workforce/default',
  components: {
    CategoryQuestionnaire,
    ActitiveTabcomponent
  },

  data() {
    return {
      orgData: this.$storage.getUniversal('orgData'),
      receivedData: null,
      filterForm: '',
      loading: false,
      exportLoading: false,
      responses: [],
      totalItems: 0,
      options: { itemsPerPage: 5, page: 1 },
      formOptions: [],
      baseHeaders: [
        { text: 'Response ID', value: 'responseId', sortable: false, width: '150px' },
        { text: 'Form Name', value: 'formName', sortable: false, width: '200px' },
        { text: 'Form Type', value: 'formType', sortable: false, width: '100px' }
      ],
      questionHeaders: [],
      columnWidth: '150px'
    }
  },
  computed: {
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return 'Forms'
    },
    tabItems() {
      return [
        {
          key: 'Forms',
          label: 'Forms',
          icon: 'mdi-form-select',
          component: 'CategoryQuestionnaire'
        },
        {
          key: 'Responses',
          label: 'Responses',
          icon: 'mdi-table',
          component: 'ResponsesTable'
        }
      ]
    },
    dynamicHeaders() {
      return [...this.baseHeaders, ...this.questionHeaders]
    },
    formattedResponses() {
      return this.responses.map((item) => {
        const formattedItem = {
          _id: item._id,
          responseId: item.responseId || '-',
          formName: item.linkedFormDetails.categoryName || item.linkedFormDetails,
          formType: item.linkedFormDetails.isPrivate ? 'Private' : 'Public' || '-'
        }
        // Add each question response to the item
        item.questionsAnswered.forEach((qa) => {
          let responseValue = '-'

          const questionBase = qa.question
          // Format response based on question type
          if (qa.userResponse !== null && qa.userResponse !== undefined) {
            if (questionBase.questionType === 'CHECKBOX' && Array.isArray(qa.userResponse)) {
              responseValue = qa.userResponse.join(', ')
            } else if (questionBase.questionType === 'NUMBER') {
              responseValue = Number(qa.userResponse).toLocaleString()
            } else if (questionBase.questionType === 'DATA_CATEGORY') {
              try {
                const parsedResponse = JSON.parse(qa.userResponse[0])
                responseValue = parsedResponse.text
              } catch (error) {
                responseValue = qa.userResponse[0]
              }
            } else if (Array.isArray(qa.userResponse)) {
              responseValue = qa.userResponse.join(', ')
            } else {
              responseValue = qa.userResponse
            }
          }

          formattedItem[`question_${questionBase.question}`] = responseValue
        })

        return formattedItem
      })
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchResponses()
      },
      deep: true
    },
    responses: {
      handler() {
        this.generateQuestionHeaders()
      },
      deep: true
    }
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },
    generateQuestionHeaders() {
      this.questionHeaders = []
      const uniqueQuestions = new Set()
      this.responses.forEach((response) => {
        response.questionsAnswered.forEach((qa) => {
          uniqueQuestions.add(qa.question.question)
        })
      })
      if (uniqueQuestions.size > 0) {
        this.columnWidth = `${150}px`
      }
      uniqueQuestions.forEach((question) => {
        this.questionHeaders.push({
          text: question,
          value: `question_${question}`,
          sortable: false,
          width: this.columnWidth
        })
      })
    },

    async fetchResponses() {
      try {
        this.loading = true
        const { page, itemsPerPage } = this.options
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems
        }

        if (this.filterForm) {
          params.linkedForm = this.filterForm
        }

        const response = await this.$axios.get(`/workforce/orgSetup/form-responses`, {
          params
        })

        this.responses = response.data.responses
        console.log('Fetched responses:', this.responses)
        this.totalItems = response.data.pagination?.totalCount || 0
      } catch (error) {
        console.error('Error fetching responses:', error)
      } finally {
        this.loading = false
      }
    },

    async fetchFormOptions() {
      try {
        const response = await this.$axios.get(`/workforce/orgSetup/dynamic-forms`)
        this.formOptions = response.data.data.map((form) => ({
          text: form.categoryName,
          value: form._id
        }))
      } catch (error) {
        console.error('Error fetching form options:', error)
      }
    },

    handleInput: debounce(function () {
      this.options.page = 1
      this.fetchResponses()
    }, 1000),

    getStatusColor(status) {
      const statusColors = {
        Private: 'blue',
        Public: 'green'
      }
      return statusColors[status] || 'grey'
    },
    async exportToExcel() {
      try {
        this.exportLoading = true
        const batchSize = 50
        const totalPages = Math.ceil(this.totalItems / batchSize)
        const promises = []

        for (let page = 1; page <= totalPages; page++) {
          const params = {
            page,
            limit: batchSize
          }

          if (this.filterForm) {
            params.linkedForm = this.filterForm
          }

          const promise = this.$axios.get(`/workforce/orgSetup/form-responses/${this.orgData.id}`, {
            params
          })
          promises.push(promise)
        }
        const responses = await Promise.all(promises)

        const allResponses = responses.reduce((acc, response) => {
          return acc.concat(response.data.responses)
        }, [])

        const excelData = this.formatDataForExcel(allResponses)

        const ws = XLSX.utils.json_to_sheet(excelData)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Form Responses')

        const currentDate = new Date().toISOString().split('T')[0]
        const formName = this.filterForm
          ? this.formOptions.find((option) => option.value === this.filterForm)?.text || 'Selected'
          : 'All'

        XLSX.writeFile(wb, `${formName}_Form_Responses_${currentDate}.xlsx`)

        this.$toast.success('Export completed successfully')
      } catch (error) {
        console.error('Error exporting data:', error)
        this.$toast.error('Failed to export data')
      } finally {
        this.exportLoading = false
      }
    },

    formatDataForExcel(responses) {
      const uniqueQuestions = new Set()

      return responses.map((response) => {
        const formattedResponse = {
          'Response ID': response._id.substring(response._id.length - 8).toUpperCase(),
          'Form Name': response.linkedFormDetails.categoryName || response.linkedFormDetails
          // 'Submitted Date': new Date(response.createdAt).toLocaleString(),
          // 'Submitted By': response.createdBy?.fullName || 'Unknown'
        }

        response.questions.forEach((question) => {
          let responseValue = '-'

          if (question.userResponse !== null && question.userResponse !== undefined) {
            if (question.questionType === 'CHECKBOX' && Array.isArray(question.userResponse)) {
              responseValue = question.userResponse.join(', ')
            } else if (question.questionType === 'NUMBER') {
              responseValue = Number(question.userResponse).toLocaleString()
            } else if (Array.isArray(question.userResponse)) {
              responseValue = question.userResponse.join(', ')
            } else {
              responseValue = question.userResponse
            }
          }

          formattedResponse[question.question] = responseValue
        })

        uniqueQuestions.forEach((question) => {
          if (!formattedResponse[question]) {
            formattedResponse[question] = '-'
          }
        })

        return formattedResponse
      })
    }
  },
  mounted() {
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }
    this.fetchResponses()
    this.fetchFormOptions()
  }
}
</script>

<style scoped>
.v-data-table {
  max-width: 100%;
  overflow-x: auto;
}

.dynamic-response-table {
  table-layout: fixed;
}

.response-id-cell {
  width: 150px;
  font-family: monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-name-cell {
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.question-cell {
  white-space: normal;
  word-break: break-word;
}
</style>
