<template>
  <div>
    <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
      <!-- Tab Actions Slot - wraps child component buttons/filters -->
      <template v-slot:tab-buttons="{ activeTab }">
        <div class="flex items-center space-x-2">
          <!-- Leads tab actions -->
          <template v-if="activeTab === 'leads'">
            <div class="flex items-center space-x-2">
              <!-- Filter PopOver -->
              <FilterPopOver :selectedCount="selectedCount">
                <v-card outlined class="w-72 max-h-96">
                  <v-list>
                    <!-- Company Filter -->
                    <v-list-item class="px-0">
                      <v-col class="pb-0 pt-0">
                        <v-combobox
                          v-model="selectedCompany"
                          :items="companyNames"
                          :label="leadLable.companyName"
                          :placeholder="leadLable.companyName"
                          hide-details
                          outlined
                          dense
                          clearable
                          @change="handleChange"
                        />
                      </v-col>
                    </v-list-item>

                    <!-- Company Branch Filter -->
                    <v-list-item class="px-0">
                      <v-col class="pb-0">
                        <v-autocomplete
                          v-model="selectedCompanyBranch"
                          :items="selectedCompanyBranches"
                          label="Customer Address"
                          placeholder="Customer Address"
                          :disabled="!selectedCompany?.value"
                          hide-details
                          outlined
                          dense
                          clearable
                          @change="handleChange"
                        />
                      </v-col>
                    </v-list-item>

                    <!-- Warranty Status Filter -->
                    <v-list-item class="px-0" v-if="isComplaintsEnabled">
                      <v-col class="pb-0">
                        <v-select
                          v-model="productWarrantyStatus"
                          label="Warranty Status"
                          placeholder="Warranty Status"
                          :items="warrantyOptions"
                          item-text="warranty"
                          item-value="warranty"
                          hide-details
                          outlined
                          dense
                          clearable
                          @change="handleChange"
                        />
                      </v-col>
                    </v-list-item>

                    <!-- AMC Status Filter -->
                    <v-list-item class="px-0" v-if="isComplaintsEnabled">
                      <v-col class="pb-0">
                        <v-select
                          v-model="haveAmcPlan"
                          label="AMC Status"
                          placeholder="AMC Status"
                          :items="[
                            { text: 'AMC Active', value: true },
                            { text: 'AMC Inactive', value: false }
                          ]"
                          hide-details
                          outlined
                          dense
                          clearable
                          @change="handleChange"
                        />
                      </v-col>
                    </v-list-item>
                  </v-list>
                </v-card>
              </FilterPopOver>

              <!-- Upload Files -->
              <UploadFiles
                :upload-url="uploadUrl"
                :download-data="leadsDataFormat"
                :file-name="'leadDummyData'"
                :useCase="useCaseLeadConfig"
                @updateUploadData="bulkFileUpload"
              />
            </div>
          </template>

          <!-- Task tab actions -->
          <template v-if="activeTab === 'task'">
            <v-tooltip top>
              <template #activator="{ on, attrs }">
                <v-btn
                  :color="$vuetify.theme.currentTheme.primary"
                  :loading="exportLoading"
                  :disabled="exportLoading"
                  class="ml-4 white--text mr-4 mb-2"
                  @click="exportTaskDataToXLSX"
                  v-bind="attrs"
                  v-on="on"
                >
                  Export
                  <v-icon right dark>mdi-download</v-icon>
                </v-btn>
              </template>
              <span>Export Tasks</span>
            </v-tooltip>

            <!-- Task Filter PopOver -->
            <FilterPopOver :selectedCount="taskSelectedCount">
              <v-card outlined class="max-h-96 w-72">
                <v-list>
                  <v-list-item class="px-0">
                    <v-col class="pb-0 pt-0">
                      <v-menu
                        v-model="dateMenu"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                      >
                        <template v-slot:activator="{ on }">
                          <v-text-field
                            v-model="selectedDateRange"
                            label="Date Range"
                            prepend-inner-icon="mdi-calendar"
                            readonly
                            v-on="on"
                            outlined
                            dense
                            hide-details
                            clearable
                            @click:clear="clearDateRange"
                          />
                        </template>
                        <!-- Date picker content would go here -->
                        <v-date-picker
                          v-model="dates"
                          @input="onDateSelected"
                          no-title
                          @change="menu = false"
                          range
                          scrollable
                        />
                      </v-menu>
                    </v-col>
                  </v-list-item>
                  <v-list-item class="px-0">
                    <v-col class="pb-0">
                      <v-autocomplete
                        v-model="filterAssignedTo"
                        label="Select Assigned To"
                        placeholder="Select Assigned To"
                        :items="populateUser"
                        item-value="_id"
                        item-text="full_name"
                        hide-details
                        outlined
                        dense
                        clearable
                        @change="handleChange"
                      ></v-autocomplete>
                    </v-col>
                  </v-list-item>
                  <v-list-item class="px-0">
                    <v-col class="pb-0">
                      <v-select
                        v-model="filterPayment"
                        label="Payment Status"
                        :items="paymentOptions"
                        hide-details
                        outlined
                        dense
                        clearable
                        @change="handleChange"
                      ></v-select>
                    </v-col>
                  </v-list-item>
                </v-list>
              </v-card>
            </FilterPopOver>
          </template>

          <!-- AddLead tab actions -->
          <template v-if="activeTab === 'AddLead'">
            <slot name="addlead-actions"></slot>
          </template>
        </div>
      </template>

      <template v-for="tab in visibleTabs" v-slot:[tab.key]="">
        <div v-if="!isLoad">
          <v-main style="padding: 0px" class="bg-[#f7fafb] dark-bg-default">
            <component
              @handleEditAndChangeTab="handleEditAndChangeTabMethod"
              @search="handleSearch"
              :is="tab.component"
              :useCaseLeadConfig="useCaseLeadConfig"
              :useCaseTaskConfig="useCaseTaskConfig"
              :tabSwitch="handleTabSwitch"
              :lead-steps="leadSteps"
              :task-steps="taskSteps"
              :users="users"
              :companies="companies"
              :fetchCompanies="fetchCompanies"
              :editItems="editItems"
              :selectedLeadStatus="selectedLeadStatus"
              :selectedCompany="selectedCompany"
              :selectedCompanyBranch="selectedCompanyBranch"
              :productWarrantyStatus="productWarrantyStatus"
              :haveAmcPlan="haveAmcPlan"
              :ref="tab.key"
            />
          </v-main>
        </div>
        <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </template>
    </ActitiveTabcomponent>
  </div>
</template>

<script>
import Leads from '@/components/Workforce/LeadManagement/LeadsTable.vue'
import AddLead from '@/components/Workforce/LeadManagement/AddLead.vue'
import Tasks from '@/components/Workforce/LeadManagement/TaskTable.vue'
import { hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
import { sortDropdownOptions } from '@/utils/common'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'
import FilterPopOver from '@/components/FilterPopOver.vue'
import UploadFiles from '@/components/Workforce/LeadManagement/uploadFile.vue'

export default {
  name: 'LeadPage',
  layout: 'Workforce/default',
  components: {
    Leads,
    Tasks,
    AddLead,
    ActitiveTabcomponent,
    FilterPopOver,
    UploadFiles
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      orgCode: this.$storage.getUniversal('organization_code'),
      orgLang: this.$store.state.orgLanguage,
      tabs: 0,
      leadName: '',
      leadSteps: [],
      taskSteps: [],
      users: [],
      companies: [],
      editItems: {},
      isLoad: true,
      selectedLeadStatus: [],
      selectedCount: 0,
      selectedCompany: null,
      selectedCompanyBranch: null,
      productWarrantyStatus: null,
      haveAmcPlan: null,
      companyNames: [],
      exportLoading: false,
      leadLable: {
        leadTitle: 'Lead',
        companyName: 'Company'
      },

      warrantyOptions: ['IN_WARRANTY', 'OUT_OF_WARRANTY'],
      uploadUrl: '',
      leadsDataFormat: [],
      taskSelectedCount: 0,
      dateMenu: false,
      selectedDateRange: null
    }
  },
  watch: {
    tabs() {
      if (this.tabs !== 2) {
        this.editItems = null
      }
    },
    '$route.params.usecase': async function (newQuery) {
      if (newQuery) {
        this.tabs = 0
      }
    },
    selectedCompany() {
      this.updateSelectedCount()
    },
    selectedCompanyBranch() {
      this.updateSelectedCount()
    },
    productWarrantyStatus() {
      this.updateSelectedCount()
    },
    haveAmcPlan() {
      this.updateSelectedCount()
    },
    selectedDateRange() {
      this.updateTaskSelectedCount()
    }
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },
    handleTabSwitch() {
      this.tabs = 0
    },
    handleEditAndChangeTabMethod(item) {
      this.editItems = item
      this.tabs = 2
    },
    handleSearch(item) {
      this.fetchCompanies(item)
    },
    async fetchLeadSteps() {
      try {
        const response = await this.$axios.get(`/workforce/lead/steps`)

        const leadStepsArray = []

        for (const key in response.data?.leadSteps) {
          if (Object.prototype.hasOwnProperty.call(response.data?.leadSteps, key)) {
            leadStepsArray.push({
              key: key,
              value: response.data.leadSteps[key]
            })
          }
        }

        this.leadSteps = leadStepsArray
      } catch (error) {
        console.log(error)
      }
    },
    async fetchCompanies(searchText) {
      try {
        const params = {
          companySearch: searchText
        }
        const response = await this.$axios.get('/workforce/allcompany', {
          params
        })
        const companies = response.data.companies
        this.companies = sortDropdownOptions(companies, 'name')

        if (this.selectedCompanyId) {
          const selectedCompany = this.companies.find(
            (company) => company._id === this.selectedCompanyId
          )
          if (selectedCompany) {
            const selectedCompanyContacts = selectedCompany?.companyBranches[0]?.contacts || []
            if (selectedCompanyContacts) {
              this.contacts = selectedCompanyContacts.map((contact) => ({
                firstName: contact.firstName
              }))
            }
          }
        }

        this.isLoading = false
        this.contacts = []
        this.selectedCompanyId = ''
      } catch (error) {
        console.error(error)
      }
    },
    fetchUser() {
      this.$axios
        .get(`/workforce/users`, {
          params: {
            limit: '*'
          }
        })
        .then((res) => {
          this.users = res.data.users.map((user) => ({
            ...user,
            id: user._id,
            full_name: `${user.firstName || ''} ${user.lastName || ''}`
          }))
          this.users.push({
            id: null,
            _id: null,
            firstName: 'Unassigned'
          })
        })
        .catch((error) => {
          console.log(error.message)
        })
    },
    async fetchTaskSteps() {
      try {
        const response = await this.$axios.get(`/workforce/task/steps`)

        const taskStepsArray = []

        for (const key in response.data?.taskSteps) {
          if (Object.prototype.hasOwnProperty.call(response.data?.taskSteps, key)) {
            taskStepsArray.push({
              key: key,
              value: response.data.taskSteps[key]
            })
          }
        }

        this.taskSteps = taskStepsArray
      } catch (error) {
        console.log(error)
      }
    },
    handleLeadStatusChange() {
      if (this.$refs.leads && this.$refs.leads[0]) {
        this.$refs.leads[0].selectedLeadStatus = this.selectedLeadStatus
        this.$refs.leads[0].handleLeadStatusChange()
      }
    },
    toggleSelectAll() {
      if (this.allLeadStatusSelected) {
        this.selectedLeadStatus = []
      } else {
        this.selectedLeadStatus = this.leadSteps.map((step) => step.key)
      }
      this.handleLeadStatusChange()
    },
    onStatusCleared() {
      this.selectedLeadStatus = []
      if (this.$refs.leads && this.$refs.leads[0]) {
        this.$refs.leads[0].selectedLeadStatus = []
        this.$refs.leads[0].onStatusCleared()
      }
    },
    exportDataToXLSX() {
      if (this.$refs.leads && this.$refs.leads[0]) {
        this.$refs.leads[0].exportDataToXLSX()
      }
    },
    handleChange() {
      this.updateSelectedCount()
      if (this.$refs.leads && this.$refs.leads[0]) {
        // Sync filter data to child component
        this.$refs.leads[0].selectedCompany = this.selectedCompany
        this.$refs.leads[0].selectedCompanyBranch = this.selectedCompanyBranch
        this.$refs.leads[0].productWarrantyStatus = this.productWarrantyStatus
        this.$refs.leads[0].haveAmcPlan = this.haveAmcPlan
        this.$refs.leads[0].handleChange()
      }
    },
    bulkFileUpload(data) {
      if (this.$refs.leads && this.$refs.leads[0]) {
        this.$refs.leads[0].bulkFileUpload(data)
      }
    },
    async initializeData() {
      await this.fetchLeadSteps()
      await this.fetchCompanies()
      this.setupLeadStepsSelect()
      this.setupCompanyNames()
    },
    setupLeadStepsSelect() {
      this.leadStepsSelect = this.leadSteps.map((step) => ({
        key: step.key,
        value: step.value
      }))
    },
    setupCompanyNames() {
      this.companyNames = this.companies.map((company) => ({
        text: company.name,
        value: company._id
      }))
    },
    updateSelectedCount() {
      this.selectedCount = [
        this.selectedCompany,
        this.selectedCompanyBranch,
        this.productWarrantyStatus,
        this.haveAmcPlan
      ].filter((item) => item !== null && item !== '').length
    },
    exportTaskDataToXLSX() {
      if (this.$refs.task && this.$refs.task[0]) {
        this.$refs.task[0].exportDataToXLSX()
      }
    },
    clearDateRange() {
      this.selectedDateRange = null
      this.updateTaskSelectedCount()
      // Call task component method to clear date filter
      if (this.$refs.task && this.$refs.task[0]) {
        this.$refs.task[0].clearDateFilter && this.$refs.task[0].clearDateFilter()
      }
    },
    updateTaskSelectedCount() {
      this.taskSelectedCount = [this.selectedDateRange].filter(
        (item) => item !== null && item !== ''
      ).length
    }
  },
  computed: {
    useCaseLeadConfig() {
      const currentUsecase = this.$route.params.usecase
      const orgConfig = this.orgLang.data || {}
      const leadModules = orgConfig.leadModules || []
      const currentConfig = leadModules.find((m) => m.useCaseType === currentUsecase)
      return currentConfig
    },
    useCaseTaskConfig() {
      const currentUsecase = this.$route.params.usecase
      const orgConfig = this.orgLang.data || {}

      const taskModules = orgConfig.taskModules || []

      const currentConfig = taskModules.find((m) => m.useCaseType === currentUsecase)

      return currentConfig
    },
    visibleTabs() {
      const filtered = this.tabItems.filter((tab) => tab.visible)
      return filtered
    },
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.visibleTabs.length > 0 ? this.visibleTabs[0].key : 'Leads'
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.taskEdit)
    },
    haveInventoryAccess() {
      return hasPermission(this.userData, permissionData.inventoryRead)
    },
    hideAllTabs() {
      if (
        this.$route.query?.complaint_no &&
        !hasPermission(this.userData, permissionData.leadRead)
      ) {
        return this.canEdit && this.haveInventoryAccess && this.$route.query?.complaint_no
      }
      return false
    },
    taskLable() {
      if (!this.useCaseTaskConfig) return {}
      return {
        taskTab: this.useCaseTaskConfig.tabs?.tasks,
        addTask: this.useCaseTaskConfig.tabs?.addANewTask
      }
    },
    tabItems() {
      const taskLabel = this.taskLable
      return [
        {
          key: 'leads',
          label: this.useCaseLeadConfig?.tabs?.lead || '',
          icon: 'mdi-account-multiple',
          component: 'Leads',
          visible: true
        },

        {
          key: 'task',
          label: taskLabel.taskTab,
          icon: 'mdi-clipboard-text',
          component: 'Tasks',
          visible: true
        },
        {
          key: 'AddLead',
          label: this.useCaseLeadConfig?.tabs?.addANewLead || '',
          component: 'AddLead',
          icon: 'mdi-account-plus',
          visible: true
        }
      ]
    },
    isComplaintsEnabled() {
      return this.$route.params.usecase === 'inventory'
    },
    // leadStepsSelect() {
    //   return this.leadSteps.map((step) => ({
    //     key: step.key,
    //     value: step.value
    //   }))
    // },
    icon() {
      if (this.allLeadStatusSelected) return 'mdi-close-box'
      if (this.someLeadStatusSelected) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },
    allLeadStatusSelected() {
      return this.selectedLeadStatus.length === this.leadSteps.length
    },
    someLeadStatusSelected() {
      return this.selectedLeadStatus.length > 0 && !this.allLeadStatusSelected
    },
    selectedCompanyBranches() {
      if (!this.selectedCompany) return []
      const selectedCompany = this.companies.find(
        (company) => company._id === this.selectedCompany?.value
      )
      return (
        selectedCompany?.companyBranches?.map((branch) => ({
          text: `[ Alias - ${branch.alias || ''} ] - ${branch.address.addressLine1}`,
          value: branch._id
        })) || []
      )
    }
  },
  async mounted() {
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }

    this.$nextTick(() => {
      this.isLoad = false
    })
    await this.initializeData()
    await this.fetchUser()
    await this.fetchTaskSteps()
    this.isLoad = false
  }
}
</script>
<style>
.v-main__wrap.v-content__wrap {
  padding: 0px 20px;
}

.theme--light.v-tabs .v-tab:hover::before {
  opacity: 0;
}

.theme--light.v-tabs .v-tab--active:hover::before,
.theme--light.v-tabs .v-tab--active::before {
  opacity: 0;
}

.custom-tabs {
  padding-left: 0px;
}

.v-tab {
  font-size: 16px;
}
</style>
