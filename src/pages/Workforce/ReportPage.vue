<template>
  <v-container fluid class="report-dashboard pa-0 mt-4">
    <v-stepper v-model="currentStep" vertical flat>
      <!-- Step 1: Select Reports -->
      <v-stepper-step :complete="currentStep > 1" step="1">
        Select Reports
        <small>Choose one or more reports to generate</small>
      </v-stepper-step>

      <v-stepper-content step="1">
        <v-row class="px-4">
          <v-col
            v-for="report in reportTypes"
            :key="report.id"
            cols="12"
            sm="6"
            md="4"
          >
            <v-hover v-slot="{ hover }">
              <v-card
                class="report-card mt-4"
                :elevation="hover ? 8 : 2"
                :class="{
                  'on-hover': hover,
                  selected: selectedReports.includes(report.id),
                }"
                @click="toggleReportSelection(report.id)"
              >
                <v-card-title class="text-h6">
                  <v-icon
                    left
                    :color="
                      selectedReports.includes(report.id) ? 'primary' : ''
                    "
                  >
                    {{ getReportIcon(report.name) }}
                  </v-icon>
                  {{ report.name }}
                  <v-scale-transition>
                    <v-icon
                      v-if="selectedReports.includes(report.id)"
                      class="check-icon"
                      color="primary"
                      large
                    >
                      mdi-check-circle
                    </v-icon>
                  </v-scale-transition>
                </v-card-title>
                <v-card-text>
                  <p>{{ report.description }}</p>
                  <v-chip
                    :color="
                      selectedReports.includes(report.id) ? 'primary' : 'grey'
                    "
                    text-color="white"
                    small
                  >
                    {{
                      selectedReports.includes(report.id)
                        ? "Selected"
                        : "Select"
                    }}
                  </v-chip>
                </v-card-text>
              </v-card>
            </v-hover>
          </v-col>
        </v-row>
        <v-btn
          color="primary"
          @click="currentStep = 2"
          :disabled="!selectedReports.length"
          class="mt-4"
        >
          Continue to Filters
        </v-btn>
      </v-stepper-content>

      <!-- Step 2: Apply Filters -->
      <v-stepper-step :complete="currentStep > 2" step="2">
        Apply Filters
        <small>Refine your report data</small>
      </v-stepper-step>

      <v-stepper-content step="2">
        <v-card class="mb-4">
          <v-card-text>
            <v-row>
              <v-col cols="12" md="4">
                <v-menu
                  v-model="dateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="dateRange"
                      label="Date Range"
                      prepend-icon="mdi-calendar"
                      readonly
                      v-bind="attrs"
                      v-on="on"
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="dates"
                    range
                    @input="dateMenu = false"
                  ></v-date-picker>
                </v-menu>
              </v-col>
              <v-col cols="12" md="4">
                <v-select
                  v-model="selectedDepartment"
                  :items="users"
                  label="Users"
                  prepend-icon="mdi-domain"
                ></v-select>
              </v-col>
              <v-col cols="12" md="4">
                <v-select
                  v-model="selectedStatus"
                  :items="statuses"
                  label="Status"
                  prepend-icon="mdi-flag"
                ></v-select>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
        <v-btn color="primary" @click="generateReports" class="mr-2"
          >Generate Reports</v-btn
        >
        <v-btn text @click="currentStep = 1">Back to Selection</v-btn>
      </v-stepper-content>

      <!-- Step 3: Download Queue -->
      <v-stepper-step step="3">
        Download Queue
        <small>Track and download your reports</small>
      </v-stepper-step>

      <v-stepper-content step="3">
        <v-slide-y-transition group>
          <v-card
            v-for="(report, index) in downloadQueue"
            :key="index"
            class="mb-4 download-queue-item"
          >
            <v-card-text>
              <v-row align="center">
                <v-col cols="6">
                  <span class="text-subtitle-1">{{ report.name }}</span>
                </v-col>
                <v-col cols="4">
                  <v-progress-linear
                    :value="report.progress"
                    height="20"
                    rounded
                    :color="report.progress === 100 ? 'success' : 'primary'"
                    striped
                    :indeterminate="report.progress < 100"
                  >
                    <template v-slot:default="{ value }">
                      <strong>{{ Math.ceil(value) }}%</strong>
                    </template>
                  </v-progress-linear>
                </v-col>
                <v-col cols="2" class="text-right">
                  <v-btn
                    v-if="report.progress === 100"
                    text
                    color="success"
                    @click="downloadReport(report)"
                  >
                    <v-icon left>mdi-download</v-icon>
                    Download
                  </v-btn>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-slide-y-transition>
        <v-btn text @click="currentStep = 1" v-if="downloadQueue.length === 0"
          >Start New Reports</v-btn
        >
      </v-stepper-content>
    </v-stepper>

    <!-- Download Ready Dialog -->
    <v-dialog
      v-model="downloadDialog"
      max-width="400"
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-card-title class="text-h5 green--text">
          <v-icon large left color="green">mdi-check-circle</v-icon>
          Report Ready
        </v-card-title>
        <v-card-text class="text-subtitle-1 mt-4">
          Your report "{{ readyReport ? readyReport.name : "" }}" is ready to
          download.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" text @click="downloadReadyReport">
            <v-icon left>mdi-download</v-icon>
            Download
          </v-btn>
          <v-btn color="grey" text @click="downloadDialog = false">Close</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 1,
      reportTypes: [
        {
          id: 1,
          name: "Case/Visit Report",
          description: "Detailed summary of cases and visits.",
        },
        {
          id: 2,
          name: "Product/Complaint/Inventory Report",
          description:
            "Overview of stock levels, product sell and complaint on the products.",
        },
        {
          id: 3,
          name: "Distance/Activity Report",
          description: "Tracking employee distance and activity data.",
        },
        {
          id: 4,
          name: "Case/Visit Analytics",
          description: "Analysis of case visit trends and performance.",
        },
        {
          id: 5,
          name: "Employee Attendance",
          description: "Records of employee attendance and work hours.",
        },
        {
          id: 6,
          name: "Call/Campaign Report",
          description: "Analysis of call activities and marketing campaigns.",
        },
      ],
      selectedReports: [],
      dateMenu: false,
      dates: [],
      users: [
        "All",
        "Gaurav Shukla",
        "Vaibav Kataria",
        "Nitu Deka",
        "Abhinava",
        "Pranav",
      ],
      selectedDepartment: "All",
      statuses: ["All", "Completed", "In Progress", "Pending"],
      selectedStatus: "All",
      downloadQueue: [],
      downloadDialog: false,
      readyReport: null,
    };
  },
  computed: {
    dateRange() {
      return this.dates.join(" - ");
    },
  },
  methods: {
    toggleReportSelection(reportId) {
      const index = this.selectedReports.indexOf(reportId);
      if (index === -1) {
        this.selectedReports.push(reportId);
      } else {
        this.selectedReports.splice(index, 1);
      }
    },
    getReportIcon(reportName) {
      const iconMap = {
        "Case/Visit Report": "mdi-file-chart",
        "Product/Complain/Inventory Report": "mdi-warehouse",
        "Distance/Activity Report": "mdi-map-marker-distance",
        "Case/Visit Analytics": "mdi-chart-bar",
        "Employee Attendance": "mdi-account-clock",
        "Call/Campaign Report": "mdi-phone-in-talk",
      };
      return iconMap[reportName] || "mdi-file-document";
    },

    generateReports() {
      this.currentStep = 3;
      this.selectedReports.forEach((reportId) => {
        const report = this.reportTypes.find((r) => r.id === reportId);
        if (report) {
          this.downloadQueue.push({
            id: reportId,
            name: report.name,
            progress: 0,
          });
          this.simulateReportGeneration(reportId);
        }
      });
    },
    simulateReportGeneration(reportId) {
      const reportIndex = this.downloadQueue.findIndex(
        (r) => r.id === reportId
      );
      const interval = setInterval(() => {
        if (this.downloadQueue[reportIndex].progress < 100) {
          this.downloadQueue[reportIndex].progress +=
            Math.floor(Math.random() * 10) + 1;
          if (this.downloadQueue[reportIndex].progress > 100) {
            this.downloadQueue[reportIndex].progress = 100;
          }
          // Force Vue to react to the change in the nested object
          this.downloadQueue = [...this.downloadQueue];
        } else {
          clearInterval(interval);
          this.showDownloadReadyDialog(this.downloadQueue[reportIndex]);
        }
      }, 500);
    },
    showDownloadReadyDialog(report) {
      this.readyReport = report;
      this.downloadDialog = true;
    },
    downloadReadyReport() {
      if (this.readyReport) {
        this.downloadReport(this.readyReport);
        this.downloadDialog = false;
      }
    },
    downloadReport(report) {
      console.log(`Downloading ${report.name}`);
      // Simulating API call for report download
      setTimeout(() => {
        const blob = new Blob(["Simulated report data"], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = `${report.name.replace(/\s+/g, "_")}.xlsx`;
        link.click();
        URL.revokeObjectURL(link.href);

        const index = this.downloadQueue.findIndex((r) => r.id === report.id);
        if (index !== -1) {
          this.downloadQueue.splice(index, 1);
        }

        if (this.downloadQueue.length === 0) {
          this.currentStep = 1;
          this.selectedReports = [];
        }
      }, 1000);
    },
  },
};
</script>

<style scoped>
.report-dashboard {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.report-card {
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.report-card.on-hover {
  transform: translateY(-5px);
}

.report-card.selected {
  border: 2px solid #1976d2;
}

.download-queue-item {
  transition: all 0.3s ease;
}

.v-stepper__content {
  padding: 16px;
}

/* Animations */
.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter,
.v-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
.check-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  /* background-color: white; */
  border-radius: 50%;
}
</style>
