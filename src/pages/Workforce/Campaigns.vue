<template>
  <div class="p-3 bg-white mt-4">
    <div class="flex items-center justify-between">
      <h2 class="font-semibold text-gray-800 text-2xl">Campaigns</h2>
      <div class="flex gap-2 items-center justify-end">
        <div class="w-72">
          <v-col class="p-0">
            <v-autocomplete
              v-model="campaignStatus"
              label="Select Status"
              placeholder="Select Status"
              :items="campaignStatusOptions"
              item-text="label"
              item-value="value"
              hide-details
              outlined
              dense
              clearable
              @change="fetchCurrentCampaigns"
            ></v-autocomplete>
          </v-col>
        </div>
        <div class="w-72">
          <v-col class="py-0">
            <v-menu
              v-model="menu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
            >
              <template v-slot:activator="{ on }">
                <v-text-field
                  label="Select Date Range"
                  placeholder="Select Date Range"
                  v-model="formattedDate"
                  prepend-inner-icon="mdi-calendar"
                  readonly
                  hide-details
                  outlined
                  dense
                  clearable
                  v-on="on"
                  @click:clear="onDateCleared"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="dates"
                @input="onDateSelected"
                no-title
                @change="menu = false"
                range
                scrollable
              />
            </v-menu>
          </v-col>
        </div>
        <v-btn
          @click="toggleShowAddCampaign()"
          color="primary"
          :class="[
            $vuetify.breakpoint.lgAndUp
              ? ' white--text'
              : 'ml-3 flex-col white--text mt-2',
          ]"
        >
          Add Campaign
          <v-icon right dark>mdi-plus</v-icon>
        </v-btn>
      </div>
    </div>
    <div
      v-if="!campaigns.length"
      class="flex flex-col items-center justify-center my-72"
    >
      <h1 class="text-gray-800 text-xl mt-2">No Campaigns found</h1>
    </div>
    <div v-else class="mt-8">
      <v-data-table
        :headers="campaignTableHeaders"
        :items="campaigns"
        :loading="fetchingData"
        class="pl-7 pr-7 pt-4 rounded-lg"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30] }"
        item-key="_id"
        fixed-header
        show-expand
        :single-expand="singleExpand"
        :expanded.sync="expanded"
      >
        <template v-slot:item.title="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.title }}
          </td>
        </template>
        <template v-slot:item.status="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.status === "INITIALIZED" ? "IN_PROGRESS" : item.status }}
          </td>
        </template>
        <template v-slot:item.description="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ item.description }}
          </td>
        </template>
        <template v-slot:item.endDateTime="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ formatDate(item.endDateTime) }}
          </td>
        </template>
        <template v-slot:item.startDateTime="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ formatDate(item.startDateTime) }}
          </td>
        </template>
        <template v-slot:item.createdAt="{ item }">
          <td class="px-4 py-6 font-semibold">
            {{ formatDate(item.createdAt) }}
          </td>
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4">
            <div class="flex items-center gap-4">
              <v-btn
                @click="
                  () => stopCampaign({ campaignId: item._id, indx: item.indx })
                "
                v-if="
                  item.status === 'IN_PROGRESS' || item.status === 'INITIALIZED'
                "
                fab
                x-small
                color="red"
                :loading="item.updatingStatus"
                dark
              >
                <v-icon>mdi-stop</v-icon>
              </v-btn>
              <v-btn
                @click="
                  () => startCampaign({ campaignId: item._id, indx: item.indx })
                "
                v-if="item.status === 'PENDING'"
                fab
                color="green"
                x-small
                dark
                :loading="item.updatingStatus"
              >
                <v-icon>mdi-play</v-icon>
              </v-btn>
              <v-btn
                @click="
                  () =>
                    cancelCampaign({ campaignId: item._id, indx: item.indx })
                "
                color="red"
                elevation="2"
                v-if="item.status !== 'CANCELLED' && false"
                outlined
                :loading="item.cancelling"
                >Cancel</v-btn
              >
            </div>
          </td>
        </template>
        <template v-slot:expanded-item="{ headers }">
          <td :colspan="headers.length">
            <div class="p-2 bg-gray-100">
              <v-data-table
                class="expanded-table"
                :headers="expandedHeaders"
                :items="currentCampaignCases"
                :items-per-page="5"
              >
                <template v-slot:item.caseNo="{ item }">
                  <td
                    @click="redirectToCase(item.case)"
                    class="cursor-pointer hover:text-blue-800 px-4 py-6 font-semibold"
                  >
                    {{ item.case.caseNo }}
                  </td>
                </template>
                <template v-slot:item.customer="{ item }">
                  <td class="px-4 py-6 font-semibold">
                    {{ item.case.customer.customerFullName }}
                  </td>
                </template>
                <template v-slot:item.assignedTo="{ item }">
                  <td class="px-4 py-6 font-semibold">
                    <p
                      v-for="agent in item.case.assignedTo"
                      :key="agent.fullName"
                      class="mb-0"
                    >
                      {{ agent.fullName }}
                    </p>
                  </td>
                </template>
                <template v-slot:item.status="{ item }">
                  <td class="px-4 py-6 font-semibold">
                    {{
                      item.callInfo
                        ? callDialStatus(item.callInfo.response).message
                        : "-"
                    }}
                  </td>
                </template>
              </v-data-table>
            </div>
          </td>
        </template>
      </v-data-table>
    </div>
    <v-dialog v-model="isAddCampaignVisible" width="800" persistent>
      <AddCampaign
        :orgConfig="orgConfig"
        v-if="isAddCampaignVisible"
        @create="onCreateCampaign"
        @cancel="onCancelAddCampaign"
      />
    </v-dialog>
  </div>
</template>

<script>
import { callStatusMap } from "@/utils/workforce/utils.js";
import dayjs from "dayjs";

// import Channels from "@/components/Workforce/Campaign/Channels.vue";
import AddCampaign from "@/components/Workforce/Campaign/AddCampaign.vue";

export default {
  name: "CampaignsPage",
  layout: "Workforce/default",
  components: {
    AddCampaign,
  },
  data() {
    return {
      authData: this.$storage.getUniversal("token"),
      isAddCampaignVisible: false,
      activeSection: 0,
      name: "",
      menu: false,
      campaignStatus: "",
      singleExpand: true,
      expanded: [],
      startDate: null,
      endDate: null,
      dates: [],
      description: "",
      campaigns: [],
      fetchingData: false,
      updatingCampaignStatus: false,
      deletingCampaign: false,
      orgConfig: null,
      currentCampaignCases: [],
      expandedHeaders: [{ text: "Case No", value: "caseNo", sortable: false }],
    };
  },
  watch: {
    expanded: {
      handler(ids) {
        if (!ids.length) return;
        this.currentCampaignCases = [];
        this.expandedHeaders = [];
        const campaignId = ids[0]._id;
        const campaignIndx = this.campaigns.findIndex(
          (c) => c._id === campaignId
        );
        this.fetchCampaign(campaignId, campaignIndx);
      },
    },
    activeSection: {
      handler() {
        this.fetchCurrentCampaigns();
      },
      immediate: false,
    },
  },
  methods: {
    redirectToCase(caseData) {
      const subCases = this.orgConfig.enabledSubCases[0].subCase;
      const subCase = subCases.find((s) => s.name === caseData.caseType);

      const redirectUrl = `/workforce/collection-management/${subCase._id}?caseNo=${caseData.caseNo}`;
      window.open(redirectUrl, "_blank");
    },
    onDateCleared() {
      this.dates = [];
      this.startDate = null;
      this.endDate = null;
      this.fetchCurrentCampaigns();
    },
    onDateSelected() {
      if (this.dates.length === 2) {
        this.startDate = this.$dayjs(this.dates[0]).format("YYYY-MM-DD");
        this.endDate = this.$dayjs(this.dates[1]).format("YYYY-MM-DD");
        this.fetchCurrentCampaigns();
      } else {
        this.startDate = this.$dayjs(this.dates[0]).format("YYYY-MM-DD");
        this.endDate = null;
      }
    },
    callDialStatus(response) {
      const {
        EVENT_TYPE = "",
        A_DIAL_STATUS = "",
        B_DIAL_STATUS = "",
      } = response;

      const key = `${EVENT_TYPE.trim()}|${A_DIAL_STATUS.trim()}|${B_DIAL_STATUS.trim()}`;

      return callStatusMap.get(key);
    },
    populateExpandedHeaders(casesData) {
      const subCases = this.orgConfig.enabledSubCases[0].subCase;
      const subCase = subCases.find(
        (s) => s.name === casesData[0].case.caseType
      );
      const fieldMappings = subCase.fieldMappings;
      this.expandedHeaders = [
        {
          text: fieldMappings.caseNo.displayName,
          value: "caseNo",
          sortable: false,
        },
        {
          text: fieldMappings.assignedTo.displayName,
          value: "assignedTo",
          sortable: false,
        },
        {
          text: fieldMappings.customerFullName.displayName,
          value: "customer",
          sortable: false,
        },
        {
          text: "Status",
          value: "status",
          sortable: false,
        },
      ];
    },
    formatDate(date) {
      return dayjs(date).format("DD MMM YYYY, hh:mm a");
    },
    onCancelAddCampaign() {
      this.isAddCampaignVisible = false;
    },
    toggleShowAddCampaign() {
      this.isAddCampaignVisible = !this.isAddCampaignVisible;
    },
    onCreateCampaign() {
      this.isAddCampaignVisible = false;
      this.fetchCurrentCampaigns();
    },
    async fetchCampaign(campaignId, campaignIndx) {
      try {
        if (!this.orgConfig.enabledSubCases.length) return;
        const cachedCases = this.campaigns[campaignIndx].cases || [];

        if (cachedCases[0]?.caseNo) {
          return this.populateExpandedHeaders(cachedCases);
        }

        const response = await this.$axios.get(
          `/workforce/campaign/${campaignId}`
        );
        const campaign = response.data.data;
        this.campaigns[campaignIndx] = campaign;
        this.currentCampaignCases = campaign.cases;
        this.populateExpandedHeaders(campaign.cases);
      } catch (error) {
        console.error("Error fetching config:", error);
      }
    },
    async fetchConfig() {
      try {
        const response = await this.$axios.get("/workforce/org/config");

        const data = response.data?.config;

        this.orgConfig = data;
      } catch (error) {
        console.error("Error fetching config:", error);
      }
    },
    async startCampaign({ campaignId, indx }) {
      const newCampaigns = [...this.campaigns];
      newCampaigns[indx].cancelling = true;
      this.campaigns = newCampaigns;

      const response = await this.$axios.post(
        "/workforce/campaign",
        {
          campaignId,
          populate: true,
        },
        {
          headers: {
            Authorization: this.authData.access_token,
          },
        }
      );

      if (response.data.success) {
        newCampaigns[indx].starting = false;
        newCampaigns[indx].status = "INITIALIZED";
        this.campaigns = newCampaigns;
        this.$toast.success("Campaign started successfully");
      }
    },
    async cancelCampaign({ campaignId, indx }) {
      const newCampaigns = [...this.campaigns];
      newCampaigns[indx].cancelling = true;
      this.campaigns = newCampaigns;

      await this.$axios.put(
        "/workforce/campaign/status",
        {
          campaignId,
          status: "2",
          channelType: "call",
        },
        {
          headers: {
            Authorization: this.authData.access_token,
          },
        }
      );
      newCampaigns[indx].cancelling = false;
      newCampaigns[indx].status = "CANCELLED";
      this.campaigns = newCampaigns;
      this.$toast.success("Campaign cancelled successfully");
    },
    async stopCampaign({ campaignId }) {
      const response = await this.$axios.post(
        "/workforce/campaign/status",
        {
          campaignId,
          status: "CANCELLED",
        },
        {
          headers: {
            Authorization: this.authData.access_token,
          },
        }
      );

      if (response.data.success) {
        this.$toast.success("Campaign stopped successfully");
      }
    },
    async updateCampaignStatus({ campaignId, status, indx }) {
      const newCampaigns = [...this.campaigns];
      newCampaigns[indx].updatingStatus = true;
      this.campaigns = newCampaigns;

      await this.$axios.put(
        "/workforce/campaign/status",
        {
          campaignId,
          status,
          channelType: "call",
        },
        {
          headers: {
            Authorization: this.authData.access_token,
          },
        }
      );
      newCampaigns[indx].updatingStatus = false;
      newCampaigns[indx].status = status === "1" ? "PAUSED" : "IN_PROGRESS";
      this.campaigns = newCampaigns;
      this.$toast.success(
        status === "1"
          ? "Campaign paused successfully"
          : "Campaign resumed successfully"
      );
    },
    async fetchCampaignsHistory() {
      this.fetchingData = true;
      const response = await this.$axios.get(
        "/workforce/campaign/campaigns?history=true",
        {
          headers: {
            Authorization: this.authData.access_token,
          },
        }
      );
      this.fetchingData = false;

      this.campaigns = response.data.data.map((c, i) => ({
        ...c,
        indx: i,
        id: c.channelType === "call" ? c.channelOptions?.campaignId : "-",
      }));
    },
    async fetchCurrentCampaigns() {
      this.fetchingData = true;
      const response = await this.$axios.get("/workforce/campaign/campaigns", {
        params: {
          status: this.campaignStatus || undefined,
          start: this.startDate || undefined,
          end: this.endDate || undefined,
        },
        headers: {
          Authorization: this.authData.access_token,
        },
      });
      this.fetchingData = false;

      this.campaigns = response.data.data.map((c, i) => ({
        ...c,
        indx: i,
        id: c.channelType === "call" ? c.channelOptions?.campaignId : "-",
      }));
    },
  },
  computed: {
    campaignStatusOptions() {
      return [
        { label: "Pending", value: "PENDING" },
        { label: "In-Progress", value: "INITIALIZED" },
      ];
    },
    formattedDate() {
      if (!this.startDate || !this.endDate) return "Select Date Range";
      return `${this.startDate} to ${this.endDate}`;
    },
    campaignTableHeaders() {
      const headers = [
        { text: "Campaign Title", value: "title", sortable: false },
        { text: "Campaign Description", value: "description", sortable: false },
        { text: "Status", value: "status", sortable: false },
        { text: "Created At", value: "createdAt", sortable: false },
      ];

      if (this.activeSection === 0) {
        headers.push({
          text: "Action",
          value: "action",
          sortable: false,
        });
      }

      return headers;
    },
  },
  mounted() {
    this.fetchCurrentCampaigns();
    this.fetchConfig();
  },
};
</script>

<style scoped></style>
