<template>
  <div>
    <div v-if="!isLoad">
      <v-main style="padding: 0px" class="bg-[#F7FAFB] dark-bg-default pt-2">
        <!-- <v-alert dense border="left" type="warning">
            Attendance and Location history of the previous month will be
            automatically cleared on <strong>10th of every month</strong>. Kindly
            download the reports before it gets cleared, as history cannot be
            <strong>recovered</strong>.
          </v-alert> -->
        <!-- Filter and Export Section (Hidden for Employees tab) -->

        <!-- Active Tab Component -->
        <ActitiveTabcomponent
          :tabs="visibleTabs"
          :default-tab="activeTab"
          @tab-changed="handleTabChange"
        >
          <!-- Tab Actions Slot - wraps filter and export buttons -->
          <template v-slot:tab-buttons="{ activeTab }">
            <div v-if="activeTab !== 'employees'" class="flex items-center space-x-2">
              <FilterPopOver :selectedCount="selectedCount">
                <v-card outlined class="w-72 max-h-96">
                  <div :class="[$vuetify.breakpoint.lgAndUp ? '' : 'mr-auto ml-3 mt-2 mb-2']">
                    <div class="flex items-center" v-if="tabs === 2">
                      <v-label> Matrix </v-label>
                      <v-switch
                        :ripple="false"
                        class="ml-4 flex justify-start items-center custom-switch"
                        inset
                        dense
                        v-model="graphicalView"
                      ></v-switch>
                      <v-label> Graph </v-label>
                    </div>
                  </div>
                  <v-col
                    :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
                    :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
                    v-if="!graphicalView && tabs === 2"
                    class="pb-0"
                  >
                    <v-select
                      label="Select Status"
                      placeholder="Select Status"
                      v-model="selectedTaskStatus"
                      :items="taskSteps"
                      item-text="value"
                      item-value="key"
                      hide-details
                      solo
                      flat
                      multiple
                      small-chips
                      clearable
                      outlined
                      dense
                      :color="$vuetify.theme.currentTheme.primary"
                    >
                      <v-list-item
                        class="pl-4 select-all-item"
                        slot="prepend-item"
                        @click="toggleSelectAll"
                      >
                        <v-list-item-action>
                          <v-icon :color="selectedTaskStatus.length > 0 ? 'blue darken-2' : ''">{{
                            icon
                          }}</v-icon>
                        </v-list-item-action>
                        <v-list-item-title>Select All</v-list-item-title>
                      </v-list-item>
                      <v-divider slot="prepend-item" />
                      <template v-slot:selection="{ item, index }">
                        <v-chip small v-if="index < 1">
                          <span class="chip-text">{{ item.value }}</span>
                        </v-chip>
                        <span v-if="index === 1" class="grey--text text-caption">
                          (+{{ selectedTaskStatus.length - 3 }} more)
                        </span>
                      </template>
                    </v-select>
                  </v-col>

                  <v-col
                    :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
                    :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
                    class="pb-0"
                  >
                    <v-select
                      v-model="selectedColumns"
                      :items="visibleSubColumnOptions"
                      item-value="label"
                      item-text="label"
                      multiple
                      chips
                      label="Select Columns"
                      hide-details
                      solo
                      flat
                      outlined
                      dense
                      v-if="!roasterTab"
                      :color="$vuetify.theme.currentTheme.primary"
                    >
                      <v-list-item
                        class="pl-4 pt-0 select-all-item"
                        slot="prepend-item"
                        @click="toggleSelectAllColumn"
                      >
                        <v-list-item-action>
                          <v-icon :color="selectedColumns.length > 0 ? 'blue darken-2' : ''">{{
                            icon
                          }}</v-icon>
                        </v-list-item-action>
                        <v-list-item-title>Select All</v-list-item-title>
                      </v-list-item>
                      <v-divider slot="prepend-item" />
                      <template v-slot:selection="{ item, index }">
                        <v-chip small v-if="index < 1" class="chip-text-div">
                          <span class="chip-text">{{ item.label }}</span>
                        </v-chip>
                        <span v-if="index === 1" class="grey--text text-caption">
                          (+{{ selectedColumns.length - 1 }} more)
                        </span>
                      </template>
                    </v-select>
                  </v-col>
                  <v-col
                    :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
                    :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
                    class="pb-0"
                  >
                    <v-select
                      v-model="dateRangeType"
                      :items="dateRangeTypes"
                      label="Date Range Type"
                      hide-details
                      solo
                      flat
                      outlined
                      dense
                    ></v-select>
                  </v-col>
                  <v-col
                    :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
                    :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
                    class="pb-0"
                  >
                    <v-menu
                      v-model="menu"
                      :close-on-content-click="false"
                      transition="scale-transition"
                      offset-y
                      class="checkBorder_a"
                    >
                      <template v-slot:activator="{ on }">
                        <v-text-field
                          label="Select Date Range"
                          placeholder="Select Date Range"
                          v-model="formattedMonth"
                          prepend-inner-icon="mdi-calendar"
                          readonly
                          hide-details
                          solo
                          flat
                          outlined
                          dense
                          class="checkBorder"
                          clearable
                          @click:clear="onDateCleared"
                          v-on="on"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-if="dateRangeType === 'Monthly'"
                        v-model="selectedDate"
                        @input="onDateSelected"
                        :min="minDate"
                        :max="maxDate"
                        type="month"
                        scrollable
                      />

                      <v-date-picker
                        v-show="dateRangeType === 'Custom Date'"
                        v-model="customDates"
                        :min="minDate"
                        :max="maxDate"
                        range
                        scrollable
                        type="date"
                        @input="onCustomDateSelected"
                      ></v-date-picker>
                    </v-menu>
                  </v-col>
                  <v-col
                    :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
                    :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
                  >
                    <v-select
                      v-model="employeeStatus"
                      :items="['ACTIVE', 'INACTIVE']"
                      label="Status"
                      hide-details
                      solo
                      flat
                      outlined
                      dense
                      @change="handleInput"
                    ></v-select>
                  </v-col>
                  <!-- <v-col
            :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
            :class="[$vuetify.breakpoint.lgAndUp ? '' : 'ml-auto']"
          >
            <v-text-field
              label="Employee Name"
              placeholder="Employee Name"
              v-model="employeeSearch"
              prepend-inner-icon="mdi-magnify"
              persistent-hint
              hide-details
              solo
              flat
              clearable
              @input="handleInput"
              outlined
              dense
            ></v-text-field>
          </v-col> -->

                  <div
                    :class="[$vuetify.breakpoint.lgAndUp ? 'ml-3' : 'mr-auto ml-3 mt-2 mb-4']"
                    v-if="roasterTab"
                    v-show="isFeatureVisible('ASSIGN_SHIFTS_TO_EMPLOYEE') && canWrite"
                  >
                    <v-btn
                      :color="$vuetify.theme.currentTheme.primary"
                      class="white--text"
                      dark
                      @click.stop="toggleDrawer"
                    >
                      Shift Details
                    </v-btn>
                  </div>
                </v-card>
              </FilterPopOver>
              <div
                :class="[$vuetify.breakpoint.lgAndUp ? '' : 'mr-auto ml-3 mt-2 mb-4']"
                class="mx-3"
              >
                <v-menu offset-y>
                  <template v-slot:activator="{ on }">
                    <v-btn
                      :loading="exportLoading"
                      :disabled="exportLoading"
                      :color="$vuetify.theme.currentTheme.primary"
                      class="white--text"
                      :class="[$vuetify.breakpoint.lgAndUp ? '' : 'w-fit']"
                      v-on="on"
                      v-show="isExportButtonVisible && canEdit"
                    >
                      Export
                      <v-icon right dark>mdi-download</v-icon>
                    </v-btn>
                  </template>
                  <v-list v-if="roasterTab" v-show="isFeatureVisible('DOWNLOAD_ROSTER')">
                    <v-list-item class="text-sm" @click="exportShiftData"
                      >Download Shift Data</v-list-item
                    >
                  </v-list>
                  <v-list v-else v-show="isFeatureVisible('DOWNLOAD_REPORT')">
                    <v-list-item class="text-sm" @click="downloadCheckInUsers"
                      >Aggregated Muster Report (&rarr;)</v-list-item
                    >
                    <v-list-item class="text-sm" @click="downloadUsersData"
                      >Detailed Muster Report (&uarr;)</v-list-item
                    >
                    <v-list-item class="text-sm" @click="exportTaskData"
                      >Download
                      {{ firstSubCaseTabName?.visitTable?.displayName || 'Visit/Task' }}
                      Data</v-list-item
                    >
                  </v-list>
                </v-menu>
              </div>
            </div>
          </template>

          <!-- Tab Content Slots -->
          <template v-for="tab in visibleTabs" v-slot:[tab.key]="">
            <component
              :key="tab.key"
              :is="tab.component"
              :task-steps="taskSteps"
              :orgConfig="orgConfig"
              :employees="employees"
              :teamData="teamData"
              :view="graphicalView"
              :search="search"
              :selectedTaskStatus="selectedTaskStatus"
              :selected-columns="selectedColumns"
              :customDates="customDates"
              :startOfMonth="startOfMonth"
              :endOfMonth="endOfMonth"
              :userIds="userIds"
              :dateRangeType="dateRangeType"
              @updateTeamData="updateTeamData"
              @cancel="cancel"
              ref="exportReport"
              :date-range-type="dateRangeType"
              :menu.sync="menu"
              :formatted-month.sync="formattedMonth"
              :selected-date.sync="selectedDate"
              :custom-dates.sync="customDates"
              :min-date="minDate"
              :max-date="maxDate"
              :date-range-types="dateRangeTypes"
              @update-date-range-type="onDateRangeTypeChange"
              @onDateCleared="onDateCleared"
              @onDateSelects="onDateMonthSelected"
              @onCustomDateSelects="onCustomDateSelecteds"
              :apiLoading="apiLoading"
            />
            <RoasterTable
              v-if="tab.roasterComponent"
              :key="`${tab.key}-roaster`"
              :is="roasterTab"
              :orgConfig="orgConfig"
              :teamData="teamData"
              :startOfMonth="startOfMonth"
              :endOfMonth="endOfMonth"
              :customDates="customDates"
              :dateRangeType="dateRangeType"
              :roasterDrawer="roasterDrawer"
              :search="employeeSearch"
            />
            <v-pagination
              :key="`${tab.key}-pagination`"
              v-model="currentPage"
              :length="totalPages"
              :total-visible="5"
              v-if="
                currentTab &&
                totalPages > 1 &&
                !renderTable &&
                // TimeLineNewVue &&
                activeTab !== 'template'
              "
              :color="$vuetify.theme.currentTheme.primary"
            ></v-pagination>
          </template>
        </ActitiveTabcomponent>
        <DailyVisitExport :employeeData="employeeArray" :orgConfig="orgConfig" />
      </v-main>
    </div>
    <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
  </div>
</template>

<script>
import AttendanceTable from '@/components/Workforce/AttendanceManagement/AttendancePage.vue'
import DailyVisitExport from '@/components/Workforce/AttendanceManagement/DailyVisitExport.vue'
import { debounce, isModuleFeatureAllowed, hasPermission, fullName } from '@/utils/common'
import RoasterTable from '@/components/Workforce/AttendanceManagement/RoasterTable.vue'
import permissionData from '@/utils/permissions'
import { TAB_ITEMS } from '@/utils/workforce/constants'
import FilterPopOver from '@/components/FilterPopOver.vue'
import EmployeePageCard from '@/components/EmployeePageCard.vue'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'
// import TimeLineNewVue from '@/components/Workforce/AttendanceManagement/TimeLineNew.vue'

export default {
  name: 'SchedulerPage',
  layout: 'Workforce/default',
  components: {
    DailyVisitExport,
    AttendanceTable,
    RoasterTable,
    EmployeePageCard,
    FilterPopOver,
    ActitiveTabcomponent
    // TimeLineNewVue
  },
  data() {
    return {
      userOrg: this.$storage.getUniversal('user'),
      userData: this.$store.state.wfmPermissions,
      subCases: this.$store.state.subCases,
      tabs: 0,
      taskSteps: [],
      teamData: [],
      employeeSearch: '',
      search: '',
      orgConfig: null,
      exportReport: null,
      isCheckedIn: true,
      menu: false,
      exportLoading: false,
      graphicalView: true,
      selectedCount: 1,
      employeeStatus: 'ACTIVE',
      selectedTaskStatus: ['COMPLETED', 'MISSED'],
      selectedColumns: ['Attendance', 'First Clock-In', 'Last Clock-In', 'Last Clock-Out'],
      subColumnOptions: [
        { label: 'Attendance', visible: true },
        { label: 'First Clock-In', visible: true },
        { label: 'Last Clock-In', visible: true },
        { label: 'Last Clock-Out', visible: true },
        { label: 'Working Hours', visible: true },
        { label: 'Status', visible: true },
        { label: 'Overtime', visible: true },
        {
          label: 'Clock-In Image',
          visible: this.isAttendanceFeature('CLOCKIN_WITH_SELFIE')
        },
        {
          label: 'Clock-Out Image',
          visible: this.isAttendanceFeature('CLOCKOUT_WITH_SELFIE')
        },
        { label: 'Delay In Shift', visible: true },
        { label: 'Breaks Count', visible: true },
        { label: 'Distance (km)', visible: true }
      ],
      checkInOptions: [
        { text: 'Clocked In', value: true },
        { text: 'Not Clocked In', value: false }
      ],
      dateRangeType: 'Monthly',
      dateRangeTypes: ['Monthly', 'Custom Date'],
      selectedDate: this.$dayjs().format('YYYY-MM-DD'),
      startOfMonth: '',
      endOfMonth: '',
      customDates: [],
      testModel: [],
      totalPages: null,
      currentPage: 1,
      userIds: [],
      employeeArray: [],
      isLoad: true,
      roasterDrawer: false,
      apiLoading: false,
      parallelCallDateRange: 12
    }
  },
  watch: {
    '$route.query.search': {
      handler(inputVal) {
        try {
          this.search = inputVal
          this.currentPage = 1
          this.debouncedFetchUsers()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },
    currentPage: {
      handler(newPage) {
        if (newPage) {
          this.onDateSelected()
          this.disablePagination = true
        }
      },
      immediate: true
    },
    roasterTab: {
      handler() {
        this.roasterDrawer = false
      }
    },
    activeTab: {
      handler(newTab) {
        const tabIndex = this.visibleTabs.findIndex((tab) => tab.key === newTab)
        this.tabs = tabIndex !== -1 ? tabIndex : 0

        if (this.tabs === 1) {
          this.selectedDate = this.$dayjs().endOf('month').format('YYYY-MM-DD')
          this.updateSelectedDate()
        }
        this.selectedDate = new Date().toISOString().substr(0, 10)
        this.onDateSelected()
        this.customDates = []
      }
    }
  },
  methods: {
    tabVisible(key) {
      if (key == 'attendance') {
        return this.canRead && this.isFeatureVisible('MANAGE_ATTENDANCE')
      } else if (key == 'timeline') {
        return true
      } else if (key == 'roaster') {
        return this.canRead && this.isFeatureVisible('MANAGE_ROSTER')
      } else if (key == 'employees') {
        return true
      }
      return false
    },
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },
    onDateRangeTypeChange(newType) {
      this.dateRangeType = newType
    },
    onDateCleared() {
      this.selectedDate = null
      this.customDates = []
    },
    onDateMonthSelected(date) {
      this.selectedDate = date
      this.onDateSelected()
    },
    onCustomDateSelecteds(dates) {
      this.customDates = dates
    },
    async fetchTaskSteps() {
      try {
        const response = await this.$axios.get(`/workforce/task/steps`)

        const taskStepsArray = []

        for (const key in response.data.taskSteps) {
          if (Object.prototype.hasOwnProperty.call(response.data.taskSteps, key)) {
            taskStepsArray.push({
              key: key,
              value: response.data.taskSteps[key]
            })
          }
        }

        this.taskSteps = taskStepsArray
      } catch (error) {
        console.log(error)
      }
    },
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config
        return data || {}
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async fetchDataInIntervals(apiEndpoint, moduleName) {
      try {
        let paramsArray = []
        const userIdChunks = []
        const batchSize = 5

        for (let i = 0; i < this.userIds.length; i += 12) {
          userIdChunks.push(this.userIds.slice(i, i + 12)?.join(','))
        }

        if (this.dateRangeType === 'Monthly' && this.userIds.length > 12) {
          const daysInMonth = this.$dayjs(this.endOfMonth).format('DD')

          for (let start = 1; start <= daysInMonth; start += this.parallelCallDateRange) {
            const end = Math.min(start + (this.parallelCallDateRange - 1), daysInMonth)
            userIdChunks.forEach((userIds) => {
              paramsArray.push({
                start: this.$dayjs(this.startOfMonth).date(start).format('YYYY-MM-DD'),
                end: this.$dayjs(this.endOfMonth).date(end).format('YYYY-MM-DD'),
                userIds
              })
            })
          }
        } else if (this.dateRangeType === 'Monthly' && this.userIds.length <= 12) {
          paramsArray.push({
            start: this.startOfMonth,
            end: this.endOfMonth,
            userIds: this.userIds.join(',')
          })
        } else if (this.dateRangeType === 'Custom Date') {
          userIdChunks.forEach((userIds) => {
            paramsArray.push({
              start: this.$dayjs(this.customDates[0]).format('YYYY-MM-DD'),
              end: this.$dayjs(this.customDates[1]).format('YYYY-MM-DD'),
              userIds
            })
          })
        }

        this.apiLoading = true
        const results = []

        for (let i = 0; i < paramsArray.length; i += batchSize) {
          const batch = paramsArray.slice(i, i + batchSize)
          const requests = batch.map((params) => this.$axios.get(apiEndpoint, { params }))
          const responses = await Promise.all(requests)
          results.push(...responses.flatMap((response) => response.data[moduleName] || []))
        }

        this.apiLoading = false
        return results
      } catch (error) {
        console.error(`Error fetching data from ${apiEndpoint}:`, error)
        return []
      }
    },
    async fetchTeam() {
      try {
        const fetchedTeamData = await this.fetchDataInIntervals(
          '/workforce/user/v2/attendance',
          'employeeAttendance'
        )

        const attendanceMap = {}
        fetchedTeamData.forEach((attendance) => {
          const employeeId = attendance.userId._id
          if (!attendanceMap[employeeId]) {
            attendanceMap[employeeId] = []
          }
          attendanceMap[employeeId].push(attendance)
        })

        const transformedTeamData = []

        this.employees?.forEach((employee) => {
          const employeeId = employee._id

          if (Object.prototype.hasOwnProperty.call(attendanceMap, employeeId)) {
            attendanceMap[employeeId].forEach((el) => {
              transformedTeamData.push({
                ...el,
                profileImage: employee.profileImage,
                userId: {
                  ...el.userId,
                  reportesTo: employee.reportesTo
                }
              })
            })
          } else {
            transformedTeamData.push({
              userId: employee,
              checkInOutData: [],
              attendanceDate: '',
              attendanceMarkedBySystem: false,
              profileImage: employee.profileImage
            })
          }
        })
        this.teamData = transformedTeamData
      } catch (error) {
        console.error('Error fetching team data:', error)
      } finally {
        this.isLoad = false
      }
    },
    async fetchUsers(limit = 12) {
      try {
        const params = {
          limit,
          page: this.currentPage,
          sortBy: 'firstName',
          sortOrder: 'asc',
          status: this.employeeStatus
        }

        if (limit === '*') {
          params.page = 1
        }

        if (this.search) {
          params.search = this.search
        }
        if (this.userOrg?.branch && !this.userOrg?.role?.includes('WFM_ADMIN')) {
          params.branch = this.userOrg?.branch
        }
        const response = await this.$axios.get('/workforce/v2/users', {
          params
        })
        this.employees = response.data?.users
        if (this.employees && this.employees.length > 0) {
          this.userIds = this.employees.map((user) => user._id)
        }
        this.totalPages = response.data?.pagination.totalPages
        await this.fetchTeam()
      } catch (error) {
        console.log(error)
      }
    },
    async fetchAllLocations() {
      try {
        const locationHistory = await this.fetchDataInIntervals(
          '/workforce/user/v2/location',
          'locationHistory'
        )

        const employeeData = locationHistory.reduce((acc, location) => {
          const userId = location.userId?._id
          const matchedUser = this.employees?.find((el) => el._id === userId)
          const empRole = matchedUser?.wfmRole
          const empCity = matchedUser?.city
          const designation = matchedUser?.designation

          if (!acc[userId]) {
            acc[userId] = {
              fullName: location.userId?.fullName,
              empId: location.userId?._id,
              empRole: empRole?.role || '-',
              empCity: empCity || '-',
              designation: designation || '-',
              employeeId: location.userId?.employeeId,
              locationData: [],
              branch: location.userId?.branch?.branchName,
              reportesTo: fullName(location.userId?.reportesTo)
            }
          }
          acc[userId].locationData.push(location)
          return acc
        }, {})

        this.employeeArray = Object.values(employeeData)
      } catch (error) {
        console.log(error)
      }
    },
    toggleSelectAll() {
      this.$nextTick(() => {
        if (this.allTaskStatusSelected) {
          this.selectedTaskStatus = []
        } else {
          this.selectedTaskStatus = this.taskSteps.map((step) => step.key)
        }
      })
    },
    toggleSelectAllColumn() {
      this.$nextTick(() => {
        if (this.allColumnSelected) {
          this.selectedColumns = []
        } else {
          this.selectedColumns = this.visibleSubColumnOptions.map((step) => step.label)
        }
      })
    },

    async onDateSelected() {
      const selectedMonth = this.$dayjs(this.selectedDate).month()
      const currentMonth = this.$dayjs().month()
      const selectedYear = this.$dayjs(this.selectedDate).year()
      const currentYear = this.$dayjs().year()

      if (this.tabs === 0) {
        this.startOfMonth = this.$dayjs(this.selectedDate).startOf('month').format('YYYY-MM-DD')

        if (selectedMonth === currentMonth && selectedYear === currentYear) {
          this.endOfMonth = this.$dayjs().format('YYYY-MM-DD')
        } else {
          this.endOfMonth = this.$dayjs(this.selectedDate).endOf('month').format('YYYY-MM-DD')
        }
      } else if (this.tabs === 1) {
        this.startOfMonth = this.$dayjs(this.selectedDate).startOf('month').format('YYYY-MM-DD')
        this.endOfMonth = this.$dayjs(this.selectedDate).endOf('month').format('YYYY-MM-DD')
      }
      await this.fetchUsers()
    },
    onCustomDateSelected(newDates) {
      if (newDates && newDates.length === 2) {
        this.customDates = newDates
        this.fetchTeam()
      }
    },
    // onDateCleared() {
    //   this.selectedDate = new Date().toISOString().substr(0, 10);
    //   this.fetchTeam();
    // },
    handleInput: async function () {
      this.search = this.employeeSearch
      this.debouncedFetchUsers()
    },
    async downloadCheckInUsers() {
      this.exportLoading = true
      try {
        await this.fetchUsers('*')
        this.$root.$emit('export-excel')
      } catch (error) {
        console.error('Error downloading check-in users:', error)
      } finally {
        this.exportLoading = false
      }
    },
    async downloadUsersData() {
      this.exportLoading = true
      try {
        this.currentPage = 1
        await this.fetchUsers('*')
        this.$root.$emit('export-format-excel')
      } catch (error) {
        console.error('Error downloading check-in users:', error)
      } finally {
        this.exportLoading = false
      }
    },
    async cancel() {
      await this.fetchUsers('12')
    },
    async exportTaskData() {
      this.exportLoading = true
      try {
        await this.fetchUsers('*')
        await this.fetchAllLocations()
        this.$root.$emit('export-daily-excel')
      } catch (error) {
        console.log(error)
      } finally {
        this.exportLoading = false
      }
    },
    async exportShiftData() {
      this.exportLoading = true
      try {
        this.$root.$emit('export-data')
      } catch (error) {
        console.log(error)
      } finally {
        this.exportLoading = false
      }
    },
    updateTeamData(data) {
      const index = this.teamData.indexOf(data)
      if (index !== -1) {
        const newTeamData = this.teamData.splice(index, 1, data)
        return newTeamData
      }
    },
    toggleDrawer() {
      this.roasterDrawer = !this.roasterDrawer
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('ATTENDANCE', feature)
    },
    isAttendanceFeature(feature) {
      return isModuleFeatureAllowed('ATTENDANCE_MANAGEMENT', feature)
    },
    updateSelectedDate() {
      if (this.tabs == 1) {
        this.selectedDate = this.$dayjs().endOf('month').format('YYYY-MM-DD')
      }
    }
  },
  async mounted() {
    const query = { ...this.$route.query }
    if (!query.activeTab) {
      const defaultTab = this.visibleTabs[0]?.key
      if (defaultTab) {
        query.activeTab = defaultTab
        this.$router.replace({ query })
      }
    }
    await this.fetchTaskSteps()
    this.orgConfig = await this.getConfig()
    this.isLoad = false
  },
  computed: {
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.visibleTabs[0]?.key || 'attendance'
    },
    currentTab() {
      return this.tabItems[this.tabs]?.component
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.attendenceEdit)
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.attendenceWrite)
    },
    canRead() {
      return hasPermission(this.userData, permissionData.attendenceRead)
    },
    tabItems() {
      // console.log('tabItems', TAB_ITEMS.TEAM_REVIEW)
      return TAB_ITEMS.TEAM_REVIEW.map((tab) => ({
        ...tab,
        visibility: this.tabVisible(tab.key)
      }))
    },
    roasterTab() {
      return this.tabItems[this.tabs].roasterComponent
    },
    isExportButtonVisible() {
      return this.isFeatureVisible('DOWNLOAD_REPORT') || this.isFeatureVisible('DOWNLOAD_ROSTER')
    },
    formattedMonth() {
      if (this.dateRangeType === 'Monthly') {
        return this.$dayjs(this.selectedDate).format('MMMM YYYY')
      } else {
        if (this.customDates.length === 2) {
          const start = this.$dayjs(this.customDates[0]).format('YYYY-MM-DD')
          const end = this.$dayjs(this.customDates[1]).format('YYYY-MM-DD')
          return `${start} to ${end}`
        } else if (this.customDates.length === 1) {
          return this.$dayjs(this.customDates[0]).format('YYYY-MM-DD')
        }
        return this.$dayjs(this.customDates[0]).format('YYYY-MM-DD')
      }
      // return this.$dayjs().format("MMMM YYYY");
    },
    allTaskStatusSelected() {
      return this.selectedTaskStatus.length === this.taskSteps.length
    },
    someTaskStatusSelected() {
      return this.selectedTaskStatus.length > 0 && !this.allTaskStatusSelected
    },
    icon() {
      if (this.allTaskStatusSelected) return 'mdi-close-box'
      if (this.someTaskStatusSelected) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },
    allColumnSelected() {
      return this.selectedColumns.length === this.subColumnOptions.length
    },
    someColumnSelected() {
      return this.selectedColumns.length > 0 && !this.allColumnSelected
    },
    iconColumn() {
      if (this.allColumnSelected) return 'mdi-close-box'
      if (this.someColumnSelected) return 'mdi-minus-box'
      return 'mdi-checkbox-blank-outline'
    },
    minDate() {
      const now = this.$dayjs()
      const currentYear = now.year()
      return this.$dayjs().subtract(1, 'year').startOf('month').format('YYYY-MM-DD')
    },
    maxDate() {
      if (this.tabs == 1) {
        const now = this.$dayjs()
        const currentYear = now.year()
        return this.$dayjs().endOf('month').format('YYYY-MM-DD')
      } else {
        return this.$dayjs().format('YYYY-MM-DD')
      }
    },
    renderTable() {
      return this.dateRangeType === 'Custom Date' && this.customDates.length !== 2
    },
    visibleSubColumnOptions() {
      return this.subColumnOptions?.filter((option) => option.visible)
    },
    visibleTabs() {
      return this.tabItems
        .filter((tab) => tab.visibility)
        .map((tab) => ({
          key: tab.key,
          label: tab.name,
          icon: tab.icon,
          component: tab.component,
          roasterComponent: tab.roasterComponent
        }))
    },
    firstSubCaseTabName() {
      if (!this.subCases?.[0]?.subCase) return {}
      const firstSubCase = this.subCases?.[0]?.subCase || {}
      return firstSubCase[0]?.tableTabMapping || {}
    }
  },
  beforeCreate() {
    this.debouncedFetchUsers = () => {}
  },
  created() {
    this.debouncedFetchUsers = debounce(this.fetchUsers, 800)
  }
}
</script>

<style scoped>
.v-tab:before {
  background-color: transparent;
}

.v-tab {
  font-size: 16px;
}

.chip-text-div {
  max-width: 50%;
  /* outline: 1px solid red; */
}
.v-list {
  padding-top: 0px !important;
}
</style>
