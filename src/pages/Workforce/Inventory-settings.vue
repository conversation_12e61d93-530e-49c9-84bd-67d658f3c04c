<template>
  <div>
    <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
      <!-- Tab Actions Slot - wraps child component buttons -->
      <template v-slot:tab-buttons="{ activeTab }">
        <div class="flex items-center space-x-2">
          <!-- Stores tab buttons -->
          <template v-if="activeTab === 'Stores'">
            <v-btn
              @click="handleStoreAction('add')"
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              v-show="canWrite"
            >
              <v-icon left dark>mdi-plus</v-icon>
              Add
            </v-btn>
          </template>

          <!-- Partners tab buttons
          <template v-if="activeTab === 'Partners'">
            <v-tooltip top>
              <template #activator="{ on }">
                <v-btn
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text mr-4"
                  v-on="on"
                  v-show="canWrite"
                  @click="handlePartnerAction('bulk-create')"
                >
                  Bulk Create
                </v-btn>
              </template>
              <span>Bulk create new brands and products</span>
            </v-tooltip>
            <v-btn
              @click="handlePartnerAction('add')"
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              v-show="canWrite"
            >
              <v-icon left dark>mdi-plus</v-icon>
              Add
            </v-btn>
          </template> -->

          <!-- Products tab buttons -->
          <template v-if="activeTab === 'Products'">
            <v-tooltip top>
              <template #activator="{ on }">
                <v-btn
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text mr-4"
                  v-on="on"
                  v-show="canWrite"
                  @click="handleProductAction('bulk-create')"
                >
                  Bulk Create
                </v-btn>
              </template>
              <span>Bulk create new products and brands</span>
            </v-tooltip>
            <v-btn
              @click="handleProductAction('add')"
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              v-show="canWrite"
            >
              <v-icon left dark>mdi-plus</v-icon>
              Add
            </v-btn>
          </template>
        </div>
      </template>

      <!-- Tab Content Slots -->
      <template v-for="tab in visibleTabItems" v-slot:[tab.key]="">
        <div v-if="!isLoad">
          <v-main class="bg-[#F7FAFB] dark-bg-default" style="padding: 0px">
            <component :is="tab.component" :ref="tab.key" />
          </v-main>
        </div>
        <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </template>
    </ActitiveTabcomponent>
  </div>
</template>

<script>
import Stores from '~/components/Workforce/InventorySettings/Stores.vue'
import Partners from '~/components/Workforce/InventorySettings/Partners.vue'
import Products from '~/components/Workforce/InventorySettings/Products.vue'
import { isModuleFeatureAllowed, hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'
import { active } from 'sortablejs'

export default {
  name: 'InventorySettings',
  layout: 'Workforce/default',
  components: {
    Stores,
    Partners,
    Products,
    ActitiveTabcomponent
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      tabs: 0,
      isLoad: true
    }
  },
  computed: {
    canRead() {
      return hasPermission(this.userData, permissionData.inventoryRead)
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    },
    tabItems() {
      return [
        {
          key: 'Stores',
          label: 'Stores',
          icon: 'mdi-store',
          component: 'Stores',
          visible: this.canRead && this.isFeatureVisible('MANAGE_STORES')
        },
        // {
        //   key: 'Partners',
        //   label: 'Brands',
        //   icon: 'mdi-bank',
        //   component: 'Partners',
        //   visible: this.canRead && this.isFeatureVisible('MANAGE_PARTNERS')
        // },
        {
          key: 'Products',
          label: 'Products',
          icon: 'mdi-package-variant',
          component: 'Products',
          visible: this.canRead && this.isFeatureVisible('MANAGE_PARTNER_PRODUCT_RELATION')
        }
      ]
    },
    visibleTabItems() {
      return this.tabItems.filter((tab) => tab.visible)
    },
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.visibleTabItems.length > 0 ? this.visibleTabItems[0].key : 'Stores'
    }
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },

    handleStoreAction(action) {
      const storeComponent = this.$refs.Stores?.[0]
      if (storeComponent) {
        if (action === 'add') {
          storeComponent.toggleshowAddStore()
        }
      }
    },

    handlePartnerAction(action) {
      const partnerComponent = this.$refs.Partners?.[0]
      if (partnerComponent) {
        if (action === 'add') {
          partnerComponent.toggleshowAddPartner()
        } else if (action === 'bulk-create') {
          partnerComponent.uploadBrands()
        }
      }
    },

    handleProductAction(action) {
      const productComponent = this.$refs.Products?.[0]
      if (productComponent) {
        if (action === 'add') {
          productComponent.toggleshowAddProduct()
        } else if (action === 'bulk-create') {
          productComponent.uploadBrands()
        }
      }
    },

    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('STOCK_MANAGEMENT', feature)
    }
  },
  mounted() {
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }

    // Remove loading state and detect initial tab buttons
    this.$nextTick(() => {
      this.isLoad = false
    })
  }
}
</script>

<style scoped>
.v-tab:before {
  background-color: transparent;
}

.v-tab {
  font-size: 16px;
}

.v-btn {
  text-transform: none !important;
  font-size: 12px;
}
</style>
