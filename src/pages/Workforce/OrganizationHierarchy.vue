<template>
  <div>
    <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
      <template v-slot:tab-buttons="{ activeTab }">
        <div class="flex items-center space-x-2">
          <!-- Role tab actions -->
          <template v-if="activeTab === 'role'">
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              @click="handleAddRole"
              dense
            >
              <v-icon left dark>mdi-plus</v-icon>
              Add
            </v-btn>
          </template>

          <!-- Employees tab actions -->
          <template v-if="activeTab === 'employees'">
            <!-- Add employeea button -->
            <v-btn
              @click="onboardNewEmployees"
              v-show="isFeatureVisible('CREATE_EMPLOYEES') && canWrite"
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
            >
              <v-icon left>mdi-plus</v-icon>
              Add {{ orgLanguage.employees }}
            </v-btn>
            <FilterPopOver :selectedCount="selectedCount">
              <v-card
                outlined
                class="w-72 max-h-96"
                :color="$vuetify.theme.currentTheme.navigationColor"
              >
                <v-list>
                  <v-list-item class="px-0">
                    <v-col class="pb-0">
                      <v-select
                        class="wfm-search-fields"
                        :items="teams"
                        hide-details
                        solo
                        flat
                        clearable
                        outlined
                        label="Select Team"
                        placeholder="Select Team"
                        v-model="selectedTeamName"
                        item-text="teamName"
                        item-value="_id"
                        @input="handleEmployeeInput"
                        dense
                      ></v-select>
                    </v-col>
                  </v-list-item>
                  <v-list-item class="px-0">
                    <v-col class="pb-0">
                      <v-select
                        class="wfm-search-fields"
                        :items="branches"
                        hide-details
                        solo
                        flat
                        clearable
                        outlined
                        label="Select Branch"
                        placeholder="Select Branch"
                        v-model="selectedBranchName"
                        item-text="branchName"
                        item-value="_id"
                        @input="handleEmployeeInput"
                        dense
                      ></v-select>
                    </v-col>
                  </v-list-item>
                  <v-list-item class="px-0">
                    <v-col class="pb-0">
                      <v-select
                        class="wfm-search-fields"
                        :items="roles"
                        hide-details
                        solo
                        flat
                        clearable
                        outlined
                        label="Select Role"
                        placeholder="Select Role"
                        v-model="selectedRoleFilter"
                        item-text="role"
                        item-value="_id"
                        @input="handleEmployeeInput"
                        dense
                      ></v-select>
                    </v-col>
                  </v-list-item>
                  <v-list-item class="px-0">
                    <v-col class="pb-0">
                      <v-select
                        class="wfm-search-fields"
                        :items="status"
                        hide-details
                        solo
                        flat
                        outlined
                        label="Select Status"
                        placeholder="Select Status"
                        v-model="selectedStatus"
                        item-text="role"
                        item-value="_id"
                        @input="handleEmployeeInput"
                        dense
                      ></v-select>
                    </v-col>
                  </v-list-item>
                </v-list>
              </v-card>
            </FilterPopOver>
            <v-menu offset-y class="w-16 ml-4">
              <template v-slot:activator="{ on }">
                <v-btn icon v-on="on" height="43px">
                  <v-icon>mdi-dots-vertical</v-icon>
                </v-btn>
              </template>
              <v-list>
                <!-- Parent: Assign -->
                <v-menu open-on-hover offset-x :close-on-content-click="false" left>
                  <template v-slot:activator="{ on, attrs }">
                    <v-list-item
                      v-on="on"
                      v-bind="attrs"
                      :class="{ highlighted: hoveredParent === 'Assign' }"
                      @mouseover="hoveredParent = 'Assign'"
                      @mouseleave="hoveredParent = null"
                    >
                      <v-icon class="mr-2" :class="{ highlighted: hoveredParent === 'Assign' }"
                        >mdi-menu-left</v-icon
                      >
                      <v-list-item-title>Assign</v-list-item-title>
                    </v-list-item>
                  </template>
                  <v-list>
                    <v-list-item
                      @click="handleShowDialog('Teams')"
                      v-show="isFeatureVisible('ASSIGN_TEAM') && isAnyTeamSettingAllowed"
                    >
                      <v-list-item-title>Teams</v-list-item-title>
                    </v-list-item>
                    <v-list-item
                      @click="handleShowDialog('Branches')"
                      v-show="isFeatureVisible('ASSIGN_BRANCH') && isAnyBranchSettingAllowed"
                    >
                      <v-list-item-title>Branch</v-list-item-title>
                    </v-list-item>
                    <v-list-item
                      @click="handleShowDialog('SalaryTemplate')"
                      v-show="
                        isFeatureVisible('ASSIGN_SALARY_TEMPLATE') && isAnySalarySettingAllowed
                      "
                    >
                      <v-list-item-title>Salary Template</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>

                <!-- Parent: Export -->
                <v-menu open-on-hover offset-x :close-on-content-click="false" left>
                  <template v-slot:activator="{ on, attrs }">
                    <v-list-item
                      v-on="on"
                      v-bind="attrs"
                      :class="{ highlighted: hoveredParent === 'Export' }"
                      @mouseover="hoveredParent = 'Export'"
                      @mouseleave="hoveredParent = null"
                    >
                      <v-icon class="mr-2" :class="{ highlighted: hoveredParent === 'Export' }"
                        >mdi-menu-left</v-icon
                      >
                      <v-list-item-title>Export</v-list-item-title>
                    </v-list-item>
                  </template>
                  <v-list>
                    <v-list-item @click="fetchAndDownloadInactive('xlsx')">
                      <v-list-item-title>Non-Engaged {{ orgLanguage.employees }}</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="exportAllEmployees">
                      <v-list-item-title>All {{ orgLanguage.employees }}</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>

                <!-- Parent: Employee -->
                <v-menu open-on-hover offset-x :close-on-content-click="false" left>
                  <template v-slot:activator="{ on, attrs }">
                    <v-list-item
                      v-on="on"
                      v-bind="attrs"
                      :class="{ highlighted: hoveredParent === 'Employee' }"
                      @mouseover="hoveredParent = 'Employee'"
                      @mouseleave="hoveredParent = null"
                    >
                      <v-icon class="mr-2" :class="{ highlighted: hoveredParent === 'Employee' }"
                        >mdi-menu-left</v-icon
                      >
                      <v-list-item-title>{{ orgLanguage.employees }}</v-list-item-title>
                    </v-list-item>
                  </template>
                  <v-list>
                    <v-list-item
                      :loading="loadingImport"
                      :disabled="loadingImport"
                      @click="importEmployees"
                      v-show="canWrite"
                    >
                      <v-list-item-title>Refresh List</v-list-item-title>
                    </v-list-item>
                    <v-list-item
                      @click="openMap"
                      v-show="isFeatureVisible('EMPLOYEE_LIVE_TRACKING')"
                    >
                      <v-list-item-title>Live Location</v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </v-list>
            </v-menu>
          </template>

          <!-- Branches tab actions -->
          <template v-if="activeTab === 'branches'">
            <v-btn color="primary" @click="handleAddBranch" class="font-medium">
              <v-icon left>mdi-plus</v-icon>
              Add
            </v-btn>
          </template>

          <!-- Organization hierarchy tab actions -->
          <template v-if="activeTab === 'organizationhierarchy'">
            <slot name="organizationhierarchy-actions"></slot>
          </template>
        </div>
      </template>

      <!-- Tab Component Content -->
      <template v-for="tab in visibleTabs" v-slot:[tab.key]="">
        <div v-if="!isLoad" :key="tab.key">
          <v-main style="padding: 0px" class="bg-[#F7FAFB] dark-bg-default">
            <div v-if="tab.key === 'organizationhierarchy'">
              <TreeNode />
            </div>
            <component
              v-else
              :is="tab.component"
              :key="tab.key"
              v-bind="getComponentProps(tab)"
              :ref="tab.key"
            />
          </v-main>
        </div>
        <div v-else class="loading-container" :key="`loading-${tab.key}`">
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </template>
    </ActitiveTabcomponent>
  </div>
</template>

<script>
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'
import { TAB_ITEMS } from '@/utils/workforce/constants'
import EmployeesPage from '@/pages/Workforce/EmployeesPage.vue'
import Roles from '@/components/Workforce/Settings/Roles.vue'
import TreeNode from '@/components/Workforce/EmployeeManagement/TreeNode.vue'
import BranchTeam from '@/components/Workforce/Settings/OrgConfigComponents/BranchTeam.vue'
import FilterPopOver from '@/components/FilterPopOver.vue'
import { hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'

export default {
  name: 'OrganizationHierarchy',
  components: {
    ActitiveTabcomponent,
    EmployeesPage,
    Roles,
    TreeNode,
    BranchTeam,
    FilterPopOver
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      isLoad: false,
      employeeTab: 0,
      totalEmployees: 0,
      totalDepartments: 0,
      hierarchyLevels: 0,
      // Role tab data
      additionalPermissions: [],
      // Employee tab data

      teams: [],
      branches: [],
      roles: [],
      status: ['ACTIVE', 'INACTIVE', 'CLOSED'],
      selectedTeamName: null,
      selectedBranchName: null,
      selectedRoleFilter: null,
      selectedStatus: 'ACTIVE',
      hoveredParent: null,
      loadingImport: false
    }
  },
  computed: {
    orgLanguage() {
      return this.orgLang.data.leftNav
    },
    tabItems() {
      return TAB_ITEMS.ORGANIZATION.filter((tab) => this.tabVisible(tab.key)).map((tab) => ({
        ...tab,
        label: tab.name,
        icon: tab.icon || 'mdi-account-group',
        visibility: true,
        component: this.getTabComponent(tab.key)
      }))
    },
    visibleTabs() {
      return this.tabItems.filter((tab) => tab.visibility)
    },
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.visibleTabs.length > 0 ? this.visibleTabs[0].key : 'employees'
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.userWrite)
    },
    selectedCount() {
      let count = 0

      // Count selected filters
      if (this.selectedTeamName) count++
      if (this.selectedBranchName) count++
      if (this.selectedRoleFilter) count++
      if (this.selectedStatus) count++

      return count
    }
  },
  methods: {
    tabVisible(key) {
      const visibilityMap = {
        employees: true,
        role: true,
        branches: true,
        team: true,
        organizationhierarchy: true
      }
      return visibilityMap[key] || false
    },
    getTabComponent(key) {
      const componentMap = {
        employees: 'EmployeesPage',
        role: 'Roles',
        branches: 'BranchTeam',
        team: 'BranchTeam',
        organizationhierarchy: null
      }
      return componentMap[key]
    },
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },
    handleAddRole() {
      // Call the addRole method from the Roles component
      if (this.$refs.role && this.$refs.role[0]) {
        this.$refs.role[0].openForm()
      }
    },
    handleAddBranch() {
      if (this.$refs.branches && this.$refs.branches[0]) {
        this.$refs.branches[0].showBranchModal = true
      }
    },
    // Employee tab methods
    handleEmployeeInput() {
      // Delegate to EmployeesPage component
      if (this.$refs.employees && this.$refs.employees[0]) {
        const empComponent = this.$refs.employees[0]
        empComponent.selectedTeamName = this.selectedTeamName
        empComponent.selectedBranchName = this.selectedBranchName
        empComponent.selectedRoleFilter = this.selectedRoleFilter
        empComponent.selectedStatus = this.selectedStatus
        empComponent.handleInput()
      }
    },
    async fetchEmployeesData() {
      try {
        // Fetch teams
        const teamsResponse = await this.$axios.get('/workforce/teams')
        this.teams = teamsResponse.data.teams || []

        // Fetch branches
        const branchesResponse = await this.$axios.get('/workforce/branches')
        this.branches = branchesResponse.data.branches || []

        // Fetch roles
        const rolesResponse = await this.$axios.get('workforce/org/role')
        this.roles = rolesResponse.data.role || []
      } catch (error) {
        console.error('Error fetching employees data:', error)
      }
    },
    // Employee menu methods
    handleShowDialog(type) {
      // Delegate to EmployeesPage component
      if (this.$refs.employees && this.$refs.employees[0]) {
        this.$refs.employees[0].handleShowDialog(type)
      }
    },
    fetchAndDownloadInactive(format) {
      // Delegate to EmployeesPage component
      if (this.$refs.employees && this.$refs.employees[0]) {
        this.$refs.employees[0].fetchAndDownloadInactive(format)
      }
    },
    exportAllEmployees() {
      // Delegate to EmployeesPage component
      if (this.$refs.employees && this.$refs.employees[0]) {
        this.$refs.employees[0].exportAllEmployees()
      }
    },
    importEmployees() {
      // Delegate to EmployeesPage component
      if (this.$refs.employees && this.$refs.employees[0]) {
        this.$refs.employees[0].importEmployees()
      }
    },
    onboardNewEmployees() {
      // Delegate to EmployeesPage component
      if (this.$refs.employees && this.$refs.employees[0]) {
        this.$refs.employees[0].onboardNewEmployees()
      }
    },
    openMap() {
      // Delegate to EmployeesPage component
      if (this.$refs.employees && this.$refs.employees[0]) {
        this.$refs.employees[0].openMap()
      }
    },
    isFeatureVisible(feature) {
      // Simple feature visibility check - can be enhanced based on your needs
      return true
    },
    isAnyTeamSettingAllowed() {
      return true
    },
    isAnyBranchSettingAllowed() {
      return true
    },
    isAnySalarySettingAllowed() {
      return true
    },
    getComponentProps(tab) {
      const propsMap = {
        employees: {},
        role: {},
        branches: {
          mode: 'branches'
        },
        team: {
          mode: 'team'
        },
        organizationhierarchy: {}
      }
      return propsMap[tab.key] || {}
    }
  },
  watch: {
    activeTab(newTab) {
      const tabIndex = this.visibleTabs.findIndex((tab) => tab.key === newTab)
      this.employeeTab = tabIndex !== -1 ? tabIndex : 0
    }
  },
  mounted() {
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }
    const activeTabIndex = this.visibleTabs.findIndex((tab) => tab.key === this.activeTab)
    this.employeeTab = activeTabIndex !== -1 ? activeTabIndex : 0
    this.$nextTick(() => {
      this.isLoad = false
    })

    this.fetchEmployeesData()
  }
}
</script>

<style scoped>
.wfm-search-fields.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 40px !important;
}

.chip-width {
  width: 65%;
  overflow: hidden;
}

.chip-text {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.select-all-item {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}

.highlighted {
  background: #21427d;
  color: white !important;
}
</style>
