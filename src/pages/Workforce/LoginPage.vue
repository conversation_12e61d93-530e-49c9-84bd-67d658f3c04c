<template>
  <div class="h-screen px-0 pt-0 pb-0 mx-0 my-0 box-border">
    <v-row class="h-screen">
      <v-col
        class="lg:flex bg-blue-600 gradient lg:flex-col lg:justify-center px-10 hidden"
        cols="12"
        lg="8"
      >
        <div class="carousel-container h-screen">
          <v-carousel
            v-model="currentSlide"
            cycle
            interval="2000"
            hide-delimiters
            :show-arrows="false"
          >
            <v-carousel-item v-for="(slide, i) in slides" :key="i" :reverse-transition="false">
              <div class="max-h-screen" :class="{ 'carousel-selected': currentSlide === i }">
                <img :src="slide.image" class="image" />
                <div class="text">
                  <h1 class="text-5xl text-start text-white mb-4">
                    {{ slide.headerText }}
                  </h1>
                  <p class="text-xl text-start text-white">
                    {{ slide.subText }}
                  </p>
                </div>
              </div>
            </v-carousel-item>
          </v-carousel>
        </div>
      </v-col>

      <v-col cols="12" lg="4">
        <v-container>
          <div class="flex justify-end">
            <span> version {{ versionNumber }} </span>
          </div>
          <v-row class="flex-grow justify-center items-center">
            <v-col cols="12" sm="10" class="flex justify-center">
              <img src="@/assets/img/cualityWork.png" class="h-13 w-auto" alt="Logo" />
            </v-col>
          </v-row>
          <v-row class="flex-grow justify-center items-center mt-12">
            <v-col cols="12" sm="10">
              <!-- Welcome Message with Transition -->
              <transition name="fade-up" mode="out-in">
                <h4 class="font-bold text-2xl mb-6" :key="currentStep">
                  {{ welcomeMessage }}
                </h4>
              </transition>

              <!-- <transition-group name="fade-slide" mode="out-in"> -->
              <!-- Step 1: Phone Number -->
              <div v-if="currentStep === 'phone'" key="phone" class="login-step">
                <v-text-field
                  v-model="form.mobile"
                  outlined
                  dense
                  label="Phone Number"
                  placeholder="Phone Number"
                  class="mb-2"
                  ref="phoneInput"
                  @input="checkNumberValidity"
                ></v-text-field>
                <div class="d-flex justify-end">
                  <v-btn small color="primary" @click="validateAndNext" :disabled="!isNumberValid"
                    >Proceed
                    <v-icon>mdi-arrow-right</v-icon>
                  </v-btn>
                </div>
              </div>

              <!-- Step 2: Passcode -->
              <div v-if="currentStep === 'passcode'" key="passcode" class="login-step">
                <div class="user-info d-flex align-center mb-4">
                  <v-avatar color="primary" size="36" class="mr-3">
                    <span class="white--text">{{ userInitials }}</span>
                  </v-avatar>
                  <div>
                    <div class="text-body-1 font-weight-medium">
                      {{ maskNumber(form.mobile) }}
                    </div>
                    <a
                      @click="currentStep = 'phone'"
                      class="text-caption text-primary cursor-pointer"
                      >Change number</a
                    >
                  </div>
                </div>

                <PasscodeInput v-model="form.passCode" />

                <v-divider class="my-4"></v-divider>

                <div class="d-flex justify-space-between align-center">
                  <span
                    class="text-primary cursor-pointer hover-effect text-blue-500 underline"
                    @click="forgotPasscode"
                  >
                    Forgot Passcode?
                  </span>
                  <v-btn
                    small
                    color="primary"
                    @click="login"
                    :disabled="form.passCode?.length !== 6"
                    :loading="isLoading"
                    >Login
                    <v-icon>mdi-login</v-icon>
                  </v-btn>
                </div>
              </div>

              <!-- Step 3: OTP -->
              <div v-if="currentStep === 'otp'" key="otp" class="login-step">
                <div class="mb-4 text-center">
                  <p class="text-body-2 text-grey-darken-1">
                    We've sent a verification code to {{ maskNumber(form.mobile) }}
                  </p>
                </div>

                <PasscodeInput v-model="form.otp" :otpField="4" />
                <v-divider class="my-4"></v-divider>
                <div class="text-center mb-4">
                  <p v-if="otpCounter > 0" class="text-caption">
                    Resend OTP in <span class="font-weight-bold">{{ otpCounter }}s</span>
                  </p>
                  <span v-else class="text-primary cursor-pointer hover-effect" @click="resendOTP">
                    Resend OTP
                  </span>
                </div>

                <div class="d-flex justify-center mt-2">
                  <v-btn
                    color="primary"
                    @click="verifyOTP"
                    :disabled="form.otp?.length !== 4"
                    :loading="isLoading"
                    elevation="2"
                    class="px-8"
                  >
                    Verify
                  </v-btn>
                </div>
                <div class="d-flex justify-center mt-2">
                  <v-btn small color="secondary" @click="currentStep = 'passcode'"> Back </v-btn>
                </div>
              </div>

              <!-- Step 4: Set New Passcode -->
              <div v-if="currentStep === 'set-passcode'" key="set-passcode" class="login-step">
                <div class="mb-4 text-center">
                  <p class="text-body-2 text-grey-darken-1">Set a new 6-digit passcode</p>
                </div>
                <PasscodeInput v-model="form.setPasscode" />
                <v-divider class="my-4"></v-divider>
                <div class="mb-4 text-center">
                  <p class="text-body-2 text-grey-darken-1">Confirm passcode</p>
                </div>
                <PasscodeInput v-model="form.confirmPasscode" />

                <div class="d-flex justify-center">
                  <v-btn
                    color="primary"
                    @click="setNewPasscode"
                    :disabled="!canSetPasscode"
                    :loading="isLoading"
                    elevation="2"
                    class="px-8"
                  >
                    Set Passcode
                  </v-btn>
                </div>
                <div class="d-flex justify-center mt-2">
                  <v-btn small color="secondary" @click="currentStep = 'passcode'"> Back </v-btn>
                </div>
              </div>
              <!-- </transition-group> -->

              <!-- Toast Messages -->
              <div v-for="(alert, index) in alerts" :key="index" class="mb-2">
                <v-alert
                  dense
                  dismissible
                  shaped
                  text
                  :type="alert.type"
                  @input="removeAlert(index)"
                >
                  {{ alert.message }}
                </v-alert>
              </div>
            </v-col>
          </v-row>
        </v-container>
        <div class="fixed bottom-4 right-4 z-50">
          <v-btn
            icon
            :color="$vuetify.theme.currentTheme.primary"
            @click="openComplaintCard"
            class="shadow-lg"
          >
            <v-icon>mdi-comment-processing-outline</v-icon>
          </v-btn>
          <QueryComponent v-if="isComplaintOpen" @handleCloseComplaint="openComplaintCard" />
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import QueryComponent from '@/components/queryComponent.vue'
import Designer from '@/assets/img/Designer.svg'
import leftImage from '@/assets/img/LeftImage.svg'
import analysisImage from '@/assets/img/Market_analysis.svg'
import PasscodeInput from '@/components/PasscodeInput.vue'
import { maskPhoneNumber } from '@/utils/common'
import { version } from '../../version'

export default {
  name: 'LoginPage',
  layout: 'newLayout',
  components: {
    QueryComponent,
    PasscodeInput
  },
  data() {
    return {
      currentStep: 'phone', // phone, passcode, otp, set-passcode
      userName: '',
      currentSlide: 0,
      otpCounter: 0,
      otpTimer: null,
      isLoading: false,
      isComplaintOpen: false,
      showPasscode: false,
      versionNumber: version,
      form: {
        mobile: null,
        passCode: null,
        otp: null,
        setPasscode: null,
        confirmPasscode: null
      },
      alerts: [],
      isNumberValid: false
    }
  },
  computed: {
    slides() {
      return [
        {
          image: Designer,
          headerText: 'Best Employee Experience Platform.',
          subText:
            "Don't limit yourself. Many people limit themselves to what they think they can do. You can go as far as your mind lets you. You achieve what you believe."
        },
        {
          image: leftImage,
          headerText: 'Finally a place where it all comes together',
          subText:
            'Employees like you can even make Mondays a joy. Thanks for your hard work and super attitude.'
        },
        {
          image: analysisImage,
          headerText: 'Focus on the work that matters',
          subText: "Business opportunities are like buses, there's always another one coming."
        }
      ]
    },
    welcomeMessage() {
      switch (this.currentStep) {
        case 'phone':
          return 'Login'
        case 'passcode':
          return this.userName ? `Welcome back, ${this.userName}` : 'Enter your passcode'
        case 'otp':
          return 'Verify your identity'
        case 'set-passcode':
          return 'Create new passcode'
        default:
          return 'Welcome'
      }
    },
    userInitials() {
      if (this.userName) {
        const nameParts = this.userName.split(' ')
        if (nameParts && nameParts.length > 1) {
          return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase()
        }
        return nameParts[0][0].toUpperCase()
      }
      return ''
    },
    canSetPasscode() {
      if (
        this.form.setPasscode?.length === 6 &&
        this.form.confirmPasscode?.length === 6 &&
        this.form.setPasscode !== this.form.confirmPasscode
      ) {
        this.showToast('Confirm password and New password are not same', 'error')
      }
      return (
        this.form.setPasscode &&
        this.form.confirmPasscode &&
        this.form.setPasscode.length === 6 &&
        this.form.setPasscode === this.form.confirmPasscode
      )
    }
  },
  watch: {
    currentStep() {
      this.form.passCode = null
      this.form.setPasscode = null
      this.form.confirmPasscode = null
      this.form.otp = null
      if (this.currentStep === 'phone') {
        this.$nextTick(() => {
          this.$refs.phoneInput.focus()
        })
      }
      this.checkNumberValidity()
    }
  },
  created() {
    const userData = JSON.parse(localStorage.getItem('user'))
    if (userData && userData.user && userData.user.mobile) {
      this.userName = `${userData.user.first_name || ''} ${userData.user.last_name || ''}`.trim()
      this.form.mobile = userData.user.mobile
      this.currentStep = 'passcode'
    }
  },
  methods: {
    maskNumber(num) {
      return maskPhoneNumber(String(num))
    },
    showToast(message, type = 'info') {
      const alert = { message, type }
      this.alerts.push(alert)
      setTimeout(() => {
        this.removeAlert(this.alerts.indexOf(alert))
      }, 10000)
    },
    removeAlert(index) {
      if (index > -1) {
        this.alerts.splice(index, 1)
      }
    },
    validateAndNext() {
      if (!this.form.mobile) {
        this.showToast('Please enter your phone number', 'error')
        return
      }

      this.isLoading = true
      this.$axios
        .post('/ext/user', { mobile: this.form.mobile })
        .then((res) => {
          const data = res.data
          if (data.status) {
            const userDetails = {
              user: {
                first_name: data.first_name,
                last_name: data.last_name,
                mobile: this.form.mobile
              },
              role: data.role || []
            }
            this.userName = `${data.first_name || ''} ${data.last_name || ''}`.trim()
            if (data.organization_code) {
              localStorage.setItem('organization_code', data.organization_code)
            }
            localStorage.setItem('user', JSON.stringify(userDetails))
            if (!data.skip_otp) {
              this.sendOTP()
              this.currentStep = 'otp'
            } else {
              this.currentStep = 'passcode'
            }
          } else {
            this.showToast('Invalid phone number', 'error')
          }
        })
        .catch((err) => {
          console.error(err)
          this.showToast('Failed to validate phone number', 'error')
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    login() {
      if (!this.form.passCode) {
        this.showToast('Please enter your passcode', 'error')
        return
      }

      this.isLoading = true
      this.$axios
        .post('/auth/login', {
          mobile: this.form.mobile,
          passcode: this.form.passCode,
          organization_code: localStorage.getItem('organization_code')
        })
        .then(async (res) => {
          if (res.data.access_token) {
            this.$storage.setUniversal('token', res.data)
            await this.setUser()
            const langData = await this.fetchOrgLang()
            const configData = await this.getConfig()
            await this.fetchLoginUserId()

            if (
              this.$storage.getUniversal('user').role.includes('WFM_ADMIN') ||
              this.$storage.getUniversal('user').role.includes('WFM_ACCESS')
            ) {
              this.$router.replace('/workforce/dashboard')
            } else {
              this.showToast('You are not authorized', 'error')
              localStorage.clear()
            }
          }
        })
        .catch((err) => {
          console.error(err)
          this.showToast('Invalid credentials', 'error')
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    forgotPasscode() {
      this.sendOTP()
      this.currentStep = 'otp'
    },

    sendOTP() {
      this.isLoading = true
      this.$axios
        .post('/auth/otp', { mobile: Number(this.form.mobile) })
        .then(() => {
          this.showToast('OTP sent!', 'info')
          this.startOtpCounter()
        })
        .catch((err) => {
          console.error(err)
          this.showToast('Failed to send OTP', 'error')
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    resendOTP() {
      if (this.otpCounter > 0) return
      this.sendOTP()
    },

    startOtpCounter() {
      this.otpCounter = 60
      clearInterval(this.otpTimer)
      this.otpTimer = setInterval(() => {
        if (this.otpCounter > 0) {
          this.otpCounter--
        } else {
          clearInterval(this.otpTimer)
        }
      }, 1000)
    },

    verifyOTP() {
      if (!this.form.otp) {
        this.showToast('Please enter OTP', 'error')
        return
      }
      this.isLoading = true
      this.$axios
        .post('/auth/otp/verify', {
          mobile: this.form.mobile,
          otp: this.form.otp,
          organization_code: localStorage.getItem('organization_code')
        })
        .then((res) => {
          if (res.data.access_token) {
            this.tempToken = res.data?.access_token
            this.setUser()
            this.currentStep = 'set-passcode'
          }
        })
        .catch((err) => {
          console.error(err)
          this.showToast('Invalid OTP', 'error')
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    setNewPasscode() {
      if (this.form.setPasscode !== this.form.confirmPasscode) {
        this.showToast('Passcodes do not match', 'error')
        return
      }

      if (this.form.setPasscode.length !== 6) {
        this.showToast('Passcode must be 6 digits', 'error')
        return
      }

      this.isLoading = true
      this.$axios
        .post(
          '/profile/passcode',
          { passcode: this.form.setPasscode },
          { headers: { Authorization: this.tempToken } }
        )
        .then((res) => {
          if (res.status === 200) {
            this.showToast('Passcode set successfully', 'success')
            this.form.passCode = this.form.setPasscode
            this.form.setPasscode = null
            this.form.confirmPasscode = null
            this.currentStep = 'passcode'
          }
        })
        .catch((err) => {
          console.error(err)
          this.showToast('Failed to set passcode', 'error')
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    async setUser() {
      try {
        const res = await this.$axios.get('/auth/user', {
          headers: { Authorization: this.tempToken }
        })
        this.$storage.setUniversal('user', res.data)
        this.$store.dispatch('setWfmPermissions', res.data.wfmPermissions)
      } catch (error) {
        console.error(error)
      }
    },

    async fetchOrgLang() {
      try {
        const response = await this.$axios.get('/workforce/org/lang')
        this.$store.dispatch('setOrgLanguage', response.data)
        return response.data
      } catch (error) {
        console.error(error)
        return null
      }
    },

    async fetchLoginUserId() {
      try {
        const response = await this.$axios.get('/workforce/user')
        const userData = {
          ...this.$storage.getUniversal('user'),
          _id: response.data?.user?._id
        }
        this.$storage.setUniversal('user', userData)
      } catch (error) {
        console.error(error)
      }
    },

    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')
        const data = response.data?.config
        const obj = {
          directComplete: data.isTaskDirectCompletionAllowedOrg,
          recurAllow: data.isTaskRecurrenceAllowedOrg
        }
        localStorage.setItem('task', JSON.stringify(obj))
        return data
      } catch (error) {
        console.error('Error fetching config:', error)
        return null
      }
    },

    openComplaintCard() {
      this.isComplaintOpen = !this.isComplaintOpen
    },

    checkNumberValidity() {
      this.isNumberValid = this.form.mobile && String(this.form.mobile).length === 10
    }
  },
  beforeDestroy() {
    clearInterval(this.otpTimer)
  }
}
</script>

<style scoped>
.hidden {
  display: none;
}
.gradient {
  background: #5d37bc;
  background: linear-gradient(135deg, #1e3c72, #2a5298);
}
@media (min-width: 1200px) {
  .hidden {
    display: block;
  }
}
.carousel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.image {
  max-width: 100%;
  text-align: start;
  margin: 0;
}
.text {
  padding: 30px;
  text-align: center;
}

/* New animations and transitions */
.fade-up-enter-active,
.fade-up-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}
.fade-up-enter,
.fade-up-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: opacity 0.4s, transform 0.4s;
}
.fade-slide-enter,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.login-step {
  position: relative;
  padding: 8px 0;
}

.hover-effect {
  transition: all 0.2s ease;
}
.hover-effect:hover {
  opacity: 0.8;
  text-decoration: underline;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
  }
}

.user-info {
  background-color: rgba(33, 150, 243, 0.05);
  border-radius: 8px;
  padding: 10px;
}
</style>
