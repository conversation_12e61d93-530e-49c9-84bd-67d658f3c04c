<template>
  <div class="flex flex-col md:flex-row gap-4">
    <div class="w-full md:w-3/5">
      <!-- <v-tabs v-model="tab" :color="$vuetify.theme.currentTheme.primary">
        <v-tab
          v-for="(tab, index) in tabs"
          :key="index"
          :color="$vuetify.theme.currentTheme.primary"
          >{{ tab }}</v-tab
        >
      </v-tabs> -->
      <div
        v-if="tab === 0 && form"
        class="bg-white dark-bg-custom px-5 py-5 mt-4 border border-gray-300 rounded-md"
      >
        <v-row no-gutters v-if="form">
          <v-row class="m-0">
            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.code"
                outlined
                dense
                hint="Should be 6 Characters"
                label="Organization Code *"
                disabled
                hide-details
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-text-field
                v-model="form.name"
                outlined
                dense
                hide-details
                label="Organization Name *"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row class="mt-4 w-full" no-gutters>
            <v-col cols="12">
              <strong>Registered Address</strong>
            </v-col>
          </v-row>

          <v-row class="w-full">
            <v-col cols="12">
              <CommonAutocomplete
                :addressLine1="addressLine1"
                :branch="branches"
                v-model="selectedAddress"
                @selected-place="handleSelectedPlace"
              />
            </v-col>
          </v-row>
          <v-row class="">
            <v-col cols="12" md="4">
              <v-text-field
                v-model="form.city"
                outlined
                dense
                hide-details
                label="City *"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4">
              <v-select
                :items="states"
                v-model="form.state"
                label="State *"
                dense
                hide-details
                outlined
              ></v-select>
            </v-col>
            <v-col cols="12" md="4">
              <v-text-field
                type="number"
                v-model="form.pincode"
                outlined
                dense
                hide-details
                label="Pincode *"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row no-gutters class="flex justify-end mt-4 w-full">
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              @click="updateOrg()"
            >
              Update
            </v-btn>
          </v-row>
        </v-row>
      </div>
    </div>
    <div class="map-container w-full md:w-2/5 mt-4 h-80 md:h-auto">
      <div ref="map" class="w-full h-full"></div>
    </div>
  </div>
</template>

<script>
/* global google */
import CommonAutocomplete from '~/components/CommonAutoComplete.vue'

export default {
  name: 'ProfileComponent',
  layout: 'Workforce/default',
  data() {
    return {
      user: this.$storage.getUniversal('user'),
      orgCode: this.$storage.getUniversal('organization_code'),
      tab: 0,
      tabs: ['Details'],
      orgData: null,
      form: {
        code: '',
        name: '',
        address_line_1: '',
        address_line_2: '',
        city: '',
        state: '',
        pincode: ''
      },
      modal1: false,
      modal2: false,
      disabled: true,
      states: [],
      branches: [],
      addressLine1: '',
      map: null,
      placesServiceData: null,
      selectedAddress: '',
      orgDataStorage: this.$storage.getUniversal('orgData')
    }
  },
  components: {
    CommonAutocomplete
  },
  methods: {
    handleSelectedPlace(place) {
      this.selectedAddress = place.formatted_address
      this.form.city = place.city
      this.form.state = place.state
      this.form.pincode = place.pincode
      this.form.location = place.location
      if (place.location) {
        this.setMarker(place.location.lat(), place.location.lng(), place.formatted_address)
      }
    },

    async addGoogleMapsScript() {
      try {
        const scriptTag = document.querySelector('script[src*="maps.googleapis.com"]')
        if (!scriptTag) {
          const script = document.createElement('script')
          script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.VUE_APP_MAP_KEY}&libraries=places&callback=Function.prototype`
          script.defer = true
          script.async = true
          script.onload = this.initMap // Initialize the map after the script is loaded
          document.head.appendChild(script)
        } else {
          setTimeout(() => {
            this.initMap()
          }, 1000)
        }
      } catch (error) {
        console.log(error)
      }
    },
    async initMap() {
      try {
        const mapOptions = {
          center: { lat: 37.7749, lng: -122.4194 },
          zoom: 15
        }
        this.map = new window.google.maps.Map(this.$refs.map, mapOptions)

        if (this.form.location && this.form.location.lat && this.form.location.lng) {
          this.setMarker(this.form.location.lat, this.form.location.lng, this.form.address_line_1)
        }

        this.placesServiceData = new google.maps.places.PlacesService(document.createElement('div'))
      } catch (error) {
        console.error('An error occurred while initializing the map:', error)
      }
    },
    customFilter() {
      return true
    },

    setMarker(lat, lng, address) {
      if (this.marker) {
        this.marker.setMap(null)
      }

      this.marker = new google.maps.Marker({
        map: this.map,
        position: { lat, lng },
        title: address
      })

      const infoWindowContent = `<div class="dark-bg-custom p-2">${address}</div>`

      if (this.infoWindow) {
        this.infoWindow.close()
      }

      this.infoWindow = new google.maps.InfoWindow({
        content: infoWindowContent
      })

      this.infoWindow.open(this.map, this.marker)

      this.map.setCenter({ lat, lng })

      this.form.lat = lat
      this.form.lng = lng
      this.form.address_line_1 = address
    },
    async getStates() {
      try {
        const response = await this.$axios.get('/ext/states')

        this.states = response.data
      } catch (error) {
        console.log(error)
      }
    },
    async getOrgConfig() {
      try {
        const response = await this.$axios.get(`/organizations`)

        this.orgData = response?.data[0]
        this.form = {
          orgId: this.orgData ? this.orgData.id : '',
          code: this.orgData ? this.orgData?.code : '',
          name: this.orgData ? this.orgData.name : '',
          valid_from: this.orgData ? this.orgData.valid_from : '',
          valid_to: this.orgData ? this.orgData.valid_to : '',
          address_line_1: this.orgData ? this.orgData.organization_address.address_line_1 : '',
          address_line_2: this.orgData ? this.orgData.organization_address.address_line_2 : '',
          city: this.orgData ? this.orgData.organization_address.city : '',
          state: this.orgData ? this.orgData.organization_address.state : '',
          pincode: this.orgData ? this.orgData.organization_address.pincode : '',
          location: {
            lat: this.orgData?.organization_address?.lat,
            lng: this.orgData?.organization_address?.lng
          }
        }
        // addressLine1 = { label: this.form.address_line_1, value: this.form.location };
      } catch (error) {
        console.log(error)
      }
    },
    async updateOrg() {
      if (this.form.code == '' || this.form.code == undefined) {
        this.toasterError('Please enter the Organization code')
        return false
      }
      if (this.form.code.length != 6) {
        this.toasterError('Organization code should be 6 characters')
        return false
      }
      if (this.form.name == '' || this.form.name == undefined) {
        this.toasterError('Please enter the Organization name')
        return false
      }
      if (this.form.address_line_1 == '' || this.form.address_line_1 == undefined) {
        this.toasterError('Please enter the Address Line 1')
        return false
      }
      if (this.form.address_line_2 == '' || this.form.address_line_2 == undefined) {
        this.toasterError('Please enter the Address Line 2')
        return false
      }
      if (this.form.city == '' || this.form.city == undefined) {
        this.toasterError('Please enter the City')
        return false
      }
      if (this.form.state == '' || this.form.state == undefined) {
        this.toasterError('Please enter the State')
        return false
      }
      if (this.form.pincode == '' || this.form.pincode == undefined) {
        this.toasterError('Please enter the Pincode')
        return false
      }

      const payload = {
        orgCode: this.form.code,
        name: this.form.name,
        address: {
          address_line_1: this.form.address_line_1,
          city: this.form.city,
          state: this.form.state,
          pincode: Number(this.form.pincode),
          lat: Number(this.form.lat),
          lng: Number(this.form.lng)
        }
      }
      try {
        const response = await this.$axios.put(`/workforce/org/info`, payload)
        if (response.data.success) {
          // this.orgDataStorage.name = response.data.orgInfoUpdated.name;
          this.orgDataStorage.organization_address.address_line_1 =
            response.data.orgInfoUpdated.address_line_1
          this.orgDataStorage.organization_address.city = response.data.orgInfoUpdated.city
          this.orgDataStorage.organization_address.lat = response.data.orgInfoUpdated.lat
          this.orgDataStorage.organization_address.lng = response.data.orgInfoUpdated.lng
          this.orgDataStorage.organization_address.state = response.data.orgInfoUpdated.state
          this.orgDataStorage.organization_address.pincode = response.data.orgInfoUpdated.pincode
          this.setCookie('orgData', JSON.stringify(this.orgDataStorage))

          const successMessage = response?.data?.message || 'Organization updated successfully'
          this.$toast.success(successMessage)
        }
      } catch (error) {
        console.error('Error updating org.', error)
      }
    },
    toasterError(msg) {
      this.$toast.error(msg)
    },
    setCookie(name, value) {
      document.cookie = name + '=' + (value || '') + '; path=/'
    }
  },
  computed: {
    getAddressLine1() {
      return this.getAddressLine1 ? this.getAddressLine1 : this.form.address_line_1
    }
  },

  mounted() {
    const addressLine_1 = this.$storage.getUniversal('orgData').organization_address.address_line_1
    this.branches = [{ label: addressLine_1 }]
    this.addressLine1 = addressLine_1
    this.addGoogleMapsScript()
    this.getOrgConfig()
    this.getStates()
  }
}
</script>
<style></style>
<style scoped>
.map-container {
  border: 1px solid #ccc !important;
  padding: 10px !important;
}
</style>
