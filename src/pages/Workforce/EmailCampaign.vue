<template>
  <div>
    <!-- Active Tab Component -->
    <ActiveTabComponent :tabs="tabs" default-tab="subscribers" @tab-changed="handleTabChange">
      <!-- Email Templates Tab Content -->
      <template #templates>
        <div class="px-4 py-6">
          <!-- Integration Status Alert -->
          <v-alert v-if="!mailerLiteConfig.bearerToken" type="warning" dense text class="mb-6">
            <div class="d-flex align-center justify-space-between">
              <div>
                <strong>MailerLite Integration Required</strong>
                <br />
                Please configure your MailerLite API token in Platform Integrations to use email
                campaign features.
              </div>
              <v-btn color="primary" small outlined @click="$router.push('/workforce/settings')">
                <v-icon left small>mdi-cog</v-icon>
                Configure
              </v-btn>
            </div>
          </v-alert>

          <v-card-title class="d-flex justify-space-between align-center">
            <div></div>

            <div class="d-flex justify-end align-center gap-3">
              <v-btn color="primary" @click="openTemplateModal">
                <v-icon left>mdi-plus</v-icon>
                New Template
              </v-btn>
              <v-btn
                color="info"
                @click="refreshTemplates"
                :loading="loading.templates"
                :disabled="!mailerLiteConfig.bearerToken"
              >
                <v-icon left>mdi-refresh</v-icon>
                Refresh
              </v-btn>
            </div>
          </v-card-title>
          <v-card class="mb-6" flat>
            <v-card-text>
              <v-data-table
                :headers="templateHeaders"
                :items="templates"
                :loading="loading.templates"
                class="elevation-0"
                :items-per-page="templatesPagination.per_page"
                :server-items-length="templatesPagination.total"
                :page.sync="templatesPagination.current_page"
                @update:page="handleTemplatePageChange"
                loading-text="Loading templates from MailerLite..."
                no-data-text="No templates found. Configure your API token and create your first template."
              >
                <template v-slot:item.type="{ item }">
                  <v-chip small :color="getTemplateTypeColor(item.type)" text-color="white">
                    {{ item.type }}
                  </v-chip>
                </template>

                <template v-slot:item.status="{ item }">
                  <v-chip small :color="getTemplateStatusColor(item.status)" text-color="white">
                    {{ item.status }}
                  </v-chip>
                </template>

                <template v-slot:item.created_at="{ item }">
                  {{ formatDate(item.created_at) }}
                </template>

                <template v-slot:item.actions="{ item }">
                  <v-btn icon small color="primary" @click="editTemplate(item)">
                    <v-icon small>mdi-pencil</v-icon>
                  </v-btn>
                  <v-btn icon small color="info" @click="duplicateTemplate(item)">
                    <v-icon small>mdi-content-copy</v-icon>
                  </v-btn>
                  <v-btn icon small color="error" @click="confirmDeleteTemplate(item)">
                    <v-icon small>mdi-delete</v-icon>
                  </v-btn>
                </template>
              </v-data-table>
            </v-card-text>
          </v-card>
        </div>
      </template>

      <!-- Subscribers Tab Content -->
      <template #subscribers>
        <div class="px-4 py-6">
          <!-- Integration Status Alert -->
          <v-alert v-if="!mailerLiteConfig.bearerToken" type="warning" dense text class="mb-6">
            <div class="d-flex align-center justify-space-between">
              <div>
                <strong>MailerLite Integration Required</strong>
                <br />
                Please configure your MailerLite API token in Platform Integrations to use email
                campaign features.
              </div>
              <v-btn color="primary" small outlined @click="$router.push('/workforce/settings')">
                <v-icon left small>mdi-cog</v-icon>
                Configure
              </v-btn>
            </div>
          </v-alert>

          <v-card-title class="d-flex justify-space-between align-center">
            <div></div>
            <div class="d-flex justify-end gap-2">
              <v-btn outlined color="primary" @click="openGroupModal">
                <v-icon left>mdi-plus-circle</v-icon>
                New Group
              </v-btn>
              <v-btn
                color="primary"
                @click="openSubscriberModal"
                :disabled="!mailerLiteConfig.bearerToken"
              >
                <v-icon left>mdi-account-plus</v-icon>
                Add Subscriber
              </v-btn>
              <v-btn
                color="info"
                @click="refreshData"
                :loading="loading.subscribers || loading.groups"
                :disabled="!mailerLiteConfig.bearerToken"
              >
                <v-icon left>mdi-refresh</v-icon>
                Refresh
              </v-btn>
              <v-btn outlined color="success" @click="openImportModal">
                <v-icon left>mdi-file-import</v-icon>
                Import from Excel
              </v-btn>
            </div>
          </v-card-title>
          <v-card class="mb-6" flat>
            <!-- Subscriber Groups -->
            <v-card-text>
              <div class="mb-8">
                <div class="d-flex justify-space-between align-center mb-4">
                  <h3 class="text-lg font-medium text-gray-800">Subscriber Groups</h3>
                </div>

                <v-row v-if="subscriberGroups.length > 0">
                  <v-col v-for="group in subscriberGroups" :key="group.id" cols="12" md="4">
                    <v-card outlined hover class="h-100">
                      <v-card-text>
                        <div class="d-flex justify-space-between align-start mb-2">
                          <h4 class="font-medium text-gray-800">{{ group.name }}</h4>
                          <div class="d-flex align-center gap-1">
                            <v-chip small :color="group.color" text-color="white">
                              {{ group.active_count }}
                            </v-chip>
                            <v-menu bottom left>
                              <template v-slot:activator="{ on, attrs }">
                                <v-btn icon small v-bind="attrs" v-on="on">
                                  <v-icon small>mdi-dots-vertical</v-icon>
                                </v-btn>
                              </template>
                              <v-list dense>
                                <v-list-item @click="editGroup(group)">
                                  <v-list-item-icon>
                                    <v-icon small>mdi-pencil</v-icon>
                                  </v-list-item-icon>
                                  <v-list-item-content>
                                    <v-list-item-title>Edit</v-list-item-title>
                                  </v-list-item-content>
                                </v-list-item>
                                <v-list-item @click="confirmDeleteGroup(group)">
                                  <v-list-item-icon>
                                    <v-icon small color="error">mdi-delete</v-icon>
                                  </v-list-item-icon>
                                  <v-list-item-content>
                                    <v-list-item-title>Delete</v-list-item-title>
                                  </v-list-item-content>
                                </v-list-item>
                              </v-list>
                            </v-menu>
                          </div>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">
                          Active: {{ group.active_count }} | Total: {{ group.total_count }}
                        </p>
                        <div class="d-flex gap-2">
                          <v-btn text small color="primary" @click="editGroup(group)">
                            <v-icon left small>mdi-pencil</v-icon>
                            Edit
                          </v-btn>
                          <v-btn text small color="info" @click="viewGroupSubscribers(group)">
                            <v-icon left small>mdi-eye</v-icon>
                            View
                          </v-btn>
                        </div>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>

                <!-- Loading state for groups -->
                <div v-else-if="loading.groups" class="text-center py-8">
                  <v-progress-circular indeterminate color="primary"></v-progress-circular>
                  <p class="mt-2 text-gray-600">Loading groups...</p>
                </div>

                <!-- Empty state for groups -->
                <div v-else class="text-center py-8">
                  <v-icon size="64" color="gray">mdi-account-group-outline</v-icon>
                  <p class="mt-2 text-gray-600">No groups found. Create your first group!</p>
                </div>
              </div>

              <!-- Subscriber List -->
              <div>
                <div class="d-flex justify-space-between align-center mb-4">
                  <h3 class="text-lg font-medium text-gray-800">
                    All Subscribers
                    <v-chip v-if="subscribersPagination.total > 0" small class="ml-2">
                      {{ subscribersPagination.total }}
                    </v-chip>
                  </h3>
                  <div class="d-flex align-center gap-3">
                    <v-select
                      v-model="subscriberFilters.status"
                      :items="statusOptions"
                      label="Status"
                      dense
                      outlined
                      hide-details
                      style="max-width: 150px"
                      @change="applyFilters"
                    ></v-select>
                    <v-select
                      v-model="subscriberFilters.group_id"
                      :items="groupFilterOptions"
                      label="Group"
                      dense
                      outlined
                      hide-details
                      clearable
                      style="max-width: 200px"
                      @change="applyFilters"
                    ></v-select>
                    <v-text-field
                      v-model="subscriberSearch"
                      placeholder="Search subscribers..."
                      dense
                      outlined
                      hide-details
                      prepend-inner-icon="mdi-magnify"
                      style="max-width: 300px"
                    ></v-text-field>
                    <v-btn
                      outlined
                      @click="exportSubscribers"
                      :disabled="!mailerLiteConfig.bearerToken"
                    >
                      <v-icon left>mdi-download</v-icon>
                      Export
                    </v-btn>
                  </div>
                </div>

                <!-- Active Filters -->
                <div v-if="hasActiveFilters" class="mb-3">
                  <v-chip
                    v-if="subscriberFilters.status !== 'active'"
                    small
                    close
                    @click:close="clearStatusFilter"
                    class="mr-2"
                  >
                    Status: {{ subscriberFilters.status }}
                  </v-chip>
                  <v-chip
                    v-if="subscriberFilters.group_id"
                    small
                    close
                    @click:close="clearGroupFilter"
                    class="mr-2"
                  >
                    Group: {{ getGroupNameById(subscriberFilters.group_id) }}
                  </v-chip>
                  <v-btn text small color="primary" @click="clearAllFilters">
                    Clear All Filters
                  </v-btn>
                </div>

                <v-data-table
                  :headers="subscriberHeaders"
                  :items="subscribers"
                  :search="subscriberSearch"
                  :loading="loading.subscribers"
                  class="elevation-0"
                  :items-per-page="subscribersPagination.per_page"
                  :server-items-length="subscribersPagination.total"
                  :page.sync="subscribersPagination.current_page"
                  @update:page="handlePageChange"
                  loading-text="Loading subscribers from MailerLite..."
                  no-data-text="No subscribers found. Configure your API token and refresh."
                >
                  <template v-slot:item.groups="{ item }">
                    <v-chip
                      v-for="group in item.groups"
                      :key="group"
                      small
                      class="mr-1 mb-1"
                      :color="getGroupColor(group)"
                      text-color="white"
                    >
                      {{ group }}
                    </v-chip>
                  </template>

                  <template v-slot:item.status="{ item }">
                    <v-chip small :color="getStatusColor(item.status)" text-color="white">
                      {{ item.status }}
                    </v-chip>
                  </template>

                  <template v-slot:item.actions="{ item }">
                    <v-btn icon small color="primary" @click="editSubscriber(item)">
                      <v-icon small>mdi-pencil</v-icon>
                    </v-btn>
                    <v-btn icon small color="error" @click="confirmDeleteSubscriber(item)">
                      <v-icon small>mdi-delete</v-icon>
                    </v-btn>
                  </template>
                </v-data-table>
              </div>
            </v-card-text>
          </v-card>
        </div>
      </template>
      <template #preBuildTemplates>
        <EmailTemplates />
      </template>
    </ActiveTabComponent>

    <!-- Template/Campaign Modal -->
    <v-dialog v-model="templateModal" max-width="1400" persistent>
      <v-card>
        <v-card-title class="d-flex justify-space-between align-center">
          <span class="text-xl font-semibold"
            >{{ editingTemplate ? 'Edit' : 'Create New' }} Email Campaign</span
          >
          <v-btn icon @click="closeTemplateModal">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text>
          <v-form ref="templateForm" v-model="templateFormValid">
            <!-- Campaign Basic Info -->
            <v-card outlined class="mb-4">
              <v-card-title class="text-lg">Campaign Information</v-card-title>
              <v-card-text>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="templateForm.name"
                      label="Campaign Name"
                      outlined
                      dense
                      required
                      :rules="[(v) => !!v || 'Campaign name is required']"
                      hint="Enter a descriptive name for your email campaign"
                      persistent-hint
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="templateForm.subject"
                      label="Email Subject"
                      outlined
                      dense
                      required
                      :rules="[(v) => !!v || 'Email subject is required']"
                      hint="This will appear in the recipient's inbox"
                      persistent-hint
                    ></v-text-field>
                  </v-col>
                </v-row>

                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="templateForm.fromName"
                      label="Sender Name"
                      outlined
                      dense
                      required
                      :rules="[(v) => !!v || 'Sender name is required']"
                      hint="Name that will appear as the sender"
                      persistent-hint
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="templateForm.fromEmail"
                      label="Sender Email"
                      type="email"
                      outlined
                      dense
                      required
                      :rules="[
                        (v) => !!v || 'Sender email is required',
                        (v) => /.+@.+\..+/.test(v) || 'Email must be valid'
                      ]"
                      hint="Email address that will appear as the sender"
                      persistent-hint
                    ></v-text-field>
                  </v-col>
                </v-row>

                <v-row>
                  <v-col cols="12">
                    <v-text-field
                      v-model="templateForm.preheader"
                      label="Preheader Text (Optional)"
                      outlined
                      dense
                      hint="Preview text that appears after the subject line in email clients"
                      persistent-hint
                      counter="150"
                      :rules="[
                        (v) => !v || v.length <= 150 || 'Preheader must be 150 characters or less'
                      ]"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>

            <!-- Recipients -->
            <v-card outlined class="mb-4">
              <v-card-title class="text-lg">Recipients</v-card-title>
              <v-card-text>
                <div class="mb-3">
                  <label class="text-gray-700 text-sm font-medium mb-2 d-block"
                    >Select Recipient Groups</label
                  >
                  <v-alert v-if="subscriberGroups.length === 0" type="warning" dense text>
                    No groups available. Create groups first to organize your recipients.
                  </v-alert>
                  <div v-else class="d-flex flex-wrap gap-3">
                    <v-checkbox
                      v-for="group in subscriberGroups"
                      :key="group.id"
                      v-model="templateForm.groups"
                      :value="group.name"
                      :label="`${group.name} (${group.active_count} subscribers)`"
                      hide-details
                    ></v-checkbox>
                  </div>
                </div>

                <!-- Selected Recipients Summary -->
                <div v-if="templateForm.groups.length > 0" class="mt-3">
                  <v-alert type="info" dense text>
                    <strong>Selected Groups:</strong> {{ templateForm.groups.join(', ') }}
                    <br />
                    <strong>Total Recipients:</strong> {{ getTotalRecipients() }} subscribers
                  </v-alert>
                </div>
              </v-card-text>
            </v-card>

            <!-- Email Content -->
            <v-card outlined class="mb-4">
              <v-card-title class="text-lg">Email Content</v-card-title>
              <v-card-text>
                <!-- Editor Toolbar -->
                <div class="editor-toolbar bg-gray-50 pa-2 mb-3 rounded">
                  <v-btn-toggle dense>
                    <v-btn small @click="execCommand('bold')" title="Bold">
                      <v-icon small>mdi-format-bold</v-icon>
                    </v-btn>
                    <v-btn small @click="execCommand('italic')" title="Italic">
                      <v-icon small>mdi-format-italic</v-icon>
                    </v-btn>
                    <v-btn small @click="execCommand('underline')" title="Underline">
                      <v-icon small>mdi-format-underline</v-icon>
                    </v-btn>
                    <v-btn small @click="execCommand('insertUnorderedList')" title="Bullet List">
                      <v-icon small>mdi-format-list-bulleted</v-icon>
                    </v-btn>
                    <v-btn small @click="execCommand('insertOrderedList')" title="Numbered List">
                      <v-icon small>mdi-format-list-numbered</v-icon>
                    </v-btn>
                    <v-btn small @click="insertLink" title="Insert Link">
                      <v-icon small>mdi-link</v-icon>
                    </v-btn>
                    <v-btn small @click="insertImage" title="Insert Image">
                      <v-icon small>mdi-image</v-icon>
                    </v-btn>
                  </v-btn-toggle>

                  <v-divider vertical class="mx-2"></v-divider>

                  <!-- Personalization Variables -->
                  <v-menu offset-y>
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn small v-bind="attrs" v-on="on" title="Insert Variable">
                        <v-icon small left>mdi-account</v-icon>
                        Variables
                      </v-btn>
                    </template>
                    <v-list dense>
                      <v-list-item @click="insertVariable('{first_name}')">
                        <v-list-item-title>First Name</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="insertVariable('{last_name}')">
                        <v-list-item-title>Last Name</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="insertVariable('{email}')">
                        <v-list-item-title>Email</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>

                <!-- Rich Text Editor -->
                <div
                  ref="emailEditor"
                  class="email-editor border rounded pa-4 bg-white"
                  contenteditable="true"
                  @input="updatePreview"
                  @paste="handlePaste"
                  style="
                    min-height: 400px;
                    border: 1px solid #ddd;
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                  "
                  v-html="templateForm.content"
                ></div>

                <div class="text-caption mt-2 text-gray-600">
                  Use the toolbar above to format your email. You can insert personalization
                  variables like {first_name} to customize emails for each recipient.
                </div>
              </v-card-text>
            </v-card>

            <!-- Preview -->
            <v-card outlined>
              <v-card-title class="text-lg">
                Preview
                <v-spacer></v-spacer>
                <v-btn small outlined @click="sendTestEmail" :disabled="!templateFormValid">
                  <v-icon small left>mdi-send</v-icon>
                  Send Test
                </v-btn>
              </v-card-title>
              <v-card-text>
                <div class="preview-container">
                  <!-- Email Header Preview -->
                  <div class="email-preview-header mb-3 pa-3 bg-gray-100 rounded">
                    <div class="text-sm">
                      <strong>From:</strong> {{ templateForm.fromName || 'Sender Name' }} &lt;{{
                        templateForm.fromEmail || '<EMAIL>'
                      }}&gt;
                    </div>
                    <div class="text-sm">
                      <strong>Subject:</strong> {{ templateForm.subject || 'Email Subject' }}
                    </div>
                    <div v-if="templateForm.preheader" class="text-sm text-gray-600">
                      <strong>Preheader:</strong> {{ templateForm.preheader }}
                    </div>
                  </div>

                  <!-- Email Content Preview -->
                  <div
                    class="template-preview border rounded pa-4 bg-white"
                    style="min-height: 300px; font-family: Arial, sans-serif; line-height: 1.6"
                  >
                    <div v-html="templatePreview"></div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </v-form>
        </v-card-text>

        <v-card-actions class="justify-space-between">
          <div>
            <v-btn text @click="closeTemplateModal">Cancel</v-btn>
          </div>
          <div>
            <v-btn
              text
              color="primary"
              @click="saveDraft"
              :disabled="!templateForm.name || !templateForm.subject"
              class="mr-2"
            >
              Save as Draft
            </v-btn>
            <v-btn
              color="primary"
              @click="saveTemplate"
              :disabled="!templateFormValid || templateForm.groups.length === 0"
              :loading="loading.templates"
            >
              {{ editingTemplate ? 'Update Campaign' : 'Create Campaign' }}
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Subscriber Modal -->
    <v-dialog v-model="subscriberModal" max-width="700" persistent>
      <v-card class="subscriber-modal-card" flat>
        <div class="flex justify-between p-4 bg-gray-200 dark-bg-custom">
          <h4 class="text-xl font-semibold">
            {{ editingSubscriber ? 'Edit' : 'Add New' }} Subscriber
          </h4>
          <v-icon @click="closeSubscriberModal" class="cursor-pointer">mdi-close</v-icon>
        </div>

        <div class="p-6">
          <v-form ref="subscriberForm" v-model="subscriberFormValid" lazy-validation>
            <!-- Subscriber Source Selection -->
            <div class="mb-6">
              <label class="text-gray-700 text-sm font-medium mb-3 d-block"
                >Subscriber Source</label
              >
              <v-radio-group
                v-model="subscriberForm.source"
                row
                hide-details
                class="mt-0"
                @change="onSourceChange"
              >
                <v-radio label="Manual Entry" value="manual" color="primary"></v-radio>
                <v-radio label="System Users" value="users" color="primary"></v-radio>
                <v-radio label="Customers" value="customers" color="primary"></v-radio>
              </v-radio-group>
            </div>

            <!-- Manual Entry Fields -->
            <template v-if="subscriberForm.source === 'manual'">
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="subscriberForm.email"
                    label="Email Address *"
                    type="email"
                    outlined
                    dense
                    hide-details
                    required
                    :rules="emailRules"
                    prepend-inner-icon="mdi-email-outline"
                  ></v-text-field>
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="6">
                  <v-text-field
                    v-model="subscriberForm.firstName"
                    label="First Name"
                    outlined
                    dense
                    hide-details
                    prepend-inner-icon="mdi-account-outline"
                  ></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    v-model="subscriberForm.lastName"
                    label="Last Name"
                    outlined
                    hide-details
                    dense
                  ></v-text-field>
                </v-col>
              </v-row>
            </template>

            <!-- System Users Selection -->
            <template v-if="subscriberForm.source === 'users'">
              <v-autocomplete
                v-model="subscriberForm.selectedUser"
                :items="systemUsers"
                :item-text="getUserFullName"
                item-value="_id"
                :search-input.sync="userSearch"
                @update:search-input="debounceUserSearch"
                @input="onUserSelected"
                label="Search System Users *"
                outlined
                dense
                hide-no-data
                prepend-inner-icon="mdi-account-search-outline"
                :rules="[(v) => !!v || 'Please select a user']"
                clearable
                :loading="loading.users"
                :item-disabled="(item) => !item.email"
              >
                <template v-slot:item="{ item, attrs, on }">
                  <v-list-item
                    v-bind="attrs"
                    v-on="on"
                    class="user-list-item"
                    :disabled="!item.email"
                    :class="{ 'disabled-item': !item.email }"
                  >
                    <v-list-item-avatar>
                      <v-avatar size="32" :color="item.email ? 'primary' : 'grey'">
                        <span class="white--text text-sm">{{ getUserInitials(item) }}</span>
                      </v-avatar>
                    </v-list-item-avatar>
                    <v-list-item-content>
                      <v-list-item-title :class="{ 'text--disabled': !item.email }">
                        {{ getUserFullName(item) }}
                      </v-list-item-title>
                      <v-list-item-subtitle :class="{ 'text--disabled': !item.email }">
                        {{ item.email || 'No email address' }}
                      </v-list-item-subtitle>
                    </v-list-item-content>
                    <v-list-item-action v-if="!item.email">
                      <v-icon color="error" small>mdi-close-circle</v-icon>
                    </v-list-item-action>
                  </v-list-item>
                </template>
                <template v-slot:no-data>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title>
                        {{ userSearch ? 'No users found' : 'Start typing to search users...' }}
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </template>

            <!-- Customers Selection -->
            <template v-if="subscriberForm.source === 'customers'">
              <v-autocomplete
                v-model="subscriberForm.selectedCustomer"
                :items="customers"
                :item-text="getCustomerFullName"
                item-value="_id"
                :search-input.sync="customerSearch"
                @update:search-input="debounceCustomerSearch"
                @input="onCustomerSelected"
                label="Search Customers *"
                outlined
                dense
                hide-no-data
                prepend-inner-icon="mdi-account-search-outline"
                :rules="[(v) => !!v || 'Please select a customer']"
                clearable
                :loading="loading.customers"
                :item-disabled="(item) => !item.email && !item.phone"
              >
                <template v-slot:item="{ item, attrs, on }">
                  <v-list-item
                    v-bind="attrs"
                    v-on="on"
                    class="customer-list-item"
                    :disabled="!item.email && !item.phone"
                    :class="{ 'disabled-item': !item.email && !item.phone }"
                  >
                    <v-list-item-avatar>
                      <v-avatar size="32" :color="item.email || item.phone ? 'success' : 'grey'">
                        <span class="white--text text-sm">{{ getCustomerInitials(item) }}</span>
                      </v-avatar>
                    </v-list-item-avatar>
                    <v-list-item-content>
                      <v-list-item-title :class="{ 'text--disabled': !item.email && !item.phone }">
                        {{ getCustomerFullName(item) }}
                      </v-list-item-title>
                      <v-list-item-subtitle
                        :class="{ 'text--disabled': !item.email && !item.phone }"
                      >
                        {{
                          item.email || (item.phone ? `Phone: ${item.phone}` : 'No contact info')
                        }}
                      </v-list-item-subtitle>
                    </v-list-item-content>
                    <v-list-item-action v-if="!item.email && !item.phone">
                      <v-icon color="error" small>mdi-close-circle</v-icon>
                    </v-list-item-action>
                    <v-list-item-action v-else-if="!item.email && item.phone">
                      <v-tooltip bottom>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon color="warning" small v-bind="attrs" v-on="on"
                            >mdi-alert-circle</v-icon
                          >
                        </template>
                        <span>Phone number will be used as email identifier</span>
                      </v-tooltip>
                    </v-list-item-action>
                  </v-list-item>
                </template>
                <template v-slot:no-data>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title>
                        {{
                          customerSearch
                            ? 'No customers found'
                            : 'Start typing to search customers...'
                        }}
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </template>

            <!-- Groups Selection -->
            <div>
              <label class="text-gray-700 text-sm font-medium mb-3 d-block">
                <v-icon small class="mr-1">mdi-account-group</v-icon>
                Assign to Groups
              </label>
              <div class="groups-container p-4 border rounded-lg bg-gray-50">
                <div v-if="subscriberGroups.length === 0" class="text-center py-4">
                  <v-icon size="48" color="grey lighten-1">mdi-account-group-outline</v-icon>
                  <p class="text-grey text-sm mt-2">No groups available. Create groups first.</p>
                </div>
                <v-row v-else>
                  <v-col v-for="group in subscriberGroups" :key="group.id" cols="6" md="4">
                    <v-checkbox
                      v-model="subscriberForm.groups"
                      :value="group.name"
                      :label="`${group.name} (${group.active_count})`"
                      hide-details
                      dense
                      class="group-checkbox"
                    >
                      <template v-slot:label>
                        <div class="d-flex align-center">
                          <v-chip x-small :color="group.color" class="mr-2" text-color="white">
                            {{ group.active_count }}
                          </v-chip>
                          <span>{{ group.name }}</span>
                        </div>
                      </template>
                    </v-checkbox>
                  </v-col>
                </v-row>
              </div>
            </div>

            <!-- Selected User/Customer Preview -->
            <div v-if="selectedUserPreview || selectedCustomerPreview" class="mt-4">
              <v-card outlined class="pa-3">
                <div class="d-flex align-center">
                  <v-avatar
                    size="40"
                    :color="selectedUserPreview ? 'primary' : 'success'"
                    class="mr-3"
                  >
                    <span class="white--text">
                      {{
                        selectedUserPreview
                          ? getUserInitials(selectedUserPreview)
                          : getCustomerInitials(selectedCustomerPreview)
                      }}
                    </span>
                  </v-avatar>
                  <div>
                    <p class="font-medium mb-1">
                      {{
                        selectedUserPreview
                          ? getUserFullName(selectedUserPreview)
                          : getCustomerFullName(selectedCustomerPreview)
                      }}
                    </p>
                    <p class="text-sm text-grey">
                      {{
                        selectedUserPreview
                          ? selectedUserPreview.email
                          : selectedCustomerPreview.email || selectedCustomerPreview.phone
                      }}
                    </p>
                  </div>
                </div>
              </v-card>
            </div>
          </v-form>
        </div>

        <v-card-actions class="px-6 pb-6">
          <div class="flex items-center justify-between w-full">
            <v-btn outlined class="w-32 h-12 rounded-md" @click="closeSubscriberModal">
              Cancel
            </v-btn>
            <v-btn
              class="white--text w-32 h-12 rounded-md"
              :color="$vuetify.theme.currentTheme.primary"
              @click="saveSubscriber"
              :disabled="!subscriberFormValid"
              :loading="loading.subscribers"
            >
              {{ editingSubscriber ? 'Update' : 'Add' }}
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Import Modal -->
    <v-dialog v-model="importModal" max-width="700" persistent>
      <v-card class="subscriber-modal-card" flat>
        <div class="flex justify-between p-4 bg-gray-200 dark-bg-custom">
          <h4 class="text-xl font-semibold">Import Subscribers</h4>
          <v-icon @click="closeImportModal" class="cursor-pointer">mdi-close</v-icon>
        </div>

        <div class="p-6">
          <v-form ref="importForm" v-model="importFormValid" lazy-validation>
            <!-- File Requirements Info -->
            <div class="mb-6">
              <label class="text-gray-700 text-sm font-medium mb-3 d-block">
                <v-icon small class="mr-1">mdi-information-outline</v-icon>
                File Requirements
              </label>
              <div class="groups-container p-4 border rounded-lg bg-gray-50">
                <ul class="text-sm text-gray-600 mb-0">
                  <li>Supported formats: Excel (.xlsx, .xls) or CSV (.csv)</li>
                  <li>Required columns: <strong>email</strong></li>
                  <li>Optional columns: <strong>first_name</strong>, <strong>last_name</strong></li>
                  <li>Maximum file size: 10MB</li>
                  <li>Maximum rows: 10,000 subscribers</li>
                </ul>
              </div>
            </div>

            <!-- File Upload -->
            <v-row>
              <v-col cols="12">
                <v-file-input
                  v-model="importFile"
                  label="Select Excel or CSV File *"
                  accept=".xlsx,.xls,.csv"
                  outlined
                  dense
                  hide-details
                  required
                  :rules="[
                    (v) => !!v || 'Please select a file',
                    (v) => !v || v.size < ******** || 'File size must be less than 10MB'
                  ]"
                  prepend-inner-icon="mdi-file-excel-outline"
                  show-size
                  clearable
                ></v-file-input>
              </v-col>
            </v-row>

            <!-- Groups Selection -->
            <div>
              <label class="text-gray-700 text-sm font-medium mb-3 d-block">
                <v-icon small class="mr-1">mdi-account-group</v-icon>
                Assign to Groups
              </label>
              <div class="groups-container p-4 border rounded-lg bg-gray-50">
                <div v-if="subscriberGroups.length === 0" class="text-center py-4">
                  <v-icon size="48" color="grey lighten-1">mdi-account-group-outline</v-icon>
                  <p class="text-grey text-sm mt-2">No groups available. Create groups first.</p>
                </div>
                <v-row v-else>
                  <v-col v-for="group in subscriberGroups" :key="group.id" cols="6" md="4">
                    <v-checkbox
                      v-model="importForm.groups"
                      :value="group.name"
                      :label="`${group.name} (${group.active_count})`"
                      hide-details
                      dense
                      class="group-checkbox"
                    >
                      <template v-slot:label>
                        <div class="d-flex align-center">
                          <v-chip x-small :color="group.color" class="mr-2" text-color="white">
                            {{ group.active_count }}
                          </v-chip>
                          <span>{{ group.name }}</span>
                        </div>
                      </template>
                    </v-checkbox>
                  </v-col>
                </v-row>
              </div>
            </div>

            <!-- Sample Download -->
            <div class="mt-4 text-center">
              <v-btn text color="primary" @click="downloadSample">
                <v-icon left>mdi-download</v-icon>
                Download Sample Template
              </v-btn>
            </div>
          </v-form>
        </div>

        <v-card-actions class="px-6 pb-6">
          <div class="flex items-center justify-between w-full">
            <v-btn outlined class="w-32 h-12 rounded-md" @click="closeImportModal"> Cancel </v-btn>
            <v-btn
              class="white--text w-32 h-12 rounded-md"
              :color="$vuetify.theme.currentTheme.primary"
              @click="confirmImport"
              :disabled="!importFile || importForm.groups.length === 0"
              :loading="loading.import"
            >
              Import
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Group Modal -->
    <v-dialog v-model="groupModal" max-width="700" persistent>
      <v-card class="subscriber-modal-card" flat>
        <div class="flex justify-between p-4 bg-gray-200 dark-bg-custom">
          <h4 class="text-xl font-semibold">{{ editingGroup ? 'Edit' : 'Create New' }} Group</h4>
          <v-icon @click="closeGroupModal" class="cursor-pointer">mdi-close</v-icon>
        </div>

        <div class="p-6">
          <v-form ref="groupForm" v-model="groupFormValid" lazy-validation>
            <!-- Basic Information -->
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="groupForm.name"
                  label="Group Name *"
                  outlined
                  dense
                  hide-details
                  required
                  :rules="[
                    (v) => !!v || 'Group name is required',
                    (v) => (v && v.length >= 2) || 'Group name must be at least 2 characters',
                    (v) => (v && v.length <= 50) || 'Group name must be less than 50 characters'
                  ]"
                  prepend-inner-icon="mdi-tag-outline"
                  counter="50"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="groupForm.description"
                  label="Description (Optional)"
                  outlined
                  dense
                  rows="3"
                  hide-details
                  prepend-inner-icon="mdi-text"
                  counter="200"
                  :rules="[
                    (v) => !v || v.length <= 200 || 'Description must be less than 200 characters'
                  ]"
                ></v-textarea>
              </v-col>
            </v-row>

            <!-- Group Statistics (only show when editing) -->
            <div v-if="editingGroup && groupForm.id">
              <label class="text-gray-700 text-sm font-medium mb-3 d-block">
                <v-icon small class="mr-1">mdi-chart-line</v-icon>
                Group Statistics
              </label>
              <div class="groups-container p-4 border rounded-lg bg-gray-50">
                <v-row>
                  <v-col cols="6" md="3">
                    <div class="text-center">
                      <v-icon size="24" color="success" class="mb-1">mdi-account-check</v-icon>
                      <div class="text-h6 font-weight-bold success--text">
                        {{ groupForm.active_count || 0 }}
                      </div>
                      <div class="text-caption grey--text">Active</div>
                    </div>
                  </v-col>
                  <v-col cols="6" md="3">
                    <div class="text-center">
                      <v-icon size="24" color="primary" class="mb-1">mdi-account-group</v-icon>
                      <div class="text-h6 font-weight-bold primary--text">
                        {{ groupForm.total_count || 0 }}
                      </div>
                      <div class="text-caption grey--text">Total</div>
                    </div>
                  </v-col>
                  <v-col cols="6" md="3">
                    <div class="text-center">
                      <v-icon size="24" color="orange" class="mb-1">mdi-email-send</v-icon>
                      <div class="text-h6 font-weight-bold orange--text">
                        {{ groupForm.sent_count || 0 }}
                      </div>
                      <div class="text-caption grey--text">Sent</div>
                    </div>
                  </v-col>
                  <v-col cols="6" md="3">
                    <div class="text-center">
                      <v-icon size="24" color="info" class="mb-1">mdi-email-open</v-icon>
                      <div class="text-h6 font-weight-bold info--text">
                        {{ groupForm.open_count || 0 }}
                      </div>
                      <div class="text-caption grey--text">Opens</div>
                    </div>
                  </v-col>
                </v-row>
              </div>
            </div>

            <!-- Tips for new groups -->
            <div v-if="!editingGroup" class="mt-4">
              <label class="text-gray-700 text-sm font-medium mb-3 d-block">
                <v-icon small class="mr-1">mdi-lightbulb-outline</v-icon>
                Group Tips
              </label>
              <div class="groups-container p-4 border rounded-lg bg-gray-50">
                <ul class="text-sm text-gray-600 mb-0">
                  <li>Use descriptive names like "Newsletter Subscribers" or "Product Updates"</li>
                  <li>Groups help you send targeted campaigns to specific audiences</li>
                  <li>You can assign subscribers to multiple groups</li>
                  <li>Group statistics will be available after adding subscribers</li>
                </ul>
              </div>
            </div>
          </v-form>
        </div>

        <v-card-actions class="px-6 pb-6">
          <div class="flex items-center justify-between w-full">
            <v-btn outlined class="w-32 h-12 rounded-md" @click="closeGroupModal"> Cancel </v-btn>
            <v-btn
              class="white--text w-32 h-12 rounded-md"
              :color="$vuetify.theme.currentTheme.primary"
              @click="saveGroup"
              :disabled="!groupFormValid"
              :loading="loading.groups"
            >
              {{ editingGroup ? 'Update' : 'Create' }}
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Confirmation Dialog -->
    <v-dialog v-model="confirmDialog.show" max-width="400" persistent>
      <v-card>
        <v-card-title class="text-h6">
          {{ confirmDialog.title }}
        </v-card-title>
        <v-card-text>
          {{ confirmDialog.message }}
        </v-card-text>
        <v-card-actions class="justify-end">
          <v-btn text @click="confirmDialog.show = false"> Cancel </v-btn>
          <v-btn
            color="error"
            @click="
              confirmDialog.action()
              confirmDialog.show = false
            "
            :loading="confirmDialog.loading"
          >
            {{ confirmDialog.confirmText }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import ActiveTabComponent from '@/components/ActitiveTabcomponent.vue'
import { debounce, fullName } from '@/utils/common'
import EmailTemplates from './EmailTemplates.vue'
import axios from 'axios'

export default {
  name: 'EmailCampaign',
  components: {
    ActiveTabComponent,
    EmailTemplates
  },
  data() {
    return {
      // MailerLite Configuration
      mailerLiteConfig: {
        apiUrl: 'https://connect.mailerlite.com/api',
        bearerToken: '', // You'll provide this
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json'
        }
      },

      // Loading states
      loading: {
        subscribers: false,
        groups: false,
        templates: false,
        users: false,
        customers: false,
        import: false
      },

      // Tab configuration
      tabs: [
        {
          key: 'subscribers',
          label: 'Subscribers',
          icon: 'mdi-account-group',
          component: 'subscribers'
        },
        {
          key: 'templates',
          label: 'Email Templates',
          icon: 'mdi-email-outline',
          component: 'templates'
        },
        {
          key: 'preBuildTemplates',
          label: 'Templates',
          icon: 'mdi-email-outline',
          component: 'preBuildTemplates'
        }
      ],

      // Template data
      templateModal: false,
      editingTemplate: false,
      templateFormValid: false,
      templateForm: {
        id: null,
        name: '',
        subject: '',
        fromName: '',
        fromEmail: '',
        preheader: '',
        content:
          "<p>Hello {first_name},</p><p><br></p><p>Welcome to our service! We're excited to have you on board.</p><p><br></p><p>Best regards,<br>The Team</p>",
        groups: []
      },
      templatePreview: '',

      templateHeaders: [
        { text: 'Name', value: 'name', sortable: true },
        { text: 'Subject', value: 'subject', sortable: true },
        { text: 'Type', value: 'type', sortable: true },
        { text: 'Status', value: 'status', sortable: true },
        { text: 'Created', value: 'created_at', sortable: true },
        { text: 'Actions', value: 'actions', sortable: false, width: '150px' }
      ],

      // MailerLite Templates Data
      templates: [],
      templatesPagination: {
        current_page: 1,
        per_page: 25,
        total: 0,
        last_page: 1
      },

      // Subscriber data
      subscriberModal: false,
      subscriberFormValid: false,
      editingSubscriber: false,
      subscriberForm: {
        id: null,
        source: 'manual', // manual, users, customers
        email: '',
        firstName: '',
        lastName: '',
        groups: [],
        selectedUser: null,
        selectedCustomer: null
      },
      subscriberSearch: '',

      // System data for subscriber selection
      systemUsers: [],
      customers: [],
      userSearch: '',
      customerSearch: '',

      subscriberHeaders: [
        { text: 'Email', value: 'email', sortable: true },
        { text: 'Name', value: 'name', sortable: true },
        { text: 'Status', value: 'status', sortable: true },
        { text: 'Groups', value: 'groups', sortable: false },
        { text: 'Subscribed', value: 'subscribed_at', sortable: true },
        { text: 'Actions', value: 'actions', sortable: false, width: '120px' }
      ],

      // MailerLite Data
      subscribers: [],
      subscriberGroups: [],
      subscribersPagination: {
        current_page: 1,
        per_page: 25,
        total: 0,
        last_page: 1
      },

      // Filters
      subscriberFilters: {
        status: 'active', // active, unsubscribed, unconfirmed, bounced, junk
        group_id: null
      },

      statusOptions: [
        { text: 'Active', value: 'active' },
        { text: 'Unsubscribed', value: 'unsubscribed' },
        { text: 'Unconfirmed', value: 'unconfirmed' },
        { text: 'Bounced', value: 'bounced' },
        { text: 'Junk', value: 'junk' }
      ],

      // Confirmation dialog
      confirmDialog: {
        show: false,
        title: '',
        message: '',
        confirmText: 'Delete',
        action: () => {},
        loading: false
      },

      // Import data
      importModal: false,
      importFormValid: false,
      importFile: null,
      importForm: {
        groups: []
      },

      // Group data
      groupModal: false,
      groupFormValid: false,
      editingGroup: false,
      groupForm: {
        id: null,
        name: '',
        description: '',
        active_count: 0,
        total_count: 0,
        sent_count: 0,
        open_count: 0,
        click_count: 0
      },

      // Validation rules
      emailRules: [
        (v) => !!v || 'Email is required',
        (v) => /.+@.+\..+/.test(v) || 'Email must be valid'
      ]
    }
  },

  computed: {
    groupFilterOptions() {
      return [
        { text: 'All Groups', value: null },
        ...this.subscriberGroups.map((group) => ({
          text: `${group.name} (${group.active_count})`,
          value: group.id
        }))
      ]
    },

    hasActiveFilters() {
      return this.subscriberFilters.status !== 'active' || this.subscriberFilters.group_id !== null
    },

    selectedUserPreview() {
      if (this.subscriberForm.source === 'users' && this.subscriberForm.selectedUser) {
        return this.systemUsers.find((user) => user._id === this.subscriberForm.selectedUser)
      }
      return null
    },

    selectedCustomerPreview() {
      if (this.subscriberForm.source === 'customers' && this.subscriberForm.selectedCustomer) {
        return this.customers.find(
          (customer) => customer._id === this.subscriberForm.selectedCustomer
        )
      }
      return null
    }
  },

  async mounted() {
    this.updatePreview()

    // Check if token is stored in localStorage
    const storedToken = localStorage.getItem('mailerlite_token')
    if (storedToken) {
      this.setMailerLiteToken(storedToken)
    }
    await Promise.all([this.fetchSubscribers(), this.fetchGroups()])
  },

  methods: {
    // MailerLite API Methods
    setMailerLiteToken(token) {
      this.mailerLiteConfig.bearerToken = token
      this.mailerLiteConfig.headers.Authorization = `Bearer ${token}`
    },

    async fetchSubscribers(page = 1, filters = {}) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return
      }

      this.loading.subscribers = true
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: this.subscribersPagination.per_page.toString(),
          ...filters
        })

        const response = await axios.get(`${this.mailerLiteConfig.apiUrl}/subscribers?${params}`, {
          headers: this.mailerLiteConfig.headers
        })

        this.subscribers = response.data.data.map((subscriber) => ({
          id: subscriber.id,
          email: subscriber.email,
          name:
            `${subscriber.fields.name || ''} ${subscriber.fields.last_name || ''}`.trim() || 'N/A',
          firstName: subscriber.fields.name || '',
          lastName: subscriber.fields.last_name || '',
          status: subscriber.status,
          subscribed_at: subscriber.subscribed_at,
          groups: subscriber.groups?.map((group) => group.name) || [],
          fields: subscriber.fields
        }))

        // Update pagination
        this.subscribersPagination = {
          current_page: response.data.meta.current_page,
          per_page: response.data.meta.per_page,
          total: response.data.meta.total,
          last_page: response.data.meta.last_page
        }
      } catch (error) {
        console.error('Error fetching subscribers:', error)
        this.$toast.error('Failed to fetch subscribers from MailerLite')
      } finally {
        this.loading.subscribers = false
      }
    },

    async fetchGroups() {
      if (!this.mailerLiteConfig.bearerToken) {
        return
      }

      this.loading.groups = true
      try {
        const response = await axios.get(`${this.mailerLiteConfig.apiUrl}/groups`, {
          headers: this.mailerLiteConfig.headers
        })

        this.subscriberGroups = response.data.data.map((group) => ({
          id: group.id,
          name: group.name,
          description: group.name,
          count: group.active_count || 0,
          color: this.getRandomColor(),
          total_count: group.total_count || 0,
          active_count: group.active_count || 0,
          sent_count: group.sent_count || 0,
          open_count: group.open_count || 0,
          click_count: group.click_count || 0
        }))
      } catch (error) {
        console.error('Error fetching groups:', error)
        this.$toast.error('Failed to fetch groups from MailerLite')
      } finally {
        this.loading.groups = false
      }
    },

    async createGroup(groupData) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        const payload = {
          name: groupData.name
        }

        const response = await axios.post(`${this.mailerLiteConfig.apiUrl}/groups`, payload, {
          headers: this.mailerLiteConfig.headers
        })

        this.$toast.success('Group created successfully!')

        // Refresh groups list
        await this.fetchGroups()
        return true
      } catch (error) {
        console.error('Error creating group:', error)
        if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else {
          this.$toast.error('Failed to create group')
        }
        return false
      }
    },

    async updateGroup(groupId, groupData) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        const payload = {
          name: groupData.name
        }

        const response = await axios.put(
          `${this.mailerLiteConfig.apiUrl}/groups/${groupId}`,
          payload,
          {
            headers: this.mailerLiteConfig.headers
          }
        )
        this.$toast.success('Group updated successfully!')

        // Refresh groups list
        await this.fetchGroups()
        return true
      } catch (error) {
        console.error('Error updating group:', error)
        if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else {
          this.$toast.error('Failed to update group')
        }
        return false
      }
    },

    async deleteGroup(groupId) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        await axios.delete(`${this.mailerLiteConfig.apiUrl}/groups/${groupId}`, {
          headers: this.mailerLiteConfig.headers
        })

        this.$toast.success('Group deleted successfully!')

        // Refresh groups list
        await this.fetchGroups()
        return true
      } catch (error) {
        console.error('Error deleting group:', error)
        if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else {
          this.$toast.error('Failed to delete group')
        }
        return false
      }
    },

    // MailerLite Campaign Template API Methods
    async fetchTemplates(page = 1) {
      if (!this.mailerLiteConfig.bearerToken) {
        return
      }

      this.loading.templates = true
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: this.templatesPagination.per_page.toString()
        })

        const response = await axios.get(`${this.mailerLiteConfig.apiUrl}/campaigns?${params}`, {
          headers: this.mailerLiteConfig.headers
        })

        this.templates = response.data.data.map((campaign) => ({
          id: campaign.id,
          name: campaign.name,
          subject: campaign.emails?.[0]?.subject || 'No subject',
          fromName: campaign.emails?.[0]?.from_name || '',
          fromEmail: campaign.emails?.[0]?.from || '',
          preheader: campaign.emails?.[0]?.preheader || '',
          type: campaign.type,
          status: campaign.status,
          created_at: campaign.created_at,
          content: campaign.emails?.[0]?.plain_text || '',
          html_content: campaign.emails?.[0]?.html || '',
          groups: campaign.groups || [],
          settings: campaign.settings || {}
        }))

        // Update pagination
        this.templatesPagination = {
          current_page: response.data.meta.current_page,
          per_page: response.data.meta.per_page,
          total: response.data.meta.total,
          last_page: response.data.meta.last_page
        }
      } catch (error) {
        console.error('Error fetching templates:', error)
        this.$toast.error('Failed to fetch templates from MailerLite')
      } finally {
        this.loading.templates = false
      }
    },

    async createTemplate(templateData) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        // Get group IDs from selected group names
        const groupIds =
          templateData.groups
            ?.map((groupName) => {
              const group = this.subscriberGroups.find((g) => g.name === groupName)
              return group ? group.id : null
            })
            .filter(Boolean) || []

        if (groupIds.length === 0) {
          this.$toast.error('Please select at least one recipient group')
          return false
        }

        // Prepare email content with preheader
        let emailContent = templateData.content
        if (templateData.preheader) {
          emailContent = `<div style="display: none; max-height: 0; overflow: hidden;">${templateData.preheader}</div>${emailContent}`
        }

        const payload = {
          name: templateData.name,
          type: 'regular',
          emails: [
            {
              subject: templateData.subject,
              from_name: templateData.fromName,
              from: templateData.fromEmail,
              html: emailContent,
              plain_text: this.stripHtml(templateData.content)
            }
          ],
          groups: groupIds,
          settings: {
            track_opens: true,
            track_clicks: true,
            use_google_analytics: false,
            track_text_clicks: true,
            track_html_clicks: true
          }
        }

        const response = await axios.post(`${this.mailerLiteConfig.apiUrl}/campaigns`, payload, {
          headers: this.mailerLiteConfig.headers
        })
        this.$toast.success('Email campaign created successfully!')

        // Refresh templates list
        await this.fetchTemplates()
        return true
      } catch (error) {
        console.error('Error creating campaign:', error)

        if (error.response?.status === 422) {
          // Validation errors
          if (error.response.data?.errors) {
            const errorMessages = []
            Object.keys(error.response.data.errors).forEach((field) => {
              const fieldErrors = error.response.data.errors[field]
              errorMessages.push(`${field}: ${fieldErrors.join(', ')}`)
            })
            this.$toast.error(`Validation errors: ${errorMessages.join('; ')}`)
          } else {
            this.$toast.error('Validation error. Please check all required fields.')
          }
        } else if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else if (error.response?.status === 401) {
          this.$toast.error('Authentication failed. Please check your API token.')
        } else {
          this.$toast.error('Failed to create campaign. Please try again.')
        }
        return false
      }
    },

    async updateTemplate(templateId, templateData) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        // Get group IDs from selected group names
        const groupIds =
          templateData.groups
            ?.map((groupName) => {
              const group = this.subscriberGroups.find((g) => g.name === groupName)
              return group ? group.id : null
            })
            .filter(Boolean) || []

        if (groupIds.length === 0) {
          this.$toast.error('Please select at least one recipient group')
          return false
        }

        // Prepare email content with preheader
        let emailContent = templateData.content
        if (templateData.preheader) {
          emailContent = `<div style="display: none; max-height: 0; overflow: hidden;">${templateData.preheader}</div>${emailContent}`
        }

        const payload = {
          name: templateData.name,
          emails: [
            {
              subject: templateData.subject,
              from_name: templateData.fromName,
              from: templateData.fromEmail,
              html: emailContent,
              plain_text: this.stripHtml(templateData.content)
            }
          ],
          groups: groupIds
        }

        const response = await axios.put(
          `${this.mailerLiteConfig.apiUrl}/campaigns/${templateId}`,
          payload,
          {
            headers: this.mailerLiteConfig.headers
          }
        )
        this.$toast.success('Campaign updated successfully!')

        // Refresh templates list
        await this.fetchTemplates()
        return true
      } catch (error) {
        console.error('Error updating campaign:', error)

        if (error.response?.status === 422) {
          if (error.response.data?.errors) {
            const errorMessages = []
            Object.keys(error.response.data.errors).forEach((field) => {
              const fieldErrors = error.response.data.errors[field]
              errorMessages.push(`${field}: ${fieldErrors.join(', ')}`)
            })
            this.$toast.error(`Validation errors: ${errorMessages.join('; ')}`)
          } else {
            this.$toast.error('Validation error. Please check all required fields.')
          }
        } else if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else {
          this.$toast.error('Failed to update campaign')
        }
        return false
      }
    },

    async deleteTemplate(templateId) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        await axios.delete(`${this.mailerLiteConfig.apiUrl}/campaigns/${templateId}`, {
          headers: this.mailerLiteConfig.headers
        })

        this.$toast.success('Template deleted successfully!')

        // Refresh templates list
        await this.fetchTemplates()
        return true
      } catch (error) {
        console.error('Error deleting template:', error)
        if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else {
          this.$toast.error('Failed to delete template')
        }
        return false
      }
    },

    stripHtml(html) {
      const tmp = document.createElement('div')
      tmp.innerHTML = html
      return tmp.textContent || tmp.innerText || ''
    },

    async createSubscriber(subscriberData) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        // First create the subscriber without groups
        const payload = {
          email: subscriberData.email,
          fields: {
            name: subscriberData.firstName,
            last_name: subscriberData.lastName
          }
        }

        const response = await axios.post(`${this.mailerLiteConfig.apiUrl}/subscribers`, payload, {
          headers: this.mailerLiteConfig.headers
        })

        const newSubscriberId = response.data.data.id

        // Then add subscriber to selected groups
        if (subscriberData.groups && subscriberData.groups.length > 0) {
          const groupIds = subscriberData.groups
            .map((groupName) => {
              const group = this.subscriberGroups.find((g) => g.name === groupName)
              return group ? group.id : null
            })
            .filter(Boolean)

          // Add subscriber to each selected group using the correct API endpoint
          for (const groupId of groupIds) {
            try {
              await axios.post(
                `${this.mailerLiteConfig.apiUrl}/subscribers/${newSubscriberId}/groups/${groupId}`,
                {},
                {
                  headers: this.mailerLiteConfig.headers
                }
              )
            } catch (groupError) {
              console.warn(`Failed to add subscriber to group ${groupId}:`, groupError)
            }
          }
        }

        this.$toast.success('Subscriber added successfully!')

        // Refresh subscribers list and groups (to update counts)
        await Promise.all([this.fetchSubscribers(), this.fetchGroups()])
        return true
      } catch (error) {
        console.error('Error creating subscriber:', error)
        if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else if (error.response?.data?.errors) {
          const errorMessages = Object.values(error.response.data.errors).flat()
          this.$toast.error(errorMessages.join(', '))
        } else {
          this.$toast.error('Failed to create subscriber')
        }
        return false
      }
    },

    async updateSubscriber(subscriberId, subscriberData) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        // Update subscriber fields
        const payload = {
          fields: {
            name: subscriberData.firstName || '',
            last_name: subscriberData.lastName || ''
          }
        }

        const response = await axios.put(
          `${this.mailerLiteConfig.apiUrl}/subscribers/${subscriberId}`,
          payload,
          {
            headers: this.mailerLiteConfig.headers
          }
        )

        // Handle group assignments using the correct MailerLite API approach
        if (subscriberData.groups) {
          const groupIds = subscriberData.groups
            .map((groupName) => {
              const group = this.subscriberGroups.find((g) => g.name === groupName)
              return group ? group.id : null
            })
            .filter(Boolean)

          // First, get current subscriber groups to determine what to add/remove
          const currentSubscriber = await axios.get(
            `${this.mailerLiteConfig.apiUrl}/subscribers/${subscriberId}`,
            {
              headers: this.mailerLiteConfig.headers
            }
          )

          const currentGroupIds = currentSubscriber.data.data.groups?.map((g) => g.id) || []

          // Add subscriber to new groups using the correct API endpoint
          for (const groupId of groupIds) {
            if (!currentGroupIds.includes(groupId)) {
              try {
                await axios.post(
                  `${this.mailerLiteConfig.apiUrl}/subscribers/${subscriberId}/groups/${groupId}`,
                  {},
                  {
                    headers: this.mailerLiteConfig.headers
                  }
                )
              } catch (groupError) {
                console.warn(`Failed to add subscriber to group ${groupId}:`, groupError)
              }
            }
          }

          // Remove subscriber from groups they're no longer assigned to using the correct API endpoint
          for (const currentGroupId of currentGroupIds) {
            if (!groupIds.includes(currentGroupId)) {
              try {
                await axios.delete(
                  `${this.mailerLiteConfig.apiUrl}/subscribers/${subscriberId}/groups/${currentGroupId}`,
                  {
                    headers: this.mailerLiteConfig.headers
                  }
                )
              } catch (groupError) {
                console.warn(
                  `Failed to remove subscriber from group ${currentGroupId}:`,
                  groupError
                )
              }
            }
          }
        }

        this.$toast.success('Subscriber updated successfully!')

        // Refresh subscribers list and groups
        await Promise.all([this.fetchSubscribers(), this.fetchGroups()])
        return true
      } catch (error) {
        console.error('Error updating subscriber:', error)
        if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else {
          this.$toast.error('Failed to update subscriber')
        }
        return false
      }
    },

    async deleteSubscriber(subscriberId) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return false
      }

      try {
        await axios.delete(`${this.mailerLiteConfig.apiUrl}/subscribers/${subscriberId}`, {
          headers: this.mailerLiteConfig.headers
        })

        this.$toast.success('Subscriber deleted successfully!')

        // Refresh subscribers list
        await this.fetchSubscribers()
        return true
      } catch (error) {
        console.error('Error deleting subscriber:', error)
        this.$toast.error('Failed to delete subscriber')
        return false
      }
    },

    getRandomColor() {
      const colors = ['blue', 'purple', 'green', 'orange', 'red', 'teal', 'indigo', 'pink']
      return colors[Math.floor(Math.random() * colors.length)]
    },

    async refreshData() {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.warning('Please configure your MailerLite API token first')
        return
      }

      await Promise.all([this.fetchSubscribers(), this.fetchGroups()])
    },

    async handlePageChange(page) {
      // Check if we're viewing a specific group
      if (this.subscriberFilters.group_id) {
        // Fetch subscribers for the specific group
        await this.fetchGroupSubscribers(this.subscriberFilters.group_id, page)
      } else {
        // Fetch all subscribers with filters
        this.fetchSubscribers(page, this.subscriberFilters)
      }
    },

    getStatusColor(status) {
      const colors = {
        active: 'success',
        unsubscribed: 'error',
        unconfirmed: 'warning',
        bounced: 'error',
        junk: 'error'
      }
      return colors[status] || 'grey'
    },

    editSubscriber(subscriber) {
      this.editingSubscriber = true
      this.subscriberForm = {
        id: subscriber.id,
        source: 'manual', // Default to manual for existing subscribers
        email: subscriber.email,
        firstName: subscriber.firstName,
        lastName: subscriber.lastName,
        groups: subscriber.groups,
        selectedUser: null,
        selectedCustomer: null
      }
      this.subscriberModal = true
    },

    confirmDeleteSubscriber(subscriber) {
      this.confirmDialog = {
        show: true,
        title: 'Delete Subscriber',
        message: `Are you sure you want to delete subscriber "${subscriber.email}"? This action cannot be undone.`,
        confirmText: 'Delete Subscriber',
        action: () => this.deleteSubscriber(subscriber.id),
        loading: false
      }
    },

    // Tab handling
    handleTabChange(tabKey) {
      // Load data when switching tabs
      if (this.mailerLiteConfig.bearerToken) {
        if (tabKey === 'templates') {
          this.fetchTemplates()
        } else if (tabKey === 'subscribers') {
          this.fetchSubscribers()
          this.fetchGroups()
        }
      }
    },

    editIcon(item) {},
    viewDetails(item) {},
    editAction(item) {},

    // Template methods
    openTemplateModal() {
      this.editingTemplate = false
      this.resetTemplateForm()
      this.templateModal = true
    },

    closeTemplateModal() {
      this.templateModal = false
      this.resetTemplateForm()
    },

    resetTemplateForm() {
      this.editingTemplate = false
      this.templateForm = {
        id: null,
        name: '',
        subject: '',
        fromName: '',
        fromEmail: '',
        preheader: '',
        content:
          "<p>Hello {first_name},</p><p><br></p><p>Welcome to our service! We're excited to have you on board.</p><p><br></p><p>Best regards,<br>The Team</p>",
        groups: []
      }
      this.updatePreview()
    },

    editTemplate(template) {
      this.editingTemplate = true
      this.templateForm = {
        id: template.id,
        name: template.name,
        subject: template.subject,
        fromName: template.fromName || '',
        fromEmail: template.fromEmail || '',
        preheader: template.preheader || '',
        content:
          template.html_content ||
          template.content ||
          "<p>Hello {first_name},</p><p><br></p><p>Welcome to our service! We're excited to have you on board.</p><p><br></p><p>Best regards,<br>The Team</p>",
        groups: template.groups?.map((group) => group.name) || []
      }
      this.updatePreview()
      this.templateModal = true
    },

    duplicateTemplate(template) {
      this.editingTemplate = false
      this.templateForm = {
        id: null,
        name: `${template.name} (Copy)`,
        subject: template.subject,
        fromName: template.fromName || '',
        fromEmail: template.fromEmail || '',
        preheader: template.preheader || '',
        content:
          template.html_content ||
          template.content ||
          "<p>Hello {first_name},</p><p><br></p><p>Welcome to our service! We're excited to have you on board.</p><p><br></p><p>Best regards,<br>The Team</p>",
        groups: template.groups?.map((group) => group.name) || []
      }
      this.updatePreview()
      this.templateModal = true
    },

    async saveTemplate() {
      if (this.$refs.templateForm.validate()) {
        let success = false

        if (this.editingTemplate && this.templateForm.id) {
          success = await this.updateTemplate(this.templateForm.id, this.templateForm)
        } else {
          success = await this.createTemplate(this.templateForm)
        }

        if (success) {
          this.closeTemplateModal()
        }
      }
    },

    confirmDeleteTemplate(template) {
      this.confirmDialog = {
        show: true,
        title: 'Delete Template',
        message: `Are you sure you want to delete the template "${template.name}"? This action cannot be undone.`,
        confirmText: 'Delete Template',
        action: () => this.deleteTemplate(template.id),
        loading: false
      }
    },

    async refreshTemplates() {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.warning('Please configure your MailerLite API token first')
        return
      }

      await this.fetchTemplates()
    },

    handleTemplatePageChange(page) {
      this.fetchTemplates(page)
    },

    getTemplateTypeColor(type) {
      const colors = {
        regular: 'primary',
        ab: 'orange',
        resend: 'purple',
        rss: 'green'
      }
      return colors[type] || 'grey'
    },

    getTemplateStatusColor(status) {
      const colors = {
        draft: 'grey',
        ready: 'blue',
        sending: 'orange',
        sent: 'success',
        cancelled: 'error'
      }
      return colors[status] || 'grey'
    },

    formatDate(dateString) {
      if (!dateString) return 'N/A'
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    execCommand(command) {
      document.execCommand(command, false, null)
      this.$refs.emailEditor.focus()
      this.updatePreview()
    },

    updatePreview() {
      this.$nextTick(() => {
        if (this.$refs.emailEditor) {
          this.templateForm.content = this.$refs.emailEditor.innerHTML
          this.templatePreview = this.templateForm.content
        }
      })
    },

    // Subscriber methods
    openSubscriberModal() {
      this.editingSubscriber = false
      this.resetSubscriberForm()
      this.subscriberModal = true
    },

    closeSubscriberModal() {
      this.subscriberModal = false
      this.resetSubscriberForm()
    },

    resetSubscriberForm() {
      this.editingSubscriber = false
      this.subscriberForm = {
        id: null,
        source: 'manual',
        email: '',
        firstName: '',
        lastName: '',
        groups: [],
        selectedUser: null,
        selectedCustomer: null
      }
      this.userSearch = ''
      this.customerSearch = ''
      this.systemUsers = []
      this.customers = []
    },

    async saveSubscriber() {
      if (this.$refs.subscriberForm.validate()) {
        // Prepare subscriber data based on source
        let subscriberData = { ...this.subscriberForm }

        if (this.subscriberForm.source === 'users' && this.selectedUserPreview) {
          subscriberData.email = this.selectedUserPreview.email
          subscriberData.firstName =
            this.selectedUserPreview.firstName || this.selectedUserPreview.name
          subscriberData.lastName = this.selectedUserPreview.lastName || ''
        } else if (this.subscriberForm.source === 'customers' && this.selectedCustomerPreview) {
          subscriberData.email =
            this.selectedCustomerPreview.email ||
            `${this.selectedCustomerPreview.phone}@customer.local`
          subscriberData.firstName =
            this.selectedCustomerPreview.firstName ||
            this.selectedCustomerPreview.customerFullName?.split(' ')[0] ||
            ''
          subscriberData.lastName =
            this.selectedCustomerPreview.lastName ||
            this.selectedCustomerPreview.customerFullName?.split(' ').slice(1).join(' ') ||
            ''
        }

        let success = false

        if (this.editingSubscriber && this.subscriberForm.id) {
          success = await this.updateSubscriber(this.subscriberForm.id, subscriberData)
        } else {
          success = await this.createSubscriber(subscriberData)
        }

        if (success) {
          this.closeSubscriberModal()
        }
      }
    },

    // System Users API Methods
    async fetchSystemUsers() {
      if (!this.userSearch || this.userSearch.length < 2) return

      // Don't fetch if a user is already selected
      if (this.subscriberForm.selectedUser) return

      this.loading.users = true
      try {
        const params = {
          search: this.userSearch,
          limit: 20
        }

        const response = await this.$axios.get('/workforce/v2/users', { params })
        this.systemUsers = response.data?.users || []
      } catch (error) {
        console.error('Error fetching system users:', error)
        this.$toast.error('Failed to fetch system users')
      } finally {
        this.loading.users = false
      }
    },

    debounceUserSearch: debounce(function () {
      // Don't search if a user is already selected
      if (this.subscriberForm.selectedUser) return
      this.fetchSystemUsers()
    }, 800),

    // Customers API Methods
    async fetchCustomers() {
      if (!this.customerSearch || this.customerSearch.length < 2) return

      // Don't fetch if a customer is already selected
      if (this.subscriberForm.selectedCustomer) return

      this.loading.customers = true
      try {
        const params = {
          search: this.customerSearch,
          limit: 20
        }

        const response = await this.$axios.get('/workforce/customers', { params })
        this.customers = response.data?.customers || []
      } catch (error) {
        console.error('Error fetching customers:', error)
        this.$toast.error('Failed to fetch customers')
      } finally {
        this.loading.customers = false
      }
    },

    debounceCustomerSearch: debounce(function () {
      // Don't search if a customer is already selected
      if (this.subscriberForm.selectedCustomer) return
      this.fetchCustomers()
    }, 800),

    // Handle source change to clear previous selections
    onSourceChange() {
      this.subscriberForm.selectedUser = null
      this.subscriberForm.selectedCustomer = null
      this.userSearch = ''
      this.customerSearch = ''
      this.systemUsers = []
      this.customers = []
    },

    // Handle user selection to stop further API calls
    onUserSelected(value) {
      if (value) {
        // Clear search to prevent further API calls
        this.userSearch = ''
      }
    },

    // Handle customer selection to stop further API calls
    onCustomerSelected(value) {
      if (value) {
        // Clear search to prevent further API calls
        this.customerSearch = ''
      }
    },

    // Helper methods for display
    getUserFullName(user) {
      if (!user) return ''
      return `${user.firstName || user.name || ''} ${user.lastName || ''}`.trim() || user.email
    },

    getUserInitials(user) {
      if (!user) return '??'
      const firstName = user.firstName || user.name || ''
      const lastName = user.lastName || ''
      return (
        `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() ||
        user.email?.charAt(0).toUpperCase() ||
        '??'
      )
    },

    getCustomerFullName(customer) {
      if (!customer) return ''
      return (
        customer.customerFullName ||
        `${customer.firstName || ''} ${customer.lastName || ''}`.trim() ||
        customer.email ||
        customer.phone ||
        'Unknown Customer'
      )
    },

    getCustomerInitials(customer) {
      if (!customer) return '??'
      const fullName =
        customer.customerFullName || `${customer.firstName || ''} ${customer.lastName || ''}`.trim()
      if (fullName) {
        const names = fullName.split(' ')
        return `${names[0]?.charAt(0)}${names[1]?.charAt(0) || ''}`.toUpperCase()
      }
      return customer.email?.charAt(0).toUpperCase() || customer.phone?.charAt(0) || '??'
    },

    // Methods to check if items should be disabled
    isUserDisabled(user) {
      return !user.email
    },

    isCustomerDisabled(customer) {
      return !customer.email && !customer.phone
    },

    // Import methods
    openImportModal() {
      this.importFile = null
      this.importFormValid = false
      this.importForm.groups = []
      this.importModal = true
    },

    closeImportModal() {
      this.importModal = false
      this.importFile = null
      this.importFormValid = false
      this.importForm.groups = []
    },

    confirmImport() {
      // Here you would normally process the import via API
      this.$toast.success('Subscribers imported successfully!')
      this.closeImportModal()
    },

    downloadSample() {
      // Here you would normally trigger a file download
      this.$toast.info('Sample Excel file downloaded!')
    },

    // Group methods
    openGroupModal() {
      this.editingGroup = false
      this.resetGroupForm()
      this.groupModal = true
    },

    closeGroupModal() {
      this.groupModal = false
      this.resetGroupForm()
    },

    resetGroupForm() {
      this.editingGroup = false
      this.groupForm = {
        id: null,
        name: '',
        description: '',
        active_count: 0,
        total_count: 0,
        sent_count: 0,
        open_count: 0,
        click_count: 0
      }
    },

    editGroup(group) {
      this.editingGroup = true
      this.groupForm = {
        id: group.id,
        name: group.name,
        description: group.description || '',
        active_count: group.active_count || 0,
        total_count: group.total_count || 0,
        sent_count: group.sent_count || 0,
        open_count: group.open_count || 0,
        click_count: group.click_count || 0
      }
      this.groupModal = true
    },

    async saveGroup() {
      if (this.$refs.groupForm.validate()) {
        let success = false

        if (this.editingGroup && this.groupForm.id) {
          success = await this.updateGroup(this.groupForm.id, this.groupForm)
        } else {
          success = await this.createGroup(this.groupForm)
        }

        if (success) {
          this.closeGroupModal()
        }
      }
    },

    confirmDeleteGroup(group) {
      this.confirmDialog = {
        show: true,
        title: 'Delete Group',
        message: `Are you sure you want to delete the group "${group.name}"? This action cannot be undone and will remove all subscribers from this group.`,
        confirmText: 'Delete Group',
        action: () => this.deleteGroup(group.id),
        loading: false
      }
    },

    async fetchGroupSubscribers(groupId, page = 1) {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return
      }

      this.loading.subscribers = true
      try {
        // Use the specific group subscribers endpoint
        const response = await axios.get(
          `${this.mailerLiteConfig.apiUrl}/groups/${groupId}/subscribers`,
          {
            headers: this.mailerLiteConfig.headers,
            params: {
              page: page,
              limit: this.subscribersPagination.per_page
            }
          }
        )

        // Find the group name for display
        const group = this.subscriberGroups.find((g) => g.id === groupId)
        const groupName = group ? group.name : 'Unknown Group'

        // Update subscribers list with group-specific data
        this.subscribers = response.data.data.map((subscriber) => ({
          id: subscriber.id,
          email: subscriber.email,
          name:
            `${subscriber.fields.name || ''} ${subscriber.fields.last_name || ''}`.trim() || 'N/A',
          firstName: subscriber.fields.name || '',
          lastName: subscriber.fields.last_name || '',
          status: subscriber.status,
          subscribed_at: subscriber.subscribed_at,
          groups: [groupName], // Since we're filtering by this group
          fields: subscriber.fields
        }))

        // Update pagination for group-specific view
        this.subscribersPagination = {
          current_page: response.data.meta.current_page,
          per_page: response.data.meta.per_page,
          total: response.data.meta.total,
          last_page: response.data.meta.last_page
        }
      } catch (error) {
        console.error('Error fetching group subscribers:', error)
        if (error.response?.status === 404) {
          this.$toast.error('Group not found or has no subscribers')
        } else if (error.response?.data?.message) {
          this.$toast.error(error.response.data.message)
        } else {
          this.$toast.error('Failed to fetch group subscribers')
        }
      } finally {
        this.loading.subscribers = false
      }
    },

    async viewGroupSubscribers(group) {
      // Set the filter to show we're viewing a specific group
      this.subscriberFilters.group_id = group.id

      // Fetch the first page of group subscribers
      await this.fetchGroupSubscribers(group.id, 1)

      this.$toast.success(`Showing subscribers from "${group.name}" group`)
    },

    // Filter methods
    applyFilters() {
      this.fetchSubscribers(1, this.subscriberFilters)
    },

    clearStatusFilter() {
      this.subscriberFilters.status = 'active'
      this.applyFilters()
    },

    clearGroupFilter() {
      this.subscriberFilters.group_id = null
      // Reset to show all subscribers instead of group-specific view
      this.fetchSubscribers(1, this.subscriberFilters)
    },

    clearAllFilters() {
      this.subscriberFilters = {
        status: 'active',
        group_id: null
      }
      this.applyFilters()
    },

    getGroupNameById(groupId) {
      const group = this.subscriberGroups.find((g) => g.id === groupId)
      return group ? group.name : 'Unknown Group'
    },

    async exportSubscribers() {
      if (!this.mailerLiteConfig.bearerToken) {
        this.$toast.error('Please set your MailerLite API token first')
        return
      }

      try {
        // Create CSV content
        const headers = ['Email', 'First Name', 'Last Name', 'Status', 'Groups', 'Subscribed Date']
        const csvContent = [
          headers.join(','),
          ...this.subscribers.map((subscriber) =>
            [
              subscriber.email,
              subscriber.firstName || '',
              subscriber.lastName || '',
              subscriber.status,
              subscriber.groups.join('; '),
              subscriber.subscribed_at || ''
            ]
              .map((field) => `"${field}"`)
              .join(',')
          )
        ].join('\n')

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `subscribers_${new Date().toISOString().split('T')[0]}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$toast.success('Subscribers exported successfully!')
      } catch (error) {
        console.error('Error exporting subscribers:', error)
        this.$toast.error('Failed to export subscribers')
      }
    },

    // Enhanced editor methods
    insertVariable(variable) {
      const editor = this.$refs.emailEditor
      if (editor) {
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)
          range.deleteContents()
          range.insertNode(document.createTextNode(variable))
          range.collapse(false)
          selection.removeAllRanges()
          selection.addRange(range)
        } else {
          editor.innerHTML += variable
        }
        this.updatePreview()
        editor.focus()
      }
    },

    insertLink() {
      const url = prompt('Enter the URL:')
      if (url) {
        const text = prompt('Enter the link text:', url)
        if (text) {
          const editor = this.$refs.emailEditor
          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            const link = document.createElement('a')
            link.href = url
            link.textContent = text
            link.style.color = '#1976d2'
            range.deleteContents()
            range.insertNode(link)
            range.collapse(false)
            selection.removeAllRanges()
            selection.addRange(range)
          } else {
            editor.innerHTML += `<a href="${url}" style="color: #1976d2;">${text}</a>`
          }
          this.updatePreview()
          editor.focus()
        }
      }
    },

    insertImage() {
      const url = prompt('Enter the image URL:')
      if (url) {
        const alt = prompt('Enter alt text for the image:', 'Image')
        if (alt !== null) {
          const editor = this.$refs.emailEditor
          const selection = window.getSelection()
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0)
            const img = document.createElement('img')
            img.src = url
            img.alt = alt
            img.style.maxWidth = '100%'
            img.style.height = 'auto'
            range.deleteContents()
            range.insertNode(img)
            range.collapse(false)
            selection.removeAllRanges()
            selection.addRange(range)
          } else {
            editor.innerHTML += `<img src="${url}" alt="${alt}" style="max-width: 100%; height: auto;">`
          }
          this.updatePreview()
          editor.focus()
        }
      }
    },

    handlePaste(event) {
      // Prevent pasting formatted content and paste as plain text
      event.preventDefault()
      const text = (event.clipboardData || window.clipboardData).getData('text/plain')
      document.execCommand('insertText', false, text)
      this.updatePreview()
    },

    getTotalRecipients() {
      return this.templateForm.groups.reduce((total, groupName) => {
        const group = this.subscriberGroups.find((g) => g.name === groupName)
        return total + (group ? group.active_count : 0)
      }, 0)
    },

    async saveDraft() {
      if (!this.templateForm.name || !this.templateForm.subject) {
        this.$toast.error('Campaign name and subject are required to save as draft')
        return
      }

      // For now, we'll save it as a regular campaign but with draft status
      // MailerLite campaigns are created as drafts by default
      const success = await this.createTemplate({
        ...this.templateForm,
        fromName: this.templateForm.fromName || 'Draft Sender',
        fromEmail: this.templateForm.fromEmail || '<EMAIL>',
        groups: this.templateForm.groups.length > 0 ? this.templateForm.groups : ['All Users']
      })

      if (success) {
        this.$toast.success('Campaign saved as draft!')
        this.closeTemplateModal()
      }
    },

    async sendTestEmail() {
      if (!this.templateFormValid) {
        this.$toast.error('Please fill in all required fields before sending a test email')
        return
      }

      const testEmail = prompt('Enter email address to send test email to:')
      if (!testEmail || !/.+@.+\..+/.test(testEmail)) {
        this.$toast.error('Please enter a valid email address')
        return
      }

      try {
        // For test emails, we would typically use a different API endpoint
        // This is a placeholder implementation
        this.$toast.info('Test email functionality would be implemented here')
      } catch (error) {
        console.error('Error sending test email:', error)
        this.$toast.error('Failed to send test email')
      }
    },

    // Utility methods
    getGroupColor(groupName) {
      const group = this.subscriberGroups.find((g) => g.name === groupName)
      return group ? group.color : 'gray'
    }
  }
}
</script>

<style scoped>
.email-editor {
  outline: none;
  font-family: Arial, sans-serif;
  line-height: 1.6;
}

.email-editor:focus {
  border-color: #1976d2 !important;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.template-preview {
  min-height: 200px;
  border: 1px dashed #ccc;
  background-color: #f9f9f9;
  font-family: Arial, sans-serif;
  line-height: 1.6;
}

.email-preview-header {
  background-color: #f5f5f5;
  border-radius: 4px;
  font-family: monospace;
}

.preview-container {
  max-height: 500px;
  overflow-y: auto;
}

.editor-toolbar {
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.v-data-table {
  border-radius: 8px;
}

.gap-2 > * {
  margin-right: 8px;
}

.gap-3 > * {
  margin-right: 12px;
}

/* Enhanced card styling */
.v-card.outlined {
  border: 1px solid #e0e0e0;
}

.v-card-title {
  background-color: #fafafa;
  border-bottom: 1px solid #e0e0e0;
}

/* Form field spacing */
.v-text-field {
  margin-bottom: 8px;
}

/* Checkbox styling */
.v-input--checkbox {
  margin-right: 16px;
  margin-bottom: 8px;
}

/* Alert styling */
.v-alert.v-alert--dense {
  padding: 8px 16px;
}

/* Button group styling */
.v-btn-toggle .v-btn {
  min-width: 36px;
}

/* Subscriber Modal Styling */
.subscriber-modal-card {
  border-radius: 8px;
  overflow: hidden;
}

.dark-bg-custom {
  background-color: #f5f5f5;
}

.groups-container {
  background-color: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.group-checkbox {
  margin-bottom: 8px;
}

.group-checkbox .v-input__slot {
  margin-bottom: 0;
}

/* Radio group styling */
.v-radio-group {
  margin-top: 0;
}

.v-radio-group .v-input__slot {
  margin-bottom: 0;
}

/* Autocomplete styling */
.v-autocomplete .v-input__prepend-inner {
  margin-top: 12px;
}

/* User/Customer preview card */
.v-card.outlined {
  transition: all 0.2s ease;
}

.v-card.outlined:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Button styling */
.v-btn.rounded-md {
  border-radius: 6px !important;
}

/* User and Customer list item styling */
.user-list-item.v-list-item--disabled,
.customer-list-item.v-list-item--disabled,
.user-list-item.disabled-item,
.customer-list-item.disabled-item {
  opacity: 0.6;
  pointer-events: none;
}

.user-list-item.v-list-item--disabled .v-avatar,
.customer-list-item.v-list-item--disabled .v-avatar,
.user-list-item.disabled-item .v-avatar,
.customer-list-item.disabled-item .v-avatar {
  background-color: #bdbdbd !important;
}

.disabled-item {
  cursor: not-allowed !important;
}

.text--disabled {
  color: rgba(0, 0, 0, 0.38) !important;
}

/* Tooltip styling */
.v-tooltip__content {
  font-size: 12px;
  max-width: 200px;
}

/* Alert styling for info messages */
.v-alert.v-alert--dense.v-alert--text {
  padding: 8px 12px;
  margin-top: 8px;
}

/* Dark mode support */
.theme--dark .dark-bg-custom {
  background-color: #2d2d2d;
}

.theme--dark .groups-container {
  background-color: #1e1e1e;
  border-color: #404040;
}

.theme--dark .text--disabled {
  color: rgba(255, 255, 255, 0.38) !important;
}
</style>
