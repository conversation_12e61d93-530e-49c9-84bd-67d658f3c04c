<template>
  <div>
    <div v-if="!isLoad" class="corp-exp">
      <v-card :color="$vuetify.theme.currentTheme.backGroundColor">
        <v-toolbar class="corp-toolbar" flat :color="$vuetify.theme.currentTheme.backGroundColor">
          <v-toolbar-title >
            <div class="corp-header">
              <div class="title">Transactions</div>
            </div>
          </v-toolbar-title>
        </v-toolbar>
        <div v-if="isFilterEnabled" class="advance-filters">
          <v-row class="flex justify-end">
            <v-col cols="10" sm="5" md="3">
              <v-menu
                ref="isRange"
                v-model="isRange"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="dateRangeText"
                    label="Select A Date Range"
                    prepend-icon="mdi-calendar"
                    readonly
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    hide-details
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="dates"
                  no-title
                  scrollable
                  range
                  :allowed-dates="disabledAfterToday"
                >
                </v-date-picker>
              </v-menu>
            </v-col>
            <v-col cols="10" sm="5" md="3">
              <v-select
                v-model="selectedStatus"
                :items="transactionStatues"
                label="Filter By Status"
                density="compact"
                outlined
                dense
                hide-details
              ></v-select>
            </v-col>
            <v-col class="apply-filters-control">
              <v-btn
                type="button"
                @click="printClaim"
                variant="flat"
                :color="$vuetify.theme.currentTheme.primary"
                class="ml-3 white--text"
                :disabled="!groupedTransactions"
                v-show="isFeatureVisible('PRINT_CLAIM_REPORTS')"
              >
                Print Claims
              </v-btn>

              <v-btn
                type="button"
                @click="getClaims"
                variant="flat"
                :color="$vuetify.theme.currentTheme.primary"
                class="ml-3 white--text"
              >
                Fetch Claims
              </v-btn>
              <v-btn
                type="button"
                @click="exportClaims"
                variant="flat"
                :color="$vuetify.theme.currentTheme.primary"
                class="ml-3 white--text"
                v-show="isFeatureVisible('DOWNLOAD_CLAIM_REPORTS')"
              >
                Export Claims
              </v-btn>
            </v-col>
          </v-row>
        </div>
        <v-tabs vertical v-if="groupedTransactions" class="rounded-md" :color="$vuetify.theme.currentTheme.primary">
          <v-tab left v-for="(transaction, i) in transactions" :key="i">
            <div class="trans-brief-info">
              <div class="header" @click="selectTransaction(i)">
                <p>
                  <span>
                    {{
                      groupedTransactions[transaction][0].claimant_first_name
                    }}
                    {{ groupedTransactions[transaction][0].claimant_last_name }}
                  </span>
                  <v-chip class="ma-2" color="green" text-color="white">
                    {{ groupedTransactions[transaction]?.length }}
                  </v-chip>
                </p>
                <p class="tran-id">
                  <span>{{
                    groupedTransactions[transaction][0].claimant_email
                  }}</span>
                </p>
                <p class="tran-id">
                  <span>{{
                    groupedTransactions[transaction][0].claimant_mobile
                  }}</span>
                </p>
              </div>
            </div>
          </v-tab>
          <v-tab-item v-for="(transaction, index) in transactions" :key="index">
            <v-card flat class="dark-bg-custom">
              <v-card-text>
                <ClaimItem
                  v-for="transactionData in groupedTransactions[transaction]"
                  :claimData="transactionData"
                  :key="transactionData?.claim_id"
                />
              </v-card-text>
            </v-card>

            <ClaimPrint
              :printData="groupedTransactions[transaction]"
              ref="claimPrint"
            />
          </v-tab-item>
        </v-tabs>
        <div class="no-data-message" v-else>
          No Transaction for selected filters. Try adjusting filters
        </div>
      </v-card>
    </div>
    <div
      v-else
      class="absolute w-full h-screen flex-col items-center flex justify-center"
    >
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
  </div>
</template>

<script>
import groupBy from "lodash/groupBy";
import { downloadExcel, convertToTitleCase } from "@/utils/common";
import { isModuleFeatureAllowed } from "@/utils/common";
import ClaimItem from "@/components/ClaimItem.vue";
import ClaimPrint from "@/components/ClaimPrint.vue";
export default {
  layout: "Workforce/default",
  components: {
    ClaimItem,
    ClaimPrint
  },
  data() {
    return {
      dates: [this.getFirstDayOfMonth(), this.getCurrrentDateOfMonth()],
      isRange: false,
      isFilterEnabled: true,
      user: this.$storage.getUniversal("user"),
      selectedStatus: "NEW",
      printData: null,
      claimPrint: null,
      selectedTransactionIndex: 0,
      transactionStatues: [
        "ALL",
        "NEW",
        "RESUBMITTED",
        "APPROVED",
        "REJECTED",
        "ON_HOLD",
        "RETURNED",
        "REOPENED",
        "CLOSED",
      ],
      groupedTransactions: null,
      isLoad: true,
    };
  },
  mounted() {
    this.getClaims();
    this.isLoad = false;
  },
  computed: {
    dateRangeText() {
      return this.dates.join(" ~ ");
    },
    transactions() {
        return this.groupedTransactions && Object.keys(this.groupedTransactions);
    },
  },
  methods: {
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed("CLAIM_MANAGEMENT", feature);
    },
    getFirstDayOfMonth() {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth();
      return this.$dayjs(new Date(year, month, 1)).format("YYYY-MM-DD");
    },
    getCurrrentDateOfMonth() {
      return this.$dayjs(new Date(Date.now())).format("YYYY-MM-DD");
    },
    disabledAfterToday(date) {
      return this.$dayjs().isAfter(this.$dayjs(date));
    },

    async getClaims() {
      const start = this.dates[0];
      const end = this.dates[1];
      if (!this.$dayjs(end).isAfter(start)) {
        return this.$toast.error(
          "Start date cannot be before End date. Try Again adjusting your date filters."
        );
      }
      const uri = `/api/crpx/org/claims?claimstatus=${this.selectedStatus}&startdate=${start}&enddate=${end}`;
      try {
        const res = await this.$axios.get(uri);
        if (res.data?.status === true) {
          if (res.data?.claims?.length) {
            this.groupedTransactions = groupBy(res.data.claims, "claimant_id");
          } else {
            this.groupedTransactions = null;
          }
        } else {
          this.$toast.error(res.data.message);
        }
      } catch (error) {
        console.error(error);
        this.$toast.error(error.message);
      }
    },
    selectTransaction(index) {
      this.selectedTransactionIndex = index;
    },
    printClaim() {
      if (this.selectedTransactionIndex !== null) {
        this.$refs.claimPrint[
          this.selectedTransactionIndex
        ]?.openClaimDetailsPage();
      } else {
        console.error("No transaction selected.");
      }
    },

    prepareClaimData(dataCopy) {
      const extractEntries = dataCopy.flatMap((el) => el);
      return extractEntries.map((item, index) => {
        const lastDocUrl =
          item.claimDetails?.supplement?.[
            item.claimDetails.supplement.length - 1
          ]?.document[0].doc_url || "";
        return {
          "S.No": index + 1,
          Date: this.$dayjs(item.created_at).format("DD/MM/YYYY"),
          Name: `${item.claimant_first_name} ${item.claimant_last_name}`,
          "Claim Id": item.claim_ref_id,
          "Claim Amount(in Rs)": item.total_claim_amount,
          "Location From": item.from,
          "Location To": item.to,
          "Allowance Type": item.allowance
            ? convertToTitleCase(item.allowance?.split("-")[0])
            : "",
          "Allowance Amount(in Rs)": item.allowance?.split("-")[1],
          "Mode of Travel": item.mode_of_travel?.split("-")[0],
          "Travel Amount(in Rs)": item.mode_of_travel?.split("-")[1],
          "Total Distance": item.total_distance,
          "Claim Status": item.status,
          Attachment: lastDocUrl,
        };
      });
    },
    async exportClaims() {
      const transactions = Object.values(this.groupedTransactions);
      const dataCopy = [...transactions];
      await this.fetchAllClaimDetails(dataCopy);
      this.exportData(dataCopy);
    },
    exportData(dataCopy) {
      const restructuredData = this.prepareClaimData(dataCopy);
      const currentDate = this.$dayjs().format("DD/MM/YYYY");
      const fileName = `Corporate_Claim_${currentDate}.xlsx`;
      downloadExcel(restructuredData, "Sheet1", fileName);
    },
    async fetchClaimDetails(claimId) {
      try {
        const res = await this.$axios.get(
          `/api/crpx/org/claim?claimid=${claimId}`
        );
        if (res.data.status === true) {
          return res.data.claimDetails;
        } else {
          throw new Error(res.data.message || "Failed to fetch claim details");
        }
      } catch (error) {
        console.error(error);
      }
    },
    async fetchAllClaimDetails(dataCopy) {
      const fetchPromises = [];
      dataCopy.forEach((claimGroup) => {
        claimGroup.forEach((claimData) => {
          const fetchPromise = this.fetchClaimDetails(claimData.claim_id)
            .then((claimDetails) => {
              claimData.claimDetails = claimDetails;
            })
            .catch((error) => {
              console.error(
                `Error fetching claim details for claim ID ${claimData.claim_id}:`,
                error
              );
            });

          fetchPromises.push(fetchPromise);
        });
      });
      await Promise.all(fetchPromises);
    },
  },
};
</script>

<style scoped>
.close-filters-control {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.close-filters-control i {
  font-size: 30px !important;
}
.close-filters-control span {
  cursor: pointer;
}
.apply-filters-control {
  display: flex;
  justify-content: end;
  align-items: center;
}
.corp-exp {
  position: relative;
}
.corp-exp > .v-card {
  box-shadow: none;
  background-color: #f7fafb;
  border-radius: 10px;
}
header.corp-toolbar > div {
  display: initial !important;
}
.corp-exp .v-tabs--vertical > .v-tabs-bar .v-tab {
  height: 120px;
  padding: 0 8px;
  text-transform: inherit;
}
.corp-exp .v-tab > div.trans-brief-info {
  width: 100%;
}
.corp-exp .v-tab > div.trans-brief-info > div.header {
  text-align: left;
}
.corp-exp .v-tab > div.trans-brief-info > div.header > p {
  margin-bottom: 12px;
}
.corp-exp .v-tab > div.trans-brief-info > div.header > p:nth-child(1) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}
.transaction-data > div.header > div,
.corp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.transaction-data > div.header > div {
  justify-content: flex-end;
}
.corp-exp .v-tab > div.trans-brief-info > div.header .v-chip.v-size--default {
  border-radius: 16px;
  font-size: 12px;
  height: 25px;
  margin-left: 10px;
}
.corp-exp .v-tab > div.trans-brief-info .content,
.tran-id {
  text-align: left;
}
.transaction-data {
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
}
.tran-id > span:nth-child(2),
.claim-amt > span:nth-child(2) {
  font-weight: bold;
  color: #000;
}
.corp-toolbar .v-input {
  max-width: 200px !important;
  margin-top: 30px;
}

.advance-filters {
  z-index: 1;
  width: 100%;
  padding: 0 10px;
}
.take-action {
  color: #aaa;
  font-weight: 600;
}
.no-data-message {
  text-align: center;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main-div {
  display: grid;
  grid-template-columns: 300px;
  column-gap: 15px;
  grid-auto-flow: column;
}

.tab-div {
  grid-column-start: 1;
  height: 90vh;
}

.transaction-div {
  grid-column-start: 2;
  width: 99%;
}

.vItem {
  display: flex;
  flex-direction: column;
}

.submit {
  display: flex;
  padding: 20px;
  gap: 8px;
}

.show-transaction {
  display: flex;
  flex-direction: column;
  padding: 20px;
  margin-bottom: 40px;
}

.items-div {
  display: flex;
  margin-top: 10px;
  font-size: large;
  width: 100%;
}

.items-div p {
  width: 100%;
  padding: 4px;
  float: right;
}

.commenttag {
  font-size: 18px;
}
</style>
