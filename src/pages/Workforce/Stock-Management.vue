<template>
  <div>
    <ActitiveTabcomponent
      :tabs="tabItems"
      :defaultTab="activeTab"
      @tab-changed="handleTabChange"
      @button-click="handleButtonClick"
    >
      <template #tab-buttons>
        <div class="flex space-x-2">
          <v-btn
            v-for="button in currentTabButtons"
            :key="button.key"
            :color="button.color || 'primary'"
            :outlined="button.outlined || false"
            :class="button.class || ''"
            :disabled="button.disabled || false"
            @click="handleButtonClick(button)"
            v-show="button.visible !== false"
          >
            <v-icon v-if="button.icon" left>{{ button.icon }}</v-icon>
            {{ button.label }}
          </v-btn>
        </div>
      </template>
      <!-- Tab Content Slots -->
      <template v-for="tab in visibleTabs" v-slot:[tab.key]="">
        <div v-if="!isLoad">
          <v-main class="bg-[#F7FAFB] dark-bg-default" style="padding: 0px">
            <component
              :is="tab.component"
              :ref="tab.key"
              @buttons-updated="updateTabButtons"
              @button-click="handleComponentButtonClick"
            />
          </v-main>
        </div>
        <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </template>
    </ActitiveTabcomponent>
  </div>
</template>

<script>
import InventoryOrder from '~/components/Workforce/InventoryManagement/InventoryOrder.vue'
import InventoryStatus from '~/components/Workforce/InventoryManagement/InventoryStatus.vue'
import InventoryIn from '~/components/Workforce/InventoryManagement/InventoryIn.vue'
import InventoryOut from '~/components/Workforce/InventoryManagement/InventoryOut.vue'
import EmployeeInventory from '~/components/Workforce/InventoryManagement/EmployeeInventory.vue'
import OrderStatus from '~/components/Workforce/InventoryManagement/OrderStatus.vue'
import { isModuleFeatureAllowed, hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'

export default {
  name: 'InventoryManagement',
  layout: 'Workforce/default',
  components: {
    InventoryStatus,
    InventoryOrder,
    InventoryIn,
    InventoryOut,
    EmployeeInventory,
    OrderStatus,
    ActitiveTabcomponent
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      tabs: 0,
      isLoad: true,
      tabButtons: {}, // Store buttons for each tab
      buttonHandlers: {} // Store button handlers for each tab
    }
  },
  computed: {
    canRead() {
      return hasPermission(this.userData, permissionData.inventoryRead)
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.inventoryWrite)
    },
    tabItems() {
      const tabItems = [
        {
          key: 'InventoryOrder',
          label: 'Refill Orders',
          icon: 'mdi-cart-plus',
          component: 'InventoryOrder',
          visible: this.canRead && this.isFeatureVisible('REFILL_INVENTORY')
        },
        {
          key: 'InventoryStatus',
          label: 'Current Status',
          icon: 'mdi-clipboard-list',
          component: 'InventoryStatus',
          visible: this.canRead && this.isFeatureVisible('CHECK_CURRENT_STOCK')
        },
        {
          key: 'EmployeeInventory',
          label: 'Part Assignment',
          icon: 'mdi-account-hard-hat',
          component: 'EmployeeInventory',
          visible: this.canRead
        },
        {
          key: 'OrderStatus',
          label: 'Spare Part Consumed',
          icon: 'mdi-package-down',
          component: 'OrderStatus',
          visible: this.canRead
        },
        {
          key: 'InventoryIn',
          label: 'Part In',
          icon: 'mdi-arrow-down-box',
          component: 'InventoryIn',
          visible: this.canRead && this.isFeatureVisible('CHECK_PRODUCT_HISTORY')
        },
        {
          key: 'InventoryOut',
          label: 'Part Out',
          icon: 'mdi-arrow-up-box',
          component: 'InventoryOut',
          visible: this.canRead && this.isFeatureVisible('CHECK_PRODUCT_HISTORY')
        }
      ]
      return tabItems
    },
    visibleTabs() {
      const filtered = this.tabItems.filter((tab) => tab.visible)
      return filtered
    },
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.visibleTabs.length > 0 ? this.visibleTabs[0].key : 'InventoryOrder'
    },
    currentTabButtons() {
      // Return buttons for the currently active tab
      return this.tabButtons[this.activeTab] || []
    }
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
      // Check for buttons when tab changes
      this.$nextTick(() => {
        this.detectTabButtons(tabKey)
      })
    },

    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('STOCK_MANAGEMENT', feature)
    },

    // Dynamically detect buttons in tab components
    detectTabButtons(tabKey) {
      const component = this.$refs[tabKey]
      if (component && component[0]) {
        const componentInstance = component[0]

        if (componentInstance.getTabButtons) {
          const buttons = componentInstance.getTabButtons()
          this.$set(this.tabButtons, tabKey, buttons)
        } else {
          this.$set(this.tabButtons, tabKey, [])
        }
      }
    },

    getDefaultButtonsForTab(tabKey) {
      const defaultButtons = {
        InventoryOrder: [
          {
            key: 'refill-inventory',
            label: 'Refill Inventory',
            icon: 'mdi-plus',
            color: 'primary',
            class: 'white--text',
            action: 'toggleshowRefillInventory',
            visible: this.canWrite
          }
        ],
        InventoryStatus: [
          {
            key: 'add-inventory',
            label: 'Add Inventory',
            icon: 'mdi-plus',
            color: 'primary',
            class: 'white--text',
            action: 'toggleshowAddInventory',
            visible: this.canWrite
          },
          {
            key: 'export-status',
            label: 'Export',
            icon: 'mdi-download',
            color: 'primary',
            class: 'white--text',
            action: 'exportProductDataToExcel',
            visible: this.canRead
          }
        ],
        EmployeeInventory: [
          {
            key: 'assign-part',
            label: 'Assign Part',
            icon: 'mdi-account-plus',
            color: 'primary',
            class: 'white--text',
            action: 'showAssignPartDialog',
            visible: this.canWrite
          }
        ],
        OrderStatus: [
          {
            key: 'export-consumed',
            label: 'Export',
            icon: 'mdi-download',
            color: 'primary',
            class: 'white--text',
            action: 'exportConsumedData',
            visible: this.canRead
          }
        ],
        InventoryIn: [
          {
            key: 'add-part-in',
            label: 'Add Part In',
            icon: 'mdi-plus',
            color: 'primary',
            class: 'white--text',
            action: 'showAddPartInDialog',
            visible: this.canWrite
          }
        ],
        InventoryOut: [
          {
            key: 'add-part-out',
            label: 'Add Part Out',
            icon: 'mdi-plus',
            color: 'primary',
            class: 'white--text',
            action: 'showAddPartOutDialog',
            visible: this.canWrite
          }
        ]
      }

      return defaultButtons[tabKey] || []
    },

    // Handle button clicks from the filter-button slot
    handleButtonClick(button) {
      console.log(`Button clicked: ${button.key}`, button)

      // First try to call the method on the current tab component
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0][button.action]) {
        component[0][button.action]()
        return
      }

      // If not found in component, try local methods
      if (button.action && this[button.action]) {
        this[button.action](button)
        return
      }

      // Emit event for external handling
      this.$emit(`button-click`, { button, tabKey: this.activeTab })
    },

    // Handle button clicks from child components
    handleComponentButtonClick(buttonData) {
      console.log('Component button click:', buttonData)
      this.$emit('component-button-click', buttonData)
    },

    // Update tab buttons from child components
    updateTabButtons(tabKey, buttons) {
      this.$set(this.tabButtons, tabKey, buttons)
    },

    // Local button action methods (fallbacks)
    toggleshowRefillInventory() {
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0].toggleshowRefillInventory) {
        component[0].toggleshowRefillInventory()
      }
    },

    toggleshowAddInventory() {
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0].toggleshowAddInventory) {
        component[0].toggleshowAddInventory()
      }
    },

    exportProductDataToExcel() {
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0].exportProductDataToExcel) {
        component[0].exportProductDataToExcel()
      }
    },

    showAssignPartDialog() {
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0].showAssignPartDialog) {
        component[0].showAssignPartDialog()
      }
    },

    exportConsumedData() {
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0].exportConsumedData) {
        component[0].exportConsumedData()
      }
    },

    showAddPartInDialog() {
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0].showAddPartInDialog) {
        component[0].showAddPartInDialog()
      }
    },

    showAddPartOutDialog() {
      const component = this.$refs[this.activeTab]
      if (component && component[0] && component[0].showAddPartOutDialog) {
        component[0].showAddPartOutDialog()
      }
    }
  },

  mounted() {
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }

    // Remove loading state and detect initial tab buttons
    this.$nextTick(() => {
      this.isLoad = false
      this.$nextTick(() => {
        this.detectTabButtons(this.activeTab)
      })
    })
  }
}
</script>

<style scoped>
.v-tab:before {
  background-color: transparent;
}

.v-tab {
  font-size: 16px;
}

.v-btn {
  text-transform: none !important;
  font-size: 12px;
}
</style>
