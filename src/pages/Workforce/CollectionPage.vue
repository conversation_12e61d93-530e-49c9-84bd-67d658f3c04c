<template>
  <div>
    <ActitiveTabcomponent
      :tabs="filteredTabItems"
      :defaultTab="activeTab"
      @tab-changed="handleTabChange"
    >
      <!-- Tab buttons slot for filters and actions -->
      <template #tab-buttons="{ activeTab }">
        <!-- Case table actions -->
        <div
          v-if="activeTab === 'Cases' && caseTableRef"
          class="flex items-center justify-end space-x-2"
        >
          <!-- Search inputs -->
          <div class="custom-input-group" v-if="showSearch">
            <v-select
              :items="caseTableRef.selectItems || []"
              v-model="caseTableRef.selectValue"
              solo
              flat
              dense
              hide-details
              outlined
              class="select-input"
              label="Select"
              :style="{ width: '150px' }"
              @change="caseTableRef.handleSelect"
            />
            <v-text-field
              class="text-input"
              v-model="caseTableRef.filterCase"
              :label="caseTableRef.searchPlaceholder"
              :placeholder="caseTableRef.searchPlaceholder"
              hide-details
              solo
              flat
              dense
              clearable
              outlined
              :style="{ width: '300px' }"
              @input="caseTableRef.handleInput"
            ></v-text-field>
          </div>

          <!-- <v-text-field
            v-model="caseTableRef.filterCustomer"
            :label="`Search by ${caseTableRef.tabText?.customerTable?.displayName || 'Customer'}`"
            :placeholder="`Search by ${
              caseTableRef.tabText?.customerTable?.displayName || 'Customer'
            }`"
            hide-details
            solo
            flat
            dense
            clearable
            outlined
            prepend-inner-icon="mdi-magnify"
            @input="caseTableRef.handleInput"
          ></v-text-field> -->

          <!-- Add Case Button -->
          <v-btn
            @click="caseTableRef.addNewCase"
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            v-show="caseTableRef.canWrite"
          >
            <v-icon left dark>mdi-plus</v-icon>
            Add
          </v-btn>
          <v-btn
            @click="caseTableRef.handleExportKeys"
            :loading="caseTableRef.exportLoading"
            :disabled="caseTableRef.exportLoading"
            :color="$vuetify.theme.currentTheme.primary"
            v-show="caseTableRef.canEdit"
            class="white--text"
          >
            Export Data
            <v-icon right dark>mdi-download</v-icon>
          </v-btn>

          <!-- Filter PopOver -->
          <FilterPopOver class="p-2" :selectedCount="caseSelectedCount">
            <v-card outlined class="w-72 max-h-96 py-2">
              <v-list>
                <v-list-item class="px-0" v-if="!caseTableRef.isLoanManagement">
                  <v-col class="py-0">
                    <v-autocomplete
                      v-model="caseTableRef.filterAssignedTo"
                      label="Select Assignee"
                      placeholder="Select Assignee"
                      :items="caseTableRef.users"
                      item-text="fullName"
                      item-value="_id"
                      outlined
                      hide-details
                      dense
                      class="mb-1"
                      clearable
                      @change="caseTableRef.handleChange"
                      @keydown.enter="
                        caseTableRef.filterAssignedTo = caseTableRef.setDataOnEnter(
                          caseTableRef.users
                        )
                      "
                    >
                      <template v-slot:prepend-item>
                        <v-list-item
                          ripple
                          @mousedown.prevent
                          @click="caseTableRef.handleUnassignedCases"
                          class="unassignedOptions"
                        >
                          <v-list-item-content>
                            <v-list-item-title class="py-0"> Unassigned </v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                        <v-divider class=""></v-divider>
                      </template>
                    </v-autocomplete>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="!caseTableRef.isLoanManagement">
                  <v-col class="py-0">
                    <v-select
                      v-model="caseTableRef.filterCaseOutcome"
                      label="Select Outcome"
                      placeholder="Select Outcome"
                      :items="caseTableRef.userDefinedOutcomeOptions"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="caseTableRef.handleNoOutcome"
                      @click:clear="caseTableRef.clearOutcomeFilter"
                    >
                    </v-select>
                  </v-col>
                </v-list-item>
                <v-list-item
                  class="px-0"
                  v-if="caseTableRef.branches && caseTableRef.branches.length"
                  v-show="caseTableRef.isBranchFeatureVisible('ASSIGN_BRANCH_TO_ENTITY')"
                >
                  <v-col class="py-0">
                    <v-select
                      v-model="caseTableRef.filterBranch"
                      label="Select Branch"
                      placeholder="Select Branch"
                      :items="caseTableRef.branches"
                      item-text="branchName"
                      item-value="_id"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="caseTableRef.handleChange"
                    ></v-select>
                  </v-col>
                </v-list-item>
                <v-list-item
                  class="px-0"
                  v-if="caseTableRef.teams && caseTableRef.teams.length"
                  v-show="caseTableRef.isBranchFeatureVisible('ASSIGN_BRANCH_TO_ENTITY')"
                >
                  <v-col class="py-0">
                    <v-select
                      v-model="caseTableRef.filterTeam"
                      label="Select Team"
                      placeholder="Select Team"
                      :items="caseTableRef.teams"
                      item-text="teamName"
                      item-value="_id"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="caseTableRef.handleChange"
                    ></v-select>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="caseTableRef.isEntityEnabled">
                  <v-col class="py-0">
                    <v-autocomplete
                      v-model="caseTableRef.entityId"
                      :label="`Select ${caseTableRef.entityTitle}`"
                      :placeholder="`Select ${caseTableRef.entityTitle}`"
                      @update:search-input="caseTableRef.getAllEntities"
                      :search-input.sync="caseTableRef.searchValue"
                      :items="caseTableRef.entities"
                      item-text="entityName"
                      item-value="_id"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="caseTableRef.handleChange"
                    ></v-autocomplete>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="caseTableRef.isLoanManagement">
                  <v-col class="py-0">
                    <v-autocomplete
                      v-model="caseTableRef.filterPartner"
                      label="Select Partner/Lender"
                      placeholder="Select Partner/Lender"
                      @update:search-input="caseTableRef.getAllPartners"
                      :search-input.sync="caseTableRef.searchPartnerValue"
                      :items="caseTableRef.partners"
                      item-text="partnerName"
                      item-value="_id"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="caseTableRef.handleChange"
                    ></v-autocomplete>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="!caseTableRef.isLoanManagement">
                  <v-col class="py-0">
                    <v-autocomplete
                      v-model="caseTableRef.filterStatus"
                      label="Select Status"
                      placeholder="Select Status"
                      :items="caseTableRef.caseStatusOptions"
                      item-text="label"
                      item-value="value"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="caseTableRef.handleChange"
                    ></v-autocomplete>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0">
                  <v-col class="py-0">
                    <v-menu
                      v-model="caseTableRef.menu"
                      :close-on-content-click="false"
                      transition="scale-transition"
                      offset-y
                    >
                      <template v-slot:activator="{ on }">
                        <v-text-field
                          label="Select Date Range"
                          placeholder="Select Date Range"
                          v-model="caseTableRef.formattedDate"
                          prepend-inner-icon="mdi-calendar"
                          readonly
                          hide-details
                          outlined
                          dense
                          clearable
                          v-on="on"
                          @click:clear="caseTableRef.onDateCleared"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-model="caseTableRef.dates"
                        @input="caseTableRef.onDateSelected"
                        no-title
                        @change="caseTableRef.menu = false"
                        range
                        scrollable
                      />
                    </v-menu>
                  </v-col>
                </v-list-item>
              </v-list>
            </v-card>
          </FilterPopOver>

          <!-- More actions menu -->
          <!-- <v-menu offset-y>

            <template v-slot:activator="{ on }">
              <v-btn icon v-on="on">
                <v-icon size="28px" color="grey">mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                :color="$vuetify.theme.currentTheme.primary"
                v-show="caseTableRef.canWrite"
                @click="caseTableRef.uploadCases('Create')"
                :disabled="caseTableRef.isLoanManagement"
              >
                Bulk Create {{ caseTableRef.tabText?.caseTable?.displayName || 'Cases' }}
              </v-list-item>
              <v-list-item
                :color="$vuetify.theme.currentTheme.primary"
                @click="caseTableRef.uploadCases('Update')"
                v-show="caseTableRef.canEdit"
                :disabled="caseTableRef.isLoanManagement"
              >
                Bulk Update {{ caseTableRef.tabText?.caseTable?.displayName || 'Cases' }}
              </v-list-item>
              <v-list-item
                :color="$vuetify.theme.currentTheme.primary"
                v-show="caseTableRef.canWrite && caseTableRef.isLoanManagement"
                @click="caseTableRef.openLoanBulkUpload"
              >
                Bulk Create Loans
              </v-list-item>
              <v-list-item
                @click="caseTableRef.handleExportKeys"
                :loading="caseTableRef.exportLoading"
                :disabled="caseTableRef.exportLoading"
                :color="$vuetify.theme.currentTheme.primary"
                v-show="caseTableRef.canEdit"
              >
                Export Data
                <v-icon right dark>mdi-download</v-icon>
              </v-list-item>
              <v-list-item
                @click="caseTableRef.handleShowDialog('status')"
                :disabled="!caseTableRef.selected.length"
                v-show="caseTableRef.canEdit"
                v-if="!caseTableRef.isLoanManagement"
              >
                <v-list-item-title>Update FollowUp Outcome</v-list-item-title>
              </v-list-item>
              <v-list-item
                @click="caseTableRef.handleShowDialog('FEName')"
                :disabled="!caseTableRef.selected.length"
                v-show="caseTableRef.canEdit"
                v-if="!caseTableRef.isLoanManagement"
              >
                <v-list-item-title>Assign Selected Cases</v-list-item-title>
              </v-list-item>
              <v-list-item
                @click="caseTableRef.handleShowDialog('Branches')"
                :disabled="!caseTableRef.selected.length"
                v-show="
                  caseTableRef.isBranchFeatureVisible('ASSIGN_BRANCH_TO_ENTITY') &&
                  caseTableRef.canWrite
                "
                v-if="!caseTableRef.isLoanManagement"
              >
                <v-list-item-title>Assign Branch</v-list-item-title>
              </v-list-item>
              <v-list-item
                @click="caseTableRef.handleShowDialogDelete('delete')"
                :disabled="!caseTableRef.selected.length"
                v-show="caseTableRef.canDelete"
                v-if="!caseTableRef.isLoanManagement"
              >
                <v-list-item-title>Delete Selected Cases</v-list-item-title>
              </v-list-item>
              <v-list-item
                @click="caseTableRef.handleShowDialogDeleteAll('deleteAll')"
                :disabled="!caseTableRef.cases.length"
                v-show="caseTableRef.canDelete"
              >
                <v-list-item-title>Delete All Data</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu> -->
          <div>
            <v-btn @click="showSearch = !showSearch" icon>
              <v-icon left>mdi-magnify</v-icon>
            </v-btn>
          </div>
        </div>

        <!-- Visit table actions -->
        <div
          v-else-if="activeTab === 'Visits' && visitTableRef"
          class="flex items-center justify-end space-x-2"
        >
          <!-- Search input -->
          <v-text-field
            v-model="visitTableRef.filter"
            :label="`Search by ${visitTableRef.caseConfig.fieldMappings?.taskTitle.displayName}`"
            :placeholder="`Search by ${visitTableRef.caseConfig.fieldMappings?.taskTitle.displayName}`"
            hide-details
            solo
            flat
            dense
            clearable
            outlined
            prepend-inner-icon="mdi-magnify"
            @input="visitTableRef.handleInput"
          ></v-text-field>

          <!-- Export Button -->
          <v-btn
            @click="visitTableRef.openExportDialog"
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            v-tooltip="{
              text: `Export ${
                visitTableRef.isLoanPage
                  ? 'EMI Details'
                  : visitTableRef.caseConfig.tableTabMapping?.visitTable?.displayName || 'Visits'
              }`
            }"
            :disabled="visitTableRef.isExportDisabled"
            :loading="visitTableRef.exportEmi"
            v-show="visitTableRef.canEdit"
          >
            Export
            {{
              visitTableRef.isLoanPage
                ? 'EMI Details'
                : visitTableRef.caseConfig.tableTabMapping?.visitTable?.displayName || 'Visits'
            }}
          </v-btn>

          <!-- Bulk Update Button for Loan Page -->
          <v-btn
            v-if="visitTableRef.isLoanPage"
            @click="visitTableRef.openBulkUploadDialog"
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            v-tooltip="{
              text: 'Bulk Update EMI Status via Excel'
            }"
            v-show="visitTableRef.canEdit"
          >
            <v-icon left>mdi-upload-multiple</v-icon>
            Bulk Update
          </v-btn>

          <!-- Filter PopOver -->
          <FilterPopOver class="p-2" :selectedCount="visitSelectedCount">
            <v-card outlined class="w-72 max-h-96 py-2">
              <v-list>
                <v-list-item class="px-0" v-if="!visitTableRef.isLoanPage">
                  <v-col class="py-0">
                    <v-autocomplete
                      v-model="visitTableRef.filterAssignedTo"
                      label="Select Assignee"
                      placeholder="Select Assignee"
                      :items="visitTableRef.users"
                      item-text="fullName"
                      item-value="_id"
                      hide-details
                      class="mb-1"
                      outlined
                      dense
                      clearable
                      @change="visitTableRef.handleChange"
                    >
                      <template v-slot:prepend-item>
                        <v-list-item
                          ripple
                          @mousedown.prevent
                          @click="visitTableRef.handleUnassignedCases"
                          class="unassignedOptions"
                        >
                          <v-list-item-content>
                            <v-list-item-title class="py-0"> Unassigned </v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                        <v-divider class=""></v-divider>
                      </template>
                    </v-autocomplete>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="!visitTableRef.isLoanPage">
                  <v-col class="py-0">
                    <v-select
                      v-model="visitTableRef.visitStatus"
                      label="Select Status"
                      placeholder="Select Status"
                      :items="visitTableRef.filteredTaskStatus"
                      item-text="label"
                      item-value="value"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="visitTableRef.handleChange"
                    ></v-select>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0" v-if="!visitTableRef.isLoanPage">
                  <v-col class="py-0">
                    <v-menu
                      v-model="visitTableRef.visitMenu"
                      :close-on-content-click="false"
                      transition="scale-transition"
                      offset-y
                    >
                      <template v-slot:activator="{ on }">
                        <v-text-field
                          label="Scheduled Visit Date"
                          placeholder="Scheduled Visit Date"
                          v-model="visitTableRef.visitDate"
                          prepend-inner-icon="mdi-calendar"
                          readonly
                          hide-details
                          outlined
                          dense
                          clearable
                          v-on="on"
                          @click:clear="visitTableRef.onVisitCleared"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-model="visitTableRef.selectedVisitDate"
                        @input="visitTableRef.onVisitSelected"
                        no-title
                        @change="visitTableRef.visitMenu = false"
                        scrollable
                      />
                    </v-menu>
                  </v-col>
                </v-list-item>
                <v-list-item class="px-0">
                  <v-form
                    ref="visitForm"
                    v-model="visitTableRef.valid"
                    lazy-validation
                    class="w-full"
                  >
                    <v-col class="py-0">
                      <v-menu
                        v-model="visitTableRef.menu"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                      >
                        <template v-slot:activator="{ on }">
                          <v-text-field
                            label="Select Date Range"
                            placeholder="Select Date Range"
                            v-model="visitTableRef.formattedDate"
                            prepend-inner-icon="mdi-calendar"
                            readonly
                            hide-details
                            outlined
                            dense
                            clearable
                            v-on="on"
                            @click:clear="visitTableRef.onDateCleared"
                            :rules="visitTableRef.rules.dateRule"
                          ></v-text-field>
                        </template>
                        <v-date-picker
                          v-model="visitTableRef.dates"
                          @input="visitTableRef.onDateSelected"
                          no-title
                          @change="visitTableRef.menu = false"
                          range
                          scrollable
                        />
                      </v-menu>
                    </v-col>
                  </v-form>
                </v-list-item>
                <v-list-item class="px-0" v-if="visitTableRef.isLoanPage">
                  <v-col class="py-0">
                    <v-select
                      v-model="visitTableRef.isStepsCompleted"
                      label="Select PDD Status"
                      placeholder="Select PDD Status"
                      :items="[
                        { label: 'Completed', value: 'true' },
                        { label: 'Not Completed', value: 'false' }
                      ]"
                      item-text="label"
                      item-value="value"
                      hide-details
                      outlined
                      dense
                      clearable
                      @change="visitTableRef.handleChange"
                    ></v-select>
                  </v-col>
                </v-list-item>
              </v-list>
            </v-card>
          </FilterPopOver>
        </div>

        <!-- Call History actions -->
        <div
          v-else-if="activeTab === 'CallHistory' && callHistoryRef"
          class="flex items-center justify-end space-x-2"
        >
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            @click="callHistoryRef.exportToXLSX"
            :loading="callHistoryRef.exportBtnLoad"
            v-tooltip="{
              text: 'Export Call History'
            }"
          >
            Export Call History
            <v-icon right dark>mdi-download</v-icon>
          </v-btn>

          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            @click="callHistoryRef.handleRefresh"
            class="white--text"
          >
            Refresh Call History
          </v-btn>
        </div>

        <!-- Case Analytics actions -->
        <div
          v-else-if="activeTab === 'CaseAnalytics' && caseAnalyticsRef"
          class="flex items-center justify-end space-x-2"
        >
          <v-btn
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
            v-tooltip="{
              text: 'Export Analytics'
            }"
            v-show="caseAnalyticsRef.canEdit"
            @click="caseAnalyticsRef.exportToXLSX"
          >
            Export Analytics
            <v-icon right dark>mdi-download</v-icon>
          </v-btn>
        </div>

        <!-- Customer actions -->
        <div
          v-else-if="activeTab === 'CaseCustomer' && customerRef"
          class="flex items-center justify-end space-x-2"
        >
          <v-btn
            @click="customerRef.addNewCustomer"
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
          >
            <v-icon left dark>mdi-plus</v-icon>
            Add Customer
          </v-btn>

          <v-btn
            @click="customerRef.bulkCustomer"
            :color="$vuetify.theme.currentTheme.primary"
            class="white--text"
          >
            Bulk Upload
            <v-icon right dark>mdi-upload</v-icon>
          </v-btn>

          <!-- More actions menu -->
          <v-menu offset-y>
            <template v-slot:activator="{ on }">
              <v-btn icon v-on="on">
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                @click="customerRef.handleShowDialogDelete()"
                :disabled="!customerRef.selected.length"
              >
                <v-list-item-title>Bulk Delete</v-list-item-title>
              </v-list-item>
              <v-list-item @click="customerRef.exportToExcel()">
                <v-list-item-title>Export to Excel</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </template>
      <template v-for="tab in filteredTabItems" v-slot:[tab.key]="">
        <div v-if="!isLoad">
          <v-main class="bg-[#F7FAFB] dark-bg-default" style="padding: 0px">
            <div
              class="flex items-center justify-end pr-4 py-2 bg-white border-b"
              v-if="tab.key === 'CallHistory'"
            >
              <div class="text-orange-600 mr-4">
                <span> Total Calls: {{ totalCalls }} </span>
              </div>
              <div class="border-l border-gray-300 h-6 mx-4"></div>
              <div class="text-green-600">
                <span> Connected Calls: {{ connectedCalls }} </span>
              </div>
            </div>
            <component
              :is="tab.component"
              :ref="getComponentRef(tab.key)"
              :caseConfig="caseConfig"
              :employees="employees"
              :orgConfig="orgConfig"
              :entities="entities"
              @retry-call="openDialerPopOver"
              @callStats="callStats"
              @component-mounted="handleComponentMounted"
              @mounted="updateComponentRefs"
            ></component>
          </v-main>
        </div>
        <div
          v-else
          class="absolute w-full h-screen flex-col items-center flex justify-center"
          :key="`loading-${tab.key}`"
        >
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </template>
    </ActitiveTabcomponent>
    <DialerPopOver
      v-if="showDialer"
      :callItem="callItem"
      @handleCloseDialer="closeDialerPopOver"
      class="absolute top-0 left-0"
    />
  </div>
</template>

<script>
import Cases from '@/components/Workforce/CollectionManagement/CaseModules/CaseTable.vue'
import Visits from '@/components/Workforce/CollectionManagement/VisitModules/VisitTable.vue'
import CaseAnalytics from '@/components/Workforce/CollectionManagement/CaseModules/CaseAnalytics.vue'
import CallHistory from '@/components/Workforce/CollectionManagement/CaseModules/CallHistory.vue'
import Customer from './CollectionCustomer.vue'

import { isModuleFeatureAllowed } from '@/utils/common'
import DialerPopOver from '@/components/DialerPopOver.vue'
import { TAB_ITEMS } from '@/utils/workforce/constants'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'
import FilterPopOver from '@/components/FilterPopOver.vue'

export default {
  name: 'CollectionPage',
  layout: 'Workforce/default',
  components: {
    Cases,
    Visits,
    CaseAnalytics,
    CallHistory,
    DialerPopOver,
    Customer,
    ActitiveTabcomponent,
    FilterPopOver
  },
  data() {
    return {
      userData: this.$storage.getUniversal('user'),
      caseConfig: null,
      orgConfig: null,
      employees: [],
      isLoad: true,
      showDialer: false,
      showSearch: false,
      callItem: null,
      connectedCalls: 0,
      totalCalls: 0,
      entities: [],
      caseTableRef: null,
      visitTableRef: null,
      callHistoryRef: null,
      caseAnalyticsRef: null,
      customerRef: null
    }
  },
  computed: {
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.filteredTabItems.length > 0 ? this.filteredTabItems[0].key : 'Cases'
    },
    filteredTabItems() {
      return this.tabItems.filter((tab) => {
        if (tab.key === 'CaseAnalytics') {
          return (
            this.isFeatureVisible('DASHBOARD', 'SHOW_CASE_ANALYTICS') &&
            !this.caseConfig?.loanManagementFieldMapping
          )
        }
        if (tab.key === 'CallHistory') {
          return (
            this.isFeatureVisible('ADDONS', 'CALL_A_CUSTOMER') &&
            !this.caseConfig?.loanManagementFieldMapping
          )
        }
        return true
      })
    },
    tabItems() {
      return TAB_ITEMS.COLLECTION.map((tab) => ({
        ...tab,
        label: this.caseConfig?.tableTabMapping?.[tab.nameKey]?.displayName || tab.name,
        icon: tab.icon || 'mdi-view-dashboard'
      }))
    },
    caseSelectedCount() {
      if (!this.caseTableRef) return 0
      let count = 0
      if (this.caseTableRef.filterAssignedTo) count++
      if (this.caseTableRef.filterCaseOutcome) count++
      if (this.caseTableRef.filterStatus) count++
      if (this.caseTableRef.dates && this.caseTableRef.dates.length > 0) count++
      if (this.caseTableRef.entityId) count++
      if (this.caseTableRef.filterPartner) count++
      return count
    },
    visitSelectedCount() {
      if (!this.visitTableRef) return 0
      let count = 0
      if (this.visitTableRef.filterAssignedTo) count++
      if (this.visitTableRef.visitStatus) count++
      if (this.visitTableRef.selectedVisitDate) count++
      if (this.visitTableRef.dates && this.visitTableRef.dates.length > 0) count++
      if (this.visitTableRef.isStepsCompleted !== null) count++
      return count
    }
  },
  watch: {
    '$route.params.subCase': async function (newQuery) {
      if (newQuery) {
        await this.refetchData()
      }
    },
    activeTab: {
      handler() {
        this.$nextTick(() => {
          this.updateComponentRefs()
        })
      },
      immediate: true
    },
    // Watch for changes in component references
    caseTableRef: {
      handler() {
        this.$forceUpdate()
      },
      deep: true
    },
    visitTableRef: {
      handler() {
        this.$forceUpdate()
      },
      deep: true
    },
    callHistoryRef: {
      handler() {
        this.$forceUpdate()
      },
      deep: true
    },
    caseAnalyticsRef: {
      handler() {
        this.$forceUpdate()
      },
      deep: true
    },
    customerRef: {
      handler() {
        this.$forceUpdate()
      },
      deep: true
    }
  },
  async mounted() {
    await this.refetchData()
    this.$nextTick(() => {
      this.updateComponentRefs()
    })
  },
  async updated() {
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
      // Update component refs when tab changes
      this.$nextTick(() => {
        this.updateComponentRefs()
      })
    },
    updateComponentRefs() {
      const refs = {
        caseTable: this.$refs.caseTable,
        visitTable: this.$refs.visitTable,
        callHistory: this.$refs.callHistory,
        caseAnalytics: this.$refs.caseAnalytics,
        customer: this.$refs.customer
      }

      Object.keys(refs).forEach((key) => {
        if (refs[key]) {
          const ref = Array.isArray(refs[key]) ? refs[key][0] : refs[key]
          this[`${key}Ref`] = ref
        }
      })
    },
    openDialerPopOver(item) {
      this.callItem = item
      this.showDialer = true
    },
    closeDialerPopOver() {
      this.showDialer = false
      this.callId = null
    },
    callStats(val) {
      if (val) {
        this.totalCalls = val.totalCalls
        this.connectedCalls = val.result
      } else {
        this.totalCalls = 0
        this.connectedCalls = 0
      }
    },
    // async fetchEntities() {
    //   try {
    //     const response = await this.$axios.get(
    //       "/workforce/collection/entities"
    //     );
    //     this.entities = response?.data?.entities;
    //   } catch (error) {
    //     console.log(error);
    //   }
    // },
    async fetchUseCaseById(id) {
      try {
        const res = await this.$axios.get(`/workforce/orgSetup/extend-usecase/${id}`)
        return res.data?.data
      } catch (error) {
        console.log(error)
      }
    },
    async refetchData() {
      await this.fetchUsers()
      const config = await this.getConfig()
      // await this.fetchEntities();
      const subCaseId = this.$route.params.subCase
      let caseConfig

      config.enabledSubCases.forEach((scItem) => {
        caseConfig = scItem.subCase.find((sc) => sc._id === subCaseId)
      })
      if (!caseConfig) {
        const useCaseGroups = await this.fetchUseCaseById(subCaseId)
        caseConfig = useCaseGroups
        caseConfig.fieldMappings = {
          ...caseConfig.baseFieldMappings,
          ...caseConfig?.loanManagementFieldMapping,
          ...caseConfig?.customerFieldMappings
        }
      }
      // console.log(caseConfig)
      this.orgConfig = config
      this.caseConfig = caseConfig
      this.tabs = 0
      this.isLoad = false
    },
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config
        return data || {}
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async fetchUsers() {
      try {
        const params = {
          limit: '*',
          sortBy: 'firstName',
          sortOrder: 'asc'
        }
        if (this.userData?.branch && !this.userData?.role?.includes('WFM_ADMIN')) {
          params.branch = this.userData?.branch
        }
        if (this.caseConfig?.branch) {
          params.branch = this.caseConfig?.branch
        }
        const response = await this.$axios.get('/workforce/v2/users', {
          params
        })
        this.employees = response.data?.users
      } catch (error) {
        console.log(error)
      }
    },
    isFeatureVisible(module, feature) {
      return isModuleFeatureAllowed(module, feature)
    },
    getComponentRef(tabKey) {
      const refMap = {
        Cases: 'caseTable',
        Visits: 'visitTable',
        CallHistory: 'callHistory',
        CaseAnalytics: 'caseAnalytics',
        CaseCustomer: 'customer'
      }
      return refMap[tabKey] || null
    },
    handleComponentMounted(componentName) {
      this.$nextTick(() => {
        // Get references to all child components
        const refs = {
          caseTable: this.$refs.caseTable,
          visitTable: this.$refs.visitTable,
          callHistory: this.$refs.callHistory,
          caseAnalytics: this.$refs.caseAnalytics,
          customer: this.$refs.customer
        }

        // Update all refs
        Object.keys(refs).forEach((key) => {
          if (refs[key]) {
            const ref = Array.isArray(refs[key]) ? refs[key][0] : refs[key]
            this[`${key}Ref`] = ref
          }
        })

        // Force update to trigger reactivity
        this.$forceUpdate()
      })
    }
  }
}
</script>

<style scoped>
.v-tab:before {
  background-color: transparent;
}

.v-tab {
  font-size: 16px;
}

.custom-input-group {
  display: flex;
  /* gap: 8px; */
}

.custom-input-group .select-input,
.custom-input-group .text-input {
  flex-shrink: 0;
}
</style>
