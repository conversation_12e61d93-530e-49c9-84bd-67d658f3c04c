<template>
  <div>
    <div v-if="!isLoad">
      <!-- <v-app> -->
      <v-main class="bg-[#F7FAFB] dark-bg-default" style="padding: 0px">
        <v-col>
          <Configurations />
        </v-col>
      </v-main>
    </div>
    <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
  </div>
</template>

<script>
import Configurations from '~/components/Workforce/Settings/Configurations.vue'

export default {
  name: 'SettingsPage',
  layout: 'Workforce/default',
  components: {
    Configurations
  },
  data() {
    return {
      isLoad: true
    }
  },
  methods: {},
  mounted() {
    this.isLoad = false
  }
}
</script>

<style scoped></style>
