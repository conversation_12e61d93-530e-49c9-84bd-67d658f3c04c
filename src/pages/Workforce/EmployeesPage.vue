<template>
  <div>
    <div v-if="!isLoad" class="py-2">
      <div class="overflow-x-auto overflow-y-auto">
        <v-data-table
          v-model="selected"
          :headers="tableHeaders"
          :items="employees"
          v-if="employees"
          class="rounded-lg mt-2"
          item-key="_id"
          :options.sync="options"
          :server-items-length="totalEmployees"
          :footer-props="{ itemsPerPageOptions: [5, 9, 10, 15, 30] }"
          :loading="isLoading"
          :row-class="rowClass"
          show-select
        >
          <template v-slot:item.firstName="{ item }">
            <td class="px-4 py-5 td_font">
              <employee-info :item="item" view="employeeTable" />
            </td>
            <td v-if="item.isOwner">
              <span
                class="text-[12px] rounded-xl p-2"
                :class="{
                  'text-green-600 bg-green-100': !$vuetify.theme.dark,
                  'text-white': $vuetify.theme.dark
                }"
                :style="{
                  backgroundColor: $vuetify.theme.dark ? $vuetify.theme.currentTheme.secondary : ''
                }"
              >
                OWNER
              </span>
            </td>
          </template>

          <template v-slot:item.employeeId="{ item }">
            <td class="px-4 font-medium">{{ item.employeeId }}</td>
          </template>

          <template v-slot:item.mobile="{ item }">
            <td class="px-4 font-medium">{{ item.maskedMobileNumber }}</td>
          </template>

          <template v-slot:item.email="{ item }">
            <td class="px-4 font-medium">{{ item.maskedEmail }}</td>
          </template>
          <template v-slot:item.team="{ item }">
            <td class="px-4 font-medium">
              {{ item.team ? item.team?.teamName : '-' }}
            </td>
          </template>
          <template v-slot:item.branch="{ item }">
            <td class="px-4 font-medium">
              {{ item.branch ? findBranch(item).branchName : '-' }}
            </td>
          </template>
          <template v-slot:item.status="{ item }">
            <td class="px-4">{{ item.status }}</td>
          </template>

          <template v-slot:item.role="{ item }">
            <template>
              <td class="px-4">
                <v-select
                  hide-details
                  solo
                  :readonly="!canEdit"
                  flat
                  placeholder="No Role"
                  v-model="item.wfmRole"
                  :items="getRoleDropdown(item)"
                  item-text="role"
                  item-value="_id"
                  class="my-2 fixed-width-column"
                  :menu-props="{ offsetY: true }"
                  @change="updateRole(item)"
                  @click="newRoleId(item)"
                >
                  <template v-slot:selection="{ item }">
                    <v-tooltip top>
                      <template v-slot:activator="{ on }">
                        <div v-on="on" class="fixed-width-column">
                          {{ item.role }}
                        </div>
                      </template>
                      <span>{{ item.role }}</span>
                    </v-tooltip>
                  </template>
                </v-select>
              </td>
            </template>
          </template>

          <template v-slot:item.directManager="{ item }">
            <v-select
              hide-details
              solo
              flat
              placeholder="No Manager"
              :readonly="!canEdit"
              v-model="item.reportesTo"
              :items="getSelectItems(item)"
              :item-text="getManagerFullName"
              item-value="_id"
              @change="assignToManager(item)"
              class="my-2 fixed-width-column"
              :menu-props="{ offsetY: true }"
            >
              <template v-slot:selection="{ item }">
                <v-tooltip top>
                  <template v-slot:activator="{ on }">
                    <div v-on="on" class="fixed-width-column">
                      {{ getManagerFullName(item) }}
                    </div>
                  </template>
                  <span>{{ getManagerFullName(item) }}</span>
                </v-tooltip>
              </template>
              <template v-slot:prepend-item>
                <v-list-item
                  ripple
                  v-show="item.reportesTo"
                  @mousedown.prevent
                  @click="assignToManager({ _id: item._id, reportesTo: null })"
                  class="unassignedOptions"
                >
                  <v-list-item-content>
                    <v-list-item-title class="py-0"> Unassigned (No Role) </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
                <v-divider class=""></v-divider>
              </template>
            </v-select>
          </template>
          <template v-slot:item.action="{ item }">
            <td class="px-4 py-5">
              <action-button
                :item="item"
                :canEdit="canEdit"
                :canDelete="false"
                :showEditButton="canEdit"
                :showDeleteButton="false"
                :showLeaveButton="canEdit"
                :showExpenseButton="canEdit"
                :handleEdit="() => editUser(item)"
                :handleLeaveAction="() => userLeaves(item)"
                :handleExpenseAction="() => addUserExpense(item)"
                :handleDelete="() => selectUserDelete(item)"
              />
            </td>
          </template>
        </v-data-table>
        <no-data title="Nothing to Display" subTitle="You need to import the employees" v-else>
          <v-btn
            @click="importEmployees"
            :color="$vuetify.theme.currentTheme.primary"
            :loading="loadingImport"
            :disabled="loadingImport"
            class="white--text"
            v-show="canWrite"
          >
            Import Employees
            <v-icon right dark> mdi-download </v-icon>
          </v-btn>
        </no-data>
      </div>

      <!-- <div class="overflow-x-auto overflow-y-auto mt-2" v-else>
        <div class="mb-3">
          <div v-if="loading">
            <div class="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <v-skeleton-loader
                v-for="n in 9"
                :key="n"
                height="200"
                type="card"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else-if="employees.length">
            <div class="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <v-card
                v-for="employee in employees"
                :key="employee._id"
                class="p-3 flex flex-col justify-between wfm-emp-card"
                :class="{ 'selected-card': isSelected(employee) }"
                :color="$vuetify.theme.currentTheme.cardColor"
                @mouseenter="employee.showCheckbox = true"
                @mouseleave="employee.showCheckbox = isSelected(employee) ? true : false"
              >
                <div>
                  <v-checkbox
                    :color="$vuetify.theme.currentTheme.primary"
                    hide-details
                    class="mt-0 pt-0 absolute top-1 left-1 z-[3] custom-checkbox dark-text-white"
                    :input-value="isSelected(employee)"
                    @change="updateSelected(employee)"
                    v-show="
                      (employee.showCheckbox || isSelected(employee)) &&
                      shouldCheckboxVisible &&
                      canEdit
                    "
                  >
                  </v-checkbox>
                </div>

                <v-row no-gutters>
                  <v-col cols="">
                    <v-avatar size="100">
                      <v-img :src="getEmployeeImage(employee)" cover></v-img>
                    </v-avatar>
                    <p
                      class="text-[12px] text-[#00cc00] rounded-xl p-1 mt-2 flex items-center justify-center w-[110px]"
                      :class="{ 'bg-green-100': !$vuetify.theme.dark }"
                      :style="{
                        backgroundColor: $vuetify.theme.dark
                          ? $vuetify.theme.currentTheme.secondary
                          : ''
                      }"
                      v-if="employee.isOwner"
                    >
                      OWNER
                    </p>
                    <p
                      class="text-xs text-center w-[110px] mt-2 font-bold"
                      :class="{ 'text-green-500': !$vuetify.theme.dark }"
                      :style="{
                        color: $vuetify.theme.dark ? $vuetify.theme.currentTheme.secondary : ''
                      }"
                      v-if="employee?.clockIn"
                    >
                      Clock-In
                    </p>
                    <p
                      class="text-[#21427D] text-xs text-center w-[110px] mt-2 font-bold"
                      small
                      v-else
                    >
                      Clock-Out
                    </p>
                  </v-col>

                  <v-col cols="6">
                    <v-row no-gutters class="ml-2">
                      <v-col>
                        <p class="mb-2 text-sm font-semibold">
                          {{ userFullName(employee) }}
                          <v-icon
                            v-show="isLiveLocationEnabled"
                            color="warning"
                            class="ml-2 mb-1"
                            small
                            v-tooltip="{
                              text: `Live location can't be tracked.`
                            }"
                            >mdi-shield-alert-outline</v-icon
                          >
                        </p>
                        <p class="mb-2 text-sm">
                          <span class="font-semibold">Email - </span>
                          {{ employee.maskedEmail }}
                        </p>
                        <p class="mb-2 text-sm">
                          <span class="font-semibold">Masked Mobile - </span>
                          {{ employee.maskedMobileNumber }}
                        </p>
                        <p class="mb-2 text-sm">
                          <span class="font-semibold">{{ orgLanguage.employees }} Id - </span>
                          {{ employee.employeeId }}
                        </p>
                        <p class="mb-2 text-sm" v-show="isFeatureVisible('ASSIGN_ROLE')">
                          <span class="font-semibold">Role - </span>{{ employee.wfmRole?.role }}
                        </p>
                        <p class="mb-2 text-sm">
                          <span class="font-semibold">Reporting To - </span
                          >{{ directManager(employee.reportesTo?._id) }}
                        </p>
                        <p
                          class="mb-2 text-sm"
                          v-if="employee.team"
                          v-show="isFeatureVisible('ASSIGN_TEAM')"
                        >
                          <span class="font-semibold">Team Name - </span
                          >{{ employee.team?.teamName }}
                        </p>
                        <p
                          class="mb-2 text-sm"
                          v-if="employee.branch"
                          v-show="isFeatureVisible('ASSIGN_BRANCH')"
                        >
                          <span class="font-semibold">Branch Name - </span
                          >{{ findBranch(employee)?.branchName }}
                        </p>

                        <div class="flex flex-wrap items-center gap-2">
                          <p class="text-xs font-semibold mb-0">Status</p>
                          <v-switch
                            hide-details
                            color="green"
                            v-show="canEdit"
                            class="mt-0 pt-0"
                            :input-value="employee.status === 'ACTIVE'"
                            @change="toggleEmployeeStatus(employee)"
                          >
                          </v-switch>
                          <p class="text-xs font-semibold mb-0">
                            {{ employee.status }}
                          </p>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                  <div class="w-4 mr-3">
                    <v-btn icon>
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <v-icon
                            color="green"
                            @click="openMessage(employee)"
                            v-on="on"
                            v-bind="attrs"
                          >
                            mdi-whatsapp
                          </v-icon>
                        </template>
                        <span>Send Whatsapp Message</span>
                      </v-tooltip>
                    </v-btn>
                  </div>
                  <v-col cols="1">
                    <v-menu offset-y>
                      <template v-slot:activator="{ on }">
                        <v-btn icon v-on="on">
                          <v-icon>mdi-dots-vertical</v-icon>
                        </v-btn>
                      </template>
                      <v-list>
                        <v-list-item @click="editUser(employee)" v-show="canEdit">
                          <v-list-item-title>Edit</v-list-item-title>
                        </v-list-item>
                        <v-list-item
                          @click="userLeaves(employee)"
                          v-show="isAnyLeaveSettingAllowed && canEdit"
                        >
                          <v-list-item-title>Leaves</v-list-item-title>
                        </v-list-item>
                        <v-list-item
                          @click="addUserExpense(employee)"
                          v-show="isAnyClaimSettingAllowed && canEdit"
                        >
                          <v-list-item-title>Set Expense Limits</v-list-item-title>
                        </v-list-item>
                        <v-list-item @click="selectUserDelete(employee)" v-show="canDelete">
                          <v-list-item-title>Delete</v-list-item-title>
                        </v-list-item>
                        <v-divider></v-divider>
                        <v-list-item @click="userDetails(employee)">
                          <v-list-item-title><span>View Details</span></v-list-item-title>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </v-col>
                </v-row>
              </v-card>
            </div>
          </div>
          <no-data
            title="No Results found"
            subTitle="Retry adjusting filters"
            v-else-if="!isLoading && !employees.length"
          >
          </no-data>
        </div>
        <v-pagination
          v-model="options.page"
          :length="totalPages"
          :total-visible="5"
          v-if="totalPages > 1"
          :color="$vuetify.theme.currentTheme.primary"
        ></v-pagination>
      </div> -->

      <v-dialog v-model="shareMessageDialog" max-width="600">
        <div class="bg-white dark-bg-default p-5">
          <div class="flex justify-between mb-4">
            <h4 class="mb-0 text-xl font-bold">Whatsapp message</h4>
            <v-icon @click="shareMessageDialog = false">mdi-close</v-icon>
          </div>
          <v-textarea outlined v-model="whatsappMessage" label="Type message" hide-details>
          </v-textarea>
          <v-card-actions class="flex justify-end px-0">
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              :disabled="!whatsappMessage"
              @click="sendOnWhatsapp"
              >Send</v-btn
            >
          </v-card-actions>
        </div>
      </v-dialog>

      <v-dialog
        v-model="openEditEmployee"
        max-width="600"
        :color="$vuetify.theme.currentTheme.cardColor"
      >
        <div class="bg-white p-5 pt-0 dark-bg-modal">
          <div
            class="sticky flex top-0 justify-between z-50 align-top items-center bg-white dark-bg-modal"
          >
            <v-card-title style="min-width: 200px" class=""> Edit Profile </v-card-title>
            <v-btn plain @click="cancelUserInfo">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </div>
          <v-stepper v-model="activeStep" vertical flat ripple="false" v-show="jumpToStep">
            <!-- Profile Section -->
            <v-stepper-step :complete="!!isProfileComplete" step="1" editable>
              Profile
            </v-stepper-step>
            <v-stepper-content step="1">
              <div class="flex justify-between mt-4">
                <v-text-field
                  v-model="userFirstName"
                  label="First Name"
                  class="w-1/2"
                  :disabled="!isOrgAdmin"
                  outlined
                  dense
                ></v-text-field>
                <div class="p-1" />
                <v-text-field
                  v-model="userLastName"
                  label="Last Name"
                  class="w-1/2"
                  :disabled="!isOrgAdmin"
                  outlined
                  dense
                ></v-text-field>
              </div>
              <div class="flex justify-between">
                <v-text-field
                  :value="showEmail ? editEmp.email : editEmp.maskedEmail"
                  label="Email Id"
                  class="w-1/2"
                  :disabled="!isOrgAdmin"
                  @click:append="showEmail = !showEmail"
                  :append-icon="showEmail ? 'mdi-eye' : 'mdi-eye-off'"
                  outlined
                  dense
                ></v-text-field>
                <div class="p-1" />
                <v-text-field
                  :value="showMobile ? editEmp.mobile : editEmp.maskedMobileNumber"
                  label="Mobile Number"
                  class="w-1/2"
                  :disabled="!isOrgAdmin"
                  @click:append="showMobile = !showMobile"
                  :append-icon="showMobile ? 'mdi-eye' : 'mdi-eye-off'"
                  outlined
                  dense
                ></v-text-field>
              </div>
              <div class="flex items-center justify-end">
                <v-btn outlined @click="cancelUserInfo" class="mr-4">Cancel</v-btn>
                <v-btn
                  @click="activeStep++"
                  class="white--text"
                  :color="$vuetify.theme.currentTheme.primary"
                  >Continue</v-btn
                >
              </div>
            </v-stepper-content>

            <!-- Employee Detail Section -->
            <v-stepper-step :complete="!!isEmployeeDetailsComplete" step="2" editable>
              {{ orgLanguage.employees }} Detail
            </v-stepper-step>
            <v-stepper-content step="2">
              <p class="text-sm text-orange-500 px-2">
                <strong>NOTE: </strong>Before changing the
                <strong> {{ orgLanguage.employees }} ID </strong> ensure that the
                <strong> {{ orgLanguage.employees }} ID </strong>
                is unique and not assigned to any other {{ orgLanguage.employees }}.
              </p>
              <v-divider class="my-2"></v-divider>
              <div class="flex justify-between gap-2 mt-4">
                <v-text-field
                  class="w-1/2"
                  outlined
                  dense
                  v-model="editEmp.employeeId"
                  :label="`${orgLanguage.employees} ID *`"
                  :clearable="true"
                  :hint="employeeIdMessage"
                ></v-text-field>

                <v-select
                  :items="employeeTypes"
                  v-model="editEmp.employeeType"
                  :label="`${orgLanguage.employees} Type *`"
                  item-text="name"
                  item-value="name"
                  dense
                  outlined
                  class="w-1/2"
                  :clearable="true"
                ></v-select>
              </div>
              <div class="flex justify-between gap-2">
                <v-text-field
                  class="w-1/3"
                  outlined
                  dense
                  v-model="editEmp.department"
                  label="Department"
                ></v-text-field>

                <v-text-field
                  outlined
                  dense
                  v-model="editEmp.designation"
                  label="Designation"
                  class="w-1/3"
                ></v-text-field>

                <v-text-field
                  class="w-1/3"
                  outlined
                  dense
                  v-model="editEmp.ctc"
                  label="CTC"
                  type="number"
                ></v-text-field>
                <v-text-field
                  class="w-1/3"
                  outlined
                  dense
                  v-model="monthlySalary"
                  label="Monthly Salary"
                  type="number"
                  disabled
                ></v-text-field>
              </div>
              <div class="flex justify-between gap-2">
                <v-text-field
                  class="w-1/2"
                  outlined
                  dense
                  v-model="employeeDetails.uanNumber"
                  label="UAN Number"
                  type="number"
                ></v-text-field>
                <v-text-field
                  class="w-1/2"
                  outlined
                  dense
                  v-model="employeeDetails.panNumber"
                  label="PAN Number"
                ></v-text-field>
              </div>
              <div class="flex justify-between gap-2">
                <v-text-field
                  class="w-1/2"
                  outlined
                  dense
                  v-model="employeeDetails.bankName"
                  label="Bank Name"
                ></v-text-field>
                <v-text-field
                  class="w-1/2"
                  outlined
                  dense
                  v-model="employeeDetails.ifscCode"
                  label="IFSC Code"
                ></v-text-field>
              </div>
              <v-text-field
                outlined
                dense
                v-model="employeeDetails.bankAccountNumber"
                label="Bank Account Number"
                class="w-full"
                type="number"
              ></v-text-field>
              <div class="flex items-center justify-end">
                <v-btn outlined class="mr-4" @click="activeStep--">Back</v-btn>
                <v-btn
                  @click="activeStep++"
                  class="white--text"
                  :color="$vuetify.theme.currentTheme.primary"
                  >Continue</v-btn
                >
              </div>
            </v-stepper-content>

            <!-- Current Address Section -->
            <v-stepper-step :complete="!!isCurrentAddressComplete" step="3" editable>
              Current Address (Optional)
            </v-stepper-step>
            <v-stepper-content step="3">
              <div v-if="!editEmp.addressLine1">
                <CommonAutocomplete
                  class="mb-6 mt-4"
                  :addressLine1="addressLine1"
                  v-model="selectedAddress"
                  @selected-place="handleSelectedPlaceCurrent"
                />
              </div>
              <div v-else>
                <div class="flex">
                  <v-text-field
                    outlined
                    dense
                    class="mt-2 mr-2"
                    v-model="editEmp.addressLine1"
                    label="Address Line 1"
                    item-text="Address Line 1"
                    item-value="addressLine1"
                  ></v-text-field>
                  <v-btn text color="red" @click="clearAddress" class="mt-3" small>Clear</v-btn>
                </div>
                <div class="flex justify-between gap-2">
                  <v-text-field
                    class="w-1/2"
                    outlined
                    dense
                    v-model="editEmp.city"
                    label="City"
                    item-text="city"
                    item-value="city"
                  ></v-text-field>
                  <v-text-field
                    class="w-1/2"
                    outlined
                    dense
                    v-model="editEmp.state"
                    label="State"
                  ></v-text-field>
                </div>
                <div class="flex justify-between gap-2">
                  <!-- <v-text-field
                    class="w-1/2"
                    outlined
                    dense
                    v-model="editEmp.country"
                    label="Country"
                  ></v-text-field> -->
                  <v-text-field
                    outlined
                    dense
                    v-model="editEmp.pinCode"
                    label="Pin Code"
                    type="number"
                  ></v-text-field>
                </div>
              </div>
              <div class="flex items-center justify-end mt-4">
                <v-btn outlined class="mr-4" @click="activeStep--">Back</v-btn>
                <v-btn
                  @click="activeStep++"
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text"
                  >Continue</v-btn
                >
              </div>
            </v-stepper-content>

            <!-- Roles And Management Section -->
            <v-stepper-step :complete="!!isRolesAndManagementComplete" step="4" editable>
              Roles And Management
            </v-stepper-step>
            <v-stepper-content step="4">
              <v-select
                v-model="editEmp.wfmRole"
                :items="getRoleDropdown(editEmp)"
                label="Role"
                item-text="role"
                item-value="_id"
                outlined
                dense
                @change="updateRole(editEmp)"
                @click="newRoleId(editEmp)"
                v-show="isFeatureVisible('ASSIGN_ROLE')"
                class="mt-4"
              >
              </v-select>

              <v-select
                v-model="editEmp.reportesTo"
                :items="getSelectItems(editEmp)"
                label="Manager"
                :item-text="getManagerFullName"
                item-value="_id"
                outlined
                dense
                @click="newManagerId(editEmp)"
              >
              </v-select>
              <v-select
                v-model="editEmp.status"
                :items="status"
                label="Status"
                outlined
                dense
                hide-details
                @click="newStatusId(editEmp)"
              ></v-select>
              <div class="flex items-center justify-end mt-4">
                <v-btn outlined class="mr-4" @click="activeStep--">Back</v-btn>
                <v-btn
                  @click="saveUserInfo"
                  :color="$vuetify.theme.currentTheme.primary"
                  class="white--text"
                  >Save</v-btn
                >
              </div>
            </v-stepper-content>
          </v-stepper>
        </div>
      </v-dialog>

      <v-dialog v-model="showConfirmationDialog" max-width="700" persistent>
        <v-card>
          <v-card-title class="text-h6 justify-center text-[#6B6D78]"
            >Are you sure you want to assign {{ roleName }} role to
            {{ employeeFullName }}?</v-card-title
          >
          <v-card-text class="text-center mt-1">
            Kindly confirm if you want to assign this role. <br />
            {{ alertText }}
          </v-card-text>
          <v-card-actions class="flex justify-center mb-2">
            <v-btn outlined @click="cancelUpdate" class="mr-4"
              ><span class="text-black">Cancel</span></v-btn
            >
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              @click="confirmUpdate"
              >Confirm</v-btn
            >
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="mapDialog" max-width="1000">
        <MapView
          :userLiveLocation="userLiveLocation"
          v-if="userLiveLocation && userLiveLocation.length"
        />
        <div class="w-full h-full bg-white" v-else>
          <NoData
            class="flex w-full justify-center"
            title="Nothing to Display"
            :subTitle="errorMessage"
          />
        </div>
      </v-dialog>

      <v-dialog v-model="openExpenseDialog" max-width="73rem" persistent>
        <div class="bg-white p-4 pt-0 dark-bg-modal">
          <TravelAndAllowance
            :travelArray="travelArray"
            :allowanceArray="allowanceArray"
            :travelModes="travelModes"
            :allowanceTypes="allowanceTypes"
            @onCancel="cancelExpense"
            :selectUserExpense="selectUserExpense"
          />
          <v-card-actions class="flex justify-end mx-3 mt-4">
            <v-btn outlined class="mr-4" @click="cancelExpense"
              ><span class="text-black">Cancel</span></v-btn
            >
            <v-btn
              :color="$vuetify.theme.currentTheme.primary"
              class="white--text"
              @click="confirmUserExpense"
              >Confirm</v-btn
            >
          </v-card-actions>
        </div>
      </v-dialog>
      <v-dialog v-model="showLeadManagement" max-width="76rem">
        <EmployeeLeaves
          :employeeData="employeeData"
          :employeeImage="employeeImage"
          :orgConfigSettings="orgConfigSettings"
          @handleLeaveClose="handleLeaveManagementDialog"
        />
      </v-dialog>

      <v-dialog v-model="openUserDetails" max-width="76rem" persistent>
        <DetailTabs
          :selectedUserProfile="selectedUserProfile"
          :branch="findBranch(selectedUserProfile)"
          @handleClose="handleProfileModal"
        />
      </v-dialog>

      <UpdateCases
        :visible.sync="showDialog"
        :handleShowDialog="handleShowDialog"
        :items="selected"
        :teams="teams"
        :dialogLable="dialogLable"
        :branches="branches"
      >
      </UpdateCases>

      <!-- navigation drawer -->
      <v-navigation-drawer width="50%" v-model="rightDrawer" v-bind:right="right" fixed>
        <EmployeesActionTabs
          v-on:refreshEmployeeAction="(newUser) => refreshEmployee(newUser)"
          v-on:cancelDrawerAction="cancelDrawer"
          :managersList="managers"
          :roleList="roles"
          @callParentFunction="downloadDemo('xlsx')"
        />
      </v-navigation-drawer>
    </div>
    <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
    <!-- <tree-modal v-if="isModalOpen" @close="closeModal" :isOpen="isModalOpen" /> -->
  </div>
</template>
<script>
import {
  debounce,
  hasPermission,
  fullName,
  isModuleFeatureAllowed,
  isAnyFeatureAllowed
} from '@/utils/common'
import { demoEmployeesData } from '@/utils/demoFormat'
import ActionButton from '@/components/ActionButton'
import MapView from '@/components/Workforce/EmployeeManagement/MapView'
import TravelAndAllowance from '@/components/TravelAndAllowance'
import EmployeeLeaves from '@/components/Workforce/EmployeeManagement/LeaveManagement/EmployeeLeaves'
import {
  roleUpdateSuccess,
  roleUpdateError,
  employeeImportSuccess,
  assignEmployeeMessage,
  assignEmployeeError
} from '@/utils/toastMessages'
import EmployeesActionTabs from '@/components/Workforce/EmployeeManagement/EmployeesActionComponent.vue'
import NoData from '@/components/NoData.vue'
import UpdateCases from '@/components/Workforce/CollectionManagement/CaseModules/UpdateCases.vue'
import EmployeeInfo from '@/components/EmployeeInfo.vue'
import permissionData from '@/utils/permissions'
import CommonAutocomplete from '@/components/CommonAutoComplete.vue'
import FilterPopOver from '@/components/FilterPopOver.vue'
import TreeModal from '@/components/Workforce/EmployeeManagement/TreeModal.vue'
import DetailTabs from '@/components/Workforce/EmployeeManagement/LeaveManagement/DetailTabs.vue'

export default {
  layout: 'Workforce/default',
  name: 'EmployeesPage',
  components: {
    NoData,
    EmployeeInfo,
    ActionButton,
    MapView,
    EmployeesActionTabs,
    TravelAndAllowance,
    EmployeeLeaves,
    UpdateCases,
    CommonAutocomplete,
    // FilterPopOver,
    // TreeModal,
    DetailTabs
  },
  data() {
    return {
      loginUser: this.$storage.getUniversal('user'),
      orgCode: this.$storage.getUniversal('organization_code'),
      userData: this.$store.state.wfmPermissions,
      orgLang: this.$store.state.orgLanguage,
      options: { sortBy: [], sortDesc: [], itemsPerPage: 10, page: 1 },
      totalEmployees: 0,
      totalPages: 0,
      currentPage: 1,
      isLoading: true,
      searchEmployee: '',
      showLeadManagement: false,
      isTableView: false,
      filterVisible: false,
      shareMessageDialog: false,
      whatsappMessage: '',
      selectedTeamName: null,
      addressLine1: '',
      selectedAddress: '',
      rightDrawer: false,
      right: true,
      mapDialog: false,
      showConfirmationDialog: false,
      loadingImport: false,
      downloadMenu: false,
      selectedRoleFilter: null,
      selectedStatus: 'ACTIVE',
      employeeData: null,
      filterName: '',
      employees: [],
      inActiveUsers: [],
      userLiveLocation: [],
      selected: [],
      selectedEmployeeIds: new Set(), // Add this to track selected employees by ID
      employeeFullName: '',
      editEmp: {},
      employeeDetails: {},
      openEditEmployee: false,
      roleName: '',
      tempRole: null,
      tempManager: null,
      tempStatus: null,
      userFirstName: '',
      userLastName: '',
      selectedBranchName: null,
      previousRoles: {},
      showFilters: false,
      openExpenseDialog: false,
      fetchOnSwitch: false,
      roles: [],
      status: ['ACTIVE', 'INACTIVE', 'CLOSED'],
      alertText: '',
      employeeImage: '',
      waNumber: '',
      dialogLable: '',
      dialogBranchLable: '',
      showDialog: false,
      showBranchDialog: false,
      showCheckbox: false,
      managers: [],
      teams: [],
      branches: [],
      // hoveredParent:"",
      orgConfigSettings: {},
      loading: true,
      selectUserExpense: null,
      errorMessage: '',
      csvFormat: this.generateCsvHeader(demoEmployeesData),
      employeesFormat: demoEmployeesData,
      travelArray: [{ mode: '', pricePerKm: null }],
      allowanceArray: [{ type: '', amount: null }],
      employeeTypes: [],
      activeStep: 1,
      selectedUserProfile: null,
      openUserDetails: false,
      showMobile: false,
      showEmail: false,
      previousEmployeeId: '',
      employeeIdMessage: '',
      isEmployeeIdAvailable: false,
      travelModes: [
        { text: 'Bus', value: 'BUS', pricePerKm: null, mode: '' },
        { text: 'Train', value: 'TRAIN', pricePerKm: null, mode: '' },
        { text: 'Toll', value: 'TOLL', pricePerKm: null, mode: '' },
        {
          text: 'AutoRickshaw',
          value: 'AUTORICKSHAW',
          pricePerKm: null,
          mode: ''
        },
        { text: 'Taxi', value: 'TAXI', pricePerKm: null, mode: '' },
        { text: 'Bike', value: 'BIKE', pricePerKm: null, mode: '' },
        {
          text: 'Fuel Bill',
          value: 'FUEL_BILL',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        },
        {
          text: 'Other Expenses',
          value: 'OTHER_EXPENSES',
          pricePerKm: null,
          min: null,
          max: null,
          mode: ''
        }
      ],
      allowanceTypes: [
        {
          text: 'Entertainment allowance',
          value: 'ENTERTAINMENT_ALLOWANCE',
          type: '',
          amount: null
        },
        {
          text: 'Travel allowance',
          value: 'TRAVEL_ALLOWANCE',
          type: '',
          amount: null
        },
        {
          text: 'Dearness allowance',
          value: 'DEARNESS_ALLOWANCE',
          type: '',
          amount: null
        },
        { text: 'Medicine', value: 'MEDICINE', type: '', amount: '' },
        {
          text: 'Education Allowance',
          value: 'EDUCATION_ALLOWANCE',
          type: '',
          amount: null
        },
        { text: 'House', value: 'HOUSE', type: '', amount: null },
        { text: 'Transport', value: 'TRANSPORT', type: '', amount: null }
      ],
      isLoad: true,
      selectedCount: 1,
      isEdit: false,
      isModalOpen: false,
      tempEmpId: null
    }
  },
  watch: {
    'editEmp.employeeId': function (newVal) {
      if (newVal && this.previousEmployeeId && newVal !== this.previousEmployeeId) {
        this.checkEmployeeIdAvailability(newVal)
        this.tempEmpId = newVal
      } else if (!newVal) {
        this.checkEmployeeIdAvailability(newVal)
        this.isEmployeeIdAvailable = false
      } else {
        this.employeeIdMessage = ''
      }
    },
    '$route.query.search': function (newQuery) {
      try {
        this.filterName = newQuery || ''
        this.fetchEmployees()
      } catch (error) {
        console.error('Error updating employee search:', error)
      }
    },
    filterName(val) {
      if (!val && this.$route.query.search) {
        this.$router.replace({ query: {} }).catch((err) => {
          if (err.name !== 'NavigationDuplicated') {
            throw err
          }
        })
      }
    },
    jumpToStep(newStep) {
      this.activeStep = newStep
    },
    currentPage: {
      handler() {
        const empQuery = this.$route.query?.search
        if (empQuery) {
          this.filterName = empQuery
        }
        this.fetchEmployees()
      },
      deep: true,
      immediate: true
    },
    options: {
      handler() {
        if (this.fetchOnSwitch) {
          this.fetchOnSwitch = false
        } else {
          this.fetchEmployees()
        }
      },
      deep: true,
      immediate: true
    },
    userLiveLocation: {
      handler(newVal) {
        this.userLiveLocation = newVal
      },
      deep: true
    },
    selectUserExpense: {
      handler(newVal) {
        if (newVal) {
          if (
            this.selectUserExpense?.travelModes?.length ||
            this.selectUserExpense?.allowances?.length
          ) {
            this.travelArray = [...this.selectUserExpense?.travelModes]
            this.allowanceArray = [...this.selectUserExpense?.allowances]
          }
        }
      },
      deep: true
    },
    selectedTeamName() {
      this.updateSelectedCount()
    },
    selectedRoleFilter() {
      this.updateSelectedCount()
    },
    selectedStatus(val) {
      if (val) {
        this.updateSelectedCount()
      }
    }
  },
  computed: {
    orgLanguage() {
      return this.orgLang.data.leftNav
    },
    rowClass() {
      return (item, index) => {
        return index % 2 === 0 ? 'even-row' : 'odd-row'
      }
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.userWrite)
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.userEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.userDelete)
    },
    tableHeaders() {
      const action = {
        text: 'Action',
        value: 'action',
        width: '10%',
        sortable: false
      }
      const teamCol = {
        text: 'Team',
        value: 'team',
        width: '15%',
        sortable: false
      }

      const branchCol = {
        text: 'Branch',
        value: 'branch',
        width: '15%',
        sortable: false
      }

      const tableHeaders = [
        { text: 'Name', value: 'firstName', width: '20%' },
        { text: 'Code', value: 'employeeId', width: '10%' },
        {
          text: 'Phone Number',
          value: 'mobile',
          width: '10%',
          sortable: false
        },
        { text: 'Email ID', value: 'email', width: '15%' },
        ...(this.isFeatureVisible('ASSIGN_TEAM') && this.isAnyTeamSettingAllowed ? [teamCol] : []),
        ...(this.isFeatureVisible('ASSIGN_BRANCH') && this.isAnyBranchSettingAllowed
          ? [branchCol]
          : []),
        // { text: "Team", value: "team", width: "15%", sortable: false },
        // { text: "Branch", value: "branch", width: "15%", sortable: false },
        { text: 'Role', value: 'role', width: '10%', sortable: false },
        {
          text: 'Manager',
          value: 'directManager',
          width: '10%',
          sortable: false
        },
        { text: 'Status', value: 'status', width: '10%', sortable: false },
        ...(this.canEdit || this.canDelete ? [action] : [])
      ]
      return tableHeaders
    },
    shouldCheckboxVisible() {
      return (
        this.isFeatureVisible('BULK_ASSIGN_TEAMS') ||
        this.isFeatureVisible('BULK_ASSIGN_BRANCH') ||
        this.isFeatureVisible('BULK_ASSIGN_SALARY_TEMPLATE')
      )
    },
    getManagerFullName() {
      return (manager) =>
        `${manager?.firstName} ${manager?.lastName}(${manager?.wfmRole?.role || 'No Role'})`
    },
    isOrgAdmin() {
      return this.loginUser?.role?.includes('WFM_ADMIN')
    },
    monthlySalary() {
      return this.employeeMonthlySalary(this.editEmp?.ctc)
    },
    jumpToStep() {
      if (!this.isProfileComplete) {
        return 1
      } else if (!this.isEmployeeDetailsComplete) {
        return 2
      } else if (!this.isCurrentAddressComplete) {
        return 3
      } else {
        return 4
      }
    },
    isProfileComplete() {
      return (
        this.editEmp.firstName &&
        this.editEmp.lastName &&
        !!this.editEmp.maskedEmail &&
        !!this.editEmp.maskedMobileNumber
      )
    },
    isEmployeeDetailsComplete() {
      return (
        this.employeeDetails.employee_id &&
        this.employeeDetails.employee_type &&
        this.employeeDetails.department &&
        this.employeeDetails.designation &&
        this.employeeDetails.salary &&
        this.employeeDetails.uanNumber &&
        this.employeeDetails.panNumber &&
        this.employeeDetails.bankName &&
        this.employeeDetails.ifscCode &&
        this.employeeDetails.bankAccountNumber
      )
    },
    isCurrentAddressComplete() {
      return (
        this.editEmp.addressLine1 &&
        this.editEmp.city &&
        this.editEmp.state &&
        this.editEmp.country &&
        this.editEmp.pinCode
      )
    },
    isRolesAndManagementComplete() {
      return this.editEmp.wfmRole && this.editEmp.reportesTo && this.editEmp.status
    },
    employeeIdRule() {
      return () => {
        if (this.employeeIdMessage) {
          return this.isEmployeeIdAvailable ? true : this.employeeIdMessage
        }
        return true
      }
    },
    isAnyBranchSettingAllowed() {
      return isAnyFeatureAllowed('BRANCH_MANAGEMENT')
    },
    isAnyTeamSettingAllowed() {
      return isAnyFeatureAllowed('TEAM_MANAGEMENT')
    },
    isAnySalarySettingAllowed() {
      return isAnyFeatureAllowed('SALARY_MANAGEMENT')
    },
    isAnyLeaveSettingAllowed() {
      return isAnyFeatureAllowed('LEAVE_MANAGEMENT')
    },
    isAnyClaimSettingAllowed() {
      return isAnyFeatureAllowed('CLAIM_MANAGEMENT')
    }
  },

  methods: {
    async checkEmployeeIdAvailability(employeeId) {
      try {
        const params = {
          employeeId: String(employeeId)
        }
        const response = await this.$axios.get(`workforce/v2/users`, {
          params
        })

        if (!employeeId) {
          this.employeeIdMessage = 'Employee ID is required'
          this.isEmployeeIdAvailable = false
          return
        } else if (response.data.success && response.data.users.length > 0) {
          this.employeeIdMessage = 'Employee ID is already taken'
          this.isEmployeeIdAvailable = false
        } else {
          this.employeeIdMessage = 'Employee ID is available'
          this.isEmployeeIdAvailable = true
        }
      } catch (error) {
        console.error('Error checking employee ID:', error)
        this.employeeIdMessage = 'Error checking employee ID'
        this.isEmployeeIdAvailable = false
      }
    },
    handleProfileModal() {
      this.openUserDetails = false
    },
    handleLeaveManagementDialog() {
      this.showLeadManagement = false
    },
    openModal() {
      this.isModalOpen = true
    },
    closeModal() {
      this.isModalOpen = false
    },
    clearAddress() {
      this.editEmp.addressLine1 = ''
      this.editEmp.city = ''
      this.editEmp.state = ''
      this.editEmp.country = ''
      this.editEmp.pinCode = ''
      this.selectedAddress = ''
    },
    async fetchAttendece(employees) {
      try {
        let params = {
          start: this.$dayjs().format('YYYY-MM-DD'),
          end: this.$dayjs().format('YYYY-MM-DD')
        }

        let userIds = employees
          .map((employee) => {
            return employee._id
          })
          .join(',')

        if (userIds) {
          params.userIds = userIds
        }

        const response = await this.$axios.get(`/workforce/user/v2/attendance`, {
          params
        })
        const fetchedTeamData = response.data?.employeeAttendance || []
        this.employees = employees
        this.employees.map((employee) => {
          const attendanceData = fetchedTeamData?.find((data) => employee?._id === data.userId?._id)

          if (
            attendanceData?.checkInOutData[attendanceData?.checkInOutData?.length - 1]?.status ===
            'CHECKED_IN'
          ) {
            employee.clockIn = true
          } else {
            employee.clockIn = false
          }
        })
      } catch (error) {
        console.error('Error fetching Attendence data:', error)
      }
    },
    async importEmployees() {
      try {
        this.loadingImport = true
        await this.$axios.get('workforce/import/users')

        this.$toast.success(employeeImportSuccess)
        await this.fetchEmployees()
      } catch (error) {
        console.error('Error importing employees:', error)
      } finally {
        this.loadingImport = false
      }
    },
    async fetchInActiveEmployees() {
      try {
        const response = await this.$axios.get('/workforce/report/users/notloggedIn')
        this.inActiveUsers = response.data.employees
      } catch (error) {
        console.log(error.message)
      }
    },
    handleInput: debounce(function () {
      this.currentPage = 1
      this.options.page = 1
      this.fetchEmployees()
    }, 1000),
    handleTableView() {
      if (this.isTableView) {
        this.fetchOnSwitch = true
      } else {
        this.fetchOnSwitch = false
      }
    },
    newRoleId(employee) {
      this.tempRole = employee.wfmRole
    },
    newManagerId(manager) {
      this.tempManager = manager.reportesTo
    },
    newStatusId(status) {
      this.tempStatus = status.status
    },
    employeeMonthlySalary(salary) {
      return Math.floor(salary / 12)
    },
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config
        this.orgConfigSettings = data
        return data || {}
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async prepareEmployeeParams() {
      const { sortBy, sortDesc, page, itemsPerPage } = this.options
      const params = {
        page: page,
        limit: itemsPerPage,
        search: this.filterName || null,
        // skipHierarchy: "true",
        sortOrder: 'asc'
      }

      if (sortBy.length > 0) {
        params.sortBy = sortBy[0]
      } else {
        params.sortBy = 'firstName'
      }

      if (sortDesc.length > 0) {
        params.sortOrder = sortDesc[0] ? 'desc' : 'asc'
      }

      if (this.selectedRoleFilter) {
        params.wfmRole = this.selectedRoleFilter
      }

      if (this.searchEmployee) {
        params.search = this.searchEmployee
      }

      if (this.selectedTeamName) {
        params.team = this.selectedTeamName
      }

      if (this.selectedBranchName) {
        params.branch = this.selectedBranchName
      }

      if (this.selectedStatus) {
        params.status = this.selectedStatus
      }

      if (this.loginUser?.branch && !this.loginUser?.role?.includes('WFM_ADMIN')) {
        // params.branch = this.loginUser?.branch;
      }

      return params
    },

    async fetchEmployees() {
      try {
        if (this.fetchOnSwitch) {
          this.fetchOnSwitch = false
          return
        }
        this.isLoading = true
        // this.loading = true

        const params = await this.prepareEmployeeParams()
        const response = await this.$axios.get('workforce/v2/users', {
          params
        })
        if (response.data.success) {
          await this.fetchAttendece(response.data.users)
          this.totalEmployees = response.data.pagination
            ? response.data.pagination.totalCount
            : this.employees.length
          this.totalPages = response.data?.pagination.totalPages
          this.employees = response.data.users.map((emp) => ({
            ...emp,
            showCheckbox: false
          }))
        }
      } catch (error) {
        console.error('Error fetching employees:', error)
      } finally {
        this.isLoading = false
        this.loading = false
      }
    },
    async fetchAllEmployeeDataInChunks() {
      const allEmployees = []
      const itemsPerPage = 50

      try {
        const initialParams = {
          ...this.prepareEmployeeParams(),
          page: 1,
          limit: itemsPerPage
        }
        const initialResponse = await this.$axios.get('workforce/v2/users', {
          params: initialParams
        })

        if (!initialResponse.data.success) return allEmployees

        allEmployees.push(...initialResponse.data.users)
        const totalPages = initialResponse.data.pagination.totalPages

        if (totalPages === 1) return allEmployees

        const promises = []
        for (let page = 2; page <= totalPages; page++) {
          const params = {
            ...this.prepareEmployeeParams(),
            page,
            limit: itemsPerPage
          }
          promises.push(this.$axios.get('workforce/v2/users', { params }))
        }

        const responses = await Promise.allSettled(promises)

        responses.forEach((result) => {
          if (result.status === 'fulfilled' && result.value.data.success) {
            allEmployees.push(...result.value.data.users)
          }
        })

        return allEmployees
      } catch (error) {
        console.error('Error fetching all employee data in chunks:', error)
        return allEmployees
      }
    },
    async exportAllEmployees() {
      try {
        const XLSX = require('xlsx')

        const allEmployees = await this.fetchAllEmployeeDataInChunks()

        const headers = [
          ['Name', 'Employee ID', 'Mobile', 'Email', 'Team', 'Branch', 'Status', 'Role', 'Manager']
        ]

        const rows = allEmployees.map((employee) => [
          employee.fullName,
          employee.employeeId,
          employee.mobile,
          employee.maskedEmail,
          employee.team?.teamName || '-',
          employee.branch?.branchName || '-',
          employee.status,
          employee.wfmRole || '-',
          employee.reportesTo ? this.getManagerFullName(employee.reportesTo) : ''
        ])

        const worksheet = XLSX.utils.aoa_to_sheet([...headers, ...rows])
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'All Employees')

        XLSX.writeFile(workbook, 'All_Employee_Data.xlsx')

        this.$toast.success('Successfully exported all employee data!')
      } catch (error) {
        console.error('Error exporting all employee data:', error)
        this.$toast.error('Failed to export employee data.')
      }
    },
    handlePageChange(newPage) {
      this.pagination.page = newPage
      this.fetchEmployees()
    },
    getEmployeeImage(item) {
      if (item.profileImage) {
        return `${process.env.VUE_APP_S3_BASE_URL}${item?.profileImage}`
      } else {
        if (item.gender === 'M') {
          return require('~/assets/img/profileMale.png')
        } else if (item.gender === 'F') {
          return require('~/assets/img/profileFemale.png')
        } else {
          return require('~/assets/img/profileOther.png')
        }
      }
    },
    findBranch(item) {
      return this.branches.find((el) => el._id === item?.branch)
    },
    updateRole(employee) {
      const selectedRoleId = employee.wfmRole
      // const previousRoleId = this.previousRoles[employee._id];
      this.employeeFullName = employee.firstName + ' ' + employee.lastName
      const selectedRole = this.roles.find((role) => role._id === selectedRoleId)
      this.roleName = selectedRole ? selectedRole.role : 'unassigned'
      // const previousRole = this.roles.find(
      //   (role) => role._id === previousRoleId
      // );
      const hasAdminAccess =
        selectedRole && selectedRole.extraPrivileges.includes('WORKFORCE_ADMIN_ACCESS')

      if (hasAdminAccess) {
        this.alertText = 'This Role has Workforce management dashoboard access.'
      } else if (selectedRole) {
        this.alertText = ''
      } else {
        this.alertText = 'This will clear all of the access.'
      }

      this.employeeToUpdate = employee
      this.showConfirmationDialog = true
    },

    confirmUpdate() {
      this.showConfirmationDialog = false

      if (this.employeeToUpdate) {
        this.performRoleUpdate(this.employeeToUpdate)
        this.employeeToUpdate = null
      }
    },
    cancelUpdate() {
      this.showConfirmationDialog = false
      if (this.employeeToUpdate) {
        this.employeeToUpdate.wfmRole = this.tempRole
        this.employeeToUpdate = null
      }
    },
    async performRoleUpdate(employee) {
      let payload = {}
      if (this.travelArray[0].pricePerKm || this.allowanceArray[0].amount) {
        payload.userInfo = {
          travelModes: this.travelArray,
          allowances: this.allowanceArray
        }
        this.$axios
          .patch(`workforce/users/${employee._id}?action=UPDATE_USER_INFO`, payload)
          .then(() => {
            this.$toast.success(roleUpdateSuccess)
            this.fetchManager()
          })
          .catch((error) => {
            console.error(roleUpdateError, error)
            this.$toast.error(roleUpdateError)
          })
      } else {
        this.previousRoles[employee._id] = employee.wfmRole
        payload.wfmRoleId = employee.wfmRole

        this.$axios
          .patch(`workforce/users/${employee._id}`, payload)
          .then(() => {
            this.$toast.success(roleUpdateSuccess)
            this.fetchManager()
          })
          .catch((error) => {
            console.error(roleUpdateError, error)
            this.$toast.error(roleUpdateError)
          })
      }
    },
    async fetchManager() {
      try {
        const response = await this.$axios.get('/workforce/users?roleAssigned=true')
        this.managers = response.data.users
      } catch (error) {
        console.log(error, 'Error fecthing managers')
      }
    },
    directManager(managerId) {
      const manager = this.managers.find((manager) => manager._id === managerId)
      return manager ? fullName(manager) : 'No Manager'
    },

    async assignToManager(employee) {
      const reportesToId = employee.reportesTo?._id || employee.reportesTo

      try {
        const response = await this.$axios.patch(
          `workforce/users/${employee._id}?action=UPDATE_REPORTING_TO`,
          {
            reportesToId: reportesToId || null
          }
        )
        if (reportesToId === null) {
          employee.reportesTo = null
        }
        const reportees = response.data.user.reportesTo
        this.$toast.success(assignEmployeeMessage(employee, reportees))
      } catch (error) {
        console.error(assignEmployeeError, error)
        this.$toast.error(assignEmployeeError)
      }
    },
    async fetchRoles() {
      try {
        const response = await this.$axios.get('workforce/org/role')
        if (response.data.success) {
          this.roles = response.data.role
        } else {
          console.error('Error fetching roles:', response.data.message)
        }
      } catch (error) {
        console.error('Error fetching roles:', error)
      }
    },
    toggleFilters() {
      this.showFilters = !this.showFilters
    },
    openMessage(employee) {
      this.shareMessageDialog = true
      this.waNumber = employee.mobile
    },
    sendOnWhatsapp() {
      const encodedMessage = encodeURIComponent(this.whatsappMessage)
      const whatsappUrl = `https://api.whatsapp.com/send?phone=${this.waNumber}&text=${encodedMessage}`
      window.open(whatsappUrl, '_blank')
    },
    getRoleDropdown(employee) {
      if (employee?.wfmRole) {
        return [...this.roles, { _id: null, role: 'Unassigned' }]
      } else {
        return this.roles
      }
    },
    generateCsvHeader(data) {
      if (data.length === 0) {
        return []
      }
      const Item = data[0]
      return Object.keys(Item)
    },
    async downloadDemo(format) {
      this.downloadFile(format, this.employeesFormat, 'Sample_User_Format')
    },
    async fetchAndDownloadInactive(format) {
      try {
        await this.fetchInActiveEmployees()
        this.downloadFile(format, this.inActiveUsers, 'Non-Engaged_User_Data')
      } catch (error) {
        console.error('Error fetching and downloading In Active User data:', error)
      }
    },
    async saveUserInfo() {
      this.isEdit = true
      try {
        this.assignToManager(this.editEmp)
        if (this.employeeToUpdate) {
          this.performRoleUpdate(this.employeeToUpdate)
        }
        await this.cancelUserInfo()
        await this.updateEmployeeDetails()
        this.$toast.success('Employee details updated successfully !!')
        this.isEdit = false
        this.openEditEmployee = false
        this.previousEmployeeId = ''
      } catch (error) {
        console.log(error)
      }
    },
    cancelUserInfo() {
      // this.importEmployees();
      this.tempManager = null
      this.employeeToUpdate = null
      this.openEditEmployee = false
      this.editEmp.employeeId = this.previousEmployeeId
      this.previousEmployeeId = ''
    },
    downloadFile(format, data, fileName) {
      if (format === 'csv') {
        this.downloadCSV(data, `${fileName}.csv`)
      } else if (format === 'xlsx') {
        this.downloadXLSX(data, `${fileName}.xlsx`)
      }
    },
    downloadCSV(data, fileName) {
      if (!data || data.length === 0) {
        console.error('No data to download.')
        return
      }

      const csvRows = []
      const headerRow = Object.keys(data[0]).join(',')
      csvRows.push(headerRow)

      data.forEach((item) => {
        const rowData = Object.values(item)
          .map((value) => (typeof value === 'string' ? `"${value}"` : value))
          .join(',')
        csvRows.push(rowData)
      })

      const csvContent = csvRows.join('\n')

      const blob = new Blob([csvContent], { type: 'text/csv' })
      const a = document.createElement('a')
      a.href = window.URL.createObjectURL(blob)
      a.download = fileName
      a.click()
      window.URL.revokeObjectURL(a.href)
    },
    downloadXLSX(data, fileName) {
      const XLSX = require('xlsx')
      const excelSheet = XLSX.utils.json_to_sheet(data)
      const newBook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(newBook, excelSheet, 'Sheet1')
      XLSX.writeFile(newBook, `${fileName}`)
    },
    onboardNewEmployees() {
      this.rightDrawer = !this.rightDrawer
    },
    getSelectItems(item) {
      const filteredManagers = this.managers.filter((manager) => {
        return manager._id !== item._id
      })
      if (item.reportesTo) {
        return [...filteredManagers, { _id: null, firstName: 'Unassigned', lastName: '' }]
      } else {
        return filteredManagers
      }
    },
    addUserExpense(item) {
      this.openExpenseDialog = true
      this.selectUserExpense = item
    },
    async editUser(employee) {
      this.openEditEmployee = true
      this.editEmp = employee
      this.previousEmployeeId = employee.employeeId
      this.userFirstName = employee.firstName
      this.userLastName = employee.lastName
      await this.getEmployeeDetails(employee.userId)
    },
    async userDetails(employee) {
      this.selectedUserProfile = employee
      this.openUserDetails = true
    },
    async getEmployeeDetails(userId) {
      try {
        const response = await this.$axios.get(`/workforce/user/employeeDetails?userId=${userId}`)
        this.employeeDetails = response.data?.user
      } catch (error) {
        console.log(error)
      }
    },
    async updateEmployeeDetails(employee) {
      try {
        if (employee) {
          this.editEmp = employee
        }
        const details = this.editEmp
        const payload = {
          employee_id: this.tempEmpId || details.employeeId,
          employee_type: details.employeeType,
          salary: Number(details.ctc) || undefined,
          monthly_income: this.monthlySalary || undefined,
          department: details.department,
          designation: details.designation,
          status: details.status,
          userAddress: {}
        }
        if (this.userFirstName !== details.firstName || this.userLastName !== details.lastName) {
          payload.last_name = this.userLastName
          payload.first_name = this.userFirstName
          payload.userMobile = details.mobile
        }
        if (this.selectedAddress) {
          payload.userAddress.addressLine1 = details.addressLine1 || undefined
          payload.userAddress.city = details.city || undefined
          payload.userAddress.state = details.state || undefined
          payload.userAddress.country = details.country || undefined
          payload.userAddress.pinCode = Number(details.pinCode) || undefined
        }
        await this.$axios.put(`/workforce/user/employeeDetails/${details.userId}`, payload)
        await this.importEmployees()

        // this.getEmployeeDetails()
      } catch (error) {
        console.log(error)
      }
    },
    userLeaves(employee) {
      this.showLeadManagement = true
      this.employeeData = employee
      this.employeeImage = this.getEmployeeImage(employee)
    },
    async confirmUserExpense() {
      this.performRoleUpdate(this.selectUserExpense)
      this.cancelExpense()
    },
    cancelExpense() {
      this.openExpenseDialog = false
      this.selectUserExpense = null
      this.travelArray = [{ mode: '', pricePerKm: null }]
      this.allowanceArray = [{ type: '', amount: null }]
    },
    updateTravelArray(newArray) {
      this.travelArray = newArray
    },
    updateAllowanceArray(newArray) {
      this.allowanceArray = newArray
    },
    selectUserDelete(item) {
      try {
        this.$axios
          .delete(`/workforce/user/${item._id}`)
          .then((response) => {
            const successMessage = response?.data?.message || 'Employee deleted successfully !!'
            this.$toast.success(successMessage)
            this.fetchEmployees()
          })
          .catch((error) => {
            console.error(error)
          })
      } catch (error) {
        console.error('Error in deleteLead:', error)
      }
    },
    async refreshEmployee(newUser) {
      this.rightDrawer = false
      await this.importEmployees()
      if (newUser.user_id) {
        try {
          const params = {
            skipHierarchy: 'true',
            userId: newUser.user_id
          }

          const emp = await this.$axios.get(`workforce/v2/users`, { params })
          const empId = emp.data?.users[0]?._id
          await this.$axios.patch(`workforce/users/${empId}?action=UPDATE_REPORTING_TO`, {
            reportesToId: newUser.manager?._id || null
          })
          // UPDATE_USER_INFO
          if (newUser.manager?.branch) {
            await this.$axios.patch(`workforce/users/${empId}?action=UPDATE_USER_INFO`, {
              userInfo: { branch: newUser.manager?.branch }
            })
          }
          if (newUser.selectedRole?._id) {
            const payload = {
              wfmRoleId: newUser.selectedRole?._id
            }
            this.$axios.patch(`workforce/users/${empId}`, payload)
          }
          await this.fetchEmployees()
        } catch (error) {
          console.error(assignEmployeeError, error)
          this.$toast.error(assignEmployeeError)
        }
      }
    },
    cancelDrawer() {
      this.rightDrawer = false
    },
    userFullName(item) {
      return fullName(item)
    },
    isSelected(employee) {
      return this.selectedEmployeeIds.has(employee._id)
    },

    // Func for updated selected array with employee data
    updateSelected(employee) {
      if (this.selectedEmployeeIds.has(employee._id)) {
        this.selectedEmployeeIds.delete(employee._id)
        // Also remove from selected array if present
        const index = this.selected.findIndex((e) => e._id === employee._id)
        if (index !== -1) {
          this.selected.splice(index, 1)
        }
      } else {
        this.selectedEmployeeIds.add(employee._id)
        // Add to selected array if not already present
        if (!this.selected.some((e) => e._id === employee._id)) {
          this.selected.push(employee)
        }
      }
    },
    async openMap() {
      try {
        if (this.selected.length === 0) {
          this.$toast.error('Please select at least one employee')
          return
        }
        this.mapDialog = true
        const filterSelectedUser = this.selected.filter((el) => {
          return el.roamUserId
        })
        const selectedUserRoamId = filterSelectedUser.map((user) => {
          return user.roamUserId
        })
        const payload = {
          roamUserIds: selectedUserRoamId
        }
        const response = await this.$axios.post('/workforce/users/live-location', payload)
        this.userLiveLocation = response.data?.data
      } catch (error) {
        console.log(error)
        this.errorMessage = error.response.data.message || error
        this.$toast.error(this.errorMessage)
      }
    },
    async fetchTeams() {
      try {
        const response = await this.$axios.get('/workforce/teams')
        this.teams = response.data.teams
      } catch (error) {
        console.error('Error fetching teams:', error)
      }
    },
    async fetchBranches() {
      try {
        const response = await this.$axios.get('/workforce/branches')
        this.branches = response.data.branches
      } catch (error) {
        console.error('Error fetching bracnhes:', error)
      }
    },
    handleShowDialog(label) {
      if (this.selected.length === 0) {
        this.$toast.error('Please select at least one employee')
        return
      }
      this.dialogLable = label
      this.showDialog = !this.showDialog
      if (this.showDialog === false) {
        this.fetchEmployees()
        this.selected = []
        this.selectedEmployeeIds.clear()
      }
    },
    getEmployeeTypes() {
      this.employeeTypes = []
      this.$axios.get('/ext/employee-types').then((res) => {
        for (let item of res.data) {
          this.employeeTypes.push({ name: item })
        }
      })
    },
    handleSelectedPlaceCurrent(place) {
      this.editEmp.addressLine1 = place.formatted_address
      this.selectedAddress = place.formatted_address
      this.editEmp.city = place.city
      this.editEmp.state = place.state
      this.editEmp.country = place.country
      this.editEmp.pinCode = place.pincode
    },
    updateSelectedCount() {
      this.selectedCount = [
        this.selectedTeamName,
        this.selectedRoleFilter,
        this.selectedStatus
      ].filter((item) => item !== null && item !== '').length
    },
    async toggleEmployeeStatus(employee) {
      try {
        employee.status = employee.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
        await this.updateEmployeeDetails(employee)
        this.$toast.success('Employee status updated successfully!')
      } catch (error) {
        console.error('Error updating employee status:', error)
        this.$toast.error('Failed to update employee status.')
      }
    },

    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('EMPLOYEE_MANAGEMENT', feature)
    }
  },
  async mounted() {
    await this.fetchRoles()
    await this.fetchManager()
    await this.getConfig()
    await this.fetchTeams()
    await this.getEmployeeTypes()
    await this.fetchBranches()
    this.isLoad = false
  }
}
</script>

<style scoped>
.filter-row {
  visibility: visible;
  max-height: 100px;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.hidden {
  max-height: 0;
  visibility: hidden;
}

.fixed-width-column {
  font-size: 14px;
  width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.even-row {
  background-color: #e1dbdb;
  /* Your even row background color */
}

.odd-row {
  background-color: #ffffff;
  /* Your odd row background color */
}

.v-data-table >>> .v-data-table__checkbox {
  padding-left: 5px;
}

.warning-text {
  color: #fb8c00;
}

.custom-checkbox >>> .v-input--selection-controls__ripple {
  display: none;
}

.custom-checkbox:hover >>> .v-input--selection-controls__input .v-icon {
  color: #2a83ff !important;
}

.v-data-table >>> .v-data-table__wrapper > table > tbody > tr > td > .v-data-table__checkbox {
  text-align: center;
}

.wfm-emp-card {
  box-shadow: none !important;
  border: 1px solid #cdced0 !important;
}

.wfm-search-fields.v-text-field--outlined > .v-input__control > .v-input__slot {
  min-height: 40px !important;
}

.custom-expansion-panels >>> .v-expansion-panel {
  box-shadow: none !important;
}
.custom-expansion-panels >>> .v-expansion-panel-header {
  border: none;
}
.custom-expansion-panels >>> .v-expansion-panel-content {
  padding: 0;
}

.v-expansion-panel {
  box-shadow: none !important;
}

/* Remove shadow from the header as well */
.v-expansion-panel-header {
  box-shadow: none !important;
}

/* Remove border if needed */
.v-expansion-panel-header {
  border: none !important;
}

/* Ensure that content padding is set to your needs */
.v-expansion-panel-content {
  padding: 16px !important; /* Adjust padding as needed */
}

/* Optional: Ensure background color is consistent */
.v-expansion-panel-header,
.v-expansion-panel-content {
  background-color: #fff; /* Set background color if needed */
}
.unassignedOptions {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}
.v-list {
  padding-top: 0px !important;
}
.text-green-500 {
  color: #4caf50;
}
.text-red-500 {
  color: #f44336;
}
.selected-card {
  border: 2px solid #2a83ff !important;
}
.highlighted {
  background: #21427d;
  color: white !important;
}
</style>
