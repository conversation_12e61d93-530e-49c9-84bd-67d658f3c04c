<template>
  <div class="workflow-steps">
    <!-- <v-card flat class="mb-6"> -->
    <div class="flex space-between p-4">
      <v-text-field
        v-model="filterWorkflow"
        label="Search workflow"
        solo
        flat
        hide-details
        clearable
        outlined
        dense
        prepend-inner-icon="mdi-magnify"
        @input="handleInput"
        class="mr-4"
      ></v-text-field>
      <v-btn color="primary" @click="openWorkflowDialog()">
        <v-icon left>mdi-plus</v-icon>
        Add
      </v-btn>
    </div>

    <v-data-table
      :headers="headers"
      :items="workflowTemplates"
      :loading="loading"
      :options.sync="options"
      :server-items-length="totalItems"
      :footer-props="{
        'items-per-page-options': [5, 10, 15, 30]
      }"
      class="px-7 pt-4 text-sm"
    >
      <template v-slot:item.name="{ item }">
        <td class="px-4 py-6 font-semibold">
          {{ item.name }}
        </td>
      </template>
      <template v-slot:item.type="{ item }">
        <td class="px-4 py-6 font-semibold">
          {{ formatType(item.type) }}
        </td>
      </template>

      <template v-slot:item.stepsCount="{ item }">
        <td class="px-4 py-4 font-semibold">{{ item.steps ? item.steps.length : 0 }} steps</td>
      </template>

      <!-- <template v-slot:item.lateFee="{ item }">
        <td class="px-4 py-4 font-semibold">
          <span
            v-if="
              item.type === 'EMI_LEVEL' &&
              item.lateFeeValue !== null &&
              item.lateFeeValue !== undefined
            "
          >
            {{ formatLateFee(item) }}
          </span>
          <span v-else class="text--secondary">N/A</span>
        </td>
      </template> -->

      <template v-slot:item.isActive="{ item }">
        <td class="px-4 py-4 font-semibold">
          <v-chip small outlined label :color="statusColor(item.isActive).textColor">
            {{ item.isActive ? 'Active' : 'Inactive' }}
          </v-chip>
        </td>
      </template>

      <!-- <template v-slot:item.actions="{ item }">
        <td class="px-4 py-4 font-semibold">
          <v-btn icon small @click="openWorkflowDialog(item)">
            <v-icon small>mdi-pencil</v-icon>
          </v-btn>
          <v-btn icon small @click="viewWorkflowSteps(item)">
            <v-icon small>mdi-eye</v-icon>
          </v-btn>
          <v-btn icon small @click="toggleWorkflowStatus(item)">
            <v-icon small>{{ item.isActive ? 'mdi-close' : 'mdi-check' }}</v-icon>
          </v-btn>
        </td>
      </template> -->
      <template v-slot:item.action="{ item }">
        <td class="px-4 py-6">
          <action-button
            :item="item"
            :canEdit="true"
            :canWrite="true"
            :canDelete="true"
            :handleEdit="() => openWorkflowDialog(item)"
            :addTaskAction="() => viewWorkflowSteps(item)"
            :handleDelete="() => deleteWorkflowStatus(item)"
            :showAddIcon="'mdi-eye'"
            :plusIconText="'View Steps'"
            :showAddTaskButton="true"
          />
        </td>
      </template>
    </v-data-table>
    <!-- </v-card> -->

    <!-- Workflow Template Dialog -->
    <v-dialog v-model="workflowDialog" max-width="900px" persistent>
      <v-card class="workflow-form-card" flat>
        <!-- Header -->
        <div class="form-header">
          <h3 class="form-title">
            {{ editingWorkflow._id ? 'Edit Workflow Template' : 'Add Workflow Template' }}
          </h3>
          <v-btn icon @click="closeWorkflowDialog">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>

        <v-divider></v-divider>

        <!-- Form Content -->
        <div class="form-content">
          <v-form ref="workflowForm" v-model="workflowFormValid" lazy-validation>
            <!-- Basic Information Section -->
            <div class="form-section">
              <h4 class="section-title">
                <v-icon color="primary" class="mr-2">mdi-information-outline</v-icon>
                Basic Information
              </h4>

              <div class="section-content">
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="editingWorkflow.name"
                      label="Template Name"
                      :rules="[(v) => !!v || 'Name is required']"
                      outlined
                      dense
                      hide-details
                      prepend-inner-icon="mdi-format-title"
                      placeholder="Enter template name"
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12" md="6">
                    <v-select
                      v-model="editingWorkflow.type"
                      label="Template Type"
                      :items="workflowTypes"
                      item-text="text"
                      item-value="value"
                      :rules="[(v) => !!v || 'Type is required']"
                      outlined
                      dense
                      hide-details
                      prepend-inner-icon="mdi-tag-outline"
                      placeholder="Select template type"
                    ></v-select>
                  </v-col>

                  <!-- <v-col cols="12" md="6">
                    <v-switch
                      v-model="editingWorkflow.isActive"
                      label="Active"
                      color="primary"
                      hide-details
                    ></v-switch>
                  </v-col> -->
                </v-row>
              </div>
            </div>

            <!-- Workflow Steps Section -->
            <div class="form-section">
              <div class="section-title-with-action">
                <h4 class="section-title">
                  <v-icon color="primary" class="mr-2">mdi-timeline-outline</v-icon>
                  Workflow Steps
                </h4>
              </div>

              <div class="section-content">
                <v-expansion-panels v-model="expandedStepPanel" multiple class="workflow-panels">
                  <v-expansion-panel
                    v-for="(step, index) in editingWorkflow.steps"
                    :key="index"
                    class="workflow-step-panel"
                  >
                    <v-expansion-panel-header class="workflow-panel-header">
                      <div class="workflow-header-content">
                        <div class="step-info">
                          <span class="step-number">{{ index + 1 }}</span>
                          <span class="step-name">{{ step.name || `New Step` }}</span>
                          <v-chip
                            x-small
                            class="status-chip"
                            :color="step.isRequired ? 'success' : 'grey'"
                            text-color="white"
                          >
                            {{ step.isRequired ? 'Required' : 'Optional' }}
                          </v-chip>
                          <v-chip
                            x-small
                            v-if="step.emiFrequency"
                            class="status-chip ml-1"
                            color="info"
                            text-color="white"
                          >
                            {{ step.emiFrequency }} days
                          </v-chip>
                        </div>
                        <v-btn icon x-small @click.stop="removeStep(index)" class="remove-step-btn">
                          <v-icon small>mdi-close</v-icon>
                        </v-btn>
                      </div>
                    </v-expansion-panel-header>

                    <v-expansion-panel-content class="workflow-panel-content">
                      <v-row>
                        <!-- <v-col cols="12" md="6">
                          <v-text-field
                            v-model="step.stepId"
                            label="Step ID"
                            :rules="[(v) => !!v || 'Step ID is required']"
                            outlined
                            dense
                            hide-details
                            prepend-inner-icon="mdi-identifier"
                          ></v-text-field>
                        </v-col> -->

                        <v-col cols="12">
                          <v-autocomplete
                            v-model="step.name"
                            label="Step Name"
                            :items="workflowStepOptions"
                            :rules="[(v) => !!v || 'Step Name is required']"
                            outlined
                            dense
                            hide-details
                            prepend-inner-icon="mdi-format-title"
                            placeholder="Select a workflow step"
                            @change="initializePredefinedRequirements(step)"
                          ></v-autocomplete>
                        </v-col>

                        <v-col cols="12" md="6" v-if="editingWorkflow.type === 'EMI_LEVEL'">
                          <v-text-field
                            v-model.number="step.emiFrequency"
                            label="EMI Frequency (in days)"
                            type="number"
                            outlined
                            dense
                            hide-details
                            prepend-inner-icon="mdi-calendar-clock"
                            placeholder="Enter EMI frequency"
                          ></v-text-field>
                        </v-col>

                        <!-- <v-col cols="12" md="6">
                          <v-text-field
                            v-model.number="step.order"
                            label="Order"
                            type="number"
                            :rules="[(v) => !!v || 'Order is required']"
                            outlined
                            dense
                            hide-details
                            prepend-inner-icon="mdi-order-numeric-ascending"
                          ></v-text-field>
                        </v-col> -->

                        <!-- <v-col cols="12" md="6">
                          <v-switch
                            v-model="step.isRequired"
                            label="Required"
                            color="primary"
                            hide-details
                          ></v-switch>
                        </v-col> -->

                        <!-- Requirements Section -->
                        <v-col cols="12" class="p-0">
                          <div class="requirements-section">
                            <div class="section-title-with-action">
                              <h5 class="requirements-title mb-0">Requirements</h5>
                            </div>

                            <!-- Pre-defined Requirements -->
                            <div
                              v-if="getPredefinedRequirements(step.name).length > 0"
                              class="predefined-requirements mb-3"
                            >
                              <div class="text-caption text--secondary">
                                Pre-defined Requirements:
                              </div>
                              <v-card
                                v-for="(req, reqIndex) in getPredefinedRequirements(step.name)"
                                :key="`predefined-${index}-${reqIndex}`"
                                outlined
                                class="requirement-card mb-2"
                              >
                                <div class="flex justify-between pa-3">
                                  <div class="d-flex align-center justify-space-between">
                                    <span class="font-weight-medium">{{ req }}</span>
                                  </div>
                                  <div class="flex justify-end">
                                    <v-checkbox
                                      v-model="step.predefinedRequirements[req].isRequired"
                                      label="Required"
                                      color="primary"
                                      dense
                                      class="mt-0 pt-0"
                                      hide-details
                                    ></v-checkbox>
                                    <v-divider vertical class="mx-2"></v-divider>
                                    <v-checkbox
                                      v-model="step.predefinedRequirements[req].isDocument"
                                      label="Document"
                                      color="primary"
                                      dense
                                      class="mt-0 pt-0"
                                      hide-details
                                    ></v-checkbox>
                                  </div>
                                </div>
                              </v-card>
                            </div>

                            <!-- Custom Requirements -->
                            <div
                              v-if="step.customRequirements && step.customRequirements.length > 0"
                              class="custom-requirements"
                            >
                              <div class="text-caption text--secondary mb-2">
                                Custom Requirements:
                              </div>
                              <v-card
                                v-for="(req, reqIndex) in step.customRequirements"
                                :key="`custom-${index}-${reqIndex}`"
                                outlined
                                class="requirement-card mb-2 position-relative"
                              >
                                <!-- Delete button positioned at top right -->
                                <v-btn
                                  icon
                                  x-small
                                  color="error"
                                  @click="removeCustomRequirement(index, reqIndex)"
                                  class="delete-req-btn-top-right"
                                >
                                  <v-icon x-small>mdi-close</v-icon>
                                </v-btn>

                                <div class="pa-3">
                                  <div class="flex justify-between">
                                    <v-text-field
                                      v-model="req.name"
                                      label="Requirement Name"
                                      outlined
                                      dense
                                      hide-details
                                    ></v-text-field>
                                    <div class="flex justify-end">
                                      <v-checkbox
                                        v-model="req.isRequired"
                                        label="Required"
                                        color="primary"
                                        dense
                                        hide-details
                                        class="mt-0 pt-0"
                                      ></v-checkbox>
                                      <v-divider vertical class="mx-2"></v-divider>
                                      <v-checkbox
                                        v-model="req.isDocument"
                                        label="Document"
                                        color="primary"
                                        dense
                                        class="mt-0 pt-0"
                                        hide-details
                                      ></v-checkbox>
                                    </div>
                                  </div>
                                </div>
                              </v-card>
                            </div>
                            <div class="flex justify-end" v-if="step.name">
                              <v-btn
                                x-small
                                color="primary"
                                outlined
                                @click="addCustomRequirement(index)"
                                class="add-step-btn"
                              >
                                <v-icon x-small class="mr-1">mdi-plus</v-icon>
                                Add Custom
                              </v-btn>
                            </div>

                            <div
                              v-if="
                                getPredefinedRequirements(step.name).length === 0 &&
                                (!step.customRequirements || step.customRequirements.length === 0)
                              "
                              class="no-requirements-message mt-2"
                            >
                              <v-icon small color="grey lighten-1"
                                >mdi-clipboard-list-outline</v-icon
                              >
                              <p class="text-caption">
                                No requirements available. Click "Add Custom" to create a custom
                                requirement.
                              </p>
                            </div>
                          </div>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>

                <div v-if="editingWorkflow.steps.length === 0" class="no-steps-message">
                  <v-icon large color="grey lighten-1">mdi-timeline-plus-outline</v-icon>
                  <p>No workflow steps added yet. Click "Add Step" to create your first step.</p>
                </div>
                <div class="flex justify-end mt-2">
                  <v-btn
                    small
                    color="primary"
                    outlined
                    @click="addStep"
                    class="add-step-btn"
                    v-if="editingWorkflow.type"
                  >
                    <v-icon small class="mr-1">mdi-plus</v-icon>
                    Add Step
                  </v-btn>
                </div>
              </div>
            </div>
          </v-form>
        </div>

        <!-- Form Actions -->
        <v-divider></v-divider>

        <div class="form-actions">
          <v-btn text @click="closeWorkflowDialog" class="cancel-btn"> Cancel </v-btn>
          <v-btn
            color="primary"
            @click="saveWorkflowTemplate"
            :loading="saving"
            :disabled="saving"
            class="save-btn"
          >
            <v-icon left>mdi-content-save</v-icon>
            {{ editingWorkflow._id ? 'Update' : 'Save' }}
          </v-btn>
        </div>
      </v-card>
    </v-dialog>

    <!-- View Steps Dialog -->
    <v-dialog v-model="viewStepsDialog" max-width="1000px">
      <v-card class="workflow-form-card" flat>
        <div class="form-header">
          <h3 class="form-title">{{ selectedWorkflow.name }} Steps</h3>
          <v-btn icon @click="viewStepsDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>

        <v-divider></v-divider>

        <div class="form-content">
          <v-timeline dense class="workflow-timeline">
            <v-timeline-item
              v-for="(step, index) in selectedWorkflow.steps"
              :key="index"
              :color="step.isRequired ? 'primary' : 'grey'"
              small
              class="timeline-item"
            >
              <template v-slot:opposite>
                <span class="timeline-order">{{ index + 1 }}</span>
              </template>
              <div class="timeline-content">
                <div class="d-flex align-center">
                  <strong class="step-title">{{ step.name }}</strong>
                  <v-chip
                    x-small
                    v-if="step.isRequired"
                    color="primary"
                    text-color="white"
                    class="ml-2"
                  >
                    Required
                  </v-chip>
                  <v-chip x-small v-else color="grey" text-color="white" class="ml-2">
                    Optional
                  </v-chip>
                  <v-chip
                    x-small
                    v-if="step.emiFrequency"
                    color="info"
                    text-color="white"
                    class="ml-2"
                  >
                    {{ step.emiFrequency }} days
                  </v-chip>
                </div>
                <!-- Requirements -->
                <div
                  v-if="step.requirements && step.requirements.length > 0"
                  class="mt-3 requirements-container"
                >
                  <div class="text-subtitle-2 mb-2">Requirements:</div>
                  <div class="requirements-chips">
                    <v-chip
                      v-for="(req, reqIndex) in step.requirements"
                      :key="`view-req-${index}-${reqIndex}`"
                      class="mr-2 mb-2"
                      small
                      :color="req.isRequired ? 'primary' : 'grey'"
                      :outlined="!req.isRequired"
                    >
                      {{ req.name }}
                      <v-icon v-if="req.isDocument" small class="ml-1">mdi-file-document</v-icon>
                    </v-chip>
                  </div>
                </div>
              </div>
            </v-timeline-item>
          </v-timeline>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import ActionButton from '@/components/ActionButton.vue'
import { debounce } from '@/utils/common'
import CombinedInput from '@/components/common/CombinedInput.vue'
import { chipStatusColors } from '@/utils/workforce/statusColors'
import { workFlowStepsOptions } from '@/utils/workforce/constants'
export default {
  name: 'WorkflowSteps',
  components: {
    ActionButton
    // CombinedInput
  },
  data() {
    return {
      headers: [
        { text: 'Template Name', value: 'name' },
        { text: 'Type', value: 'type' },
        { text: 'Steps', value: 'stepsCount' },
        // { text: 'Late Fee', value: 'lateFee' },
        { text: 'Status', value: 'isActive' },
        { text: 'Actions', value: 'action', sortable: false }
      ],
      workflowTemplates: [],
      loading: false,
      saving: false,
      totalItems: 0,
      options: {
        page: 1,
        itemsPerPage: 10
      },

      // Dialog controls
      workflowDialog: false,
      viewStepsDialog: false,

      // Form data
      workflowFormValid: false,
      editingWorkflow: {
        name: '',
        type: '',
        isActive: true,
        steps: []
      },
      selectedWorkflow: {},
      expandedStepPanel: [],
      filterWorkflow: '',

      // Dropdown options
      workflowTypes: [
        { text: 'Loan', value: 'LOAN_LEVEL' },
        { text: 'Disbursement', value: 'DISBURSEMENT_LEVEL' },
        { text: 'EMI', value: 'EMI_LEVEL' }
      ]
    }
  },

  computed: {
    workflowStepOptions() {
      // Determine which workflow type steps to show
      let workflowTypeKey = 'loan' // default
      if (this.editingWorkflow.type === 'DISBURSEMENT_LEVEL') {
        workflowTypeKey = 'disbursement'
      } else if (this.editingWorkflow.type === 'EMI_LEVEL') {
        workflowTypeKey = 'emi'
      }

      // Get the steps for the selected workflow type
      const stepsForType = workFlowStepsOptions[workflowTypeKey]
      if (!stepsForType) {
        return []
      }

      // Create grouped options with headers and items
      const groupedOptions = []

      Object.keys(stepsForType).forEach((categoryName) => {
        // Add header
        groupedOptions.push({
          header: categoryName
        })

        // Add step options under this category
        const categorySteps = stepsForType[categoryName]
        Object.keys(categorySteps).forEach((stepName) => {
          groupedOptions.push({
            text: stepName,
            value: stepName
          })
        })
      })

      return groupedOptions
    }
  },

  mounted() {
    this.fetchWorkflowTemplates()
  },

  watch: {
    options: {
      handler() {
        this.fetchWorkflowTemplates()
      },
      deep: true
    }
  },

  methods: {
    handleInput: debounce(async function () {
      this.options.page = 1
      await this.fetchWorkflowTemplates()
    }, 800),
    async fetchWorkflowTemplates() {
      try {
        this.loading = true
        const { page, itemsPerPage } = this.options

        const params = {
          page,
          limit: itemsPerPage
        }

        if (this.filterWorkflow) {
          params.search = this.filterWorkflow
        }
        const response = await this.$axios.get('/workforce/collection/workflow-templates', {
          params
        })

        this.workflowTemplates = response.data.templates || []
        this.totalItems = response.data.pagination?.totalCount
      } catch (error) {
        console.error('Error fetching workflow templates:', error)
        this.$toast.error('Failed to load workflow templates')
      } finally {
        this.loading = false
      }
    },

    openWorkflowDialog(workflow = null) {
      if (workflow) {
        // Edit existing workflow - convert new structure to old structure for editing
        this.editingWorkflow = JSON.parse(JSON.stringify(workflow))

        // Convert the new structure to the old structure for editing
        if (this.editingWorkflow.steps) {
          this.editingWorkflow.steps = this.editingWorkflow.steps.map((step) => {
            const convertedStep = {
              stepId: step.stepId,
              name: step.name,
              isRequired: step.isRequired,
              emiFrequency: step.emiFrequency,
              predefinedRequirements: {},
              customRequirements: []
            }

            // Convert requirements back to predefined and custom
            if (step.requirements && step.requirements.length > 0) {
              // Get predefined requirements for this step based on workflow type
              let workflowTypeKey = 'loan' // default
              if (workflow.type === 'DISBURSEMENT_LEVEL') {
                workflowTypeKey = 'disbursement'
              } else if (workflow.type === 'EMI_LEVEL') {
                workflowTypeKey = 'emi'
              }

              const stepsForType = workFlowStepsOptions[workflowTypeKey]
              let predefinedReqs = []

              // Find the step in any category
              for (const categoryName in stepsForType) {
                const categorySteps = stepsForType[categoryName]
                if (categorySteps[step.name]) {
                  predefinedReqs = categorySteps[step.name] || []
                  break
                }
              }

              step.requirements.forEach((req) => {
                if (predefinedReqs.includes(req.name)) {
                  // This is a predefined requirement
                  convertedStep.predefinedRequirements[req.name] = {
                    isRequired: req.isRequired,
                    isDocument: req.isDocument
                  }
                } else {
                  // This is a custom requirement
                  convertedStep.customRequirements.push({
                    name: req.name,
                    isRequired: req.isRequired,
                    isDocument: req.isDocument
                  })
                }
              })
            }

            return convertedStep
          })
        }
      } else {
        // Create new workflow
        this.editingWorkflow = {
          name: '',
          type: '',
          isActive: true,
          steps: []
        }
      }

      // Expand first step panel if there are steps
      this.expandedStepPanel = this.editingWorkflow.steps.length > 0 ? [0] : []

      this.workflowDialog = true
    },
    statusColor(isActive) {
      if (typeof isActive === 'boolean') {
        const status = isActive ? 'active' : 'inactive'
        const colorInfo = chipStatusColors[status]
        return colorInfo
      }

      const colorInfo = chipStatusColors[isActive?.toLowerCase()]
      return colorInfo
    },

    closeWorkflowDialog() {
      this.workflowDialog = false
      this.editingWorkflow = {
        name: '',
        type: '',
        isActive: true,
        steps: []
      }
    },

    addStep() {
      const newStepOrder = this.editingWorkflow.steps.length + 1
      this.editingWorkflow.steps.push({
        stepId: `STEP_${newStepOrder}`,
        name: '',
        isRequired: true,
        emiFrequency: null,
        predefinedRequirements: {},
        customRequirements: []
      })

      // Expand the newly added step panel
      this.expandedStepPanel = [...this.expandedStepPanel, this.editingWorkflow.steps.length - 1]
    },

    removeStep(index) {
      this.editingWorkflow.steps.splice(index, 1)

      // Update expanded panels
      this.expandedStepPanel = this.expandedStepPanel
        .filter((i) => i !== index)
        .map((i) => (i > index ? i - 1 : i))
    },

    addCustomRequirement(stepIndex) {
      if (!this.editingWorkflow.steps[stepIndex].customRequirements) {
        this.$set(this.editingWorkflow.steps[stepIndex], 'customRequirements', [])
      }

      this.editingWorkflow.steps[stepIndex].customRequirements.push({
        name: '',
        isRequired: true,
        isDocument: false
      })
    },

    removeCustomRequirement(stepIndex, reqIndex) {
      this.editingWorkflow.steps[stepIndex].customRequirements.splice(reqIndex, 1)
    },

    getPredefinedRequirements(stepName) {
      if (!stepName) return []

      // Determine which workflow type steps to show
      let workflowTypeKey = 'loan' // default
      if (this.editingWorkflow.type === 'DISBURSEMENT_LEVEL') {
        workflowTypeKey = 'disbursement'
      } else if (this.editingWorkflow.type === 'EMI_LEVEL') {
        workflowTypeKey = 'emi'
      }

      const stepsForType = workFlowStepsOptions[workflowTypeKey]

      // Find the step in any category
      for (const categoryName in stepsForType) {
        const categorySteps = stepsForType[categoryName]
        if (categorySteps[stepName]) {
          return categorySteps[stepName] || []
        }
      }

      return []
    },

    initializePredefinedRequirements(step) {
      const predefinedReqs = this.getPredefinedRequirements(step.name)
      if (!step.predefinedRequirements) {
        this.$set(step, 'predefinedRequirements', {})
      }

      predefinedReqs.forEach((req) => {
        if (!step.predefinedRequirements[req]) {
          this.$set(step.predefinedRequirements, req, {
            isRequired: true,
            isDocument: false
          })
        }
      })
    },

    async saveWorkflowTemplate() {
      if (!this.$refs.workflowForm.validate()) {
        this.$toast.error('Please fill all required fields')
        return
      }

      try {
        this.saving = true

        const payload = {
          name: this.editingWorkflow.name,
          type: this.editingWorkflow.type,
          isActive: this.editingWorkflow.isActive,
          steps: this.editingWorkflow.steps.map((step) => {
            // Collect requirements for this specific step
            const stepRequirements = []

            // Add predefined requirements for this step
            if (step.predefinedRequirements) {
              Object.keys(step.predefinedRequirements).forEach((reqName) => {
                const reqData = step.predefinedRequirements[reqName]
                stepRequirements.push({
                  name: reqName,
                  isRequired: reqData.isRequired,
                  isDocument: reqData.isDocument
                })
              })
            }

            // Add custom requirements for this step
            if (step.customRequirements && step.customRequirements.length > 0) {
              step.customRequirements.forEach((req) => {
                if (req.name && req.name.trim()) {
                  stepRequirements.push({
                    name: req.name,
                    isRequired: req.isRequired,
                    isDocument: req.isDocument
                  })
                }
              })
            }

            return {
              stepId: step.stepId,
              name: step.name,
              isRequired: step.isRequired,
              emiFrequency: step.emiFrequency || null,
              requirements: stepRequirements,
              assignmentRules: {
                autoAssign: false
              },
              conditions: []
            }
          })
        }

        let response
        if (this.editingWorkflow._id) {
          // Update existing template
          response = await this.$axios.put(
            `/workforce/collection/workflow-templates/${this.editingWorkflow._id}`,
            payload
          )
        } else {
          // Create new template
          response = await this.$axios.post('/workforce/collection/workflow-templates', payload)
        }

        this.$toast.success(
          `Workflow template ${this.editingWorkflow._id ? 'updated' : 'created'} successfully`
        )
        this.closeWorkflowDialog()
        this.fetchWorkflowTemplates()
      } catch (error) {
        console.error('Error saving workflow template:', error)
        const errorMessage = error?.response?.data?.message || 'Failed to save workflow template'
        this.$toast.error(errorMessage)
      } finally {
        this.saving = false
      }
    },

    viewWorkflowSteps(workflow) {
      this.selectedWorkflow = workflow
      this.viewStepsDialog = true
    },

    async deleteWorkflowStatus(workflow) {
      try {
        await this.$axios.delete(`/workforce/collection/workflow-templates/${workflow._id}`)
        const index = this.workflowTemplates.findIndex((w) => w._id === workflow._id)
        if (index !== -1) {
          this.workflowTemplates.splice(index, 1)
        }
        this.$toast.success('Workflow template deleted successfully')
      } catch (error) {
        console.error('Error deleting workflow:', error)
        this.$toast.error('Failed to delete workflow template')
      }
    },

    // Helper methods
    formatType(type) {
      switch (type) {
        case 'LOAN_LEVEL':
          return 'Loan'
        case 'DISBURSEMENT_LEVEL':
          return 'Disbursement'
        case 'EMI_LEVEL':
          return 'EMI'
        default:
          return type
      }
    }
  }
}
</script>

<style scoped>
.workflow-steps {
  padding: 20px;
}

.workflow-form-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05) !important;
  margin: 0 auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #f8f9fa;
}

.form-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #21427d;
  margin: 0;
}

.form-content {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1rem;
  font-weight: 500;
  color: #21427d;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-content {
  padding-left: 8px;
}

.add-step-btn {
  text-transform: none;
  font-weight: 500;
}

.workflow-panels {
  background: transparent;
}

.workflow-step-panel {
  margin-bottom: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: none !important;
}

.workflow-step-panel:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.workflow-panel-header {
  padding: 12px 16px;
  min-height: 48px;
  background-color: #f8f9fa;
}

.workflow-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.step-info {
  display: flex;
  align-items: center;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #21427d;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  margin-right: 12px;
}

.step-name {
  font-weight: 500;
  margin-right: 12px;
}

.status-chip {
  font-size: 0.625rem;
  height: 20px !important;
}

.workflow-panel-content {
  padding: 16px;
}

.no-steps-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #e0e0e0;
}

.no-steps-message p {
  margin-top: 16px;
  color: #6c757d;
  text-align: center;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  background-color: #f8f9fa;
}

.cancel-btn {
  margin-right: 12px;
}

.save-btn {
  min-width: 120px;
  text-transform: none;
  font-weight: 500;
}

.requirements-section {
  border-top: 1px dashed #e0e0e0;
  padding-top: 6px;
}

.requirements-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #21427d;
}

.requirement-card {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.requirement-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #21427d;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  margin-right: 8px;
}

.requirement-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.no-requirements-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #e0e0e0;
}

.no-requirements-message p {
  margin-top: 8px;
  color: #6c757d;
  text-align: center;
  font-size: 0.8rem;
}

/* Dark mode support */
:deep(.theme--dark) .form-header,
:deep(.theme--dark) .form-actions,
:deep(.theme--dark) .workflow-panel-header,
:deep(.theme--dark) .requirement-header {
  background-color: #2a2a2a;
}

:deep(.theme--dark) .form-title {
  color: #8cb6e1;
}

:deep(.theme--dark) .section-title,
:deep(.theme--dark) .requirements-title {
  color: #8cb6e1;
}

:deep(.theme--dark) .no-steps-message,
:deep(.theme--dark) .no-requirements-message {
  background-color: #2a2a2a;
}

/* Timeline view styles */
.workflow-timeline {
  padding: 12px;
}

.timeline-item {
  margin-bottom: 16px;
}

.timeline-order {
  font-weight: 600;
  font-size: 1rem;
  color: #21427d;
}

.timeline-content {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.step-title {
  font-size: 1rem;
  color: #21427d;
}

.step-description {
  font-size: 0.875rem;
  color: #6c757d;
}

.requirements-container {
  border-top: 1px dashed #e0e0e0;
  padding-top: 12px;
}

.requirements-chips,
.generic-steps-chips {
  display: flex;
  flex-wrap: wrap;
}

.generic-steps-container {
  border-top: 1px dashed #e0e0e0;
  padding-top: 12px;
}

/* Fix v-timeline alignment */
:deep(.v-timeline--dense .v-timeline-item__body) {
  max-width: calc(100% - 80px);
}

:deep(.v-timeline--dense .v-timeline-item__opposite) {
  flex: none;
  max-width: 60px;
  text-align: center;
}

:deep(.v-timeline--dense:before) {
  left: 60px;
}

:deep(.v-timeline-item__dot--small) {
  left: 60px;
}

/* Dark mode support for timeline */
:deep(.theme--dark) .timeline-content {
  background-color: #2a2a2a;
  border-color: #424242;
}

:deep(.theme--dark) .timeline-order,
:deep(.theme--dark) .step-title {
  color: #8cb6e1;
}

:deep(.theme--dark) .step-description {
  color: #adb5bd;
}

:deep(.theme--dark) .requirements-container,
:deep(.theme--dark) .generic-steps-container {
  border-color: #424242;
}

/* Delete button styles */
.delete-req-btn-top-right {
  position: absolute;
  top: -3px;
  right: 0px;
  z-index: 11;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.delete-req-btn-top-right:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.requirement-card {
  position: relative;
}

/* Workflow step dropdown styles */
.workflow-step-header {
  font-weight: 800;
  font-size: 0.875rem;
  color: #21427d;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  margin: 0 -16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.workflow-step-item {
  padding: 4px 0;
  font-size: 0.875rem;
  color: #495057;
}

:deep(.theme--dark) .workflow-step-header {
  color: #8cb6e1;
  background-color: #2a2a2a;
  border-color: #424242;
}

:deep(.theme--dark) .workflow-step-item {
  color: #adb5bd;
}

/* Workflow info section styles */
.workflow-info-section {
  margin-bottom: 16px;
}

.workflow-info-section .v-card {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
}

:deep(.theme--dark) .workflow-info-section .v-card {
  background-color: #2a2a2a;
  border-color: #424242;
}
</style>
