<template>
  <div class="p-3">
    <div
      class="justify-end items-center my-3"
      :class="[$vuetify.breakpoint.lgAndUp ? 'flex justify-end' : 'flex-col']"
    >
      <div
        class="justify-end mr-4"
        :class="[$vuetify.breakpoint.lgAndUp ? 'flex items-center' : 'flex-col']"
      >
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : 'mr-auto']"
        >
          <v-combobox
            v-model="selectedCompany"
            :items="companyNames"
            label="Search Customer"
            placeholder="Search Customer"
            :search-input.sync="filterCustomer"
            @update:search-input="handleCompany"
            solo
            flat
            clearable
            outlined
            hide-details
            dense
            class="custom-input"
            @change="handleInput"
            @keydown.enter="selectedCompany = setDataOnEnter(companyNames)"
          >
          </v-combobox>
        </v-col>
        <v-col
          :cols="$vuetify.breakpoint.lgAndUp ? '' : '12'"
          :class="[$vuetify.breakpoint.lgAndUp ? '' : '']"
        >
          <v-autocomplete
            v-model="selectedCompanyBranch"
            :items="selectedCompanyBranches"
            :disabled="!selectedCompany"
            label="Search Customer Address"
            placeholder="Search Customer Address"
            solo
            flat
            clearable
            outlined
            hide-details
            dense
            class="custom-input"
            @keydown.enter="selectedCompanyBranch = setDataOnEnter(selectedCompanyBranches)"
          >
          </v-autocomplete>
        </v-col>
        <v-tooltip top>
          <template #activator="{ on, attrs }">
            <v-btn
              @click="toggleshowAddCustomers()"
              :color="$vuetify.theme.currentTheme.primary"
              class="ml-3 white--text"
              v-bind="attrs"
              v-show="canWrite"
              v-on="on"
            >
              Add
              <v-icon right dark>mdi-plus</v-icon>
            </v-btn>
          </template>
          <span>Add Customers</span>
        </v-tooltip>
      </div>
    </div>
    <div class="overflow-x-auto overflow-y-auto">
      <v-data-table
        :headers="headers"
        :items="filteredCustomers"
        :options.sync="options"
        :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30, 50] }"
        :server-items-length="totalItems"
        :loading="loadTable"
        class="pl-7 pr-7 pt-4"
        item-class="group"
        fixed-header
      >
        <template v-slot:item.customerName="{ item }">
          <td class="px-4 py-6 font-semibold">
            <v-text-field
              v-model="item.customerName"
              dense
              hide-details
              solo
              flat
              :readonly="editableCustomer !== item"
              @blur="editableCustomer === item ? cancelEditMode(item) : ''"
              :style="{
                'font-size': editableCustomer === item ? '14px' : 'inherit'
              }"
              :ref="
                item.locationId ? `textField_${item.locationId}` : `textField_${item.customerId}`
              "
            >
              <template v-slot:append v-if="canEdit">
                <v-tooltip top>
                  <template v-slot:activator="{ on }">
                    <v-icon
                      v-on="on"
                      v-if="editableCustomer !== item"
                      @click="editCustomerName(item)"
                      >mdi-pencil</v-icon
                    >
                    <v-icon v-on="on" @click="saveCustomerName(item)" v-else>mdi-check</v-icon>
                  </template>
                  <span v-if="editableCustomer !== item">Edit Customer Name</span>
                  <span v-else>Save Customer Name</span>
                </v-tooltip>
              </template>
            </v-text-field>
          </td>
        </template>
        <template v-slot:item.aliasName="{ item }">
          <td class="px-4 font-semibold">
            {{ item?.alias }}
          </td>
        </template>
        <template v-slot:item.customerDescription="{ item }">
          <td class="px-4 font-semibold">{{ item?.description || '-' }}</td>
        </template>
        <template v-slot:item.customerLocation="{ item }">
          <td
            class="px-4 font-semibold hover:text-blue-700 dark-hover-text cursor-pointer"
            @click="openGoogleMaps(item.address)"
          >
            {{ item.address ? getAddress(item?.address) : '' }}
          </td>
        </template>
        <template v-slot:item.customerContact="{ item }">
          <td class="px-4 font-semibold">
            <div class="flex items-center gap-2">
              <template>
                <div>
                  <span style="width: 120px; display: inline-block">
                    {{ item.primary?.name }}
                  </span>
                  <br />
                  <span style="width: 120px; display: inline-block">
                    {{ item.primary?.number }}
                  </span>
                </div>
              </template>
            </div>
          </td>
        </template>
        <template v-slot:item.customerEmail="{ item }">
          <td class="px-4 font-semibold">
            {{ item.primary?.email || '-' }}
          </td>
        </template>
        <template v-slot:item.action="{ item }">
          <td class="px-4 py-5">
            <action-button
              :item="item"
              :canEdit="false"
              :canWrite="canWrite"
              :showAddTaskButton="canWrite"
              :showAddIcon="'mdi-map-marker'"
              :plusIconText="'View Customer Address'"
              :showDeleteButton="false"
              :showEditButton="false"
              :showViewButton="true"
              :addTaskAction="() => addAddress(item)"
              :handleView="() => viewSelectedContact(item)"
              :handleDelete="() => deleteSelectedBranch(item)"
              :handleEdit="() => editSelectedBranch(item)"
            />
          </td>
        </template>
      </v-data-table>
    </div>
    <v-dialog v-model="showAddCustomer" width="auto" v-if="showAddCustomer">
      <CompanyAddress
        @cancel="toggleShowTable"
        @save="addCustomer"
        title="Add Customer"
        :companies="companies"
      />
    </v-dialog>
    <v-dialog max-width="600" v-model="contactsVisible" persistent>
      <ContactTabs
        :viewContacts="viewContacts"
        :actionContact="actionContact"
        @updateContacts="updateViewContacts"
        @closeDialog="handleContactDialog"
        :selectedBranches="branches"
      />
    </v-dialog>
    <v-dialog max-width="580" v-model="showAddAddress" persistent>
      <AddressTabs
        :branches="branches"
        @updateBranch="updateViewBranches"
        @closeDialog="handleContactDialog"
      />
    </v-dialog>
  </div>
</template>

<script>
import CompanyAddress from '@/components/Workforce/CompanyAddress.vue'
import ActionButton from '@/components/ActionButton'
import ContactTabs from '@/components/Workforce/CompanyManagement/CustomerContacts/ContactTabs'
import AddressTabs from '@/components/Workforce/CompanyManagement/CustomerAddress/AddressTabs'
import { debounce, hasPermission, sortDropdownOptions, getFullAddress } from '@/utils/common'
import permissionData from '@/utils/permissions'
import { setDataOnEnter } from '@/utils/workforce/utils'

export default {
  name: 'InventoryIn',
  layout: 'Workforce/default',
  components: {
    CompanyAddress,
    ActionButton,
    ContactTabs,
    AddressTabs
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      enableUseCase: this.$store.state.useCase,
      orgLang: this.$store.state.orgLanguage,
      contactsVisible: false,
      loadTable: true,
      editableCustomer: null,
      customers: [],
      companies: [],
      branches: [],
      totalItems: 0,
      options: { itemsPerPage: 5, page: 1 },
      customer: '',
      selectedCompanyBranch: null,
      showTable: true,
      viewContacts: [],
      filterCustomer: '',
      selectedCompany: '',
      filterLocation: '',
      actionContact: null,
      selectedCompanyId: null,
      showAddCustomer: false,
      showAddAddress: false,
      selectedCustomerData: null,
      previousName: null
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchCustomers()
      },
      deep: true
    },
    selectedCompany(newValue) {
      if (!newValue) {
        this.selectedCompanyBranch = null
      }
    }
  },
  methods: {
    setDataOnEnter,
    openGoogleMaps(address) {
      const fullAddress = getFullAddress(address)
      const encodedAddress = encodeURIComponent(fullAddress)
      const googleMapsURL = `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`
      window.open(googleMapsURL, '_blank')
    },
    toggleShowTable() {
      this.showAddCustomer = false
      this.showAddAddress = false
      this.showTable = true
      this.selectedCustomerData = null
      this.fetchCustomers()
    },
    toggleshowAddCustomers() {
      this.showAddCustomer = true
    },
    async fetchCustomers() {
      try {
        const { page, itemsPerPage } = this.options
        const params = { page, limit: itemsPerPage }
        if (this.selectedCompany) {
          params.companySearch = this.selectedCompany.text
        } else {
          params.companySearch = this.filterCustomer
        }
        this.loadTable = true
        const response = await this.$axios.get('/workforce/allcompany', {
          params
        })
        this.companies = response.data.companies
        if (this.filterCustomer) {
          this.customers = this.companies
            .map((el) => {
              return el.companyBranches?.map((location, index) => {
                const primaryContact = location.contacts[0]
                return {
                  index: index,
                  customerId: el._id,
                  locationId: location._id,
                  contacts: location.contacts,
                  customerName: el.name,
                  description: el.companyDetails,
                  alias: location.alias,
                  address: {
                    addressLine1: location.address.addressLine1 || '',
                    addressLine2: location.address.addressLine2 || '',
                    city: location.address.city || '',
                    state: location.address.state || '',
                    country: location.address.country || '',
                    pinCode: location.address.pinCode || null
                  },
                  primary: {
                    name: primaryContact
                      ? `${primaryContact.firstName} ${primaryContact.lastName}`
                      : '',
                    number: primaryContact ? primaryContact.phoneNumber : '',
                    email: primaryContact ? primaryContact.email : ''
                  }
                }
              })
            })
            .flat()
        } else {
          this.customers = this.companies?.map((el, index) => {
            const primaryContact = el.companyBranches[0]?.contacts[0]
            return {
              index: index,
              customerId: el._id,
              customerName: el.name,
              description: el.companyDetails,
              branches: el.companyBranches,
              contacts: el.companyBranches?.flatMap((branch, branchIndex) => {
                return branch.contacts.map((contact) => ({
                  ...contact,
                  index: branchIndex,
                  alias: branch.alias,
                  address: branch.address
                }))
              }),
              alias: el.companyBranches[0]?.alias,
              address: el.companyBranches[0]?.address,
              primary: {
                name: primaryContact
                  ? `${primaryContact?.firstName} ${primaryContact?.lastName}`
                  : '',
                number: primaryContact ? primaryContact?.phoneNumber : '',
                email: primaryContact ? primaryContact?.email : ''
              }
            }
          })
        }
        this.loadTable = false
        this.totalItems = response.data.pagination.totalCount
      } catch (error) {
        console.error('An error occurred:', error)
        this.loadTable = false
      }
    },
    async addCustomer(data) {
      const branchData = {
        alias: data.alias,
        address: {
          addressLine1: data.addLine1,
          addressLine2: data.addLine2,
          city: data.city,
          state: data.state,
          country: data.country,
          pinCode: data.pinCode,
          coordinates: {
            latitude: data.latitude,
            longitude: data.longitude
          }
        }
      }
      const company = {
        name: data.companyName,
        companyDetails: data.companyDetails,
        companyBranches: [branchData]
      }

      try {
        if (data.companyId && data.branchId) {
          await this.$axios.put(
            `/workforce/company/${data.companyId}/branches/${data.branchId}`,
            branchData
          )
        } else {
          const response = await this.$axios.post('/workforce/company', company)
          this.companies.push(response.data.company)
        }

        this.toggleShowTable()
        this.$toast.success('Customer added successfully')
      } catch (error) {
        this.$toast.error('Customer creation failed')
      }
    },
    cancelEditMode(item) {
      item.customerName = this.previousName
      this.editableCustomer = null
      this.previousName = null
    },
    editCustomerName(item) {
      this.previousName = item.customerName
      this.editableCustomer = item
      this.editableCustomerId = item.customerId
      this.$nextTick(() => {
        const textField =
          this.$refs[`textField_${item.locationId}` || `textField_${item.customerId}`]
        if (textField) {
          textField.focus()
        }
      })
    },
    saveCustomerName(item) {
      try {
        const payload = {
          name: item.customerName
        }

        this.$axios.put(`/workforce/company/${this.editableCustomerId}`, payload)
        this.$toast.success(`${item.customerName} updated successfully`)
        this.editableCustomer = null
      } catch (error) {
        console.log(error)
        this.$toast.error(error.message)
      }
    },
    updateViewBranches(item) {
      if (item.companyBranches) {
        this.branches = item.companyBranches.map((el) => {
          return {
            ...el,
            name: item.name,
            companyId: item._id
          }
        })
      }
      this.fetchCustomers()
    },
    updateViewContacts(item) {
      this.viewContacts = item
      this.fetchCustomers()
    },
    viewSelectedContact(item) {
      this.actionContact = item
      this.viewContacts = item.contacts
      this.branches = item.branches
      this.contactsVisible = true
    },
    addAddress(item) {
      if (item.branches) {
        this.branches = item.branches?.map((el) => {
          return {
            ...el,
            name: item.customerName,
            companyId: item.customerId
          }
        })
      } else {
        this.branches = []
        this.branches.push(item)
      }
      this.showAddAddress = true
    },
    handleInput: debounce(function () {
      this.fetchCustomers()
      this.options.page = 1
    }, 800),
    handleCompany: debounce(function () {
      this.fetchCustomers()
      this.options.page = 1
    }, 800),
    getAddress(address) {
      if (address) {
        const { addressLine1, addressLine2, city, country, pinCode, state } = address

        return [addressLine1, addressLine2, city, country, pinCode, state].join(', ') || ''
      }
      return '—'
    },
    handleContactDialog() {
      this.showAddAddress = false
      this.contactsVisible = false
      this.fetchCustomers()
    }
  },
  computed: {
    headers() {
      const headers = [
        {
          text: `${this.customerUsecase?.companyTable?.companyName}`,
          value: 'customerName',
          sortable: false,
          width: '280px'
        },
        {
          text: `${this.customerUsecase?.companyTable?.alias}`,
          value: 'aliasName',
          sortable: false,
          width: '15%'
        },
        {
          text: `${this.customerUsecase?.companyTable?.description}`,
          value: 'customerDescription',
          sortable: false,
          width: '15%'
        },
        {
          text: `${this.customerUsecase?.companyTable?.location}`,
          value: 'customerLocation',
          sortable: false,
          width: '18%'
        },
        {
          text: `${this.customerUsecase?.companyTable?.contact}`,
          value: 'customerContact',
          sortable: false
        },
        {
          text: `${this.customerUsecase?.companyTable?.email}`,
          value: 'customerEmail',
          sortable: false,
          width: '15%'
        },
        this.isActionEnable
          ? { text: 'Action', value: 'action', sortable: false, width: '10%' }
          : []
      ]
      return headers
    },
    canWrite() {
      return hasPermission(this.userData, permissionData.customerWrite)
    },
    canEdit() {
      return hasPermission(this.userData, permissionData.customerEdit)
    },
    canDelete() {
      return hasPermission(this.userData, permissionData.customerDelete)
    },
    isActionEnable() {
      return this.canDelete || this.canEdit || this.canWrite
    },
    companyNames() {
      const options = []

      this.companies.forEach((company) => {
        options.push({
          text: `${company.name}`,
          value: company._id
        })
      })
      return sortDropdownOptions(options)
    },
    selectedCompanyBranches() {
      if (this.selectedCompany) {
        const selectedCompany = this.companies.find(
          (company) => company._id === this.selectedCompany?.value
        )
        const options = selectedCompany?.companyBranches.map((branch) => {
          return {
            text: `[ Alias - ${branch.alias} ] - ${branch.address.addressLine1}`,
            value: branch._id
          }
        })
        return options
      }
      return []
    },
    filteredCustomers() {
      if (!this.selectedCompanyBranch) {
        return this.customers
      } else {
        const selectedBranch = this.customers.filter(
          (customer) => customer.locationId === this.selectedCompanyBranch
        )
        return selectedBranch
      }
    },
    customerUsecase() {
      let useCaseData = ['inventory', 'default', 'project']
      let currentConfig
      const orgConfig = this.orgLang.data || {}
      const leadModules = orgConfig.leadModules || []

      currentConfig = leadModules.find((m) => useCaseData.includes(m.useCaseType))

      return currentConfig
    }
  },
  mounted() {
    this.customerUsecase
  }
}
</script>

<style scoped></style>
