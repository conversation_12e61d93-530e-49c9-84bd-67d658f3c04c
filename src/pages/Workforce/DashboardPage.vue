<template>
  <div>
    <div v-if="isLoad" class="absolute w-full h-screen flex-col items-center flex justify-center">
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
    <div class="flex" v-else>
      <v-col :cols="progressVisibility ? 8 : 12" class="px-0">
        <v-row
          v-show="isFeatureVisible('SHOW_ATTENDANCE_STATS') && sectionVisibility.employeeStats"
        >
          <v-col @click="navToEmployee" class="cursor-pointer pr-0">
            <ReusableBox
              :number="statsData?.userClockInStats?.totalEmployees"
              :content="`Total Active ${orgLanguage.employees || 'Employees'}`"
              color="bg-indigo-500 dark:bg-[#4B5B9F]"
              class="py-4"
            />
          </v-col>
          <v-col class="cursor-pointer pr-0" @click="navToClockInEmployee">
            <ReusableBox
              :number="statsData?.userClockInStats?.checkedInMembers"
              :content="isComplaintsEnabled ? 'Present' : 'Clocked-in'"
              color="bg-teal-500 dark:bg-[#2A9D8F]"
              class="py-4"
            />
          </v-col>
          <v-col class="cursor-pointer" @click="navToNonClockInEmployee">
            <ReusableBox
              :number="statsData?.userClockInStats?.notCheckedInMembers"
              :content="isComplaintsEnabled ? 'Absent' : 'Not Clocked-in'"
              color="bg-red-500 dark:bg-[#D9534F]"
              class="py-4"
            />
          </v-col>
        </v-row>
        <v-row class="my-0">
          <v-col
            cols="12"
            class="relative pt-4"
            v-show="isItemTypeCase && isFeatureVisible('SHOW_CASE_ANALYTICS')"
          >
            <div
              class="absolute top-0 right-0 pr-3 pt-4"
              v-if="isItemTypeCase"
              v-show="
                isFeatureVisible('SHOW_CASE_ANALYTICS') && sectionVisibility.caseAnalyticsStats
              "
            >
              <v-select
                class="w-64 pt-2"
                :items="subCasesOptions"
                v-model="selectedSubCase"
                dense
                label="Select Team"
                placeholder="Complaint Types"
                @change="handleChange"
              ></v-select>
            </div>
            <div
              class="mt-4"
              v-if="isItemTypeCase"
              v-show="
                isFeatureVisible('SHOW_CASE_ANALYTICS') && sectionVisibility.caseAnalyticsStats
              "
            >
              <h2 class="ml-4 text-lg my-3 font-semibold">{{ selectedSubCase }} Analytics</h2>
              <v-skeleton-loader
                v-if="!subCaseCharts.series?.length"
                class="mx-auto"
                max-width="650"
                max-height="750"
                type="image"
              ></v-skeleton-loader>
              <apexchart
                v-else
                ref="apexChart"
                class="p-2 dark-text-color"
                type="bar"
                height="330"
                :options="subCaseCharts?.options"
                :series="subCaseCharts?.series"
                @click="handleSubCaseChartClick"
              />
            </div>
          </v-col>
          <v-col
            v-if="
              !!caseTaskStats.options &&
              !!caseTaskStats.series[0]?.data &&
              !!caseTaskStats.series[0]?.data?.length
            "
            cols="12"
            v-show="
              isItemTypeCase &&
              isFeatureVisible('SHOW_CASE_ANALYTICS') &&
              sectionVisibility.visitAnalyticsStats
            "
          >
            <apexchart
              ref="apexChartCase"
              class="p-2 dark-text-color"
              height="330"
              :options="caseTaskStats.options"
              :series="caseTaskStats.series"
            />
          </v-col>
          <v-col
            v-if="isTeleDataAvailable"
            cols="12"
            v-show="
              isItemTypeCase &&
              isFeatureVisible('SHOW_CASE_ANALYTICS') &&
              isCustomerCallingEnable('CALL_A_CUSTOMER') &&
              sectionVisibility.teleCallingStats
            "
          >
            <apexchart
              ref="apexChartTele"
              class="p-2 dark-text-color"
              type="bar"
              height="330"
              :options="teleCallerStats.options"
              :series="teleCallerStats.series"
            />
          </v-col>
          <v-col
            v-if="!!empStatsChart.options"
            cols="12"
            v-show="
              isItemTypeCase &&
              isFeatureVisible('SHOW_CASE_ANALYTICS') &&
              sectionVisibility.caseAnalyticsStats
            "
          >
            <!-- Add search input -->
            <div class="d-flex align-center px-3">
              <v-text-field
                v-model="searchQuery"
                :label="`Search ${orgLanguage.employees || 'Employees'}`"
                prepend-inner-icon="mdi-magnify"
                dense
                outlined
                hide-details
                class="w-1/3"
                clearable
              />
              <!-- Custom Pagination -->
              <div v-if="showPagination" class="d-flex align-center justify-center px-3">
                <v-btn icon :disabled="currentPage === 1" @click="currentPage--" class="mr-2">
                  <v-icon>mdi-chevron-left</v-icon>
                </v-btn>

                <div class="px-4 d-flex align-center">
                  <span class="text-body-2"> Page {{ currentPage }} of {{ totalPages }} </span>
                  <span class="ml-2 text-caption grey--text">
                    ({{ filteredEmployees?.length }} employees)
                  </span>
                </div>

                <v-btn
                  icon
                  :disabled="currentPage >= totalPages"
                  @click="currentPage++"
                  class="ml-2"
                >
                  <v-icon>mdi-chevron-right</v-icon>
                </v-btn>
              </div>
            </div>

            <!-- Chart -->
            <apexchart
              ref="apexChartEmpStats"
              class="p-2 dark-text-color"
              type="bar"
              height="330"
              :options="paginatedEmpStats.options"
              :series="paginatedEmpStats.series"
            />
          </v-col>
          <v-col>
            <div v-show="!isItemTypeCase && complaintOptions?.length">
              <v-select
                :items="complaintOptions"
                v-model="selectedComplaintType"
                outlined
                dense
                flat
                label="Complaint Types"
                placeholder="Complaint Types"
                @change="handleChange"
                clearable
              ></v-select>
            </div>
            <TaskColumn
              v-if="!loadingTasks && !isItemTypeCase && !isCollectionEnabled"
              :tasks="tasks"
              :cases="cases"
              :data="statsData"
              :items="items"
              :itemType="itemType"
              :selectedUseCase="selectedUseCase"
            />
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="4">
        <Progress
          :data="statsData"
          @selectedStat="fetchStatsData"
          :itemType="itemType"
          :subCasesOptions="subCasesOptions"
          :collectionVisits="collectionVisits"
          :sectionVisibility="sectionVisibility"
        />
      </v-col>
    </div>
    <div v-if="sectionVisibility.loanStats">
      <LoanDashboard :sectionVisibility="sectionVisibility" />
    </div>
  </div>
</template>

<script>
import ReusableBox from '@/components/ReusableBox.vue'
import Progress from '@/components/Workforce/Dashboard/Progress.vue'
import LoanDashboard from '@/components/Workforce/Dashboard/LoanDashboard.vue'
import TaskColumn from '@/components/Workforce/TaskColumn'
// import VueSsrCarousel from 'vue-ssr-carousel';
import { checkOrgCode, isModuleFeatureAllowed, isModuleVisible } from '@/utils/common'
export default {
  components: {
    ReusableBox,
    LoanDashboard,
    Progress,
    TaskColumn,
    apexchart: () => (typeof window !== 'undefined' ? import('vue-apexcharts') : null)
  },
  layout: 'Workforce/default',
  name: 'WorkforceDashboardComponent',
  data() {
    return {
      loanDashboardDialog: false,
      loadingTasks: true,
      orgCode: this.$storage.getUniversal('organization_code'),
      userData: this.$storage.getUniversal('user'),
      enabledUseCase: this.$store.state.useCase,
      subCases: this.$store.state.subCases,
      orgLang: this.$store.state.orgLanguage,
      tasks: [],
      cases: [],
      items: [],
      ChartOptions: {},
      selectedComplaintType: '',
      teleCallerStats: {},
      caseTaskStats: {},
      ChartSeries: [],
      collectionVisits: [],
      itemType: 'case',
      statsData: {},
      subCaseCharts: {},
      complaintOptions: [],
      config: null,
      selectedOption: null,
      selectedUseCase: '',
      isLoad: true,
      isSelfieEnabled: false,
      selectedSubCase: null,
      darkMode: false,
      subCasesMapping: [],
      adminDashboardCustomization: null,
      yRightSideColor: 'RGB(254,176,25)',
      yLeftSideColor: 'RGB(0,143,251)',
      empChartValue: [],
      empStatsChart: {},
      searchQuery: '',
      currentPage: 1,
      itemsPerPage: 10,
      isTeleDataAvailable: false
    }
  },
  watch: {
    async selectedSubCase(newValue) {
      if (!newValue) return
      await this.getConfig()
      await this.getAdminDashboardSettings()

      // await this.extractMaping(newValue);
      await this.fetchTeleCallStats(newValue)
      await this.fetchCaseTaskStats(newValue)
      await this.fetchCaseAnalyticsStats(newValue)
      await this.fetchEmpAnalytics(newValue)
    },
    '$store.state.subCases': {
      handler(newSubCases) {
        this.subCases = newSubCases
        if (this.subCasesOptions?.length) {
          this.selectedSubCase = this.subCasesOptions[0]
          this.extractMaping(this.selectedSubCase)
        }
      },
      immediate: true,
      deep: true
    },
    searchQuery() {
      this.currentPage = 1 // Reset to first page when searching
    }
  },
  async mounted() {
    this.$root.$on('modeChange', this.modeChange)
    await this.fetchStatsData('CURRENT_DAY')
    await this.getConfig()
    await this.getAdminDashboardSettings()
    await this.fetchData()
    this.isLoad = false
  },
  computed: {
    isComplaintsEnabled() {
      return checkOrgCode(this.orgCode)
    },
    isItemTypeCase() {
      return isModuleVisible('COLLECTION_MANAGEMENT')
    },
    isInventoryCompliant() {
      return isModuleVisible('SELLS')
    },
    subCasesOptions() {
      const subCaseNames = []

      if (this.subCases && Array.isArray(this.subCases)) {
        this.subCases.forEach((useCase) => {
          if (useCase.subCase && Array.isArray(useCase.subCase)) {
            let filteredSubCases = useCase.subCase
            if (this.userData?.branch && !this.userData?.role?.includes('WFM_ADMIN')) {
              filteredSubCases = filteredSubCases.filter(
                (sub) => sub?.branch === this.userData?.branch
              )
            }

            filteredSubCases.forEach((subCase) => {
              if (subCase.name) {
                subCaseNames.push(subCase.name)
              }
            })
          }
        })
      }

      return subCaseNames
    },
    sectionVisibility() {
      const sections = this.adminDashboardCustomization || {
        employeeStats: true,
        progressStats: true,
        caseAnalyticsStats: true,
        visitAnalyticsStats: true,
        visitCollectionStats: true,
        teleCallingStats: true
      }

      return sections
    },
    progressVisibility() {
      return this.sectionVisibility?.progressStats || this.sectionVisibility?.visitCollectionStats
    },
    matchingEmployeeIndexes() {
      if (!this.empStatsChart.series?.[0]?.data) return []

      const allEmployees = this.empStatsChart.options.xaxis.categories
      const searchQuery = this.searchQuery?.toLowerCase()

      return allEmployees.reduce((acc, emp, index) => {
        if (emp.toLowerCase().includes(searchQuery)) {
          acc.push(index)
        } else if (!searchQuery) {
          acc.push(index)
        }
        return acc
      }, [])
    },
    filteredEmployees() {
      if (!this.empStatsChart.series?.[0]?.data) return []

      const allEmployees = this.empStatsChart.options.xaxis.categories
      return this.matchingEmployeeIndexes.map((index) => allEmployees[index])
    },
    paginatedEmpStats() {
      const start = (this.currentPage - 1) * this.itemsPerPage
      const end = start + this.itemsPerPage
      const paginatedCategories = this.filteredEmployees.slice(start, end)

      // Filter series data using matching indexes
      const filteredSeries = this.empStatsChart.series.map((series) => ({
        ...series,
        data: this.matchingEmployeeIndexes.map((index) => series.data[index]).slice(start, end)
      }))

      return {
        options: {
          ...this.empStatsChart.options,
          xaxis: {
            ...this.empStatsChart.options.xaxis,
            categories: paginatedCategories,
            labels: {
              ...this.empStatsChart.options.xaxis?.labels,
              trim: true,
              rotate: -45,
              rotateAlways: false,
              hideOverlappingLabels: false
            }
          }
        },
        series: filteredSeries
      }
    },
    totalPages() {
      return Math.ceil(this.filteredEmployees?.length / this.itemsPerPage)
    },

    showPagination() {
      return this.filteredEmployees?.length > this.itemsPerPage
    },
    orgLanguage() {
      return this.orgLang.data.leftNav
    }
  },
  methods: {
    async modeChange() {
      if (localStorage.getItem('theme') === 'dark') {
        this.darkMode = true
      } else {
        this.darkMode = false
      }

      await this.$nextTick()
      if (this.$refs.apexChart) {
        this.$refs.apexChart.updateOptions({
          theme: {
            mode: this.darkMode ? 'dark' : 'light'
          }
        })
      }
      if (this.$refs.apexChartCase) {
        this.$refs.apexChartCase.updateOptions({
          theme: {
            mode: this.darkMode ? 'dark' : 'light'
          }
        })
      }
      if (this.$refs.apexChartTele) {
        this.$refs.apexChartTele.updateOptions({
          theme: {
            mode: this.darkMode ? 'dark' : 'light'
          }
        })
      }
      if (this.$refs.apexChartEmpStats) {
        this.$refs.apexChartEmpStats.updateOptions({
          theme: {
            mode: this.darkMode ? 'dark' : 'light'
          }
        })
      }
    },
    setStatOption(option) {
      this.selectedOption = option
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('DASHBOARD', feature)
    },
    isCustomerCallingEnable(feature) {
      return isModuleFeatureAllowed('ADDONS', feature)
    },
    async fetchCaseTaskStats(subCase) {
      try {
        const response = await this.$axios.get(
          `/workforce/collection/case/tasks/stats?caseType=${subCase}`
        )
        const stats = response.data?.data?.stats || []

        const categories = stats.map((d) => `${d._id} (${d.count})`)
        const totalVisits = stats.reduce((sum, d) => sum + (d.count || 0), 0)

        this.collectionVisits = response.data?.data?.tasks || []

        const series = [
          {
            name: 'No. of Visits',
            data: stats.map((d) => d.count)
          }
        ]

        const vM = this.extractMaping(subCase)

        this.caseTaskStats = {
          options: {
            chart: {
              id: 'casetask-stats',
              type: 'bar',
              height: 350,
              toolbar: {
                show: true
              }
            },
            title: {
              text: `${vM?.visitTable?.displayName || 'Visits'} Status (Total: ${totalVisits})`
            },
            xaxis: {
              categories: categories,
              crosshairs: {
                show: true,
                width: 1,
                position: 'back',
                opacity: 0.9,
                stroke: {
                  color: '#b6b6b6',
                  width: 1,
                  dashArray: 2
                }
              }
            },
            yaxis: {
              title: {
                text: `#${vM?.visitTable?.displayName || 'Visits'}`,
                style: {
                  color: this.yLeftSideColor
                }
              },
              axisTicks: {
                show: true
              },
              axisBorder: {
                show: true,
                color: this.yLeftSideColor
              },
              labels: {
                style: {
                  colors: this.yLeftSideColor,
                  fontWeight: 'bold'
                }
              },
              tooltip: {
                enabled: true
              }
            },
            plotOptions: {
              bar: {
                columnWidth: '45%',
                horizontal: false,
                endingShape: 'rounded',
                dataLabels: {
                  position: 'top'
                }
              }
            },
            colors: [this.yLeftSideColor],
            theme: {
              mode: this.darkMode ? 'dark' : 'light'
            },
            stroke: {
              width: [0] // 0 for bar
            },
            dataLabels: {
              enabled: true,
              enabledOnSeries: [0] // Enable data labels for bar chart 0
            }
          },
          series
        }
      } catch (error) {
        console.log(error)
      }
    },
    async fetchTeleCallStats(subCase) {
      try {
        const response = await this.$axios.get(`/workforce/tele-caller/stats?caseType=${subCase}`)

        this.isTeleDataAvailable = response.data?.data?.length > 0
        const subCaseData = response.data?.data.find((d) => d._id === subCase) || {}

        // Extract values
        const totalCalls = subCaseData.totalCalls || 0
        const receivedByAgent = subCaseData.aPartyConnectedCount || 0
        const receivedByCustomer = subCaseData.bPartyConnectedCount || 0

        // Compute total sum
        const totalSum = totalCalls + receivedByAgent + receivedByCustomer

        const categories = [
          `Total Calls (${totalCalls})`,
          `Received by Agent (${receivedByAgent})`,
          `Received by Customer (${receivedByCustomer})`
        ]

        const series = [
          {
            name: 'No. of Calls',
            data: [totalCalls, receivedByAgent, receivedByCustomer]
          }
        ]

        const teleM = this.extractMaping(subCase)

        this.teleCallerStats = {
          options: {
            chart: {
              id: 'telecaller-stats',
              type: 'bar',
              height: 350,
              toolbar: {
                show: true
              }
            },
            title: {
              text: `${teleM?.callHistoryTable?.displayName || 'Calls'} Status (Total: ${totalSum})`
            },
            xaxis: {
              categories: categories,
              crosshairs: {
                show: true,
                width: 1,
                position: 'back',
                opacity: 0.9,
                stroke: {
                  color: '#b6b6b6',
                  width: 1,
                  dashArray: 2
                }
              }
            },
            yaxis: {
              title: {
                text: `#${teleM?.callHistoryTable?.displayName || 'Call'}`,
                style: {
                  color: this.yLeftSideColor
                }
              },
              axisTicks: {
                show: true
              },
              axisBorder: {
                show: true,
                color: this.yLeftSideColor
              },
              labels: {
                style: {
                  colors: this.yLeftSideColor,
                  fontWeight: 'bold'
                }
              },
              tooltip: {
                enabled: true
              }
            },
            plotOptions: {
              bar: {
                columnWidth: '45%',
                horizontal: false,
                endingShape: 'rounded',
                dataLabels: {
                  position: 'top'
                }
              }
            },
            colors: [this.yLeftSideColor],
            theme: {
              mode: this.darkMode ? 'dark' : 'light'
            },
            dataLabels: {
              enabled: true,
              enabledOnSeries: [0] // Enable data labels for bar chart 0
            },
            legend: {
              show: true,
              horizontalAlign: 'left',
              offsetX: 40
            }
          },
          series
        }
      } catch (error) {
        console.log(error)
      }
    },

    async fetchData() {
      try {
        if (isModuleVisible('COLLECTION_MANAGEMENT')) {
          return
        }

        const params = {
          start: this.$dayjs().format('YYYY-MM-DD'),
          limit: '*'
        }
        if (this.selectedComplaintType) {
          params.search = this.selectedComplaintType
        }
        const response = await this.$axios.get('/workforce/task/org', {
          params
        })
        this.items = response.data.tasks
        this.itemType = 'task'
      } catch (error) {
        console.error(error)
      }
      this.loadingTasks = false
    },
    async fetchStatsData(selectedStat) {
      try {
        const params = {}
        if (selectedStat) {
          this.selectedUseCase = selectedStat?.useCase?.toLowerCase()
          params.pull = selectedStat?.period
          params.useCase = selectedStat?.useCase?.toLowerCase()
        }
        const response = await this.$axios.get('workforce/org/stats', {
          params
        })
        this.statsData = response.data
      } catch (error) {
        console.error(error)
      }
    },
    async fetchEmpAnalytics(subCase) {
      try {
        const params = { caseType: subCase }
        const response = await this.$axios.get('/workforce/collection/cases/analytics', { params })
        const empStats = response.data?.caseAnalytics?.userDefinedAnalytics

        const filteredData = empStats.filter((emp) => emp?._id)
        const categories = filteredData.map((emp) => emp.assignedTo)
        // filtering the empty assignedTo if the case is not assigned but data is there
        const allEmpData = categories.filter((emp) => emp.trim())
        const seriesData = {}

        // Initialize series data with zeros for all employees
        Object.keys(seriesData).forEach((status) => {
          seriesData[status].push(0)
        })

        // Populate series data with actual values
        filteredData.forEach((emp, empIndex) => {
          emp.statuses.forEach((status) => {
            const statusName = status.status || 'Un-Attended/In-Complete'
            if (!seriesData[statusName]) {
              seriesData[statusName] = Array(allEmpData.length).fill(0)
            }
            seriesData[statusName][empIndex] = status.totalCount || 0
          })
        })

        const series = Object.keys(seriesData).map((status) => ({
          name: status,
          data: seriesData[status]
        }))

        this.empStatsChart = {
          options: {
            chart: {
              type: 'bar',
              height: 350,
              stacked: true
            },
            xaxis: {
              categories: allEmpData,
              crosshairs: {
                show: true,
                width: 1,
                position: 'back',
                opacity: 0.9,
                stroke: {
                  color: '#b6b6b6',
                  width: 1,
                  dashArray: 2
                }
              }
            },
            yaxis: {
              title: {
                text: '#Employees',
                style: {
                  color: this.yLeftSideColor
                }
              },
              axisTicks: {
                show: true
              },
              axisBorder: {
                show: true,
                color: this.yLeftSideColor
              },
              labels: {
                style: {
                  colors: this.yLeftSideColor,
                  fontWeight: 'bold'
                }
              },
              tooltip: {
                enabled: true
              }
            },
            plotOptions: {
              bar: {
                columnWidth: '45%',
                horizontal: true,
                endingShape: 'rounded',
                dataLabels: {
                  position: 'center'
                }
              }
            },
            colors: [
              '#FF4560',
              '#00E396',
              '#008FFB',
              '#FEB019',
              '#775DD0',
              '#546E7A',
              '#26A69A',
              '#D10CE8'
            ],
            theme: {
              mode: this.darkMode ? 'dark' : 'light'
            },
            dataLabels: {
              enabled: true,
              formatter: function (val) {
                return val
              },
              style: {
                fontSize: '14px',
                colors: ['#fff'],
                fontWeight: 'bold'
              }
            },
            legend: {
              show: true,
              horizontalAlign: 'left',
              offsetX: 40
            }
          },
          series
        }
      } catch (error) {
        console.log(error)
      }
    },
    async fetchCaseAnalyticsStats(subCase) {
      try {
        const params = { caseType: subCase, fetch_type: 'ORG' }
        const response = await this.$axios.get('/workforce/collection/cases/analytics', { params })

        const caseAnalytics = response.data?.caseAnalytics?.userDefinedAnalytics || []

        const cM = this.extractMaping(subCase)

        // Compute total sum of all case counts
        const totalSum = caseAnalytics.reduce((sum, data) => sum + (data.caseCount || 0), 0)

        const categories = caseAnalytics.map(
          (data) => `${data.userDefinedCaseStatus || 'Un-Attended'} (${data.caseCount})`
        )

        const seriesDataTemp = caseAnalytics.map((data) => data.caseCount) || [0]

        // Check if any fund collection data exists
        const hasFundData = caseAnalytics.some((item) => item.totalFundCollected > 0)

        // Conditionally set up yaxis based on fund data
        const yaxisConfig = hasFundData
          ? [
              {
                title: {
                  text: `#${cM.caseTable?.displayName || 'Cases'}`,
                  style: { color: this.yLeftSideColor }
                },
                axisTicks: { show: true },
                axisBorder: { show: true, color: this.yLeftSideColor },
                labels: {
                  style: { colors: this.yLeftSideColor, fontWeight: 'bold' }
                },
                tooltip: { enabled: true }
              },
              {
                opposite: true,
                title: {
                  text: '#Amount',
                  style: { color: this.yRightSideColor }
                },
                axisTicks: { show: true },
                axisBorder: { show: true, color: this.yRightSideColor },
                labels: {
                  formatter: (val) => `₹ ${val.toLocaleString()}`,
                  style: { colors: this.yRightSideColor, fontWeight: 'bold' }
                },
                tooltip: { enabled: true }
              }
            ]
          : [
              {
                title: {
                  text: `#${cM.caseTable?.displayName || 'Cases'}`,
                  style: { color: this.yLeftSideColor }
                },
                axisTicks: { show: true },
                axisBorder: { show: true, color: this.yLeftSideColor },
                labels: {
                  style: { colors: this.yLeftSideColor, fontWeight: 'bold' }
                },
                tooltip: { enabled: true }
              }
            ]

        // Conditionally set colors based on fund data
        const colorsConfig = hasFundData
          ? [this.yLeftSideColor, this.yRightSideColor]
          : [this.yLeftSideColor]

        // Conditionally set stroke width based on fund data
        const strokeConfig = hasFundData ? [0, 4] : [0]

        // Prepare series data
        const seriesData = [
          {
            name: 'Cases',
            type: 'bar',
            data: seriesDataTemp
          }
        ]

        // Add amount series only if fund data exists
        if (hasFundData) {
          seriesData.push({
            name: 'Amount',
            type: 'line',
            data: caseAnalytics.map((item) => item.totalFundCollected || 0)
          })
        }

        this.subCaseCharts = {
          options: {
            chart: {
              type: 'line',
              height: 350,
              stacked: false,
              toolbar: { show: true }
            },
            title: {
              text: `${cM.caseTable?.displayName || 'Cases'} Status (Total: ${totalSum})`
            },
            xaxis: {
              categories,
              crosshairs: {
                show: true,
                width: 1,
                position: 'back',
                opacity: 0.9,
                stroke: {
                  color: '#b6b6b6',
                  width: 1,
                  dashArray: 2
                }
              }
            },
            yaxis: yaxisConfig,
            plotOptions: {
              bar: {
                columnWidth: '45%',
                horizontal: false,
                endingShape: 'rounded',
                dataLabels: { position: 'top' }
              }
            },
            colors: colorsConfig,
            theme: { mode: this.darkMode ? 'dark' : 'light' },
            stroke: { width: strokeConfig },
            dataLabels: {
              enabled: true,
              enabledOnSeries: []
            },
            markers: {
              size: 6,
              hover: { sizeOffset: 6 }
            },
            tooltip: {
              shared: true,
              intersect: false,
              y: {
                formatter: function (val, { seriesIndex }) {
                  return seriesIndex === 1 ? `₹ ${val.toLocaleString()}` : val
                }
              }
            },
            legend: {
              show: true,
              horizontalAlign: 'left',
              offsetX: 40
            }
          },
          series: seriesData
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config
        this.complaintOptions = data.predefinedComplaintTitles
        this.isSelfieEnabled = data.isCheckedInSelfie || data.isCheckedOutSelfie
        this.subCasesMapping = data.enabledSubCases[0]?.subCase
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async getAdminDashboardSettings() {
      try {
        const { data } = await this.$axios.get('/workforce/orgSetup/admin-dashboard-settings')
        this.adminDashboardCustomization = data.dashboard?.adminDashboardCustomization
      } catch (error) {
        console.log(error)
      }
    },
    navToEmployee() {
      this.$router.push('/workforce/employees')
    },
    navToClockInEmployee() {
      const checkedIn = this.statsData?.userClockInStats?.checkedInUsers
      if (checkedIn && checkedIn.length) {
        const userIds = checkedIn.map((el) => el._id)
        this.$router.push(`/workforce/team-review?userIds=${userIds}`)
      }
    },
    navToNonClockInEmployee() {
      const notCheckedIn = this.statsData?.userClockInStats?.notCheckedInUsers
      if (notCheckedIn && notCheckedIn.length) {
        const userIds = notCheckedIn.map((el) => el._id)
        this.$router.push(`/workforce/team-review?userIds=${userIds.join(',')}`)
      }
    },
    extractMaping(sCName) {
      const selectedSubCaseMapping = this.subCasesMapping.find((el) => el.name === sCName)

      return selectedSubCaseMapping?.tableTabMapping
    },
    async handleChange() {
      await this.fetchData()
    },
    handleSubCaseChartClick(event, chartContext, config) {
      const subCaseId = this.subCases[0].subCase.find((el) => el.name === this.selectedSubCase)?._id
      const outcome =
        this.subCaseCharts.options.xaxis.categories[config.dataPointIndex] || event.target.innerHTML
      const userDefinedCaseStatus = outcome?.split('(')[0]

      if (userDefinedCaseStatus.trim() === 'Un-Attended') {
        this.$router.push(
          `/workforce/collection-management/${subCaseId}?userDefinedCaseStatus=null`
        )
      } else {
        this.$router.push(
          `/workforce/collection-management/${subCaseId}?userDefinedCaseStatus=${userDefinedCaseStatus}`
        )
      }
    }
    // clearEmpNameQuery() {
    //   this.searchQuery = ''
    // }
  }
}
</script>
