<template>
  <div>
    <div v-if="!isLoad">
      <div class="flex justify-end items-center" v-if="!caseConfig?._id">
        <div class="justify-end mr-4" :class="[$vuetify.breakpoint.lgAndUp ? 'flex' : 'flex-col']">
          <v-btn
            @click="addNewCustomer"
            :color="$vuetify.theme.currentTheme.primary"
            class="ml-4 white--text my-auto"
          >
            Add
            <v-icon right dark>mdi-plus</v-icon>
          </v-btn>
        </div>
        <FilterPopOver class="p-2" :selectedCount="selectedCount" v-if="isEntityEnabled">
          <v-card outlined class="w-72 max-h-96">
            <v-list>
              <v-list-item class="px-0" v-if="isEntityEnabled">
                <v-col class="py-0">
                  <v-autocomplete
                    v-model="entityId"
                    :label="`Select ${entityTitle}`"
                    :placeholder="`Select ${entityTitle}`"
                    @update:search-input="getAllEntities"
                    :search-input.sync="searchValue"
                    :items="entities"
                    item-text="entityName"
                    item-value="_id"
                    hide-details
                    outlined
                    dense
                    clearable
                    @change="handleInput"
                  ></v-autocomplete>
                </v-col>
              </v-list-item>
            </v-list>
          </v-card>
        </FilterPopOver>
        <div :class="[$vuetify.breakpoint.lgAndUp ? '' : 'mr-auto']">
          <v-menu offset-y class="w-16">
            <template v-slot:activator="{ on }">
              <v-btn icon v-on="on" class="ml-4">
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item @click="handleShowDialogDelete()" :disabled="!selected.length">
                <v-list-item-title>Bulk Delete</v-list-item-title>
              </v-list-item>
              <v-list-item @click="bulkCustomer()">
                <v-list-item-title
                  >Bulk
                  {{ configTabMapping.customerTable?.displayName || 'Customer' }}
                  Create</v-list-item-title
                >
              </v-list-item>
              <v-list-item @click="exportToExcel()">
                <v-list-item-title>Export to Excel</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>

      <div class="overflow-x-auto overflow-y-auto">
        <v-data-table
          :headers="headers"
          :items="customers"
          class="pt-4"
          fixed-header
          :footer-props="{ itemsPerPageOptions: [5, 10, 15, 30, 50] }"
          :options.sync="options"
          :server-items-length="totalItems"
          :loading="loadTable"
          v-model="selected"
          show-select
          item-key="_id"
        >
          <template v-slot:item.customerName="{ item }">
            <td class="px-4 py-6 font-semibold" @click="showCustomerDetails(item)">
              {{ userFullName(item) }}
            </td>
          </template>
          <template v-slot:item.crn="{ item }">
            <td class="px-4 py-6 font-semibold">{{ maskNumber(item.crn) }}</td>
          </template>
          <template v-slot:item.entityName="{ item }">
            <td class="px-4 py-6 font-semibold">
              {{ item.entity?.entityName || '-' }}
            </td>
          </template>
          <template v-slot:item.email="{ item }">
            <td class="px-4 py-6 font-semibold">{{ item.customerEmail }}</td>
          </template>
          <template v-slot:item.customerMobile="{ item }">
            <td class="px-4 py-6 font-semibold">
              {{ maskNumber(item.customerMobile[0]) }}
            </td>
          </template>
          <template v-slot:item.address="{ item }">
            <td class="px-4 py-6 font-semibold" v-if="item.customerAddress.length">
              {{ customerAddress(item.customerAddress[0]) || '-' }}
              <br />
              {{
                item.customerAddress[0]?.typeOfAddress
                  ? `(${item.customerAddress[0]?.typeOfAddress})`
                  : ''
              }}
            </td>
            <td v-else>-</td>
          </template>
          <template v-slot:item.action="{ item }">
            <td class="px-4 py-6">
              <action-button
                :item="item"
                :canEdit="true"
                :handleEdit="() => customerEdit(item)"
                :handleDelete="() => customerDelete(item)"
                :showDeleteButton="true"
                :canDelete="true"
              />
            </td>
          </template>
        </v-data-table>
      </div>
      <v-dialog v-model="openAddCustomer" width="63rem" persistent>
        <AddCustomer
          @customerAdded="handleNewCustomer"
          :editCustomer="editCustomer"
          :caseType="relatedCaseType"
          :entities="entities"
          :configTabMapping="configTabMapping"
          @emittedEntity="handleEntity"
        />
      </v-dialog>
      <v-dialog v-model="showCustomerDetailsPopup" max-width="800">
        <CustomerDetails
          :customer="selectedCustomer"
          :closeCustomerPopUp="closeCustomerPopUpFunc"
        />
      </v-dialog>
      <v-dialog v-model="whatsAppMessagePopupVisible" max-width="500">
        <WhatsAppMessage :customers="selected" />
      </v-dialog>
      <AlertPopUp
        :showConfirmationDialog="showConfirmationDialog"
        :confirmationTitle="'Confirm Delete'"
        :confirmationMessage="`Are you sure you want to delete the ${
          configTabMapping.customerTable?.displayName || 'Customer'
        }?`"
        :confirmationActionText="'DELETE'"
        :confirmationActionColor="'red'"
        :performAction="custDeleteAction"
        :cancel="cancelDelete"
      />
      <AlertPopUp
        :showConfirmationDialog="showDeleteDialog"
        :confirmationTitle="'Confirm Delete'"
        :confirmationMessage="`Do you want to delete selected ${
          configTabMapping.customerTable?.displayName || 'Customer'
        }?`"
        :confirmationActionText="'DELETE'"
        :confirmationActionColor="'red'"
        :performAction="customerDeleteFunc"
        :cancel="handleShowDialogDelete"
        :loading="loading"
      />
    </div>
    <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
      <v-progress-circular
        :color="$vuetify.theme.currentTheme.primary"
        indeterminate
        :size="50"
        :width="5"
      ></v-progress-circular>
      <div class="mt-2"><span>Please Wait...</span></div>
    </div>
    <v-dialog v-model="openUploadCustomers" max-width="600" persistent>
      <UploadCustomer @close="closeUpload" :caseConfig="caseConfig" />
    </v-dialog>
  </div>
</template>
<script>
import AddCustomer from '@/components/Workforce/CollectionModules/AddCollCustomer'
import CustomerDetails from '@/components/Workforce/CollectionModules/CustomerDetails'
import AlertPopUp from '@/components/AlertPopUp'
import UploadCustomer from '@/components/Workforce/CollectionModules/UploadCustomer'
import { fullName, getFullAddress, debounce, maskPhoneNumber } from '@/utils/common'
import ActionButton from '@/components/ActionButton.vue'
import WhatsAppMessage from '@/components/Workforce/WhatsAppTemplateMessage.vue'
import FilterPopOver from '@/components/FilterPopOver.vue'
import * as XLSX from 'xlsx'
export default {
  name: 'CollectionCustomer',
  layout: 'Workforce/default',
  components: {
    ActionButton,
    AddCustomer,
    CustomerDetails,
    AlertPopUp,
    WhatsAppMessage,
    FilterPopOver,
    UploadCustomer
  },
  props: {
    caseConfig: Object
  },
  data() {
    return {
      openAddCustomer: false,
      whatsAppMessagePopupVisible: false,
      loadTable: false,
      showCustomerDetailsPopup: false,
      customers: [],
      editCustomer: null,
      options: { itemsPerPage: 10, page: 1 },
      selectedCustomer: null,
      deleteCustomer: null,
      totalItems: 0,
      filterCustomer: '',
      showConfirmationDialog: false,
      openUploadCustomers: false,
      selected: [],
      showDeleteDialog: false,
      loading: false,
      isLoad: true,
      entityId: null,
      searchValue: '',
      entities: [],
      isAnyEntityEnabled: false,
      selectedCount: 0,
      entityDisplayName: ''
    }
  },
  watch: {
    options: {
      handler() {
        this.fetchCustomers()
      },
      deep: true
    },
    '$route.query.customer': {
      async handler(customer) {
        try {
          this.filterCustomer = customer || ''
          this.options.page = 1
          this.debouncedFetchCustomers()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    },
    '$route.query.search': {
      async handler(search) {
        try {
          this.filterCustomer = search || ''
          this.options.page = 1
          this.debouncedFetchCustomers()
        } catch (error) {
          console.log(error)
        }
      },
      immediate: true
    }
  },
  computed: {
    relatedCaseType() {
      return this.$props.caseConfig?.name || ''
    },
    headers() {
      const headers = [
        {
          text: `${this.configTabMapping.customerTable?.displayName || 'Customer'} Name`,
          value: 'customerName',
          sortable: false
        },
        { text: 'CRN', value: 'crn', sortable: false },
        { text: 'Email', value: 'email', sortable: false },
        { text: 'Phone No', value: 'customerMobile', sortable: false },
        { text: 'Address', value: 'address', sortable: false },
        { text: 'Action', value: 'action', sortable: false }
      ]
      if (this.isEntityEnabled) {
        headers.splice(2, 0, {
          text: this.entityTitle,
          value: 'entityName',
          sortable: false,
          width: '150px'
        })
      }

      return headers
    },
    isEntityEnabled() {
      return this.caseConfig ? this.caseConfig.entity?.enabled : this.isAnyEntityEnabled
    },
    entityTitle() {
      return this.caseConfig ? this.caseConfig.entity?.displayName : this.entityDisplayName
    },
    configTabMapping() {
      return this.caseConfig?.tableTabMapping || {}
    },
    customerDyanmicParams() {
      let params = {}
      if (this.$route.name === 'DynamicUseCase') {
        params.useCase = this.caseConfig?.useCase
        params.subCase = this.caseConfig?._id
      } else {
        params.relatedCategory = this.relatedCaseType ? 'collection' : undefined
        params.relatedSubCategories = this.relatedCaseType || undefined
      }
      return params
    }
  },
  methods: {
    bulkCustomer() {
      this.openUploadCustomers = true
    },
    closeUpload() {
      this.openUploadCustomers = false
    },
    maskNumber(num) {
      return maskPhoneNumber(num)
    },
    closeCustomerPopUpFunc() {
      this.showCustomerDetailsPopup = false
    },
    async fetchCustomers() {
      try {
        this.loadTable = true
        const { page, itemsPerPage } = this.options
        const customerUseCase = this.customerDyanmicParams
        const params = {
          page,
          limit: itemsPerPage > -1 ? itemsPerPage : this.totalItems,
          ...customerUseCase
        }
        if (this.entityId) {
          params.entity = this.entityId
        }
        if (this.filterCustomer) {
          params.search = this.filterCustomer
        }

        const response = await this.$axios.get('/workforce/customers', {
          params
        })
        this.customers = response.data.customers
        this.loadTable = false
        this.totalItems = response.data?.pagination?.totalCount
      } catch (error) {
        console.log(error)
        const errorMessage = error.response.data.message || error
        this.$toast.error(errorMessage)
        this.loadTable = false
      }
    },
    customerDelete(item) {
      this.deleteCustomer = item
      this.showConfirmationDialog = true
    },
    userFullName(item) {
      if (item.customerFullName) {
        return item.customerFullName
      } else if (item.firstName) {
        return fullName(item)
      } else {
        return ''
      }
    },
    async custDeleteAction() {
      try {
        const item = this.deleteCustomer
        const response = await this.$axios.delete(`/workforce/customer/${item._id}`)
        const index = this.customers.findIndex((cust) => cust._id === item._id)
        if (index !== -1) {
          this.customers.splice(index, 1)
        }
        const successMessage = response?.data?.message || 'Customer deleted successfully.'
        this.$toast.success(successMessage)
        this.cancelDelete()
      } catch (error) {
        const errorMessage =
          error.response.data.message || 'An error occurred while deleting the customer.'
        this.$toast.error(errorMessage)
        console.error(error)
      }
    },
    async singleCustomerDelete(item) {
      try {
        let response = await this.$axios.delete(`/workforce/customer/${item._id}`)
        return response.data
      } catch (error) {
        console.error(error)
      }
    },
    async customerDeleteFunc() {
      this.loading = true

      const promises = []

      // Make API calls and store promises in the array
      this.selected?.forEach(async (item) => {
        promises.push(this.singleCustomerDelete(item))
      })

      // Wait for all API calls to complete
      try {
        const results = await Promise.all(promises)

        if (!results.includes(undefined)) {
          this.$toast.success('Customers deleted successfully.')
        }
        this.loading = false
        this.selected = []
        this.handleShowDialogDelete(false)
      } catch (error) {
        this.$toast.error('An error occurred while deleting the case.')
        console.error(error)
      }
    },
    getAllEntities: debounce(async function (text) {
      try {
        const params = {}
        if (this.searchValue) {
          params.search = this.searchValue
        }
        const response = await this.$axios.get('/workforce/collection/entities', { params })
        this.entities = response?.data?.entities
      } catch (error) {
        console.log(error)
      }
    }, 500),
    async getConfig() {
      try {
        const response = await this.$axios.get('/workforce/org/config')

        const data = response.data?.config
        const enabledEntity = data.enabledSubCases[0].subCase.find((el) => el.entity?.enabled)
        this.isAnyEntityEnabled = enabledEntity?.entity?.enabled || false
        if (this.isAnyEntityEnabled) {
          this.entityDisplayName = enabledEntity?.entity?.displayName || 'Entity'
        }
      } catch (error) {
        console.error('Error fetching config:', error)
      }
    },
    async handleEntity(val) {
      this.searchValue = val
      await this.getAllEntities()
    },
    cancelDelete() {
      this.showConfirmationDialog = false
      this.deleteCustomer = null
      this.fetchCustomers()
    },
    customerEdit(item) {
      this.editCustomer = item
      this.openAddCustomer = true
    },
    customerAddress(item) {
      if (item) {
        return getFullAddress(item)
      }
      return '-'
    },
    updateSelectedCount() {
      this.selectedCount = [this.entityId].filter(
        (item) => item !== null && item !== '' && item.length > 0
      ).length
    },
    addNewCustomer() {
      this.editCustomer = null
      this.openAddCustomer = true
    },
    handleNewCustomer() {
      this.openAddCustomer = false
      this.fetchCustomers()
    },
    showCustomerDetails(customer) {
      this.selectedCustomer = customer
      this.showCustomerDetailsPopup = true
    },
    handleInput: debounce(async function () {
      this.options.page = 1
      this.updateSelectedCount()
      await this.fetchCustomers()
    }, 800),

    handleShowDialogDelete() {
      this.showDeleteDialog = !this.showDeleteDialog
      if (this.showDeleteDialog === false) {
        this.fetchCustomers()
      }
    },
    async exportToExcel() {
      try {
        this.loadTable = true
        const params = {
          relatedCategory: this.relatedCaseType ? 'collection' : undefined,
          relatedSubCategories: this.relatedCaseType || undefined,
          entity: this.entityId,
          search: this.filterCustomer
        }

        // Get total count
        const countResponse = await this.$axios.get('/workforce/customers', {
          params: { ...params, page: 1, limit: 1 }
        })
        const totalRecords = countResponse.data?.pagination?.totalCount || 0

        if (totalRecords === 0) {
          this.$toast.info('No records found to export')
          return
        }

        // Fetch all customers in batches
        const batchSize = 50
        const totalBatches = Math.ceil(totalRecords / batchSize)
        const batchPromises = Array.from({ length: totalBatches }, (_, i) =>
          this.$axios
            .get('/workforce/customers', {
              params: { ...params, page: i + 1, limit: batchSize }
            })
            .then((response) => response.data.customers || [])
            .catch((error) => {
              console.error(`Batch ${i + 1} failed:`, error)
              return []
            })
        )

        const results = await Promise.all(batchPromises)
        const allCustomers = results.flat()

        if (!allCustomers.length) return

        // Prepare export data
        const exportData = allCustomers.map((customer, index) => {
          const baseData = {
            'S.No': index + 1,
            'Customer Name': this.userFullName(customer),
            CRN: customer.crn || '-',
            Email: customer.customerEmail || '-',
            'Phone No': customer.customerMobile?.[0] || '-',
            Address: customer.customerAddress?.[0]
              ? this.customerAddress(customer.customerAddress[0])
              : '-',
            'Address Type': customer.customerAddress?.[0]?.typeOfAddress || '-',
            'Created By':
              `${customer.createdBy?.firstName || ''} ${customer.createdBy?.lastName || ''}` || '-',
            'Created At': this.$dayjs(customer.createdAt).format('DD/MM/YYYY hh:mm A') || '-'
          }

          // Only add entity column if enabled
          if (this.isEntityEnabled) {
            baseData[this.entityTitle || 'Entity'] = customer.entity?.entityName || '-'
          }

          return baseData
        })

        // Create Excel workbook
        const workbook = XLSX.utils.book_new()
        const worksheet = XLSX.utils.json_to_sheet(exportData)

        // Set column widths
        const headers = Object.keys(exportData[0] || {})
        worksheet['!cols'] = headers.map((header) => ({
          wch: Math.min(
            Math.max(header.length, ...exportData.map((row) => String(row[header] || '').length)) +
              2,
            50
          )
        }))

        XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers')

        // Generate filename and save
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
        const filename = `Customers_Export_${timestamp}.xlsx`
        XLSX.writeFile(workbook, filename)

        this.$toast.success(
          `Excel file exported successfully with ${allCustomers.length} records: ${filename}`
        )
      } catch (error) {
        console.error('Export error:', error)
        this.$toast.error(error.response?.data?.message || 'Failed to export data to Excel')
      } finally {
        this.loadTable = false
      }
    }
  },
  async mounted() {
    // await this.fetchCustomers()
    await this.getConfig()
    this.isLoad = false
  },
  beforeCreate() {
    this.debouncedFetchCustomers = () => {}
  },
  created() {
    this.debouncedFetchCustomers = debounce(this.fetchCustomers, 800)
  }
}
</script>
<style scoped>
.v-data-table >>> .v-data-table__wrapper > table > tbody > tr > td > .v-data-table__checkbox {
  text-align: center;
}
</style>
