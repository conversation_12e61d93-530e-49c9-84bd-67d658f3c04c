<template>
  <div>
    <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
      <template v-for="tab in visibleTabItems" v-slot:[tab.key]="">
        <div v-if="!isLoad">
          <v-main class="bg-[#F7FAFB] dark-bg-default" style="padding: 0px">
            <component :is="tab.component" @navToAmcPlan="handlePlanTab"></component>
          </v-main>
        </div>
        <div v-else class="absolute w-full h-screen flex-col items-center flex justify-center">
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </template>
    </ActitiveTabcomponent>
  </div>
</template>

<script>
// import Overview from "~/components/Workforce/WarrantyManagement/Overview.vue";
import AmcList from '@/components/Workforce/WarrantyManagement/AmcList.vue'
import AmcPlans from '@/components/Workforce/WarrantyManagement/AmcPlans.vue'
import { isModuleFeatureAllowed, hasPermission } from '@/utils/common'
import permissionData from '@/utils/permissions'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'
export default {
  name: 'WarrantyManagement',
  layout: 'Workforce/default',
  components: {
    // Overview,
    AmcList,
    AmcPlans,
    ActitiveTabcomponent
  },
  data() {
    return {
      userData: this.$store.state.wfmPermissions,
      isLoad: true
    }
  },
  computed: {
    canRead() {
      return hasPermission(this.userData, permissionData.amcRead)
    },
    tabItems() {
      return [
        // { key: "Overview", name: "Overview", component: "Overview", visible: this.canRead && this.isFeatureVisible("MANAGE_AMC_OVERVIEW") },
        {
          key: 'AmcList',
          label: 'AMC List',
          icon: 'mdi-format-list-bulleted',
          component: 'AmcList',
          visible: this.canRead && this.isFeatureVisible('MANAGE_AMC_SELLS')
        },
        {
          key: 'AmcPlans',
          label: 'AMC Plans',
          icon: 'mdi-calendar-check',
          component: 'AmcPlans',
          visible: this.canRead && this.isFeatureVisible('MANAGE_AMC_PLANS')
        }
      ]
    },
    visibleTabItems() {
      return this.tabItems.filter((tab) => tab.visible)
    },
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    defaultTabKey() {
      return this.visibleTabItems.length > 0 ? this.visibleTabItems[0].key : 'AmcList'
    }
  },
  mounted() {
    // Set default tab if none is specified in the URL
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }

    // Remove loading state after component is mounted
    this.$nextTick(() => {
      this.isLoad = false
    })
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },
    handlePlanTab() {
      const planTabKey = 'AmcPlans'
      this.handleTabChange(planTabKey)
    },
    isFeatureVisible(feature) {
      return isModuleFeatureAllowed('AMC', feature)
    }
  }
}
</script>

<style scoped>
.v-tab:before {
  background-color: transparent;
}

.v-tab {
  font-size: 16px;
}
</style>
