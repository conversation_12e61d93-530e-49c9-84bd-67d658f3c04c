<template>
  <div class="email-templates-page">
    <!-- Header -->
    <div class="d-flex justify-space-between align-center mb-6 px-4 py-4">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">Email Templates</h1>
        <p class="text-gray-600 mt-1">Manage and preview your MailerLite email templates</p>
      </div>
      <div class="d-flex gap-3">
        <v-btn color="info" @click="fetchTemplates" :loading="loading">
          <v-icon left>mdi-refresh</v-icon>
          Refresh Templates
        </v-btn>
      </div>
    </div>

    <!-- Templates Grid -->
    <div class="px-4">
      <!-- Loading State -->
      <div v-if="loading" class="text-center py-12">
        <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular>
        <p class="mt-4 text-gray-600">Loading templates from MailerLite...</p>
      </div>

      <!-- Templates List -->
      <div v-else-if="templates.length > 0">
        <v-row>
          <v-col v-for="template in templates" :key="template.id" cols="12" md="6" lg="4">
            <v-card class="template-card" hover>
              <!-- Template Preview Thumbnail -->
              <div class="template-thumbnail">
                <div
                  v-if="template.screenshot_url"
                  class="screenshot-container"
                  @click="previewTemplate(template)"
                >
                  <img
                    :src="template.screenshot_url"
                    :alt="template.name"
                    class="template-screenshot"
                  />
                  <div class="preview-overlay">
                    <v-icon large color="white">mdi-eye</v-icon>
                  </div>
                </div>
                <div v-else class="no-preview" @click="previewTemplate(template)">
                  <v-icon size="48" color="gray">mdi-email-outline</v-icon>
                  <p class="mt-2 text-gray-500">No Preview Available</p>
                </div>
              </div>

              <!-- Template Info -->
              <v-card-text>
                <h3 class="template-title">{{ template.name || 'Untitled Template' }}</h3>
                <p class="template-subject text-gray-600">
                  Subject: {{ template.subject || 'No subject' }}
                </p>

                <!-- Template Stats -->
                <div class="template-stats mt-3">
                  <v-chip small class="mr-2">
                    <v-icon left small>mdi-calendar</v-icon>
                    {{ formatDate(template.created_at) }}
                  </v-chip>
                  <v-chip small color="primary" text-color="white">
                    {{ template.type }}
                  </v-chip>
                </div>
              </v-card-text>

              <!-- Template Actions -->
              <v-card-actions class="justify-space-between">
                <v-btn text color="primary" @click="previewTemplate(template)">
                  <v-icon left small>mdi-eye</v-icon>
                  Preview
                </v-btn>
                <div>
                  <v-btn icon @click="useTemplate(template)" title="Use Template">
                    <v-icon color="success">mdi-check-circle</v-icon>
                  </v-btn>
                  <v-btn icon @click="duplicateTemplate(template)" title="Duplicate">
                    <v-icon color="info">mdi-content-copy</v-icon>
                  </v-btn>
                </div>
              </v-card-actions>
            </v-card>
          </v-col>
        </v-row>

        <!-- Pagination -->
        <div v-if="pagination.total > pagination.per_page" class="text-center mt-6">
          <v-pagination
            v-model="pagination.current_page"
            :length="Math.ceil(pagination.total / pagination.per_page)"
            @input="handlePageChange"
            color="primary"
          ></v-pagination>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <v-icon size="96" color="gray">mdi-email-outline</v-icon>
        <h3 class="text-xl font-medium text-gray-700 mt-4">No Templates Found</h3>
        <p class="text-gray-500 mt-2">No email templates were found in your MailerLite account.</p>
        <v-btn color="primary" class="mt-4" @click="fetchTemplates">
          <v-icon left>mdi-refresh</v-icon>
          Refresh
        </v-btn>
      </div>
    </div>

    <!-- Template Preview Modal -->
    <v-dialog v-model="previewModal" max-width="1200" scrollable>
      <v-card v-if="selectedTemplate">
        <v-card-title class="d-flex justify-space-between align-center">
          <div>
            <h2>{{ selectedTemplate.name }}</h2>
            <p class="text-gray-600 text-sm mt-1">{{ selectedTemplate.subject }}</p>
          </div>
          <v-btn icon @click="previewModal = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="pa-0">
          <div class="preview-container">
            <!-- Loading State -->
            <div v-if="loadingPreview" class="loading-preview">
              <v-progress-circular indeterminate color="primary" size="48"></v-progress-circular>
              <p class="mt-4 text-gray-600">Loading template preview...</p>
            </div>

            <!-- Template HTML Preview -->
            <div v-else class="template-preview-frame">
              <iframe
                v-if="selectedTemplate.html"
                :srcdoc="selectedTemplate.html"
                class="template-iframe"
                frameborder="0"
              ></iframe>
              <div v-else class="no-content-preview">
                <v-icon size="64" color="gray">mdi-file-document-outline</v-icon>
                <p class="mt-2 text-gray-500">No HTML content available for preview</p>
              </div>
            </div>
          </div>
        </v-card-text>

        <v-card-actions class="justify-space-between">
          <div class="template-info">
            <v-chip small class="mr-2">
              <v-icon left small>mdi-calendar</v-icon>
              Created: {{ formatDate(selectedTemplate.created_at) }}
            </v-chip>
            <v-chip small class="mr-2">
              <v-icon left small>mdi-update</v-icon>
              Updated: {{ formatDate(selectedTemplate.updated_at) }}
            </v-chip>
          </div>
          <div>
            <v-btn text @click="previewModal = false">Close</v-btn>
            <v-btn color="success" @click="useTemplate(selectedTemplate)">
              <v-icon left>mdi-check-circle</v-icon>
              Use This Template
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- API Configuration Modal -->

    <!-- Success/Error Snackbars -->
    <v-snackbar v-model="snackbar.show" :color="snackbar.color" timeout="4000">
      {{ snackbar.message }}
      <template v-slot:action="{ attrs }">
        <v-btn text v-bind="attrs" @click="snackbar.show = false"> Close </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script>
import axios from 'axios'
export default {
  name: 'EmailTemplates',
  layout: 'Workforce/default',
  data() {
    return {
      templates: [],
      loading: false,
      apiToken: '',
      previewModal: false,
      selectedTemplate: null,
      loadingPreview: false,
      pagination: {
        current_page: 1,
        per_page: 10,
        total: 0
      },
      snackbar: {
        show: false,
        message: '',
        color: 'success'
      },
      // Mock data for development
      mockTemplates: [
        {
          id: '1',
          name: 'Welcome Email Template',
          subject: 'Welcome to our platform!',
          type: 'email',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z',
          screenshot_url: null,
          html: '<html><body><h1>Welcome!</h1><p>Thank you for joining us.</p></body></html>'
        },
        {
          id: '2',
          name: 'Newsletter Template',
          subject: 'Monthly Newsletter - January 2024',
          type: 'email',
          created_at: '2024-01-10T09:15:00Z',
          updated_at: '2024-01-12T14:20:00Z',
          screenshot_url: null,
          html: "<html><body><h1>Newsletter</h1><p>Here are this month's updates...</p></body></html>"
        },
        {
          id: '3',
          name: 'Promotional Email',
          subject: 'Special Offer - 50% Off!',
          type: 'email',
          created_at: '2024-01-05T16:45:00Z',
          updated_at: '2024-01-05T16:45:00Z',
          screenshot_url: null,
          html: "<html><body><h1>Special Offer!</h1><p>Don't miss out on our amazing deal.</p></body></html>"
        }
      ]
    }
  },
  async mounted() {
    await this.initializeComponent()
  },
  async activated() {
    // This is called when the component is activated (useful when used in keep-alive or tabs)
    if (!this.apiToken) {
      await this.initializeComponent()
    }
  },
  methods: {
    async initializeComponent() {
      // Get the token from localStorage (already configured in EmailCampaign)
      const storedToken = localStorage.getItem('mailerlite_token')
      if (storedToken) {
        this.apiToken = storedToken
        console.log('EmailTemplates - Token found in localStorage:', {
          tokenLength: storedToken.length,
          tokenPreview: `${storedToken.substring(0, 10)}...`
        })
        await this.fetchTemplates()
      } else {
        // If no token found, show mock data for development
        console.log('EmailTemplates - No token found, using mock data')
        this.showSnackbar('No MailerLite token found. Using mock data for development.', 'warning')
        this.loadMockData()
      }
    },

    async fetchTemplates() {
      if (!this.apiToken) {
        this.showSnackbar('No API token available', 'warning')
        this.loadMockData()
        return
      }

      this.loading = true
      try {
        // Use the same API configuration pattern as EmailCampaign.vue
        const mailerLiteConfig = {
          apiUrl: 'https://connect.mailerlite.com/api',
          headers: {
            Authorization: `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json'
          }
        }

        const params = new URLSearchParams({
          'filter[type]': 'email',
          page: this.pagination.current_page.toString(),
          sort: '-id'
        })

        console.log(
          'EmailTemplates - Making API request to:',
          `${mailerLiteConfig.apiUrl}/templates?${params}`
        )

        const response = await axios.get(`${mailerLiteConfig.apiUrl}/templates?${params}`, {
          headers: mailerLiteConfig.headers
        })

        this.templates = response.data.data || []
        this.pagination = {
          current_page: response.data.meta?.current_page || 1,
          per_page: response.data.meta?.per_page || 10,
          total: response.data.meta?.total || 0
        }

        this.showSnackbar(`Loaded ${this.templates.length} templates successfully`, 'success')
      } catch (error) {
        console.error('Error fetching templates:', error)
        if (error.response?.status === 401) {
          this.showSnackbar('Invalid API token. Please check your configuration.', 'error')
          this.hasApiToken = false
          this.showApiConfig = true
        } else if (error.message?.includes('CORS')) {
          this.showSnackbar(
            'CORS error: Using mock data for development. Please implement backend proxy (see MAILERLITE_BACKEND_PROXY.md)',
            'warning'
          )
          this.loadMockData()
        } else {
          this.showSnackbar('API unavailable: Using mock data for development', 'warning')
          this.loadMockData()
        }
      } finally {
        this.loading = false
      }
    },

    loadMockData() {
      this.templates = this.mockTemplates
      this.pagination = {
        current_page: 1,
        per_page: 10,
        total: this.mockTemplates.length
      }
    },

    async previewTemplate(template) {
      this.selectedTemplate = template
      this.previewModal = true

      // Fetch the actual HTML content from MailerLite
      await this.fetchTemplateHtml(template.id)
    },

    async fetchTemplateHtml(templateId) {
      if (!templateId) return

      this.loadingPreview = true
      try {
        const response = await axios.get(`https://groot.mailerlite.com/template_html/1156235`)

        if (response.data.success && response.data.html) {
          console.log('EmailTemplates - Fetching HTML for template:', response.data)

          this.selectedTemplate = {
            ...this.selectedTemplate,
            html: response.data.html
          }
          console.log('EmailTemplates - Template HTML fetched successfully', this.selectedTemplate)
        } else {
          console.warn('EmailTemplates - No HTML content in response')
          this.showSnackbar('No HTML content available for this template', 'warning')
        }
      } catch (error) {
        console.error('Error fetching template HTML:', error)
        this.showSnackbar('Failed to load template preview', 'error')
      } finally {
        this.loadingPreview = false
      }
    },

    async useTemplate(template) {
      // Emit event or navigate to email campaign creation with this template
      this.showSnackbar(`Template "${template.name || 'Untitled'}" selected for use`, 'success')

      // You can emit an event to parent component or navigate to email campaign page
      this.$emit('template-selected', template)

      // If HTML is not already fetched, fetch it first
      let templateWithHtml = template
      if (!template.html) {
        try {
          const response = await axios.get(
            `https://groot.mailerlite.com/template_html/${template.id}`
          )
          if (response.data.success && response.data.html) {
            templateWithHtml = {
              ...template,
              html: response.data.html
            }
          }
        } catch (error) {
          console.error('Error fetching template HTML for use:', error)
          // Continue with original template even if HTML fetch fails
        }
      }

      // Navigate to email campaign page with template data
      // Note: Update the path to match your actual email campaign route
      this.$router.push({
        path: '/workforce/campaigns', // Updated to match the existing route
        query: {
          template_id: templateWithHtml.id,
          template_name: templateWithHtml.name,
          template_subject: templateWithHtml.subject,
          template_html: templateWithHtml.html || '' // Use the fetched HTML if available
        }
      })
    },

    async duplicateTemplate(template) {
      try {
        // This would typically create a copy of the template
        this.showSnackbar(`Template "${template.name}" duplicated successfully`, 'success')
        await this.fetchTemplates() // Refresh the list
      } catch (error) {
        console.error('Error duplicating template:', error)
        this.showSnackbar('Failed to duplicate template', 'error')
      }
    },

    handlePageChange(page) {
      this.pagination.current_page = page
      this.fetchTemplates()
    },

    formatDate(dateString) {
      if (!dateString) return 'N/A'
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },

    showSnackbar(message, color = 'success') {
      this.snackbar = {
        show: true,
        message,
        color
      }
    }
  }
}
</script>

<style scoped>
.email-templates-page {
  background: #f7fafb;
  min-height: 100vh;
}

.template-card {
  height: 100%;
  transition: transform 0.2s ease-in-out;
}

.template-card:hover {
  transform: translateY(-2px);
}

.template-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.screenshot-container {
  position: relative;
  height: 100%;
  width: 100%;
}

.template-screenshot {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.screenshot-container:hover .preview-overlay {
  opacity: 1;
}

.screenshot-container:hover .template-screenshot {
  transform: scale(1.05);
}

.no-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px dashed #ddd;
}

.template-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-subject {
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-container {
  height: 70vh;
  background: #f5f5f5;
}

.template-preview-frame {
  height: 100%;
  width: 100%;
}

.template-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.no-content-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.template-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .email-templates-page {
    padding: 16px;
  }

  .template-thumbnail {
    height: 150px;
  }

  .preview-container {
    height: 50vh;
  }
}
</style>
