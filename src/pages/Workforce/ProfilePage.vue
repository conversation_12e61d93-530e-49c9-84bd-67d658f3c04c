<template>
  <div class="flex flex-col md:flex-row gap-4">
    <div class="w-full md:w-3/5">
      <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
        <!-- Profile Tab Content -->
        <template #profile>
          <div class="bg-white dark-bg-custom px-5 py-5 mt-4 border border-gray-300 rounded-md">
            <v-row>
              <v-col>
                <v-text-field
                  label="Company"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="company"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  label="UserId"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="userId"
                  disabled
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  label="Email"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="email"
                  disabled
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-text-field
                  label="First name"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="firstName"
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  label="Last name"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="lastName"
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  label="Mobile"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="phoneNumber"
                  disabled
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <CommonAutocomplete
                  :addressLine1="addressLine1"
                  v-model="selectedAddress"
                  @selected-place="handleSelectedPlace"
                />
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <v-text-field
                  label="City"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="city"
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  label="Country"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="country"
                ></v-text-field>
              </v-col>
              <v-col>
                <v-text-field
                  label="Pincode"
                  flat
                  outlined
                  dense
                  hide-details
                  v-model="pincode"
                ></v-text-field>
              </v-col>
            </v-row>
            <div class="flex justify-end">
              <v-btn :color="$vuetify.theme.currentTheme.primary" class="mt-3">
                <span class="white--text">Update</span>
              </v-btn>
            </div>
          </div>
        </template>

        <!-- Passcode Tab Content -->
        <template #passcode>
          <div class="bg-white dark-bg-custom px-5 py-5 mt-4 border border-gray-300 rounded-md">
            <v-form ref="form">
              <v-row>
                <v-col class="bg-white dark-bg-custom p-4">
                  <v-text-field
                    v-model="passcode_1"
                    outlined
                    dense
                    label="Enter 6 digit passcode"
                    type="number"
                    :rules="passcodeRules"
                  ></v-text-field>
                  <v-text-field
                    v-model="passcode_2"
                    outlined
                    dense
                    label="Repeat passcode"
                    type="number"
                    :rules="[...passcodeRules, passcodeMatchRule]"
                  ></v-text-field>
                  <div class="flex justify-end">
                    <v-btn
                      :color="$vuetify.theme.currentTheme.primary"
                      class="mt-3"
                      @click="setPasscode"
                    >
                      <span class="white--text">Update</span>
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </div>
        </template>
      </ActitiveTabcomponent>
    </div>

    <!-- Profile Image Section - only show for profile tab -->
    <div class="w-full md:w-2/5 h-80 md:h-auto" v-if="activeTab === 'profile'">
      <v-row no-gutters class="mt-16">
        <v-col>
          <div
            align="right"
            class="flex flex-col items-center justify-center py-2 bg-white dark-bg-custom border border-gray-300 rounded-md relative"
          >
            <img class="banner-img" src="@/assets/img/profile-banner.jpeg" />
            <div class="flex items-center justify-center relative" style="top: 40px">
              <v-icon class="edit-btnProfile" @click="openImageUploadDialog">mdi-pen</v-icon>
              <input
                type="file"
                ref="fileInput"
                style="display: none"
                @change="handleImageUpload"
              />
              <v-img
                :aspect-ratio="16 / 9"
                style="height: 120px; width: 120px; border-radius: 50%"
                :src="getProfileImage"
              ></v-img>
            </div>

            <div class="mt-14 text-center text-sm">
              <p class="text-blue-400">{{ this.firstName }} {{ this.lastName }}</p>
              <p class="mb-0">{{ this.email }}</p>
              <p class="mb-0">{{ this.phoneNumber }}</p>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
import CommonAutocomplete from '@/components/CommonAutoComplete.vue'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'

export default {
  name: 'ProfileComponent',
  layout: 'Workforce/default',
  components: {
    CommonAutocomplete,
    ActitiveTabcomponent
  },
  data() {
    return {
      user: this.$storage.getUniversal('user'),
      orgCode: this.$storage.getUniversal('organization_code'),
      passcode_1: null,
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      userId: '',
      company: '',
      passcode_2: null,
      activeTab: 'profile',
      tabItems: [
        {
          key: 'profile',
          label: 'Edit Profile',
          icon: 'mdi-account',
          component: 'profile',
          visible: true
        },
        {
          key: 'passcode',
          label: 'Update Passcode',
          icon: 'mdi-lock',
          component: 'passcode',
          visible: true
        }
      ],
      addressLine1: '',
      selectedAddress: '',
      city: '',
      country: '',
      pincode: null
    }
  },
  methods: {
    handleSelectedPlace(place) {
      this.selectedAddress = place.formatted_address
      this.city = place.city
      this.country = place.country
      this.pincode = place.pincode
    },
    setPasscode() {
      const isValid = this.$refs.form.validate()
      if (!isValid) return
      if (this.passcode_1?.length != 6 || this.passcode_2?.length != 6) {
        this.$toast.error('passcode should have 6 digits')
        return
      }
      if (this.passcode_1 != this.passcode_2) {
        this.$toast.error('passcode not matched')
        return
      }
      this.$axios
        .post('/profile/passcode', {
          passcode: this.passcode_1
        })
        .then(
          (res) => {
            if (res.data.message == 'Success') {
              const successMessage = res?.data?.message || 'Passcode updated successfully'
              this.$toast.success(successMessage)
              this.passcode_1 = null
              this.passcode_2 = null
              this.activeTab = 'profile'
            }
          },
          (err) => {
            console.log('err ', err)
            const errorMessage = err.response.data.message || 'Failed'
            this.$toast.error(errorMessage)
          }
        )
    },
    populateFileds() {
      this.company = this.orgCode
      this.firstName = this.user.user.first_name
      this.lastName = this.user.user.last_name
      this.email = this.user.user.email
      this.phoneNumber = this.user.user.mobile
      this.userId = this.user.user_id
    },
    openImageUploadDialog() {
      this.$refs.fileInput.click()
    },
    async handleImageUpload(event) {
      const file = event.target.files[0]
      const formData = new FormData()
      formData.append('attachments', file)

      try {
        const response = await this.$axios.post('/workforce/profile', formData)

        if (response.data.success) {
          const successMessage = response?.data?.message || 'Image uploaded successfully'
          this.$toast.success(successMessage)
        } else {
          this.$toast.error('Failed to upload image')
        }
      } catch (error) {
        console.error('Error uploading image:', error)
        const errorMessage = error.response.data.message || 'Failed to upload image'
        this.$toast.error(errorMessage)
      }
    },
    handleTabChange(newTab) {
      this.activeTab = newTab
    }
  },
  computed: {
    getProfileImage() {
      if (this.user?.user?.profile_image) {
        return `${process.env.VUE_APP_S3_BASE_URL + this.user?.user?.profile_image}`
      }
      return 'https://cdn.vuetifyjs.com/images/parallax/material.jpg'
    },
    passcodeRules() {
      return [
        (v) => !!v || 'Passcode is required',
        (v) => (v && v.length === 6) || 'Passcode should have 6 digits'
      ]
    },
    passcodeMatchRule() {
      return (v) => v === this.passcode_1 || 'Passcodes do not match'
    }
  },
  mounted() {
    this.populateFileds()
  }
}
</script>

<style>
.edit-btnProfile {
  position: absolute !important;
  bottom: 0px;
  z-index: 1;
  padding: 4px;
  background-color: #ffffff;
  border-radius: 150%;
  border: 2px solid #f0f0f0;
}

.banner-img {
  position: absolute !important;
  top: 0;
  width: 100%;
  height: 120px;
  object-fit: cover;
}
</style>
