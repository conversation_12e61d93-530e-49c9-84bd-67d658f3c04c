<template>
  <div>
    <ActitiveTabcomponent :tabs="tabItems" :defaultTab="activeTab" @tab-changed="handleTabChange">
      <template v-for="tab in visibleTabs" v-slot:[tab.key]="">
        <div v-if="!isLoad">
          <v-main class="bg-[#F7FAFB] dark-bg-default" style="padding: 0px">
            <component :is="tab.component" />
          </v-main>
        </div>
        <div v-else class="loading-container">
          <v-progress-circular
            :color="$vuetify.theme.currentTheme.primary"
            indeterminate
            :size="50"
            :width="5"
          ></v-progress-circular>
          <div class="mt-2"><span>Please Wait...</span></div>
        </div>
      </template>
    </ActitiveTabcomponent>
  </div>
</template>

<script>
import Notifications from '~/components/Workforce/Notifications/Notifications.vue'
import Alerts from '~/components/Workforce/Notifications/Alert.vue'
import { isModuleVisible, isAnyFeatureAllowed } from '@/utils/common'
import { TAB_ITEMS } from '@/utils/workforce/constants'
import ActitiveTabcomponent from '@/components/ActitiveTabcomponent.vue'
import NotificationCard from '~/components/Workforce/Settings/OrgConfigComponents/NotificationIntegration/index.vue'

export default {
  name: 'SettingsPage',
  layout: 'Workforce/default',
  components: {
    Notifications,
    Alerts,
    NotificationCard,
    ActitiveTabcomponent
  },
  data() {
    return {
      orgCode: this.$storage.getUniversal('organization_code'),
      isLoad: true
    }
  },

  computed: {
    tabItems() {
      return TAB_ITEMS.NOTIFICATION.filter((tab) => {
        if (tab.key === 'alerts') {
          return this.isFeatureVisible('SELLS', 'LEAVE_MANAGEMENT')
        }
        return tab.visibility
      }).map((tab) => ({
        ...tab,
        label: tab.name,
        icon: tab.icon || 'mdi-bell',
        component: tab.component
      }))
    },
    activeTab() {
      return this.$route.query.activeTab || this.defaultTabKey
    },
    visibleTabs() {
      return this.tabItems.filter((tab) => tab.visibility)
    },
    defaultTabKey() {
      return this.visibleTabs.length > 0 ? this.visibleTabs[0].key : 'notifications'
    }
  },
  methods: {
    handleTabChange(tabKey) {
      if (this.$route.query.activeTab !== tabKey) {
        this.$router.replace({
          query: { ...this.$route.query, activeTab: tabKey }
        })
      }
    },
    isFeatureVisible(module, feature) {
      return isModuleVisible(module) || isAnyFeatureAllowed(feature)
    }
  },
  mounted() {
    // Set default tab if none is specified in the URL
    if (!this.$route.query.activeTab && this.defaultTabKey) {
      this.$router.replace({
        query: { ...this.$route.query, activeTab: this.defaultTabKey }
      })
    }

    // Remove loading state after component is mounted
    this.$nextTick(() => {
      this.isLoad = false
    })
  }
}
</script>

<style scoped>
.v-tab:before {
  background-color: transparent;
}

.v-tab {
  font-size: 16px;
}

.loading-container {
  position: absolute;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
