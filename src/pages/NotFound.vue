<template>
  <div class="notFound-container">
    <div class="notFound-content">
      <h1 class="notFound-title">{{ errorTitle }}</h1>
      <p class="notFound-text">{{ errorText }}</p>
      <p>{{ errorDescription }}</p>
      <router-link
        :to="errorLink"
        class="notFound-link"
        @click="handleRedirect"
        >{{ errorLinkText }}</router-link
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "NotFound",
  data() {
    const authData = this.$storage.getUniversal("token");

    return {
      errorTitle: "404",
      errorText: "Page Not Found",
      errorDescription:
        "Sorry, the page you are looking for does not exist or you do not have permission to access it.",
      errorLink: authData ? "/workforce/dashboard" : "/login",
      errorLinkText: authData ? "Go to Home" : "Go to Login",
      maxAttempts: 3,
    };
  },
  methods: {
    handleRedirect() {
      if (this.$storage.getUniversal("token")) {
        this.$router.push("/workforce/dashboard");
      } else {
        this.$router.push("/login");
      }
    },
  },
  mounted() {
    if (!this.authData) {
      this.$router.push("/login");
    }
  },
};
</script>

<style scoped>
.notFound-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 92vh;
}
.notFound-content {
  text-align: center;
  color: #333;
}
.notFound-title {
  font-size: 4rem;
  font-weight: bold;
  color: red;
}
.notFound-text {
  font-size: 1.5rem;
}
.notFound-link {
  display: inline-block;
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #2a83ff;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  transition: background-color 0.3s ease;
}
.notFound-link:hover {
  background-color: #1e65d4;
}
</style>
