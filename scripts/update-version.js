const fs = require('fs')
const path = require('path')

const packagePath = path.resolve(__dirname, '../package.json')
const package = require(packagePath)

const versionData = {
  version: package.version,
  buildTime: new Date().toISOString()
}

// Update version.json in public directory
const versionPath = path.resolve(__dirname, '../public/version.json')
fs.writeFileSync(versionPath, JSON.stringify(versionData, null, 2))
