#!/bin/bash

S3_BUCKET_NAME="admin.myfinfi.com"
BUILD_DIR="dist"
S3_PATH="s3://$S3_BUCKET_NAME"
AWS_REGION="ap-south-1"
CLOUDFRONT_DISTRIBUTION_ID="E4ALDFMCPAE7J"

# Exit if any command fails
set -e

# Check if the git branch is provided
if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <git_branch>"
  exit 1
fi

# Get the git branch from the command line argument
git_branch=$1

# Step 0: Switch to the specified git branch
echo "Switching to git branch $git_branch for app repository..."
git checkout "$git_branch" || exit 1
git pull origin "$git_branch" || exit 1

# Step 1: Build the frontend
echo "Building frontend for app repository..."
yarn
yarn build || exit 1

# Step 2: Upload build files to S3
echo "Uploading files to S3..."
# Upload version files with no-cache
aws s3 sync $BUILD_DIR $S3_PATH --region $AWS_REGION \
  --exclude "index.html" \
  --cache-control "max-age=31536000,public" || exit 1

# Upload index.html with no-cache to ensure the latest version is always fetched
aws s3 cp $BUILD_DIR/index.html $S3_PATH/index.html --region $AWS_REGION \
  --cache-control "no-cache, no-store, must-revalidate" || exit 1

# Step 3: Invalidate CloudFront cache (optional)
if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
  echo "Invalidating CloudFront cache..."
  aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*" || exit 1
fi

echo "Deployment for admin repository on branch $git_branch complete!"
