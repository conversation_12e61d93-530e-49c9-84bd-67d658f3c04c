#!/bin/bash

S3_BUCKET_NAME="devadmin.myfinfi.com"
BUILD_DIR="dist"
S3_PATH="s3://$S3_BUCKET_NAME"
AWS_REGION="ap-south-1"

# Exit if any command fails
set -e

# Check if the git branch is provided
if [ "$#" -ne 1 ]; then
  echo "Usage: $0 <git_branch>"
  exit 1
fi

# Get the git branch from the command line argument
git_branch=$1

# Step 0: Switch to the specified git branch
echo "Switching to git branch $git_branch for app repository..."
git checkout "$git_branch" || exit 1
git pull origin "$git_branch" || exit 1

# Step 1: Build the frontend
echo "Building frontend for app repository..."
yarn
yarn build || exit 1

# Step 2: Upload build files to S3
echo "Uploading files to S3..."
aws s3 sync $BUILD_DIR $S3_PATH --region $AWS_REGION

echo "Deployment for admin repository on branch $git_branch complete!"
