{"name": "admin_v2", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve --port 9002", "build": "node scripts/update-version.js && vue-cli-service build", "lint": "vue-cli-service lint", "deploy:dev": "./scripts/deploy/dev.sh main", "deploy:uat": "./scripts/deploy/uat.sh main", "deploy:prod": "./scripts/deploy/prod.sh main"}, "dependencies": {"@popperjs/core": "^2.11.8", "apexcharts": "^3.54.0", "apextree": "^1.3.0", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "core-js": "^3.8.3", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "jsoneditor": "^10.2.0", "lodash": "^4.17.21", "postcss": "^8.4.47", "pusher-js": "^8.4.0-rc2", "qrcode": "^1.5.4", "read-excel-file": "^5.8.6", "sortablejs": "^1.15.3", "tailwindcss": "^3.4.13", "v-tooltip": "^2.1.3", "vue": "^2.6.14", "vue-apexcharts": "^1.6.2", "vue-cool-lightbox": "^2.7.0", "vue-mention": "^1.1.0", "vue-qrcode": "^2.2.2", "vue-router": "^3.6.5", "vue-ssr-carousel": "^2.5.0", "vuedraggable": "^2.24.3", "vuetify": "^2.6.0", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.5.8", "vue-template-compiler": "^2.6.14", "vuetify-loader": "^1.7.0"}, "eslintConfig": {"root": true, "env": {"node": true, "browser": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/valid-v-slot": "off", "no-unused-vars": "warn", "vue/require-v-for-key": "off", "vue/no-mutating-props": "off", "vue/valid-template-root": "off", "no-unreachable": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}