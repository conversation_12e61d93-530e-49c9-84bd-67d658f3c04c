stages:
  - test      # Added test stage
  - security
  - deploy
  - .post

include:
  - template: Security/SAST.gitlab-ci.yml
  # - template: Security/Dependency-Scanning.gitlab-ci.yml

# Debug job to see what jobs are available
debug_jobs:
  stage: security
  script:
    - echo "Listing all jobs in pipeline"
    - env | grep "CI_JOB"


# Override Semgrep SAST configuration to use security stage
# Configure Semgrep SAST
semgrep-sast:
  stage: security  # Override the stage
  variables:
    SECURE_LOG_LEVEL: "debug"
  artifacts:
    reports:
      sast: gl-sast-report.json
    paths:
      - gl-sast-report.json
    expire_in: 1 week

# Configure Dependency Scanning
# gemnasium-dependency-scanning:
#   stage: security
#   image: registry.gitlab.com/security-products/gemnasium-dependency-scanner:2
#   variables:
#     SECURE_LOG_LEVEL: "debug"
#     GEMNASIUM_DB_LOCAL_PATH: "gemnasium-cache"
#     PIP_CACHE_DIR: "pip-cache"
#     MAVEN_REPO_PATH: "maven-cache"
#     GRADLE_USER_HOME: "gradle-cache"
#   cache:
#     paths:
#       - gemnasium-cache/
#       - pip-cache/
#       - maven-cache/
#       - gradle-cache/
#   script:
#     - /analyzer run
#   # variables:
#   #   SECURE_LOG_LEVEL: "debug"
#   #   DS_EXCLUDED_PATHS: "spec, test, tests, tmp"
#   artifacts:
#     reports:
#       dependency_scanning: gl-dependency-scanning-report.json
#     paths:
#       - gl-dependency-scanning-report.json
#     expire_in: 1 week    



# Custom Dependency Scanning
custom_dependency_scan:
  stage: security
  image: node:latest
  before_script:
    - npm install -g npm@latest
    - npm install -g snyk
    - npm install -g audit-ci
    - apt-get update && apt-get install -y jq
  script:
    - echo "Running dependency scanning..."
    - |
      if [ -f "package.json" ]; then
        echo "Scanning Node.js dependencies..."
        # Install dependencies and create lock file
        npm install --package-lock-only --legacy-peer-deps
        
        # Run npm audit and store output
        npm audit --json > npm_audit_raw.json || true
        
        # Debug: Show raw audit data structure
        echo "Raw audit advisories:"
        jq '.advisories' npm_audit_raw.json
        
        # Create the report with proper structure
        jq '
        {
          version: "2.0.0",
          vulnerabilities: (
            if has("advisories") then
              [.advisories | to_entries[] | .value | {
                id: (.url // "unknown"),
                category: "dependency_scanning",
                name: .title,
                message: .overview,
                description: .overview,
                severity: .severity,
                solution: .recommendation,
                fixedIn: (.patched_versions // "No fix available"),
                package: {
                  name: .module_name,
                  version: .findings[0].version
                },
                scanner: {
                  id: "npm_audit",
                  name: "npm audit"
                },
                location: {
                  file: (.findings[0].paths[0] // "package.json"),
                  dependency: {
                    package: {
                      name: .module_name,
                      version: .findings[0].version
                    }
                  }
                },
                identifiers: ([
                  {
                    type: "CWE",
                    name: (.cwe // "Unknown"),
                    value: (.cwe // "Unknown"),
                    url: (.url // "")
                  }
                ] + if .cves then [
                  {
                    type: "CVE",
                    name: .cves[0],
                    value: .cves[0],
                    url: ("https://nvd.nist.gov/vuln/detail/" + .cves[0])
                  }
                ] else [] end)
              }]
            else
              []
            end
          ),
          metadata: {
            vulnerabilities: {
              info: (.metadata.vulnerabilities.info // 0),
              low: (.metadata.vulnerabilities.low // 0),
              moderate: (.metadata.vulnerabilities.moderate // 0),
              high: (.metadata.vulnerabilities.high // 0),
              critical: (.metadata.vulnerabilities.critical // 0),
              total: (.metadata.vulnerabilities.total // 0)
            },
            dependencies: (.metadata.dependencies // {
              prod: 0,
              dev: 0,
              optional: 0,
              peer: 0,
              peerOptional: 0,
              total: 0
            })
          }
        }' npm_audit_raw.json > gl-dependency-scanning-report.json
        
        # Debug: Show the number of vulnerabilities extracted
        echo "Number of vulnerabilities found:"
        jq '.vulnerabilities | length' gl-dependency-scanning-report.json
        
        echo "Vulnerability Summary:"
        jq -r '.metadata.vulnerabilities | to_entries | .[] | "\(.key): \(.value)"' gl-dependency-scanning-report.json
        
        # Debug: Show first vulnerability if any exists
        echo "Sample vulnerability (if any):"
        jq '.vulnerabilities[0]' gl-dependency-scanning-report.json
        
      else
        echo "No package.json found, creating empty report"
        echo '{
          "version": "2.0.0",
          "vulnerabilities": [],
          "metadata": {
            "vulnerabilities": {
              "info": 0,
              "low": 0,
              "moderate": 0,
              "high": 0,
              "critical": 0,
              "total": 0
            },
            "dependencies": {
              "prod": 0,
              "dev": 0,
              "optional": 0,
              "peer": 0,
              "peerOptional": 0,
              "total": 0
            }
          }
        }' > gl-dependency-scanning-report.json
      fi
      
      # Validate the JSON structure
      if ! jq empty gl-dependency-scanning-report.json 2>/dev/null; then
        echo "Error: Invalid JSON generated. Creating empty report."
        echo '{
          "version": "2.0.0",
          "vulnerabilities": [],
          "metadata": {
            "vulnerabilities": {
              "info": 0,
              "low": 0,
              "moderate": 0,
              "high": 0,
              "critical": 0,
              "total": 0
            }
          }
        }' > gl-dependency-scanning-report.json
      fi
  artifacts:
    reports:
      dependency_scanning: gl-dependency-scanning-report.json
    paths:
      - gl-dependency-scanning-report.json
      - npm_audit_raw.json
    expire_in: 1 week




generate_vapt_report:
  stage: deploy
  needs:
    - job: semgrep-sast
      artifacts: true
    - job: custom_dependency_scan
      artifacts: true  
  dependencies:
    - semgrep-sast
    - custom_dependency_scan
  script:
    - echo "=== Starting VAPT Report Generation ==="
    - echo "Workspace contents:"
    - ls -la
    - |
     mkdir -p vapt_report
      if [ -f "gl-sast-report.json" ]; then
        echo "Found Semgrep SAST report, contents:"
        cat gl-sast-report.json
        echo "Creating VAPT report directory..."
        mkdir -p vapt_report
        echo "Copying Semgrep SAST report..."
        cp gl-sast-report.json vapt_report/
        echo "Verify copied report:"
        cat vapt_report/gl-sast-report.json
      else
        echo "ERROR: Semgrep SAST report not found"
        exit 1
      fi

      # Copy Dependency Scanning report
      if [ -f "gl-dependency-scanning-report.json" ]; then
        echo "Copying Dependency Scanning report..."
        cp gl-dependency-scanning-report.json vapt_report/
        echo "Dependency Scanning report contents:"
        cat vapt_report/gl-dependency-scanning-report.json
      else
        echo "WARNING: Dependency Scanning report not found"
      fi
      echo "Final VAPT report directory contents:"
      ls -la vapt_report/  
    - echo "=== VAPT Report Generation Complete ==="
  artifacts:
    paths:
      - vapt_report/
    expire_in: 1 week